default:
  image:
    name: docker:stable
  services:
    - docker:dind

stages:
  - build
  - test
  - security-scan
  - publish

variables:
  CI_BUILD_IMAGE_NAME: "golang:1.24"
  IMAGE_REGISTRY_HOST: "$CI_REGISTRY"
  IMAGE_NAME: "$IMAGE_REGISTRY_HOST/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME"

build:
  stage: build
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
      when: always
    - if: '$CI_COMMIT_BRANCH == "main"'
      when: always
    - if: '$CI_COMMIT_TAG =~ /^v\d+\.\d+\.\d+$/'
      when: always
    - when: never
  image:
    name: $CI_BUILD_IMAGE_NAME
  before_script:
    - go mod tidy
  script:
    - go build -v -o build/api-server ./cmd/api-server/
  artifacts:
    paths:
      - build/
      - go.mod
      - go.sum

test:
  stage: test
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
      when: always
    - if: '$CI_COMMIT_BRANCH == "main"'
      when: always
    - if: '$CI_COMMIT_TAG =~ /^v\d+\.\d+\.\d+$/'
      when: always
    - when: never
  dependencies:
    - build
  image:
    name: $CI_BUILD_IMAGE_NAME
  script:
    - go fmt $(go list ./... | grep -v /vendor/)
    - go vet $(go list ./... | grep -v /vendor/)
    - go test -v --race -covermode=atomic -cover -coverprofile .testcoverage.out ./...
    - go tool cover -func .testcoverage.out

create-release:
  stage: publish
  allow_failure: false
  rules:
    - if: '$CI_COMMIT_TAG =~ /^v\d+\.\d+\.\d+$/'
      when: always
    - when: never
  image:
    name: gitlab/glab:latest
  before_script:
    - glab auth login --hostname $CI_SERVER_HOST --api-protocol https --git-protocol https --job-token $CI_JOB_TOKEN
  script:
    - glab release create $CI_COMMIT_TAG --notes "Release notes for version $CI_COMMIT_TAG"

.configure-aws-cli: &configure-aws-cli
  - apk update && apk --no-cache add aws-cli
  - aws configure set aws_access_key_id $AWS_ACCESS_KEY_ID
  - aws configure set aws_secret_access_key $AWS_SECRET_ACCESS_KEY
  - aws configure set region $AWS_DEFAULT_REGION
  - aws configure set ouput text

release-image:
  stage: publish
  allow_failure: false
  rules:
    - if: '$CI_COMMIT_TAG =~ /^v\d+\.\d+\.\d+$/'
      when: always
    - when: never
  dependencies:
    - create-release
  before_script:
    - *configure-aws-cli
#     - export REGISTRY_HOST=$CI_REGISTRY
#     - echo $CI_JOB_TOKEN | docker login $CI_REGISTRY -u $CI_REGISTRY_USER --password-stdin
#     - export IMAGE_NAME='$REGISTRY_HOST/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME'
#     - export VERSION=${CI_COMMIT_TAG#v}
    - export IMAGE_REGISTRY_HOST=$AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com
    - aws ecr get-login-password | docker login $IMAGE_REGISTRY_HOST -u AWS --password-stdin
    - export IMAGE_NAME=$IMAGE_REGISTRY_HOST/staging/api-server
    - export VERSION=${CI_COMMIT_TAG#v}
  script:
    - docker build --no-cache --pull --build-arg VERSION=$VERSION -t $IMAGE_NAME:$VERSION -t $IMAGE_NAME:latest -f Dockerfile .
    - docker push $IMAGE_NAME:$VERSION
    - docker push $IMAGE_NAME:latest
    - docker rmi $IMAGE_NAME:$VERSION $IMAGE_NAME:latest
    - docker logout $IMAGE_REGISTRY_HOST

build-image:
  stage: publish
  allow_failure: false
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
      when: always
    - if: '$CI_COMMIT_BRANCH == "main"'
      when: always
    - when: never
  before_script:
    - *configure-aws-cli
    - export IMAGE_REGISTRY_HOST=$AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com
    - aws ecr get-login-password | docker login $IMAGE_REGISTRY_HOST -u AWS --password-stdin
    - export IMAGE_NAME=$IMAGE_REGISTRY_HOST/staging/api-server
    - export VERSION=${CI_COMMIT_SHORT_SHA}
  script:
    - docker build --no-cache --pull --build-arg VERSION=$VERSION -t $IMAGE_NAME:$VERSION -t $IMAGE_NAME:latest -f Dockerfile .
    - docker push $IMAGE_NAME:$VERSION
    - docker rmi $IMAGE_NAME:$VERSION
    - docker logout $IMAGE_REGISTRY_HOST

include:
  - template: Jobs/SAST.gitlab-ci.yml

semgrep-sast:
  stage: security-scan
  allow_failure: true
  rules:
    - if: '$CI_COMMIT_TAG =~ /^v\d+\.\d+\.\d+$/'
      when: always
    - when: never

.install-trivy: &install-trivy
  - export TRIVY_VERSION=$(wget -qO - "https://api.github.com/repos/aquasecurity/trivy/releases/latest" | grep '"tag_name":' | sed -E 's/.*"v([^"]+)".*/\1/')
  - wget --no-verbose https://github.com/aquasecurity/trivy/releases/download/v${TRIVY_VERSION}/trivy_${TRIVY_VERSION}_Linux-64bit.tar.gz -O - | tar -zxvf -

trivy-dependency_scanning:
  stage: security-scan
  allow_failure: true
  rules:
    - if: '$CI_COMMIT_TAG =~ /^v\d+\.\d+\.\d+$/'
      when: always
    - when: never
  dependencies:
    - build
  before_script:
    - *install-trivy
    - export IMAGE=$IMAGE_NAME:${CI_COMMIT_TAG#v}-ds
    - docker build --pull --build-arg VERSION=${CI_COMMIT_TAG#v} -t $IMAGE -f Dockerfile .
  script:
    - ./trivy image --exit-code 0 --cache-dir .trivycache/ --pkg-types library $IMAGE
    # Fail on severe vulnerabilities
    - ./trivy image --exit-code 1 --cache-dir .trivycache/ --severity CRITICAL,HIGH --pkg-types library $IMAGE
    - docker rmi $IMAGE
  cache:
    paths:
      - .trivycache/

trivy-container_scanning:
  stage: security-scan
  allow_failure: true
  rules:
    - if: '$CI_COMMIT_TAG =~ /^v\d+\.\d+\.\d+$/'
      when: always
    - when: never
  before_script:
    - *install-trivy
    - export IMAGE=$IMAGE_NAME:${CI_COMMIT_TAG#v}-cs
    - docker build --pull --build-arg VERSION=${CI_COMMIT_TAG#v} -t $IMAGE -f Dockerfile .
  script:
    - ./trivy image --exit-code 0 --cache-dir .trivycache/ --format template --template "@contrib/gitlab.tpl" -o gl-container-scanning-report.json $IMAGE
    - ./trivy image --exit-code 0 --cache-dir .trivycache/ --pkg-types os $IMAGE
    # Fail on severe vulnerabilities
    - ./trivy image --exit-code 1 --cache-dir .trivycache/ --severity CRITICAL,HIGH --pkg-types os $IMAGE
    - docker rmi $IMAGE
  cache:
    paths:
      - .trivycache/
  artifacts:
    when: always
    reports:
      container_scanning: gl-container-scanning-report.json
