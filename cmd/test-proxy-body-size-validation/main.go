package main

import (
	"context"
	"fmt"
	"log"

	kompose_usecase "api-server/internal/usecase/kompose"
)

func main() {
	fmt.Println("=== Testing Proxy Body Size Validation ===")

	// Test cases with different proxy-body-size values
	testCases := []struct {
		name        string
		value       string
		expectError bool
	}{
		{
			name:        "Valid format: 100m",
			value:       "100m",
			expectError: false,
		},
		{
			name:        "Valid format: 1G",
			value:       "1G",
			expectError: false,
		},
		{
			name:        "Valid format: 500k",
			value:       "500k",
			expectError: false,
		},
		{
			name:        "Valid format: 0",
			value:       "0",
			expectError: false,
		},
		{
			name:        "Invalid format: 100x",
			value:       "100x",
			expectError: true,
		},
		{
			name:        "Invalid format: invalid-size",
			value:       "invalid-size",
			expectError: true,
		},
		{
			name:        "Invalid format: empty",
			value:       "",
			expectError: true,
		},
		{
			name:        "Invalid format: 100.5m",
			value:       "100.5m",
			expectError: true,
		},
	}

	for _, tc := range testCases {
		fmt.Printf("\n--- %s ---\n", tc.name)

		dockerComposeContent := fmt.Sprintf(`
services:
  web:
    image: nginx:latest
    labels:
      volvo.ingress.proxy-body-size: "%s"
      app: web
`, tc.value)

		fmt.Printf("Testing with proxy-body-size: '%s'\n", tc.value)

		response, err := kompose_usecase.TransformVolvoLabels(context.Background(), kompose_usecase.TransformVolvoLabelsInput{
			DockerComposeContent: dockerComposeContent,
		})

		if tc.expectError {
			if err != nil {
				fmt.Printf("✅ Expected error: %v\n", err)
			} else {
				fmt.Printf("❌ Expected error but got none. Transformation count: %d\n", response.TransformationCount)
			}
		} else {
			if err != nil {
				fmt.Printf("❌ Unexpected error: %v\n", err)
			} else {
				fmt.Printf("✅ Successfully transformed. Count: %d\n", response.TransformationCount)
				if len(response.TransformedLabels) > 0 {
					fmt.Printf("   Transformed to: %v\n", response.TransformedLabels[0].TransformedTo)
				}
			}
		}
	}

	fmt.Println("\n=== Testing Complete Transformation with Valid Proxy Body Size ===")

	validDockerCompose := `
services:
  web:
    image: nginx:latest
    ports:
      - "8080:80"
    labels:
      volvo.service.expose: true
      volvo.ingress.proxy-body-size: "50m"
      app: web
`

	response, err := kompose_usecase.TransformVolvoLabels(context.Background(), kompose_usecase.TransformVolvoLabelsInput{
		DockerComposeContent: validDockerCompose,
	})
	if err != nil {
		log.Fatalf("❌ Error transforming valid docker-compose: %v", err)
	}

	fmt.Printf("✅ Successfully transformed %d labels\n", response.TransformationCount)
	fmt.Println("\nTransformed labels:")
	for _, transformed := range response.TransformedLabels {
		fmt.Printf("- Service: %s\n", transformed.ServiceName)
		fmt.Printf("  Original: %s\n", transformed.OriginalLabel)
		fmt.Printf("  Transformed to: %v\n", transformed.TransformedTo)
	}

	fmt.Println("\n=== Final Transformed Content ===")
	fmt.Println(response.TransformedContent)
}
