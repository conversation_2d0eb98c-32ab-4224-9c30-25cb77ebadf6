package main

import (
	"context"
	"fmt"
	"log"

	kompose_usecase "api-server/internal/usecase/kompose"
)

func main() {
	// Example docker-compose content with kompose labels (should fail)
	dockerComposeWithKompose := `
services:
  weba-c_b:
    ports:
      - "8000:80"
    environment:
      - ENV=production
    labels:
      kompose.service.expose: true
      kompose.service.expose.ingress-class-name: "nginx"

  postgres-a:
    image: postgres:16
    environment:
      POSTGRES_USER: myuser
      POSTGRES_PASSWORD: mypassword
      POSTGRES_DB: mydb
    ports:
      - "5432:5432"
      - "5431:5432"
    volumes:
      - pgdata:/var/lib/postgresql/data
    labels:
      kompose.service.type: "ClusterIP"
      kompose.volume.size: "1Gi"

  redisa-b_c:
    image: redis:alpine
    ports:
      - "6379:6379"
      - "6666:6379"

volumes:
  pgdata: {}
`

	// Example docker-compose content without kompose labels (should pass)
	dockerComposeWithoutKompose := `
services:
  web:
    image: nginx:latest
    ports:
      - "8080:80"
    labels:
      app: web
      version: "1.0"
      environment: production

  db:
    image: postgres:13
    environment:
      POSTGRES_DB: mydb
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
    labels:
      app: database
      tier: backend

volumes:
  db_data: {}
`

	fmt.Println("=== Testing docker-compose with kompose labels ===")
	err := kompose_usecase.CheckKomposeLabels(context.Background(), kompose_usecase.CheckKomposeLabelsInput{
		DockerComposeContent: dockerComposeWithKompose,
	})
	if err != nil {
		fmt.Printf("❌ Error (expected): %v\n", err)
	} else {
		fmt.Println("✅ No kompose labels found")
	}

	fmt.Println("\n=== Testing docker-compose without kompose labels ===")
	err = kompose_usecase.CheckKomposeLabels(context.Background(), kompose_usecase.CheckKomposeLabelsInput{
		DockerComposeContent: dockerComposeWithoutKompose,
	})
	if err != nil {
		fmt.Printf("❌ Unexpected error: %v\n", err)
		log.Fatal(err)
	} else {
		fmt.Println("✅ No kompose labels found - validation passed!")
	}
}
