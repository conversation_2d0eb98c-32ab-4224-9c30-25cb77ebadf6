package main

import (
	"context"
	"fmt"
	"log"

	kompose_usecase "api-server/internal/usecase/kompose"
)

func main() {
	// Example docker-compose content with volvo labels
	dockerComposeWithVolvoLabels := `
services:
  web:
    image: nginx:latest
    ports:
      - "8080:80"
    environment:
      - ENV=production
    labels:
      volvo.service.expose: true
      volvo.service.type: "ClusterIP"
      volvo.ingress.proxy-body-size: "100m"
      app: web
      version: "1.0"

  api:
    image: api:latest
    ports:
      - "3000:3000"
    labels:
      volvo.service.nodeport: true
      app: api

  db:
    image: postgres:13
    environment:
      POSTGRES_DB: mydb
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
    volumes:
      - db_data:/var/lib/postgresql/data
    labels:
      volvo.volume.size: "10Gi"
      app: database
      tier: backend

  cache:
    image: redis:alpine
    labels:
      volvo.service.loadbalancer: true
      app: cache

volumes:
  db_data: {}
`

	fmt.Println("=== Original Docker Compose Content ===")
	fmt.Println(dockerComposeWithVolvoLabels)

	fmt.Println("\n=== Transforming Volvo Labels to Kompose Labels ===")
	response, err := kompose_usecase.TransformVolvoLabels(context.Background(), kompose_usecase.TransformVolvoLabelsInput{
		DockerComposeContent: dockerComposeWithVolvoLabels,
	})
	if err != nil {
		log.Fatalf("❌ Error transforming labels: %v", err)
	}

	fmt.Printf("✅ Successfully transformed %d volvo labels\n\n", response.TransformationCount)

	fmt.Println("=== Transformation Details ===")
	for i, transformed := range response.TransformedLabels {
		fmt.Printf("%d. Service: %s\n", i+1, transformed.ServiceName)
		fmt.Printf("   Original: %s\n", transformed.OriginalLabel)
		fmt.Printf("   Transformed to:\n")
		for _, newLabel := range transformed.TransformedTo {
			fmt.Printf("     - %s\n", newLabel)
		}
		fmt.Println()
	}

	fmt.Println("=== Transformed Docker Compose Content ===")
	fmt.Println(response.TransformedContent)

	fmt.Println("=== Verifying Kompose Labels Exist (Should Fail Validation) ===")
	err = kompose_usecase.CheckKomposeLabels(context.Background(), kompose_usecase.CheckKomposeLabelsInput{
		DockerComposeContent: response.TransformedContent,
	})
	if err != nil {
		fmt.Printf("✅ Expected validation error (kompose labels detected): %v\n", err)
	} else {
		fmt.Println("❌ Unexpected: No kompose labels found in transformed content")
	}

	fmt.Println("\n=== Testing the Transformation Pipeline ===")
	fmt.Println("1. Start with volvo labels")
	fmt.Println("2. Transform volvo labels to kompose labels")
	fmt.Println("3. Use the transformed content for kompose conversion")

	// Demonstrate that the transformed content can be used with other functions
	fmt.Println("\n=== Validating Transformed Content Structure ===")
	validationResp, err := kompose_usecase.ValidateDockerCompose(context.Background(), kompose_usecase.ValidateDockerComposeInput{
		DockerComposeContent: response.TransformedContent,
	})
	if err != nil {
		log.Fatalf("❌ Error validating transformed content: %v", err)
	}

	if validationResp.Valid {
		fmt.Println("✅ Transformed docker-compose content is valid and ready for kompose conversion")
	} else {
		fmt.Printf("❌ Transformed content has validation errors: %v\n", validationResp.Errors)
		if len(validationResp.Warnings) > 0 {
			fmt.Printf("⚠️  Warnings: %v\n", validationResp.Warnings)
		}
	}
}
