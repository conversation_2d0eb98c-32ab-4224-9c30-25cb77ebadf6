package main_test

import (
	"context"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	main "api-server/cmd/api-server"
	"api-server/internal/enums"
	"api-server/internal/handlers/mocks"
	"api-server/internal/middlewares"
	orgMocks "api-server/internal/usecase/organization/mocks"
	userMocks "api-server/internal/usecase/user/mocks"
	"api-server/pkg/jwt"
)

func TestInitUser(t *testing.T) {
	gin.SetMode(gin.TestMode)

	jwtSecret := "jwt_secret"
	validToken, _ := jwt.GenerateJWT(context.Background(), jwt.CustomClaims{}, []byte(jwtSecret), time.Duration(600)*time.Second)
	mockUserID, _ := uuid.Parse("1142ae16-fa66-42a0-a59d-0af5eeb07ec6")

	type dependencies struct {
		// config configs.GlobalConfig
		userUsecase        *userMocks.MockUserUsecase
		userHandler        *mocks.MockUserHandler
		sshKeyHandler      *mocks.MockSshKeysHandler
		accessTokenHandler *mocks.MockAccessTokenHandler
	}

	testcases := []struct {
		name             string
		mockFn           func(r *dependencies)
		req              func() *http.Request
		expectStatusCode int
		expectMessage    string
	}{
		{
			name: "GET /api/v1/users with correct permission successfully",
			mockFn: func(d *dependencies) {
				d.userHandler.On("ListUsers", mock.Anything).Once()
				d.userUsecase.On("AuthorizePlatform", mock.Anything, mock.Anything, []enums.AppPermission{enums.AppPermission_UsersRead}).Return(true, nil).Once()
			},
			req: func() *http.Request {
				req := httptest.NewRequest(http.MethodGet, "/api/v1/users", nil)
				req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", *validToken))
				return req
			},
			expectStatusCode: http.StatusOK,
			expectMessage:    "",
		},
		{
			name: "PATCH /api/v1/:user_id/role with correct permission successfully",
			mockFn: func(d *dependencies) {
				d.userHandler.On("UpdateUserRole", mock.Anything).Once()
				d.userUsecase.On("AuthorizePlatform", mock.Anything, mock.Anything, []enums.AppPermission{enums.AppPermission_UsersEdit}).Return(true, nil).Once()
				d.userUsecase.On("IsAdmin", mock.Anything, mock.Anything).Return(true, nil).Once()
			},
			req: func() *http.Request {
				req := httptest.NewRequest(http.MethodPatch, fmt.Sprintf("/api/v1/users/%s/role", mockUserID), nil)
				req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", *validToken))
				return req
			},
			expectStatusCode: http.StatusOK,
			expectMessage:    "",
		},
		{
			name: "DELETE /api/v1/:user_id with correct permission successfully",
			mockFn: func(d *dependencies) {
				d.userHandler.On("DeleteUser", mock.Anything).Once()
				d.userUsecase.On("AuthorizePlatform", mock.Anything, mock.Anything, []enums.AppPermission{enums.AppPermission_UsersDelete}).Return(true, nil).Once()
				d.userUsecase.On("IsAdmin", mock.Anything, mock.Anything).Return(true, nil).Once()
			},
			req: func() *http.Request {
				req := httptest.NewRequest(http.MethodDelete, fmt.Sprintf("/api/v1/users/%s", mockUserID), nil)
				req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", *validToken))
				return req
			},
			expectStatusCode: http.StatusOK,
			expectMessage:    "",
		},
		{
			name: "GET /api/v1/users/keys with correct permission successfully",
			mockFn: func(d *dependencies) {
				d.sshKeyHandler.On("ListSshKeys", mock.Anything).Once()
				d.userUsecase.On("AuthorizePlatform", mock.Anything, mock.Anything, []enums.AppPermission{enums.AppPermission_KeysRead}).Return(true, nil).Once()
			},
			req: func() *http.Request {
				req := httptest.NewRequest(http.MethodGet, "/api/v1/users/keys", nil)
				req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", *validToken))
				return req
			},
			expectStatusCode: http.StatusOK,
			expectMessage:    "",
		},
		{
			name: "POST /api/v1/users/keys with correct permission successfully",
			mockFn: func(d *dependencies) {
				d.sshKeyHandler.On("AddNewSshKey", mock.Anything).Once()
				d.userUsecase.On("AuthorizePlatform", mock.Anything, mock.Anything, []enums.AppPermission{enums.AppPermission_KeysEdit}).Return(true, nil).Once()
			},
			req: func() *http.Request {
				req := httptest.NewRequest(http.MethodPost, "/api/v1/users/keys", nil)
				req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", *validToken))
				return req
			},
			expectStatusCode: http.StatusOK,
			expectMessage:    "",
		},
		{
			name: "DELETE /api/v1/users/keys/:key_id with correct permission successfully",
			mockFn: func(d *dependencies) {
				d.sshKeyHandler.On("DeleteSshKey", mock.Anything).Once()
				d.userUsecase.On("AuthorizePlatform", mock.Anything, mock.Anything, []enums.AppPermission{enums.AppPermission_KeysDelete}).Return(true, nil).Once()
			},
			req: func() *http.Request {
				req := httptest.NewRequest(http.MethodDelete, fmt.Sprintf("/api/v1/users/keys/%s", mockUserID), nil)
				req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", *validToken))
				return req
			},
			expectStatusCode: http.StatusOK,
			expectMessage:    "",
		},
		{
			name: "GET /api/v1/users/keys/:key_id with correct permission successfully",
			mockFn: func(d *dependencies) {
				d.sshKeyHandler.On("GetSshKey", mock.Anything).Once()
				d.userUsecase.On("AuthorizePlatform", mock.Anything, mock.Anything, []enums.AppPermission{enums.AppPermission_KeysRead}).Return(true, nil).Once()
			},
			req: func() *http.Request {
				req := httptest.NewRequest(http.MethodGet, fmt.Sprintf("/api/v1/users/keys/%s", mockUserID), nil)
				req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", *validToken))
				return req
			},
			expectStatusCode: http.StatusOK,
			expectMessage:    "",
		},
		{
			name: "GET /api/v1/users/access_tokens with correct permission successfully",
			mockFn: func(d *dependencies) {
				d.accessTokenHandler.On("ListAccessToken", mock.Anything).Once()
				d.userUsecase.On("AuthorizePlatform", mock.Anything, mock.Anything, []enums.AppPermission{enums.AppPermission_KeysRead}).Return(true, nil).Once()
			},
			req: func() *http.Request {
				req := httptest.NewRequest(http.MethodGet, "/api/v1/users/access_tokens", nil)
				req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", *validToken))
				return req
			},
			expectStatusCode: http.StatusOK,
			expectMessage:    "",
		},
		{
			name: "POST /api/v1/users/access_tokens with correct permission successfully",
			mockFn: func(d *dependencies) {
				d.accessTokenHandler.On("CreateNewAccessToken", mock.Anything).Once()
				d.userUsecase.On("AuthorizePlatform", mock.Anything, mock.Anything, []enums.AppPermission{enums.AppPermission_KeysEdit}).Return(true, nil).Once()
			},
			req: func() *http.Request {
				req := httptest.NewRequest(http.MethodPost, "/api/v1/users/access_tokens", nil)
				req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", *validToken))
				return req
			},
			expectStatusCode: http.StatusOK,
			expectMessage:    "",
		},
		{
			name: "DELETE /api/v1/users/access_tokens/:token_id with correct permission successfully",
			mockFn: func(d *dependencies) {
				d.accessTokenHandler.On("DeleteAccessToken", mock.Anything).Once()
				d.userUsecase.On("AuthorizePlatform", mock.Anything, mock.Anything, []enums.AppPermission{enums.AppPermission_KeysDelete}).Return(true, nil).Once()
			},
			req: func() *http.Request {
				req := httptest.NewRequest(http.MethodDelete, fmt.Sprintf("/api/v1/users/access_tokens/%s", mockUserID), nil)
				req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", *validToken))
				return req
			},
			expectStatusCode: http.StatusOK,
			expectMessage:    "",
		},
		{
			name: "POST /api/v1/users/access_tokens/verify with correct permission successfully",
			mockFn: func(d *dependencies) {
				d.accessTokenHandler.On("VerifyAccessToken", mock.Anything).Once()
				d.userUsecase.On("AuthorizePlatform", mock.Anything, mock.Anything, []enums.AppPermission{enums.AppPermission_KeysRead}).Return(true, nil).Once()
			},
			req: func() *http.Request {
				req := httptest.NewRequest(http.MethodPost, "/api/v1/users/access_tokens/verify", nil)
				req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", *validToken))
				return req
			},
			expectStatusCode: http.StatusOK,
			expectMessage:    "",
		},
		{
			name: "POST /api/v1/users/:user_id/avatar with correct permission successfully",
			mockFn: func(d *dependencies) {
				d.userHandler.On("UploadUserAvatar", mock.Anything).Once()
				d.userUsecase.On("AuthorizePlatform", mock.Anything, mock.Anything, []enums.AppPermission{enums.AppPermission_UsersEdit}).Return(true, nil).Once()
			},
			req: func() *http.Request {
				req := httptest.NewRequest(http.MethodPost, fmt.Sprintf("/api/v1/users/%s/avatar", mockUserID), nil)
				req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", *validToken))
				return req
			},
			expectStatusCode: http.StatusOK,
			expectMessage:    "",
		},
		{
			name: "DELETE /api/v1/users/:user_id/avatar with correct permission successfully",
			mockFn: func(d *dependencies) {
				d.userHandler.On("DeleteUserAvatar", mock.Anything).Once()
				d.userUsecase.On("AuthorizePlatform", mock.Anything, mock.Anything, []enums.AppPermission{enums.AppPermission_UsersEdit}).Return(true, nil).Once()
			},
			req: func() *http.Request {
				req := httptest.NewRequest(http.MethodDelete, fmt.Sprintf("/api/v1/users/%s/avatar", mockUserID), nil)
				req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", *validToken))
				return req
			},
			expectStatusCode: http.StatusOK,
			expectMessage:    "",
		},
	}

	d := &dependencies{
		userUsecase:        &userMocks.MockUserUsecase{},
		userHandler:        &mocks.MockUserHandler{},
		sshKeyHandler:      &mocks.MockSshKeysHandler{},
		accessTokenHandler: &mocks.MockAccessTokenHandler{},
	}
	d.userUsecase.On("UpsertUser", mock.Anything, mock.Anything).Return(nil)

	bearerAuth := middlewares.BearerAuth(jwtSecret, d.userUsecase)
	platformPermission := middlewares.PlatformPermission(d.userUsecase)
	isAdminMiddleware := middlewares.IsAdmin(d.userUsecase)
	r := gin.Default()
	v1 := r.Group("/api/v1")
	main.InitUser(
		v1,
		d.userHandler,
		d.sshKeyHandler,
		d.accessTokenHandler,
		bearerAuth,
		platformPermission,
		isAdminMiddleware,
	)

	for _, testcase := range testcases {
		t.Run(testcase.name, func(t *testing.T) {
			testcase.mockFn(d)
			rr := httptest.NewRecorder()
			r.ServeHTTP(rr, testcase.req())

			assert.Equal(t, testcase.expectStatusCode, rr.Code)
			assert.Equal(t, testcase.expectMessage, rr.Body.String())
		})
	}

}

func TestInitRepositories(t *testing.T) {
	gin.SetMode(gin.TestMode)

	jwtSecret := "jwt_secret"
	validToken, _ := jwt.GenerateJWT(context.Background(), jwt.CustomClaims{}, []byte(jwtSecret), time.Duration(600)*time.Second)
	mockUUID, _ := uuid.Parse("f6000b19-3b23-400e-928c-e4c3ad77eda2")

	type dependencies struct {
		repositoryHandler *mocks.MockRepositoryHandler
		userUsecase       *userMocks.MockUserUsecase
		orgUsecase        *orgMocks.MockOrganizationUsecase
	}

	testcases := []struct {
		name             string
		mockFn           func(r *dependencies)
		req              func() *http.Request
		expectStatusCode int
		expectMessage    string
	}{
		{
			name: "GET /api/v1/repositories with correct permission successfully",
			mockFn: func(d *dependencies) {
				d.repositoryHandler.On("ListRepos", mock.Anything).Once()
				d.userUsecase.On("AuthorizePlatform", mock.Anything, mock.Anything, []enums.AppPermission{
					enums.AppPermission_SpacesRead,
					enums.AppPermission_ModelsRead,
					enums.AppPermission_DatasetsRead,
				}).Return(true, nil).Once()
			},
			req: func() *http.Request {
				req := httptest.NewRequest(http.MethodGet, "/api/v1/repositories", nil)
				req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", *validToken))
				return req
			},
			expectStatusCode: http.StatusOK,
			expectMessage:    "",
		},
		{
			name: "POST /api/v1/repositories with correct permission successfully",
			mockFn: func(d *dependencies) {
				d.repositoryHandler.On("AddRepo", mock.Anything).Once()
				d.userUsecase.On("AuthorizePlatform", mock.Anything, mock.Anything, []enums.AppPermission{
					enums.AppPermission_SpacesAdd,
					enums.AppPermission_ModelsAdd,
					enums.AppPermission_DatasetsAdd,
				}).Return(true, nil).Once()
			},
			req: func() *http.Request {
				req := httptest.NewRequest(http.MethodPost, "/api/v1/repositories", nil)
				req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", *validToken))
				return req
			},
			expectStatusCode: http.StatusOK,
			expectMessage:    "",
		},
		{
			name: "DELETE /api/v1/repositories/spaces/namespace/repo_name with correct permission successfully",
			mockFn: func(d *dependencies) {
				d.repositoryHandler.On("DeleteRepo", mock.Anything).Once()
				d.userUsecase.On("AuthorizePlatform", mock.Anything, mock.Anything, []enums.AppPermission{
					enums.AppPermission_SpacesRead,
					enums.AppPermission_ModelsRead,
					enums.AppPermission_DatasetsRead,
				}).Return(true, nil).Once()
				d.userUsecase.On("IsAdmin", mock.Anything, mock.Anything).Return(false, nil)
				d.userUsecase.On("AuthorizeRepo", mock.Anything, mock.Anything, mock.Anything, []enums.RepoPermission{
					enums.RepoPermission_Delete,
				}).Return(true, nil).Once()
			},
			req: func() *http.Request {
				req := httptest.NewRequest(http.MethodDelete, "/api/v1/repositories/spaces/namespace/repo_name", nil)
				req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", *validToken))
				return req
			},
			expectStatusCode: http.StatusOK,
			expectMessage:    "",
		},
		{
			name: "PATCH /api/v1/repositories/spaces/namespace/repo_name with correct permission successfully",
			mockFn: func(d *dependencies) {
				d.repositoryHandler.On("UpdateRepo", mock.Anything).Once()
				d.userUsecase.On("AuthorizePlatform", mock.Anything, mock.Anything, []enums.AppPermission{
					enums.AppPermission_SpacesRead,
					enums.AppPermission_ModelsRead,
					enums.AppPermission_DatasetsRead,
				}).Return(true, nil).Once()
				d.userUsecase.On("IsAdmin", mock.Anything, mock.Anything).Return(false, nil)
				d.userUsecase.On("AuthorizeRepo", mock.Anything, mock.Anything, mock.Anything, []enums.RepoPermission{
					enums.RepoPermission_Edit,
				}).Return(true, nil).Once()
			},
			req: func() *http.Request {
				req := httptest.NewRequest(http.MethodPatch, "/api/v1/repositories/spaces/namespace/repo_name", nil)
				req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", *validToken))
				return req
			},
			expectStatusCode: http.StatusOK,
			expectMessage:    "",
		},
		{
			name: "GET /api/v1/repositories/spaces/namespace/repo_name with correct permission successfully",
			mockFn: func(d *dependencies) {
				d.repositoryHandler.On("GetRepoInfo", mock.Anything).Once()
				d.userUsecase.On("AuthorizePlatform", mock.Anything, mock.Anything, []enums.AppPermission{
					enums.AppPermission_SpacesRead,
					enums.AppPermission_ModelsRead,
					enums.AppPermission_DatasetsRead,
				}).Return(true, nil).Once()
			},
			req: func() *http.Request {
				req := httptest.NewRequest(http.MethodGet, "/api/v1/repositories/spaces/namespace/repo_name", nil)
				req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", *validToken))
				return req
			},
			expectStatusCode: http.StatusOK,
			expectMessage:    "",
		},
		{
			name: "GET /api/v1/repositories/spaces/namespace/repo_name/archive with correct permission successfully",
			mockFn: func(d *dependencies) {
				d.repositoryHandler.On("ArchiveRepository", mock.Anything).Once()
				d.userUsecase.On("AuthorizePlatform", mock.Anything, mock.Anything, []enums.AppPermission{
					enums.AppPermission_SpacesRead,
					enums.AppPermission_ModelsRead,
					enums.AppPermission_DatasetsRead,
				}).Return(true, nil).Once()
			},
			req: func() *http.Request {
				req := httptest.NewRequest(http.MethodGet, "/api/v1/repositories/spaces/namespace/repo_name/archive", nil)
				req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", *validToken))
				return req
			},
			expectStatusCode: http.StatusOK,
			expectMessage:    "",
		},
		{
			name: "POST /api/v1/repositories/spaces/namespace/repo_name/avatar with correct permission successfully",
			mockFn: func(d *dependencies) {
				d.repositoryHandler.On("UploadRepoAvatar", mock.Anything).Once()
				d.userUsecase.On("AuthorizePlatform", mock.Anything, mock.Anything, []enums.AppPermission{
					enums.AppPermission_SpacesRead,
					enums.AppPermission_ModelsRead,
					enums.AppPermission_DatasetsRead,
				}).Return(true, nil).Once()
				d.userUsecase.On("IsAdmin", mock.Anything, mock.Anything).Return(false, nil)
				d.userUsecase.On("AuthorizeRepo", mock.Anything, mock.Anything, mock.Anything, []enums.RepoPermission{
					enums.RepoPermission_Edit,
				}).Return(true, nil).Once()
			},
			req: func() *http.Request {
				req := httptest.NewRequest(http.MethodPost, "/api/v1/repositories/spaces/namespace/repo_name/avatar", nil)
				req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", *validToken))
				return req
			},
			expectStatusCode: http.StatusOK,
			expectMessage:    "",
		},
		{
			name: "DELETE /api/v1/repositories/spaces/namespace/repo_name/avatar with correct permission successfully",
			mockFn: func(d *dependencies) {
				d.repositoryHandler.On("DeleteRepoAvatar", mock.Anything).Once()
				d.userUsecase.On("AuthorizePlatform", mock.Anything, mock.Anything, []enums.AppPermission{
					enums.AppPermission_SpacesRead,
					enums.AppPermission_ModelsRead,
					enums.AppPermission_DatasetsRead,
				}).Return(true, nil).Once()
				d.userUsecase.On("IsAdmin", mock.Anything, mock.Anything).Return(false, nil)
				d.userUsecase.On("AuthorizeRepo", mock.Anything, mock.Anything, mock.Anything, []enums.RepoPermission{
					enums.RepoPermission_Edit,
				}).Return(true, nil).Once()
			},
			req: func() *http.Request {
				req := httptest.NewRequest(http.MethodDelete, "/api/v1/repositories/spaces/namespace/repo_name/avatar", nil)
				req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", *validToken))
				return req
			},
			expectStatusCode: http.StatusOK,
			expectMessage:    "",
		},
		{
			name: "GET /api/v1/repositories/spaces/namespace/repo_name/contributors with correct permission successfully",
			mockFn: func(d *dependencies) {
				d.repositoryHandler.On("ListRepoContributors", mock.Anything).Once()
				d.userUsecase.On("AuthorizePlatform", mock.Anything, mock.Anything, []enums.AppPermission{
					enums.AppPermission_SpacesRead,
					enums.AppPermission_ModelsRead,
					enums.AppPermission_DatasetsRead,
				}).Return(true, nil).Once()
			},
			req: func() *http.Request {
				req := httptest.NewRequest(http.MethodGet, "/api/v1/repositories/spaces/namespace/repo_name/contributors", nil)
				req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", *validToken))
				return req
			},
			expectStatusCode: http.StatusOK,
			expectMessage:    "",
		},
		{
			name: "POST /api/v1/repositories/spaces/namespace/repo_name/invite with correct permission successfully",
			mockFn: func(d *dependencies) {
				d.repositoryHandler.On("InviteUsers", mock.Anything).Once()
				d.userUsecase.On("AuthorizePlatform", mock.Anything, mock.Anything, []enums.AppPermission{
					enums.AppPermission_SpacesRead,
					enums.AppPermission_ModelsRead,
					enums.AppPermission_DatasetsRead,
				}).Return(true, nil).Once()
				d.userUsecase.On("IsAdmin", mock.Anything, mock.Anything).Return(false, nil)
				d.userUsecase.On("AuthorizeRepo", mock.Anything, mock.Anything, mock.Anything, []enums.RepoPermission{
					enums.RepoPermission_Edit,
					enums.RepoPermission_MembersEdit,
				}).Return(true, nil).Once()
			},
			req: func() *http.Request {
				req := httptest.NewRequest(http.MethodPost, "/api/v1/repositories/spaces/namespace/repo_name/invite", nil)
				req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", *validToken))
				return req
			},
			expectStatusCode: http.StatusOK,
			expectMessage:    "",
		},
		{
			name: "GET /api/v1/repositories/spaces/namespace/repo_name/members with correct permission successfully",
			mockFn: func(d *dependencies) {
				d.repositoryHandler.On("ListRepoMembers", mock.Anything).Once()
				d.userUsecase.On("AuthorizePlatform", mock.Anything, mock.Anything, []enums.AppPermission{
					enums.AppPermission_SpacesRead,
					enums.AppPermission_ModelsRead,
					enums.AppPermission_DatasetsRead,
				}).Return(true, nil).Once()
			},
			req: func() *http.Request {
				req := httptest.NewRequest(http.MethodGet, "/api/v1/repositories/spaces/namespace/repo_name/members", nil)
				req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", *validToken))
				return req
			},
			expectStatusCode: http.StatusOK,
			expectMessage:    "",
		},
		{
			name: "GET /api/v1/repositories/spaces/namespace/repo_name/members/:member_id with correct permission successfully",
			mockFn: func(d *dependencies) {
				d.repositoryHandler.On("GetRepositoryMember", mock.Anything).Once()
				d.userUsecase.On("AuthorizePlatform", mock.Anything, mock.Anything, []enums.AppPermission{
					enums.AppPermission_SpacesRead,
					enums.AppPermission_ModelsRead,
					enums.AppPermission_DatasetsRead,
				}).Return(true, nil).Once()
			},
			req: func() *http.Request {
				req := httptest.NewRequest(http.MethodGet, "/api/v1/repositories/spaces/namespace/repo_name/members/member_id", nil)
				req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", *validToken))
				return req
			},
			expectStatusCode: http.StatusOK,
			expectMessage:    "",
		},
		{
			name: "DELETE /api/v1/repositories/spaces/namespace/repo_name/members/:member_id with correct permission successfully",
			mockFn: func(d *dependencies) {
				d.repositoryHandler.On("RemoveRepositoryMember", mock.Anything).Once()
				d.userUsecase.On("AuthorizePlatform", mock.Anything, mock.Anything, []enums.AppPermission{
					enums.AppPermission_SpacesRead,
					enums.AppPermission_ModelsRead,
					enums.AppPermission_DatasetsRead,
				}).Return(true, nil).Once()
				d.userUsecase.On("IsAdmin", mock.Anything, mock.Anything).Return(false, nil)
				d.userUsecase.On("AuthorizeRepo", mock.Anything, mock.Anything, mock.Anything, []enums.RepoPermission{
					enums.RepoPermission_Edit,
					enums.RepoPermission_MembersDelete,
				}).Return(true, nil).Once()
			},
			req: func() *http.Request {
				req := httptest.NewRequest(http.MethodDelete, "/api/v1/repositories/spaces/namespace/repo_name/members/member_id", nil)
				req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", *validToken))
				return req
			},
			expectStatusCode: http.StatusOK,
			expectMessage:    "",
		},
		{
			name: "PUT /api/v1/repositories/spaces/namespace/repo_name/members/:member_id with correct permission successfully",
			mockFn: func(d *dependencies) {
				d.repositoryHandler.On("UpdateRepositoryMember", mock.Anything).Once()
				d.userUsecase.On("AuthorizePlatform", mock.Anything, mock.Anything, []enums.AppPermission{
					enums.AppPermission_SpacesRead,
					enums.AppPermission_ModelsRead,
					enums.AppPermission_DatasetsRead,
				}).Return(true, nil).Once()
				d.userUsecase.On("IsAdmin", mock.Anything, mock.Anything).Return(false, nil)
				d.userUsecase.On("AuthorizeRepo", mock.Anything, mock.Anything, mock.Anything, []enums.RepoPermission{
					enums.RepoPermission_Edit,
					enums.RepoPermission_MembersEdit,
				}).Return(true, nil).Once()
			},
			req: func() *http.Request {
				req := httptest.NewRequest(http.MethodPut, "/api/v1/repositories/spaces/namespace/repo_name/members/member_id", nil)
				req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", *validToken))
				return req
			},
			expectStatusCode: http.StatusOK,
			expectMessage:    "",
		},
		{
			name: "GET /api/v1/repositories/spaces/namespace/repo_name/envs with correct permission successfully",
			mockFn: func(d *dependencies) {
				d.repositoryHandler.On("GetEnv", mock.Anything).Once()
				d.userUsecase.On("IsAdmin", mock.Anything, mock.Anything).Return(false, nil)
				d.userUsecase.On("AuthorizeRepo", mock.Anything, mock.Anything, mock.Anything, []enums.RepoPermission{
					enums.RepoPermission_Edit,
				}).Return(true, nil).Once()
			},
			req: func() *http.Request {
				req := httptest.NewRequest(http.MethodGet, "/api/v1/repositories/spaces/namespace/repo_name/envs", nil)
				req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", *validToken))
				return req
			},
			expectStatusCode: http.StatusOK,
			expectMessage:    "",
		},
		{
			name: "POST /api/v1/repositories/spaces/namespace/repo_name/envs with correct permission successfully",
			mockFn: func(d *dependencies) {
				d.repositoryHandler.On("CreateEnv", mock.Anything).Once()
				d.userUsecase.On("IsAdmin", mock.Anything, mock.Anything).Return(false, nil)
				d.userUsecase.On("AuthorizeRepo", mock.Anything, mock.Anything, mock.Anything, []enums.RepoPermission{
					enums.RepoPermission_Edit,
				}).Return(true, nil).Once()
			},
			req: func() *http.Request {
				req := httptest.NewRequest(http.MethodPost, "/api/v1/repositories/spaces/namespace/repo_name/envs", nil)
				req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", *validToken))
				return req
			},
			expectStatusCode: http.StatusOK,
			expectMessage:    "",
		},
		{
			name: "PUT /api/v1/repositories/spaces/namespace/repo_name/envs with correct permission successfully",
			mockFn: func(d *dependencies) {
				d.repositoryHandler.On("UpdateEnv", mock.Anything).Once()
				d.userUsecase.On("IsAdmin", mock.Anything, mock.Anything).Return(false, nil)
				d.userUsecase.On("AuthorizeRepo", mock.Anything, mock.Anything, mock.Anything, []enums.RepoPermission{
					enums.RepoPermission_Edit,
				}).Return(true, nil).Once()
			},
			req: func() *http.Request {
				req := httptest.NewRequest(http.MethodPut, "/api/v1/repositories/spaces/namespace/repo_name/envs", nil)
				req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", *validToken))
				return req
			},
			expectStatusCode: http.StatusOK,
			expectMessage:    "",
		},
		{
			name: "DELETE /api/v1/repositories/spaces/namespace/repo_name/envs with correct permission successfully",
			mockFn: func(d *dependencies) {
				d.repositoryHandler.On("DeleteEnv", mock.Anything).Once()
				d.userUsecase.On("IsAdmin", mock.Anything, mock.Anything).Return(false, nil)
				d.userUsecase.On("AuthorizeRepo", mock.Anything, mock.Anything, mock.Anything, []enums.RepoPermission{
					enums.RepoPermission_Edit,
				}).Return(true, nil).Once()
			},
			req: func() *http.Request {
				req := httptest.NewRequest(http.MethodDelete, "/api/v1/repositories/spaces/namespace/repo_name/envs", nil)
				req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", *validToken))
				return req
			},
			expectStatusCode: http.StatusOK,
			expectMessage:    "",
		},
		{
			name: "POST /api/v1/repositories/spaces/namespace/repo_name/deployments/start with correct permission successfully",
			mockFn: func(d *dependencies) {
				d.repositoryHandler.On("StartDeployment", mock.Anything).Once()
				d.userUsecase.On("IsAdmin", mock.Anything, mock.Anything).Return(false, nil)
				d.userUsecase.On("AuthorizeRepo", mock.Anything, mock.Anything, mock.Anything, []enums.RepoPermission{
					enums.RepoPermission_DeployRun,
				}).Return(true, nil).Once()
			},
			req: func() *http.Request {
				req := httptest.NewRequest(http.MethodPost, "/api/v1/repositories/spaces/namespace/repo_name/deployments/start", nil)
				req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", *validToken))
				return req
			},
			expectStatusCode: http.StatusOK,
			expectMessage:    "",
		},
		{
			name: "POST /api/v1/repositories/spaces/namespace/repo_name/deployments/rebuild with correct permission successfully",
			mockFn: func(d *dependencies) {
				d.repositoryHandler.On("StartDeployment", mock.Anything).Once()
				d.userUsecase.On("IsAdmin", mock.Anything, mock.Anything).Return(false, nil)
				d.userUsecase.On("AuthorizeRepo", mock.Anything, mock.Anything, mock.Anything, []enums.RepoPermission{
					enums.RepoPermission_DeployRun,
				}).Return(true, nil).Once()
			},
			req: func() *http.Request {
				req := httptest.NewRequest(http.MethodPost, "/api/v1/repositories/spaces/namespace/repo_name/deployments/rebuild", nil)
				req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", *validToken))
				return req
			},
			expectStatusCode: http.StatusOK,
			expectMessage:    "",
		},
		{
			name: "GET /api/v1/repositories/spaces/namespace/repo_name/deployments/restart with correct permission successfully",
			mockFn: func(d *dependencies) {
				d.repositoryHandler.On("RestartDeployment", mock.Anything).Once()
				d.userUsecase.On("IsAdmin", mock.Anything, mock.Anything).Return(false, nil)
				d.userUsecase.On("AuthorizeRepo", mock.Anything, mock.Anything, mock.Anything, []enums.RepoPermission{
					enums.RepoPermission_DeployRun,
				}).Return(true, nil).Once()
			},
			req: func() *http.Request {
				req := httptest.NewRequest(http.MethodGet, "/api/v1/repositories/spaces/namespace/repo_name/deployments/restart", nil)
				req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", *validToken))
				return req
			},
			expectStatusCode: http.StatusOK,
			expectMessage:    "",
		},
		{
			name: "POST /api/v1/repositories/spaces/namespace/repo_name/deployments/stop with correct permission successfully",
			mockFn: func(d *dependencies) {
				d.repositoryHandler.On("StopDeployment", mock.Anything).Once()
				d.userUsecase.On("IsAdmin", mock.Anything, mock.Anything).Return(false, nil)
				d.userUsecase.On("AuthorizeRepo", mock.Anything, mock.Anything, mock.Anything, []enums.RepoPermission{
					enums.RepoPermission_DeployStop,
				}).Return(true, nil).Once()
			},
			req: func() *http.Request {
				req := httptest.NewRequest(http.MethodPost, "/api/v1/repositories/spaces/namespace/repo_name/deployments/stop", nil)
				req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", *validToken))
				return req
			},
			expectStatusCode: http.StatusOK,
			expectMessage:    "",
		},
		// {
		// 	name: "GET /api/v1/repositories/spaces/namespace/repo_name/deployments/logs with correct permission successfully",
		// 	mockFn: func(d *dependencies) {
		// 		d.repositoryHandler.On("GetDeploymentBuildLogs", mock.Anything).Once()
		// 		d.userUsecase.On("AuthorizeRepo", mock.Anything, mock.Anything, mock.Anything, []enums.RepoPermission{
		// 			enums.RepoPermission_DeployRun,
		// 		}).Return(true, nil).Once()
		// 	},
		// 	req: func() *http.Request {
		// 		req := httptest.NewRequest(http.MethodGet, "/api/v1/repositories/spaces/namespace/repo_name/deployments/logs", nil)
		// 		query := req.URL.Query()
		// 		query.Add("access_token", *validToken)
		// 		req.URL.RawQuery = query.Encode()
		// 		return req
		// 	},
		// 	expectStatusCode: http.StatusOK,
		// 	expectMessage:    "",
		// },
		// {
		// 	name: "GET /api/v1/repositories/spaces/namespace/repo_name/deployments/logs with correct permission successfully",
		// 	mockFn: func(d *dependencies) {
		// 		d.repositoryHandler.On("GetDeploymentPodLogs", mock.Anything).Once()
		// 		d.userUsecase.On("AuthorizeRepo", mock.Anything, mock.Anything, mock.Anything, []enums.RepoPermission{
		// 			enums.RepoPermission_DeployRun,
		// 		}).Return(true, nil).Once()
		// 	},
		// 	req: func() *http.Request {
		// 		req := httptest.NewRequest(http.MethodGet, "/api/v1/repositories/spaces/namespace/repo_name/deployments/pods/logs", nil)
		// 		query := req.URL.Query()
		// 		query.Add("access_token", *validToken)
		// 		req.URL.RawQuery = query.Encode()
		// 		return req
		// 	},
		// 	expectStatusCode: http.StatusOK,
		// 	expectMessage:    "",
		// },
		// {
		// 	name: "GET /api/v1/repositories/spaces/namespace/repo_name/deployments/status with correct permission successfully",
		// 	mockFn: func(d *dependencies) {
		// 		d.repositoryHandler.On("GetDeploymentStatus", mock.Anything).Once()
		// 		d.userUsecase.On("AuthorizeRepo", mock.Anything, mock.Anything, mock.Anything, []enums.RepoPermission{
		// 			enums.RepoPermission_DeployRun,
		// 		}).Return(true, nil).Once()
		// 	},
		// 	req: func() *http.Request {
		// 		req := httptest.NewRequest(http.MethodGet, "/api/v1/repositories/spaces/namespace/repo_name/deployments/status", nil)
		// 		req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", *validToken))
		// 		return req
		// 	},
		// 	expectStatusCode: http.StatusOK,
		// 	expectMessage:    "",
		// },
		{
			name: "POST /api/v1/repositories/spaces/namespace/repo_name/deployments/terminate with correct permission successfully",
			mockFn: func(d *dependencies) {
				d.repositoryHandler.On("StopDeployment", mock.Anything).Once()
				d.userUsecase.On("IsAdmin", mock.Anything, mock.Anything).Return(false, nil)
				d.userUsecase.On("AuthorizeRepo", mock.Anything, mock.Anything, mock.Anything, []enums.RepoPermission{
					enums.RepoPermission_DeployStop,
				}).Return(true, nil).Once()
			},
			req: func() *http.Request {
				req := httptest.NewRequest(http.MethodPost, "/api/v1/repositories/spaces/namespace/repo_name/deployments/terminate", nil)
				req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", *validToken))
				return req
			},
			expectStatusCode: http.StatusOK,
			expectMessage:    "",
		},
		{
			name: "POST /api/v1/repositories/spaces/namespace/repo_name/commits with correct permission successfully",
			mockFn: func(d *dependencies) {
				d.repositoryHandler.On("CreateRepoCommit", mock.Anything).Once()
				d.userUsecase.On("AuthorizeAccessToken", mock.Anything, *validToken).Return(&mockUUID, nil).Once()
				d.userUsecase.On("AuthorizeOrgAccessToken", mock.Anything, *validToken).Return(&mockUUID, nil).Once()
				d.userUsecase.On("IsAdmin", mock.Anything, mock.Anything).Return(false, nil)
				d.userUsecase.On("AuthorizeRepo", mock.Anything, mockUUID, mock.Anything, []enums.RepoPermission{
					enums.RepoPermission_DeployRun,
				}).Return(true, nil).Once()
			},
			req: func() *http.Request {
				req := httptest.NewRequest(http.MethodPost, "/api/v1/repositories/spaces/namespace/repo_name/commits", nil)
				req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", *validToken))
				return req
			},
			expectStatusCode: http.StatusOK,
			expectMessage:    "",
		},
		{
			name: "GET /api/v1/repositories/spaces/namespace/repo_name/commits with correct permission successfully",
			mockFn: func(d *dependencies) {
				d.repositoryHandler.On("ListRepoCommits", mock.Anything).Once()
				d.userUsecase.On("AuthorizePlatform", mock.Anything, mock.Anything, []enums.AppPermission{
					enums.AppPermission_SpacesRead,
					enums.AppPermission_ModelsRead,
					enums.AppPermission_DatasetsRead,
				}).Return(true, nil).Once()
			},
			req: func() *http.Request {
				req := httptest.NewRequest(http.MethodGet, "/api/v1/repositories/spaces/namespace/repo_name/commits", nil)
				req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", *validToken))
				return req
			},
			expectStatusCode: http.StatusOK,
			expectMessage:    "",
		},
		{
			name: "GET /api/v1/repositories/spaces/namespace/repo_name/files with correct permission successfully",
			mockFn: func(d *dependencies) {
				d.repositoryHandler.On("ListRepoFiles", mock.Anything).Once()
				d.userUsecase.On("AuthorizePlatform", mock.Anything, mock.Anything, []enums.AppPermission{
					enums.AppPermission_SpacesFilesRead,
					enums.AppPermission_ModelsFilesRead,
					enums.AppPermission_DatasetsFilesRead,
				}).Return(true, nil).Once()
			},
			req: func() *http.Request {
				req := httptest.NewRequest(http.MethodGet, "/api/v1/repositories/spaces/namespace/repo_name/files", nil)
				req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", *validToken))
				return req
			},
			expectStatusCode: http.StatusOK,
			expectMessage:    "",
		},
		{
			name: "GET /api/v1/repositories/spaces/namespace/repo_name/files/*file_path with correct permission successfully",
			mockFn: func(d *dependencies) {
				d.repositoryHandler.On("GetFileContent", mock.Anything).Once()
				d.userUsecase.On("AuthorizePlatform", mock.Anything, mock.Anything, []enums.AppPermission{
					enums.AppPermission_SpacesFilesRead,
					enums.AppPermission_ModelsFilesRead,
					enums.AppPermission_DatasetsFilesRead,
				}).Return(true, nil).Once()
			},
			req: func() *http.Request {
				req := httptest.NewRequest(http.MethodGet, "/api/v1/repositories/spaces/namespace/repo_name/files/file_path", nil)
				req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", *validToken))
				return req
			},
			expectStatusCode: http.StatusOK,
			expectMessage:    "",
		},
		{
			name: "GET /api/v1/repositories/spaces/namespace/repo_name/branches with correct permission successfully",
			mockFn: func(d *dependencies) {
				d.repositoryHandler.On("ListRepoBranches", mock.Anything).Once()
				d.userUsecase.On("AuthorizePlatform", mock.Anything, mock.Anything, []enums.AppPermission{
					enums.AppPermission_SpacesRead,
					enums.AppPermission_ModelsRead,
					enums.AppPermission_DatasetsRead,
				}).Return(true, nil).Once()
			},
			req: func() *http.Request {
				req := httptest.NewRequest(http.MethodGet, "/api/v1/repositories/spaces/namespace/repo_name/branches", nil)
				req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", *validToken))
				return req
			},
			expectStatusCode: http.StatusOK,
			expectMessage:    "",
		},
	}

	d := &dependencies{
		repositoryHandler: &mocks.MockRepositoryHandler{},
		userUsecase:       &userMocks.MockUserUsecase{},
		orgUsecase:        &orgMocks.MockOrganizationUsecase{},
	}
	d.userUsecase.On("UpsertUser", mock.Anything, mock.Anything).Return(nil)

	bearerAuth := middlewares.BearerAuth(jwtSecret, d.userUsecase)
	tokenAuthOnQuery := middlewares.TokenAuthOnQuery(jwtSecret)
	accessTokenAuth := middlewares.AccessTokenAuth(d.userUsecase, d.orgUsecase)
	isAdminMiddleware := middlewares.IsAdmin(d.userUsecase)
	platformPermission := middlewares.PlatformPermission(d.userUsecase)
	repoPermission := middlewares.RepoPermission(d.userUsecase)

	r := gin.Default()
	v1 := r.Group("/api/v1")
	main.InitRepositories(
		v1,
		d.repositoryHandler,
		bearerAuth,
		tokenAuthOnQuery,
		repoPermission,
		platformPermission,
		accessTokenAuth,
		isAdminMiddleware,
	)

	for _, testcase := range testcases {
		t.Run(testcase.name, func(t *testing.T) {
			testcase.mockFn(d)
			rr := httptest.NewRecorder()
			r.ServeHTTP(rr, testcase.req())

			assert.Equal(t, testcase.expectStatusCode, rr.Code)
			assert.Equal(t, testcase.expectMessage, rr.Body.String())
		})
	}
}

func TestInitSignups(t *testing.T) {
	gin.SetMode(gin.TestMode)

	jwtSecret := "jwt_secret"
	validToken, _ := jwt.GenerateJWT(context.Background(), jwt.CustomClaims{}, []byte(jwtSecret), time.Duration(600)*time.Second)

	type dependencies struct {
		signupRequestHandler *mocks.MockSignUpRequestHandler
		userUsecase          *userMocks.MockUserUsecase
	}

	testcases := []struct {
		name             string
		mockFn           func(r *dependencies)
		req              func() *http.Request
		expectStatusCode int
		expectMessage    string
	}{
		{
			name: "GET /api/v1/admin/signups with correct permission successfully",
			mockFn: func(d *dependencies) {
				d.signupRequestHandler.On("ListApprovalRequest", mock.Anything).Once()
				d.userUsecase.On("AuthorizePlatform", mock.Anything, mock.Anything, []enums.AppPermission{
					enums.AppPermission_UsersApprove,
					enums.AppPermission_UsersRead,
				}).Return(true, nil).Once()
				d.userUsecase.On("IsAdmin", mock.Anything, mock.Anything).Return(true, nil).Once()
			},
			req: func() *http.Request {
				req := httptest.NewRequest(http.MethodGet, "/api/v1/admin/signups", nil)
				req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", *validToken))
				return req
			},
			expectStatusCode: http.StatusOK,
			expectMessage:    "",
		},
		{
			name: "POST /api/v1/admin/signups/:id/approval with correct permission successfully",
			mockFn: func(d *dependencies) {
				d.signupRequestHandler.On("ProcessSignUpRequest", mock.Anything).Once()
				d.userUsecase.On("AuthorizePlatform", mock.Anything, mock.Anything, []enums.AppPermission{
					enums.AppPermission_UsersApprove,
				}).Return(true, nil).Once()
				d.userUsecase.On("IsAdmin", mock.Anything, mock.Anything).Return(true, nil).Once()
			},
			req: func() *http.Request {
				req := httptest.NewRequest(http.MethodPatch, "/api/v1/admin/signups/123/approval", nil)
				req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", *validToken))
				return req
			},
			expectStatusCode: http.StatusOK,
			expectMessage:    "",
		},
		{
			name: "POST /api/v1/admin/invite with correct permission successfully",
			mockFn: func(d *dependencies) {
				d.signupRequestHandler.On("InviteUser", mock.Anything).Once()
				d.userUsecase.On("AuthorizePlatform", mock.Anything, mock.Anything, []enums.AppPermission{
					enums.AppPermission_UsersInvite,
				}).Return(true, nil).Once()
				d.userUsecase.On("IsAdmin", mock.Anything, mock.Anything).Return(true, nil).Once()
			},
			req: func() *http.Request {
				req := httptest.NewRequest(http.MethodPost, "/api/v1/admin/invite", nil)
				req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", *validToken))
				return req
			},
			expectStatusCode: http.StatusOK,
			expectMessage:    "",
		},
	}

	d := &dependencies{
		signupRequestHandler: &mocks.MockSignUpRequestHandler{},
		userUsecase:          &userMocks.MockUserUsecase{},
	}
	d.userUsecase.On("UpsertUser", mock.Anything, mock.Anything).Return(nil)

	bearerAuth := middlewares.BearerAuth(jwtSecret, d.userUsecase)
	platformPermission := middlewares.PlatformPermission(d.userUsecase)
	isAdminMiddleware := middlewares.IsAdmin(d.userUsecase)

	r := gin.Default()
	v1 := r.Group("/api/v1")
	main.InitSignups(
		v1,
		d.signupRequestHandler,
		bearerAuth,
		platformPermission,
		isAdminMiddleware,
	)

	for _, testcase := range testcases {
		t.Run(testcase.name, func(t *testing.T) {
			testcase.mockFn(d)
			rr := httptest.NewRecorder()
			r.ServeHTTP(rr, testcase.req())

			assert.Equal(t, testcase.expectStatusCode, rr.Code)
			assert.Equal(t, testcase.expectMessage, rr.Body.String())
		})
	}
}

func TestInitOrganization(t *testing.T) {
	gin.SetMode(gin.TestMode)

	jwtSecret := "jwt_secret"
	validToken, _ := jwt.GenerateJWT(context.Background(), jwt.CustomClaims{}, []byte(jwtSecret), time.Duration(600)*time.Second)
	mockOrgID, _ := uuid.Parse("1142ae16-fa66-42a0-a59d-0af5eeb07ec6")
	mockMemberID, _ := uuid.Parse("2242ae16-fa66-42a0-a59d-0af5eeb07ec6")

	type dependencies struct {
		organizationHandler   *mocks.MockOrganizationHandler
		userUsecase           *userMocks.MockUserUsecase
		orgAccessTokenHandler *mocks.MockOrgAccessTokenHandler
	}

	testcases := []struct {
		name             string
		mockFn           func(r *dependencies)
		req              func() *http.Request
		expectStatusCode int
		expectMessage    string
	}{
		{
			name: "POST /api/v1/organizations with correct permission successfully",
			mockFn: func(d *dependencies) {
				d.organizationHandler.On("CreateOrganization", mock.Anything).Once()
				d.userUsecase.On("AuthorizePlatform", mock.Anything, mock.Anything, []enums.AppPermission{
					enums.AppPermission_OrgsRead,
				}).Return(true, nil).Once()
			},
			req: func() *http.Request {
				req := httptest.NewRequest(http.MethodPost, "/api/v1/organizations", nil)
				req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", *validToken))
				return req
			},
			expectStatusCode: http.StatusOK,
			expectMessage:    "",
		},
		{
			name: "GET /api/v1/organizations/:org_id/members/:member_id with correct permission successfully",
			mockFn: func(d *dependencies) {
				d.organizationHandler.On("GetMember", mock.Anything).Once()
				d.userUsecase.On("AuthorizePlatform", mock.Anything, mock.Anything, []enums.AppPermission{
					enums.AppPermission_OrgsRead,
				}).Return(true, nil).Once()
			},
			req: func() *http.Request {
				req := httptest.NewRequest(http.MethodGet, fmt.Sprintf("/api/v1/organizations/%s/members/%s", mockOrgID, mockMemberID), nil)
				req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", *validToken))
				return req
			},
			expectStatusCode: http.StatusOK,
			expectMessage:    "",
		},
		{
			name: "DELETE /api/v1/organizations/:org_id/members/:member_id with correct permission successfully",
			mockFn: func(d *dependencies) {
				d.organizationHandler.On("RemoveMember", mock.Anything).Once()
				d.userUsecase.On("AuthorizePlatform", mock.Anything, mock.Anything, []enums.AppPermission{
					enums.AppPermission_OrgsEdit,
				}).Return(true, nil).Once()
				d.userUsecase.On("IsAdmin", mock.Anything, mock.Anything).Return(false, nil)
				d.userUsecase.On("AuthorizeOrg", mock.Anything, mock.Anything, mock.Anything, []enums.RepoPermission{
					enums.RepoPermission_Edit,
					enums.RepoPermission_MembersDelete,
				}).Return(true, nil).Once()
			},
			req: func() *http.Request {
				req := httptest.NewRequest(http.MethodDelete, fmt.Sprintf("/api/v1/organizations/%s/members/%s", mockOrgID, mockMemberID), nil)
				req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", *validToken))
				return req
			},
			expectStatusCode: http.StatusOK,
			expectMessage:    "",
		},
		{
			name: "PUT /api/v1/organizations/:org_id/members/:member_id with correct permission successfully",
			mockFn: func(d *dependencies) {
				d.organizationHandler.On("UpdateMember", mock.Anything).Once()
				d.userUsecase.On("AuthorizePlatform", mock.Anything, mock.Anything, []enums.AppPermission{
					enums.AppPermission_OrgsEdit,
				}).Return(true, nil).Once()
				d.userUsecase.On("IsAdmin", mock.Anything, mock.Anything).Return(false, nil)
				d.userUsecase.On("AuthorizeOrg", mock.Anything, mock.Anything, mock.Anything, []enums.RepoPermission{
					enums.RepoPermission_Edit,
					enums.RepoPermission_MembersEdit,
				}).Return(true, nil).Once()
			},
			req: func() *http.Request {
				req := httptest.NewRequest(http.MethodPut, fmt.Sprintf("/api/v1/organizations/%s/members/%s", mockOrgID, mockMemberID), nil)
				req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", *validToken))
				return req
			},
			expectStatusCode: http.StatusOK,
			expectMessage:    "",
		},
		{
			name: "DELETE /api/v1/organizations/:org_id with correct permission successfully",
			mockFn: func(d *dependencies) {
				d.organizationHandler.On("DeleteOrganization", mock.Anything).Once()
				d.userUsecase.On("AuthorizePlatform", mock.Anything, mock.Anything, []enums.AppPermission{
					enums.AppPermission_OrgsDelete,
				}).Return(true, nil).Once()
				d.userUsecase.On("IsAdmin", mock.Anything, mock.Anything).Return(false, nil)
				d.userUsecase.On("AuthorizeOrg", mock.Anything, mock.Anything, mock.Anything, []enums.RepoPermission{
					enums.RepoPermission_Delete,
				}).Return(true, nil).Once()
			},
			req: func() *http.Request {
				req := httptest.NewRequest(http.MethodDelete, fmt.Sprintf("/api/v1/organizations/%s", mockOrgID), nil)
				req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", *validToken))
				return req
			},
			expectStatusCode: http.StatusOK,
			expectMessage:    "",
		},
		{
			name: "POST /api/v1/organizations/:org_id/invite with correct permission successfully",
			mockFn: func(d *dependencies) {
				d.organizationHandler.On("InviteUsers", mock.Anything).Once()
				d.userUsecase.On("AuthorizePlatform", mock.Anything, mock.Anything, []enums.AppPermission{
					enums.AppPermission_OrgsEdit,
				}).Return(true, nil).Once()
				d.userUsecase.On("IsAdmin", mock.Anything, mock.Anything).Return(false, nil)
				d.userUsecase.On("AuthorizeOrg", mock.Anything, mock.Anything, mock.Anything, []enums.RepoPermission{
					enums.RepoPermission_Edit,
					enums.RepoPermission_MembersEdit,
				}).Return(true, nil).Once()
			},
			req: func() *http.Request {
				req := httptest.NewRequest(http.MethodPost, fmt.Sprintf("/api/v1/organizations/%s/invite", mockOrgID), nil)
				req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", *validToken))
				return req
			},
			expectStatusCode: http.StatusOK,
			expectMessage:    "",
		},
		{
			name: "GET /api/v1/organizations/me with correct permission successfully",
			mockFn: func(d *dependencies) {
				d.organizationHandler.On("ListCurrentUserOrganizations", mock.Anything).Once()
				d.userUsecase.On("AuthorizePlatform", mock.Anything, mock.Anything, []enums.AppPermission{
					enums.AppPermission_OrgsRead,
				}).Return(true, nil).Once()
			},
			req: func() *http.Request {
				req := httptest.NewRequest(http.MethodGet, "/api/v1/organizations/me", nil)
				req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", *validToken))
				return req
			},
			expectStatusCode: http.StatusOK,
			expectMessage:    "",
		},
		{
			name: "GET /api/v1/organizations/:org_id with correct permission successfully",
			mockFn: func(d *dependencies) {
				d.organizationHandler.On("GetOrganization", mock.Anything).Once()
				d.userUsecase.On("AuthorizePlatform", mock.Anything, mock.Anything, []enums.AppPermission{
					enums.AppPermission_OrgsRead,
				}).Return(true, nil).Once()
			},
			req: func() *http.Request {
				req := httptest.NewRequest(http.MethodGet, fmt.Sprintf("/api/v1/organizations/%s", mockOrgID), nil)
				req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", *validToken))
				return req
			},
			expectStatusCode: http.StatusOK,
			expectMessage:    "",
		},
		{
			name: "GET /api/v1/organizations/:org_id/members with correct permission successfully",
			mockFn: func(d *dependencies) {
				d.organizationHandler.On("ListOrganizationMembers", mock.Anything).Once()
				d.userUsecase.On("AuthorizePlatform", mock.Anything, mock.Anything, []enums.AppPermission{
					enums.AppPermission_OrgsRead,
				}).Return(true, nil).Once()
			},
			req: func() *http.Request {
				req := httptest.NewRequest(http.MethodGet, fmt.Sprintf("/api/v1/organizations/%s/members", mockOrgID), nil)
				req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", *validToken))
				return req
			},
			expectStatusCode: http.StatusOK,
			expectMessage:    "",
		},
		{
			name: "GET /api/v1/organizations with correct permission successfully",
			mockFn: func(d *dependencies) {
				d.organizationHandler.On("ListAllOrganizations", mock.Anything).Once()
				d.userUsecase.On("AuthorizePlatform", mock.Anything, mock.Anything, []enums.AppPermission{
					enums.AppPermission_OrgsRead,
				}).Return(true, nil).Once()
			},
			req: func() *http.Request {
				req := httptest.NewRequest(http.MethodGet, "/api/v1/organizations", nil)
				req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", *validToken))
				return req
			},
			expectStatusCode: http.StatusOK,
			expectMessage:    "",
		},
		{
			name: "POST /api/v1/organizations/:org_id/avatar with correct permission successfully",
			mockFn: func(d *dependencies) {
				d.organizationHandler.On("UploadOrganizationAvatar", mock.Anything).Once()
				d.userUsecase.On("AuthorizePlatform", mock.Anything, mock.Anything, []enums.AppPermission{
					enums.AppPermission_OrgsRead,
					enums.AppPermission_OrgsEdit,
				}).Return(true, nil).Once()
			},
			req: func() *http.Request {
				req := httptest.NewRequest(http.MethodPost, fmt.Sprintf("/api/v1/organizations/%s/avatar", mockOrgID), nil)
				req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", *validToken))
				return req
			},
			expectStatusCode: http.StatusOK,
			expectMessage:    "",
		},
		{
			name: "DELETE /api/v1/organizations/:org_id/avatar with correct permission successfully",
			mockFn: func(d *dependencies) {
				d.organizationHandler.On("DeleteOrganizationAvatar", mock.Anything).Once()
				d.userUsecase.On("AuthorizePlatform", mock.Anything, mock.Anything, []enums.AppPermission{
					enums.AppPermission_OrgsRead,
					enums.AppPermission_OrgsEdit,
				}).Return(true, nil).Once()
			},
			req: func() *http.Request {
				req := httptest.NewRequest(http.MethodDelete, fmt.Sprintf("/api/v1/organizations/%s/avatar", mockOrgID), nil)
				req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", *validToken))
				return req
			},
			expectStatusCode: http.StatusOK,
			expectMessage:    "",
		},
	}

	d := &dependencies{
		organizationHandler:   &mocks.MockOrganizationHandler{},
		orgAccessTokenHandler: &mocks.MockOrgAccessTokenHandler{},
		userUsecase:           &userMocks.MockUserUsecase{},
	}
	d.userUsecase.On("UpsertUser", mock.Anything, mock.Anything).Return(nil)

	bearerAuth := middlewares.BearerAuth(jwtSecret, d.userUsecase)
	orgPermission := middlewares.OrgPermission(d.userUsecase)
	platformPermission := middlewares.PlatformPermission(d.userUsecase)

	r := gin.Default()
	v1 := r.Group("/api/v1")
	main.InitOrganization(
		v1,
		d.organizationHandler,
		d.orgAccessTokenHandler,
		bearerAuth,
		orgPermission,
		platformPermission,
	)

	for _, testcase := range testcases {
		t.Run(testcase.name, func(t *testing.T) {
			testcase.mockFn(d)
			rr := httptest.NewRecorder()
			r.ServeHTTP(rr, testcase.req())

			assert.Equal(t, testcase.expectStatusCode, rr.Code)
			assert.Equal(t, testcase.expectMessage, rr.Body.String())
		})
	}
}

func TestInitHardware(t *testing.T) {
	gin.SetMode(gin.TestMode)

	jwtSecret := "jwt_secret"
	validToken, _ := jwt.GenerateJWT(context.Background(), jwt.CustomClaims{}, []byte(jwtSecret), time.Duration(600)*time.Second)

	type dependencies struct {
		hardwareHandler *mocks.MockHardwareHandler
		userUsecase     *userMocks.MockUserUsecase
		orgUsecase      *orgMocks.MockOrganizationUsecase
	}

	testcases := []struct {
		name             string
		mockFn           func(r *dependencies)
		req              func() *http.Request
		expectStatusCode int
		expectMessage    string
	}{
		{
			name: "GET /api/v1/hardwares with correct permission successfully",
			mockFn: func(d *dependencies) {
				d.hardwareHandler.On("ListGPUNodes", mock.Anything).Once()
				d.userUsecase.On("AuthorizePlatform", mock.Anything, mock.Anything, []enums.AppPermission{
					enums.AppPermission_SpacesRead,
				}).Return(true, nil).Once()
			},
			req: func() *http.Request {
				req := httptest.NewRequest(http.MethodGet, "/api/v1/hardwares", nil)
				req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", *validToken))
				return req
			},
			expectStatusCode: http.StatusOK,
			expectMessage:    "",
		},
	}

	d := &dependencies{
		hardwareHandler: &mocks.MockHardwareHandler{},
		userUsecase:     &userMocks.MockUserUsecase{},
		orgUsecase:      &orgMocks.MockOrganizationUsecase{},
	}
	d.userUsecase.On("UpsertUser", mock.Anything, mock.Anything).Return(nil)

	bearerAuth := middlewares.BearerAuth(jwtSecret, d.userUsecase)
	platformPermission := middlewares.PlatformPermission(d.userUsecase)

	r := gin.Default()
	v1 := r.Group("/api/v1")
	main.InitMachine(
		v1,
		d.hardwareHandler,
		bearerAuth,
		platformPermission,
	)

	for _, testcase := range testcases {
		t.Run(testcase.name, func(t *testing.T) {
			testcase.mockFn(d)
			rr := httptest.NewRecorder()
			r.ServeHTTP(rr, testcase.req())

			assert.Equal(t, testcase.expectStatusCode, rr.Code)
			assert.Equal(t, testcase.expectMessage, rr.Body.String())
		})
	}
}
