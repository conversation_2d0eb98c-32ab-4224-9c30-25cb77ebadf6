package main

// import (
// 	"context"
// 	"fmt"
// 	"net/http"
// 	"net/http/httptest"
// 	"testing"
//
// 	"github.com/gin-gonic/gin"
// 	"github.com/google/uuid"
// 	"github.com/stretchr/testify/assert"
//
// 	"api-server/internal/enums"
// 	"api-server/internal/middlewares"
// 	"api-server/internal/types"
// 	"api-server/internal/usecase/user/mocks"
// )
//
// // User APIs
// func TestGetUsers(t *testing.T) {
// 	gin.SetMode(gin.TestMode)
// 	requiredPermission := []enums.AppPermission{
// 		enums.AppPermission_UsersRead,
// 	}
//
// 	mockUserID := uuid.New()
//
// 	// Test cases for different permission scenarios
// 	tests := []struct {
// 		name           string
// 		path           string
// 		mockFn         func(u *mocks.MockUserUsecase)
// 		setupRequest   func(req *http.Request)
// 		setupContext   func(c *gin.Context)
// 		expectBody     string
// 		expectHTTPCode int
// 	}{
// 		{
// 			name: "Full access with all required permissions",
// 			path: "/api/v1/users",
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, requiredPermission).Return(true, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 				c.Set(enums.USER_ID, mockUserID)
// 			},
// 			expectBody:     "",
// 			expectHTTPCode: http.StatusOK,
// 		},
// 		{
// 			name: "Forbidden - Missing UsersRead permission",
// 			path: "/api/v1/users",
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, requiredPermission).Return(false, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 				c.Set(enums.USER_ID, mockUserID)
// 			},
// 			expectBody:     "{\"code\":403,\"message\":\"No permission\"}",
// 			expectHTTPCode: http.StatusForbidden,
// 		},
// 		{
// 			name: "Forbidden - Has other permissions but not UsersRead",
// 			path: "/api/v1/users",
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, requiredPermission).Return(false, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 				c.Set(enums.USER_ID, mockUserID)
// 			},
// 			expectBody:     "{\"code\":403,\"message\":\"No permission\"}",
// 			expectHTTPCode: http.StatusForbidden,
// 		},
// 	}
//
// 	// Setup mock user usecase
// 	u := &mocks.MockUserUsecase{}
//
// 	// Setup Gin router with middleware chain
// 	r := gin.Default()
// 	r.GET(("/api/v1/users"),
// 		func(c *gin.Context) {
// 			c.Set(enums.USER_ID, mockUserID)
// 			c.Next()
// 		},
// 		middlewares.PlatformPermission(u)(
// 			requiredPermission...,
// 		),
// 	)
//
// 	for _, testcase := range tests {
// 		t.Run(testcase.name, func(t *testing.T) {
// 			testcase.mockFn(u)
//
// 			// Create test request
// 			req := httptest.NewRequest(http.MethodGet, testcase.path, nil)
// 			testcase.setupRequest(req)
//
// 			// Execute request
// 			rr := httptest.NewRecorder()
// 			r.ServeHTTP(rr, req)
//
// 			// Assertions
// 			assert.Equal(t, testcase.expectHTTPCode, rr.Code)
// 			assert.Equal(t, testcase.expectBody, rr.Body.String())
// 		})
// 	}
// }
//
// func TestUpdateUser(t *testing.T) {
// 	gin.SetMode(gin.TestMode)
// 	requiredPermission := []enums.AppPermission{
// 		enums.AppPermission_UsersEdit,
// 	}
//
// 	mockUserID := uuid.New()
//
// 	// Test cases for different permission scenarios
// 	tests := []struct {
// 		name           string
// 		path           string
// 		mockFn         func(u *mocks.MockUserUsecase)
// 		setupRequest   func(req *http.Request)
// 		setupContext   func(c *gin.Context)
// 		expectBody     string
// 		expectHTTPCode int
// 	}{
// 		{
// 			name: "Full access with all required permissions",
// 			path: "/api/v1/users/:user_id/role",
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, requiredPermission).Return(true, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 				c.Set(enums.USER_ID, mockUserID)
// 			},
// 			expectBody:     "",
// 			expectHTTPCode: http.StatusOK,
// 		},
// 		{
// 			name: "Forbidden - Missing UsersEdit permission",
// 			path: "/api/v1/users/:user_id/role",
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, requiredPermission).Return(false, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 				c.Set(enums.USER_ID, mockUserID)
// 			},
// 			expectBody:     "{\"code\":403,\"message\":\"No permission\"}",
// 			expectHTTPCode: http.StatusForbidden,
// 		},
// 		{
// 			name: "Forbidden - Has UsersRead but not UsersEdit permission",
// 			path: "/api/v1/users/:user_id/role",
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, requiredPermission).Return(false, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 				c.Set(enums.USER_ID, mockUserID)
// 			},
// 			expectBody:     "{\"code\":403,\"message\":\"No permission\"}",
// 			expectHTTPCode: http.StatusForbidden,
// 		},
// 	}
//
// 	// Setup mock user usecase
// 	u := &mocks.MockUserUsecase{}
//
// 	// Setup Gin router with middleware chain
// 	r := gin.Default()
// 	r.PATCH(("/api/v1/users/:user_id/role"),
// 		func(c *gin.Context) {
// 			c.Set(enums.USER_ID, mockUserID)
// 			c.Next()
// 		},
// 		middlewares.PlatformPermission(u)(
// 			requiredPermission...,
// 		),
// 	)
//
// 	for _, testcase := range tests {
// 		t.Run(testcase.name, func(t *testing.T) {
// 			testcase.mockFn(u)
//
// 			// Create test request
// 			req := httptest.NewRequest(http.MethodPatch, testcase.path, nil)
// 			testcase.setupRequest(req)
//
// 			// Execute request
// 			rr := httptest.NewRecorder()
// 			r.ServeHTTP(rr, req)
//
// 			// Assertions
// 			assert.Equal(t, testcase.expectHTTPCode, rr.Code)
// 			assert.Equal(t, testcase.expectBody, rr.Body.String())
// 		})
// 	}
// }
//
// func TestDeleteUser(t *testing.T) {
// 	gin.SetMode(gin.TestMode)
//
// 	mockUserID := uuid.New()
// 	requiredPermission := []enums.AppPermission{
// 		enums.AppPermission_UsersDelete,
// 	}
//
// 	// Test cases for different permission scenarios
// 	tests := []struct {
// 		name           string
// 		path           string
// 		mockFn         func(u *mocks.MockUserUsecase)
// 		setupRequest   func(req *http.Request)
// 		setupContext   func(c *gin.Context)
// 		expectBody     string
// 		expectHTTPCode int
// 	}{
// 		{
// 			name: "Full access with all required permissions",
// 			path: "/api/v1/users/:user_id",
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, requiredPermission).Return(true, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 				c.Set(enums.USER_ID, mockUserID)
// 			},
// 			expectBody:     "",
// 			expectHTTPCode: http.StatusOK,
// 		},
// 		{
// 			name: "Forbidden - Missing UsersDelete permission",
// 			path: "/api/v1/users/:user_id",
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, requiredPermission).Return(false, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 				c.Set(enums.USER_ID, mockUserID)
// 			},
// 			expectBody:     "{\"code\":403,\"message\":\"No permission\"}",
// 			expectHTTPCode: http.StatusForbidden,
// 		},
// 		{
// 			name: "Forbidden - Has UsersRead but not UsersDelete permission",
// 			path: "/api/v1/users/:user_id",
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, requiredPermission).Return(false, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 				c.Set(enums.USER_ID, mockUserID)
// 			},
// 			expectBody:     "{\"code\":403,\"message\":\"No permission\"}",
// 			expectHTTPCode: http.StatusForbidden,
// 		},
// 	}
//
// 	// Setup mock user usecase
// 	u := &mocks.MockUserUsecase{}
//
// 	// Setup Gin router with middleware chain
// 	r := gin.Default()
// 	r.DELETE(("/api/v1/users/:user_id"),
// 		func(c *gin.Context) {
// 			c.Set(enums.USER_ID, mockUserID)
// 			c.Next()
// 		},
// 		middlewares.PlatformPermission(u)(
// 			requiredPermission...,
// 		),
// 	)
//
// 	for _, testcase := range tests {
// 		t.Run(testcase.name, func(t *testing.T) {
// 			testcase.mockFn(u)
//
// 			// Create test request
// 			req := httptest.NewRequest(http.MethodDelete, testcase.path, nil)
// 			testcase.setupRequest(req)
//
// 			// Execute request
// 			rr := httptest.NewRecorder()
// 			r.ServeHTTP(rr, req)
//
// 			// Assertions
// 			assert.Equal(t, testcase.expectHTTPCode, rr.Code)
// 			assert.Equal(t, testcase.expectBody, rr.Body.String())
// 		})
// 	}
// }
//
// func TestAPIGetRepository(t *testing.T) {
// 	gin.SetMode(gin.TestMode)
// 	requiredPermission := []enums.AppPermission{
// 		enums.AppPermission_SpacesRead,
// 		enums.AppPermission_ModelsRead,
// 		enums.AppPermission_DatasetsRead,
// 	}
// 	repoType := enums.RepoType_Models
// 	namespace := "namespace"
// 	repoName := "repo-name"
//
// 	mockUserID := uuid.New()
//
// 	// Test cases for different permission scenarios
// 	tests := []struct {
// 		name           string
// 		path           string
// 		mockFn         func(u *mocks.MockUserUsecase)
// 		setupRequest   func(req *http.Request)
// 		setupContext   func(c *gin.Context)
// 		expectBody     string
// 		expectHTTPCode int
// 	}{
// 		{
// 			name: "Full access with all required permissions",
// 			path: fmt.Sprintf("/api/v1/repositories/%s/%s/%s", repoType, namespace, repoName),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, requiredPermission).Return(true, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 				c.Set(enums.USER_ID, mockUserID)
// 				c.Params = gin.Params{
// 					{Key: enums.REPO_TYPE, Value: repoType.String()},
// 					{Key: enums.NAMESPACE, Value: namespace},
// 					{Key: enums.REPO_NAME, Value: repoName},
// 				}
// 			},
// 			expectBody:     "",
// 			expectHTTPCode: http.StatusOK,
// 		},
// 		{
// 			name: "Forbidden - Missing all required permissions",
// 			path: fmt.Sprintf("/api/v1/repositories/%s/%s/%s", repoType, namespace, repoName),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, requiredPermission).Return(false, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 			},
// 			expectBody:     "{\"code\":403,\"message\":\"No permission\"}",
// 			expectHTTPCode: http.StatusForbidden,
// 		},
// 		{
// 			name: "Forbidden - Missing one of required permissions",
// 			path: fmt.Sprintf("/api/v1/repositories/%s/%s/%s", repoType, namespace, repoName),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, requiredPermission).Return(false, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 			},
// 			expectBody:     "{\"code\":403,\"message\":\"No permission\"}",
// 			expectHTTPCode: http.StatusForbidden,
// 		},
// 	}
//
// 	// Setup mock user usecase
// 	u := &mocks.MockUserUsecase{}
//
// 	// Setup Gin router with middleware chain
// 	r := gin.Default()
// 	r.GET(fmt.Sprintf("/api/v1/repositories/%s/%s/%s", repoType, namespace, repoName),
// 		func(c *gin.Context) {
// 			c.Set(enums.USER_ID, mockUserID)
// 			c.Params = gin.Params{
// 				{Key: enums.REPO_TYPE, Value: repoType.String()},
// 				{Key: enums.NAMESPACE, Value: namespace},
// 				{Key: enums.REPO_NAME, Value: repoName},
// 			}
// 			c.Next()
// 		},
// 		middlewares.PlatformPermission(u)(
// 			requiredPermission...,
// 		),
// 	)
//
// 	for _, testcase := range tests {
// 		t.Run(testcase.name, func(t *testing.T) {
// 			testcase.mockFn(u)
//
// 			// Create test request
// 			req := httptest.NewRequest(http.MethodGet, testcase.path, nil)
// 			testcase.setupRequest(req)
//
// 			// Execute request
// 			rr := httptest.NewRecorder()
// 			r.ServeHTTP(rr, req)
//
// 			// Assertions
// 			assert.Equal(t, testcase.expectHTTPCode, rr.Code)
// 			assert.Equal(t, testcase.expectBody, rr.Body.String())
// 		})
// 	}
// }
//
// func TestAPICreateRepository(t *testing.T) {
// 	gin.SetMode(gin.TestMode)
// 	requiredPermission := []enums.AppPermission{
// 		enums.AppPermission_SpacesAdd,
// 		enums.AppPermission_ModelsAdd,
// 		enums.AppPermission_DatasetsAdd,
// 	}
//
// 	mockUserID := uuid.New()
//
// 	// Test cases for different permission scenarios
// 	tests := []struct {
// 		name           string
// 		path           string
// 		mockFn         func(u *mocks.MockUserUsecase)
// 		setupRequest   func(req *http.Request)
// 		setupContext   func(c *gin.Context)
// 		expectBody     string
// 		expectHTTPCode int
// 	}{
// 		{
// 			name: "Full access with all required permissions",
// 			path: "/api/v1/repositories",
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, requiredPermission).Return(true, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 				c.Set(enums.USER_ID, mockUserID)
// 			},
// 			expectBody:     "",
// 			expectHTTPCode: http.StatusOK,
// 		},
// 		{
// 			name: "Forbidden - Missing all required permissions",
// 			path: "/api/v1/repositories",
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, requiredPermission).Return(false, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 			},
// 			expectBody:     "{\"code\":403,\"message\":\"No permission\"}",
// 			expectHTTPCode: http.StatusForbidden,
// 		},
// 		{
// 			name: "Forbidden - Missing one of required permissions",
// 			path: "/api/v1/repositories",
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, requiredPermission).Return(false, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 			},
// 			expectBody:     "{\"code\":403,\"message\":\"No permission\"}",
// 			expectHTTPCode: http.StatusForbidden,
// 		},
// 	}
//
// 	// Setup mock user usecase
// 	u := &mocks.MockUserUsecase{}
//
// 	// Setup Gin router with middleware chain
// 	r := gin.Default()
// 	r.POST("/api/v1/repositories",
// 		func(c *gin.Context) {
// 			c.Set(enums.USER_ID, mockUserID)
// 			c.Next()
// 		},
// 		middlewares.PlatformPermission(u)(
// 			requiredPermission...,
// 		),
// 	)
//
// 	for _, testcase := range tests {
// 		t.Run(testcase.name, func(t *testing.T) {
// 			testcase.mockFn(u)
//
// 			// Create test request
// 			req := httptest.NewRequest(http.MethodPost, testcase.path, nil)
// 			testcase.setupRequest(req)
//
// 			// Execute request
// 			rr := httptest.NewRecorder()
// 			r.ServeHTTP(rr, req)
//
// 			// Assertions
// 			assert.Equal(t, testcase.expectHTTPCode, rr.Code)
// 			assert.Equal(t, testcase.expectBody, rr.Body.String())
// 		})
// 	}
// }
//
// func TestAPIGetListRepository(t *testing.T) {
// 	gin.SetMode(gin.TestMode)
// 	requiredPermission := []enums.AppPermission{
// 		enums.AppPermission_SpacesRead,
// 		enums.AppPermission_ModelsRead,
// 		enums.AppPermission_DatasetsRead,
// 	}
//
// 	mockUserID := uuid.New()
//
// 	// Test cases for different permission scenarios
// 	tests := []struct {
// 		name           string
// 		path           string
// 		mockFn         func(u *mocks.MockUserUsecase)
// 		setupRequest   func(req *http.Request)
// 		setupContext   func(c *gin.Context)
// 		expectBody     string
// 		expectHTTPCode int
// 	}{
// 		{
// 			name: "Full access with all required permissions",
// 			path: "/api/v1/repositories",
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, requiredPermission).Return(true, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 				c.Set(enums.USER_ID, mockUserID)
// 			},
// 			expectBody:     "",
// 			expectHTTPCode: http.StatusOK,
// 		},
// 		{
// 			name: "Forbidden - Missing all required permissions",
// 			path: "/api/v1/repositories",
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, requiredPermission).Return(false, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 			},
// 			expectBody:     "{\"code\":403,\"message\":\"No permission\"}",
// 			expectHTTPCode: http.StatusForbidden,
// 		},
// 		{
// 			name: "Forbidden - Missing one of required permissions",
// 			path: "/api/v1/repositories",
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, requiredPermission).Return(false, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 			},
// 			expectBody:     "{\"code\":403,\"message\":\"No permission\"}",
// 			expectHTTPCode: http.StatusForbidden,
// 		},
// 	}
//
// 	// Setup mock user usecase
// 	u := &mocks.MockUserUsecase{}
//
// 	// Setup Gin router with middleware chain
// 	r := gin.Default()
// 	r.GET("/api/v1/repositories",
// 		func(c *gin.Context) {
// 			c.Set(enums.USER_ID, mockUserID)
// 			c.Next()
// 		},
// 		middlewares.PlatformPermission(u)(
// 			requiredPermission...,
// 		),
// 	)
//
// 	for _, testcase := range tests {
// 		t.Run(testcase.name, func(t *testing.T) {
// 			testcase.mockFn(u)
//
// 			// Create test request
// 			req := httptest.NewRequest(http.MethodGet, testcase.path, nil)
// 			testcase.setupRequest(req)
//
// 			// Execute request
// 			rr := httptest.NewRecorder()
// 			r.ServeHTTP(rr, req)
//
// 			// Assertions
// 			assert.Equal(t, testcase.expectHTTPCode, rr.Code)
// 			assert.Equal(t, testcase.expectBody, rr.Body.String())
// 		})
// 	}
// }
//
// func TestAPIDeleteRepository(t *testing.T) {
// 	gin.SetMode(gin.TestMode)
// 	platformPermission := []enums.AppPermission{
// 		enums.AppPermission_SpacesRead,
// 	}
//
// 	repoPermission := []enums.RepoPermission{
// 		enums.RepoPermission_Delete,
// 	}
//
// 	mockUserID := uuid.New()
// 	repoType := enums.RepoType_Models
// 	namespace := "namespace"
// 	repoName := "repo-name"
// 	repoId := *types.NewRepoID(repoType, namespace, repoName)
//
// 	// Test cases for different permission scenarios
// 	tests := []struct {
// 		name           string
// 		path           string
// 		mockFn         func(u *mocks.MockUserUsecase)
// 		setupRequest   func(req *http.Request)
// 		setupContext   func(c *gin.Context)
// 		expectBody     string
// 		expectHTTPCode int
// 	}{
// 		{
// 			name: "Full access with all required permissions",
// 			path: fmt.Sprintf("/api/v1/repositories/%s/%s/%s", repoType, namespace, repoName),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, platformPermission).Return(true, nil).Once()
// 				u.On("AuthorizeRepo", context.Background(), mockUserID, repoId, repoPermission).Return(true, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 				c.Set(enums.USER_ID, mockUserID)
// 			},
// 			expectBody:     "",
// 			expectHTTPCode: http.StatusOK,
// 		},
// 		{
// 			name: "Forbidden - Missing platform required permissions",
// 			path: fmt.Sprintf("/api/v1/repositories/%s/%s/%s", repoType, namespace, repoName),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, platformPermission).Return(false, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 			},
// 			expectBody:     "{\"code\":403,\"message\":\"No permission\"}",
// 			expectHTTPCode: http.StatusForbidden,
// 		},
// 		{
// 			name: "Forbidden - Missing repository required permissions",
// 			path: fmt.Sprintf("/api/v1/repositories/%s/%s/%s", repoType, namespace, repoName),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, platformPermission).Return(true, nil).Once()
// 				u.On("AuthorizeRepo", context.Background(), mockUserID, repoId, repoPermission).Return(false, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 			},
// 			expectBody:     "{\"code\":403,\"message\":\"No permission\"}",
// 			expectHTTPCode: http.StatusForbidden,
// 		},
// 	}
//
// 	// Setup mock user usecase
// 	u := &mocks.MockUserUsecase{}
//
// 	// Setup Gin router with middleware chain
// 	r := gin.Default()
// 	r.DELETE("/api/v1/repositories/:repo_type/:namespace/:repo_name",
// 		func(c *gin.Context) {
// 			c.Set(enums.USER_ID, mockUserID)
// 			c.Next()
// 		},
// 		middlewares.PlatformPermission(u)(
// 			platformPermission...,
// 		),
// 		middlewares.RepoPermission(u)(
// 			repoPermission...,
// 		),
// 	)
//
// 	for _, testcase := range tests {
// 		t.Run(testcase.name, func(t *testing.T) {
// 			testcase.mockFn(u)
//
// 			// Create test request
// 			req := httptest.NewRequest(http.MethodDelete, testcase.path, nil)
// 			testcase.setupRequest(req)
//
// 			// Execute request
// 			rr := httptest.NewRecorder()
// 			r.ServeHTTP(rr, req)
//
// 			// Assertions
// 			assert.Equal(t, testcase.expectHTTPCode, rr.Code)
// 			assert.Equal(t, testcase.expectBody, rr.Body.String())
// 		})
// 	}
// }
//
// func TestAPIArchiveRepository(t *testing.T) {
// 	gin.SetMode(gin.TestMode)
// 	platformPermission := []enums.AppPermission{
// 		enums.AppPermission_SpacesRead,
// 		enums.AppPermission_ModelsRead,
// 		enums.AppPermission_DatasetsRead,
// 	}
//
// 	mockUserID := uuid.New()
// 	repoType := enums.RepoType_Models
// 	namespace := "namespace"
// 	repoName := "repo-name"
//
// 	// Test cases for different permission scenarios
// 	tests := []struct {
// 		name           string
// 		path           string
// 		mockFn         func(u *mocks.MockUserUsecase)
// 		setupRequest   func(req *http.Request)
// 		setupContext   func(c *gin.Context)
// 		expectBody     string
// 		expectHTTPCode int
// 	}{
// 		{
// 			name: "Full access with all required permissions",
// 			path: fmt.Sprintf("/api/v1/repositories/%s/%s/%s/archive", repoType, namespace, repoName),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, platformPermission).Return(true, nil).Once()
//
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 				c.Set(enums.USER_ID, mockUserID)
// 			},
// 			expectBody:     "",
// 			expectHTTPCode: http.StatusOK,
// 		},
// 		{
// 			name: "Forbidden - Missing platform required permissions",
// 			path: fmt.Sprintf("/api/v1/repositories/%s/%s/%s/archive", repoType, namespace, repoName),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, platformPermission).Return(false, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 			},
// 			expectBody:     "{\"code\":403,\"message\":\"No permission\"}",
// 			expectHTTPCode: http.StatusForbidden,
// 		},
// 	}
//
// 	// Setup mock user usecase
// 	u := &mocks.MockUserUsecase{}
//
// 	// Setup Gin router with middleware chain
// 	r := gin.Default()
// 	r.GET("/api/v1/repositories/:repo_type/:namespace/:repo_name/archive",
// 		func(c *gin.Context) {
// 			c.Set(enums.USER_ID, mockUserID)
// 			c.Next()
// 		},
// 		middlewares.PlatformPermission(u)(
// 			platformPermission...,
// 		),
// 	)
//
// 	for _, testcase := range tests {
// 		t.Run(testcase.name, func(t *testing.T) {
// 			testcase.mockFn(u)
//
// 			// Create test request
// 			req := httptest.NewRequest(http.MethodGet, testcase.path, nil)
// 			testcase.setupRequest(req)
//
// 			// Execute request
// 			rr := httptest.NewRecorder()
// 			r.ServeHTTP(rr, req)
//
// 			// Assertions
// 			assert.Equal(t, testcase.expectHTTPCode, rr.Code)
// 			assert.Equal(t, testcase.expectBody, rr.Body.String())
// 		})
// 	}
// }
//
// func TestAPIUploadAvatar(t *testing.T) {
// 	gin.SetMode(gin.TestMode)
// 	platformPermission := []enums.AppPermission{
// 		enums.AppPermission_SpacesRead,
// 	}
//
// 	repoPermission := []enums.RepoPermission{
// 		enums.RepoPermission_Edit,
// 	}
//
// 	mockUserID := uuid.New()
// 	repoType := enums.RepoType_Models
// 	namespace := "namespace"
// 	repoName := "repo-name"
// 	repoId := *types.NewRepoID(repoType, namespace, repoName)
//
// 	// Test cases for different permission scenarios
// 	tests := []struct {
// 		name           string
// 		path           string
// 		mockFn         func(u *mocks.MockUserUsecase)
// 		setupRequest   func(req *http.Request)
// 		setupContext   func(c *gin.Context)
// 		expectBody     string
// 		expectHTTPCode int
// 	}{
// 		{
// 			name: "Full access with all required permissions",
// 			path: fmt.Sprintf("/api/v1/repositories/%s/%s/%s/avatar", repoType, namespace, repoName),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, platformPermission).Return(true, nil).Once()
// 				u.On("AuthorizeRepo", context.Background(), mockUserID, repoId, repoPermission).Return(true, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 				c.Set(enums.USER_ID, mockUserID)
// 			},
// 			expectBody:     "",
// 			expectHTTPCode: http.StatusOK,
// 		},
// 		{
// 			name: "Forbidden - Missing platform required permissions",
// 			path: fmt.Sprintf("/api/v1/repositories/%s/%s/%s/avatar", repoType, namespace, repoName),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, platformPermission).Return(false, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 			},
// 			expectBody:     "{\"code\":403,\"message\":\"No permission\"}",
// 			expectHTTPCode: http.StatusForbidden,
// 		},
// 		{
// 			name: "Forbidden - Missing repository required permissions",
// 			path: fmt.Sprintf("/api/v1/repositories/%s/%s/%s/avatar", repoType, namespace, repoName),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, platformPermission).Return(true, nil).Once()
// 				u.On("AuthorizeRepo", context.Background(), mockUserID, repoId, repoPermission).Return(false, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 			},
// 			expectBody:     "{\"code\":403,\"message\":\"No permission\"}",
// 			expectHTTPCode: http.StatusForbidden,
// 		},
// 	}
//
// 	// Setup mock user usecase
// 	u := &mocks.MockUserUsecase{}
//
// 	// Setup Gin router with middleware chain
// 	r := gin.Default()
// 	r.POST("/api/v1/repositories/:repo_type/:namespace/:repo_name/avatar",
// 		func(c *gin.Context) {
// 			c.Set(enums.USER_ID, mockUserID)
// 			c.Next()
// 		},
// 		middlewares.PlatformPermission(u)(
// 			platformPermission...,
// 		),
// 		middlewares.RepoPermission(u)(
// 			repoPermission...,
// 		),
// 	)
//
// 	for _, testcase := range tests {
// 		t.Run(testcase.name, func(t *testing.T) {
// 			testcase.mockFn(u)
//
// 			// Create test request
// 			req := httptest.NewRequest(http.MethodPost, testcase.path, nil)
// 			testcase.setupRequest(req)
//
// 			// Execute request
// 			rr := httptest.NewRecorder()
// 			r.ServeHTTP(rr, req)
//
// 			// Assertions
// 			assert.Equal(t, testcase.expectHTTPCode, rr.Code)
// 			assert.Equal(t, testcase.expectBody, rr.Body.String())
// 		})
// 	}
// }
//
// func TestAPIDeleteAvatar(t *testing.T) {
// 	gin.SetMode(gin.TestMode)
// 	platformPermission := []enums.AppPermission{
// 		enums.AppPermission_SpacesRead,
// 	}
//
// 	repoPermission := []enums.RepoPermission{
// 		enums.RepoPermission_Edit,
// 	}
//
// 	mockUserID := uuid.New()
// 	repoType := enums.RepoType_Models
// 	namespace := "namespace"
// 	repoName := "repo-name"
// 	repoId := *types.NewRepoID(repoType, namespace, repoName)
//
// 	// Test cases for different permission scenarios
// 	tests := []struct {
// 		name           string
// 		path           string
// 		mockFn         func(u *mocks.MockUserUsecase)
// 		setupRequest   func(req *http.Request)
// 		setupContext   func(c *gin.Context)
// 		expectBody     string
// 		expectHTTPCode int
// 	}{
// 		{
// 			name: "Full access with all required permissions",
// 			path: fmt.Sprintf("/api/v1/repositories/%s/%s/%s/avatar", repoType, namespace, repoName),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, platformPermission).Return(true, nil).Once()
// 				u.On("AuthorizeRepo", context.Background(), mockUserID, repoId, repoPermission).Return(true, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 				c.Set(enums.USER_ID, mockUserID)
// 			},
// 			expectBody:     "",
// 			expectHTTPCode: http.StatusOK,
// 		},
// 		{
// 			name: "Forbidden - Missing platform required permissions",
// 			path: fmt.Sprintf("/api/v1/repositories/%s/%s/%s/avatar", repoType, namespace, repoName),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, platformPermission).Return(false, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 			},
// 			expectBody:     "{\"code\":403,\"message\":\"No permission\"}",
// 			expectHTTPCode: http.StatusForbidden,
// 		},
// 		{
// 			name: "Forbidden - Missing repository required permissions",
// 			path: fmt.Sprintf("/api/v1/repositories/%s/%s/%s/avatar", repoType, namespace, repoName),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, platformPermission).Return(true, nil).Once()
// 				u.On("AuthorizeRepo", context.Background(), mockUserID, repoId, repoPermission).Return(false, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 			},
// 			expectBody:     "{\"code\":403,\"message\":\"No permission\"}",
// 			expectHTTPCode: http.StatusForbidden,
// 		},
// 	}
//
// 	// Setup mock user usecase
// 	u := &mocks.MockUserUsecase{}
//
// 	// Setup Gin router with middleware chain
// 	r := gin.Default()
// 	r.DELETE("/api/v1/repositories/:repo_type/:namespace/:repo_name/avatar",
// 		func(c *gin.Context) {
// 			c.Set(enums.USER_ID, mockUserID)
// 			c.Next()
// 		},
// 		middlewares.PlatformPermission(u)(
// 			platformPermission...,
// 		),
// 		middlewares.RepoPermission(u)(
// 			repoPermission...,
// 		),
// 	)
//
// 	for _, testcase := range tests {
// 		t.Run(testcase.name, func(t *testing.T) {
// 			testcase.mockFn(u)
//
// 			// Create test request
// 			req := httptest.NewRequest(http.MethodDelete, testcase.path, nil)
// 			testcase.setupRequest(req)
//
// 			// Execute request
// 			rr := httptest.NewRecorder()
// 			r.ServeHTTP(rr, req)
//
// 			// Assertions
// 			assert.Equal(t, testcase.expectHTTPCode, rr.Code)
// 			assert.Equal(t, testcase.expectBody, rr.Body.String())
// 		})
// 	}
// }
//
// func TestAPIGetContributor(t *testing.T) {
// 	gin.SetMode(gin.TestMode)
// 	platformPermission := []enums.AppPermission{
// 		enums.AppPermission_SpacesRead,
// 	}
//
// 	mockUserID := uuid.New()
// 	repoType := enums.RepoType_Models
// 	namespace := "namespace"
// 	repoName := "repo-name"
//
// 	// Test cases for different permission scenarios
// 	tests := []struct {
// 		name           string
// 		path           string
// 		mockFn         func(u *mocks.MockUserUsecase)
// 		setupRequest   func(req *http.Request)
// 		setupContext   func(c *gin.Context)
// 		expectBody     string
// 		expectHTTPCode int
// 	}{
// 		{
// 			name: "Full access with all required permissions",
// 			path: fmt.Sprintf("/api/v1/repositories/%s/%s/%s/contributors", repoType, namespace, repoName),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, platformPermission).Return(true, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 				c.Set(enums.USER_ID, mockUserID)
// 			},
// 			expectBody:     "",
// 			expectHTTPCode: http.StatusOK,
// 		},
// 		{
// 			name: "Forbidden - Missing platform required permissions",
// 			path: fmt.Sprintf("/api/v1/repositories/%s/%s/%s/contributors", repoType, namespace, repoName),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, platformPermission).Return(false, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 			},
// 			expectBody:     "{\"code\":403,\"message\":\"No permission\"}",
// 			expectHTTPCode: http.StatusForbidden,
// 		},
// 	}
//
// 	// Setup mock user usecase
// 	u := &mocks.MockUserUsecase{}
//
// 	// Setup Gin router with middleware chain
// 	r := gin.Default()
// 	r.GET("/api/v1/repositories/:repo_type/:namespace/:repo_name/contributors",
// 		func(c *gin.Context) {
// 			c.Set(enums.USER_ID, mockUserID)
// 			c.Next()
// 		},
// 		middlewares.PlatformPermission(u)(
// 			platformPermission...,
// 		),
// 	)
//
// 	for _, testcase := range tests {
// 		t.Run(testcase.name, func(t *testing.T) {
// 			testcase.mockFn(u)
//
// 			// Create test request
// 			req := httptest.NewRequest(http.MethodGet, testcase.path, nil)
// 			testcase.setupRequest(req)
//
// 			// Execute request
// 			rr := httptest.NewRecorder()
// 			r.ServeHTTP(rr, req)
//
// 			// Assertions
// 			assert.Equal(t, testcase.expectHTTPCode, rr.Code)
// 			assert.Equal(t, testcase.expectBody, rr.Body.String())
// 		})
// 	}
// }
//
// func TestAPIInviteMember(t *testing.T) {
// 	gin.SetMode(gin.TestMode)
// 	platformPermission := []enums.AppPermission{
// 		enums.AppPermission_SpacesRead,
// 	}
//
// 	mockUserID := uuid.New()
// 	repoType := enums.RepoType_Models
// 	namespace := "namespace"
// 	repoName := "repo-name"
//
// 	// Test cases for different permission scenarios
// 	tests := []struct {
// 		name           string
// 		path           string
// 		mockFn         func(u *mocks.MockUserUsecase)
// 		setupRequest   func(req *http.Request)
// 		setupContext   func(c *gin.Context)
// 		expectBody     string
// 		expectHTTPCode int
// 	}{
// 		{
// 			name: "Full access with all required permissions",
// 			path: fmt.Sprintf("/api/v1/repositories/%s/%s/%s/invite", repoType, namespace, repoName),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, platformPermission).Return(true, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 				c.Set(enums.USER_ID, mockUserID)
// 			},
// 			expectBody:     "",
// 			expectHTTPCode: http.StatusOK,
// 		},
// 		{
// 			name: "Forbidden - Missing platform required permissions",
// 			path: fmt.Sprintf("/api/v1/repositories/%s/%s/%s/invite", repoType, namespace, repoName),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, platformPermission).Return(false, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 			},
// 			expectBody:     "{\"code\":403,\"message\":\"No permission\"}",
// 			expectHTTPCode: http.StatusForbidden,
// 		},
// 	}
//
// 	// Setup mock user usecase
// 	u := &mocks.MockUserUsecase{}
//
// 	// Setup Gin router with middleware chain
// 	r := gin.Default()
// 	r.POST("/api/v1/repositories/:repo_type/:namespace/:repo_name/invite",
// 		func(c *gin.Context) {
// 			c.Set(enums.USER_ID, mockUserID)
// 			c.Next()
// 		},
// 		middlewares.PlatformPermission(u)(
// 			platformPermission...,
// 		),
// 	)
//
// 	for _, testcase := range tests {
// 		t.Run(testcase.name, func(t *testing.T) {
// 			testcase.mockFn(u)
//
// 			// Create test request
// 			req := httptest.NewRequest(http.MethodPost, testcase.path, nil)
// 			testcase.setupRequest(req)
//
// 			// Execute request
// 			rr := httptest.NewRecorder()
// 			r.ServeHTTP(rr, req)
//
// 			// Assertions
// 			assert.Equal(t, testcase.expectHTTPCode, rr.Code)
// 			assert.Equal(t, testcase.expectBody, rr.Body.String())
// 		})
// 	}
// }
//
// func TestAPIGetMembers(t *testing.T) {
// 	gin.SetMode(gin.TestMode)
// 	platformPermission := []enums.AppPermission{
// 		enums.AppPermission_SpacesRead,
// 	}
//
// 	mockUserID := uuid.New()
// 	repoType := enums.RepoType_Models
// 	namespace := "namespace"
// 	repoName := "repo-name"
//
// 	// Test cases for different permission scenarios
// 	tests := []struct {
// 		name           string
// 		path           string
// 		mockFn         func(u *mocks.MockUserUsecase)
// 		setupRequest   func(req *http.Request)
// 		setupContext   func(c *gin.Context)
// 		expectBody     string
// 		expectHTTPCode int
// 	}{
// 		{
// 			name: "Full access with all required permissions",
// 			path: fmt.Sprintf("/api/v1/repositories/%s/%s/%s/members", repoType, namespace, repoName),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, platformPermission).Return(true, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 				c.Set(enums.USER_ID, mockUserID)
// 			},
// 			expectBody:     "",
// 			expectHTTPCode: http.StatusOK,
// 		},
// 		{
// 			name: "Forbidden - Missing platform required permissions",
// 			path: fmt.Sprintf("/api/v1/repositories/%s/%s/%s/members", repoType, namespace, repoName),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, platformPermission).Return(false, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 			},
// 			expectBody:     "{\"code\":403,\"message\":\"No permission\"}",
// 			expectHTTPCode: http.StatusForbidden,
// 		},
// 	}
//
// 	// Setup mock user usecase
// 	u := &mocks.MockUserUsecase{}
//
// 	// Setup Gin router with middleware chain
// 	r := gin.Default()
// 	r.GET("/api/v1/repositories/:repo_type/:namespace/:repo_name/members",
// 		func(c *gin.Context) {
// 			c.Set(enums.USER_ID, mockUserID)
// 			c.Next()
// 		},
// 		middlewares.PlatformPermission(u)(
// 			platformPermission...,
// 		),
// 	)
//
// 	for _, testcase := range tests {
// 		t.Run(testcase.name, func(t *testing.T) {
// 			testcase.mockFn(u)
//
// 			// Create test request
// 			req := httptest.NewRequest(http.MethodGet, testcase.path, nil)
// 			testcase.setupRequest(req)
//
// 			// Execute request
// 			rr := httptest.NewRecorder()
// 			r.ServeHTTP(rr, req)
//
// 			// Assertions
// 			assert.Equal(t, testcase.expectHTTPCode, rr.Code)
// 			assert.Equal(t, testcase.expectBody, rr.Body.String())
// 		})
// 	}
// }
//
// func TestAPIGetMemberById(t *testing.T) {
// 	gin.SetMode(gin.TestMode)
// 	platformPermission := []enums.AppPermission{
// 		enums.AppPermission_SpacesRead,
// 	}
//
// 	mockUserID := uuid.New()
// 	repoType := enums.RepoType_Models
// 	namespace := "namespace"
// 	repoName := "repo-name"
//
// 	// Test cases for different permission scenarios
// 	tests := []struct {
// 		name           string
// 		path           string
// 		mockFn         func(u *mocks.MockUserUsecase)
// 		setupRequest   func(req *http.Request)
// 		setupContext   func(c *gin.Context)
// 		expectBody     string
// 		expectHTTPCode int
// 	}{
// 		{
// 			name: "Full access with all required permissions",
// 			path: fmt.Sprintf("/api/v1/repositories/%s/%s/%s/%s", repoType, namespace, repoName, mockUserID),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, platformPermission).Return(true, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 				c.Set(enums.USER_ID, mockUserID)
// 			},
// 			expectBody:     "",
// 			expectHTTPCode: http.StatusOK,
// 		},
// 		{
// 			name: "Forbidden - Missing platform required permissions",
// 			path: fmt.Sprintf("/api/v1/repositories/%s/%s/%s/%s", repoType, namespace, repoName, mockUserID),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, platformPermission).Return(false, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 			},
// 			expectBody:     "{\"code\":403,\"message\":\"No permission\"}",
// 			expectHTTPCode: http.StatusForbidden,
// 		},
// 	}
//
// 	// Setup mock user usecase
// 	u := &mocks.MockUserUsecase{}
//
// 	// Setup Gin router with middleware chain
// 	r := gin.Default()
// 	r.GET("/api/v1/repositories/:repo_type/:namespace/:repo_name/:member_id",
// 		func(c *gin.Context) {
// 			c.Set(enums.USER_ID, mockUserID)
// 			c.Next()
// 		},
// 		middlewares.PlatformPermission(u)(
// 			platformPermission...,
// 		),
// 	)
//
// 	for _, testcase := range tests {
// 		t.Run(testcase.name, func(t *testing.T) {
// 			testcase.mockFn(u)
//
// 			// Create test request
// 			req := httptest.NewRequest(http.MethodGet, testcase.path, nil)
// 			testcase.setupRequest(req)
//
// 			// Execute request
// 			rr := httptest.NewRecorder()
// 			r.ServeHTTP(rr, req)
//
// 			// Assertions
// 			assert.Equal(t, testcase.expectHTTPCode, rr.Code)
// 			assert.Equal(t, testcase.expectBody, rr.Body.String())
// 		})
// 	}
// }
//
// func TestAPIDeleteMemberById(t *testing.T) {
// 	gin.SetMode(gin.TestMode)
// 	platformPermission := []enums.AppPermission{
// 		enums.AppPermission_SpacesRead,
// 	}
//
// 	repoPermission := []enums.RepoPermission{
// 		enums.RepoPermission_Edit, enums.RepoPermission_MembersDelete,
// 	}
//
// 	mockUserID := uuid.New()
// 	repoType := enums.RepoType_Models
// 	namespace := "namespace"
// 	repoName := "repo-name"
// 	repoId := *types.NewRepoID(repoType, namespace, repoName)
//
// 	// Test cases for different permission scenarios
// 	tests := []struct {
// 		name           string
// 		path           string
// 		mockFn         func(u *mocks.MockUserUsecase)
// 		setupRequest   func(req *http.Request)
// 		setupContext   func(c *gin.Context)
// 		expectBody     string
// 		expectHTTPCode int
// 	}{
// 		{
// 			name: "Full access with all required permissions",
// 			path: fmt.Sprintf("/api/v1/repositories/%s/%s/%s/%s", repoType, namespace, repoName, mockUserID),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, platformPermission).Return(true, nil).Once()
// 				u.On("AuthorizeRepo", context.Background(), mockUserID, repoId, repoPermission).Return(true, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 				c.Set(enums.USER_ID, mockUserID)
// 			},
// 			expectBody:     "",
// 			expectHTTPCode: http.StatusOK,
// 		},
// 		{
// 			name: "Forbidden - Missing platform required permissions",
// 			path: fmt.Sprintf("/api/v1/repositories/%s/%s/%s/%s", repoType, namespace, repoName, mockUserID),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, platformPermission).Return(false, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 			},
// 			expectBody:     "{\"code\":403,\"message\":\"No permission\"}",
// 			expectHTTPCode: http.StatusForbidden,
// 		},
// 	}
//
// 	// Setup mock user usecase
// 	u := &mocks.MockUserUsecase{}
//
// 	// Setup Gin router with middleware chain
// 	r := gin.Default()
// 	r.DELETE("/api/v1/repositories/:repo_type/:namespace/:repo_name/:member_id",
// 		func(c *gin.Context) {
// 			c.Set(enums.USER_ID, mockUserID)
// 			c.Next()
// 		},
// 		middlewares.PlatformPermission(u)(
// 			platformPermission...,
// 		),
// 		middlewares.RepoPermission(u)(
// 			repoPermission...,
// 		),
// 	)
//
// 	for _, testcase := range tests {
// 		t.Run(testcase.name, func(t *testing.T) {
// 			testcase.mockFn(u)
//
// 			// Create test request
// 			req := httptest.NewRequest(http.MethodDelete, testcase.path, nil)
// 			testcase.setupRequest(req)
//
// 			// Execute request
// 			rr := httptest.NewRecorder()
// 			r.ServeHTTP(rr, req)
//
// 			// Assertions
// 			assert.Equal(t, testcase.expectHTTPCode, rr.Code)
// 			assert.Equal(t, testcase.expectBody, rr.Body.String())
// 		})
// 	}
// }
//
// func TestAPIUpdateMemberById(t *testing.T) {
// 	gin.SetMode(gin.TestMode)
// 	platformPermission := []enums.AppPermission{
// 		enums.AppPermission_SpacesRead,
// 	}
//
// 	repoPermission := []enums.RepoPermission{
// 		enums.RepoPermission_Edit, enums.RepoPermission_MembersDelete,
// 	}
//
// 	mockUserID := uuid.New()
// 	repoType := enums.RepoType_Models
// 	namespace := "namespace"
// 	repoName := "repo-name"
// 	repoId := *types.NewRepoID(repoType, namespace, repoName)
//
// 	// Test cases for different permission scenarios
// 	tests := []struct {
// 		name           string
// 		path           string
// 		mockFn         func(u *mocks.MockUserUsecase)
// 		setupRequest   func(req *http.Request)
// 		setupContext   func(c *gin.Context)
// 		expectBody     string
// 		expectHTTPCode int
// 	}{
// 		{
// 			name: "Full access with all required permissions",
// 			path: fmt.Sprintf("/api/v1/repositories/%s/%s/%s/%s", repoType, namespace, repoName, mockUserID),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, platformPermission).Return(true, nil).Once()
// 				u.On("AuthorizeRepo", context.Background(), mockUserID, repoId, repoPermission).Return(true, nil).Once()
//
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 				c.Set(enums.USER_ID, mockUserID)
// 			},
// 			expectBody:     "",
// 			expectHTTPCode: http.StatusOK,
// 		},
// 		{
// 			name: "Forbidden - Missing platform required permissions",
// 			path: fmt.Sprintf("/api/v1/repositories/%s/%s/%s/%s", repoType, namespace, repoName, mockUserID),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, platformPermission).Return(false, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 			},
// 			expectBody:     "{\"code\":403,\"message\":\"No permission\"}",
// 			expectHTTPCode: http.StatusForbidden,
// 		},
// 	}
//
// 	// Setup mock user usecase
// 	u := &mocks.MockUserUsecase{}
//
// 	// Setup Gin router with middleware chain
// 	r := gin.Default()
// 	r.PUT("/api/v1/repositories/:repo_type/:namespace/:repo_name/:member_id",
// 		func(c *gin.Context) {
// 			c.Set(enums.USER_ID, mockUserID)
// 			c.Next()
// 		},
// 		middlewares.PlatformPermission(u)(
// 			platformPermission...,
// 		),
// 		middlewares.RepoPermission(u)(
// 			repoPermission...,
// 		),
// 	)
//
// 	for _, testcase := range tests {
// 		t.Run(testcase.name, func(t *testing.T) {
// 			testcase.mockFn(u)
//
// 			// Create test request
// 			req := httptest.NewRequest(http.MethodPut, testcase.path, nil)
// 			testcase.setupRequest(req)
//
// 			// Execute request
// 			rr := httptest.NewRecorder()
// 			r.ServeHTTP(rr, req)
//
// 			// Assertions
// 			assert.Equal(t, testcase.expectHTTPCode, rr.Code)
// 			assert.Equal(t, testcase.expectBody, rr.Body.String())
// 		})
// 	}
// }
//
// func TestAPICreateEnv(t *testing.T) {
// 	gin.SetMode(gin.TestMode)
// 	repoPermission := []enums.RepoPermission{
// 		enums.RepoPermission_Edit,
// 	}
//
// 	mockUserID := uuid.New()
// 	repoType := enums.RepoType_Models
// 	namespace := "namespace"
// 	repoName := "repo-name"
// 	repoId := *types.NewRepoID(repoType, namespace, repoName)
//
// 	// Test cases for different permission scenarios
// 	tests := []struct {
// 		name           string
// 		path           string
// 		mockFn         func(u *mocks.MockUserUsecase)
// 		setupRequest   func(req *http.Request)
// 		setupContext   func(c *gin.Context)
// 		expectBody     string
// 		expectHTTPCode int
// 	}{
// 		{
// 			name: "Full access with all required permissions",
// 			path: fmt.Sprintf("/api/v1/repositories/%s/%s/%s/envs", repoType, namespace, repoName),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizeRepo", context.Background(), mockUserID, repoId, repoPermission).Return(true, nil).Once()
//
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 				c.Set(enums.USER_ID, mockUserID)
// 			},
// 			expectBody:     "",
// 			expectHTTPCode: http.StatusOK,
// 		},
// 		{
// 			name: "Forbidden - Missing platform required permissions",
// 			path: fmt.Sprintf("/api/v1/repositories/%s/%s/%s/envs", repoType, namespace, repoName),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizeRepo", context.Background(), mockUserID, repoId, repoPermission).Return(false, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 			},
// 			expectBody:     "{\"code\":403,\"message\":\"No permission\"}",
// 			expectHTTPCode: http.StatusForbidden,
// 		},
// 	}
//
// 	// Setup mock user usecase
// 	u := &mocks.MockUserUsecase{}
//
// 	// Setup Gin router with middleware chain
// 	r := gin.Default()
// 	r.POST("/api/v1/repositories/:repo_type/:namespace/:repo_name/envs",
// 		func(c *gin.Context) {
// 			c.Set(enums.USER_ID, mockUserID)
// 			c.Next()
// 		},
// 		middlewares.RepoPermission(u)(
// 			repoPermission...,
// 		),
// 	)
//
// 	for _, testcase := range tests {
// 		t.Run(testcase.name, func(t *testing.T) {
// 			testcase.mockFn(u)
//
// 			// Create test request
// 			req := httptest.NewRequest(http.MethodPost, testcase.path, nil)
// 			testcase.setupRequest(req)
//
// 			// Execute request
// 			rr := httptest.NewRecorder()
// 			r.ServeHTTP(rr, req)
//
// 			// Assertions
// 			assert.Equal(t, testcase.expectHTTPCode, rr.Code)
// 			assert.Equal(t, testcase.expectBody, rr.Body.String())
// 		})
// 	}
// }
//
// func TestAPIGetEnv(t *testing.T) {
// 	gin.SetMode(gin.TestMode)
// 	repoPermission := []enums.RepoPermission{
// 		enums.RepoPermission_Edit,
// 	}
//
// 	mockUserID := uuid.New()
// 	repoType := enums.RepoType_Models
// 	namespace := "namespace"
// 	repoName := "repo-name"
// 	repoId := *types.NewRepoID(repoType, namespace, repoName)
//
// 	// Test cases for different permission scenarios
// 	tests := []struct {
// 		name           string
// 		path           string
// 		mockFn         func(u *mocks.MockUserUsecase)
// 		setupRequest   func(req *http.Request)
// 		setupContext   func(c *gin.Context)
// 		expectBody     string
// 		expectHTTPCode int
// 	}{
// 		{
// 			name: "Full access with all required permissions",
// 			path: fmt.Sprintf("/api/v1/repositories/%s/%s/%s/envs", repoType, namespace, repoName),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizeRepo", context.Background(), mockUserID, repoId, repoPermission).Return(true, nil).Once()
//
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 				c.Set(enums.USER_ID, mockUserID)
// 			},
// 			expectBody:     "",
// 			expectHTTPCode: http.StatusOK,
// 		},
// 		{
// 			name: "Forbidden - Missing platform required permissions",
// 			path: fmt.Sprintf("/api/v1/repositories/%s/%s/%s/envs", repoType, namespace, repoName),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizeRepo", context.Background(), mockUserID, repoId, repoPermission).Return(false, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 			},
// 			expectBody:     "{\"code\":403,\"message\":\"No permission\"}",
// 			expectHTTPCode: http.StatusForbidden,
// 		},
// 	}
//
// 	// Setup mock user usecase
// 	u := &mocks.MockUserUsecase{}
//
// 	// Setup Gin router with middleware chain
// 	r := gin.Default()
// 	r.GET("/api/v1/repositories/:repo_type/:namespace/:repo_name/envs",
// 		func(c *gin.Context) {
// 			c.Set(enums.USER_ID, mockUserID)
// 			c.Next()
// 		},
// 		middlewares.RepoPermission(u)(
// 			repoPermission...,
// 		),
// 	)
//
// 	for _, testcase := range tests {
// 		t.Run(testcase.name, func(t *testing.T) {
// 			testcase.mockFn(u)
//
// 			// Create test request
// 			req := httptest.NewRequest(http.MethodGet, testcase.path, nil)
// 			testcase.setupRequest(req)
//
// 			// Execute request
// 			rr := httptest.NewRecorder()
// 			r.ServeHTTP(rr, req)
//
// 			// Assertions
// 			assert.Equal(t, testcase.expectHTTPCode, rr.Code)
// 			assert.Equal(t, testcase.expectBody, rr.Body.String())
// 		})
// 	}
// }
//
// func TestAPIUpdateEnv(t *testing.T) {
// 	gin.SetMode(gin.TestMode)
// 	repoPermission := []enums.RepoPermission{
// 		enums.RepoPermission_Edit,
// 	}
//
// 	mockUserID := uuid.New()
// 	repoType := enums.RepoType_Models
// 	namespace := "namespace"
// 	repoName := "repo-name"
// 	repoId := *types.NewRepoID(repoType, namespace, repoName)
//
// 	// Test cases for different permission scenarios
// 	tests := []struct {
// 		name           string
// 		path           string
// 		mockFn         func(u *mocks.MockUserUsecase)
// 		setupRequest   func(req *http.Request)
// 		setupContext   func(c *gin.Context)
// 		expectBody     string
// 		expectHTTPCode int
// 	}{
// 		{
// 			name: "Full access with all required permissions",
// 			path: fmt.Sprintf("/api/v1/repositories/%s/%s/%s/envs", repoType, namespace, repoName),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizeRepo", context.Background(), mockUserID, repoId, repoPermission).Return(true, nil).Once()
//
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 				c.Set(enums.USER_ID, mockUserID)
// 			},
// 			expectBody:     "",
// 			expectHTTPCode: http.StatusOK,
// 		},
// 		{
// 			name: "Forbidden - Missing platform required permissions",
// 			path: fmt.Sprintf("/api/v1/repositories/%s/%s/%s/envs", repoType, namespace, repoName),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizeRepo", context.Background(), mockUserID, repoId, repoPermission).Return(false, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 			},
// 			expectBody:     "{\"code\":403,\"message\":\"No permission\"}",
// 			expectHTTPCode: http.StatusForbidden,
// 		},
// 	}
//
// 	// Setup mock user usecase
// 	u := &mocks.MockUserUsecase{}
//
// 	// Setup Gin router with middleware chain
// 	r := gin.Default()
// 	r.PUT("/api/v1/repositories/:repo_type/:namespace/:repo_name/envs",
// 		func(c *gin.Context) {
// 			c.Set(enums.USER_ID, mockUserID)
// 			c.Next()
// 		},
// 		middlewares.RepoPermission(u)(
// 			repoPermission...,
// 		),
// 	)
//
// 	for _, testcase := range tests {
// 		t.Run(testcase.name, func(t *testing.T) {
// 			testcase.mockFn(u)
//
// 			// Create test request
// 			req := httptest.NewRequest(http.MethodPut, testcase.path, nil)
// 			testcase.setupRequest(req)
//
// 			// Execute request
// 			rr := httptest.NewRecorder()
// 			r.ServeHTTP(rr, req)
//
// 			// Assertions
// 			assert.Equal(t, testcase.expectHTTPCode, rr.Code)
// 			assert.Equal(t, testcase.expectBody, rr.Body.String())
// 		})
// 	}
// }
//
// func TestAPIDeleteEnv(t *testing.T) {
// 	gin.SetMode(gin.TestMode)
// 	repoPermission := []enums.RepoPermission{
// 		enums.RepoPermission_Edit,
// 	}
//
// 	mockUserID := uuid.New()
// 	repoType := enums.RepoType_Models
// 	namespace := "namespace"
// 	repoName := "repo-name"
// 	repoId := *types.NewRepoID(repoType, namespace, repoName)
//
// 	// Test cases for different permission scenarios
// 	tests := []struct {
// 		name           string
// 		path           string
// 		mockFn         func(u *mocks.MockUserUsecase)
// 		setupRequest   func(req *http.Request)
// 		setupContext   func(c *gin.Context)
// 		expectBody     string
// 		expectHTTPCode int
// 	}{
// 		{
// 			name: "Full access with all required permissions",
// 			path: fmt.Sprintf("/api/v1/repositories/%s/%s/%s/envs", repoType, namespace, repoName),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizeRepo", context.Background(), mockUserID, repoId, repoPermission).Return(true, nil).Once()
//
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 				c.Set(enums.USER_ID, mockUserID)
// 			},
// 			expectBody:     "",
// 			expectHTTPCode: http.StatusOK,
// 		},
// 		{
// 			name: "Forbidden - Missing platform required permissions",
// 			path: fmt.Sprintf("/api/v1/repositories/%s/%s/%s/envs", repoType, namespace, repoName),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizeRepo", context.Background(), mockUserID, repoId, repoPermission).Return(false, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 			},
// 			expectBody:     "{\"code\":403,\"message\":\"No permission\"}",
// 			expectHTTPCode: http.StatusForbidden,
// 		},
// 	}
//
// 	// Setup mock user usecase
// 	u := &mocks.MockUserUsecase{}
//
// 	// Setup Gin router with middleware chain
// 	r := gin.Default()
// 	r.DELETE("/api/v1/repositories/:repo_type/:namespace/:repo_name/envs",
// 		func(c *gin.Context) {
// 			c.Set(enums.USER_ID, mockUserID)
// 			c.Next()
// 		},
// 		middlewares.RepoPermission(u)(
// 			repoPermission...,
// 		),
// 	)
//
// 	for _, testcase := range tests {
// 		t.Run(testcase.name, func(t *testing.T) {
// 			testcase.mockFn(u)
//
// 			// Create test request
// 			req := httptest.NewRequest(http.MethodDelete, testcase.path, nil)
// 			testcase.setupRequest(req)
//
// 			// Execute request
// 			rr := httptest.NewRecorder()
// 			r.ServeHTTP(rr, req)
//
// 			// Assertions
// 			assert.Equal(t, testcase.expectHTTPCode, rr.Code)
// 			assert.Equal(t, testcase.expectBody, rr.Body.String())
// 		})
// 	}
// }
//
// func TestAPIStartDeploment(t *testing.T) {
// 	gin.SetMode(gin.TestMode)
// 	repoPermission := []enums.RepoPermission{
// 		enums.RepoPermission_DeployRun,
// 	}
//
// 	mockUserID := uuid.New()
// 	repoType := enums.RepoType_Models
// 	namespace := "namespace"
// 	repoName := "repo-name"
// 	repoId := *types.NewRepoID(repoType, namespace, repoName)
//
// 	// Test cases for different permission scenarios
// 	tests := []struct {
// 		name           string
// 		path           string
// 		mockFn         func(u *mocks.MockUserUsecase)
// 		setupRequest   func(req *http.Request)
// 		setupContext   func(c *gin.Context)
// 		expectBody     string
// 		expectHTTPCode int
// 	}{
// 		{
// 			name: "Full access with all required permissions",
// 			path: fmt.Sprintf("/api/v1/repositories/%s/%s/%s/deployments/start", repoType, namespace, repoName),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizeRepo", context.Background(), mockUserID, repoId, repoPermission).Return(true, nil).Once()
//
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 				c.Set(enums.USER_ID, mockUserID)
// 			},
// 			expectBody:     "",
// 			expectHTTPCode: http.StatusOK,
// 		},
// 		{
// 			name: "Forbidden - Missing platform required permissions",
// 			path: fmt.Sprintf("/api/v1/repositories/%s/%s/%s/deployments/start", repoType, namespace, repoName),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizeRepo", context.Background(), mockUserID, repoId, repoPermission).Return(false, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 			},
// 			expectBody:     "{\"code\":403,\"message\":\"No permission\"}",
// 			expectHTTPCode: http.StatusForbidden,
// 		},
// 	}
//
// 	// Setup mock user usecase
// 	u := &mocks.MockUserUsecase{}
//
// 	// Setup Gin router with middleware chain
// 	r := gin.Default()
// 	r.POST("/api/v1/repositories/:repo_type/:namespace/:repo_name/deployments/start",
// 		func(c *gin.Context) {
// 			c.Set(enums.USER_ID, mockUserID)
// 			c.Next()
// 		},
// 		middlewares.RepoPermission(u)(
// 			repoPermission...,
// 		),
// 	)
//
// 	for _, testcase := range tests {
// 		t.Run(testcase.name, func(t *testing.T) {
// 			testcase.mockFn(u)
//
// 			// Create test request
// 			req := httptest.NewRequest(http.MethodPost, testcase.path, nil)
// 			testcase.setupRequest(req)
//
// 			// Execute request
// 			rr := httptest.NewRecorder()
// 			r.ServeHTTP(rr, req)
//
// 			// Assertions
// 			assert.Equal(t, testcase.expectHTTPCode, rr.Code)
// 			assert.Equal(t, testcase.expectBody, rr.Body.String())
// 		})
// 	}
// }
//
// func TestAPIRebuildDeploment(t *testing.T) {
// 	gin.SetMode(gin.TestMode)
// 	repoPermission := []enums.RepoPermission{
// 		enums.RepoPermission_DeployRun,
// 	}
//
// 	mockUserID := uuid.New()
// 	repoType := enums.RepoType_Models
// 	namespace := "namespace"
// 	repoName := "repo-name"
// 	repoId := *types.NewRepoID(repoType, namespace, repoName)
//
// 	// Test cases for different permission scenarios
// 	tests := []struct {
// 		name           string
// 		path           string
// 		mockFn         func(u *mocks.MockUserUsecase)
// 		setupRequest   func(req *http.Request)
// 		setupContext   func(c *gin.Context)
// 		expectBody     string
// 		expectHTTPCode int
// 	}{
// 		{
// 			name: "Full access with all required permissions",
// 			path: fmt.Sprintf("/api/v1/repositories/%s/%s/%s/deployments/rebuild", repoType, namespace, repoName),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizeRepo", context.Background(), mockUserID, repoId, repoPermission).Return(true, nil).Once()
//
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 				c.Set(enums.USER_ID, mockUserID)
// 			},
// 			expectBody:     "",
// 			expectHTTPCode: http.StatusOK,
// 		},
// 		{
// 			name: "Forbidden - Missing platform required permissions",
// 			path: fmt.Sprintf("/api/v1/repositories/%s/%s/%s/deployments/rebuild", repoType, namespace, repoName),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizeRepo", context.Background(), mockUserID, repoId, repoPermission).Return(false, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 			},
// 			expectBody:     "{\"code\":403,\"message\":\"No permission\"}",
// 			expectHTTPCode: http.StatusForbidden,
// 		},
// 	}
//
// 	// Setup mock user usecase
// 	u := &mocks.MockUserUsecase{}
//
// 	// Setup Gin router with middleware chain
// 	r := gin.Default()
// 	r.POST("/api/v1/repositories/:repo_type/:namespace/:repo_name/deployments/rebuild",
// 		func(c *gin.Context) {
// 			c.Set(enums.USER_ID, mockUserID)
// 			c.Next()
// 		},
// 		middlewares.RepoPermission(u)(
// 			repoPermission...,
// 		),
// 	)
//
// 	for _, testcase := range tests {
// 		t.Run(testcase.name, func(t *testing.T) {
// 			testcase.mockFn(u)
//
// 			// Create test request
// 			req := httptest.NewRequest(http.MethodPost, testcase.path, nil)
// 			testcase.setupRequest(req)
//
// 			// Execute request
// 			rr := httptest.NewRecorder()
// 			r.ServeHTTP(rr, req)
//
// 			// Assertions
// 			assert.Equal(t, testcase.expectHTTPCode, rr.Code)
// 			assert.Equal(t, testcase.expectBody, rr.Body.String())
// 		})
// 	}
// }
//
// func TestAPIRestartDeploment(t *testing.T) {
// 	gin.SetMode(gin.TestMode)
// 	repoPermission := []enums.RepoPermission{
// 		enums.RepoPermission_DeployRun,
// 	}
//
// 	mockUserID := uuid.New()
// 	repoType := enums.RepoType_Models
// 	namespace := "namespace"
// 	repoName := "repo-name"
// 	repoId := *types.NewRepoID(repoType, namespace, repoName)
//
// 	// Test cases for different permission scenarios
// 	tests := []struct {
// 		name           string
// 		path           string
// 		mockFn         func(u *mocks.MockUserUsecase)
// 		setupRequest   func(req *http.Request)
// 		setupContext   func(c *gin.Context)
// 		expectBody     string
// 		expectHTTPCode int
// 	}{
// 		{
// 			name: "Full access with all required permissions",
// 			path: fmt.Sprintf("/api/v1/repositories/%s/%s/%s/deployments/restart", repoType, namespace, repoName),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizeRepo", context.Background(), mockUserID, repoId, repoPermission).Return(true, nil).Once()
//
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 				c.Set(enums.USER_ID, mockUserID)
// 			},
// 			expectBody:     "",
// 			expectHTTPCode: http.StatusOK,
// 		},
// 		{
// 			name: "Forbidden - Missing platform required permissions",
// 			path: fmt.Sprintf("/api/v1/repositories/%s/%s/%s/deployments/restart", repoType, namespace, repoName),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizeRepo", context.Background(), mockUserID, repoId, repoPermission).Return(false, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 			},
// 			expectBody:     "{\"code\":403,\"message\":\"No permission\"}",
// 			expectHTTPCode: http.StatusForbidden,
// 		},
// 	}
//
// 	// Setup mock user usecase
// 	u := &mocks.MockUserUsecase{}
//
// 	// Setup Gin router with middleware chain
// 	r := gin.Default()
// 	r.GET("/api/v1/repositories/:repo_type/:namespace/:repo_name/deployments/restart",
// 		func(c *gin.Context) {
// 			c.Set(enums.USER_ID, mockUserID)
// 			c.Next()
// 		},
// 		middlewares.RepoPermission(u)(
// 			repoPermission...,
// 		),
// 	)
//
// 	for _, testcase := range tests {
// 		t.Run(testcase.name, func(t *testing.T) {
// 			testcase.mockFn(u)
//
// 			// Create test request
// 			req := httptest.NewRequest(http.MethodGet, testcase.path, nil)
// 			testcase.setupRequest(req)
//
// 			// Execute request
// 			rr := httptest.NewRecorder()
// 			r.ServeHTTP(rr, req)
//
// 			// Assertions
// 			assert.Equal(t, testcase.expectHTTPCode, rr.Code)
// 			assert.Equal(t, testcase.expectBody, rr.Body.String())
// 		})
// 	}
// }
//
// func TestAPIStopDeploment(t *testing.T) {
// 	gin.SetMode(gin.TestMode)
// 	repoPermission := []enums.RepoPermission{
// 		enums.RepoPermission_DeployStop,
// 	}
//
// 	mockUserID := uuid.New()
// 	repoType := enums.RepoType_Models
// 	namespace := "namespace"
// 	repoName := "repo-name"
// 	repoId := *types.NewRepoID(repoType, namespace, repoName)
//
// 	// Test cases for different permission scenarios
// 	tests := []struct {
// 		name           string
// 		path           string
// 		mockFn         func(u *mocks.MockUserUsecase)
// 		setupRequest   func(req *http.Request)
// 		setupContext   func(c *gin.Context)
// 		expectBody     string
// 		expectHTTPCode int
// 	}{
// 		{
// 			name: "Full access with all required permissions",
// 			path: fmt.Sprintf("/api/v1/repositories/%s/%s/%s/deployments/stop", repoType, namespace, repoName),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizeRepo", context.Background(), mockUserID, repoId, repoPermission).Return(true, nil).Once()
//
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 				c.Set(enums.USER_ID, mockUserID)
// 			},
// 			expectBody:     "",
// 			expectHTTPCode: http.StatusOK,
// 		},
// 		{
// 			name: "Forbidden - Missing platform required permissions",
// 			path: fmt.Sprintf("/api/v1/repositories/%s/%s/%s/deployments/stop", repoType, namespace, repoName),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizeRepo", context.Background(), mockUserID, repoId, repoPermission).Return(false, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 			},
// 			expectBody:     "{\"code\":403,\"message\":\"No permission\"}",
// 			expectHTTPCode: http.StatusForbidden,
// 		},
// 	}
//
// 	// Setup mock user usecase
// 	u := &mocks.MockUserUsecase{}
//
// 	// Setup Gin router with middleware chain
// 	r := gin.Default()
// 	r.POST("/api/v1/repositories/:repo_type/:namespace/:repo_name/deployments/stop",
// 		func(c *gin.Context) {
// 			c.Set(enums.USER_ID, mockUserID)
// 			c.Next()
// 		},
// 		middlewares.RepoPermission(u)(
// 			repoPermission...,
// 		),
// 	)
//
// 	for _, testcase := range tests {
// 		t.Run(testcase.name, func(t *testing.T) {
// 			testcase.mockFn(u)
//
// 			// Create test request
// 			req := httptest.NewRequest(http.MethodPost, testcase.path, nil)
// 			testcase.setupRequest(req)
//
// 			// Execute request
// 			rr := httptest.NewRecorder()
// 			r.ServeHTTP(rr, req)
//
// 			// Assertions
// 			assert.Equal(t, testcase.expectHTTPCode, rr.Code)
// 			assert.Equal(t, testcase.expectBody, rr.Body.String())
// 		})
// 	}
// }
//
// func TestAPITerminateDeploment(t *testing.T) {
// 	gin.SetMode(gin.TestMode)
// 	repoPermission := []enums.RepoPermission{
// 		enums.RepoPermission_DeployStop,
// 	}
//
// 	mockUserID := uuid.New()
// 	repoType := enums.RepoType_Models
// 	namespace := "namespace"
// 	repoName := "repo-name"
// 	repoId := *types.NewRepoID(repoType, namespace, repoName)
//
// 	// Test cases for different permission scenarios
// 	tests := []struct {
// 		name           string
// 		path           string
// 		mockFn         func(u *mocks.MockUserUsecase)
// 		setupRequest   func(req *http.Request)
// 		setupContext   func(c *gin.Context)
// 		expectBody     string
// 		expectHTTPCode int
// 	}{
// 		{
// 			name: "Full access with all required permissions",
// 			path: fmt.Sprintf("/api/v1/repositories/%s/%s/%s/deployments/terminate", repoType, namespace, repoName),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizeRepo", context.Background(), mockUserID, repoId, repoPermission).Return(true, nil).Once()
//
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 				c.Set(enums.USER_ID, mockUserID)
// 			},
// 			expectBody:     "",
// 			expectHTTPCode: http.StatusOK,
// 		},
// 		{
// 			name: "Forbidden - Missing platform required permissions",
// 			path: fmt.Sprintf("/api/v1/repositories/%s/%s/%s/deployments/terminate", repoType, namespace, repoName),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizeRepo", context.Background(), mockUserID, repoId, repoPermission).Return(false, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 			},
// 			expectBody:     "{\"code\":403,\"message\":\"No permission\"}",
// 			expectHTTPCode: http.StatusForbidden,
// 		},
// 	}
//
// 	// Setup mock user usecase
// 	u := &mocks.MockUserUsecase{}
//
// 	// Setup Gin router with middleware chain
// 	r := gin.Default()
// 	r.POST("/api/v1/repositories/:repo_type/:namespace/:repo_name/deployments/terminate",
// 		func(c *gin.Context) {
// 			c.Set(enums.USER_ID, mockUserID)
// 			c.Next()
// 		},
// 		middlewares.RepoPermission(u)(
// 			repoPermission...,
// 		),
// 	)
//
// 	for _, testcase := range tests {
// 		t.Run(testcase.name, func(t *testing.T) {
// 			testcase.mockFn(u)
//
// 			// Create test request
// 			req := httptest.NewRequest(http.MethodPost, testcase.path, nil)
// 			testcase.setupRequest(req)
//
// 			// Execute request
// 			rr := httptest.NewRecorder()
// 			r.ServeHTTP(rr, req)
//
// 			// Assertions
// 			assert.Equal(t, testcase.expectHTTPCode, rr.Code)
// 			assert.Equal(t, testcase.expectBody, rr.Body.String())
// 		})
// 	}
// }
//
// func TestAPIGetCommits(t *testing.T) {
// 	gin.SetMode(gin.TestMode)
// 	platformPermission := []enums.AppPermission{
// 		enums.AppPermission_SpacesRead,
// 		enums.AppPermission_ModelsRead,
// 		enums.AppPermission_DatasetsRead,
// 	}
//
// 	mockUserID := uuid.New()
// 	repoType := enums.RepoType_Models
// 	namespace := "namespace"
// 	repoName := "repo-name"
//
// 	// Test cases for different permission scenarios
// 	tests := []struct {
// 		name           string
// 		path           string
// 		mockFn         func(u *mocks.MockUserUsecase)
// 		setupRequest   func(req *http.Request)
// 		setupContext   func(c *gin.Context)
// 		expectBody     string
// 		expectHTTPCode int
// 	}{
// 		{
// 			name: "Full access with all required permissions",
// 			path: fmt.Sprintf("/api/v1/repositories/%s/%s/%s/commits", repoType, namespace, repoName),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, platformPermission).Return(true, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 				c.Set(enums.USER_ID, mockUserID)
// 			},
// 			expectBody:     "",
// 			expectHTTPCode: http.StatusOK,
// 		},
// 		{
// 			name: "Forbidden - Missing platform required permissions",
// 			path: fmt.Sprintf("/api/v1/repositories/%s/%s/%s/commits", repoType, namespace, repoName),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, platformPermission).Return(false, nil).Once()
//
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 			},
// 			expectBody:     "{\"code\":403,\"message\":\"No permission\"}",
// 			expectHTTPCode: http.StatusForbidden,
// 		},
// 	}
//
// 	// Setup mock user usecase
// 	u := &mocks.MockUserUsecase{}
//
// 	// Setup Gin router with middleware chain
// 	r := gin.Default()
// 	r.GET("/api/v1/repositories/:repo_type/:namespace/:repo_name/commits",
// 		func(c *gin.Context) {
// 			c.Set(enums.USER_ID, mockUserID)
// 			c.Next()
// 		},
// 		middlewares.PlatformPermission(u)(
// 			platformPermission...,
// 		),
// 	)
//
// 	for _, testcase := range tests {
// 		t.Run(testcase.name, func(t *testing.T) {
// 			testcase.mockFn(u)
//
// 			// Create test request
// 			req := httptest.NewRequest(http.MethodGet, testcase.path, nil)
// 			testcase.setupRequest(req)
//
// 			// Execute request
// 			rr := httptest.NewRecorder()
// 			r.ServeHTTP(rr, req)
//
// 			// Assertions
// 			assert.Equal(t, testcase.expectHTTPCode, rr.Code)
// 			assert.Equal(t, testcase.expectBody, rr.Body.String())
// 		})
// 	}
// }
//
// func TestAPIGetFiles(t *testing.T) {
// 	gin.SetMode(gin.TestMode)
// 	platformPermission := []enums.AppPermission{
// 		enums.AppPermission_SpacesRead,
// 		enums.AppPermission_ModelsRead,
// 		enums.AppPermission_DatasetsRead,
// 	}
//
// 	mockUserID := uuid.New()
// 	repoType := enums.RepoType_Models
// 	namespace := "namespace"
// 	repoName := "repo-name"
//
// 	// Test cases for different permission scenarios
// 	tests := []struct {
// 		name           string
// 		path           string
// 		mockFn         func(u *mocks.MockUserUsecase)
// 		setupRequest   func(req *http.Request)
// 		setupContext   func(c *gin.Context)
// 		expectBody     string
// 		expectHTTPCode int
// 	}{
// 		{
// 			name: "Full access with all required permissions",
// 			path: fmt.Sprintf("/api/v1/repositories/%s/%s/%s/files", repoType, namespace, repoName),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, platformPermission).Return(true, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 				c.Set(enums.USER_ID, mockUserID)
// 			},
// 			expectBody:     "",
// 			expectHTTPCode: http.StatusOK,
// 		},
// 		{
// 			name: "Forbidden - Missing platform required permissions",
// 			path: fmt.Sprintf("/api/v1/repositories/%s/%s/%s/files", repoType, namespace, repoName),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, platformPermission).Return(false, nil).Once()
//
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 			},
// 			expectBody:     "{\"code\":403,\"message\":\"No permission\"}",
// 			expectHTTPCode: http.StatusForbidden,
// 		},
// 	}
//
// 	// Setup mock user usecase
// 	u := &mocks.MockUserUsecase{}
//
// 	// Setup Gin router with middleware chain
// 	r := gin.Default()
// 	r.GET("/api/v1/repositories/:repo_type/:namespace/:repo_name/files",
// 		func(c *gin.Context) {
// 			c.Set(enums.USER_ID, mockUserID)
// 			c.Next()
// 		},
// 		middlewares.PlatformPermission(u)(
// 			platformPermission...,
// 		),
// 	)
//
// 	for _, testcase := range tests {
// 		t.Run(testcase.name, func(t *testing.T) {
// 			testcase.mockFn(u)
//
// 			// Create test request
// 			req := httptest.NewRequest(http.MethodGet, testcase.path, nil)
// 			testcase.setupRequest(req)
//
// 			// Execute request
// 			rr := httptest.NewRecorder()
// 			r.ServeHTTP(rr, req)
//
// 			// Assertions
// 			assert.Equal(t, testcase.expectHTTPCode, rr.Code)
// 			assert.Equal(t, testcase.expectBody, rr.Body.String())
// 		})
// 	}
// }
//
// func TestAPIGetFileContent(t *testing.T) {
// 	gin.SetMode(gin.TestMode)
// 	platformPermission := []enums.AppPermission{
// 		enums.AppPermission_SpacesRead,
// 		enums.AppPermission_ModelsRead,
// 		enums.AppPermission_DatasetsRead,
// 	}
//
// 	mockUserID := uuid.New()
// 	repoType := enums.RepoType_Models
// 	namespace := "namespace"
// 	repoName := "repo-name"
//
// 	// Test cases for different permission scenarios
// 	tests := []struct {
// 		name           string
// 		path           string
// 		mockFn         func(u *mocks.MockUserUsecase)
// 		setupRequest   func(req *http.Request)
// 		setupContext   func(c *gin.Context)
// 		expectBody     string
// 		expectHTTPCode int
// 	}{
// 		{
// 			name: "Full access with all required permissions",
// 			path: fmt.Sprintf("/api/v1/repositories/%s/%s/%s/files/sample.md", repoType, namespace, repoName),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, platformPermission).Return(true, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 				c.Set(enums.USER_ID, mockUserID)
// 			},
// 			expectBody:     "",
// 			expectHTTPCode: http.StatusOK,
// 		},
// 		{
// 			name: "Forbidden - Missing platform required permissions",
// 			path: fmt.Sprintf("/api/v1/repositories/%s/%s/%s/files/sample.md", repoType, namespace, repoName),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, platformPermission).Return(false, nil).Once()
//
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 			},
// 			expectBody:     "{\"code\":403,\"message\":\"No permission\"}",
// 			expectHTTPCode: http.StatusForbidden,
// 		},
// 	}
//
// 	// Setup mock user usecase
// 	u := &mocks.MockUserUsecase{}
//
// 	// Setup Gin router with middleware chain
// 	r := gin.Default()
// 	r.GET("/api/v1/repositories/:repo_type/:namespace/:repo_name/files/*file_path",
// 		func(c *gin.Context) {
// 			c.Set(enums.USER_ID, mockUserID)
// 			c.Next()
// 		},
// 		middlewares.PlatformPermission(u)(
// 			platformPermission...,
// 		),
// 	)
//
// 	for _, testcase := range tests {
// 		t.Run(testcase.name, func(t *testing.T) {
// 			testcase.mockFn(u)
//
// 			// Create test request
// 			req := httptest.NewRequest(http.MethodGet, testcase.path, nil)
// 			testcase.setupRequest(req)
//
// 			// Execute request
// 			rr := httptest.NewRecorder()
// 			r.ServeHTTP(rr, req)
//
// 			// Assertions
// 			assert.Equal(t, testcase.expectHTTPCode, rr.Code)
// 			assert.Equal(t, testcase.expectBody, rr.Body.String())
// 		})
// 	}
// }
//
// func TestAPIGetBranches(t *testing.T) {
// 	gin.SetMode(gin.TestMode)
// 	platformPermission := []enums.AppPermission{
// 		enums.AppPermission_SpacesRead,
// 		enums.AppPermission_ModelsRead,
// 		enums.AppPermission_DatasetsRead,
// 	}
//
// 	mockUserID := uuid.New()
// 	repoType := enums.RepoType_Models
// 	namespace := "namespace"
// 	repoName := "repo-name"
//
// 	// Test cases for different permission scenarios
// 	tests := []struct {
// 		name           string
// 		path           string
// 		mockFn         func(u *mocks.MockUserUsecase)
// 		setupRequest   func(req *http.Request)
// 		setupContext   func(c *gin.Context)
// 		expectBody     string
// 		expectHTTPCode int
// 	}{
// 		{
// 			name: "Full access with all required permissions",
// 			path: fmt.Sprintf("/api/v1/repositories/%s/%s/%s/branches", repoType, namespace, repoName),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, platformPermission).Return(true, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 				c.Set(enums.USER_ID, mockUserID)
// 			},
// 			expectBody:     "",
// 			expectHTTPCode: http.StatusOK,
// 		},
// 		{
// 			name: "Forbidden - Missing platform required permissions",
// 			path: fmt.Sprintf("/api/v1/repositories/%s/%s/%s/branches", repoType, namespace, repoName),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, platformPermission).Return(false, nil).Once()
//
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 			},
// 			expectBody:     "{\"code\":403,\"message\":\"No permission\"}",
// 			expectHTTPCode: http.StatusForbidden,
// 		},
// 	}
//
// 	// Setup mock user usecase
// 	u := &mocks.MockUserUsecase{}
//
// 	// Setup Gin router with middleware chain
// 	r := gin.Default()
// 	r.GET("/api/v1/repositories/:repo_type/:namespace/:repo_name/branches",
// 		func(c *gin.Context) {
// 			c.Set(enums.USER_ID, mockUserID)
// 			c.Next()
// 		},
// 		middlewares.PlatformPermission(u)(
// 			platformPermission...,
// 		),
// 	)
//
// 	for _, testcase := range tests {
// 		t.Run(testcase.name, func(t *testing.T) {
// 			testcase.mockFn(u)
//
// 			// Create test request
// 			req := httptest.NewRequest(http.MethodGet, testcase.path, nil)
// 			testcase.setupRequest(req)
//
// 			// Execute request
// 			rr := httptest.NewRecorder()
// 			r.ServeHTTP(rr, req)
//
// 			// Assertions
// 			assert.Equal(t, testcase.expectHTTPCode, rr.Code)
// 			assert.Equal(t, testcase.expectBody, rr.Body.String())
// 		})
// 	}
// }
//
// func TestAPIGetSignupRequest(t *testing.T) {
// 	gin.SetMode(gin.TestMode)
// 	platformPermission := []enums.AppPermission{
// 		enums.AppPermission_UsersApprove, enums.AppPermission_UsersRead,
// 	}
//
// 	mockUserID := uuid.New()
//
// 	// Test cases for different permission scenarios
// 	tests := []struct {
// 		name           string
// 		path           string
// 		mockFn         func(u *mocks.MockUserUsecase)
// 		setupRequest   func(req *http.Request)
// 		setupContext   func(c *gin.Context)
// 		expectBody     string
// 		expectHTTPCode int
// 	}{
// 		{
// 			name: "Full access with all required permissions",
// 			path: "/api/v1/admin/signups",
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, platformPermission).Return(true, nil).Once()
// 				u.On("IsAsmin", context.Background(), mockUserID).Return(true, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 				c.Set(enums.USER_ID, mockUserID)
// 			},
// 			expectBody:     "",
// 			expectHTTPCode: http.StatusOK,
// 		},
// 		{
// 			name: "Forbidden - Missing platform required permissions",
// 			path: "/api/v1/admin/signups",
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, platformPermission).Return(true, nil).Once()
// 				u.On("IsAsmin", context.Background(), mockUserID).Return(false, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 			},
// 			expectBody:     "{\"code\":403,\"message\":\"No permission\"}",
// 			expectHTTPCode: http.StatusForbidden,
// 		},
// 		{
// 			name: "Forbidden - Missing platform required permissions",
// 			path: "/api/v1/admin/signups",
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, platformPermission).Return(false, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 			},
// 			expectBody:     "{\"code\":403,\"message\":\"No permission\"}",
// 			expectHTTPCode: http.StatusForbidden,
// 		},
// 	}
//
// 	// Setup mock user usecase
// 	u := &mocks.MockUserUsecase{}
//
// 	// Setup Gin router with middleware chain
// 	r := gin.Default()
// 	r.GET("/api/v1/admin/signups",
// 		func(c *gin.Context) {
// 			c.Set(enums.USER_ID, mockUserID)
// 			c.Next()
// 		},
// 		middlewares.PlatformPermission(u)(
// 			platformPermission...,
// 		),
// 		middlewares.IsAdmin(u),
// 	)
//
// 	for _, testcase := range tests {
// 		t.Run(testcase.name, func(t *testing.T) {
// 			testcase.mockFn(u)
//
// 			// Create test request
// 			req := httptest.NewRequest(http.MethodGet, testcase.path, nil)
// 			testcase.setupRequest(req)
//
// 			// Execute request
// 			rr := httptest.NewRecorder()
// 			r.ServeHTTP(rr, req)
//
// 			// Assertions
// 			assert.Equal(t, testcase.expectHTTPCode, rr.Code)
// 			assert.Equal(t, testcase.expectBody, rr.Body.String())
// 		})
// 	}
// }
//
// func TestAPIApproveSignupRequest(t *testing.T) {
// 	gin.SetMode(gin.TestMode)
// 	platformPermission := []enums.AppPermission{
// 		enums.AppPermission_UsersApprove,
// 	}
//
// 	mockUserID := uuid.New()
//
// 	// Test cases for different permission scenarios
// 	tests := []struct {
// 		name           string
// 		path           string
// 		mockFn         func(u *mocks.MockUserUsecase)
// 		setupRequest   func(req *http.Request)
// 		setupContext   func(c *gin.Context)
// 		expectBody     string
// 		expectHTTPCode int
// 	}{
// 		{
// 			name: "Full access with all required permissions",
// 			path: fmt.Sprintf("/api/v1/admin/signups/%s/approval", mockUserID),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, platformPermission).Return(true, nil).Once()
// 				u.On("IsAsmin", context.Background(), mockUserID).Return(true, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 				c.Set(enums.USER_ID, mockUserID)
// 			},
// 			expectBody:     "",
// 			expectHTTPCode: http.StatusOK,
// 		},
// 		{
// 			name: "Forbidden - Missing platform required permissions",
// 			path: fmt.Sprintf("/api/v1/admin/signups/%s/approval", mockUserID),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, platformPermission).Return(true, nil).Once()
// 				u.On("IsAsmin", context.Background(), mockUserID).Return(false, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 			},
// 			expectBody:     "{\"code\":403,\"message\":\"No permission\"}",
// 			expectHTTPCode: http.StatusForbidden,
// 		},
// 		{
// 			name: "Forbidden - Missing platform required permissions",
// 			path: fmt.Sprintf("/api/v1/admin/signups/%s/approval", mockUserID),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, platformPermission).Return(false, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 			},
// 			expectBody:     "{\"code\":403,\"message\":\"No permission\"}",
// 			expectHTTPCode: http.StatusForbidden,
// 		},
// 	}
//
// 	// Setup mock user usecase
// 	u := &mocks.MockUserUsecase{}
//
// 	// Setup Gin router with middleware chain
// 	r := gin.Default()
// 	r.PATCH(fmt.Sprintf("/api/v1/admin/signups/%s/approval", mockUserID),
// 		func(c *gin.Context) {
// 			c.Set(enums.USER_ID, mockUserID)
// 			c.Next()
// 		},
// 		middlewares.PlatformPermission(u)(
// 			platformPermission...,
// 		),
// 		middlewares.IsAdmin(u),
// 	)
//
// 	for _, testcase := range tests {
// 		t.Run(testcase.name, func(t *testing.T) {
// 			testcase.mockFn(u)
//
// 			// Create test request
// 			req := httptest.NewRequest(http.MethodPatch, testcase.path, nil)
// 			testcase.setupRequest(req)
//
// 			// Execute request
// 			rr := httptest.NewRecorder()
// 			r.ServeHTTP(rr, req)
//
// 			// Assertions
// 			assert.Equal(t, testcase.expectHTTPCode, rr.Code)
// 			assert.Equal(t, testcase.expectBody, rr.Body.String())
// 		})
// 	}
// }
//
// func TestAPIInviteUser(t *testing.T) {
// 	gin.SetMode(gin.TestMode)
// 	platformPermission := []enums.AppPermission{
// 		enums.AppPermission_UsersInvite,
// 	}
//
// 	mockUserID := uuid.New()
//
// 	// Test cases for different permission scenarios
// 	tests := []struct {
// 		name           string
// 		path           string
// 		mockFn         func(u *mocks.MockUserUsecase)
// 		setupRequest   func(req *http.Request)
// 		setupContext   func(c *gin.Context)
// 		expectBody     string
// 		expectHTTPCode int
// 	}{
// 		{
// 			name: "Full access with all required permissions",
// 			path: "/api/v1/admin/invite",
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, platformPermission).Return(true, nil).Once()
// 				u.On("IsAsmin", context.Background(), mockUserID).Return(true, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 				c.Set(enums.USER_ID, mockUserID)
// 			},
// 			expectBody:     "",
// 			expectHTTPCode: http.StatusOK,
// 		},
// 		{
// 			name: "Forbidden - Missing platform required permissions",
// 			path: "/api/v1/admin/invite",
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, platformPermission).Return(true, nil).Once()
// 				u.On("IsAsmin", context.Background(), mockUserID).Return(false, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 			},
// 			expectBody:     "{\"code\":403,\"message\":\"No permission\"}",
// 			expectHTTPCode: http.StatusForbidden,
// 		},
// 		{
// 			name: "Forbidden - Missing platform required permissions",
// 			path: "/api/v1/admin/invite",
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, platformPermission).Return(false, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 			},
// 			expectBody:     "{\"code\":403,\"message\":\"No permission\"}",
// 			expectHTTPCode: http.StatusForbidden,
// 		},
// 	}
//
// 	// Setup mock user usecase
// 	u := &mocks.MockUserUsecase{}
//
// 	// Setup Gin router with middleware chain
// 	r := gin.Default()
// 	r.POST("/api/v1/admin/invite",
// 		func(c *gin.Context) {
// 			c.Set(enums.USER_ID, mockUserID)
// 			c.Next()
// 		},
// 		middlewares.PlatformPermission(u)(
// 			platformPermission...,
// 		),
// 		middlewares.IsAdmin(u),
// 	)
//
// 	for _, testcase := range tests {
// 		t.Run(testcase.name, func(t *testing.T) {
// 			testcase.mockFn(u)
//
// 			// Create test request
// 			req := httptest.NewRequest(http.MethodPost, testcase.path, nil)
// 			testcase.setupRequest(req)
//
// 			// Execute request
// 			rr := httptest.NewRecorder()
// 			r.ServeHTTP(rr, req)
//
// 			// Assertions
// 			assert.Equal(t, testcase.expectHTTPCode, rr.Code)
// 			assert.Equal(t, testcase.expectBody, rr.Body.String())
// 		})
// 	}
// }
//
// func TestAPICreateOrganization(t *testing.T) {
// 	gin.SetMode(gin.TestMode)
// 	platformPermission := []enums.AppPermission{
// 		enums.AppPermission_OrgsRead,
// 	}
//
// 	mockUserID := uuid.New()
//
// 	// Test cases for different permission scenarios
// 	tests := []struct {
// 		name           string
// 		path           string
// 		mockFn         func(u *mocks.MockUserUsecase)
// 		setupRequest   func(req *http.Request)
// 		setupContext   func(c *gin.Context)
// 		expectBody     string
// 		expectHTTPCode int
// 	}{
// 		{
// 			name: "Full access with all required permissions",
// 			path: "/api/v1/organizations",
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, platformPermission).Return(true, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 				c.Set(enums.USER_ID, mockUserID)
// 			},
// 			expectBody:     "",
// 			expectHTTPCode: http.StatusOK,
// 		},
// 		{
// 			name: "Forbidden - Missing platform required permissions",
// 			path: "/api/v1/organizations",
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, platformPermission).Return(false, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 			},
// 			expectBody:     "{\"code\":403,\"message\":\"No permission\"}",
// 			expectHTTPCode: http.StatusForbidden,
// 		},
// 	}
//
// 	// Setup mock user usecase
// 	u := &mocks.MockUserUsecase{}
//
// 	// Setup Gin router with middleware chain
// 	r := gin.Default()
// 	r.POST("/api/v1/organizations",
// 		func(c *gin.Context) {
// 			c.Set(enums.USER_ID, mockUserID)
// 			c.Next()
// 		},
// 		middlewares.PlatformPermission(u)(
// 			platformPermission...,
// 		),
// 	)
//
// 	for _, testcase := range tests {
// 		t.Run(testcase.name, func(t *testing.T) {
// 			testcase.mockFn(u)
//
// 			// Create test request
// 			req := httptest.NewRequest(http.MethodPost, testcase.path, nil)
// 			testcase.setupRequest(req)
//
// 			// Execute request
// 			rr := httptest.NewRecorder()
// 			r.ServeHTTP(rr, req)
//
// 			// Assertions
// 			assert.Equal(t, testcase.expectHTTPCode, rr.Code)
// 			assert.Equal(t, testcase.expectBody, rr.Body.String())
// 		})
// 	}
// }
//
// func TestAPIGetOrganizationMemberById(t *testing.T) {
// 	gin.SetMode(gin.TestMode)
// 	platformPermission := []enums.AppPermission{
// 		enums.AppPermission_OrgsRead,
// 	}
//
// 	mockUserID := uuid.New()
// 	orgId := uuid.New()
//
// 	// Test cases for different permission scenarios
// 	tests := []struct {
// 		name           string
// 		path           string
// 		mockFn         func(u *mocks.MockUserUsecase)
// 		setupRequest   func(req *http.Request)
// 		setupContext   func(c *gin.Context)
// 		expectBody     string
// 		expectHTTPCode int
// 	}{
// 		{
// 			name: "Full access with all required permissions",
// 			path: fmt.Sprintf("/api/v1/organizations/%s/members/%s", orgId, mockUserID),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, platformPermission).Return(true, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 				c.Set(enums.USER_ID, mockUserID)
// 			},
// 			expectBody:     "",
// 			expectHTTPCode: http.StatusOK,
// 		},
// 		{
// 			name: "Forbidden - Missing platform required permissions",
// 			path: fmt.Sprintf("/api/v1/organizations/%s/members/%s", orgId, mockUserID),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, platformPermission).Return(false, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 			},
// 			expectBody:     "{\"code\":403,\"message\":\"No permission\"}",
// 			expectHTTPCode: http.StatusForbidden,
// 		},
// 	}
//
// 	// Setup mock user usecase
// 	u := &mocks.MockUserUsecase{}
//
// 	// Setup Gin router with middleware chain
// 	r := gin.Default()
// 	r.GET(fmt.Sprintf("/api/v1/organizations/%s/members/%s", orgId, mockUserID),
// 		func(c *gin.Context) {
// 			c.Set(enums.USER_ID, mockUserID)
// 			c.Next()
// 		},
// 		middlewares.PlatformPermission(u)(
// 			platformPermission...,
// 		),
// 	)
//
// 	for _, testcase := range tests {
// 		t.Run(testcase.name, func(t *testing.T) {
// 			testcase.mockFn(u)
//
// 			// Create test request
// 			req := httptest.NewRequest(http.MethodGet, testcase.path, nil)
// 			testcase.setupRequest(req)
//
// 			// Execute request
// 			rr := httptest.NewRecorder()
// 			r.ServeHTTP(rr, req)
//
// 			// Assertions
// 			assert.Equal(t, testcase.expectHTTPCode, rr.Code)
// 			assert.Equal(t, testcase.expectBody, rr.Body.String())
// 		})
// 	}
// }
//
// func TestAPIDeleteOrganizationMemberById(t *testing.T) {
// 	gin.SetMode(gin.TestMode)
// 	platformPermission := []enums.AppPermission{
// 		enums.AppPermission_OrgsRead,
// 		enums.AppPermission_OrgsDelete,
// 	}
//
// 	orgPermission := []enums.RepoPermission{
// 		enums.RepoPermission_Edit, enums.RepoPermission_MembersDelete,
// 	}
//
// 	mockUserID := uuid.New()
// 	orgId := uuid.New()
//
// 	// Test cases for different permission scenarios
// 	tests := []struct {
// 		name           string
// 		path           string
// 		mockFn         func(u *mocks.MockUserUsecase)
// 		setupRequest   func(req *http.Request)
// 		setupContext   func(c *gin.Context)
// 		expectBody     string
// 		expectHTTPCode int
// 	}{
// 		{
// 			name: "Full access with all required permissions",
// 			path: fmt.Sprintf("/api/v1/organizations/%s/members/%s", orgId, mockUserID),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, platformPermission).Return(true, nil).Once()
// 				u.On("AuthorizeOrg", context.Background(), mockUserID, orgId, orgPermission).Return(true, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 				c.Set(enums.USER_ID, mockUserID)
// 			},
// 			expectBody:     "",
// 			expectHTTPCode: http.StatusOK,
// 		},
// 		{
// 			name: "Forbidden - Missing platform required permissions",
// 			path: fmt.Sprintf("/api/v1/organizations/%s/members/%s", orgId, mockUserID),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, platformPermission).Return(false, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 			},
// 			expectBody:     "{\"code\":403,\"message\":\"No permission\"}",
// 			expectHTTPCode: http.StatusForbidden,
// 		},
// 	}
//
// 	// Setup mock user usecase
// 	u := &mocks.MockUserUsecase{}
//
// 	// Setup Gin router with middleware chain
// 	r := gin.Default()
// 	r.DELETE("/api/v1/organizations/:org_id/members/:member_id",
// 		func(c *gin.Context) {
// 			c.Set(enums.USER_ID, mockUserID)
// 			c.Next()
// 		},
// 		middlewares.PlatformPermission(u)(
// 			platformPermission...,
// 		),
// 		middlewares.OrgPermission(u)(
// 			orgPermission...,
// 		),
// 	)
//
// 	for _, testcase := range tests {
// 		t.Run(testcase.name, func(t *testing.T) {
// 			testcase.mockFn(u)
//
// 			// Create test request
// 			req := httptest.NewRequest(http.MethodDelete, testcase.path, nil)
// 			testcase.setupRequest(req)
//
// 			// Execute request
// 			rr := httptest.NewRecorder()
// 			r.ServeHTTP(rr, req)
//
// 			// Assertions
// 			assert.Equal(t, testcase.expectHTTPCode, rr.Code)
// 			assert.Equal(t, testcase.expectBody, rr.Body.String())
// 		})
// 	}
// }
//
// func TestAPIUpdateOrganizationMemberById(t *testing.T) {
// 	gin.SetMode(gin.TestMode)
// 	platformPermission := []enums.AppPermission{
// 		enums.AppPermission_OrgsRead,
// 		enums.AppPermission_OrgsDelete,
// 	}
//
// 	orgPermission := []enums.RepoPermission{
// 		enums.RepoPermission_Edit, enums.RepoPermission_MembersDelete,
// 	}
//
// 	mockUserID := uuid.New()
// 	orgId := uuid.New()
//
// 	// Test cases for different permission scenarios
// 	tests := []struct {
// 		name           string
// 		path           string
// 		mockFn         func(u *mocks.MockUserUsecase)
// 		setupRequest   func(req *http.Request)
// 		setupContext   func(c *gin.Context)
// 		expectBody     string
// 		expectHTTPCode int
// 	}{
// 		{
// 			name: "Full access with all required permissions",
// 			path: fmt.Sprintf("/api/v1/organizations/%s/members/%s", orgId, mockUserID),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, platformPermission).Return(true, nil).Once()
// 				u.On("AuthorizeOrg", context.Background(), mockUserID, orgId, orgPermission).Return(true, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 				c.Set(enums.USER_ID, mockUserID)
// 			},
// 			expectBody:     "",
// 			expectHTTPCode: http.StatusOK,
// 		},
// 		{
// 			name: "Forbidden - Missing platform required permissions",
// 			path: fmt.Sprintf("/api/v1/organizations/%s/members/%s", orgId, mockUserID),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, platformPermission).Return(false, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 			},
// 			expectBody:     "{\"code\":403,\"message\":\"No permission\"}",
// 			expectHTTPCode: http.StatusForbidden,
// 		},
// 	}
//
// 	// Setup mock user usecase
// 	u := &mocks.MockUserUsecase{}
//
// 	// Setup Gin router with middleware chain
// 	r := gin.Default()
// 	r.PUT("/api/v1/organizations/:org_id/members/:member_id",
// 		func(c *gin.Context) {
// 			c.Set(enums.USER_ID, mockUserID)
// 			c.Next()
// 		},
// 		middlewares.PlatformPermission(u)(
// 			platformPermission...,
// 		),
// 		middlewares.OrgPermission(u)(
// 			orgPermission...,
// 		),
// 	)
//
// 	for _, testcase := range tests {
// 		t.Run(testcase.name, func(t *testing.T) {
// 			testcase.mockFn(u)
//
// 			// Create test request
// 			req := httptest.NewRequest(http.MethodPut, testcase.path, nil)
// 			testcase.setupRequest(req)
//
// 			// Execute request
// 			rr := httptest.NewRecorder()
// 			r.ServeHTTP(rr, req)
//
// 			// Assertions
// 			assert.Equal(t, testcase.expectHTTPCode, rr.Code)
// 			assert.Equal(t, testcase.expectBody, rr.Body.String())
// 		})
// 	}
// }
//
// func TestAPIDeleteOrganization(t *testing.T) {
// 	gin.SetMode(gin.TestMode)
// 	platformPermission := []enums.AppPermission{
// 		enums.AppPermission_OrgsRead,
// 		enums.AppPermission_OrgsDelete,
// 	}
//
// 	mockUserID := uuid.New()
// 	orgId := uuid.New()
//
// 	// Test cases for different permission scenarios
// 	tests := []struct {
// 		name           string
// 		path           string
// 		mockFn         func(u *mocks.MockUserUsecase)
// 		setupRequest   func(req *http.Request)
// 		setupContext   func(c *gin.Context)
// 		expectBody     string
// 		expectHTTPCode int
// 	}{
// 		{
// 			name: "Full access with all required permissions",
// 			path: fmt.Sprintf("/api/v1/organizations/%s", orgId),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, platformPermission).Return(true, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 				c.Set(enums.USER_ID, mockUserID)
// 			},
// 			expectBody:     "",
// 			expectHTTPCode: http.StatusOK,
// 		},
// 		{
// 			name: "Forbidden - Missing platform required permissions",
// 			path: fmt.Sprintf("/api/v1/organizations/%s", orgId),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, platformPermission).Return(false, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 			},
// 			expectBody:     "{\"code\":403,\"message\":\"No permission\"}",
// 			expectHTTPCode: http.StatusForbidden,
// 		},
// 	}
//
// 	// Setup mock user usecase
// 	u := &mocks.MockUserUsecase{}
//
// 	// Setup Gin router with middleware chain
// 	r := gin.Default()
// 	r.DELETE(fmt.Sprintf("/api/v1/organizations/%s", orgId),
// 		func(c *gin.Context) {
// 			c.Set(enums.USER_ID, mockUserID)
// 			c.Next()
// 		},
// 		middlewares.PlatformPermission(u)(
// 			platformPermission...,
// 		),
// 	)
//
// 	for _, testcase := range tests {
// 		t.Run(testcase.name, func(t *testing.T) {
// 			testcase.mockFn(u)
//
// 			// Create test request
// 			req := httptest.NewRequest(http.MethodDelete, testcase.path, nil)
// 			testcase.setupRequest(req)
//
// 			// Execute request
// 			rr := httptest.NewRecorder()
// 			r.ServeHTTP(rr, req)
//
// 			// Assertions
// 			assert.Equal(t, testcase.expectHTTPCode, rr.Code)
// 			assert.Equal(t, testcase.expectBody, rr.Body.String())
// 		})
// 	}
// }
//
// func TestAPIInviteUserToOrganization(t *testing.T) {
// 	gin.SetMode(gin.TestMode)
// 	platformPermission := []enums.AppPermission{
// 		enums.AppPermission_OrgsRead,
// 		enums.AppPermission_OrgsEdit,
// 	}
//
// 	orgPermission := []enums.RepoPermission{
// 		enums.RepoPermission_Edit, enums.RepoPermission_MembersEdit,
// 	}
//
// 	mockUserID := uuid.New()
// 	orgId := uuid.New()
//
// 	// Test cases for different permission scenarios
// 	tests := []struct {
// 		name           string
// 		path           string
// 		mockFn         func(u *mocks.MockUserUsecase)
// 		setupRequest   func(req *http.Request)
// 		setupContext   func(c *gin.Context)
// 		expectBody     string
// 		expectHTTPCode int
// 	}{
// 		{
// 			name: "Full access with all required permissions",
// 			path: fmt.Sprintf("/api/v1/organizations/%s/invite", orgId),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, platformPermission).Return(true, nil).Once()
// 				u.On("AuthorizeOrg", context.Background(), mockUserID, orgId, orgPermission).Return(true, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 				c.Set(enums.USER_ID, mockUserID)
// 				c.Set(enums.ORG_ID, orgId)
// 			},
// 			expectBody:     "",
// 			expectHTTPCode: http.StatusOK,
// 		},
// 		{
// 			name: "Forbidden - Missing platform required permissions",
// 			path: fmt.Sprintf("/api/v1/organizations/%s/invite", orgId),
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, platformPermission).Return(false, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 				c.Set(enums.USER_ID, mockUserID)
// 				c.Set(enums.ORG_ID, orgId)
// 			},
// 			expectBody:     "{\"code\":403,\"message\":\"No permission\"}",
// 			expectHTTPCode: http.StatusForbidden,
// 		},
// 	}
//
// 	// Setup mock user usecase
// 	u := &mocks.MockUserUsecase{}
//
// 	// Setup Gin router with middleware chain
// 	r := gin.Default()
// 	r.POST("/api/v1/organizations/:org_id/invite",
// 		func(c *gin.Context) {
// 			c.Set(enums.USER_ID, mockUserID)
// 			c.Set(enums.ORG_ID, orgId)
// 			c.Next()
// 		},
// 		middlewares.PlatformPermission(u)(
// 			platformPermission...,
// 		),
// 		middlewares.OrgPermission(u)(
// 			orgPermission...,
// 		),
// 	)
//
// 	for _, testcase := range tests {
// 		t.Run(testcase.name, func(t *testing.T) {
// 			testcase.mockFn(u)
//
// 			// Create test request
// 			req := httptest.NewRequest(http.MethodPost, testcase.path, nil)
// 			testcase.setupRequest(req)
//
// 			// Execute request
// 			rr := httptest.NewRecorder()
// 			r.ServeHTTP(rr, req)
//
// 			// Assertions
// 			assert.Equal(t, testcase.expectHTTPCode, rr.Code)
// 			assert.Equal(t, testcase.expectBody, rr.Body.String())
// 		})
// 	}
// }
//
// func TestAPIListOrganizations(t *testing.T) {
// 	gin.SetMode(gin.TestMode)
// 	platformPermission := []enums.AppPermission{
// 		enums.AppPermission_OrgsRead,
// 		enums.AppPermission_OrgsEdit,
// 	}
//
// 	mockUserID := uuid.New()
// 	orgId := uuid.New()
//
// 	// Test cases for different permission scenarios
// 	tests := []struct {
// 		name           string
// 		path           string
// 		mockFn         func(u *mocks.MockUserUsecase)
// 		setupRequest   func(req *http.Request)
// 		setupContext   func(c *gin.Context)
// 		expectBody     string
// 		expectHTTPCode int
// 	}{
// 		{
// 			name: "Full access with all required permissions",
// 			path: "/api/v1/organizations",
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, platformPermission).Return(true, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 				c.Set(enums.USER_ID, mockUserID)
// 				c.Set(enums.ORG_ID, orgId)
// 			},
// 			expectBody:     "",
// 			expectHTTPCode: http.StatusOK,
// 		},
// 		{
// 			name: "Forbidden - Missing platform required permissions",
// 			path: "/api/v1/organizations",
// 			mockFn: func(u *mocks.MockUserUsecase) {
// 				u.On("AuthorizePlatform", context.Background(), mockUserID, platformPermission).Return(false, nil).Once()
// 			},
// 			setupRequest: func(req *http.Request) {
// 				req.Header.Set("Authorization", "Bearer token")
// 			},
// 			setupContext: func(c *gin.Context) {
// 				c.Set(enums.USER_ID, mockUserID)
// 				c.Set(enums.ORG_ID, orgId)
// 			},
// 			expectBody:     "{\"code\":403,\"message\":\"No permission\"}",
// 			expectHTTPCode: http.StatusForbidden,
// 		},
// 	}
//
// 	// Setup mock user usecase
// 	u := &mocks.MockUserUsecase{}
//
// 	// Setup Gin router with middleware chain
// 	r := gin.Default()
// 	r.GET("/api/v1/organizations",
// 		func(c *gin.Context) {
// 			c.Set(enums.USER_ID, mockUserID)
// 			c.Set(enums.ORG_ID, orgId)
// 			c.Next()
// 		},
// 		middlewares.PlatformPermission(u)(
// 			platformPermission...,
// 		),
// 	)
//
// 	for _, testcase := range tests {
// 		t.Run(testcase.name, func(t *testing.T) {
// 			testcase.mockFn(u)
//
// 			// Create test request
// 			req := httptest.NewRequest(http.MethodGet, testcase.path, nil)
// 			testcase.setupRequest(req)
//
// 			// Execute request
// 			rr := httptest.NewRecorder()
// 			r.ServeHTTP(rr, req)
//
// 			// Assertions
// 			assert.Equal(t, testcase.expectHTTPCode, rr.Code)
// 			assert.Equal(t, testcase.expectBody, rr.Body.String())
// 		})
// 	}
// }
