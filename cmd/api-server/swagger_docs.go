package main

import (
	"api-server/docs"
	_ "api-server/docs" // docs is generated by <PERSON>wag CLI, you have to import it.
	"os"
)

//	@title			AI Benchmarking Platform Service
//	@version		1.0
//	@description	A machine learning benchmarking management service API developed in Go using Gin framework.
//	@termsOfService

//	@contact.name	AI Benchmarking Maintain team
//	@contact.email	<EMAIL>

//	@license.name	Apache 2.0
//	@license.url	http://www.apache.org/licenses/LICENSE-2.0.html

//	@schemes	http https
//	@host		localhost:8080
//	@BasePath	/api/v1

//	@securityDefinitions.apikey	Bearer
//	@in							header
//	@name						Authorization
//	@description				Type "Bearer" followed by a space and JWT token.

func init() {
	host := "localhost:8080"
	if v, ok := os.LookupEnv("VOLVO_API_EXTERNAL_ADDRESS"); ok && len(v) > 0 {
		host = v
	}
	docs.SwaggerInfo.Host = host
}
