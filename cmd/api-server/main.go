package main

import (
	"context"
	"errors"
	"net/http"
	"os/signal"
	"path/filepath"
	"syscall"
	"time"

	"api-server/configs"
	"api-server/internal/db"
	"api-server/pkg/oteltrace"
	"api-server/pkg/otelzap"
)

func main() {
	defer otelzap.Logger.Sync() // nolint

	defer func() {
		if err := oteltrace.Shutdown(context.Background()); err != nil {
			otelzap.Logger.Fatal("failed to shutdown otel tracer", otelzap.SetErr(err)...)
		}
	}()

	// Create context that listens for the interrupt signal from the OS.
	ctx, stop := signal.NotifyContext(context.Background(), syscall.SIGINT, syscall.SIGTERM)
	defer stop()

	config := configs.NewGlobalConfig()

	dbInstance, err := db.NewDb(config.Database)
	if err != nil {
		otelzap.Logger.Panic("connect to database", otelzap.SetErr(err)...)
	}
	defer dbInstance.Close()

	otelzap.Logger.Info("successfully connected to database")

	if config.Migrate {
		if err := db.Migrate(filepath.Join(".", "migrations"), config.Database, dbInstance); err != nil {
			otelzap.Logger.Panic("failed to migrate database", otelzap.SetErr(err)...)
		}
		otelzap.Logger.Info("successfully migrated database")
	}

	router := InitRouter(ctx, config, dbInstance)
	srv := &http.Server{
		Addr:    ":8080",
		Handler: router,
	}

	otelzap.Logger.Info("server is running")
	// Initializing the server in a goroutine so that
	// it won't block the graceful shutdown handling below
	go func() {
		if err := srv.ListenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
			otelzap.Logger.Fatal("listen: \n", otelzap.SetErr(err)...)
		}
	}()

	// Listen for the interrupt signal.
	<-ctx.Done()

	// Restore default behavior on the interrupt signal and notify user of shutdown.
	stop()
	otelzap.Logger.Info("shutting down gracefully, press Ctrl+C again to force")

	// The context is used to inform the server it has 5 seconds to finish
	// the request it is currently handling
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err := srv.Shutdown(ctx); err != nil {
		otelzap.Logger.Fatal("Server forced to shutdown: ", otelzap.SetErr(err)...)
	}

	otelzap.Logger.Info("Server exiting")
}
