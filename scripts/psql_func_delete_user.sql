DROP FUNCTION IF EXISTS public.delete_user;
CREATE OR R<PERSON><PERSON>CE FUNCTION public.delete_user(
    email_addr text
) R<PERSON><PERSON>NS void AS $$
declare
  user_id uuid;
  auth_user_id uuid;
BEGIN
  SELECT id INTO user_id FROM auth.users as t WHERE t.email = email_addr;
  SELECT id INTO auth_user_id FROM auth.identities as t WHERE t.email = email_addr;

  DELETE FROM auth.users WHERE id = user_id;
  DELETE FROM auth.identities  WHERE id = auth_user_id;
  DELETE FROM public.users WHERE id = user_id;
  DELETE FROM public.user_git_groups WHERE user_id = user_id;
END;
$$ LANGUAGE plpgsql;

/*
Command to execute function: select public.delete_user('<EMAIL>');
*.