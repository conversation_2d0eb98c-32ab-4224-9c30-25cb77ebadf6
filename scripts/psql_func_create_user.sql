DROP FUNCTION IF EXISTS public.create_user;
CREATE OR R<PERSON>LACE FUNCTION public.create_user(
    email text,
    name text,
    username text,
    password text,
    git_user_id int,
    git_access_token text,
    git_expiry_at date,
    git_models_id int,
    git_datasets_id int,
    git_spaces_id int,
    confirm BOOL = true
) R<PERSON>URNS void AS $$
declare
  user_id uuid;
  encrypted_pw text;
  confirmation timestamp;
BEGIN
  user_id := gen_random_uuid();
  encrypted_pw := crypt(password, gen_salt('bf'));
  CASE
    WHEN confirm THEN
      confirmation := now();
    ELSE
      confirmation := null;
  END CASE;

  INSERT INTO auth.users
    (instance_id, id, aud, role, email, encrypted_password, email_confirmed_at, recovery_sent_at, last_sign_in_at, raw_app_meta_data, raw_user_meta_data, created_at, updated_at, confirmation_token, email_change, email_change_token_new, recovery_token)
  VALUES
    ('00000000-0000-0000-0000-000000000000', user_id, 'authenticated', 'authenticated', email, encrypted_pw, confirmation, confirmation, confirmation, '{"provider":"email","providers":["email"]}', '{}', now(), now(), '', '', '', '');

  INSERT INTO auth.identities (provider_id, user_id, identity_data, provider, last_sign_in_at, created_at, updated_at)
  VALUES
    (gen_random_uuid(), user_id, format('{"sub":"%s","email":"%s"}', user_id::text, email)::jsonb, 'email', now(), now(), now());

  INSERT INTO public.users
    (id, name, username, ref_git_userid, git_access_token, git_access_token_expires_at, status, role, created_at, updated_at)
  VALUES (user_id, name, username, git_user_id, git_access_token, git_expiry_at, 'assigned','admin', now(), now());

  INSERT INTO user_git_groups (user_id, ref_git_models_id, ref_git_spaces_id, ref_git_datasets_id) 
  VALUES (user_id, git_models_id, git_spaces_id, git_datasets_id);
END;
$$ LANGUAGE plpgsql;

/*
Find function using psql: /df public.create_user
Command to execute function: select public.create_user('<EMAIL>', 'volvo administrator', 'administrator', 'Qwerty123!', 1, '**************************', '2025-03-01', 4, 5, 6);
*/
