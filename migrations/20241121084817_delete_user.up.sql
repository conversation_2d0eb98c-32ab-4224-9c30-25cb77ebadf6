create or replace function delete_user(
    email_addr text
)
returns void
language plpgsql
as $$
declare 
    user_id uuid;
    auth_user_id uuid;
begin
    SELECT id INTO user_id FROM auth.users as t WHERE t.email = email_addr;
    SELECT id INTO auth_user_id FROM auth.identities as t WHERE t.email = email_addr;

    DELETE FROM auth.users WHERE id = user_id;
    DELETE FROM auth.identities  WHERE id = auth_user_id;
    DELETE FROM public.users WHERE id = user_id;
    DELETE FROM public.user_git_groups WHERE user_id = user_id;
end;
$$;