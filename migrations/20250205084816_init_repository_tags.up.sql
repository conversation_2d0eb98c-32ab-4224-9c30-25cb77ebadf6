CREATE TABLE IF NOT EXISTS "tags" (
                                    "id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                                    "type" VARCHAR(50) NOT NULL,         -- e.g., Main, Tasks, Libraries, Languages
                                    "sub_type" VARCHAR(50),             -- e.g., Multimodal, Computer Vision, Modalities, Size (rows), Format
                                    "value" VARCHAR(100) NOT NULL UNIQUE,   -- e.g., library:json, task:computer-vision:object-detection, tasks:multimodal:any-to-any
                                    "name" VARCHAR(100) NOT NULL,       -- e.g., Json, Object Detection
                                    "icon_url" VARCHAR(100) NOT NULL,   -- URL for the tag's icon
                                    "created_at" TIMESTAMPTZ DEFAULT NOW(),
                                    "updated_at" TIMESTAMPTZ DEFAULT NOW(),
                                    "query" VARCHAR(100) NOT NULL,    -- e.g., tags, languages, task_categories, size_categories. Support for query
                                    "repo_types" text[] NOT NULL  -- e.g., models, datasets, spaces
);

-- Create common tags
INSERT INTO "tags" ("type", "sub_type", "value", "name", "icon_url", "query", "repo_types")
VALUES
                                      ('Main', 'Modalities', 'modalities:3d', '3D', 'https://example.com/icons/object-detection.png','tags',ARRAY['datasets', 'models']),
                                      ('Main', 'Modalities', 'modalities:audio', 'Audio', 'https://example.com/icons/object-detection.png','tags',ARRAY['datasets', 'models']),
                                      ('Main', 'Modalities', 'modalities:image', 'Image', 'https://example.com/icons/object-detection.png','tags',ARRAY['datasets', 'models']),
                                      ('Main', 'Modalities', 'modalities:text', 'Text', 'https://example.com/icons/object-detection.png','tags',ARRAY['datasets', 'models']),
                                      ('Main', 'Format', 'format:json', 'Json', 'https://example.com/icons/classification.png','tags',ARRAY['datasets', 'models']),
                                      ('Main', 'Format', 'format:csv', 'Csv', 'https://example.com/icons/classification.png','tags',ARRAY['datasets', 'models']),
                                      ('Main', 'Format', 'format:parquet', 'Parquet', 'https://example.com/icons/classification.png','tags',ARRAY['datasets', 'models']),
                                      ('Main', 'Format', 'format:text', 'Text', 'https://example.com/icons/classification.png','tags',ARRAY['datasets', 'models']),
                                      ('Tasks', 'Computer Vision', 'task_categories:object-detection', 'Object Detection', 'https://example.com/icons/object-detection.png','task_categories',ARRAY['datasets', 'models']),
                                      ('Tasks', 'Multimodal', 'task_categories:classification', 'Classification', 'https://example.com/icons/classification.png','task_categories',ARRAY['datasets', 'models']);
-- Language Tags
INSERT INTO "tags" ("type", "sub_type", "value", "name", "icon_url", "query", "repo_types") VALUES
('Languages', '', 'language:en', 'English', 'https://example.com/icons/english.png','languages',ARRAY['datasets', 'models']),
('Languages', '', 'language:fr', 'French', 'https://example.com/icons/french.png','languages',ARRAY['datasets', 'models']),
('Languages', '', 'language:us', 'United State', 'https://example.com/icons/english.png','languages',ARRAY['datasets', 'models']),
('Languages', '', 'language:uk', 'United Kingdom', 'https://example.com/icons/french.png','languages',ARRAY['datasets', 'models']),
('Languages', '', 'language:zh', 'Chinese', 'https://example.com/icons/chinese.png','languages',ARRAY['datasets', 'models']),
('Languages', '', 'language:es', 'Spanish', 'https://example.com/icons/spanish.png','languages',ARRAY['datasets', 'models']),
('Languages', '', 'language:de', 'German', 'https://example.com/icons/german.png','languages',ARRAY['datasets', 'models']),
('Languages', '', 'language:ja', 'Japanese', 'https://example.com/icons/japanese.png','languages',ARRAY['datasets', 'models']),
('Languages', '', 'language:ko', 'Korean', 'https://example.com/icons/korean.png','languages',ARRAY['datasets', 'models']),
('Languages', '', 'language:it', 'Italian', 'https://example.com/icons/italian.png','languages',ARRAY['datasets', 'models']),
('Languages', '', 'language:pt', 'Portuguese', 'https://example.com/icons/portuguese.png','languages',ARRAY['datasets', 'models']),
('Languages', '', 'language:ru', 'Russian', 'https://example.com/icons/russian.png','languages',ARRAY['datasets', 'models']),
('Languages', '', 'language:hi', 'Hindi', 'https://example.com/icons/hindi.png','languages',ARRAY['datasets', 'models']),
('Languages', '', 'language:th', 'Thai', 'https://example.com/icons/thai.png','languages',ARRAY['datasets', 'models']),
('Languages', '', 'language:ar', 'Arabic', 'https://example.com/icons/arabic.png','languages',ARRAY['datasets', 'models']),
('Languages', '', 'language:id', 'Indonesian', 'https://example.com/icons/indonesian.png','languages',ARRAY['datasets', 'models']),
('Languages', '', 'language:pl', 'Polish', 'https://example.com/icons/polish.png','languages',ARRAY['datasets', 'models']),
('Languages', '', 'language:tr', 'Turkish', 'https://example.com/icons/turkish.png','languages',ARRAY['datasets', 'models']),
('Languages', '', 'language:nl', 'Dutch', 'https://example.com/icons/dutch.png','languages',ARRAY['datasets', 'models']),
('Languages', '', 'language:multi', 'Multilingual', 'https://example.com/icons/multilingual.png','languages',ARRAY['datasets', 'models']),
('Languages', '', 'language:fi', 'Finnish', 'https://example.com/icons/finnish.png','languages',ARRAY['datasets', 'models']),
('Languages', '', 'language:ro', 'Romanian', 'https://example.com/icons/romanian.png','languages',ARRAY['datasets', 'models']),
('Languages', '', 'language:sv', 'Swedish', 'https://example.com/icons/swedish.png','languages',ARRAY['datasets', 'models']),
('Languages', '', 'language:cs', 'Czech', 'https://example.com/icons/czech.png','languages',ARRAY['datasets', 'models']),
('Languages', '', 'language:fa', 'Persian', 'https://example.com/icons/persian.png','languages',ARRAY['datasets', 'models']),
('Languages', '', 'language:el', 'Greek', 'https://example.com/icons/greek.png','languages',ARRAY['datasets', 'models']),
('Languages', '', 'language:ne', 'Nepali', 'https://example.com/icons/nepali.png','languages',ARRAY['datasets', 'models']),
('Languages', '', 'language:da', 'Danish', 'https://example.com/icons/danish.png','languages',ARRAY['datasets', 'models']),
('Languages', '', 'language:he', 'Hebrew', 'https://example.com/icons/hebrew.png','languages',ARRAY['datasets', 'models']),
('Languages', '', 'language:bn', 'Bengali', 'https://example.com/icons/bengali.png','languages',ARRAY['datasets', 'models']),
('Languages', '', 'language:ca', 'Catalan', 'https://example.com/icons/catalan.png','languages',ARRAY['datasets', 'models']),
('Languages', '', 'language:bg', 'Bulgarian', 'https://example.com/icons/bulgarian.png','languages',ARRAY['datasets', 'models']),
('Languages', '', 'language:ta', 'Tamil', 'https://example.com/icons/tamil.png','languages',ARRAY['datasets', 'models']),
('Languages', '', 'language:hu', 'Hungarian', 'https://example.com/icons/hungarian.png','languages',ARRAY['datasets', 'models']),
('Languages', '', 'language:ms', 'Malay', 'https://example.com/icons/malay.png','languages',ARRAY['datasets', 'models']),
('Languages', '', 'language:no', 'Norwegian', 'https://example.com/icons/norwegian.png','languages',ARRAY['datasets', 'models']),
('Languages', '', 'language:ur', 'Urdu', 'https://example.com/icons/urdu.png','languages',ARRAY['datasets', 'models']),
('Languages', '', 'language:sw', 'Swahili', 'https://example.com/icons/swahili.png','languages',ARRAY['datasets', 'models']),
('Languages', '', 'language:mr', 'Marathi', 'https://example.com/icons/marathi.png','languages',ARRAY['datasets', 'models']),
('Languages', '', 'language:et', 'Estonian', 'https://example.com/icons/estonian.png','languages',ARRAY['datasets', 'models']),
('Languages', '', 'language:te', 'Telugu', 'https://example.com/icons/telugu.png','languages',ARRAY['datasets', 'models']),
('Languages', '', 'language:sl', 'Slovenian', 'https://example.com/icons/slovenian.png','languages',ARRAY['datasets', 'models']),
('Languages', '', 'language:sk', 'Slovak', 'https://example.com/icons/slovak.png','languages',ARRAY['datasets', 'models']),
('Languages', '', 'language:lt', 'Lithuanian', 'https://example.com/icons/lithuanian.png','languages',ARRAY['datasets', 'models']),
('Languages', '', 'language:gu', 'Gujarati', 'https://example.com/icons/gujarati.png','languages',ARRAY['datasets', 'models']),
('Languages', '', 'language:gl', 'Galician', 'https://example.com/icons/galician.png','languages',ARRAY['datasets', 'models']),
('Languages', '', 'language:ml', 'Malayalam', 'https://example.com/icons/malayalam.png','languages',ARRAY['datasets', 'models']),
('Languages', '', 'language:hr', 'Croatian', 'https://example.com/icons/croatian.png','languages',ARRAY['datasets', 'models']),
('Languages', '', 'language:is', 'Icelandic', 'https://example.com/icons/icelandic.png','languages',ARRAY['datasets', 'models']),
('Languages', '', 'language:sr', 'Serbian', 'https://example.com/icons/serbian.png','languages',ARRAY['datasets', 'models']),
('Languages', '', 'language:my', 'Burmese', 'https://example.com/icons/burmese.png','languages',ARRAY['datasets', 'models']),
('Languages', '', 'language:eu', 'Basque', 'https://example.com/icons/basque.png','languages',ARRAY['datasets', 'models']),
('Languages', '', 'language:lv', 'Latvian', 'https://example.com/icons/latvian.png','languages',ARRAY['datasets', 'models']),
('Languages', '', 'language:kn', 'Kannada', 'https://example.com/icons/kannada.png','languages',ARRAY['datasets', 'models']),
('Languages', '', 'language:pa', 'Panjabi', 'https://example.com/icons/panjabi.png','languages',ARRAY['datasets', 'models']),
('Languages', '', 'language:mn', 'Mongolian', 'https://example.com/icons/mongolian.png','languages',ARRAY['datasets', 'models']),
('Languages', '', 'language:af', 'Afrikaans', 'https://example.com/icons/afrikaans.png','languages',ARRAY['datasets', 'models']),
('Languages', '', 'language:am', 'Amharic', 'https://example.com/icons/amharic.png','languages',ARRAY['datasets', 'models']),
('Languages', '', 'language:km', 'Khmer', 'https://example.com/icons/khmer.png','languages',ARRAY['datasets', 'models']),
('Languages', '', 'language:lo', 'Lao', 'https://example.com/icons/lao.png','languages',ARRAY['datasets', 'models']),
('Languages', '', 'language:tl', 'Tagalog', 'https://example.com/icons/tagalog.png','languages',ARRAY['datasets', 'models']),
('Languages', '', 'language:ha', 'Hausa', 'https://example.com/icons/hausa.png','languages',ARRAY['datasets', 'models']),
('Languages', '', 'language:ka', 'Georgian', 'https://example.com/icons/georgian.png','languages',ARRAY['datasets', 'models']),
('Languages', '', 'language:mk', 'Macedonian', 'https://example.com/icons/macedonian.png','languages',ARRAY['datasets', 'models']),
('Languages', '', 'language:si', 'Sinhala', 'https://example.com/icons/sinhala.png','languages',ARRAY['datasets', 'models']);
-- Library Tags
INSERT INTO "tags" ("type", "sub_type", "value", "name", "icon_url", "query", "repo_types") VALUES
('Libraries', '', 'libraries:libjson', 'JSON', 'https://example.com/icons/json.png','libraries',ARRAY['datasets', 'models']),
('Libraries', '', 'libraries:pytorch', 'PyTorch', 'https://example.com/icons/pytorch.png','libraries',ARRAY['datasets', 'models']),
('Libraries', '', 'libraries:tensorflow', 'TensorFlow', 'https://example.com/icons/tensorflow.png', 'libraries', ARRAY['datasets', 'models']),
('Libraries', '', 'libraries:jax', 'JAX', 'https://example.com/icons/jax.png', 'libraries', ARRAY['datasets', 'models']),
('Libraries', '', 'libraries:safetensors', 'Safetensors', 'https://example.com/icons/safetensors.png', 'libraries', ARRAY['datasets', 'models']),
('Libraries', '', 'libraries:transformers', 'Transformers', 'https://example.com/icons/transformers.png', 'libraries', ARRAY['datasets', 'models']),
('Libraries', '', 'libraries:peft', 'PEFT', 'https://example.com/icons/peft.png', 'libraries', ARRAY['datasets', 'models']),
('Libraries', '', 'libraries:tensorboard', 'TensorBoard', 'https://example.com/icons/tensorboard.png', 'libraries', ARRAY['datasets', 'models']),
('Libraries', '', 'libraries:gguf', 'GGUF', 'https://example.com/icons/gguf.png', 'libraries', ARRAY['datasets', 'models']),
('Libraries', '', 'libraries:diffusers', 'Diffusers', 'https://example.com/icons/diffusers.png', 'libraries', ARRAY['datasets', 'models']),
('Libraries', '', 'libraries:stable-baselines3', 'stable-baselines3', 'https://example.com/icons/stable-baselines3.png', 'libraries', ARRAY['datasets', 'models']),
('Libraries', '', 'libraries:onnx', 'ONNX', 'https://example.com/icons/onnx.png', 'libraries', ARRAY['datasets', 'models']),
('Libraries', '', 'libraries:sentence-transformers', 'sentence-transformers', 'https://example.com/icons/sentence-transformers.png', 'libraries', ARRAY['datasets', 'models']),
('Libraries', '', 'libraries:ml-agents', 'ml-agents', 'https://example.com/icons/ml-agents.png', 'libraries', ARRAY['datasets', 'models']),
('Libraries', '', 'libraries:tf-keras', 'TF-Keras', 'https://example.com/icons/tf-keras.png', 'libraries', ARRAY['datasets', 'models']),
('Libraries', '', 'libraries:adapters', 'Adapters', 'https://example.com/icons/adapters.png', 'libraries', ARRAY['datasets', 'models']),
('Libraries', '', 'libraries:mlx', 'MLX', 'https://example.com/icons/mlx.png', 'libraries', ARRAY['datasets', 'models']),
('Libraries', '', 'libraries:setfit', 'setfit', 'https://example.com/icons/setfit.png', 'libraries', ARRAY['datasets', 'models']),
('Libraries', '', 'libraries:timm', 'timm', 'https://example.com/icons/timm.png', 'libraries', ARRAY['datasets', 'models']),
('Libraries', '', 'libraries:sample-factory', 'sample-factory', 'https://example.com/icons/sample-factory.png', 'libraries', ARRAY['datasets', 'models']),
('Libraries', '', 'libraries:keras', 'Keras', 'https://example.com/icons/keras.png', 'libraries', ARRAY['datasets', 'models']),
('Libraries', '', 'libraries:flair', 'Flair', 'https://example.com/icons/flair.png', 'libraries', ARRAY['datasets', 'models']),
('Libraries', '', 'libraries:transformers.js', 'Transformers.js', 'https://example.com/icons/transformers-js.png', 'libraries', ARRAY['datasets', 'models']),
('Libraries', '', 'libraries:openvino', 'OpenVINO', 'https://example.com/icons/openvino.png', 'libraries', ARRAY['datasets', 'models']),
('Libraries', '', 'libraries:spacy', 'spaCy', 'https://example.com/icons/spacy.png', 'libraries', ARRAY['datasets', 'models']),
('Libraries', '', 'libraries:fastai', 'fastai', 'https://example.com/icons/fastai.png', 'libraries', ARRAY['datasets', 'models']),
('Libraries', '', 'libraries:bertopic', 'BERTopic', 'https://example.com/icons/bertopic.png', 'libraries', ARRAY['datasets', 'models']),
('Libraries', '', 'libraries:espnet', 'ESPnet', 'https://example.com/icons/espnet.png', 'libraries', ARRAY['datasets', 'models']),
('Libraries', '', 'libraries:joblib', 'Joblib', 'https://example.com/icons/joblib.png', 'libraries', ARRAY['datasets', 'models']),
('Libraries', '', 'libraries:nemo', 'NeMo', 'https://example.com/icons/nemo.png', 'libraries', ARRAY['datasets', 'models']),
('Libraries', '', 'libraries:coreml', 'Core ML', 'https://example.com/icons/coreml.png', 'libraries', ARRAY['datasets', 'models']),
('Libraries', '', 'libraries:tflite', 'TF Lite', 'https://example.com/icons/tflite.png', 'libraries', ARRAY['datasets', 'models']),
('Libraries', '', 'libraries:openclip', 'OpenCLIP', 'https://example.com/icons/openclip.png', 'libraries', ARRAY['datasets', 'models']),
('Libraries', '', 'libraries:rust', 'Rust', 'https://example.com/icons/rust.png', 'libraries', ARRAY['datasets', 'models']),
('Libraries', '', 'libraries:scikit-learn', 'Scikit-learn', 'https://example.com/icons/scikit-learn.png', 'libraries', ARRAY['datasets', 'models']),
('Libraries', '', 'libraries:fasttext', 'fastText', 'https://example.com/icons/fasttext.png', 'libraries', ARRAY['datasets', 'models']),
('Libraries', '', 'libraries:kerashub', 'KerasHub', 'https://example.com/icons/kerashub.png', 'libraries', ARRAY['datasets', 'models']),
('Libraries', '', 'libraries:speechbrain', 'speechbrain', 'https://example.com/icons/speechbrain.png', 'libraries', ARRAY['datasets', 'models']),
('Libraries', '', 'libraries:paddlepaddle', 'PaddlePaddle', 'https://example.com/icons/paddlepaddle.png', 'libraries', ARRAY['datasets', 'models']),
('Libraries', '', 'libraries:fairseq', 'Fairseq', 'https://example.com/icons/fairseq.png', 'libraries', ARRAY['datasets', 'models']),
('Libraries', '', 'libraries:asteroid', 'Asteroid', 'https://example.com/icons/asteroid.png', 'libraries', ARRAY['datasets', 'models']);
-- License Tags
INSERT INTO "tags" ("type", "sub_type", "value", "name", "icon_url", "query", "repo_types") VALUES
('Licenses', '', 'license:mit', 'mit', 'https://example.com/icons/json.png','license',ARRAY['datasets', 'models']),
('Licenses', '', 'license:apache-2.0', 'apache-2.0', 'https://example.com/icons/json.png','license',ARRAY['datasets', 'models']),
('Licenses', '', 'license:openrail', 'openrail', 'https://example.com/icons/json.png','license',ARRAY['datasets', 'models']),
('Licenses', '', 'license:cc-by-4.0', 'cc-by-4.0', 'https://example.com/icons/json.png','license',ARRAY['datasets', 'models']),
('Licenses', '', 'license:other', 'other', 'https://example.com/icons/json.png','license',ARRAY['datasets', 'models']),
('Licenses', '', 'license:cc-by-sa-4.0', 'cc-by-sa-4.0', 'https://example.com/icons/json.png','license',ARRAY['datasets', 'models']),
('Licenses', '', 'license:cc-by-nc-4.0', 'cc-by-nc-4.0', 'https://example.com/icons/json.png','license',ARRAY['datasets', 'models']),
('Licenses', '', 'license:cc-by-nc-sa-4.0', 'cc-by-nc-sa-4.0', 'https://example.com/icons/json.png','license',ARRAY['datasets', 'models']),
('Licenses', '', 'license:cc0-1.0', 'cc0-1.0', 'https://example.com/icons/json.png','license',ARRAY['datasets', 'models']),
('Licenses', '', 'license:cc', 'cc', 'https://example.com/icons/json.png','license',ARRAY['datasets', 'models']),
('Licenses', '', 'license:cc-by-nc-nd-4.0', 'cc-by-nc-nd-4.0', 'https://example.com/icons/json.png','license',ARRAY['datasets', 'models']),
('Licenses', '', 'license:afl-3.0', 'afl-3.0', 'https://example.com/icons/json.png','license',ARRAY['datasets', 'models']),
('Licenses', '', 'license:gpl-3.0', 'gpl-3.0', 'https://example.com/icons/json.png','license',ARRAY['datasets', 'models']),
('Licenses', '', 'license:cc-by-sa-3.0', 'cc-by-sa-3.0', 'https://example.com/icons/json.png','license',ARRAY['datasets', 'models']),
('Licenses', '', 'license:odc-by', 'odc-by', 'https://example.com/icons/json.png','license',ARRAY['datasets', 'models']),
('Licenses', '', 'license:gpl', 'gpl', 'https://example.com/icons/json.png','license',ARRAY['datasets', 'models']),
('Licenses', '', 'license:creativeml-openrail-m', 'creativeml-openrail-m', 'https://example.com/icons/json.png','license',ARRAY['datasets', 'models']),
('Licenses', '', 'license:llama2', 'llama2', 'https://example.com/icons/json.png','license',ARRAY['datasets', 'models']),
('Licenses', '', 'license:wtfpl', 'wtfpl', 'https://example.com/icons/json.png','license',ARRAY['datasets', 'models']),
('Licenses', '', 'license:llama3', 'llama3', 'https://example.com/icons/json.png','license',ARRAY['datasets', 'models']),
('Licenses', '', 'license:cc-by-nd-4.0', 'cc-by-nd-4.0', 'https://example.com/icons/json.png','license',ARRAY['datasets', 'models']),
('Licenses', '', 'license:unlicense', 'unlicense', 'https://example.com/icons/json.png','license',ARRAY['datasets', 'models']),
('Licenses', '', 'license:agpl-3.0', 'agpl-3.0', 'https://example.com/icons/json.png','license',ARRAY['datasets', 'models']),
('Licenses', '', 'license:cc-by-3.0', 'cc-by-3.0', 'https://example.com/icons/json.png','license',ARRAY['datasets', 'models']),
('Licenses', '', 'license:llama3.1', 'llama3.1', 'https://example.com/icons/json.png','license',ARRAY['datasets', 'models']),
('Licenses', '', 'license:cc-by-2.0', 'cc-by-2.0', 'https://example.com/icons/json.png','license',ARRAY['datasets', 'models']),
('Licenses', '', 'license:artistic-2.0', 'artistic-2.0', 'https://example.com/icons/json.png','license',ARRAY['datasets', 'models']),
('Licenses', '', 'license:bsd', 'bsd', 'https://example.com/icons/json.png','license',ARRAY['datasets', 'models']),
('Licenses', '', 'license:cc-by-nc-2.0', 'cc-by-nc-2.0', 'https://example.com/icons/json.png','license',ARRAY['datasets', 'models']),
('Licenses', '', 'license:bigscience-openrail-m', 'bigscience-openrail-m', 'https://example.com/icons/json.png','license',ARRAY['datasets', 'models']);
-- Create size tags
INSERT INTO "tags" ("type", "sub_type", "value", "name", "icon_url", "created_at", "updated_at", "query", "repo_types")
VALUES
    ('Main', 'Size (rows)', 'size_categories:n<1K', '<1K', 'https://example.com/icons/classification.png', NOW() + INTERVAL '8 second', NOW() + INTERVAL '8 second','size_categories',ARRAY['datasets']),
    ('Main', 'Size (rows)', 'size_categories:1K<n<10K', '10K', 'https://example.com/icons/classification.png', NOW() + INTERVAL '9 second', NOW() + INTERVAL '9 second','size_categories',ARRAY['datasets']),
    ('Main', 'Size (rows)', 'size_categories:10K<n<100K', '100K', 'https://example.com/icons/classification.png', NOW() + INTERVAL '10 second', NOW() + INTERVAL '10 second','size_categories',ARRAY['datasets']),
    ('Main', 'Size (rows)', 'size_categories:100K<n<1M', '1M', 'https://example.com/icons/classification.png', NOW() + INTERVAL '11 second', NOW() + INTERVAL '11 second','size_categories',ARRAY['datasets']),
    ('Main', 'Size (rows)', 'size_categories:1M<n<10M', '10M', 'https://example.com/icons/classification.png', NOW() + INTERVAL '12 second', NOW() + INTERVAL '12 second','size_categories',ARRAY['datasets']),
    ('Main', 'Size (rows)', 'size_categories:10M<n<100M', '100M', 'https://example.com/icons/classification.png', NOW() + INTERVAL '13 second', NOW() + INTERVAL '13 second','size_categories',ARRAY['datasets']),
    ('Main', 'Size (rows)', 'size_categories:100M<n<1B', '1B', 'https://example.com/icons/classification.png', NOW() + INTERVAL '14 second', NOW() + INTERVAL '14 second','size_categories',ARRAY['datasets']),
    ('Main', 'Size (rows)', 'size_categories:1B<n<10B', '10B', 'https://example.com/icons/classification.png', NOW() + INTERVAL '15 second', NOW() + INTERVAL '15 second','size_categories',ARRAY['datasets']),
    ('Main', 'Size (rows)', 'size_categories:10B<n<100B', '100B', 'https://example.com/icons/classification.png', NOW() + INTERVAL '16 second', NOW() + INTERVAL '16 second','size_categories',ARRAY['datasets']),
    ('Main', 'Size (rows)', 'size_categories:100B<n<1T', '1T', 'https://example.com/icons/classification.png', NOW() + INTERVAL '17 second', NOW() + INTERVAL '17 second','size_categories',ARRAY['datasets']),
    ('Main', 'Size (rows)', 'size_categories:n>1T', '>1T', 'https://example.com/icons/classification.png', NOW() + INTERVAL '18 second', NOW() + INTERVAL '18 second','size_categories',ARRAY['datasets']);


CREATE TABLE IF NOT EXISTS "repo_tags" (
                                    "repository_id" UUID NOT NULL,
                                    "tag_id" UUID NOT NULL,
                                    PRIMARY KEY ("repository_id", "tag_id")
);

ALTER TABLE "repo_tags" ADD FOREIGN KEY ("repository_id") REFERENCES "repositories" ("id");
ALTER TABLE "repo_tags" ADD FOREIGN KEY ("tag_id") REFERENCES "tags" ("id");

ALTER TABLE "repositories" ADD metadata jsonb NULL;
CREATE INDEX repositories_metadata_idx ON public.repositories ("metadata");

-- Speed up filtering on `type` and `sub_type`
CREATE INDEX tags_repo_type_idx ON tags (type);
CREATE INDEX tags_sub_type_idx ON tags (sub_type);
-- Speed up filtering on `name` and `value`
CREATE INDEX tags_name_value_idx ON tags (name, value);
