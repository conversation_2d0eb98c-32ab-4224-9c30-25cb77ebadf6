-- Remove permissions for composes repositories
DELETE FROM app_permissions WHERE permission IN ('composes.add', 'composes.read', 'composes.files.read');

-- Remove ref_git_composes_id column from user_git_groups table
ALTER TABLE "user_git_groups" DROP COLUMN IF EXISTS "ref_git_composes_id";

-- Remove ref_git_composes_id column from org_git_groups table
ALTER TABLE "org_git_groups" DROP COLUMN IF EXISTS "ref_git_composes_id";
