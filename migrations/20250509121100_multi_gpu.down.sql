ALTER TABLE public.hardwares DROP CONSTRAINT IF EXISTS hardwares_repo_id_fkey;
ALTER TABLE public.hardwares DROP CONSTRAINT IF EXISTS hardwares_repo_id_unique;

ALTER TABLE public.hardwares
ADD CONSTRAINT hardwares_name_key UNIQUE (name);

ALTER TABLE public.hardwares
DROP COLUMN gpu_model;

ALTER TABLE public.hardwares
DROP COLUMN repo_id;

ALTER TABLE public."deployments"
ADD CONSTRAINT deployments_hardware_id_fkey
FOREIGN KEY (hardware_id)
REFERENCES public.hardwares(id);

-- Add back hardware_id column to repositories
ALTER TABLE public.repositories ADD COLUMN hardware_id uuid;

-- Add back the foreign key constraint
ALTER TABLE public.repositories
ADD CONSTRAINT repositories_hardware_id_fkey
FOREIGN KEY (hardware_id)
REFERENCES public.hardwares(id);
