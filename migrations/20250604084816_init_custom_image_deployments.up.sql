CREATE TABLE IF NOT EXISTS custom_image_deployments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    deployment_name TEXT NOT NULL UNIQUE,
    image_uri TEXT NOT NULL,
    user_id UUID,
    org_id UUID,
    node_name TEXT,
    port INTEGER NOT NULL,
    env JSONB NOT NULL DEFAULT '{}'::JSON<PERSON>,
    num_cpu INTEGER NOT NULL,
    mem INTEGER NOT NULL,
    gpu_model TEXT,
    gpu_mem INTEGER,
    proxy_body_size INTEGER DEFAULT 100,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

COMMENT ON COLUMN custom_image_deployments.proxy_body_size IS 'Proxy body size limit in megabytes for handling large request bodies';

CREATE INDEX IF NOT EXISTS idx_custom_image_deployments_user_id ON custom_image_deployments(user_id);
CREATE INDEX IF NOT EXISTS idx_custom_image_deployments_org_id ON custom_image_deployments(org_id);

ALTER TABLE "custom_image_deployments" ADD FOREIGN KEY ("user_id") REFERENCES "users" ("id") ON DELETE SET NULL;
ALTER TABLE "custom_image_deployments" ADD FOREIGN KEY ("org_id") REFERENCES "organizations" ("id") ON DELETE SET NULL;

ALTER TYPE app_permission ADD VALUE 'ecr.read';
ALTER TYPE app_permission ADD VALUE 'ecr.edit';
ALTER TYPE app_permission ADD VALUE 'ecr.delete';
