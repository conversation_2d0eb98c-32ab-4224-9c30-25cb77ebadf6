create or replace function update_user_password(
    email_addr text,
    password text
)
returns void
language plpgsql
as $$
declare 
  user_id uuid;
  encrypted_pw text;
begin
    encrypted_pw := crypt(password, gen_salt('bf'));
    SELECT id INTO user_id FROM auth.users as t WHERE t.email = email_addr;

    UPDATE auth.users
    SET encrypted_password = encrypted_pw
    WHERE id = user_id;
end;
$$;