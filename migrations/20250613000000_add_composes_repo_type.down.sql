-- remove new value from app_permission type in PostgreSQL, create a new type and drop the old one
CREATE TYPE "app_permission_old" AS ENUM (
  'users.read',
  'users.edit',
  'users.delete',
  'users.approve',
  'users.invite',
  'keys.read',
  'keys.edit',
  'keys.delete',
  'organizations.read',
  'organizations.edit',
  'organizations.delete',
  'spaces.add',
  'spaces.read',
  'datasets.add',
  'datasets.read',
  'models.add',
  'models.read',
  'datasets.files.read',
  'models.files.read',
  'spaces.files.read',
  'ecr.read',
  'ecr.edit',
  'ecr.delete'
);
ALTER TABLE app_permissions
ALTER COLUMN permission TYPE "app_permission_old" USING permission::text::"app_permission_old";

DROP TYPE "app_permission";
ALTER TYPE "app_permission_old" RENAME TO "app_permission";

-- remove new value from repo_type type in PostgreSQL, create a new type and drop the old one
CREATE TYPE "repo_type_old" AS ENUM (
  'spaces',
  'models',
  'datasets'
);
ALTER TABLE repositories
ALTER COLUMN type TYPE "repo_type_old" USING type::text::"repo_type_old";

DROP TYPE "repo_type";
ALTER TYPE "repo_type_old" RENAME TO "repo_type";
