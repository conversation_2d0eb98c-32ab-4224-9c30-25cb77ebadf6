CREATE TABLE IF NOT EXISTS "org_access_tokens" (
                               "id" uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
                               "org_id" uuid,
                               "name" varchar(150) NOT NULL,
                               "access_token" text  NOT NULL,
                               "scopes" text NOT NULL,
                               "revoked" boolean  NOT NULL,
                               "expires_at" timestamp,
                               "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                               "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);
ALTER TABLE "org_access_tokens" ADD FOREIGN KEY ("org_id") REFERENCES "organizations" ("id") ON DELETE CASCADE;