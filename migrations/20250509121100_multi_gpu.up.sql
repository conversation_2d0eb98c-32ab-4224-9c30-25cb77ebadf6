ALTER TABLE public."hardwares" DROP CONSTRAINT hardwares_name_key;
ALTER TABLE public."hardwares" ADD COLUMN gpu_model varchar(100);
ALTER TABLE public."hardwares" ADD COLUMN repo_id uuid;
ALTER TABLE public."deployments" DROP CONSTRAINT IF EXISTS deployments_hardware_id_fkey;

-- Migrate data
-- Create a temporary table to store the mapping of old hardware IDs to new hardware IDs
CREATE TEMPORARY TABLE hardware_mapping (
    repo_id UUID,
    old_hardware_id UUID,
    new_hardware_id UUID
);

-- For each 'spaces' repository, create a new hardware record and store the mapping
DO $$
DECLARE
    rec RECORD;
    new_hw_id UUID;
BEGIN
    FOR rec IN
        SELECT r.id AS repo_id, h.id AS old_hardware_id, h.name, h.num_cpu, h.mem, h.gpu_mem, h.created_at, h.updated_at
        FROM repositories r
        JOIN hardwares h ON r.hardware_id = h.id
        WHERE r.type = 'spaces'
          AND r.hardware_id IS NOT NULL
    LOOP
        INSERT INTO hardwares (name, num_cpu, mem, created_at, updated_at, repo_id)
        VALUES ('cpu', rec.num_cpu, rec.mem, NOW(), NOW(), rec.repo_id)
        RETURNING id INTO new_hw_id;

        INSERT INTO hardware_mapping (repo_id, old_hardware_id, new_hardware_id)
        VALUES (rec.repo_id, rec.old_hardware_id, new_hw_id);
    END LOOP;
EXCEPTION
    WHEN OTHERS THEN RAISE EXCEPTION 'Error during hardware migration: %', SQLERRM;
END $$;

-- Drop the temporary table
DROP TABLE hardware_mapping;

-- delete old hardwares with no repo_id
DELETE FROM public.hardwares h WHERE h.repo_id is NULL;

--- update ON DELETE CASCADE
ALTER TABLE public.hardwares DROP CONSTRAINT IF EXISTS hardwares_repo_id_fkey;
ALTER TABLE public.hardwares DROP CONSTRAINT IF EXISTS hardwares_repo_id_unique;

-- Add the new UNIQUE constraint
ALTER TABLE public.hardwares
ADD CONSTRAINT hardwares_repo_id_unique UNIQUE (repo_id);

-- Add the new constraint with ON DELETE CASCADE
ALTER TABLE public.hardwares
ADD CONSTRAINT hardwares_repo_id_fkey
FOREIGN KEY (repo_id)
REFERENCES public.repositories(id)
ON DELETE CASCADE;

-- Drop the hardware_id column from repositories table
ALTER TABLE public.repositories DROP CONSTRAINT IF EXISTS repositories_hardware_id_fkey;
ALTER TABLE public.repositories DROP COLUMN hardware_id;
