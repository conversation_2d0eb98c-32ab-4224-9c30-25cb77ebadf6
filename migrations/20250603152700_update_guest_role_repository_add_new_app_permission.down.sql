-- remove new value from app_permission type in PostgreSQL, create a new type and drop the old one
CREATE TYPE "app_permission_old" AS ENUM (
  'users.read',
  'users.edit',
  'users.delete',
  'users.approve',
  'users.invite',
  'keys.read',
  'keys.edit',
  'keys.delete',
  'organizations.read',
  'organizations.edit',
  'organizations.delete',
  'spaces.add',
  'spaces.read',
  'datasets.add',
  'datasets.read',
  'models.add',
  'models.read'
);

ALTER TABLE app_permissions
ALTER COLUMN permission TYPE "app_permission_old" USING permission::text::"app_permission_old";

DROP TYPE "app_permission";
ALTER TYPE "app_permission_old" RENAME TO "app_permission";
