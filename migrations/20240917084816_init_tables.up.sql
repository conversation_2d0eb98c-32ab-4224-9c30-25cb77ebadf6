CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- <PERSON><PERSON> enum types
CREATE TYPE "app_role" AS ENUM (
  'admin',
  'user',
  'guest'
);

CREATE TYPE "app_permission" AS ENUM (
  'users.read',
  'users.edit',
  'users.delete',
  'users.approve',
  'users.invite',
  'keys.read',
  'keys.edit',
  'keys.delete',
  'organizations.read',
  'organizations.edit',
  'organizations.delete',
  'spaces.add',
  'spaces.read',
  'datasets.add',
  'datasets.read',
  'models.add',
  'models.read'
);

CREATE TYPE "org_type" AS ENUM (
  'company',
  'university',
  'open_source',
  'project'
);

CREATE TYPE "repo_type" AS ENUM (
  'spaces',
  'models',
  'datasets'
);

CREATE TYPE "repo_visibility" AS ENUM (
  'private',
  'internal'
);

CREATE TYPE "repo_role" AS ENUM (
  'owner',
  'developer'
);

CREATE TYPE "repo_permission" AS ENUM (
  'repo.edit',
  'repo.delete',
  'repo.members.edit',
  'repo.members.delete',
  'repo.deploy.run',
  'repo.deploy.stop'
);

CREATE TYPE "usage_type" AS ENUM (
  'auth',
  'signing',
  'auth_and_signing'
);

-- Create tables with timestamps
CREATE TABLE IF NOT EXISTS "users" (
                         "id" uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
                         "name" varchar(150) NOT NULL,
                         "username" varchar(200) UNIQUE NOT NULL,
                         "avatar" text,
                         "ref_git_userid" bigint UNIQUE NOT NULL,
                         "git_access_token" text,
                         "git_access_token_expires_at" DATE,
                         "role" app_role NOT NULL,
                         "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                         "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

CREATE TABLE IF NOT EXISTS "user_ssh_public_keys" (
                             "id" uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
                             "user_id" uuid,
                             "name" varchar(150) NOT NULL,
                             "public_key" text UNIQUE NOT NULL,
                             "ref_git_sshkeyid" integer UNIQUE NOT NULL,
                             "usage_type" usage_type NOT NULL,
                             "expires_at" timestamp,
                             "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                             "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

CREATE TABLE IF NOT EXISTS "user_access_tokens" (
                               "id" uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
                               "user_id" uuid,
                               "name" varchar(150) NOT NULL,
                               "access_token" text  NOT NULL,
                               "scopes" text NOT NULL,
                               "revoked" boolean  NOT NULL,
                               "expires_at" timestamp,
                               "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                               "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

CREATE TABLE IF NOT EXISTS "organizations" (
                                 "id" uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
                                 "name" varchar(200) UNIQUE NOT NULL,
                                 "path_name" varchar(200) UNIQUE NOT NULL,
                                 "type" org_type NOT NULL,
                                 "ref_git_groupid" int NOT NULL,
                                 "avatar" text,
                                 "interest" text,
                                 "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                                 "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

CREATE TABLE IF NOT EXISTS "org_members" (
                               "id" uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
                               "user_id" uuid NOT NULL,
                               "org_id" uuid NOT NULL,
                               "role" repo_role NOT NULL,
                               "expires_at" timestamp,
                               "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                               "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

CREATE TABLE IF NOT EXISTS "app_permissions" (
                                   "id" uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
                                   "role" app_role NOT NULL,
                                   "permission" app_permission NOT NULL,
                                   "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                                   "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                                   CONSTRAINT unique_app_role_permission UNIQUE (role, permission)
);

CREATE TABLE IF NOT EXISTS "repositories" (
                                "id" uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
                                "ref_git_repoid" int,
                                "name" varchar(250) NOT NULL,
                                "avatar" text,
                                "visibility" repo_visibility NOT NULL,
                                "type" repo_type NOT NULL,
                                "user_id" uuid,
                                "org_id" uuid,
                                "datasets" text[],
                                "readme_file" text,
                                "license" text,
                                "license_file" text,
                                "languages" text[],
                                "tags" text[],
                                "libraries" text[],
                                "size" text,
                                "format" text,
                                "modalities" text[],
                                "hardware_id" uuid,
                                "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                                "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

CREATE TABLE IF NOT EXISTS "repo_members" (
                                "id" uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
                                "user_id" uuid NOT NULL,
                                "repo_id" uuid NOT NULL,
                                "role" repo_role NOT NULL,
                                "expires_at" timestamp,
                                "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                                "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

CREATE TABLE IF NOT EXISTS "repo_permissions" (
                                    "id" uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
                                    "role" repo_role NOT NULL,
                                    "permission" repo_permission NOT NULL,
                                    "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                                    "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                                    CONSTRAINT unique_repo_role_permission UNIQUE (role, permission)
);

CREATE TABLE IF NOT EXISTS "signup_requests" (
                                 "id" uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
                                 "email" TEXT NOT NULL,
                                 "username" VARCHAR(120) UNIQUE NOT NULL,
                                 "name" VARCHAR(120) NOT NULL,
                                 "password" TEXT NOT NULL,
                                --  "user_id" uuid,
                                 "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                                 "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

CREATE TABLE IF NOT EXISTS "user_git_groups" (
                         "id" uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
                         "user_id" uuid NOT NULL,
                         "ref_git_models_id" bigint UNIQUE NOT NULL,
                         "ref_git_spaces_id" bigint UNIQUE NOT NULL,
                         "ref_git_datasets_id" bigint UNIQUE NOT NULL,
                         "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                         "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

 CREATE TABLE IF NOT EXISTS "org_git_groups" (
                         "id" uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
                         "org_id" uuid NOT NULL,
                         "ref_git_models_id" bigint UNIQUE NOT NULL,
                         "ref_git_spaces_id" bigint UNIQUE NOT NULL,
                         "ref_git_datasets_id" bigint UNIQUE NOT NULL,
                         "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                         "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

CREATE TABLE IF NOT EXISTS "deployments" (
                                    "id" uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
                                    "name" TEXT NOT NULL,
                                    "workflow_name" TEXT NOT NULL, -- argo workflow name
                                    "status" TEXT, -- build status
                                    "duration" REAL, -- build duration
                                    "repo_id" uuid UNIQUE NOT NULL,
                                    "revision" TEXT,
                                    "commit" TEXT,
                                    "hardware_id" UUID NOT NULL,
                                    "created_at" TIMESTAMPTZ DEFAULT NOW(),
                                    "updated_at" TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS "repo_access_tokens" (
                               "id" uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
                               "repo_id" uuid,
                               "name" varchar(150) NOT NULL,
                               "access_token" text  NOT NULL,
                               "role" text NOT NULL,
                               "scopes" text NOT NULL,
                               "ref_git_id" bigint NOT NULL,
                               "revoked" boolean NOT NULL,
                               "expires_at" timestamp,
                               "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                               "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

CREATE TABLE IF NOT EXISTS "hardwares" (
                               "id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                               "name" TEXT UNIQUE NOT NULL,
                               "num_cpu" BIGINT NOT NULL,
                               "mem" BIGINT NOT NULL, -- memory in megabytes
                               "gpu_mem" BIGINT, -- memory in megabytes
                               "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                               "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

CREATE TABLE IF NOT EXISTS "repo_envs" (
                                "id" uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
                                "repo_id" uuid,
                                "env" jsonb not null default '{}'::jsonb,
                                "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                                "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Add foreign key constraints
ALTER TABLE "user_ssh_public_keys" ADD FOREIGN KEY ("user_id") REFERENCES "users" ("id");
ALTER TABLE "user_access_tokens" ADD FOREIGN KEY ("user_id") REFERENCES "users" ("id");
ALTER TABLE "org_members" ADD FOREIGN KEY ("org_id") REFERENCES "organizations" ("id");
ALTER TABLE "org_members" ADD FOREIGN KEY ("user_id") REFERENCES "users" ("id");
ALTER TABLE "repositories" ADD FOREIGN KEY ("user_id") REFERENCES "users" ("id");
ALTER TABLE "repositories" ADD FOREIGN KEY ("org_id") REFERENCES "organizations" ("id");
ALTER TABLE "repositories" ADD FOREIGN KEY ("hardware_id") REFERENCES "hardwares" ("id");
ALTER TABLE "repo_members" ADD FOREIGN KEY ("repo_id") REFERENCES "repositories" ("id");
ALTER TABLE "repo_members" ADD FOREIGN KEY ("user_id") REFERENCES "users" ("id");
ALTER TABLE "user_git_groups" ADD FOREIGN KEY ("user_id") REFERENCES "users" ("id");
ALTER TABLE "org_git_groups" ADD FOREIGN KEY ("org_id") REFERENCES "organizations" ("id");
-- ALTER TABLE "signup_requests" ADD FOREIGN KEY ("user_id") REFERENCES "users" ("id");
ALTER TABLE "deployments" ADD FOREIGN KEY ("repo_id") REFERENCES "repositories" ("id");
ALTER TABLE "deployments" ADD FOREIGN KEY ("hardware_id") REFERENCES "hardwares" ("id");
ALTER TABLE "repo_access_tokens" ADD FOREIGN KEY ("repo_id") REFERENCES "repositories" ("id");
ALTER TABLE "repo_envs" ADD FOREIGN KEY ("repo_id") REFERENCES "repositories" ("id") ON DELETE CASCADE;

INSERT INTO app_permissions (role, permission)
VALUES
-- init admin permissions
('admin', 'users.read'),
('admin', 'users.edit'),
('admin', 'users.delete'),
('admin', 'users.approve'),
('admin', 'users.invite'),
('admin', 'keys.read'),
('admin', 'keys.edit'),
('admin', 'keys.delete'),
('admin', 'organizations.read'),
('admin', 'organizations.edit'),
('admin', 'organizations.delete'),
('admin', 'spaces.add'),
('admin', 'spaces.read'),
('admin', 'datasets.add'),
('admin', 'datasets.read'),
('admin', 'models.add'),
('admin', 'models.read'),
-- init user permissions
('user', 'users.read'),
('user', 'organizations.read'),
('user', 'organizations.edit'),
('user', 'organizations.delete'),
('user', 'spaces.add'),
('user', 'spaces.read'),
('user', 'datasets.add'),
('user', 'datasets.read'),
('user', 'models.add'),
('user', 'models.read'),
('user', 'keys.read'),
('user', 'keys.edit'),
('user', 'keys.delete'),
-- init guest permissions
('guest', 'spaces.read'),
('guest', 'datasets.read'),
('guest', 'models.read'),
('guest', 'organizations.read');

INSERT INTO repo_permissions (role, permission)
VALUES
-- init owner permissions
('owner', 'repo.edit'),
('owner', 'repo.delete'),
('owner', 'repo.members.edit'),
('owner', 'repo.members.delete'),
('owner', 'repo.deploy.run'),
('owner', 'repo.deploy.stop'),
-- init developer permissions
('developer', 'repo.deploy.run'),
('developer', 'repo.deploy.stop');

INSERT INTO hardwares (name, num_cpu, mem, gpu_mem)
VALUES
('cpu', 1, 512, NULL), -- 1 cpu, 512MiB mem, no GPU
('gpu', 1, 4096, 16384); -- 1 cpu, 4GiB mem, 16GiB GPU

-- init admin and admin's default groups
-- INSERT INTO users (id , name, username, ref_git_userid, git_access_token,status, role)
-- VALUES ('f57096b9-1161-4fb0-90f8-143e13087650', 'admin','<EMAIL>', 1, '**************************','assigned', 'admin' );

-- INSERT INTO user_git_groups (user_id, ref_git_models_id, ref_git_spaces_id, ref_git_datasets_id)
-- VALUES ('f57096b9-1161-4fb0-90f8-143e13087650', 7, 6, 8);
