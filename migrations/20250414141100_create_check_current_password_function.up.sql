create or replace function check_current_password(user_id uuid, current_plain_password varchar)
returns json
language plpgsql
security definer
as $$
DECLARE
_uid uuid; -- for checking by 'is not found'
BEGIN
  -- Get user by his current auth.uid and current password
  SELECT id INTO _uid
  FROM auth.users
  WHERE id = user_id
  AND encrypted_password = crypt(current_plain_password::text, auth.users.encrypted_password);

  -- Check the currect password
  IF NOT FOUND THEN
    RAISE EXCEPTION 'incorrect password';
  END IF;

  RETURN '{"data":true}';
END;
$$
