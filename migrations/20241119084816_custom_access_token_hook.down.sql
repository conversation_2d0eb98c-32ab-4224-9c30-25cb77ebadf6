create or replace function public.custom_access_token_hook(event jsonb)
returns jsonb
language plpgsql
as $$
    declare
        claims jsonb;
		user_id jsonb;
    begin
        claims := event->'claims';
		user_id := event->'user_id';

        claims := jsonb_set(claims, '{user_id}', user_id, true);

        -- Update the 'claims' object in the original event
        event := jsonb_set(event, '{claims}', claims);

        -- Return the modified or original event
        return event;
    end;
$$;
