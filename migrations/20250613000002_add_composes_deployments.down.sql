-- remove foreign key constraint on repo_id
ALTER TABLE custom_image_deployments DROP CONSTRAINT IF EXISTS custom_image_deployments_repo_id_fkey;

-- remove unique constraint on deployment_name, namespace
ALTER TABLE custom_image_deployments DROP CONSTRAINT IF EXISTS custom_image_deployments_deployment_name_namespace_key;

-- add unique constraint on deployment_name
ALTER TABLE custom_image_deployments
ADD CONSTRAINT custom_image_deployments_deployment_name_key UNIQUE (deployment_name);

-- remove new columns from custom_image_deployments
ALTER TABLE custom_image_deployments
DROP COLUMN IF EXISTS volumes,
DROP COLUMN IF EXISTS author,
DROP COLUMN IF EXISTS compose_ports,
DROP COLUMN IF EXISTS restart_policy,
DROP COLUMN IF EXISTS deployment_type,
DROP COLUMN IF EXISTS repo_id,
DROP COLUMN IF EXISTS namespace,
DROP COLUMN IF EXISTS error_message;

-- drop type custom_image_deployment_type
DROP TYPE IF EXISTS "custom_image_deployment_type";
