-- Drop foreign keys
ALTER TABLE "repo_members" DROP CONSTRAINT "repo_members_user_id_fkey";
ALTER TABLE "repo_members" DROP CONSTRAINT "repo_members_repo_id_fkey";

ALTER TABLE "repositories" DROP CONSTRAINT "repositories_org_id_fkey";
ALTER TABLE "repositories" DROP CONSTRAINT "repositories_user_id_fkey";
ALTER TABLE "repositories" DROP CONSTRAINT "repositories_hardware_id_fkey";


ALTER TABLE "org_members" DROP CONSTRAINT "org_members_user_id_fkey";
ALTER TABLE "org_members" DROP CONSTRAINT "org_members_org_id_fkey";

ALTER TABLE "user_access_tokens" DROP CONSTRAINT "user_access_tokens_user_id_fkey";
ALTER TABLE "user_ssh_public_keys" DROP CONSTRAINT "user_ssh_public_keys_user_id_fkey";
-- ALTER TABLE "signup_requests" DROP CONSTRAINT "signup_requests_pkey";

-- ALTER TABLE "auth"."users" DROP CONSTRAINT "auth_users_id_fkey";

ALTER TABLE "deployments" DROP CONSTRAINT "deployments_repo_id_fkey";
ALTER TABLE "deployments" DROP CONSTRAINT "deployments_hardware_id_fkey";

ALTER TABLE "user_git_groups" DROP CONSTRAINT "user_git_groups_user_id_fkey";
ALTER TABLE "org_git_groups" DROP CONSTRAINT "org_git_groups_org_id_fkey";
ALTER TABLE "repo_envs" DROP CONSTRAINT "repo_envs_repo_id_fkey";
ALTER TABLE "repo_access_tokens" DROP CONSTRAINT "repo_access_tokens_repo_id_fkey";


-- Drop tables in reverse order 
DROP TABLE IF EXISTS "repo_permissions";
DROP TABLE IF EXISTS "repo_members";
DROP TABLE IF EXISTS "repositories";
DROP TABLE IF EXISTS "app_permissions";
DROP TABLE IF EXISTS "user_app_roles";
DROP TABLE IF EXISTS "org_members";
DROP TABLE IF EXISTS "organizations";
DROP TABLE IF EXISTS "user_access_tokens";
DROP TABLE IF EXISTS "user_ssh_public_keys";
DROP TABLE IF EXISTS "auth"."users";
DROP TABLE IF EXISTS "users";
DROP TABLE IF EXISTS "signup_requests";
DROP TABLE IF EXISTS "deployments";
DROP TABLE IF EXISTS "user_git_groups";
DROP TABLE IF EXISTS "org_git_groups";
DROP TABLE IF EXISTS "repo_envs";
DROP TABLE IF EXISTS "hardwares";
DROP TABLE IF EXISTS "repo_access_tokens";

-- Drop types in reverse order
DROP TYPE IF EXISTS "repo_permission";
DROP TYPE IF EXISTS "repo_role";
DROP TYPE IF EXISTS "repo_visibility";
DROP TYPE IF EXISTS "repo_type";
DROP TYPE IF EXISTS "org_type";
DROP TYPE IF EXISTS "app_permission";
DROP TYPE IF EXISTS "app_role";

-- Drop the schema
DROP SCHEMA IF EXISTS "auth" CASCADE;
