-- remove new value from app_permission type in PostgreSQL, create a new type and drop the old one
CREATE TYPE "app_permission_old" AS ENUM (
  'users.read',
  'users.edit',
  'users.delete',
  'users.approve',
  'users.invite',
  'keys.read',
  'keys.edit',
  'keys.delete',
  'organizations.read',
  'organizations.edit',
  'organizations.delete',
  'spaces.add',
  'spaces.read',
  'datasets.add',
  'datasets.read',
  'models.add',
  'models.read',
  'datasets.files.read',
  'models.files.read',
  'spaces.files.read'
);
ALTER TABLE app_permissions
ALTER COLUMN permission TYPE "app_permission_old" USING permission::text::"app_permission_old";

DROP TYPE "app_permission";
ALTER TYPE "app_permission_old" RENAME TO "app_permission";

-- drop indexes
DROP INDEX IF EXISTS idx_custom_image_deployments_user_id;
DROP INDEX IF EXISTS idx_custom_image_deployments_org_id;

-- drop foreign keys
ALTER TABLE "custom_image_deployments" DROP CONSTRAINT "custom_image_deployments_user_id_fkey";
ALTER TABLE "custom_image_deployments" DROP CONSTRAINT "custom_image_deployments_org_id_fkey";

-- drop table
DROP TABLE IF EXISTS custom_image_deployments;
