CREATE TABLE IF NOT EXISTS "templates" (
                                    "id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                                    "type" VARCHAR(50),         -- 
                                    "name" VARCHAR(50),             -- 
                                    "icon" VARCHAR(500),       --
                                    "git_ref_id" bigint UNIQUE NOT NULL,   -- gitlab id
                                    "created_at" TIMESTAMPTZ DEFAULT NOW(),
                                    "updated_at" TIMESTAMPTZ DEFAULT NOW()
);


INSERT INTO "templates" (id, type, name, git_ref_id) 
VALUES 
    (uuid_generate_v4(), 'Frontend', 'Front-End Template', 62),
    (uuid_generate_v4(), 'Backend', 'Back-End Template', 63);
