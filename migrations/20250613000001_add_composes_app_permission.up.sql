-- Add ref_git_composes_id column to org_git_groups table
ALTER TABLE "org_git_groups" ADD COLUMN "ref_git_composes_id" bigint UNIQUE;

-- Add ref_git_composes_id column to user_git_groups table
ALTER TABLE "user_git_groups" ADD COLUMN "ref_git_composes_id" bigint UNIQUE;

-- Add permissions for user and admin roles
INSERT INTO app_permissions (role, permission)
VALUES
('user', 'composes.add'),
('user', 'composes.read'),
('user', 'composes.files.read'),
('admin', 'composes.add'),
('admin', 'composes.read'),
('admin', 'composes.files.read');
