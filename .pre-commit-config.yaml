# See https://pre-commit.com for more information
# See https://pre-commit.com/hooks.html for more hooks

fail_fast: true

exclude: ^docs/

repos:
- repo: https://github.com/pre-commit/pre-commit-hooks
  rev: v3.2.0
  hooks:
    - id: trailing-whitespace
    # - id: end-of-file-fixer
    - id: check-yaml
    - id: check-added-large-files

- repo: local
  hooks:
    - id: goimports
      name: Fix import
      entry: goimports -w
      language: golang
      types: [go]
      require_serial: true
      additional_dependencies: ['golang.org/x/tools/cmd/goimports@latest']

    - id: fmt
      name: Go format
      entry: gofmt -w
      language: system
      types: [go]
      require_serial: true

    - id: golangci-lint-config-verify
      name: golangci-lint-config-verify
      entry: golangci-lint config verify
      files: '\.golangci\.(?:yml|yaml|toml|json)'
      language: golang
      require_serial: true
      pass_filenames: false
      additional_dependencies: ['github.com/golangci/golangci-lint/cmd/golangci-lint@v1.64.8']

    - id: golangci-lint
      name: golangci-lint
      entry: golangci-lint run ./... --new-from-rev HEAD --fix
      language: golang
      types: [go]
      require_serial: true
      pass_filenames: false
      additional_dependencies: ['github.com/golangci/golangci-lint/cmd/golangci-lint@v1.64.8']

- repo: local
  hooks:
    - id: swag-fmt
      name: Format swaggo/swag comment
      entry: swag fmt
      pass_filenames: false
      language: golang
      require_serial: true
      additional_dependencies: ['github.com/swaggo/swag/cmd/swag@latest']

    - id: swag-init
      name: Build swaggo/swag doc
      entry: swag init -g ./cmd/api-server/swagger_docs.go --exclude ./internal/models --outputTypes go,yaml -d . --parseDependency --parseInternal --parseDepth 1 -o docs
      pass_filenames: false
      language: golang
      require_serial: false
      additional_dependencies: ['github.com/swaggo/swag/cmd/swag@latest']
