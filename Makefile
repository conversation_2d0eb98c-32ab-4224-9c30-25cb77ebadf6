
init:
	go install github.com/swaggo/swag/cmd/swag@latest
	go install -tags "postgres" github.com/golang-migrate/migrate/v4/cmd/migrate@latest
	go install github.com/vektra/mockery/v2@v2.46.2
	go mod tidy
	@$(MAKE) swagger

clean:
	go clean -modcache

run:
	./run.sh

test:
	go clean -testcache
	go test ./cmd/api-server ./internal/... ./pkg/...

swagger:
	`go env GOPATH`/bin/swag fmt
	`go env GOPATH`/bin/swag init --generalInfo ./cmd/api-server/swagger_docs.go \
		--outputTypes go,yaml --generatedTime \
		--dir ./ --parseDependency --parseInternal --parseDepth 1 -o ./docs

mock:
	`go env GOPATH`/bin/mockery

PSQL_CONN_STR := 'postgres://postgres:postgres@localhost:5432/volvo?sslmode=disable'
migrate-up:
	`go env GOPATH`/bin/migrate -database ${PSQL_CONN_STR} -path ./migrations/ up

migrate-down:
	`go env GOPATH`/bin/migrate -database ${PSQL_CONN_STR} -path ./migrations/ down

migrate-force:
	`go env GOPATH`/bin/migrate -database ${PSQL_CONN_STR} -path ./migrations/ force 20240917084816


IMAGE_NAME:=volvo/api-server:latest
CONTAINER_NAME:=volvo-api-server
docker-build:
	docker build -t ${IMAGE_NAME} -f Dockerfile .

docker-run:
	docker run -p 8080:8080 --name ${CONTAINER_NAME} ${IMAGE_NAME}

docker-rm:
	@docker stop ${CONTAINER_NAME}
	@docker rm ${CONTAINER_NAME}