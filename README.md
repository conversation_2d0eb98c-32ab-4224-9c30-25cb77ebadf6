# Volvo API Server

## Install pre-commit
- Install:
    - [pre-commit](https://pre-commit.com/#install)

```bash
pre-commit install
```

## Getting Started

```bash
make init
```

## Supabase Auth
- [Supabase Auth Doc](./AUTH.md)

## Config environment

```bash
cp config.tmp.yaml config.yaml
```
## Run service
```bash
docker compose up
```

## Run migrate
``` bash
migrate -path migrations -database "$DATABASE_URL"  up
```

## Run server
```bash
make run
```

## Run all unit tests
```bash
go test ./...
```

- With coverage
```bash
go test ./... -coverprofile=coverage.out
grep -vE "/mocks/|/docs/|/dto/|/configs/|swagger_docs|types" coverage.out > coverage_filtered.out
go tool cover -func=coverage_filtered.out
```

## References

- [Golang project layout](https://github.com/golang-standards/project-layout)
- [Gin-Gonic Swagger library](https://github.com/swaggo/gin-swagger)
