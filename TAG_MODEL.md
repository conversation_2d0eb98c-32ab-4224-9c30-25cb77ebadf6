---
language:
  - Python
  - JavaScript
  - TypeScript

license: MIT
license_name: MIT License
license_link: https://opensource.org/licenses/MIT

libraries:
  - PyTorch
  - TensorFlow
  - Hugging Face Transformers

task_categories:
  - Natural Language Processing
  - Computer Vision
  - Speech Recognition

library_name: transformers

tags:
  - deep-learning
  - nlp
  - bert
  - text-classification

datasets:
  - GLUE
  - SQuAD
  - MNLI

metrics:
  - accuracy
  - f1-score
  - precision
  - recall

base_model: bert-base-uncased

model-index:
  - name: bert-base-uncased-finetuned-mrpc
    results:
      - task:
          type: text-classification
          name: Text Classification
        dataset:
          type: glue
          name: MRPC
          config: mrpc
          split: validation
          revision: 1.0.0
          args:
            max_length: 128
            batch_size: 32
        metrics:
          - type: accuracy
            value: 0.85
            name: Accuracy
            config: default
            args:
              threshold: 0.5
          - type: f1
            value: 0.88
            name: F1 Score
            config: default
            args:
              average: weighted
        source:
          name: Hugging Face
          url: https://huggingface.co/bert-base-uncased-finetuned-mrpc

  - name: bert-base-uncased-finetuned-squad
    results:
      - task:
          type: question-answering
          name: Question Answering
        dataset:
          type: squad
          name: SQuAD
          config: v1.1
          split: validation
          revision: 1.0.0
          args:
            max_length: 384
            stride: 128
        metrics:
          - type: exact_match
            value: 0.82
            name: Exact Match
            config: default
            args: {}
          - type: f1
            value: 0.89
            name: F1 Score
            config: default
            args:
              average: weighted
        source:
          name: Hugging Face
          url: https://huggingface.co/bert-base-uncased-finetuned-squad
---
