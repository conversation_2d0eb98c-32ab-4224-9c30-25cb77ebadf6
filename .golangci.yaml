linters:
  disable-all: true
  enable:
    - gocritic
    - gosimple
    - gocognit
    - goconst
    - govet
    - errcheck
    # - exhaustive
    - funlen
    - cyclop
    - importas
    - ineffassign
    - staticcheck
    - unused

run:
  # Number of operating system threads (`GOMAXPROCS`) that can execute golangci-lint simultaneously.
  # If it is explicitly set to 0 (i.e. not the default) then golangci-lint will automatically set the value to match Linux container CPU quota.
  # Default: the number of logical CPUs in the machine
  concurrency: 3
  # Timeout for analysis, e.g. 30s, 5m.
  # Default: 1m
  timeout: 1m
  # Exit code when at least one issue was found.
  # Default: 1
  issues-exit-code: 2
  # Include test files or not.
  # Default: true
  tests: false
  # If set, we pass it to "go list -mod={option}". From "go help modules":
  # If invoked with -mod=readonly, the go command is disallowed from the implicit
  # automatic updating of go.mod described above. Instead, it fails when any changes
  # to go.mod are needed. This setting is most useful to check that go.mod does
  # not need updates, such as in a continuous integration and testing system.
  # If invoked with -mod=vendor, the go command assumes that the vendor
  # directory holds the correct copies of dependencies and ignores
  # the dependency descriptions in go.mod.
  #
  # Allowed values: readonly|vendor|mod
  # Default: ""
  modules-download-mode: readonly
  # Allow multiple parallel golangci-lint instances running.
  # If false, golangci-lint acquires file lock on start.
  # Default: false
  allow-parallel-runners: true
  # Allow multiple golangci-lint instances running, but serialize them around a lock.
  # If false, golangci-lint exits with an error if it fails to acquire file lock on start.
  # Default: false
  allow-serial-runners: false

linters-settings:
  funlen:
    lines: 100 # Set maximum allowed lines in a function
    statements: 80 # Optional: Maximum number of statements (default is 40)
    ignore-comments: true # Optional: Whether to ignore comments when counting lines
