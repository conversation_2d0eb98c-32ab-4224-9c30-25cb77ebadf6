package jwt_test

import (
	"context"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"api-server/pkg/jwt"
)

func TestGenerateJWT(t *testing.T) {
	ctx := context.Background()
	secretKey := []byte("test-secret-key")
	userID := uuid.New()
	expiry := 1 * time.Hour

	tests := []struct {
		name        string
		claims      jwt.CustomClaims
		secretKey   []byte
		expiry      time.Duration
		expectError bool
		errorMsg    string
	}{
		{
			name: "Valid claims",
			claims: jwt.CustomClaims{
				UserID: userID,
			},
			secretKey:   secretKey,
			expiry:      expiry,
			expectError: false,
		},
		{
			name: "Empty secret key",
			claims: jwt.CustomClaims{
				UserID: userID,
			},
			secretKey:   []byte{},
			expiry:      expiry,
			expectError: true,
			errorMsg:    "secret key cannot be empty",
		},
		{
			name: "Zero expiry",
			claims: jwt.CustomClaims{
				UserID: userID,
			},
			secretKey:   secretKey,
			expiry:      0,
			expectError: false,
		},
		{
			name: "Nil secret key",
			claims: jwt.CustomClaims{
				UserID: userID,
			},
			secretKey:   nil,
			expiry:      expiry,
			expectError: true,
			errorMsg:    "secret key cannot be empty",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			token, err := jwt.GenerateJWT(ctx, tt.claims, tt.secretKey, tt.expiry)

			if tt.expectError {
				assert.Error(t, err)
				assert.Nil(t, token)
				if tt.errorMsg != "" {
					assert.Contains(t, err.Error(), tt.errorMsg)
				}
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, token)
				assert.NotEmpty(t, *token)

				// Verify the token can be validated
				claims, err := jwt.ValidateJWT(ctx, *token, tt.secretKey)
				assert.NoError(t, err)
				assert.NotNil(t, claims)
				assert.Equal(t, userID, claims.UserID)

				// For non-zero expiry, verify the expiry time
				if tt.expiry > 0 {
					assert.NotNil(t, claims.ExpiresAt)
					assert.True(t, claims.ExpiresAt.After(time.Now()))
				} else {
					assert.Nil(t, claims.ExpiresAt)
				}
			}
		})
	}
}

func TestValidateJWT(t *testing.T) {
	ctx := context.Background()
	secretKey := []byte("test-secret-key")
	userID := uuid.New()
	expiry := 1 * time.Hour

	// Generate a valid token for testing
	claims := jwt.CustomClaims{
		UserID: userID,
	}
	validToken, err := jwt.GenerateJWT(ctx, claims, secretKey, expiry)
	require.NoError(t, err)
	require.NotNil(t, validToken)

	tests := []struct {
		name        string
		token       string
		secretKey   []byte
		expectError bool
		errorMsg    string
	}{
		{
			name:        "Valid token",
			token:       *validToken,
			secretKey:   secretKey,
			expectError: false,
		},
		{
			name:        "Invalid token",
			token:       "invalid.token.here",
			secretKey:   secretKey,
			expectError: true,
			errorMsg:    "token is malformed",
		},
		{
			name:        "Empty token",
			token:       "",
			secretKey:   secretKey,
			expectError: true,
			errorMsg:    "token is malformed",
		},
		{
			name:        "Wrong secret key",
			token:       *validToken,
			secretKey:   []byte("wrong-secret-key"),
			expectError: true,
			errorMsg:    "token signature is invalid",
		},
		{
			name:        "Empty secret key",
			token:       *validToken,
			secretKey:   []byte{},
			expectError: true,
			errorMsg:    "secret key cannot be empty",
		},
		{
			name:        "Nil secret key",
			token:       *validToken,
			secretKey:   nil,
			expectError: true,
			errorMsg:    "secret key cannot be empty",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			claims, err := jwt.ValidateJWT(ctx, tt.token, tt.secretKey)

			if tt.expectError {
				assert.Error(t, err)
				assert.Nil(t, claims)
				if tt.errorMsg != "" {
					assert.Contains(t, err.Error(), tt.errorMsg)
				}
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, claims)
				assert.Equal(t, userID, claims.UserID)
			}
		})
	}
}

func TestJWTExpiry(t *testing.T) {
	ctx := context.Background()
	secretKey := []byte("test-secret-key")
	userID := uuid.New()
	expiry := 1000 * time.Millisecond

	// Generate a token with short expiry
	claims := jwt.CustomClaims{
		UserID: userID,
	}
	token, err := jwt.GenerateJWT(ctx, claims, secretKey, expiry)
	require.NoError(t, err)
	require.NotNil(t, token)

	// Validate token immediately
	validClaims, err := jwt.ValidateJWT(ctx, *token, secretKey)
	assert.NoError(t, err)
	assert.NotNil(t, validClaims)
	assert.Equal(t, userID, validClaims.UserID)

	// Wait for token to expire
	time.Sleep(expiry + 50*time.Millisecond)

	// Try to validate expired token
	expiredClaims, err := jwt.ValidateJWT(ctx, *token, secretKey)
	assert.Error(t, err)
	assert.Nil(t, expiredClaims)
	assert.Contains(t, err.Error(), "token is expired")
}
