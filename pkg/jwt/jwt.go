package jwt

import (
	"context"
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"go.opentelemetry.io/otel/codes"

	"api-server/pkg/oteltrace"
	"api-server/pkg/otelzap"
)

type CustomClaims struct {
	jwt.RegisteredClaims
	UserID uuid.UUID `json:"user_id,omitempty"`
}

func GenerateJWT(ctx context.Context, claims CustomClaims, secretKey []byte, expiry time.Duration) (*string, error) {
	c, span := oteltrace.Tracer.Start(ctx, "pkg.jwt.GenerateJWT")
	defer span.End()

	if len(secretKey) == 0 {
		err := fmt.Errorf("secret key cannot be empty")
		span.SetStatus(codes.Error, err.Error())
		span.RecordError(err)
		otelzap.ErrorWithContext(c, "failed to generate jwt token", err)
		return nil, err
	}

	issuedAt := time.Now()
	claims.IssuedAt = jwt.NewNumericDate(issuedAt)

	// Only set expiry if it's greater than zero
	if expiry > 0 {
		claims.ExpiresAt = jwt.NewNumericDate(issuedAt.Add(expiry))
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString(secretKey)
	if err != nil {
		span.SetStatus(codes.Error, err.Error())
		span.RecordError(err)
		otelzap.ErrorWithContext(c, "failed to generate jwt token", err)
		return nil, err
	}

	return &tokenString, nil
}

func ValidateJWT(ctx context.Context, tokenString string, secretKey []byte) (*CustomClaims, error) {
	c, span := oteltrace.Tracer.Start(ctx, "pkg.jwt.ValidateJWT")
	defer span.End()

	if len(secretKey) == 0 {
		err := fmt.Errorf("secret key cannot be empty")
		span.SetStatus(codes.Error, err.Error())
		span.RecordError(err)
		otelzap.ErrorWithContext(c, "failed to validate jwt token", err)
		return nil, err
	}

	token, err := jwt.ParseWithClaims(tokenString, &CustomClaims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			err := fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
			span.SetStatus(codes.Error, err.Error())
			span.RecordError(err)
			otelzap.ErrorWithContext(c, "failed to validate jwt token", err)
			return nil, err
		}

		return secretKey, nil
	})
	if err != nil {
		span.SetStatus(codes.Error, err.Error())
		span.RecordError(err)
		otelzap.ErrorWithContext(c, "failed to validate jwt token", err)
		return nil, err
	}

	if claims, ok := token.Claims.(*CustomClaims); ok {
		return claims, nil
	} else {
		return nil, fmt.Errorf("invalid token claims")
	}
}
