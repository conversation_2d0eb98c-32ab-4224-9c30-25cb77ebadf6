package validator_test

import (
	"errors"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"

	"api-server/pkg/validator"
)

func TestNameValidator(t *testing.T) {
	v := validator.GetValidator()

	type TestStruct struct {
		Name string `validate:"name"`
	}

	tests := []struct {
		name     string
		input    TestStruct
		expected bool
	}{
		{"Valid name", TestStruct{"John Doe"}, true},
		{"Valid name with numbers", TestStruct{"John123"}, true},
		{"Valid name with underscore", TestStruct{"John_Doe"}, true},
		{"Valid name with spaces", TestStruct{"John <PERSON>"}, true},
		{"Valid name with dot, space", TestStruct{"John.D Doe"}, true},
		{"Valid name with dot, hyphen", TestStruct{"John.D-Doe"}, true},
		{"Valid name with hyphen", TestStruct{"John D-Doe"}, true},
		{"Valid name", TestStruct{"3D"}, true},
		{"Valid name", TestStruct{"33D"}, true},

		{"Invalid name with dot at the end", TestStruct{"<PERSON> D-Doe."}, false},
		{"Invalid name end with a space", TestStruct{"John Doe "}, false},
		{"Invalid name start with a space", TestStruct{" John Doe"}, false},
		{"Invalid name start with an underscore", TestStruct{"_John Doe"}, false},
		{"Invalid name end with an underscore", TestStruct{"John Doe_"}, false},
		{"Invalid name with special character", TestStruct{"John@Doe"}, false},
		{
			"Invalid name too long",
			TestStruct{"A really long name that exceeds fifty characters just to test validation"},
			false,
		},
		{"Invalid empty name", TestStruct{""}, false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := v.Struct(tt.input)
			valid := err == nil
			assert.Equal(t, tt.expected, valid)
		})
	}
}

func TestUsernameValidator(t *testing.T) {
	v := validator.GetValidator()

	type TestStruct struct {
		Name string `validate:"username"`
	}

	tests := []struct {
		name     string
		input    TestStruct
		expected bool
	}{
		{"Valid username", TestStruct{"john_doe"}, true},
		{"Valid alphanumeric", TestStruct{"john123"}, true},
		{"Valid username with hyphen", TestStruct{"john-doe"}, true},
		{"Valid username with underscore", TestStruct{"user_name"}, true},
		{"Valid long username", TestStruct{"a_very_long_username"}, true},
		{"Valid username with number", TestStruct{"3_D"}, true},
		{"Valid username number", TestStruct{"3D"}, true},
		{"Invalid starts with hyphen", TestStruct{"-username"}, false},
		{"Invalid username with special character", TestStruct{"user@name"}, false},
		{"Invalid username with dot", TestStruct{"john.doe"}, false},
		{"Invalid username with space", TestStruct{"john doe"}, false},
		{"Invalid username with space prefix", TestStruct{" john_doe"}, false},
		{
			"Invalid username too long",
			TestStruct{"a_really_long_username_that_exceeds_the_maximum_length_of_50_characters"},
			false,
		},
		{"Invalid username empty", TestStruct{""}, false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := v.Struct(tt.input)
			valid := err == nil
			assert.Equal(t, tt.expected, valid)
		})
	}
}

func TestRepoNameValidator(t *testing.T) {
	v := validator.GetValidator()

	type TestStruct struct {
		Name string `validate:"repoName"`
	}

	tests := []struct {
		name     string
		input    TestStruct
		expected bool
	}{
		{"Valid username", TestStruct{"john_doe"}, true},
		{"Valid alphanumeric", TestStruct{"john123"}, true},
		{"Valid username with hyphen", TestStruct{"john-doe"}, true},
		{"Valid username with underscore", TestStruct{"user_name"}, true},
		{"Valid username with uppercase", TestStruct{"USER_NAME"}, true},
		{"Valid long username", TestStruct{"a_very_long_username"}, true},

		{"Invalid starts with hyphen", TestStruct{"-username"}, false},
		{"Invalid username with special character", TestStruct{"user@name"}, false},
		{"Invalid username with dot", TestStruct{"john.doe"}, false},
		{"Invalid username with space", TestStruct{"john doe"}, false},
		{"Invalid username with space prefix", TestStruct{" john_DOE"}, false},
		{
			"Invalid username too long",
			TestStruct{"a_really_long_username_that_exceeds_the_maximum_length_of_50_characters"},
			false,
		},
		{"Invalid username have __", TestStruct{"john__doe"}, false},
		{"Invalid username have -_", TestStruct{"john-_-_doe"}, false},
		{"Invalid username have many _", TestStruct{"john________doe"}, false},
		{"Invalid username empty", TestStruct{""}, false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := v.Struct(tt.input)
			valid := err == nil
			assert.Equal(t, tt.expected, valid)
		})
	}
}

func TestAlphanumericUnderscore(t *testing.T) {
	v := validator.GetValidator()

	type TestStruct struct {
		Name string `validate:"alphanumericUnderscore"`
	}

	tests := []struct {
		name     string
		input    TestStruct
		expected bool
	}{
		{"Valid name with numbers", TestStruct{"John123"}, true},
		{"Valid name with underscore", TestStruct{"John_Doe"}, true},
		{"Valid name start with _", TestStruct{"_REST"}, true},
		{"Valid name end with _", TestStruct{"REST_"}, true},
		{"Valid name start with numbers", TestStruct{"123_REST_"}, true},

		{"Invalid name with spaces", TestStruct{"John Doe"}, false},
		{"Invalid name", TestStruct{"John Doe"}, false},
		{"Invalid name with special character", TestStruct{"John@Doe"}, false},
		{"Invalid empty name", TestStruct{""}, false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := v.Struct(tt.input)
			valid := err == nil
			assert.Equal(t, tt.expected, valid)
		})
	}
}

func TestMaxItemsValidator(t *testing.T) {
	v := validator.GetValidator()

	type TestStruct struct {
		Tags string `validate:"maxItems=3"`
	}

	tests := []struct {
		name     string
		input    TestStruct
		expected bool
	}{
		{"Valid with 1 item", TestStruct{"tag1"}, true},
		{"Valid with 2 items", TestStruct{"tag1,tag2"}, true},
		{"Valid with 3 items", TestStruct{"tag1,tag2,tag3"}, true},
		{"Empty string", TestStruct{""}, true},

		{"Invalid with 4 items", TestStruct{"tag1,tag2,tag3,tag4"}, false},
		{"Only commas (invalid)", TestStruct{",,,"}, false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := v.Struct(tt.input)
			valid := err == nil
			assert.Equal(t, tt.expected, valid)
		})
	}
}

func TestKeywordValidator(t *testing.T) {
	v := validator.GetValidator()

	type TestStruct struct {
		Name string `validate:"keyword"`
	}

	tests := []struct {
		name     string
		input    TestStruct
		expected bool
	}{
		{"Valid: simple name", TestStruct{"validName"}, true},
		{"Valid: name with dot", TestStruct{"john.doe"}, true},
		{"Valid: email-like", TestStruct{"<EMAIL>"}, true},
		{"Valid: includes dash", TestStruct{"john-doe"}, true},
		{"Valid: includes space", TestStruct{"hello world"}, true},
		{"Valid: 100 characters", TestStruct{strings.Repeat("a", 100)}, true},
		{"Valid: only allowed symbols", TestStruct{"_.@- "}, true},
		{"Valid: allowed symbols at end", TestStruct{"sample@"}, true},
		{"Valid: allowed symbols at first and at end", TestStruct{"@sample@"}, true},

		{"Invalid: empty string", TestStruct{""}, false},
		{"Invalid: 101 characters", TestStruct{strings.Repeat("a", 101)}, false},
		{"Invalid: contains !", TestStruct{"hello!"}, false},
		{"Invalid: contains #", TestStruct{"#hashtag"}, false},
		{"Invalid: emoji", TestStruct{"hello🙂"}, false},
		{"Invalid: non-ASCII", TestStruct{"tên_tiếng_việt"}, false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := v.Struct(tt.input)
			valid := err == nil
			assert.Equal(t, tt.expected, valid)
		})
	}
}

func TestGitlabReservedProjectsValidator(t *testing.T) {
	v := validator.GetValidator()

	type TestStruct struct {
		Name string `validate:"gitlabReservedProjects"`
	}

	tests := []struct {
		name     string
		input    TestStruct
		expected bool
	}{
		{"Valid: simple name", TestStruct{"aaa"}, true},
		{"Invalid: reserved 1", TestStruct{"\\-"}, false},
		{"Invalid: reserved 2", TestStruct{"badges"}, false},
		{"Invalid: reserved 3", TestStruct{"blame"}, false},
		{"Invalid: reserved 4", TestStruct{"blob"}, false},
		{"Invalid: reserved 5", TestStruct{"builds"}, false},
		{"Invalid: reserved 6", TestStruct{"commits"}, false},
		{"Invalid: reserved 7", TestStruct{"create"}, false},
		{"Invalid: reserved 8", TestStruct{"create_dir"}, false},
		{"Invalid: reserved 9", TestStruct{"edit"}, false},
		{"Invalid: reserved 10", TestStruct{"environments/folders"}, false},
		{"Invalid: reserved 11", TestStruct{"files"}, false},
		{"Invalid: reserved 12", TestStruct{"find_file"}, false},
		{"Invalid: reserved 13", TestStruct{"gitlab-lfs/objects"}, false},
		{"Invalid: reserved 14", TestStruct{"info/lfs/objects"}, false},
		{"Invalid: reserved 15", TestStruct{"new"}, false},
		{"Invalid: reserved 16", TestStruct{"preview"}, false},
		{"Invalid: reserved 17", TestStruct{"raw"}, false},
		{"Invalid: reserved 18", TestStruct{"refs"}, false},
		{"Invalid: reserved 19", TestStruct{"tree"}, false},
		{"Invalid: reserved 20", TestStruct{"update"}, false},
		{"Invalid: reserved 21", TestStruct{"wikis"}, false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := v.Struct(tt.input)
			valid := err == nil
			assert.Equal(t, tt.expected, valid)
		})
	}
}

func TestGitlabReservedGroupsValidator(t *testing.T) {
	v := validator.GetValidator()

	type TestStruct struct {
		Name string `validate:"gitlabReservedGroups"`
	}

	tests := []struct {
		name     string
		input    TestStruct
		expected bool
	}{
		{"Valid: simple name", TestStruct{"aaa"}, true},
		{"Invalid: reserved 1", TestStruct{"\\-"}, false},
		{"Invalid: reserved 2", TestStruct{".well-known"}, false},
		{"Invalid: reserved 3", TestStruct{"404.html"}, false},
		{"Invalid: reserved 4", TestStruct{"422.html"}, false},
		{"Invalid: reserved 5", TestStruct{"500.html"}, false},
		{"Invalid: reserved 6", TestStruct{"502.html"}, false},
		{"Invalid: reserved 7", TestStruct{"503.html"}, false},
		{"Invalid: reserved 8", TestStruct{"admin"}, false},
		{"Invalid: reserved 9", TestStruct{"api"}, false},
		{"Invalid: reserved 10", TestStruct{"apple-touch-icon.png"}, false},
		{"Invalid: reserved 11", TestStruct{"assets"}, false},
		{"Invalid: reserved 12", TestStruct{"dashboard"}, false},
		{"Invalid: reserved 13", TestStruct{"deploy.html"}, false},
		{"Invalid: reserved 14", TestStruct{"explore"}, false},
		{"Invalid: reserved 15", TestStruct{"favicon.ico"}, false},
		{"Invalid: reserved 16", TestStruct{"favicon.png"}, false},
		{"Invalid: reserved 17", TestStruct{"files"}, false},
		{"Invalid: reserved 18", TestStruct{"groups"}, false},
		{"Invalid: reserved 19", TestStruct{"health_check"}, false},
		{"Invalid: reserved 20", TestStruct{"help"}, false},
		{"Invalid: reserved 21", TestStruct{"import"}, false},
		{"Invalid: reserved 22", TestStruct{"jwt"}, false},
		{"Invalid: reserved 23", TestStruct{"login"}, false},
		{"Invalid: reserved 24", TestStruct{"oauth"}, false},
		{"Invalid: reserved 25", TestStruct{"profile"}, false},
		{"Invalid: reserved 26", TestStruct{"projects"}, false},
		{"Invalid: reserved 27", TestStruct{"public"}, false},
		{"Invalid: reserved 28", TestStruct{"robots.txt"}, false},
		{"Invalid: reserved 29", TestStruct{"s"}, false},
		{"Invalid: reserved 30", TestStruct{"search"}, false},
		{"Invalid: reserved 31", TestStruct{"sitemap"}, false},
		{"Invalid: reserved 32", TestStruct{"sitemap.xml"}, false},
		{"Invalid: reserved 33", TestStruct{"sitemap.xml.gz"}, false},
		{"Invalid: reserved 34", TestStruct{"slash-command-logo.png"}, false},
		{"Invalid: reserved 35", TestStruct{"snippets"}, false},
		{"Invalid: reserved 36", TestStruct{"unsubscribes"}, false},
		{"Invalid: reserved 37", TestStruct{"uploads"}, false},
		{"Invalid: reserved 38", TestStruct{"users"}, false},
		{"Invalid: reserved 39", TestStruct{"v2"}, false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := v.Struct(tt.input)
			valid := err == nil
			assert.Equal(t, tt.expected, valid)
		})
	}
}

func TestValidate(t *testing.T) {
	type TestStruct struct {
		Name     string `validate:"name"`
		Username string `validate:"username"`
		RepoName string `validate:"repoName"`
		Tags     string `validate:"maxItems=3"`
		Keyword  string `validate:"keyword"`
		Project  string `validate:"gitlabReservedProjects"`
		Group    string `validate:"gitlabReservedGroups"`
	}

	tests := []struct {
		name     string
		input    TestStruct
		expected error
	}{
		{
			name: "Valid input",
			input: TestStruct{
				Name:     "John Doe",
				Username: "john_doe",
				RepoName: "my-repo",
				Tags:     "tag1,tag2",
				Keyword:  "valid-keyword",
				Project:  "valid-project",
				Group:    "valid-group",
			},
			expected: nil,
		},
		{
			name: "Invalid name",
			input: TestStruct{
				Name:     "Invalid@Name",
				Username: "john_doe",
				RepoName: "my-repo",
				Tags:     "tag1,tag2",
				Keyword:  "valid-keyword",
				Project:  "valid-project",
				Group:    "valid-group",
			},
			expected: errors.New("Name must start and end with a letter or number, may contain letters, numbers, underscores, spaces, and must be between 2 and 50 characters long"),
		},
		{
			name: "Invalid username",
			input: TestStruct{
				Name:     "John Doe",
				Username: "invalid@username",
				RepoName: "my-repo",
				Tags:     "tag1,tag2",
				Keyword:  "valid-keyword",
				Project:  "valid-project",
				Group:    "valid-group",
			},
			expected: errors.New("Username must start with a letter or number, may contain underscores or hyphens, and must be between 2 and 50 characters long."),
		},
		{
			name: "Invalid repo name",
			input: TestStruct{
				Name:     "John Doe",
				Username: "john_doe",
				RepoName: "invalid__repo",
				Tags:     "tag1,tag2",
				Keyword:  "valid-keyword",
				Project:  "valid-project",
				Group:    "valid-group",
			},
			expected: errors.New("RepoName must start with a letter or number, may contain 1 underscore or hyphen between words, and must be between 2 and 50 characters long."),
		},
		{
			name: "Invalid max items",
			input: TestStruct{
				Name:     "John Doe",
				Username: "john_doe",
				RepoName: "my-repo",
				Tags:     "tag1,tag2,tag3,tag4",
				Keyword:  "valid-keyword",
				Project:  "valid-project",
				Group:    "valid-group",
			},
			expected: errors.New("Tags must contain at most 3 items"),
		},
		{
			name: "Invalid keyword",
			input: TestStruct{
				Name:     "John Doe",
				Username: "john_doe",
				RepoName: "my-repo",
				Tags:     "tag1,tag2",
				Keyword:  "invalid!keyword",
				Project:  "valid-project",
				Group:    "valid-group",
			},
			expected: errors.New("Keyword must start and end with a letter or number, may contain letters, numbers, underscores, spaces, at and must be between 1 and 100 characters long"),
		},
		{
			name: "Invalid gitlab reserved project",
			input: TestStruct{
				Name:     "John Doe",
				Username: "john_doe",
				RepoName: "my-repo",
				Tags:     "tag1,tag2",
				Keyword:  "valid-keyword",
				Project:  "badges",
				Group:    "valid-group",
			},
			expected: errors.New("badges is a reserved keyword. Please choose different input"),
		},
		{
			name: "Invalid gitlab reserved group",
			input: TestStruct{
				Name:     "John Doe",
				Username: "john_doe",
				RepoName: "my-repo",
				Tags:     "tag1,tag2",
				Keyword:  "valid-keyword",
				Project:  "valid-project",
				Group:    "admin",
			},
			expected: errors.New("admin is a reserved keyword. Please choose different input"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validator.Validate(tt.input)
			if tt.expected == nil {
				assert.NoError(t, err)
			} else {
				assert.EqualError(t, err, tt.expected.Error())
			}
		})
	}
}

func TestIsValidSubdomain(t *testing.T) {
	v := validator.GetValidator()

	type TestStruct struct {
		Subdomain string `validate:"isValidSubdomain"`
	}

	tests := []struct {
		name     string
		input    TestStruct
		expected bool
	}{
		{"Valid 1 character", TestStruct{"a"}, true},
		{"Valid name", TestStruct{"example"}, true},
		{"Valid name dash", TestStruct{"sub-domain"}, true},
		{"Valid name double dash", TestStruct{"sub--domain"}, true},
		{"Valid long name", TestStruct{"a-very-long-subdomain-name-that-is-still-valid123"}, true},
		{"Valid start number", TestStruct{"1-a"}, true},
		{"Valid end number", TestStruct{"a-1"}, true},

		{"Invalid empty", TestStruct{""}, false},
		{"Invalid underscore", TestStruct{"sub_domain"}, false},
		{"Invalid start underscore", TestStruct{"_sub-domain"}, false},
		{"Invalid end underscore", TestStruct{"sub-domain_"}, false},
		{"Invalid dot", TestStruct{"sub.domain"}, false},
		{"Invalid start dash", TestStruct{"-startdash"}, false},
		{"Invalid end dash", TestStruct{"endash-"}, false},
		{"Invalid uppercase", TestStruct{"UPPERCASE"}, false},
		{"Invalid double dot", TestStruct{"sub..domain"}, false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := v.Struct(tt.input)
			valid := err == nil
			assert.Equal(t, tt.expected, valid)
		})
	}
}
