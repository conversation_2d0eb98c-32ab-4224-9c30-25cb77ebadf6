package validator

import (
	"errors"
	"fmt"
	"log"
	"regexp"
	"slices"
	"strconv"
	"strings"
	"sync"

	"github.com/go-playground/validator/v10"

	"api-server/internal/enums"
)

var (
	validateIns *validator.Validate
	once        sync.Once
)

func GetValidator() *validator.Validate {
	once.Do(func() {
		validateIns = validator.New(validator.WithRequiredStructEnabled())
		err := validateIns.RegisterValidation("name", nameValidator)
		if err != nil {
			log.Fatalf("failed to register name validator")
		}

		err = validateIns.RegisterValidation("username", usernameValidator)
		if err != nil {
			log.Fatalf("failed to register username validator")
		}

		err = validateIns.RegisterValidation("repoName", repoNameValidator)
		if err != nil {
			log.Fatalf("failed to register repoName validator")
		}

		err = validateIns.RegisterValidation("alphanumericUnderscore", alphanumericWithUnderscore)
		if err != nil {
			log.Fatalf("failed to register alphanumericUnderscore validator")
		}

		err = validateIns.RegisterValidation("maxItems", maxItems)
		if err != nil {
			log.Fatalf("failed to register maxItems validator")
		}

		err = validateIns.RegisterValidation("keyword", keywordValidator)
		if err != nil {
			log.Fatalf("failed to register keyword validator")
		}

		err = validateIns.RegisterValidation(
			"gitlabReservedProjects",
			gitlabReservedProjectsValidator,
		)
		if err != nil {
			log.Fatalf("failed to register gitlabReservedProjects validator")
		}

		err = validateIns.RegisterValidation("gitlabReservedGroups", gitlabReservedGroupsValidator)
		if err != nil {
			log.Fatalf("failed to register gitlabReservedGroups validator")
		}

		err = validateIns.RegisterValidation("isValidSubdomain", isValidSubdomainValidator)
		if err != nil {
			log.Fatalf("failed to register isValidSubdomain validator")
		}
	})

	return validateIns
}

// custom messages for validator types/messages
var customMessages = map[string]string{
	"name":                   "must start and end with a letter or number, may contain letters, numbers, underscores, spaces, and must be between 2 and 50 characters long",
	"username":               "must start with a letter or number, may contain underscores or hyphens, and must be between 2 and 50 characters long.",
	"repoName":               "must start with a letter or number, may contain 1 underscore or hyphen between words, and must be between 2 and 50 characters long.",
	"alphanumericUnderscore": "may contain letters, numbers or underscores",
	"keyword":                "must start and end with a letter or number, may contain letters, numbers, underscores, spaces, at and must be between 1 and 100 characters long",
	"gitlabReservedProjects": "%s is a reserved keyword. Please choose different input",
	"gitlabReservedGroups":   "%s is a reserved keyword. Please choose different input",
	"maxItems":               "must contain at most %s items",
	"isValidSubdomain":       "must start with a letter or number, contain hyphen between words and must be between 1 and 253 characters long",
}

func Validate(input any) error {
	err := GetValidator().Struct(input)
	if err == nil {
		return nil
	}

	// only support custom messages with defined validator types
	var msg string
	for _, e := range err.(validator.ValidationErrors) {
		field := e.Field()
		tag := e.Tag()

		if tag == "gitlabReservedProjects" || tag == "gitlabReservedGroups" {
			value := e.Value()
			if m, exists := customMessages[tag]; exists {
				msg = fmt.Sprintf(m, value)
			}
		} else if tag == "maxItems" {
			if m, exists := customMessages[tag]; exists {
				msg = fmt.Sprintf("%s %s", field, fmt.Sprintf(m, e.Param()))
			}
		} else {
			if m, exists := customMessages[tag]; exists {
				msg = fmt.Sprintf("%s %s", field, m)
			}
		}
	}

	if msg != "" {
		return errors.New(msg)
	}

	return err
}

func nameValidator(fl validator.FieldLevel) bool {
	name := fl.Field().String()
	re := regexp.MustCompile(`^[a-zA-Z0-9]([a-zA-Z0-9_.\-\s]{0,253}[a-zA-Z0-9])+$`)
	return re.MatchString(name) && len(name) <= 50
}

func usernameValidator(fl validator.FieldLevel) bool {
	username := fl.Field().String()
	re := regexp.MustCompile(`^[a-zA-Z0-9]([-_a-zA-Z0-9]{0,253}[a-zA-Z0-9])?$`)
	return re.MatchString(username) && len(username) <= 50
}

func repoNameValidator(fl validator.FieldLevel) bool {
	repoName := fl.Field().String()

	if strings.Contains(repoName, "--") ||
		strings.Contains(repoName, "__") ||
		strings.Contains(repoName, "-_") ||
		strings.Contains(repoName, "_-") {
		return false
	}

	re := regexp.MustCompile(`^[a-zA-Z0-9]([-_a-zA-Z0-9]{0,253}[a-zA-Z0-9])?$`)

	return re.MatchString(repoName) && len(repoName) <= 50
}

func alphanumericWithUnderscore(fl validator.FieldLevel) bool {
	alphanumericUnderscore := fl.Field().String()
	re := regexp.MustCompile(`^[_a-zA-Z0-9]+$`)
	return re.MatchString(alphanumericUnderscore)
}

func maxItems(fl validator.FieldLevel) bool {
	val := fl.Field().String()
	limit, err := strconv.Atoi(fl.Param()) // the number passed after =
	if err != nil {
		limit = 100 //default value
	}

	return len(strings.Split(val, ",")) <= limit
}

func keywordValidator(fl validator.FieldLevel) bool {
	name := fl.Field().String()
	re := regexp.MustCompile(`^[a-zA-Z0-9_.@\-\s]{1,100}$`)
	return re.MatchString(name)
}

func gitlabReservedProjectsValidator(fl validator.FieldLevel) bool {
	value := fl.Field().String()
	return !slices.Contains(enums.GITLAB_RESERVED_PROJECTS, value)
}

func gitlabReservedGroupsValidator(fl validator.FieldLevel) bool {
	value := fl.Field().String()
	return !slices.Contains(enums.GITLAB_RESERVED_GROUPS, value)
}

const dns1123SubdomainFmt = `[a-z0-9]([-a-z0-9]*[a-z0-9])?`
const dns1123SubdomainMaxLength = 253

var dns1123SubdomainRegexp = regexp.MustCompile("^" + dns1123SubdomainFmt + "$")

func isValidSubdomainValidator(fl validator.FieldLevel) bool {
	subdomain := fl.Field().String()
	if len(subdomain) == 0 || len(subdomain) > dns1123SubdomainMaxLength {
		return false
	}
	return dns1123SubdomainRegexp.MatchString(subdomain)
}
