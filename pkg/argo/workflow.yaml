apiVersion: argoproj.io/v1alpha1
kind: Workflow
metadata:
  generateName: workflow-
spec:
  entrypoint: main
  arguments:
    parameters:
    - name: source-repo
      value: _
    - name: result-repo
      value: _
    - name: git-ssl-verify
      value: false
    - name: git-commit-message
      value: _
    - name: git-user-name
      value: _
    - name: git-user-email
      value: _
    - name: git-remote-branch
      value: _
    - name: volvo-apiserver-url
      value: http://host.docker.internal:8080
  onExit: workflow-status

  templates:
  - name: main
    steps:
    - - name: update-workflow-status
        template: workflow-status
        arguments:
            parameters:
            - name: status
              value: Running
    - - name: clone-source-repo
        template: git-clone
        arguments:
            parameters:
            - name: repo
              value: "{{workflow.parameters.source-repo}}"
    - - name: run-container
        template: container
        arguments:
            artifacts:
            - name: source
              from: "{{steps.clone-source-repo.outputs.artifacts.source}}"
    - - name: clone-result-repo
        template: git-clone
        arguments:
            parameters:
            - name: repo
              value: "{{workflow.parameters.result-repo}}"
    - - name: push-result
        template: git-push
        arguments:
          artifacts:
          - name: result
            from: "{{steps.run-container.outputs.artifacts.result}}"
          - name: source
            from: "{{steps.clone-result-repo.outputs.artifacts.source}}"
          parameters:
          - name: git-commit-message
            value: "{{workflow.parameters.git-commit-message}}"
          - name: git-user-name
            value: "{{workflow.parameters.git-user-name}}"
          - name: git-user-email
            value:  "{{workflow.parameters.git-user-email}}"
          - name: git-remote-branch
            value: "{{workflow.parameters.git-remote-branch}}"

  - name: container
    inputs:
      artifacts:
      - name: source
        path: /src
    outputs:
      artifacts:
      - name: result
        path: /src/data.json
    container:
      image: python:3.9-slim
      workingDir: /src
      command: [sh, -c]
      args: ["python main.py && ls"]

  - name: workflow-status
    inputs:
      parameters:
      - name: status
        value: "{{workflow.status}}"
    container:
      image: alpine/curl:latest
      command: [sh, -c]
      env:
      - name: API_KEY
        valueFrom:
          secretKeyRef:
            name: apikey
            key: api-key
      args: ["
        curl --request POST \
        --url {{workflow.parameters.volvo-apiserver-url}}/api/v1/workflows/status \
        --header 'Authorization: Bearer '\"$API_KEY\"'' \
        --header 'Content-Type: application/json' \
        --data-raw '{
          \"name\": \"{{workflow.name}}\",
          \"status\": \"{{inputs.parameters.status}}\",
          \"duration\": {{workflow.duration}}
        }'
        "]

  - name: git-push
    inputs:
      parameters:
      - name: git-commit-message
      - name: git-user-name
      - name: git-user-email
      - name: git-remote-branch
      artifacts:
      - name: result
        path: /src/{{workflow.name}}.json
      - name: source
        path: /src
    container:
      image: alpine/git
      workingDir: /src
      command: [sh, -c]
      args: ["
        echo \"Pushing to Git!\" && \
        git config user.name \"{{inputs.parameters.git-user-name}}\" && \
        git config user.email \"{{inputs.parameters.git-user-email}}\" && \
        git add . && \
        git commit -m \"{{inputs.parameters.git-commit-message}}\" && \
        git -c http.sslVerify={{workflow.parameters.git-ssl-verify}} push {{workflow.parameters.result-repo}} HEAD:{{inputs.parameters.git-remote-branch}} && \
        echo \"Pushed to Git!\"
        "]

  - name: git-clone
    inputs:
      parameters:
      - name: repo
    outputs:
      artifacts:
      - name: source
        path: /src
    container:
      image: alpine/git
      workingDir: /src
      command: [sh, -c]
      args: ["git -c http.sslVerify={{workflow.parameters.git-ssl-verify}} clone --depth 1 {{inputs.parameters.repo}} ."]
