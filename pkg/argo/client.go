package argo

import (
	"context"
	"fmt"

	"github.com/argoproj/argo-workflows/v3/pkg/apiclient"
)

func New(url string, accessToken string) (apiclient.Client, error) {
	var offlineFiles []string

	_, client, err := apiclient.NewClientFromOpts(
		apiclient.Opts{
			ArgoServerOpts: apiclient.ArgoServerOpts{
				URL:                url,
				Path:               "",
				Secure:             true,
				InsecureSkipVerify: true,
				HTTP1:              true,
				Headers:            []string{},
			},
			InstanceID: "",
			AuthSupplier: func() string {
				bearerToken := fmt.Sprintf("Bearer %s", accessToken)
				return bearerToken
			},
			// ClientConfigSupplier: func() clientcmd.ClientConfig { return GetConfig() },
			Offline:      false,
			OfflineFiles: offlineFiles,
			Context:      context.Background(),
		})
	if err != nil {
		return nil, err
	}

	return client, nil
}
