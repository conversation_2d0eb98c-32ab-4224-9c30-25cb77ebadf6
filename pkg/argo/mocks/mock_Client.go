// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	clusterworkflowtemplate "github.com/argoproj/argo-workflows/v3/pkg/apiclient/clusterworkflowtemplate"
	cronworkflow "github.com/argoproj/argo-workflows/v3/pkg/apiclient/cronworkflow"

	info "github.com/argoproj/argo-workflows/v3/pkg/apiclient/info"

	mock "github.com/stretchr/testify/mock"

	workflow "github.com/argoproj/argo-workflows/v3/pkg/apiclient/workflow"

	workflowarchive "github.com/argoproj/argo-workflows/v3/pkg/apiclient/workflowarchive"

	workflowtemplate "github.com/argoproj/argo-workflows/v3/pkg/apiclient/workflowtemplate"
)

// MockClient is an autogenerated mock type for the Client type
type MockClient struct {
	mock.Mock
}

type MockClient_Expecter struct {
	mock *mock.Mock
}

func (_m *MockClient) EXPECT() *MockClient_Expecter {
	return &MockClient_Expecter{mock: &_m.Mock}
}

// NewArchivedWorkflowServiceClient provides a mock function with no fields
func (_m *MockClient) NewArchivedWorkflowServiceClient() (workflowarchive.ArchivedWorkflowServiceClient, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for NewArchivedWorkflowServiceClient")
	}

	var r0 workflowarchive.ArchivedWorkflowServiceClient
	var r1 error
	if rf, ok := ret.Get(0).(func() (workflowarchive.ArchivedWorkflowServiceClient, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() workflowarchive.ArchivedWorkflowServiceClient); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(workflowarchive.ArchivedWorkflowServiceClient)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockClient_NewArchivedWorkflowServiceClient_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'NewArchivedWorkflowServiceClient'
type MockClient_NewArchivedWorkflowServiceClient_Call struct {
	*mock.Call
}

// NewArchivedWorkflowServiceClient is a helper method to define mock.On call
func (_e *MockClient_Expecter) NewArchivedWorkflowServiceClient() *MockClient_NewArchivedWorkflowServiceClient_Call {
	return &MockClient_NewArchivedWorkflowServiceClient_Call{Call: _e.mock.On("NewArchivedWorkflowServiceClient")}
}

func (_c *MockClient_NewArchivedWorkflowServiceClient_Call) Run(run func()) *MockClient_NewArchivedWorkflowServiceClient_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockClient_NewArchivedWorkflowServiceClient_Call) Return(_a0 workflowarchive.ArchivedWorkflowServiceClient, _a1 error) *MockClient_NewArchivedWorkflowServiceClient_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockClient_NewArchivedWorkflowServiceClient_Call) RunAndReturn(run func() (workflowarchive.ArchivedWorkflowServiceClient, error)) *MockClient_NewArchivedWorkflowServiceClient_Call {
	_c.Call.Return(run)
	return _c
}

// NewClusterWorkflowTemplateServiceClient provides a mock function with no fields
func (_m *MockClient) NewClusterWorkflowTemplateServiceClient() (clusterworkflowtemplate.ClusterWorkflowTemplateServiceClient, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for NewClusterWorkflowTemplateServiceClient")
	}

	var r0 clusterworkflowtemplate.ClusterWorkflowTemplateServiceClient
	var r1 error
	if rf, ok := ret.Get(0).(func() (clusterworkflowtemplate.ClusterWorkflowTemplateServiceClient, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() clusterworkflowtemplate.ClusterWorkflowTemplateServiceClient); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(clusterworkflowtemplate.ClusterWorkflowTemplateServiceClient)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockClient_NewClusterWorkflowTemplateServiceClient_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'NewClusterWorkflowTemplateServiceClient'
type MockClient_NewClusterWorkflowTemplateServiceClient_Call struct {
	*mock.Call
}

// NewClusterWorkflowTemplateServiceClient is a helper method to define mock.On call
func (_e *MockClient_Expecter) NewClusterWorkflowTemplateServiceClient() *MockClient_NewClusterWorkflowTemplateServiceClient_Call {
	return &MockClient_NewClusterWorkflowTemplateServiceClient_Call{Call: _e.mock.On("NewClusterWorkflowTemplateServiceClient")}
}

func (_c *MockClient_NewClusterWorkflowTemplateServiceClient_Call) Run(run func()) *MockClient_NewClusterWorkflowTemplateServiceClient_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockClient_NewClusterWorkflowTemplateServiceClient_Call) Return(_a0 clusterworkflowtemplate.ClusterWorkflowTemplateServiceClient, _a1 error) *MockClient_NewClusterWorkflowTemplateServiceClient_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockClient_NewClusterWorkflowTemplateServiceClient_Call) RunAndReturn(run func() (clusterworkflowtemplate.ClusterWorkflowTemplateServiceClient, error)) *MockClient_NewClusterWorkflowTemplateServiceClient_Call {
	_c.Call.Return(run)
	return _c
}

// NewCronWorkflowServiceClient provides a mock function with no fields
func (_m *MockClient) NewCronWorkflowServiceClient() (cronworkflow.CronWorkflowServiceClient, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for NewCronWorkflowServiceClient")
	}

	var r0 cronworkflow.CronWorkflowServiceClient
	var r1 error
	if rf, ok := ret.Get(0).(func() (cronworkflow.CronWorkflowServiceClient, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() cronworkflow.CronWorkflowServiceClient); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(cronworkflow.CronWorkflowServiceClient)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockClient_NewCronWorkflowServiceClient_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'NewCronWorkflowServiceClient'
type MockClient_NewCronWorkflowServiceClient_Call struct {
	*mock.Call
}

// NewCronWorkflowServiceClient is a helper method to define mock.On call
func (_e *MockClient_Expecter) NewCronWorkflowServiceClient() *MockClient_NewCronWorkflowServiceClient_Call {
	return &MockClient_NewCronWorkflowServiceClient_Call{Call: _e.mock.On("NewCronWorkflowServiceClient")}
}

func (_c *MockClient_NewCronWorkflowServiceClient_Call) Run(run func()) *MockClient_NewCronWorkflowServiceClient_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockClient_NewCronWorkflowServiceClient_Call) Return(_a0 cronworkflow.CronWorkflowServiceClient, _a1 error) *MockClient_NewCronWorkflowServiceClient_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockClient_NewCronWorkflowServiceClient_Call) RunAndReturn(run func() (cronworkflow.CronWorkflowServiceClient, error)) *MockClient_NewCronWorkflowServiceClient_Call {
	_c.Call.Return(run)
	return _c
}

// NewInfoServiceClient provides a mock function with no fields
func (_m *MockClient) NewInfoServiceClient() (info.InfoServiceClient, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for NewInfoServiceClient")
	}

	var r0 info.InfoServiceClient
	var r1 error
	if rf, ok := ret.Get(0).(func() (info.InfoServiceClient, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() info.InfoServiceClient); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(info.InfoServiceClient)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockClient_NewInfoServiceClient_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'NewInfoServiceClient'
type MockClient_NewInfoServiceClient_Call struct {
	*mock.Call
}

// NewInfoServiceClient is a helper method to define mock.On call
func (_e *MockClient_Expecter) NewInfoServiceClient() *MockClient_NewInfoServiceClient_Call {
	return &MockClient_NewInfoServiceClient_Call{Call: _e.mock.On("NewInfoServiceClient")}
}

func (_c *MockClient_NewInfoServiceClient_Call) Run(run func()) *MockClient_NewInfoServiceClient_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockClient_NewInfoServiceClient_Call) Return(_a0 info.InfoServiceClient, _a1 error) *MockClient_NewInfoServiceClient_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockClient_NewInfoServiceClient_Call) RunAndReturn(run func() (info.InfoServiceClient, error)) *MockClient_NewInfoServiceClient_Call {
	_c.Call.Return(run)
	return _c
}

// NewWorkflowServiceClient provides a mock function with no fields
func (_m *MockClient) NewWorkflowServiceClient() workflow.WorkflowServiceClient {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for NewWorkflowServiceClient")
	}

	var r0 workflow.WorkflowServiceClient
	if rf, ok := ret.Get(0).(func() workflow.WorkflowServiceClient); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(workflow.WorkflowServiceClient)
		}
	}

	return r0
}

// MockClient_NewWorkflowServiceClient_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'NewWorkflowServiceClient'
type MockClient_NewWorkflowServiceClient_Call struct {
	*mock.Call
}

// NewWorkflowServiceClient is a helper method to define mock.On call
func (_e *MockClient_Expecter) NewWorkflowServiceClient() *MockClient_NewWorkflowServiceClient_Call {
	return &MockClient_NewWorkflowServiceClient_Call{Call: _e.mock.On("NewWorkflowServiceClient")}
}

func (_c *MockClient_NewWorkflowServiceClient_Call) Run(run func()) *MockClient_NewWorkflowServiceClient_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockClient_NewWorkflowServiceClient_Call) Return(_a0 workflow.WorkflowServiceClient) *MockClient_NewWorkflowServiceClient_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockClient_NewWorkflowServiceClient_Call) RunAndReturn(run func() workflow.WorkflowServiceClient) *MockClient_NewWorkflowServiceClient_Call {
	_c.Call.Return(run)
	return _c
}

// NewWorkflowTemplateServiceClient provides a mock function with no fields
func (_m *MockClient) NewWorkflowTemplateServiceClient() (workflowtemplate.WorkflowTemplateServiceClient, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for NewWorkflowTemplateServiceClient")
	}

	var r0 workflowtemplate.WorkflowTemplateServiceClient
	var r1 error
	if rf, ok := ret.Get(0).(func() (workflowtemplate.WorkflowTemplateServiceClient, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() workflowtemplate.WorkflowTemplateServiceClient); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(workflowtemplate.WorkflowTemplateServiceClient)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockClient_NewWorkflowTemplateServiceClient_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'NewWorkflowTemplateServiceClient'
type MockClient_NewWorkflowTemplateServiceClient_Call struct {
	*mock.Call
}

// NewWorkflowTemplateServiceClient is a helper method to define mock.On call
func (_e *MockClient_Expecter) NewWorkflowTemplateServiceClient() *MockClient_NewWorkflowTemplateServiceClient_Call {
	return &MockClient_NewWorkflowTemplateServiceClient_Call{Call: _e.mock.On("NewWorkflowTemplateServiceClient")}
}

func (_c *MockClient_NewWorkflowTemplateServiceClient_Call) Run(run func()) *MockClient_NewWorkflowTemplateServiceClient_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockClient_NewWorkflowTemplateServiceClient_Call) Return(_a0 workflowtemplate.WorkflowTemplateServiceClient, _a1 error) *MockClient_NewWorkflowTemplateServiceClient_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockClient_NewWorkflowTemplateServiceClient_Call) RunAndReturn(run func() (workflowtemplate.WorkflowTemplateServiceClient, error)) *MockClient_NewWorkflowTemplateServiceClient_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockClient creates a new instance of MockClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockClient {
	mock := &MockClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
