// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	context "context"

	grpc "google.golang.org/grpc"

	mock "github.com/stretchr/testify/mock"

	v1alpha1 "github.com/argoproj/argo-workflows/v3/pkg/apis/workflow/v1alpha1"

	workflow "github.com/argoproj/argo-workflows/v3/pkg/apiclient/workflow"
)

// MockWorkflowServiceClient is an autogenerated mock type for the WorkflowServiceClient type
type MockWorkflowServiceClient struct {
	mock.Mock
}

type MockWorkflowServiceClient_Expecter struct {
	mock *mock.Mock
}

func (_m *MockWorkflowServiceClient) EXPECT() *MockWorkflowServiceClient_Expecter {
	return &MockWorkflowServiceClient_Expecter{mock: &_m.Mock}
}

// CreateWorkflow provides a mock function with given fields: ctx, in, opts
func (_m *MockWorkflowServiceClient) CreateWorkflow(ctx context.Context, in *workflow.WorkflowCreateRequest, opts ...grpc.CallOption) (*v1alpha1.Workflow, error) {
	_va := make([]interface{}, len(opts))
	for _i := range opts {
		_va[_i] = opts[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, in)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for CreateWorkflow")
	}

	var r0 *v1alpha1.Workflow
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *workflow.WorkflowCreateRequest, ...grpc.CallOption) (*v1alpha1.Workflow, error)); ok {
		return rf(ctx, in, opts...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *workflow.WorkflowCreateRequest, ...grpc.CallOption) *v1alpha1.Workflow); ok {
		r0 = rf(ctx, in, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*v1alpha1.Workflow)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *workflow.WorkflowCreateRequest, ...grpc.CallOption) error); ok {
		r1 = rf(ctx, in, opts...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockWorkflowServiceClient_CreateWorkflow_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateWorkflow'
type MockWorkflowServiceClient_CreateWorkflow_Call struct {
	*mock.Call
}

// CreateWorkflow is a helper method to define mock.On call
//   - ctx context.Context
//   - in *workflow.WorkflowCreateRequest
//   - opts ...grpc.CallOption
func (_e *MockWorkflowServiceClient_Expecter) CreateWorkflow(ctx interface{}, in interface{}, opts ...interface{}) *MockWorkflowServiceClient_CreateWorkflow_Call {
	return &MockWorkflowServiceClient_CreateWorkflow_Call{Call: _e.mock.On("CreateWorkflow",
		append([]interface{}{ctx, in}, opts...)...)}
}

func (_c *MockWorkflowServiceClient_CreateWorkflow_Call) Run(run func(ctx context.Context, in *workflow.WorkflowCreateRequest, opts ...grpc.CallOption)) *MockWorkflowServiceClient_CreateWorkflow_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]grpc.CallOption, len(args)-2)
		for i, a := range args[2:] {
			if a != nil {
				variadicArgs[i] = a.(grpc.CallOption)
			}
		}
		run(args[0].(context.Context), args[1].(*workflow.WorkflowCreateRequest), variadicArgs...)
	})
	return _c
}

func (_c *MockWorkflowServiceClient_CreateWorkflow_Call) Return(_a0 *v1alpha1.Workflow, _a1 error) *MockWorkflowServiceClient_CreateWorkflow_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockWorkflowServiceClient_CreateWorkflow_Call) RunAndReturn(run func(context.Context, *workflow.WorkflowCreateRequest, ...grpc.CallOption) (*v1alpha1.Workflow, error)) *MockWorkflowServiceClient_CreateWorkflow_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteWorkflow provides a mock function with given fields: ctx, in, opts
func (_m *MockWorkflowServiceClient) DeleteWorkflow(ctx context.Context, in *workflow.WorkflowDeleteRequest, opts ...grpc.CallOption) (*workflow.WorkflowDeleteResponse, error) {
	_va := make([]interface{}, len(opts))
	for _i := range opts {
		_va[_i] = opts[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, in)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for DeleteWorkflow")
	}

	var r0 *workflow.WorkflowDeleteResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *workflow.WorkflowDeleteRequest, ...grpc.CallOption) (*workflow.WorkflowDeleteResponse, error)); ok {
		return rf(ctx, in, opts...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *workflow.WorkflowDeleteRequest, ...grpc.CallOption) *workflow.WorkflowDeleteResponse); ok {
		r0 = rf(ctx, in, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*workflow.WorkflowDeleteResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *workflow.WorkflowDeleteRequest, ...grpc.CallOption) error); ok {
		r1 = rf(ctx, in, opts...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockWorkflowServiceClient_DeleteWorkflow_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteWorkflow'
type MockWorkflowServiceClient_DeleteWorkflow_Call struct {
	*mock.Call
}

// DeleteWorkflow is a helper method to define mock.On call
//   - ctx context.Context
//   - in *workflow.WorkflowDeleteRequest
//   - opts ...grpc.CallOption
func (_e *MockWorkflowServiceClient_Expecter) DeleteWorkflow(ctx interface{}, in interface{}, opts ...interface{}) *MockWorkflowServiceClient_DeleteWorkflow_Call {
	return &MockWorkflowServiceClient_DeleteWorkflow_Call{Call: _e.mock.On("DeleteWorkflow",
		append([]interface{}{ctx, in}, opts...)...)}
}

func (_c *MockWorkflowServiceClient_DeleteWorkflow_Call) Run(run func(ctx context.Context, in *workflow.WorkflowDeleteRequest, opts ...grpc.CallOption)) *MockWorkflowServiceClient_DeleteWorkflow_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]grpc.CallOption, len(args)-2)
		for i, a := range args[2:] {
			if a != nil {
				variadicArgs[i] = a.(grpc.CallOption)
			}
		}
		run(args[0].(context.Context), args[1].(*workflow.WorkflowDeleteRequest), variadicArgs...)
	})
	return _c
}

func (_c *MockWorkflowServiceClient_DeleteWorkflow_Call) Return(_a0 *workflow.WorkflowDeleteResponse, _a1 error) *MockWorkflowServiceClient_DeleteWorkflow_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockWorkflowServiceClient_DeleteWorkflow_Call) RunAndReturn(run func(context.Context, *workflow.WorkflowDeleteRequest, ...grpc.CallOption) (*workflow.WorkflowDeleteResponse, error)) *MockWorkflowServiceClient_DeleteWorkflow_Call {
	_c.Call.Return(run)
	return _c
}

// GetWorkflow provides a mock function with given fields: ctx, in, opts
func (_m *MockWorkflowServiceClient) GetWorkflow(ctx context.Context, in *workflow.WorkflowGetRequest, opts ...grpc.CallOption) (*v1alpha1.Workflow, error) {
	_va := make([]interface{}, len(opts))
	for _i := range opts {
		_va[_i] = opts[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, in)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for GetWorkflow")
	}

	var r0 *v1alpha1.Workflow
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *workflow.WorkflowGetRequest, ...grpc.CallOption) (*v1alpha1.Workflow, error)); ok {
		return rf(ctx, in, opts...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *workflow.WorkflowGetRequest, ...grpc.CallOption) *v1alpha1.Workflow); ok {
		r0 = rf(ctx, in, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*v1alpha1.Workflow)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *workflow.WorkflowGetRequest, ...grpc.CallOption) error); ok {
		r1 = rf(ctx, in, opts...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockWorkflowServiceClient_GetWorkflow_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetWorkflow'
type MockWorkflowServiceClient_GetWorkflow_Call struct {
	*mock.Call
}

// GetWorkflow is a helper method to define mock.On call
//   - ctx context.Context
//   - in *workflow.WorkflowGetRequest
//   - opts ...grpc.CallOption
func (_e *MockWorkflowServiceClient_Expecter) GetWorkflow(ctx interface{}, in interface{}, opts ...interface{}) *MockWorkflowServiceClient_GetWorkflow_Call {
	return &MockWorkflowServiceClient_GetWorkflow_Call{Call: _e.mock.On("GetWorkflow",
		append([]interface{}{ctx, in}, opts...)...)}
}

func (_c *MockWorkflowServiceClient_GetWorkflow_Call) Run(run func(ctx context.Context, in *workflow.WorkflowGetRequest, opts ...grpc.CallOption)) *MockWorkflowServiceClient_GetWorkflow_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]grpc.CallOption, len(args)-2)
		for i, a := range args[2:] {
			if a != nil {
				variadicArgs[i] = a.(grpc.CallOption)
			}
		}
		run(args[0].(context.Context), args[1].(*workflow.WorkflowGetRequest), variadicArgs...)
	})
	return _c
}

func (_c *MockWorkflowServiceClient_GetWorkflow_Call) Return(_a0 *v1alpha1.Workflow, _a1 error) *MockWorkflowServiceClient_GetWorkflow_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockWorkflowServiceClient_GetWorkflow_Call) RunAndReturn(run func(context.Context, *workflow.WorkflowGetRequest, ...grpc.CallOption) (*v1alpha1.Workflow, error)) *MockWorkflowServiceClient_GetWorkflow_Call {
	_c.Call.Return(run)
	return _c
}

// LintWorkflow provides a mock function with given fields: ctx, in, opts
func (_m *MockWorkflowServiceClient) LintWorkflow(ctx context.Context, in *workflow.WorkflowLintRequest, opts ...grpc.CallOption) (*v1alpha1.Workflow, error) {
	_va := make([]interface{}, len(opts))
	for _i := range opts {
		_va[_i] = opts[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, in)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for LintWorkflow")
	}

	var r0 *v1alpha1.Workflow
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *workflow.WorkflowLintRequest, ...grpc.CallOption) (*v1alpha1.Workflow, error)); ok {
		return rf(ctx, in, opts...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *workflow.WorkflowLintRequest, ...grpc.CallOption) *v1alpha1.Workflow); ok {
		r0 = rf(ctx, in, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*v1alpha1.Workflow)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *workflow.WorkflowLintRequest, ...grpc.CallOption) error); ok {
		r1 = rf(ctx, in, opts...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockWorkflowServiceClient_LintWorkflow_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LintWorkflow'
type MockWorkflowServiceClient_LintWorkflow_Call struct {
	*mock.Call
}

// LintWorkflow is a helper method to define mock.On call
//   - ctx context.Context
//   - in *workflow.WorkflowLintRequest
//   - opts ...grpc.CallOption
func (_e *MockWorkflowServiceClient_Expecter) LintWorkflow(ctx interface{}, in interface{}, opts ...interface{}) *MockWorkflowServiceClient_LintWorkflow_Call {
	return &MockWorkflowServiceClient_LintWorkflow_Call{Call: _e.mock.On("LintWorkflow",
		append([]interface{}{ctx, in}, opts...)...)}
}

func (_c *MockWorkflowServiceClient_LintWorkflow_Call) Run(run func(ctx context.Context, in *workflow.WorkflowLintRequest, opts ...grpc.CallOption)) *MockWorkflowServiceClient_LintWorkflow_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]grpc.CallOption, len(args)-2)
		for i, a := range args[2:] {
			if a != nil {
				variadicArgs[i] = a.(grpc.CallOption)
			}
		}
		run(args[0].(context.Context), args[1].(*workflow.WorkflowLintRequest), variadicArgs...)
	})
	return _c
}

func (_c *MockWorkflowServiceClient_LintWorkflow_Call) Return(_a0 *v1alpha1.Workflow, _a1 error) *MockWorkflowServiceClient_LintWorkflow_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockWorkflowServiceClient_LintWorkflow_Call) RunAndReturn(run func(context.Context, *workflow.WorkflowLintRequest, ...grpc.CallOption) (*v1alpha1.Workflow, error)) *MockWorkflowServiceClient_LintWorkflow_Call {
	_c.Call.Return(run)
	return _c
}

// ListWorkflows provides a mock function with given fields: ctx, in, opts
func (_m *MockWorkflowServiceClient) ListWorkflows(ctx context.Context, in *workflow.WorkflowListRequest, opts ...grpc.CallOption) (*v1alpha1.WorkflowList, error) {
	_va := make([]interface{}, len(opts))
	for _i := range opts {
		_va[_i] = opts[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, in)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for ListWorkflows")
	}

	var r0 *v1alpha1.WorkflowList
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *workflow.WorkflowListRequest, ...grpc.CallOption) (*v1alpha1.WorkflowList, error)); ok {
		return rf(ctx, in, opts...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *workflow.WorkflowListRequest, ...grpc.CallOption) *v1alpha1.WorkflowList); ok {
		r0 = rf(ctx, in, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*v1alpha1.WorkflowList)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *workflow.WorkflowListRequest, ...grpc.CallOption) error); ok {
		r1 = rf(ctx, in, opts...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockWorkflowServiceClient_ListWorkflows_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListWorkflows'
type MockWorkflowServiceClient_ListWorkflows_Call struct {
	*mock.Call
}

// ListWorkflows is a helper method to define mock.On call
//   - ctx context.Context
//   - in *workflow.WorkflowListRequest
//   - opts ...grpc.CallOption
func (_e *MockWorkflowServiceClient_Expecter) ListWorkflows(ctx interface{}, in interface{}, opts ...interface{}) *MockWorkflowServiceClient_ListWorkflows_Call {
	return &MockWorkflowServiceClient_ListWorkflows_Call{Call: _e.mock.On("ListWorkflows",
		append([]interface{}{ctx, in}, opts...)...)}
}

func (_c *MockWorkflowServiceClient_ListWorkflows_Call) Run(run func(ctx context.Context, in *workflow.WorkflowListRequest, opts ...grpc.CallOption)) *MockWorkflowServiceClient_ListWorkflows_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]grpc.CallOption, len(args)-2)
		for i, a := range args[2:] {
			if a != nil {
				variadicArgs[i] = a.(grpc.CallOption)
			}
		}
		run(args[0].(context.Context), args[1].(*workflow.WorkflowListRequest), variadicArgs...)
	})
	return _c
}

func (_c *MockWorkflowServiceClient_ListWorkflows_Call) Return(_a0 *v1alpha1.WorkflowList, _a1 error) *MockWorkflowServiceClient_ListWorkflows_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockWorkflowServiceClient_ListWorkflows_Call) RunAndReturn(run func(context.Context, *workflow.WorkflowListRequest, ...grpc.CallOption) (*v1alpha1.WorkflowList, error)) *MockWorkflowServiceClient_ListWorkflows_Call {
	_c.Call.Return(run)
	return _c
}

// PodLogs provides a mock function with given fields: ctx, in, opts
func (_m *MockWorkflowServiceClient) PodLogs(ctx context.Context, in *workflow.WorkflowLogRequest, opts ...grpc.CallOption) (workflow.WorkflowService_PodLogsClient, error) {
	_va := make([]interface{}, len(opts))
	for _i := range opts {
		_va[_i] = opts[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, in)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for PodLogs")
	}

	var r0 workflow.WorkflowService_PodLogsClient
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *workflow.WorkflowLogRequest, ...grpc.CallOption) (workflow.WorkflowService_PodLogsClient, error)); ok {
		return rf(ctx, in, opts...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *workflow.WorkflowLogRequest, ...grpc.CallOption) workflow.WorkflowService_PodLogsClient); ok {
		r0 = rf(ctx, in, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(workflow.WorkflowService_PodLogsClient)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *workflow.WorkflowLogRequest, ...grpc.CallOption) error); ok {
		r1 = rf(ctx, in, opts...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockWorkflowServiceClient_PodLogs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PodLogs'
type MockWorkflowServiceClient_PodLogs_Call struct {
	*mock.Call
}

// PodLogs is a helper method to define mock.On call
//   - ctx context.Context
//   - in *workflow.WorkflowLogRequest
//   - opts ...grpc.CallOption
func (_e *MockWorkflowServiceClient_Expecter) PodLogs(ctx interface{}, in interface{}, opts ...interface{}) *MockWorkflowServiceClient_PodLogs_Call {
	return &MockWorkflowServiceClient_PodLogs_Call{Call: _e.mock.On("PodLogs",
		append([]interface{}{ctx, in}, opts...)...)}
}

func (_c *MockWorkflowServiceClient_PodLogs_Call) Run(run func(ctx context.Context, in *workflow.WorkflowLogRequest, opts ...grpc.CallOption)) *MockWorkflowServiceClient_PodLogs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]grpc.CallOption, len(args)-2)
		for i, a := range args[2:] {
			if a != nil {
				variadicArgs[i] = a.(grpc.CallOption)
			}
		}
		run(args[0].(context.Context), args[1].(*workflow.WorkflowLogRequest), variadicArgs...)
	})
	return _c
}

func (_c *MockWorkflowServiceClient_PodLogs_Call) Return(_a0 workflow.WorkflowService_PodLogsClient, _a1 error) *MockWorkflowServiceClient_PodLogs_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockWorkflowServiceClient_PodLogs_Call) RunAndReturn(run func(context.Context, *workflow.WorkflowLogRequest, ...grpc.CallOption) (workflow.WorkflowService_PodLogsClient, error)) *MockWorkflowServiceClient_PodLogs_Call {
	_c.Call.Return(run)
	return _c
}

// ResubmitWorkflow provides a mock function with given fields: ctx, in, opts
func (_m *MockWorkflowServiceClient) ResubmitWorkflow(ctx context.Context, in *workflow.WorkflowResubmitRequest, opts ...grpc.CallOption) (*v1alpha1.Workflow, error) {
	_va := make([]interface{}, len(opts))
	for _i := range opts {
		_va[_i] = opts[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, in)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for ResubmitWorkflow")
	}

	var r0 *v1alpha1.Workflow
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *workflow.WorkflowResubmitRequest, ...grpc.CallOption) (*v1alpha1.Workflow, error)); ok {
		return rf(ctx, in, opts...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *workflow.WorkflowResubmitRequest, ...grpc.CallOption) *v1alpha1.Workflow); ok {
		r0 = rf(ctx, in, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*v1alpha1.Workflow)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *workflow.WorkflowResubmitRequest, ...grpc.CallOption) error); ok {
		r1 = rf(ctx, in, opts...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockWorkflowServiceClient_ResubmitWorkflow_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ResubmitWorkflow'
type MockWorkflowServiceClient_ResubmitWorkflow_Call struct {
	*mock.Call
}

// ResubmitWorkflow is a helper method to define mock.On call
//   - ctx context.Context
//   - in *workflow.WorkflowResubmitRequest
//   - opts ...grpc.CallOption
func (_e *MockWorkflowServiceClient_Expecter) ResubmitWorkflow(ctx interface{}, in interface{}, opts ...interface{}) *MockWorkflowServiceClient_ResubmitWorkflow_Call {
	return &MockWorkflowServiceClient_ResubmitWorkflow_Call{Call: _e.mock.On("ResubmitWorkflow",
		append([]interface{}{ctx, in}, opts...)...)}
}

func (_c *MockWorkflowServiceClient_ResubmitWorkflow_Call) Run(run func(ctx context.Context, in *workflow.WorkflowResubmitRequest, opts ...grpc.CallOption)) *MockWorkflowServiceClient_ResubmitWorkflow_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]grpc.CallOption, len(args)-2)
		for i, a := range args[2:] {
			if a != nil {
				variadicArgs[i] = a.(grpc.CallOption)
			}
		}
		run(args[0].(context.Context), args[1].(*workflow.WorkflowResubmitRequest), variadicArgs...)
	})
	return _c
}

func (_c *MockWorkflowServiceClient_ResubmitWorkflow_Call) Return(_a0 *v1alpha1.Workflow, _a1 error) *MockWorkflowServiceClient_ResubmitWorkflow_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockWorkflowServiceClient_ResubmitWorkflow_Call) RunAndReturn(run func(context.Context, *workflow.WorkflowResubmitRequest, ...grpc.CallOption) (*v1alpha1.Workflow, error)) *MockWorkflowServiceClient_ResubmitWorkflow_Call {
	_c.Call.Return(run)
	return _c
}

// ResumeWorkflow provides a mock function with given fields: ctx, in, opts
func (_m *MockWorkflowServiceClient) ResumeWorkflow(ctx context.Context, in *workflow.WorkflowResumeRequest, opts ...grpc.CallOption) (*v1alpha1.Workflow, error) {
	_va := make([]interface{}, len(opts))
	for _i := range opts {
		_va[_i] = opts[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, in)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for ResumeWorkflow")
	}

	var r0 *v1alpha1.Workflow
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *workflow.WorkflowResumeRequest, ...grpc.CallOption) (*v1alpha1.Workflow, error)); ok {
		return rf(ctx, in, opts...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *workflow.WorkflowResumeRequest, ...grpc.CallOption) *v1alpha1.Workflow); ok {
		r0 = rf(ctx, in, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*v1alpha1.Workflow)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *workflow.WorkflowResumeRequest, ...grpc.CallOption) error); ok {
		r1 = rf(ctx, in, opts...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockWorkflowServiceClient_ResumeWorkflow_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ResumeWorkflow'
type MockWorkflowServiceClient_ResumeWorkflow_Call struct {
	*mock.Call
}

// ResumeWorkflow is a helper method to define mock.On call
//   - ctx context.Context
//   - in *workflow.WorkflowResumeRequest
//   - opts ...grpc.CallOption
func (_e *MockWorkflowServiceClient_Expecter) ResumeWorkflow(ctx interface{}, in interface{}, opts ...interface{}) *MockWorkflowServiceClient_ResumeWorkflow_Call {
	return &MockWorkflowServiceClient_ResumeWorkflow_Call{Call: _e.mock.On("ResumeWorkflow",
		append([]interface{}{ctx, in}, opts...)...)}
}

func (_c *MockWorkflowServiceClient_ResumeWorkflow_Call) Run(run func(ctx context.Context, in *workflow.WorkflowResumeRequest, opts ...grpc.CallOption)) *MockWorkflowServiceClient_ResumeWorkflow_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]grpc.CallOption, len(args)-2)
		for i, a := range args[2:] {
			if a != nil {
				variadicArgs[i] = a.(grpc.CallOption)
			}
		}
		run(args[0].(context.Context), args[1].(*workflow.WorkflowResumeRequest), variadicArgs...)
	})
	return _c
}

func (_c *MockWorkflowServiceClient_ResumeWorkflow_Call) Return(_a0 *v1alpha1.Workflow, _a1 error) *MockWorkflowServiceClient_ResumeWorkflow_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockWorkflowServiceClient_ResumeWorkflow_Call) RunAndReturn(run func(context.Context, *workflow.WorkflowResumeRequest, ...grpc.CallOption) (*v1alpha1.Workflow, error)) *MockWorkflowServiceClient_ResumeWorkflow_Call {
	_c.Call.Return(run)
	return _c
}

// RetryWorkflow provides a mock function with given fields: ctx, in, opts
func (_m *MockWorkflowServiceClient) RetryWorkflow(ctx context.Context, in *workflow.WorkflowRetryRequest, opts ...grpc.CallOption) (*v1alpha1.Workflow, error) {
	_va := make([]interface{}, len(opts))
	for _i := range opts {
		_va[_i] = opts[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, in)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for RetryWorkflow")
	}

	var r0 *v1alpha1.Workflow
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *workflow.WorkflowRetryRequest, ...grpc.CallOption) (*v1alpha1.Workflow, error)); ok {
		return rf(ctx, in, opts...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *workflow.WorkflowRetryRequest, ...grpc.CallOption) *v1alpha1.Workflow); ok {
		r0 = rf(ctx, in, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*v1alpha1.Workflow)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *workflow.WorkflowRetryRequest, ...grpc.CallOption) error); ok {
		r1 = rf(ctx, in, opts...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockWorkflowServiceClient_RetryWorkflow_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RetryWorkflow'
type MockWorkflowServiceClient_RetryWorkflow_Call struct {
	*mock.Call
}

// RetryWorkflow is a helper method to define mock.On call
//   - ctx context.Context
//   - in *workflow.WorkflowRetryRequest
//   - opts ...grpc.CallOption
func (_e *MockWorkflowServiceClient_Expecter) RetryWorkflow(ctx interface{}, in interface{}, opts ...interface{}) *MockWorkflowServiceClient_RetryWorkflow_Call {
	return &MockWorkflowServiceClient_RetryWorkflow_Call{Call: _e.mock.On("RetryWorkflow",
		append([]interface{}{ctx, in}, opts...)...)}
}

func (_c *MockWorkflowServiceClient_RetryWorkflow_Call) Run(run func(ctx context.Context, in *workflow.WorkflowRetryRequest, opts ...grpc.CallOption)) *MockWorkflowServiceClient_RetryWorkflow_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]grpc.CallOption, len(args)-2)
		for i, a := range args[2:] {
			if a != nil {
				variadicArgs[i] = a.(grpc.CallOption)
			}
		}
		run(args[0].(context.Context), args[1].(*workflow.WorkflowRetryRequest), variadicArgs...)
	})
	return _c
}

func (_c *MockWorkflowServiceClient_RetryWorkflow_Call) Return(_a0 *v1alpha1.Workflow, _a1 error) *MockWorkflowServiceClient_RetryWorkflow_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockWorkflowServiceClient_RetryWorkflow_Call) RunAndReturn(run func(context.Context, *workflow.WorkflowRetryRequest, ...grpc.CallOption) (*v1alpha1.Workflow, error)) *MockWorkflowServiceClient_RetryWorkflow_Call {
	_c.Call.Return(run)
	return _c
}

// SetWorkflow provides a mock function with given fields: ctx, in, opts
func (_m *MockWorkflowServiceClient) SetWorkflow(ctx context.Context, in *workflow.WorkflowSetRequest, opts ...grpc.CallOption) (*v1alpha1.Workflow, error) {
	_va := make([]interface{}, len(opts))
	for _i := range opts {
		_va[_i] = opts[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, in)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for SetWorkflow")
	}

	var r0 *v1alpha1.Workflow
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *workflow.WorkflowSetRequest, ...grpc.CallOption) (*v1alpha1.Workflow, error)); ok {
		return rf(ctx, in, opts...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *workflow.WorkflowSetRequest, ...grpc.CallOption) *v1alpha1.Workflow); ok {
		r0 = rf(ctx, in, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*v1alpha1.Workflow)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *workflow.WorkflowSetRequest, ...grpc.CallOption) error); ok {
		r1 = rf(ctx, in, opts...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockWorkflowServiceClient_SetWorkflow_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetWorkflow'
type MockWorkflowServiceClient_SetWorkflow_Call struct {
	*mock.Call
}

// SetWorkflow is a helper method to define mock.On call
//   - ctx context.Context
//   - in *workflow.WorkflowSetRequest
//   - opts ...grpc.CallOption
func (_e *MockWorkflowServiceClient_Expecter) SetWorkflow(ctx interface{}, in interface{}, opts ...interface{}) *MockWorkflowServiceClient_SetWorkflow_Call {
	return &MockWorkflowServiceClient_SetWorkflow_Call{Call: _e.mock.On("SetWorkflow",
		append([]interface{}{ctx, in}, opts...)...)}
}

func (_c *MockWorkflowServiceClient_SetWorkflow_Call) Run(run func(ctx context.Context, in *workflow.WorkflowSetRequest, opts ...grpc.CallOption)) *MockWorkflowServiceClient_SetWorkflow_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]grpc.CallOption, len(args)-2)
		for i, a := range args[2:] {
			if a != nil {
				variadicArgs[i] = a.(grpc.CallOption)
			}
		}
		run(args[0].(context.Context), args[1].(*workflow.WorkflowSetRequest), variadicArgs...)
	})
	return _c
}

func (_c *MockWorkflowServiceClient_SetWorkflow_Call) Return(_a0 *v1alpha1.Workflow, _a1 error) *MockWorkflowServiceClient_SetWorkflow_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockWorkflowServiceClient_SetWorkflow_Call) RunAndReturn(run func(context.Context, *workflow.WorkflowSetRequest, ...grpc.CallOption) (*v1alpha1.Workflow, error)) *MockWorkflowServiceClient_SetWorkflow_Call {
	_c.Call.Return(run)
	return _c
}

// StopWorkflow provides a mock function with given fields: ctx, in, opts
func (_m *MockWorkflowServiceClient) StopWorkflow(ctx context.Context, in *workflow.WorkflowStopRequest, opts ...grpc.CallOption) (*v1alpha1.Workflow, error) {
	_va := make([]interface{}, len(opts))
	for _i := range opts {
		_va[_i] = opts[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, in)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for StopWorkflow")
	}

	var r0 *v1alpha1.Workflow
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *workflow.WorkflowStopRequest, ...grpc.CallOption) (*v1alpha1.Workflow, error)); ok {
		return rf(ctx, in, opts...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *workflow.WorkflowStopRequest, ...grpc.CallOption) *v1alpha1.Workflow); ok {
		r0 = rf(ctx, in, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*v1alpha1.Workflow)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *workflow.WorkflowStopRequest, ...grpc.CallOption) error); ok {
		r1 = rf(ctx, in, opts...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockWorkflowServiceClient_StopWorkflow_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'StopWorkflow'
type MockWorkflowServiceClient_StopWorkflow_Call struct {
	*mock.Call
}

// StopWorkflow is a helper method to define mock.On call
//   - ctx context.Context
//   - in *workflow.WorkflowStopRequest
//   - opts ...grpc.CallOption
func (_e *MockWorkflowServiceClient_Expecter) StopWorkflow(ctx interface{}, in interface{}, opts ...interface{}) *MockWorkflowServiceClient_StopWorkflow_Call {
	return &MockWorkflowServiceClient_StopWorkflow_Call{Call: _e.mock.On("StopWorkflow",
		append([]interface{}{ctx, in}, opts...)...)}
}

func (_c *MockWorkflowServiceClient_StopWorkflow_Call) Run(run func(ctx context.Context, in *workflow.WorkflowStopRequest, opts ...grpc.CallOption)) *MockWorkflowServiceClient_StopWorkflow_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]grpc.CallOption, len(args)-2)
		for i, a := range args[2:] {
			if a != nil {
				variadicArgs[i] = a.(grpc.CallOption)
			}
		}
		run(args[0].(context.Context), args[1].(*workflow.WorkflowStopRequest), variadicArgs...)
	})
	return _c
}

func (_c *MockWorkflowServiceClient_StopWorkflow_Call) Return(_a0 *v1alpha1.Workflow, _a1 error) *MockWorkflowServiceClient_StopWorkflow_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockWorkflowServiceClient_StopWorkflow_Call) RunAndReturn(run func(context.Context, *workflow.WorkflowStopRequest, ...grpc.CallOption) (*v1alpha1.Workflow, error)) *MockWorkflowServiceClient_StopWorkflow_Call {
	_c.Call.Return(run)
	return _c
}

// SubmitWorkflow provides a mock function with given fields: ctx, in, opts
func (_m *MockWorkflowServiceClient) SubmitWorkflow(ctx context.Context, in *workflow.WorkflowSubmitRequest, opts ...grpc.CallOption) (*v1alpha1.Workflow, error) {
	_va := make([]interface{}, len(opts))
	for _i := range opts {
		_va[_i] = opts[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, in)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for SubmitWorkflow")
	}

	var r0 *v1alpha1.Workflow
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *workflow.WorkflowSubmitRequest, ...grpc.CallOption) (*v1alpha1.Workflow, error)); ok {
		return rf(ctx, in, opts...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *workflow.WorkflowSubmitRequest, ...grpc.CallOption) *v1alpha1.Workflow); ok {
		r0 = rf(ctx, in, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*v1alpha1.Workflow)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *workflow.WorkflowSubmitRequest, ...grpc.CallOption) error); ok {
		r1 = rf(ctx, in, opts...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockWorkflowServiceClient_SubmitWorkflow_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SubmitWorkflow'
type MockWorkflowServiceClient_SubmitWorkflow_Call struct {
	*mock.Call
}

// SubmitWorkflow is a helper method to define mock.On call
//   - ctx context.Context
//   - in *workflow.WorkflowSubmitRequest
//   - opts ...grpc.CallOption
func (_e *MockWorkflowServiceClient_Expecter) SubmitWorkflow(ctx interface{}, in interface{}, opts ...interface{}) *MockWorkflowServiceClient_SubmitWorkflow_Call {
	return &MockWorkflowServiceClient_SubmitWorkflow_Call{Call: _e.mock.On("SubmitWorkflow",
		append([]interface{}{ctx, in}, opts...)...)}
}

func (_c *MockWorkflowServiceClient_SubmitWorkflow_Call) Run(run func(ctx context.Context, in *workflow.WorkflowSubmitRequest, opts ...grpc.CallOption)) *MockWorkflowServiceClient_SubmitWorkflow_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]grpc.CallOption, len(args)-2)
		for i, a := range args[2:] {
			if a != nil {
				variadicArgs[i] = a.(grpc.CallOption)
			}
		}
		run(args[0].(context.Context), args[1].(*workflow.WorkflowSubmitRequest), variadicArgs...)
	})
	return _c
}

func (_c *MockWorkflowServiceClient_SubmitWorkflow_Call) Return(_a0 *v1alpha1.Workflow, _a1 error) *MockWorkflowServiceClient_SubmitWorkflow_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockWorkflowServiceClient_SubmitWorkflow_Call) RunAndReturn(run func(context.Context, *workflow.WorkflowSubmitRequest, ...grpc.CallOption) (*v1alpha1.Workflow, error)) *MockWorkflowServiceClient_SubmitWorkflow_Call {
	_c.Call.Return(run)
	return _c
}

// SuspendWorkflow provides a mock function with given fields: ctx, in, opts
func (_m *MockWorkflowServiceClient) SuspendWorkflow(ctx context.Context, in *workflow.WorkflowSuspendRequest, opts ...grpc.CallOption) (*v1alpha1.Workflow, error) {
	_va := make([]interface{}, len(opts))
	for _i := range opts {
		_va[_i] = opts[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, in)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for SuspendWorkflow")
	}

	var r0 *v1alpha1.Workflow
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *workflow.WorkflowSuspendRequest, ...grpc.CallOption) (*v1alpha1.Workflow, error)); ok {
		return rf(ctx, in, opts...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *workflow.WorkflowSuspendRequest, ...grpc.CallOption) *v1alpha1.Workflow); ok {
		r0 = rf(ctx, in, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*v1alpha1.Workflow)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *workflow.WorkflowSuspendRequest, ...grpc.CallOption) error); ok {
		r1 = rf(ctx, in, opts...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockWorkflowServiceClient_SuspendWorkflow_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SuspendWorkflow'
type MockWorkflowServiceClient_SuspendWorkflow_Call struct {
	*mock.Call
}

// SuspendWorkflow is a helper method to define mock.On call
//   - ctx context.Context
//   - in *workflow.WorkflowSuspendRequest
//   - opts ...grpc.CallOption
func (_e *MockWorkflowServiceClient_Expecter) SuspendWorkflow(ctx interface{}, in interface{}, opts ...interface{}) *MockWorkflowServiceClient_SuspendWorkflow_Call {
	return &MockWorkflowServiceClient_SuspendWorkflow_Call{Call: _e.mock.On("SuspendWorkflow",
		append([]interface{}{ctx, in}, opts...)...)}
}

func (_c *MockWorkflowServiceClient_SuspendWorkflow_Call) Run(run func(ctx context.Context, in *workflow.WorkflowSuspendRequest, opts ...grpc.CallOption)) *MockWorkflowServiceClient_SuspendWorkflow_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]grpc.CallOption, len(args)-2)
		for i, a := range args[2:] {
			if a != nil {
				variadicArgs[i] = a.(grpc.CallOption)
			}
		}
		run(args[0].(context.Context), args[1].(*workflow.WorkflowSuspendRequest), variadicArgs...)
	})
	return _c
}

func (_c *MockWorkflowServiceClient_SuspendWorkflow_Call) Return(_a0 *v1alpha1.Workflow, _a1 error) *MockWorkflowServiceClient_SuspendWorkflow_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockWorkflowServiceClient_SuspendWorkflow_Call) RunAndReturn(run func(context.Context, *workflow.WorkflowSuspendRequest, ...grpc.CallOption) (*v1alpha1.Workflow, error)) *MockWorkflowServiceClient_SuspendWorkflow_Call {
	_c.Call.Return(run)
	return _c
}

// TerminateWorkflow provides a mock function with given fields: ctx, in, opts
func (_m *MockWorkflowServiceClient) TerminateWorkflow(ctx context.Context, in *workflow.WorkflowTerminateRequest, opts ...grpc.CallOption) (*v1alpha1.Workflow, error) {
	_va := make([]interface{}, len(opts))
	for _i := range opts {
		_va[_i] = opts[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, in)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for TerminateWorkflow")
	}

	var r0 *v1alpha1.Workflow
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *workflow.WorkflowTerminateRequest, ...grpc.CallOption) (*v1alpha1.Workflow, error)); ok {
		return rf(ctx, in, opts...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *workflow.WorkflowTerminateRequest, ...grpc.CallOption) *v1alpha1.Workflow); ok {
		r0 = rf(ctx, in, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*v1alpha1.Workflow)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *workflow.WorkflowTerminateRequest, ...grpc.CallOption) error); ok {
		r1 = rf(ctx, in, opts...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockWorkflowServiceClient_TerminateWorkflow_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'TerminateWorkflow'
type MockWorkflowServiceClient_TerminateWorkflow_Call struct {
	*mock.Call
}

// TerminateWorkflow is a helper method to define mock.On call
//   - ctx context.Context
//   - in *workflow.WorkflowTerminateRequest
//   - opts ...grpc.CallOption
func (_e *MockWorkflowServiceClient_Expecter) TerminateWorkflow(ctx interface{}, in interface{}, opts ...interface{}) *MockWorkflowServiceClient_TerminateWorkflow_Call {
	return &MockWorkflowServiceClient_TerminateWorkflow_Call{Call: _e.mock.On("TerminateWorkflow",
		append([]interface{}{ctx, in}, opts...)...)}
}

func (_c *MockWorkflowServiceClient_TerminateWorkflow_Call) Run(run func(ctx context.Context, in *workflow.WorkflowTerminateRequest, opts ...grpc.CallOption)) *MockWorkflowServiceClient_TerminateWorkflow_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]grpc.CallOption, len(args)-2)
		for i, a := range args[2:] {
			if a != nil {
				variadicArgs[i] = a.(grpc.CallOption)
			}
		}
		run(args[0].(context.Context), args[1].(*workflow.WorkflowTerminateRequest), variadicArgs...)
	})
	return _c
}

func (_c *MockWorkflowServiceClient_TerminateWorkflow_Call) Return(_a0 *v1alpha1.Workflow, _a1 error) *MockWorkflowServiceClient_TerminateWorkflow_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockWorkflowServiceClient_TerminateWorkflow_Call) RunAndReturn(run func(context.Context, *workflow.WorkflowTerminateRequest, ...grpc.CallOption) (*v1alpha1.Workflow, error)) *MockWorkflowServiceClient_TerminateWorkflow_Call {
	_c.Call.Return(run)
	return _c
}

// WatchEvents provides a mock function with given fields: ctx, in, opts
func (_m *MockWorkflowServiceClient) WatchEvents(ctx context.Context, in *workflow.WatchEventsRequest, opts ...grpc.CallOption) (workflow.WorkflowService_WatchEventsClient, error) {
	_va := make([]interface{}, len(opts))
	for _i := range opts {
		_va[_i] = opts[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, in)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for WatchEvents")
	}

	var r0 workflow.WorkflowService_WatchEventsClient
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *workflow.WatchEventsRequest, ...grpc.CallOption) (workflow.WorkflowService_WatchEventsClient, error)); ok {
		return rf(ctx, in, opts...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *workflow.WatchEventsRequest, ...grpc.CallOption) workflow.WorkflowService_WatchEventsClient); ok {
		r0 = rf(ctx, in, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(workflow.WorkflowService_WatchEventsClient)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *workflow.WatchEventsRequest, ...grpc.CallOption) error); ok {
		r1 = rf(ctx, in, opts...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockWorkflowServiceClient_WatchEvents_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchEvents'
type MockWorkflowServiceClient_WatchEvents_Call struct {
	*mock.Call
}

// WatchEvents is a helper method to define mock.On call
//   - ctx context.Context
//   - in *workflow.WatchEventsRequest
//   - opts ...grpc.CallOption
func (_e *MockWorkflowServiceClient_Expecter) WatchEvents(ctx interface{}, in interface{}, opts ...interface{}) *MockWorkflowServiceClient_WatchEvents_Call {
	return &MockWorkflowServiceClient_WatchEvents_Call{Call: _e.mock.On("WatchEvents",
		append([]interface{}{ctx, in}, opts...)...)}
}

func (_c *MockWorkflowServiceClient_WatchEvents_Call) Run(run func(ctx context.Context, in *workflow.WatchEventsRequest, opts ...grpc.CallOption)) *MockWorkflowServiceClient_WatchEvents_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]grpc.CallOption, len(args)-2)
		for i, a := range args[2:] {
			if a != nil {
				variadicArgs[i] = a.(grpc.CallOption)
			}
		}
		run(args[0].(context.Context), args[1].(*workflow.WatchEventsRequest), variadicArgs...)
	})
	return _c
}

func (_c *MockWorkflowServiceClient_WatchEvents_Call) Return(_a0 workflow.WorkflowService_WatchEventsClient, _a1 error) *MockWorkflowServiceClient_WatchEvents_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockWorkflowServiceClient_WatchEvents_Call) RunAndReturn(run func(context.Context, *workflow.WatchEventsRequest, ...grpc.CallOption) (workflow.WorkflowService_WatchEventsClient, error)) *MockWorkflowServiceClient_WatchEvents_Call {
	_c.Call.Return(run)
	return _c
}

// WatchWorkflows provides a mock function with given fields: ctx, in, opts
func (_m *MockWorkflowServiceClient) WatchWorkflows(ctx context.Context, in *workflow.WatchWorkflowsRequest, opts ...grpc.CallOption) (workflow.WorkflowService_WatchWorkflowsClient, error) {
	_va := make([]interface{}, len(opts))
	for _i := range opts {
		_va[_i] = opts[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, in)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for WatchWorkflows")
	}

	var r0 workflow.WorkflowService_WatchWorkflowsClient
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *workflow.WatchWorkflowsRequest, ...grpc.CallOption) (workflow.WorkflowService_WatchWorkflowsClient, error)); ok {
		return rf(ctx, in, opts...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *workflow.WatchWorkflowsRequest, ...grpc.CallOption) workflow.WorkflowService_WatchWorkflowsClient); ok {
		r0 = rf(ctx, in, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(workflow.WorkflowService_WatchWorkflowsClient)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *workflow.WatchWorkflowsRequest, ...grpc.CallOption) error); ok {
		r1 = rf(ctx, in, opts...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockWorkflowServiceClient_WatchWorkflows_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchWorkflows'
type MockWorkflowServiceClient_WatchWorkflows_Call struct {
	*mock.Call
}

// WatchWorkflows is a helper method to define mock.On call
//   - ctx context.Context
//   - in *workflow.WatchWorkflowsRequest
//   - opts ...grpc.CallOption
func (_e *MockWorkflowServiceClient_Expecter) WatchWorkflows(ctx interface{}, in interface{}, opts ...interface{}) *MockWorkflowServiceClient_WatchWorkflows_Call {
	return &MockWorkflowServiceClient_WatchWorkflows_Call{Call: _e.mock.On("WatchWorkflows",
		append([]interface{}{ctx, in}, opts...)...)}
}

func (_c *MockWorkflowServiceClient_WatchWorkflows_Call) Run(run func(ctx context.Context, in *workflow.WatchWorkflowsRequest, opts ...grpc.CallOption)) *MockWorkflowServiceClient_WatchWorkflows_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]grpc.CallOption, len(args)-2)
		for i, a := range args[2:] {
			if a != nil {
				variadicArgs[i] = a.(grpc.CallOption)
			}
		}
		run(args[0].(context.Context), args[1].(*workflow.WatchWorkflowsRequest), variadicArgs...)
	})
	return _c
}

func (_c *MockWorkflowServiceClient_WatchWorkflows_Call) Return(_a0 workflow.WorkflowService_WatchWorkflowsClient, _a1 error) *MockWorkflowServiceClient_WatchWorkflows_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockWorkflowServiceClient_WatchWorkflows_Call) RunAndReturn(run func(context.Context, *workflow.WatchWorkflowsRequest, ...grpc.CallOption) (workflow.WorkflowService_WatchWorkflowsClient, error)) *MockWorkflowServiceClient_WatchWorkflows_Call {
	_c.Call.Return(run)
	return _c
}

// WorkflowLogs provides a mock function with given fields: ctx, in, opts
func (_m *MockWorkflowServiceClient) WorkflowLogs(ctx context.Context, in *workflow.WorkflowLogRequest, opts ...grpc.CallOption) (workflow.WorkflowService_WorkflowLogsClient, error) {
	_va := make([]interface{}, len(opts))
	for _i := range opts {
		_va[_i] = opts[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, in)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for WorkflowLogs")
	}

	var r0 workflow.WorkflowService_WorkflowLogsClient
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *workflow.WorkflowLogRequest, ...grpc.CallOption) (workflow.WorkflowService_WorkflowLogsClient, error)); ok {
		return rf(ctx, in, opts...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *workflow.WorkflowLogRequest, ...grpc.CallOption) workflow.WorkflowService_WorkflowLogsClient); ok {
		r0 = rf(ctx, in, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(workflow.WorkflowService_WorkflowLogsClient)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *workflow.WorkflowLogRequest, ...grpc.CallOption) error); ok {
		r1 = rf(ctx, in, opts...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockWorkflowServiceClient_WorkflowLogs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WorkflowLogs'
type MockWorkflowServiceClient_WorkflowLogs_Call struct {
	*mock.Call
}

// WorkflowLogs is a helper method to define mock.On call
//   - ctx context.Context
//   - in *workflow.WorkflowLogRequest
//   - opts ...grpc.CallOption
func (_e *MockWorkflowServiceClient_Expecter) WorkflowLogs(ctx interface{}, in interface{}, opts ...interface{}) *MockWorkflowServiceClient_WorkflowLogs_Call {
	return &MockWorkflowServiceClient_WorkflowLogs_Call{Call: _e.mock.On("WorkflowLogs",
		append([]interface{}{ctx, in}, opts...)...)}
}

func (_c *MockWorkflowServiceClient_WorkflowLogs_Call) Run(run func(ctx context.Context, in *workflow.WorkflowLogRequest, opts ...grpc.CallOption)) *MockWorkflowServiceClient_WorkflowLogs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]grpc.CallOption, len(args)-2)
		for i, a := range args[2:] {
			if a != nil {
				variadicArgs[i] = a.(grpc.CallOption)
			}
		}
		run(args[0].(context.Context), args[1].(*workflow.WorkflowLogRequest), variadicArgs...)
	})
	return _c
}

func (_c *MockWorkflowServiceClient_WorkflowLogs_Call) Return(_a0 workflow.WorkflowService_WorkflowLogsClient, _a1 error) *MockWorkflowServiceClient_WorkflowLogs_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockWorkflowServiceClient_WorkflowLogs_Call) RunAndReturn(run func(context.Context, *workflow.WorkflowLogRequest, ...grpc.CallOption) (workflow.WorkflowService_WorkflowLogsClient, error)) *MockWorkflowServiceClient_WorkflowLogs_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockWorkflowServiceClient creates a new instance of MockWorkflowServiceClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockWorkflowServiceClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockWorkflowServiceClient {
	mock := &MockWorkflowServiceClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
