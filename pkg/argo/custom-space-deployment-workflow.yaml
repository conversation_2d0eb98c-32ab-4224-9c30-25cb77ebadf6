apiVersion: argoproj.io/v1alpha1
kind: Workflow
metadata:
  generateName: space-deployment-
  namespace: argo
spec:
  serviceAccountName: argo-executor

  activeDeadlineSeconds: 3600  # Terminate workflow after 1 hour

  artifactGC:
    strategy: OnWorkflowDeletion

  volumes:
  - name: container-build
    emptyDir: {}

  arguments:
    parameters:
    - name: source-repo
      value: _
    - name: source-repo-commit
      value: _
    # - name: build-context-repo
    #   value: _
    - name: timestamp
      value: _
    - name: resource-name
      value: _
    - name: configmap-name
      value: _
    - name: space-name
      value: _
    - name: space-domain
      value: _
    - name: image-tag
      value: _
    - name: image-repository-url
      value: _
    - name: volvo-apiserver-url
      value: _
    - name: cpu-request
      value: _
    - name: cpu-limit
      value: _
    - name: mem-request
      value: _
    - name: mem-limit
      value: _
    - name: ingress-class-name
      value: _

  entrypoint: main
  onExit: update-status

  templates:
  - name: main
    steps:
    - - name: update-deployment-status
        template: update-status
        arguments:
            parameters:
            - name: status
              value: Running
    - - name: clone-source-repo
        template: git-clone
        arguments:
            parameters:
            - name: repo
              value: "{{workflow.parameters.source-repo}}"
      # - name: clone-build-context
      #   template: git-clone
      #   arguments:
      #       parameters:
      #       - name: repo
      #         value: "{{workflow.parameters.build-context-repo}}"
    - - name: build-container
        template: build-container
        arguments:
            artifacts:
            - name: repo
              from: "{{steps.clone-source-repo.outputs.artifacts.repo}}"
            # - name: context
            #   from: "{{steps.clone-build-context.outputs.artifacts.repo}}"
    - - name: apply-deployment
        template: deployment-resource

  - name: build-container
    inputs:
      artifacts:
      - name: repo
        path: /container-build/repo
      # - name: context
      #   path: /container-build/context
    container:
      image: gcr.io/kaniko-project/executor:v1.23.2
      args:
        - --dockerfile=/container-build/repo/Dockerfile
        - --context=dir:///container-build/repo
        - --destination={{workflow.parameters.image-repository-url}}:{{workflow.parameters.image-tag}}
        - --cache=false
        - --use-new-run=true
        - --reproducible=true
        - --push-retry=3
      env:
        - name: AWS_SDK_LOAD_CONFIG
          value: "true"
        - name: AWS_EC2_METADATA_DISABLED
          value: "true"
      volumeMounts:
        - name: container-build
          mountPath: /container-build

  - name: update-status
    retryStrategy:
      limit: 0
      backoff:
        duration: "5s"   # Wait 5 seconds before retrying
        factor: 2         # Increase wait time exponentially (5s, 10s, 20s)
        maxDuration: "60s" # Max wait time between retries
    inputs:
      parameters:
      - name: status
        value: "{{workflow.status}}" # Pending, Running, Succeeded, Failed, Error, Skipped, Unknown
    container:
      image: alpine/curl:latest
      command: [sh, -c]
      args: ["
        curl --request POST \
        --url {{workflow.parameters.volvo-apiserver-url}}/api/v1/repositories/deployments/status \
        --header 'Content-Type: application/json' \
        --data-raw '{
          \"name\": \"{{workflow.name}}\",
          \"status\": \"{{inputs.parameters.status}}\",
          \"duration\": {{workflow.duration}}
        }'
        "]

  - name: git-clone
    inputs:
      parameters:
      - name: repo
    outputs:
      artifacts:
      - name: repo
        path: /repo
    container:
      image: alpine/git
      workingDir: /repo
      command: [sh, -c]
      args:
        - git -c http.sslVerify=false clone {{inputs.parameters.repo}} .
        - git checkout {{workflow.parameters.source-repo-commit}}

  - name: deployment-resource
    resource:
      action: apply
      manifest: |
        apiVersion: apps/v1
        kind: Deployment
        metadata:
          namespace: space
          name: {{workflow.parameters.resource-name}}
        spec:
          replicas: 1
          selector:
            matchLabels:
              app: {{workflow.parameters.resource-name}}
          template:
            metadata:
              annotations:
                timestamp: "{{workflow.parameters.timestamp}}"
              labels:
                app: {{workflow.parameters.resource-name}}
            spec:
              containers:
                - name: {{workflow.parameters.resource-name}}
                  image: {{workflow.parameters.image-repository-url}}:{{workflow.parameters.image-tag}}
                  envFrom:
                    - configMapRef:
                        name: {{workflow.parameters.configmap-name}}
                  ports:
                    - containerPort: 7860
                  resources:
                    requests:
                      cpu: "{{workflow.parameters.cpu-request}}"
                      memory: "{{workflow.parameters.mem-request}}"
                    limits:
                      memory: "{{workflow.parameters.mem-limit}}"
              tolerations:
              - key: nvidia.com/gpu
                operator: Exists
                effect: NoSchedule
