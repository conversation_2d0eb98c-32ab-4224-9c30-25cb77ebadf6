package argo

import (
	"context"
	"errors"
	"fmt"

	workflowpkg "github.com/argoproj/argo-workflows/v3/pkg/apiclient/workflow"
	wfv1 "github.com/argoproj/argo-workflows/v3/pkg/apis/workflow/v1alpha1"
	wfcommon "github.com/argoproj/argo-workflows/v3/workflow/common"
	wfutil "github.com/argoproj/argo-workflows/v3/workflow/util"
	argoJson "github.com/argoproj/pkg/json"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

type CreateWorkflowFromFileFn func(context.Context, workflowpkg.WorkflowServiceClient, string, string, *wfv1.SubmitOpts) ([]string, error)

var CreateWorkflowFromFileFunc CreateWorkflowFromFileFn = CreateWorkflowsFromFile

type WorkflowParams map[string]string

// convert map[key]value to an array of key=value in order to submit workflow with parameters
func (p WorkflowParams) ToKeyValue() []string {
	result := []string{}
	for k, v := range p {
		result = append(result, fmt.Sprintf("%s=%s", k, v))
	}
	return result
}

func CreateWorkflowsFromFile(
	ctx context.Context,
	serviceClient workflowpkg.WorkflowServiceClient,
	namespace string,
	filePath string,
	submitOpts *wfv1.SubmitOpts,
) ([]string, error) {
	fileContents, err := wfutil.ReadManifest(filePath)
	if err != nil {
		return nil, err
	}

	var workflows []wfv1.Workflow
	for _, body := range fileContents {
		wfs := unmarshalWorkflow(body)
		workflows = append(workflows, wfs...)
	}

	return submitWorkflows(ctx, serviceClient, namespace, workflows, submitOpts)
}

func unmarshalWorkflow(wfBytes []byte) []wfv1.Workflow {
	var wf wfv1.Workflow
	var strict bool
	var jsonOpts []argoJson.JSONOpt

	err := argoJson.Unmarshal(wfBytes, &wf, jsonOpts...)
	if err == nil {
		return []wfv1.Workflow{wf}
	}

	yamlWfs, err := wfcommon.SplitWorkflowYAMLFile(wfBytes, strict)
	if err == nil {
		return yamlWfs
	}

	return nil
}

func submitWorkflows(
	ctx context.Context,
	serviceClient workflowpkg.WorkflowServiceClient,
	namespace string,
	workflows []wfv1.Workflow,
	submitOpts *wfv1.SubmitOpts,
) ([]string, error) {
	if len(workflows) == 0 {
		return nil, errors.New("No Workflow found in given files")
	}

	var workflowNames []string
	for _, wf := range workflows {
		if wf.Namespace == "" {
			// This is here to avoid passing an empty namespace when using --server-dry-run
			wf.Namespace = namespace
		}

		err := wfutil.ApplySubmitOpts(&wf, submitOpts)
		if err != nil {
			return nil, err
		}

		options := &metav1.CreateOptions{}
		if submitOpts.DryRun {
			options.DryRun = []string{"All"}
		}
		created, err := serviceClient.CreateWorkflow(ctx, &workflowpkg.WorkflowCreateRequest{
			Namespace:     wf.Namespace,
			Workflow:      &wf,
			ServerDryRun:  submitOpts.ServerDryRun,
			CreateOptions: options,
		})
		if err != nil {
			// return fmt.Errorf("Failed to submit workflow: %v", err)
			return nil, err
		}

		workflowNames = append(workflowNames, created.Name)
	}

	return workflowNames, nil
}
