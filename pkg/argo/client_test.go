package argo_test

import (
	"testing"

	"github.com/argoproj/argo-workflows/v3/pkg/apiclient"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"api-server/pkg/argo"
)

func TestNew(t *testing.T) {
	tests := []struct {
		name        string
		url         string
		accessToken string
		expectError bool
	}{
		{
			name:        "Valid configuration",
			url:         "https://argo-server:2746",
			accessToken: "test-token",
			expectError: false,
		},
		{
			name:        "Empty URL",
			url:         "",
			accessToken: "test-token",
			expectError: false, // Empty URL is allowed
		},
		{
			name:        "Empty access token",
			url:         "https://argo-server:2746",
			accessToken: "",
			expectError: false, // Empty token is allowed
		},
		{
			name:        "Invalid URL",
			url:         "not-a-url",
			accessToken: "test-token",
			expectError: false, // Invalid URL is allowed
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			client, err := argo.New(tt.url, tt.accessToken)

			if tt.expectError {
				assert.Error(t, err)
				assert.Nil(t, client)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, client)

				// Verify client implements the expected interface
				_, ok := client.(apiclient.Client)
				assert.True(t, ok, "Client should implement apiclient.Client interface")
			}
		})
	}
}

func TestClientConfiguration(t *testing.T) {
	// Test with valid configuration
	url := "https://argo-server:2746"
	accessToken := "test-token"

	client, err := argo.New(url, accessToken)
	require.NoError(t, err)
	require.NotNil(t, client)

	// Test that the client can be used
	// Note: This is a basic test to ensure the client is properly configured
	// We're not actually making any API calls
	_, ok := client.(apiclient.Client)
	assert.True(t, ok, "Client should implement apiclient.Client interface")

	// Test that the client has the correct configuration
	// This is a bit of a hack since we can't directly access the client's configuration
	// But we can verify that the client is not nil and implements the expected interface
	assert.NotNil(t, client, "Client should not be nil")
}
