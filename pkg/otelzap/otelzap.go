package otelzap

import (
	"context"
	"os"

	"github.com/google/uuid"
	"go.opentelemetry.io/contrib/bridges/otelzap"
	"go.opentelemetry.io/otel/sdk/log"
	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"

	"api-server/internal/utils"
)

var Logger *zap.Logger

func init() {
	// Use a working LoggerProvider implementation instead e.g. use go.opentelemetry.io/otel/sdk/log.
	provider := log.NewLoggerProvider()

	zapConfig := zap.NewProductionEncoderConfig()
	zapConfig.TimeKey = "timestamp"
	zapConfig.EncodeTime = zapcore.ISO8601TimeEncoder
	zapConfig.StacktraceKey = "stacktrace"
	zapConfig.FunctionKey = "func"
	zapConfig.CallerKey = "caller"

	// If you want to log also on stdout, you can initialize a new zap.Core
	// that has multiple outputs using the method zap.NewTee(). With the following code,
	// logs will be written to stdout and also exported to the OTEL endpoint through the bridge.
	core := zapcore.NewTee(
		zapcore.NewCore(zapcore.NewJSONEncoder(zapConfig), zapcore.AddSync(os.Stdout), zapcore.InfoLevel),
		otelzap.NewCore(os.Getenv("OTEL_SERVICE_NAME"), otelzap.WithLoggerProvider(provider)),
	)

	Logger = zap.New(core, zap.WithCaller(true), zap.AddCallerSkip(1), zap.AddStacktrace(zap.ErrorLevel))
}

// GetDefaultLogFields returns the default log fields for a given context
func GetDefaultLogFields(ctx context.Context) []zap.Field {
	r := []zap.Field{}
	if trace.SpanFromContext(ctx).SpanContext().IsValid() {
		r = append(r, zap.String("trace_id", trace.SpanFromContext(ctx).SpanContext().TraceID().String()))
		r = append(r, zap.String("span_id", trace.SpanFromContext(ctx).SpanContext().SpanID().String()))
	}

	if userID, ok := ctx.Value(utils.ContextKeyUserId).(uuid.UUID); ok && userID != uuid.Nil {
		r = append(r, zap.String("user_id", userID.String()))
	}

	return r
}

func DebugWithContext(ctx context.Context, msg string, fields ...zap.Field) {
	r := GetDefaultLogFields(ctx)
	r = append(r, fields...)
	Logger.Debug(msg, r...)
}

func InfoWithContext(ctx context.Context, msg string, fields ...zap.Field) {
	r := GetDefaultLogFields(ctx)
	r = append(r, fields...)
	Logger.Info(msg, r...)
}

func ErrorWithContext(ctx context.Context, msg string, err error, fields ...zap.Field) {
	r := GetDefaultLogFields(ctx)
	r = append(r, zap.Error(err))
	r = append(r, fields...)
	Logger.Error(msg, r...)
}

func SetErr(err error, fields ...zap.Field) []zap.Field {
	r := []zap.Field{zap.Error(err)}
	r = append(r, fields...)
	return r
}
