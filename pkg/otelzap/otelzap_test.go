package otelzap_test

import (
	"context"
	"errors"
	"testing"

	"github.com/google/uuid"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"go.uber.org/zap/zaptest/observer"

	"api-server/internal/utils"
	"api-server/pkg/otelzap"
)

func TestGetDefaultLogFields(t *testing.T) {
	// Create a test UUID
	testUUID := uuid.New()

	tests := []struct {
		name           string
		ctx            context.Context
		expectedFields int
	}{
		{
			name:           "Empty context",
			ctx:            context.Background(),
			expectedFields: 0,
		},
		{
			name:           "Context with user ID",
			ctx:            utils.WithUserId(context.Background(), testUUID),
			expectedFields: 1,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			fields := otelzap.GetDefaultLogFields(tt.ctx)
			if len(fields) != tt.expectedFields {
				t.<PERSON>rf("Expected %d fields, got %d", tt.expectedFields, len(fields))
			}

			// If we expect a user ID field, verify it
			if tt.expectedFields > 0 {
				found := false
				for _, field := range fields {
					if field.Key == "user_id" && field.String == testUUID.String() {
						found = true
						break
					}
				}
				if !found {
					t.Error("Expected user_id field not found or has incorrect value")
				}
			}
		})
	}
}

func TestLoggingFunctions(t *testing.T) {
	// Create a test logger that we can inspect
	observer, logs := observer.New(zapcore.InfoLevel)
	otelzap.Logger = zap.New(observer)

	// Create test context with user ID
	testUUID := uuid.New()
	ctx := utils.WithUserId(context.Background(), testUUID)

	// Test message and error
	testMsg := "test message"
	testErr := errors.New("test error")
	testField := zap.String("test_key", "test_value")

	t.Run("DebugWithContext", func(t *testing.T) {
		otelzap.DebugWithContext(ctx, testMsg, testField)
		// Note: Debug logs might not be captured by the observer if the level is set to Info
	})

	t.Run("InfoWithContext", func(t *testing.T) {
		otelzap.InfoWithContext(ctx, testMsg, testField)

		if len(logs.All()) == 0 {
			t.Error("Expected log entry, got none")
			return
		}

		lastLog := logs.All()[len(logs.All())-1]
		if lastLog.Message != testMsg {
			t.Errorf("Expected message %q, got %q", testMsg, lastLog.Message)
		}

		// Verify user_id field
		if lastLog.ContextMap()["user_id"] != testUUID.String() {
			t.Error("Expected user_id field not found or has incorrect value")
		}

		// Verify test field
		if lastLog.ContextMap()["test_key"] != "test_value" {
			t.Error("Expected test_key field not found or has incorrect value")
		}
	})

	t.Run("ErrorWithContext", func(t *testing.T) {
		otelzap.ErrorWithContext(ctx, testMsg, testErr, testField)

		if len(logs.All()) == 0 {
			t.Error("Expected log entry, got none")
			return
		}

		lastLog := logs.All()[len(logs.All())-1]
		if lastLog.Message != testMsg {
			t.Errorf("Expected message %q, got %q", testMsg, lastLog.Message)
		}

		// Verify error field
		if lastLog.ContextMap()["error"] != testErr.Error() {
			t.Error("Expected error field not found or has incorrect value")
		}

		// Verify user_id field
		if lastLog.ContextMap()["user_id"] != testUUID.String() {
			t.Error("Expected user_id field not found or has incorrect value")
		}

		// Verify test field
		if lastLog.ContextMap()["test_key"] != "test_value" {
			t.Error("Expected test_key field not found or has incorrect value")
		}
	})

	t.Run("SetErr", func(t *testing.T) {
		fields := otelzap.SetErr(testErr, testField)

		if len(fields) != 2 {
			t.Errorf("Expected 2 fields, got %d", len(fields))
		}

		// Verify error field
		found := false
		for _, field := range fields {
			if field.Key == "error" && field.Interface.(error).Error() == testErr.Error() {
				found = true
				break
			}
		}
		if !found {
			t.Error("Expected error field not found or has incorrect value")
		}
	})
}
