package kube

import (
	"fmt"
	"path/filepath"

	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"

	"api-server/internal/utils"
)

func New(inCluster bool) (*kubernetes.Clientset, error) {
	var config *rest.Config

	if inCluster {
		cfg, err := rest.InClusterConfig()
		if err != nil {
			return nil, fmt.Errorf("Failed to load in-cluster config: %v", err)
		}
		config = cfg
	} else {
		kubeconfig := filepath.Join(
			utils.HomeDir(), ".kube", "config",
		)
		cfg, err := clientcmd.BuildConfigFromFlags("", kubeconfig)
		if err != nil {
			return nil, fmt.Errorf("Error building kubeconfig: %v", err)
		}
		config = cfg
	}

	clientset, err := kubernetes.NewForConfig(config)
	if err != nil {
		return nil, fmt.Errorf("Error creating Kubernetes client: %v", err)
	}

	return clientset, nil
}
