package kube_test

import (
	"context"
	"os"
	"path/filepath"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"

	"api-server/pkg/kube"
)

// verifyClientConnection attempts to make a simple API call to verify the client can connect
func verifyClientConnection(t *testing.T, client *kubernetes.Clientset) error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	_, err := client.CoreV1().Nodes().List(ctx, metav1.ListOptions{})
	return err
}

func TestNew(t *testing.T) {
	// Save original home directory and restore after test
	originalHome := os.Getenv("HOME")
	defer os.Setenv("HOME", originalHome)

	// Create temporary directory for test
	tempDir, err := os.MkdirTemp("", "kube-test")
	require.NoError(t, err)
	defer os.RemoveAll(tempDir)

	// Set HOME to temp directory
	os.Setenv("HOME", tempDir)

	// Create .kube directory
	kubeDir := filepath.Join(tempDir, ".kube")
	err = os.Mkdir(kubeDir, 0755)
	require.NoError(t, err)

	tests := []struct {
		name        string
		inCluster   bool
		setupKube   func() error
		expectError bool
	}{
		{
			name:      "In-cluster config",
			inCluster: true,
			setupKube: func() error {
				// No setup needed for in-cluster
				return nil
			},
			expectError: true, // Will fail unless running in cluster
		},
		{
			name:      "Valid kubeconfig",
			inCluster: false,
			setupKube: func() error {
				// Create a valid kubeconfig file
				config := `apiVersion: v1
kind: Config
clusters:
- cluster:
    server: https://localhost:6443
    insecure-skip-tls-verify: true
  name: test-cluster
contexts:
- context:
    cluster: test-cluster
    user: test-user
  name: test-context
current-context: test-context
users:
- name: test-user
  user:
    token: test-token`
				return os.WriteFile(filepath.Join(kubeDir, "config"), []byte(config), 0644)
			},
			expectError: true, // Will fail because server is not reachable
		},
		{
			name:      "Missing kubeconfig",
			inCluster: false,
			setupKube: func() error {
				// No kubeconfig file
				return nil
			},
			expectError: true,
		},
		{
			name:      "Invalid kubeconfig",
			inCluster: false,
			setupKube: func() error {
				// Create an invalid kubeconfig file
				return os.WriteFile(filepath.Join(kubeDir, "config"), []byte("invalid yaml"), 0644)
			},
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup test environment
			if tt.setupKube != nil {
				err := tt.setupKube()
				require.NoError(t, err)
			}

			// Test client creation
			client, err := kube.New(tt.inCluster)

			if tt.expectError {
				// For error cases, either the client creation should fail
				// or the connection verification should fail
				if err != nil {
					assert.Error(t, err)
					assert.Nil(t, client)
				} else {
					assert.NotNil(t, client)
					err := verifyClientConnection(t, client)
					assert.Error(t, err, "Client should fail to connect to cluster")
				}
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, client)
				err := verifyClientConnection(t, client)
				assert.NoError(t, err, "Client should be able to connect to cluster")
			}
		})
	}
}
