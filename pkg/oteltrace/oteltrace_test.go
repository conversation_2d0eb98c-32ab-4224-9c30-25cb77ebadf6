package oteltrace_test

import (
	"context"
	"os"
	"testing"

	"api-server/pkg/oteltrace"

	"go.opentelemetry.io/otel/trace"
)

func TestTracerInitialization(t *testing.T) {
	// Test that Tracer is initialized
	if oteltrace.Tracer == nil {
		t.<PERSON>rror("Expected Tracer to be initialized")
	}

	// Test that Shutdown function is initialized
	if oteltrace.Shutdown == nil {
		t.<PERSON>rror("Expected Shutdown function to be initialized")
	}
}

func TestTracerFunctionality(t *testing.T) {
	// Create a test span
	ctx := context.Background()
	ctx, span := oteltrace.Tracer.Start(ctx, "test_span")
	defer span.End()

	// Verify span is created
	if span == nil {
		t.Error("Expected span to be created")
	}

	// Verify span context
	spanCtx := trace.SpanContextFromContext(ctx)
	if !spanCtx.IsValid() {
		t.<PERSON>("Expected valid span context")
	}

	// Verify trace ID is generated
	if spanCtx.TraceID().String() == "" {
		t.Error("Expected non-empty trace ID")
	}

	// Verify span ID is generated
	if spanCtx.SpanID().String() == "" {
		t.Error("Expected non-empty span ID")
	}
}

func TestExporterConfiguration(t *testing.T) {
	// Test cases for different environment configurations
	testCases := []struct {
		name    string
		envVars map[string]string
		cleanup func()
	}{
		{
			name:    "Default configuration (stdout)",
			envVars: map[string]string{},
			cleanup: func() {},
		},
		{
			name: "OTLP HTTP configuration",
			envVars: map[string]string{
				"OTEL_EXPORTER_OTLP_ENDPOINT": "http://localhost:4318",
				"OTEL_EXPORTER_OTLP_INSECURE": "true",
				"OTEL_EXPORTER_OTLP_TOKEN":    "test-token",
			},
			cleanup: func() {
				os.Unsetenv("OTEL_EXPORTER_OTLP_ENDPOINT")
				os.Unsetenv("OTEL_EXPORTER_OTLP_INSECURE")
				os.Unsetenv("OTEL_EXPORTER_OTLP_TOKEN")
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Set environment variables
			for k, v := range tc.envVars {
				os.Setenv(k, v)
			}
			defer tc.cleanup()

			// Create a new span to test the configuration
			ctx := context.Background()
			ctx, span := oteltrace.Tracer.Start(ctx, "test_span")
			defer span.End()

			// Verify span is created successfully
			if span == nil {
				t.Error("Expected span to be created")
			}

			// Verify span context
			spanCtx := trace.SpanContextFromContext(ctx)
			if !spanCtx.IsValid() {
				t.Error("Expected valid span context")
			}
		})
	}
}

func TestShutdown(t *testing.T) {
	// Test that shutdown function can be called without error
	ctx := context.Background()
	err := oteltrace.Shutdown(ctx)
	if err != nil {
		t.Errorf("Expected no error from Shutdown, got: %v", err)
	}
}
