package oteltrace

import (
	"context"
	"log"
	"os"
	"strings"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracehttp"
	"go.opentelemetry.io/otel/exporters/stdout/stdouttrace"
	"go.opentelemetry.io/otel/propagation"
	"go.opentelemetry.io/otel/sdk/resource"
	sdktrace "go.opentelemetry.io/otel/sdk/trace"
	semconv "go.opentelemetry.io/otel/semconv/v1.26.0"
	"go.opentelemetry.io/otel/trace"
)

var (
	Tracer   trace.Tracer
	Shutdown func(context.Context) error
)

func newExporter() (sdktrace.SpanExporter, error) {
	if endpoint, ok := os.LookupEnv("OTEL_EXPORTER_OTLP_ENDPOINT"); ok && endpoint != "" {
		opts := []otlptracehttp.Option{
			otlptracehttp.WithEndpoint(endpoint),
		}
		if insecure, ok := os.LookupEnv("OTEL_EXPORTER_OTLP_INSECURE"); ok && (strings.ToLower(insecure) == "true" || insecure == "1") {
			opts = append(opts, otlptracehttp.WithInsecure())
		}
		if token, ok := os.LookupEnv("OTEL_EXPORTER_OTLP_TOKEN"); ok && token != "" {
			opts = append(opts, otlptracehttp.WithHeaders(map[string]string{"Authorization": "Bearer " + token}))
		}
		return otlptracehttp.New(context.TODO(), opts...)
	}

	return stdouttrace.New(stdouttrace.WithPrettyPrint())
}

func newTraceProvider(exporter sdktrace.SpanExporter) *sdktrace.TracerProvider {
	// Ensure default SDK resources and the required service name are set.
	resource, err := resource.Merge(
		resource.Default(),
		resource.NewWithAttributes(
			semconv.SchemaURL,
			semconv.ServiceName(os.Getenv("OTEL_SERVICE_NAME")),
		),
	)

	if err != nil {
		panic(err)
	}

	return sdktrace.NewTracerProvider(
		sdktrace.WithBatcher(exporter),
		sdktrace.WithResource(resource),
	)
}

func init() {
	exporter, err := newExporter()
	if err != nil {
		log.Fatalf("failed to initialize exporter: %v", err)
	}

	// Create a new tracer provider with a batch span processor and the given exporter.
	traceProvider := newTraceProvider(exporter)

	otel.SetTracerProvider(traceProvider)
	otel.SetTextMapPropagator(propagation.NewCompositeTextMapPropagator(propagation.TraceContext{}, propagation.Baggage{}))

	// Finally, set the tracer that can be used for this package.
	Tracer = traceProvider.Tracer("api-server/pkg/oteltrace")
	Shutdown = traceProvider.Shutdown
}
