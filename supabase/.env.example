
############
# Secrets
# YOU MUST CHANGE THESE BEFORE GOING INTO PRODUCTION
############

POSTGRES_PASSWORD=postgres
JWT_SECRET=your-super-secret-jwt-token-with-at-least-32-characters-long
ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyAgCiAgICAicm9sZSI6ICJhbm9uIiwKICAgICJpc3MiOiAic3VwYWJhc2UtZGVtbyIsCiAgICAiaWF0IjogMTY0MTc2OTIwMCwKICAgICJleHAiOiAxNzk5NTM1NjAwCn0.dc_X5iR_VP_qT0zsiyj_I_OZ2T9FtRU2BBNWN8Bu4GE
SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyAgCiAgICAicm9sZSI6ICJzZXJ2aWNlX3JvbGUiLAogICAgImlzcyI6ICJzdXBhYmFzZS1kZW1vIiwKICAgICJpYXQiOiAxNjQxNzY5MjAwLAogICAgImV4cCI6IDE3OTk1MzU2MDAKfQ.DaYlNEoUrrEn2Ig7tqibS-PHK5vgusbcbo7X36XVt4Q
# DASHBOARD_USERNAME=supabase
# DASHBOARD_PASSWORD=this_password_is_insecure_and_should_be_updated

############
# Database - You can change these to any PostgreSQL database that has logical replication enabled.
############

POSTGRES_HOST=db
POSTGRES_DB=postgres
POSTGRES_PORT=5432
# default user is postgres

############
# Auth - Configuration for the GoTrue authentication server.
############

## General
SITE_URL=http://localhost:3000
ADDITIONAL_REDIRECT_URLS=
JWT_EXPIRY=3600 # seconds
DISABLE_SIGNUP=false
API_EXTERNAL_URL=http://localhost:9999

## Mailer Config
MAILER_OTP_EXP=600 # 600 seconds
MAILER_URLPATHS_CONFIRMATION="/verify"
MAILER_URLPATHS_INVITE="/verify"
MAILER_URLPATHS_RECOVERY="/verify"
MAILER_URLPATHS_EMAIL_CHANGE="/verify"

## Email auth
ENABLE_EMAIL_SIGNUP=true
ENABLE_EMAIL_AUTOCONFIRM=false
SMTP_ADMIN_EMAIL=admin@emailhost
SMTP_HOST=smtp.resend.com
SMTP_PORT=465
SMTP_USER=resend
SMTP_PASS=password
SMTP_SENDER_NAME=onboarding
ENABLE_ANONYMOUS_USERS=false

## Phone auth
ENABLE_PHONE_SIGNUP=false
ENABLE_PHONE_AUTOCONFIRM=false

## Azure OAuth
EXTERNAL_AZURE_ENABLED=true
EXTERNAL_AZURE_CLIENT_ID=microsoft entra client ID
EXTERNAL_AZURE_SECRET=microsoft entra secret
EXTERNAL_AZURE_REDIRECT_URI=http://localhost:9999/callback
EXTERNAL_AZURE_URL=https://login.microsoftonline.com/75a7a25d-550b-4430-8df5-30913a20cf2c
OOGLE_PROJECT_NUMBER=GOOGLE_PROJECT_NUMBER
