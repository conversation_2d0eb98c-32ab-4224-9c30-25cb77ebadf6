#!/bin/bash


export OTEL_SERVICE_NAME="volvo-api-server"
export VOLVO_POSTGRESQL_URL=postgres://postgres:postgres@localhost:5432/volvo?sslmode=disable
export VOLVO_AUTH_JWT_SECRET=d2yzalHl2ww07GPxVr77bMb1FaHk9RJf
export VOLVO_AUTH_PWD_SECRET="fQ7LNzdwGXDkuUnZgrWjwXtTc7zf9wGb"
export VOLVO_SUPABASE_HOST=https://api.volvo-dev.example.com
export VOLVO_SUPABASE_INVITE_REDIRECT_URL=https://front-end.volvo-dev.example.com/reset-password
export VOLVO_GITLAB_HOST=https://gitlab.volvo-dev.example.com
export VOLVO_GITLAB_VOLVO_API_SERVER_HOST=http://api-server.volvo.svc.cluster.local:8080
export VOLVO_SERVICE_ROLE_KEY=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************.xOp-oHXFB9_AqWi_bBH4sCXRt1q2tTOD45dQ7KEYJ44
export VOLVO_SMTP_HOST=smtp.resend.com
export VOLVO_SMTP_PORT=465
export VOLVO_SMTP_USER="resend"
export VOLVO_SMTP_PASS="re_j8ezBTJG_NpJqsqn7g1ggm6XMMgvyybwp"
export VOLVO_SMTP_ADMIN_EMAIL="<EMAIL>"
export VOLVO_CORS_ALLOWED_ORIGINS="http://localhost:5173"
export VOLVO_ARGO_WORKFLOW_HOST=argo.volvo-dev.example.com:443
export VOLVO_ARGO_WORKFLOW_ACCESS_TOKEN=*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
export VOLVO_ARGO_WORKFLOW_SPACE_IMAGE_REGISTRY=************.dkr.ecr.ap-southeast-1.amazonaws.com/staging/space
export VOLVO_ARGO_WORKFLOW_VOLVO_API_SERVER_URL=http://api-server.volvo.svc.cluster.local:8080
export VOLVO_IN_CLUSTER=false
export VOLVO_MIGRATE=false
export VOLVO_SPACE_DOMAIN=volvo-dev.example.com
export VOLVO_SPACE_BUILD_CONTEXT=https://argo:<EMAIL>/delivery/longclawx01/volvo/leaderboard-build-context.git
export VOLVO_SPACE_INGRESS_CLASS_NAME=external-nginx
export VOLVO_CUSTOM_GITLAB_SSH_HOST=ssh.gitlab.volvo-dev.example.com
export VOLVO_AWS_ACCESS_KEY=********************
export VOLVO_AWS_SECRET_KEY=WmtSWG6uBh+5qnEP9f2JmMsBb5fuiGWtQKkM30D3
export VOLVO_AWS_REGION=ap-southeast-1
export VOLVO_AWS_AVATAR_BUCKET=volvo-dev-images
export VOLVO_IMAGE_MAX_SIZE=5242880
export VOLVO_AWS_PRE_SIGN_EXPIRE=3600

go run ./cmd/api-server/*.go
