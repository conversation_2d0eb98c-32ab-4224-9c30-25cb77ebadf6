// Package docs Code generated by swaggo/swag. DO NOT EDIT
package docs

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "contact": {
            "name": "AI Benchmarking Maintain team",
            "email": "<EMAIL>"
        },
        "license": {
            "name": "Apache 2.0",
            "url": "http://www.apache.org/licenses/LICENSE-2.0.html"
        },
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/admin/invite": {
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Sign Up Request"
                ],
                "summary": "Invite user to sign up",
                "parameters": [
                    {
                        "description": "Invite user to sign up",
                        "name": "model",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/api-server_internal_dto.InviteUserInput"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    },
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "401": {
                        "description": "Unauthorized - Authentication is required to access this resource",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "403": {
                        "description": "Forbidden - User does not have permission to access this resource",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            }
        },
        "/admin/signups": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Sign Up Request"
                ],
                "summary": "List all sign up requests",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/api-server_internal_dto.GetAllSignUpRequestsOutput"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            }
        },
        "/admin/signups/{id}/approval": {
            "patch": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Sign Up Request"
                ],
                "summary": "Approve or reject user's sign up requests",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Sign up request ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Approve or Reject user",
                        "name": "model",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/ApprovalRequest"
                        }
                    }
                ],
                "responses": {
                    "204": {
                        "description": "No Content - Request has been successfully completed, but no response payload body"
                    },
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            }
        },
        "/custom_claims": {
            "post": {
                "description": "Generate custom claims for Auth hook",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "summary": "Generate custom claims for Auth hook",
                "parameters": [
                    {
                        "description": "Custom access token input",
                        "name": "model",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/CustomAccessTokenInput"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Custom access token output",
                        "schema": {
                            "$ref": "#/definitions/CustomAccessTokenOutput"
                        }
                    },
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            }
        },
        "/ecr/deployments": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "ECR"
                ],
                "summary": "List all ECR deployments",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "Page number",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "description": "Items per page",
                        "name": "per_page",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "default": "created_at",
                        "description": "Order by field",
                        "name": "order_by",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "asc",
                            "desc"
                        ],
                        "type": "string",
                        "description": "Order direction",
                        "name": "direction",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filter by user ID",
                        "name": "user_id",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filter by org ID",
                        "name": "org_id",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/api-server_internal_dto.ListECRDeploymentResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "ECR"
                ],
                "summary": "Create a new ECR deployment",
                "parameters": [
                    {
                        "description": "Deployment configuration",
                        "name": "input",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/api-server_internal_dto.CreateECRDeploymentInput"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Return deployment information",
                        "schema": {
                            "$ref": "#/definitions/api-server_internal_dto.CreateECRDeploymentResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request - Invalid input",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "401": {
                        "description": "Unauthorized - Invalid or expired token",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "403": {
                        "description": "Forbidden - User does not have required permissions",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            }
        },
        "/ecr/deployments/{id}": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "ECR"
                ],
                "summary": "Get ECR deployment by ID",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Deployment ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/api-server_internal_dto.GetECRDeploymentResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "ECR"
                ],
                "summary": "Update an existing ECR deployment",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Deployment ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Deployment update configuration",
                        "name": "input",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/api-server_internal_dto.UpdateECRDeploymentInput"
                        }
                    }
                ],
                "responses": {
                    "204": {
                        "description": "Return updated deployment information",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "400": {
                        "description": "Bad Request - Invalid input",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "401": {
                        "description": "Unauthorized - Invalid or expired token",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "403": {
                        "description": "Forbidden - User does not have required permissions",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found - Deployment not found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "ECR"
                ],
                "summary": "Delete an ECR deployment",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Deployment ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "204": {
                        "description": "No Content - Deployment deleted successfully"
                    },
                    "400": {
                        "description": "Bad Request - Invalid input",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "401": {
                        "description": "Unauthorized - Invalid or expired token",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "403": {
                        "description": "Forbidden - User does not have required permissions",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found - Deployment not found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            }
        },
        "/ecr/deployments/{id}/deploy": {
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "ECR"
                ],
                "summary": "Deploy an ECR image to Kubernetes",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Deployment ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "204": {
                        "description": "No Content",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "400": {
                        "description": "Bad Request - Invalid input",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "401": {
                        "description": "Unauthorized - Invalid or expired token",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "403": {
                        "description": "Forbidden - User does not have required permissions",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found - Deployment not found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            }
        },
        "/ecr/deployments/{id}/env": {
            "put": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "ECR"
                ],
                "summary": "Update an environment variable for an ECR deployment",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Deployment ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Environment variable input",
                        "name": "input",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/api-server_internal_dto.UpdateECRDeploymentEnvInput"
                        }
                    }
                ],
                "responses": {
                    "204": {
                        "description": "No Content - Environment variable updated successfully"
                    },
                    "400": {
                        "description": "Bad Request - Invalid input",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "401": {
                        "description": "Unauthorized - Invalid or expired token",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "403": {
                        "description": "Forbidden - User does not have required permissions",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "ECR"
                ],
                "summary": "Create a new environment variable for an ECR deployment",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Deployment ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Environment variable input",
                        "name": "input",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/api-server_internal_dto.CreateECRDeploymentEnvInput"
                        }
                    }
                ],
                "responses": {
                    "204": {
                        "description": "No Content - Environment variable created successfully"
                    },
                    "400": {
                        "description": "Bad Request - Invalid input",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "401": {
                        "description": "Unauthorized - Invalid or expired token",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "403": {
                        "description": "Forbidden - User does not have required permissions",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "ECR"
                ],
                "summary": "Delete an environment variable from an ECR deployment",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Deployment ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Environment variable input",
                        "name": "input",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/api-server_internal_dto.DeleteECRDeploymentEnvInput"
                        }
                    }
                ],
                "responses": {
                    "204": {
                        "description": "No Content - Environment variable deleted successfully"
                    },
                    "400": {
                        "description": "Bad Request - Invalid input",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "401": {
                        "description": "Unauthorized - Invalid or expired token",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "403": {
                        "description": "Forbidden - User does not have required permissions",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            }
        },
        "/ecr/deployments/{id}/stop": {
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "ECR"
                ],
                "summary": "Stop an ECR deployment",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Deployment ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "204": {
                        "description": "No Content - Deployment stopped successfully"
                    },
                    "400": {
                        "description": "Bad Request - Invalid input",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "401": {
                        "description": "Unauthorized - Invalid or expired token",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "403": {
                        "description": "Forbidden - User does not have required permissions",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found - Deployment not found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            }
        },
        "/explore": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Health"
                ],
                "summary": "Health check",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            }
        },
        "/hardwares": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Machine"
                ],
                "summary": "List GPU nodes",
                "parameters": [
                    {
                        "type": "boolean",
                        "description": "Return short version of the response, default is true",
                        "name": "short",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Return list of gpu nodes",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/api-server_internal_dto.ListGPUNodesResponse"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            }
        },
        "/organizations": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Organization"
                ],
                "summary": "List organizations",
                "responses": {
                    "200": {
                        "description": "Return list of organizations",
                        "schema": {
                            "$ref": "#/definitions/api-server_internal_dto.ListOrganizationsOutput"
                        }
                    },
                    "403": {
                        "description": "Forbidden - invalid token",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Host Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Organization"
                ],
                "summary": "Create organization",
                "parameters": [
                    {
                        "description": "Organization information",
                        "name": "model",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/CreateOrganizationInput"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Return Organization information",
                        "schema": {
                            "$ref": "#/definitions/CreateOrganizationOutput"
                        }
                    },
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Host Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            }
        },
        "/organizations/:org_id": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Organization"
                ],
                "summary": "Get organization info",
                "responses": {
                    "200": {
                        "description": "Return organization entity info",
                        "schema": {
                            "$ref": "#/definitions/FindOrganizationOutput"
                        }
                    },
                    "403": {
                        "description": "Forbidden - invalid token",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Host Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            }
        },
        "/organizations/:org_id/members": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Organization"
                ],
                "summary": "List organization members info",
                "responses": {
                    "200": {
                        "description": "Return organization members",
                        "schema": {
                            "$ref": "#/definitions/api-server_internal_dto.ListOrgMembersOutput"
                        }
                    },
                    "403": {
                        "description": "Forbidden - invalid token",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Host Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            }
        },
        "/organizations/access_tokens": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Access Token"
                ],
                "summary": "List org access token",
                "parameters": [
                    {
                        "description": "Access token",
                        "name": "model",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/ListOrgAccessTokenRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Return list token",
                        "schema": {
                            "$ref": "#/definitions/ListOrgAccessTokenResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Access Token"
                ],
                "summary": "Create new org access token",
                "parameters": [
                    {
                        "description": "Access token",
                        "name": "model",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/CreateOrgAccessTokenRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Return created token",
                        "schema": {
                            "$ref": "#/definitions/CreateOrgAccessTokenResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            }
        },
        "/organizations/access_tokens/verify": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Access Token"
                ],
                "summary": "Verify org access token validity",
                "parameters": [
                    {
                        "description": "Access token to verify",
                        "name": "model",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/VerifyOrgAccessTokenRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Return verification result",
                        "schema": {
                            "$ref": "#/definitions/VerifyOrgAccessTokenHTTPResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            }
        },
        "/organizations/access_tokens/{id}": {
            "delete": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Access Token"
                ],
                "summary": "Delete org access token",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Access token ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "204": {
                        "description": "No content",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            }
        },
        "/organizations/me": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Organization"
                ],
                "summary": "List organizations",
                "responses": {
                    "200": {
                        "description": "Return list of organizations",
                        "schema": {
                            "$ref": "#/definitions/api-server_internal_dto.ListOrganizationsOutput"
                        }
                    },
                    "403": {
                        "description": "Forbidden - invalid token",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Host Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            }
        },
        "/organizations/{id}": {
            "delete": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Organization"
                ],
                "summary": "Delete organization",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Organization ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Return message",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "401": {
                        "description": "Unauthorized - invalid token",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Host Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            }
        },
        "/organizations/{id}/invite": {
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Organization"
                ],
                "summary": "Invite users to organization",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Organization ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Invite Users to Organization information",
                        "name": "model",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/api-server_internal_dto.InviteOrgMembersInput"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Return message invite organization",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Host Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            }
        },
        "/organizations/{org_id}/avatar": {
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "multipart/form-data"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Organization"
                ],
                "summary": "Upload organization avatar",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Organization ID",
                        "name": "org_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "file",
                        "description": "Avatar file",
                        "name": "file",
                        "in": "formData",
                        "required": true
                    }
                ],
                "responses": {
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Organization"
                ],
                "summary": "Delete organization avatar",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Organization ID",
                        "name": "org_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            }
        },
        "/organizations/{org_id}/members/{member_id}": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Organization"
                ],
                "summary": "Get Member in Organizanion",
                "responses": {
                    "403": {
                        "description": "Forbidden - invalid token",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Host Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Organization"
                ],
                "summary": "Update Member in Organizanion",
                "parameters": [
                    {
                        "description": "Invite User to Organization information",
                        "name": "model",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/UpdateMemberOrganizaionInput"
                        }
                    }
                ],
                "responses": {
                    "403": {
                        "description": "Forbidden - invalid token",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Host Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Organization"
                ],
                "summary": "Remove Member in Organizanion",
                "responses": {
                    "403": {
                        "description": "Forbidden - invalid token",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Host Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            }
        },
        "/repositories": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Repository"
                ],
                "summary": "List and filter repositories",
                "parameters": [
                    {
                        "type": "string",
                        "description": "search by repos name , user 's name , org name;",
                        "name": "keyword",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "spaces",
                            "models",
                            "datasets",
                            "composes"
                        ],
                        "type": "string",
                        "description": "filter by repository type",
                        "name": "repository_type",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "filter by tags; multi-values split by comma",
                        "name": "tags",
                        "in": "query"
                    },
                    {
                        "minimum": 1,
                        "type": "integer",
                        "description": "pagination page number",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "number of results per page",
                        "name": "per_page",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "created_at",
                            "updated_at"
                        ],
                        "type": "string",
                        "description": "order the result by field name",
                        "name": "order_by",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "asc",
                            "desc"
                        ],
                        "type": "string",
                        "description": "Sort result by ascending or descending",
                        "name": "sort",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "List of matched repository results",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/Repository"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Repository"
                ],
                "summary": "Add new repository",
                "parameters": [
                    {
                        "description": "Add repository",
                        "name": "model",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/RepositoryCreateInput"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Return repository information",
                        "schema": {
                            "$ref": "#/definitions/RepositoryCreateResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            }
        },
        "/repositories/deployments": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Repository Deployment"
                ],
                "summary": "List all deployments",
                "parameters": [
                    {
                        "enum": [
                            "spaces",
                            "datasets",
                            "models"
                        ],
                        "type": "string",
                        "description": "Repository type",
                        "name": "repo_type",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Repository namespace",
                        "name": "namespace",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Repository name",
                        "name": "repo_name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Start deployment request",
                        "name": "model",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/api-server_internal_dto.StartDeploymentRequest"
                        }
                    }
                ],
                "responses": {
                    "204": {
                        "description": "No Content - Request has been successfully completed, but no response payload body"
                    },
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            }
        },
        "/repositories/deployments/status": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Repository Deployment"
                ],
                "summary": "Update deployment status",
                "parameters": [
                    {
                        "description": "Deployment Data",
                        "name": "model",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/api-server_internal_dto.UpdateDeploymentStatusRequest"
                        }
                    }
                ],
                "responses": {
                    "204": {
                        "description": "no content",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            }
        },
        "/repositories/tags": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Repository"
                ],
                "summary": "Get repository templates",
                "responses": {
                    "200": {
                        "description": "Ok",
                        "schema": {
                            "$ref": "#/definitions/api-server_internal_dto.ListRepoTagsOutput"
                        }
                    },
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Repository"
                ],
                "summary": "Create repository tags",
                "parameters": [
                    {
                        "description": "Create tag",
                        "name": "model",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/CreateRepoTagInput"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Ok",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            }
        },
        "/repositories/tags/{tag_id}": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Repository"
                ],
                "summary": "Get repository tag",
                "parameters": [
                    {
                        "type": "string",
                        "description": "tag id",
                        "name": "tag_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Repository"
                ],
                "summary": "Create repository tags",
                "parameters": [
                    {
                        "type": "string",
                        "description": "tag id",
                        "name": "tag_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            },
            "patch": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Repository"
                ],
                "summary": "Update repository tag",
                "parameters": [
                    {
                        "type": "string",
                        "description": "tag id",
                        "name": "tag_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            }
        },
        "/repositories/{repo_type}/{namespace}/{repo_name}": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Repository"
                ],
                "summary": "Get repo information",
                "parameters": [
                    {
                        "enum": [
                            "spaces",
                            "datasets",
                            "models",
                            "composes"
                        ],
                        "type": "string",
                        "description": "Repository type",
                        "name": "repo_type",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Repository namespace",
                        "name": "namespace",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Repository name",
                        "name": "repo_name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Ok",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/GetRepositoryOutput"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Repository"
                ],
                "summary": "Delete a repository",
                "parameters": [
                    {
                        "enum": [
                            "spaces",
                            "datasets",
                            "models",
                            "composes"
                        ],
                        "type": "string",
                        "description": "Repository type",
                        "name": "repo_type",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Repository namespace",
                        "name": "namespace",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Repository name",
                        "name": "repo_name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Return repository information",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            },
            "patch": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Repository"
                ],
                "summary": "Update a repository",
                "parameters": [
                    {
                        "enum": [
                            "spaces",
                            "datasets",
                            "models",
                            "composes"
                        ],
                        "type": "string",
                        "description": "Repository type",
                        "name": "repo_type",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Repository namespace",
                        "name": "namespace",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Repository name",
                        "name": "repo_name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            }
        },
        "/repositories/{repo_type}/{namespace}/{repo_name}/access_tokens": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Repository"
                ],
                "summary": "List repo access token",
                "parameters": [
                    {
                        "description": "Access token",
                        "name": "model",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/ListRepoAccessTokenRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Return list token",
                        "schema": {
                            "$ref": "#/definitions/ListRepoAccessTokenResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Repository"
                ],
                "summary": "Create new repo access token",
                "parameters": [
                    {
                        "description": "Access token",
                        "name": "model",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/CreateRepoAccessTokenRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Return created token",
                        "schema": {
                            "$ref": "#/definitions/CreateRepoAccessTokenResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            }
        },
        "/repositories/{repo_type}/{namespace}/{repo_name}/access_tokens/{id}": {
            "delete": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Repository"
                ],
                "summary": "Delete repo access token",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Access token ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "204": {
                        "description": "No content",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            }
        },
        "/repositories/{repo_type}/{namespace}/{repo_name}/archive": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Repository"
                ],
                "summary": "Archive repository",
                "parameters": [
                    {
                        "enum": [
                            "spaces",
                            "datasets",
                            "models",
                            "composes"
                        ],
                        "type": "string",
                        "description": "Repository type",
                        "name": "repo_type",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Repository namespace",
                        "name": "namespace",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Repository name",
                        "name": "repo_name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Archive type",
                        "name": "type",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Branch name",
                        "name": "ref",
                        "in": "query"
                    }
                ],
                "responses": {
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            }
        },
        "/repositories/{repo_type}/{namespace}/{repo_name}/avatar": {
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "multipart/form-data"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Repository"
                ],
                "summary": "Upload repository avatar",
                "parameters": [
                    {
                        "enum": [
                            "spaces",
                            "datasets",
                            "models"
                        ],
                        "type": "string",
                        "description": "Repository type",
                        "name": "repo_type",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Repository namespace",
                        "name": "namespace",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Repository name",
                        "name": "repo_name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "file",
                        "description": "Avatar file",
                        "name": "file",
                        "in": "formData",
                        "required": true
                    }
                ],
                "responses": {
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Repository"
                ],
                "summary": "Delete repository avatar",
                "parameters": [
                    {
                        "enum": [
                            "spaces",
                            "datasets",
                            "models"
                        ],
                        "type": "string",
                        "description": "Repository type",
                        "name": "repo_type",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Repository namespace",
                        "name": "namespace",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Repository name",
                        "name": "repo_name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            }
        },
        "/repositories/{repo_type}/{namespace}/{repo_name}/branch/{branch}": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Repository"
                ],
                "summary": "Get single repository branch",
                "parameters": [
                    {
                        "enum": [
                            "spaces",
                            "datasets",
                            "models",
                            "composes"
                        ],
                        "type": "string",
                        "description": "Repository type",
                        "name": "repo_type",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Repository namespace",
                        "name": "namespace",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Repository name",
                        "name": "repo_name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Ok",
                        "schema": {
                            "$ref": "#/definitions/RepositoryBranchInfo"
                        }
                    },
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            }
        },
        "/repositories/{repo_type}/{namespace}/{repo_name}/branches": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Repository"
                ],
                "summary": "Get repository branches",
                "parameters": [
                    {
                        "enum": [
                            "spaces",
                            "datasets",
                            "models",
                            "composes"
                        ],
                        "type": "string",
                        "description": "Repository type",
                        "name": "repo_type",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Repository namespace",
                        "name": "namespace",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Repository name",
                        "name": "repo_name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Ok",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/RepositoryBranchInfo"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            }
        },
        "/repositories/{repo_type}/{namespace}/{repo_name}/commits": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Repository"
                ],
                "summary": "Get repository commits",
                "parameters": [
                    {
                        "enum": [
                            "spaces",
                            "datasets",
                            "models",
                            "composes"
                        ],
                        "type": "string",
                        "description": "Repository type",
                        "name": "repo_type",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Repository namespace",
                        "name": "namespace",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Repository name",
                        "name": "repo_name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "File path",
                        "name": "path",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Branch name",
                        "name": "ref",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Ok",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/RepositoryCommit"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Repository"
                ],
                "summary": "Create repository commit",
                "parameters": [
                    {
                        "enum": [
                            "spaces",
                            "datasets",
                            "models",
                            "composes"
                        ],
                        "type": "string",
                        "description": "Repository type",
                        "name": "repo_type",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Repository namespace",
                        "name": "namespace",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Repository name",
                        "name": "repo_name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "File path",
                        "name": "path",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Branch name",
                        "name": "ref",
                        "in": "query"
                    },
                    {
                        "description": "Create commit to repository",
                        "name": "model",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/api-server_internal_dto.CreateRepositoryCommitInput"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Ok",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/RepositoryCommit"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            }
        },
        "/repositories/{repo_type}/{namespace}/{repo_name}/composes": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Repository Kompose"
                ],
                "summary": "Get compose services",
                "parameters": [
                    {
                        "enum": [
                            "spaces",
                            "datasets",
                            "models",
                            "composes"
                        ],
                        "type": "string",
                        "description": "Repository type",
                        "name": "repo_type",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Repository namespace",
                        "name": "namespace",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Repository name",
                        "name": "repo_name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Ok",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/api-server_internal_dto.GetComposeServicesResponse"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            }
        },
        "/repositories/{repo_type}/{namespace}/{repo_name}/contributors": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Repository"
                ],
                "summary": "Get repository contributors",
                "parameters": [
                    {
                        "enum": [
                            "spaces",
                            "datasets",
                            "models",
                            "composes"
                        ],
                        "type": "string",
                        "description": "Repository type",
                        "name": "repo_type",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Repository namespace",
                        "name": "namespace",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Repository name",
                        "name": "repo_name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Branch name",
                        "name": "ref",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Ok",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/RepositoryContributor"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            }
        },
        "/repositories/{repo_type}/{namespace}/{repo_name}/deployments/logs": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Repository Deployment"
                ],
                "summary": "Retrieve deployment build logs by repository ID",
                "parameters": [
                    {
                        "enum": [
                            "spaces",
                            "datasets",
                            "models"
                        ],
                        "type": "string",
                        "description": "Repository type",
                        "name": "repo_type",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Repository namespace",
                        "name": "namespace",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Repository name",
                        "name": "repo_name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Return deployment build logs",
                        "schema": {
                            "type": "array",
                            "items": {
                                "type": "string"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            }
        },
        "/repositories/{repo_type}/{namespace}/{repo_name}/deployments/pods/logs": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Repository Deployment"
                ],
                "summary": "Retrieve deployment pod logs by repository ID",
                "parameters": [
                    {
                        "enum": [
                            "spaces",
                            "datasets",
                            "models"
                        ],
                        "type": "string",
                        "description": "Repository type",
                        "name": "repo_type",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Repository namespace",
                        "name": "namespace",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Repository name",
                        "name": "repo_name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Return deployment pod logs",
                        "schema": {
                            "type": "array",
                            "items": {
                                "type": "string"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            }
        },
        "/repositories/{repo_type}/{namespace}/{repo_name}/deployments/restart": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Repository Deployment"
                ],
                "summary": "Restart space's Kubernetes deployment",
                "parameters": [
                    {
                        "enum": [
                            "spaces",
                            "datasets",
                            "models"
                        ],
                        "type": "string",
                        "description": "Repository type",
                        "name": "repo_type",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Repository namespace",
                        "name": "namespace",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Repository name",
                        "name": "repo_name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Restart space successfully",
                        "schema": {
                            "$ref": "#/definitions/HTTPResp"
                        }
                    },
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            }
        },
        "/repositories/{repo_type}/{namespace}/{repo_name}/deployments/start": {
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Repository Deployment"
                ],
                "summary": "Start deployment",
                "parameters": [
                    {
                        "enum": [
                            "spaces",
                            "datasets",
                            "models"
                        ],
                        "type": "string",
                        "description": "Repository type",
                        "name": "repo_type",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Repository namespace",
                        "name": "namespace",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Repository name",
                        "name": "repo_name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Start deployment request",
                        "name": "model",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/api-server_internal_dto.StartDeploymentRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Start deployment successfully",
                        "schema": {
                            "$ref": "#/definitions/api-server_internal_dto.StartDeploymentResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            }
        },
        "/repositories/{repo_type}/{namespace}/{repo_name}/deployments/status": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Repository Deployment"
                ],
                "summary": "Get deployment status",
                "parameters": [
                    {
                        "enum": [
                            "spaces",
                            "datasets",
                            "models"
                        ],
                        "type": "string",
                        "description": "Repository type",
                        "name": "repo_type",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Repository namespace",
                        "name": "namespace",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Repository name",
                        "name": "repo_name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Start deployment request",
                        "name": "model",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/api-server_internal_dto.StartDeploymentRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Start deployment successfully",
                        "schema": {
                            "$ref": "#/definitions/api-server_internal_dto.StartDeploymentResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            }
        },
        "/repositories/{repo_type}/{namespace}/{repo_name}/deployments/stop": {
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Repository Deployment"
                ],
                "summary": "Stop deployment",
                "parameters": [
                    {
                        "enum": [
                            "spaces",
                            "datasets",
                            "models"
                        ],
                        "type": "string",
                        "description": "Repository type",
                        "name": "repo_type",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Repository namespace",
                        "name": "namespace",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Repository name",
                        "name": "repo_name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Stop deployment request",
                        "name": "model",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/api-server_internal_dto.StopDeploymentRequest"
                        }
                    }
                ],
                "responses": {
                    "204": {
                        "description": "No Content - Request has been successfully completed, but no response payload body"
                    },
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            }
        },
        "/repositories/{repo_type}/{namespace}/{repo_name}/envs": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Repository"
                ],
                "summary": "Get environment variables in Repository",
                "parameters": [
                    {
                        "enum": [
                            "spaces",
                            "datasets",
                            "models",
                            "composes"
                        ],
                        "type": "string",
                        "description": "Repository type",
                        "name": "repo_type",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Repository namespace",
                        "name": "namespace",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Repository name",
                        "name": "repo_name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "403": {
                        "description": "Forbidden - invalid token",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Host Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Repository"
                ],
                "summary": "Update environment variables in Repository",
                "parameters": [
                    {
                        "enum": [
                            "spaces",
                            "datasets",
                            "models",
                            "composes"
                        ],
                        "type": "string",
                        "description": "Repository type",
                        "name": "repo_type",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Repository namespace",
                        "name": "namespace",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Repository name",
                        "name": "repo_name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Request body",
                        "name": "model",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/UpdateRepositoryEnvRequest"
                        }
                    }
                ],
                "responses": {
                    "403": {
                        "description": "Forbidden - invalid token",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Host Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Repository"
                ],
                "summary": "Create environment variables in Repository",
                "parameters": [
                    {
                        "enum": [
                            "spaces",
                            "datasets",
                            "models",
                            "composes"
                        ],
                        "type": "string",
                        "description": "Repository type",
                        "name": "repo_type",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Repository namespace",
                        "name": "namespace",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Repository name",
                        "name": "repo_name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Request body - can be either CreateRepositoryEnvRequest for single env or BulkCreateRepositoryEnvRequest for multiple envs",
                        "name": "model",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "object"
                        }
                    }
                ],
                "responses": {
                    "403": {
                        "description": "Forbidden - invalid token",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Host Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Repository"
                ],
                "summary": "Delete environment variables in Repository",
                "parameters": [
                    {
                        "enum": [
                            "spaces",
                            "datasets",
                            "models",
                            "composes"
                        ],
                        "type": "string",
                        "description": "Repository type",
                        "name": "repo_type",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Repository namespace",
                        "name": "namespace",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Repository name",
                        "name": "repo_name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Request body",
                        "name": "key",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "403": {
                        "description": "Forbidden - invalid token",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Host Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            }
        },
        "/repositories/{repo_type}/{namespace}/{repo_name}/files": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Repository File"
                ],
                "summary": "List repository files",
                "parameters": [
                    {
                        "enum": [
                            "spaces",
                            "datasets",
                            "models",
                            "composes"
                        ],
                        "type": "string",
                        "description": "Repository type",
                        "name": "repo_type",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Repository namespace",
                        "name": "namespace",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Repository name",
                        "name": "repo_name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Branch name",
                        "name": "ref",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "File path",
                        "name": "path",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "description": "Used to get a recursive tree. Default is false",
                        "name": "recursive",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "description": "Used to get all results without pagination. Default is false",
                        "name": "all",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Used to get next page of results. Get from X-Next-Page-Token header",
                        "name": "page_token",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "Number of result per page. Default is 20. Max is 100",
                        "name": "per_page",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Ok",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/RepositoryFile"
                            }
                        },
                        "headers": {
                            "X-Next-Page-Token": {
                                "type": "string",
                                "description": "Token to include when requesting the next page of results if there are more results"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            }
        },
        "/repositories/{repo_type}/{namespace}/{repo_name}/files/raw/{file_path}": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Repository File"
                ],
                "summary": "Get raw file contents",
                "parameters": [
                    {
                        "enum": [
                            "spaces",
                            "datasets",
                            "models",
                            "composes"
                        ],
                        "type": "string",
                        "description": "Repository type",
                        "name": "repo_type",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Repository namespace",
                        "name": "namespace",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Repository name",
                        "name": "repo_name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "File path",
                        "name": "file_path",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Branch name",
                        "name": "ref",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Raw file content",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            },
            "head": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Repository File"
                ],
                "summary": "Get header raw file contents",
                "parameters": [
                    {
                        "enum": [
                            "spaces",
                            "datasets",
                            "models",
                            "composes"
                        ],
                        "type": "string",
                        "description": "Repository type",
                        "name": "repo_type",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Repository namespace",
                        "name": "namespace",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Repository name",
                        "name": "repo_name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "File path",
                        "name": "file_path",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Branch name",
                        "name": "ref",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Raw file content",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            }
        },
        "/repositories/{repo_type}/{namespace}/{repo_name}/files/{file_path}": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Repository File"
                ],
                "summary": "Get file contents",
                "parameters": [
                    {
                        "enum": [
                            "spaces",
                            "datasets",
                            "models",
                            "composes"
                        ],
                        "type": "string",
                        "description": "Repository type",
                        "name": "repo_type",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Repository namespace",
                        "name": "namespace",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Repository name",
                        "name": "repo_name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "File path",
                        "name": "file_path",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Branch name",
                        "name": "ref",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Ok",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/RepositoryFileContent"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            }
        },
        "/repositories/{repo_type}/{namespace}/{repo_name}/invite": {
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Repository"
                ],
                "summary": "Invite users to repository",
                "parameters": [
                    {
                        "enum": [
                            "spaces",
                            "datasets",
                            "models",
                            "composes"
                        ],
                        "type": "string",
                        "description": "Repository type",
                        "name": "repo_type",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Repository namespace",
                        "name": "namespace",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Repository name",
                        "name": "repo_name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Invite User to Repository information",
                        "name": "model",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/api-server_internal_dto.InviteRepoMembersInput"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Return message invite organization",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Host Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            }
        },
        "/repositories/{repo_type}/{namespace}/{repo_name}/members": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Repository"
                ],
                "summary": "Get repository members",
                "parameters": [
                    {
                        "enum": [
                            "spaces",
                            "datasets",
                            "models",
                            "composes"
                        ],
                        "type": "string",
                        "description": "Repository type",
                        "name": "repo_type",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Repository namespace",
                        "name": "namespace",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Repository name",
                        "name": "repo_name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Ok",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/api-server_internal_dto.ListRepositoryMembersOutput"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            }
        },
        "/repositories/{repo_type}/{namespace}/{repo_name}/members/{member_id}": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Organization"
                ],
                "summary": "Get Member in Repository",
                "parameters": [
                    {
                        "enum": [
                            "spaces",
                            "datasets",
                            "models",
                            "composes"
                        ],
                        "type": "string",
                        "description": "Repository type",
                        "name": "repo_type",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Repository namespace",
                        "name": "namespace",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Repository name",
                        "name": "repo_name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Member id",
                        "name": "member_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "403": {
                        "description": "Forbidden - invalid token",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Host Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Organization"
                ],
                "summary": "Update Member in Repository",
                "parameters": [
                    {
                        "enum": [
                            "spaces",
                            "datasets",
                            "models",
                            "composes"
                        ],
                        "type": "string",
                        "description": "Repository type",
                        "name": "repo_type",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Repository namespace",
                        "name": "namespace",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Repository name",
                        "name": "repo_name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Member id",
                        "name": "member_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Invite User to Organization information",
                        "name": "model",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/UpdateMemberRepositoryInput"
                        }
                    }
                ],
                "responses": {
                    "403": {
                        "description": "Forbidden - invalid token",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Host Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Organization"
                ],
                "summary": "Remove Member in Repository",
                "parameters": [
                    {
                        "enum": [
                            "spaces",
                            "datasets",
                            "models",
                            "composes"
                        ],
                        "type": "string",
                        "description": "Repository type",
                        "name": "repo_type",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Repository namespace",
                        "name": "namespace",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Repository name",
                        "name": "repo_name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Member id",
                        "name": "member_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "403": {
                        "description": "Forbidden - invalid token",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Host Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            }
        },
        "/send-confirm-email": {
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Sign Up Request"
                ],
                "summary": "Send confirm email",
                "parameters": [
                    {
                        "description": "Resend confirm email",
                        "name": "model",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/api-server_internal_dto.SendConfirmEmailInput"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    },
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "401": {
                        "description": "Unauthorized - Authentication is required to access this resource",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "403": {
                        "description": "Forbidden - User does not have permission to access this resource",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            }
        },
        "/signup": {
            "post": {
                "description": "Signup a new user and send email verification",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "summary": "Signup a new user",
                "parameters": [
                    {
                        "description": "Signup input",
                        "name": "signupInput",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/api-server_internal_dto.SignupInput"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "User created successfully and email verification sent",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            }
        },
        "/users": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "User"
                ],
                "summary": "List and filter users",
                "parameters": [
                    {
                        "minimum": 1,
                        "type": "integer",
                        "description": "pagination page number",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "number of results per page",
                        "name": "per_page",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "created_at",
                            "updated_at"
                        ],
                        "type": "string",
                        "description": "order the result by field name",
                        "name": "order_by",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "asc",
                            "desc"
                        ],
                        "type": "string",
                        "description": "Sort result by ascending or descending",
                        "name": "sort",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "List of matched user results",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/User"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            }
        },
        "/users/access_tokens": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Access Token"
                ],
                "summary": "Create new access token",
                "parameters": [
                    {
                        "description": "Access token",
                        "name": "model",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/ListAccessTokenRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Return list token",
                        "schema": {
                            "$ref": "#/definitions/ListAccessTokenResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Access Token"
                ],
                "summary": "Create new access token",
                "parameters": [
                    {
                        "description": "Access token",
                        "name": "model",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/CreateAccessTokenRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Return created token",
                        "schema": {
                            "$ref": "#/definitions/CreateAccessTokenResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            }
        },
        "/users/access_tokens/verify": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Access Token"
                ],
                "summary": "Verify access token validity",
                "parameters": [
                    {
                        "description": "Access token to verify",
                        "name": "model",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/VerifyAccessTokenRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Return verification result",
                        "schema": {
                            "$ref": "#/definitions/VerifyAccessTokenHTTPResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            }
        },
        "/users/access_tokens/{id}": {
            "delete": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Access Token"
                ],
                "summary": "Create new access token",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Access token ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "204": {
                        "description": "No content",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            }
        },
        "/users/keys": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "SSH Key"
                ],
                "summary": "List user's ssh keys",
                "parameters": [
                    {
                        "minimum": 1,
                        "type": "integer",
                        "description": "pagination page number",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "number of results per page",
                        "name": "per_page",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "expires_at",
                            "created_at",
                            "updated_at"
                        ],
                        "type": "string",
                        "description": "order the result by field name",
                        "name": "order_by",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "asc",
                            "desc"
                        ],
                        "type": "string",
                        "description": "Sort result by ascending or descending",
                        "name": "sort",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "List of user's SSH keys",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/GetSSHKeyOutput"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "SSH Key"
                ],
                "summary": "Add new ssh keys",
                "parameters": [
                    {
                        "description": "SSH Key",
                        "name": "model",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/CreateSSHKeyInput"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Return created ssh key",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            }
        },
        "/users/keys/{key_id}": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "SSH Key"
                ],
                "summary": "Get user ssh key",
                "parameters": [
                    {
                        "type": "string",
                        "description": "SSH key ID",
                        "name": "key_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Return ssh key info",
                        "schema": {
                            "$ref": "#/definitions/GetSingleSSHKeyOutput"
                        }
                    },
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "SSH Key"
                ],
                "summary": "Delete user ssh key",
                "parameters": [
                    {
                        "type": "string",
                        "description": "SSH key ID",
                        "name": "key_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "204": {
                        "description": "No Content - Request has been successfully completed, but no response payload body"
                    },
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            }
        },
        "/users/me": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "User"
                ],
                "summary": "Get current user information",
                "responses": {
                    "200": {
                        "description": "Current user information",
                        "schema": {
                            "$ref": "#/definitions/User"
                        }
                    },
                    "403": {
                        "description": "Forbidden - user not authorized",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            }
        },
        "/users/me/password": {
            "put": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "User"
                ],
                "summary": "Change user password",
                "parameters": [
                    {
                        "description": "Change password request",
                        "name": "model",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/api-server_internal_dto.ChangePasswordInput"
                        }
                    }
                ],
                "responses": {
                    "204": {
                        "description": "No Content - Password has been successfully changed"
                    },
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "403": {
                        "description": "Forbidden - user not authorized",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            }
        },
        "/users/{user_id}": {
            "delete": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "User"
                ],
                "summary": "Delete user information",
                "responses": {
                    "403": {
                        "description": "Forbidden - user not authorized",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            }
        },
        "/users/{user_id}/avatar": {
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "multipart/form-data"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "User"
                ],
                "summary": "Upload avatar",
                "parameters": [
                    {
                        "type": "file",
                        "description": "Avatar file",
                        "name": "file",
                        "in": "formData",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "User ID",
                        "name": "user_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "User"
                ],
                "summary": "Delete user avatar",
                "parameters": [
                    {
                        "type": "string",
                        "description": "User ID",
                        "name": "user_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            }
        },
        "/users/{user_id}/role": {
            "patch": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "User"
                ],
                "summary": "Update user role",
                "parameters": [
                    {
                        "type": "string",
                        "description": "User ID",
                        "name": "user_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Update user role",
                        "name": "model",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/UserUpdateRoleRequest"
                        }
                    }
                ],
                "responses": {
                    "204": {
                        "description": "No Content - Request has been successfully completed, but no response payload body"
                    },
                    "400": {
                        "description": "Bad Request - invalid request",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error - Encountered an unexpected condition",
                        "schema": {
                            "$ref": "#/definitions/HTTPError"
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "AccessToken": {
            "type": "object",
            "properties": {
                "access_token": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "expires_at": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "revoked": {
                    "type": "boolean"
                },
                "scopes": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                }
            }
        },
        "AccessTokenClaims": {
            "type": "object",
            "properties": {
                "aal": {
                    "type": "string"
                },
                "app_metadata": {
                    "type": "object",
                    "additionalProperties": true
                },
                "aud": {
                    "description": "the ` + "`" + `aud` + "`" + ` (Audience) claim. See https://datatracker.ietf.org/doc/html/rfc7519#section-4.1.3",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "email": {
                    "type": "string"
                },
                "exp": {
                    "description": "the ` + "`" + `exp` + "`" + ` (Expiration Time) claim. See https://datatracker.ietf.org/doc/html/rfc7519#section-4.1.4",
                    "allOf": [
                        {
                            "$ref": "#/definitions/github_com_golang-jwt_jwt_v5.NumericDate"
                        }
                    ]
                },
                "iat": {
                    "description": "the ` + "`" + `iat` + "`" + ` (Issued At) claim. See https://datatracker.ietf.org/doc/html/rfc7519#section-4.1.6",
                    "allOf": [
                        {
                            "$ref": "#/definitions/github_com_golang-jwt_jwt_v5.NumericDate"
                        }
                    ]
                },
                "is_anonymous": {
                    "type": "boolean"
                },
                "iss": {
                    "description": "the ` + "`" + `iss` + "`" + ` (Issuer) claim. See https://datatracker.ietf.org/doc/html/rfc7519#section-4.1.1",
                    "type": "string"
                },
                "jti": {
                    "description": "the ` + "`" + `jti` + "`" + ` (JWT ID) claim. See https://datatracker.ietf.org/doc/html/rfc7519#section-4.1.7",
                    "type": "string"
                },
                "nbf": {
                    "description": "the ` + "`" + `nbf` + "`" + ` (Not Before) claim. See https://datatracker.ietf.org/doc/html/rfc7519#section-4.1.5",
                    "allOf": [
                        {
                            "$ref": "#/definitions/github_com_golang-jwt_jwt_v5.NumericDate"
                        }
                    ]
                },
                "phone": {
                    "type": "string"
                },
                "role": {
                    "type": "string"
                },
                "session_id": {
                    "type": "string"
                },
                "sub": {
                    "description": "the ` + "`" + `sub` + "`" + ` (Subject) claim. See https://datatracker.ietf.org/doc/html/rfc7519#section-4.1.2",
                    "type": "string"
                },
                "user_metadata": {
                    "type": "object",
                    "additionalProperties": true
                }
            }
        },
        "ApprovalRequest": {
            "type": "object",
            "required": [
                "is_accepted"
            ],
            "properties": {
                "is_accepted": {
                    "type": "boolean"
                }
            }
        },
        "AuthHookError": {
            "type": "object",
            "properties": {
                "http_code": {
                    "type": "integer"
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "CreateAccessTokenRequest": {
            "type": "object",
            "required": [
                "name",
                "scopes"
            ],
            "properties": {
                "expires_at": {
                    "type": "string"
                },
                "name": {
                    "type": "string",
                    "maxLength": 100
                },
                "scopes": {
                    "type": "array",
                    "minItems": 1,
                    "items": {
                        "$ref": "#/definitions/api-server_internal_enums.RepoAccessTokenScope"
                    }
                }
            }
        },
        "CreateAccessTokenResponse": {
            "type": "object",
            "properties": {
                "data": {
                    "$ref": "#/definitions/AccessToken"
                },
                "message": {
                    "type": "string"
                },
                "next": {
                    "type": "boolean"
                },
                "page_no": {
                    "type": "integer"
                },
                "page_size": {
                    "type": "integer"
                },
                "previous": {
                    "type": "boolean"
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "CreateOrgAccessTokenRequest": {
            "type": "object",
            "required": [
                "name",
                "scopes"
            ],
            "properties": {
                "expires_at": {
                    "type": "string"
                },
                "name": {
                    "type": "string",
                    "maxLength": 100
                },
                "scopes": {
                    "type": "array",
                    "minItems": 1,
                    "items": {
                        "$ref": "#/definitions/api-server_internal_enums.RepoAccessTokenScope"
                    }
                }
            }
        },
        "CreateOrgAccessTokenResponse": {
            "type": "object",
            "properties": {
                "data": {
                    "$ref": "#/definitions/OrgAccessToken"
                },
                "message": {
                    "type": "string"
                },
                "next": {
                    "type": "boolean"
                },
                "page_no": {
                    "type": "integer"
                },
                "page_size": {
                    "type": "integer"
                },
                "previous": {
                    "type": "boolean"
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "CreateOrganizationInput": {
            "type": "object",
            "required": [
                "name",
                "organization_type",
                "path_name"
            ],
            "properties": {
                "file": {
                    "$ref": "#/definitions/multipart.FileHeader"
                },
                "interest": {
                    "type": "string",
                    "maxLength": 1000
                },
                "name": {
                    "type": "string"
                },
                "organization_type": {
                    "enum": [
                        "company",
                        "university",
                        "project",
                        "open_source"
                    ],
                    "allOf": [
                        {
                            "$ref": "#/definitions/OrganizationType"
                        }
                    ]
                },
                "path_name": {
                    "type": "string"
                }
            }
        },
        "CreateOrganizationOutput": {
            "type": "object",
            "properties": {
                "uuid": {
                    "type": "string"
                }
            }
        },
        "CreateRepoAccessTokenRequest": {
            "type": "object",
            "required": [
                "expires_at",
                "name",
                "scopes",
                "user_id"
            ],
            "properties": {
                "expires_at": {
                    "type": "string"
                },
                "name": {
                    "type": "string",
                    "maxLength": 50,
                    "minLength": 1
                },
                "scopes": {
                    "type": "array",
                    "minItems": 1,
                    "items": {
                        "$ref": "#/definitions/api-server_internal_enums.RepoAccessTokenScope"
                    }
                },
                "user_id": {
                    "type": "string"
                }
            }
        },
        "CreateRepoAccessTokenResponse": {
            "type": "object",
            "properties": {
                "data": {
                    "$ref": "#/definitions/RepoAccessToken"
                },
                "message": {
                    "type": "string"
                },
                "next": {
                    "type": "boolean"
                },
                "page_no": {
                    "type": "integer"
                },
                "page_size": {
                    "type": "integer"
                },
                "previous": {
                    "type": "boolean"
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "CreateRepoTagInput": {
            "type": "object",
            "required": [
                "name",
                "repo_types",
                "type"
            ],
            "properties": {
                "icon_url": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "repo_types": {
                    "type": "array",
                    "minItems": 1,
                    "items": {
                        "type": "string"
                    }
                },
                "sub_type": {
                    "type": "string"
                },
                "type": {
                    "type": "string"
                }
            }
        },
        "CreateSSHKeyInput": {
            "type": "object",
            "required": [
                "key",
                "title",
                "usage_type"
            ],
            "properties": {
                "expires_at": {
                    "description": "Expiration date of the SSH key in ISO 8601 format (YYYY-MM-DDTHH:MM:SSZ)",
                    "type": "string"
                },
                "key": {
                    "type": "string",
                    "maxLength": 1000
                },
                "title": {
                    "type": "string",
                    "maxLength": 100
                },
                "usage_type": {
                    "description": "Usage type of the SSH key. Possible values are: \"auth\", \"signing\", \"auth_and_signing\". Default: auth_and_signing",
                    "allOf": [
                        {
                            "$ref": "#/definitions/UsageType"
                        }
                    ]
                }
            }
        },
        "CustomAccessTokenInput": {
            "type": "object",
            "properties": {
                "authentication_method": {
                    "type": "string"
                },
                "claims": {
                    "$ref": "#/definitions/AccessTokenClaims"
                },
                "user_id": {
                    "type": "string"
                }
            }
        },
        "CustomAccessTokenOutput": {
            "type": "object",
            "properties": {
                "claims": {
                    "type": "object",
                    "additionalProperties": true
                },
                "error": {
                    "$ref": "#/definitions/AuthHookError"
                }
            }
        },
        "DeploymentGPUInfo": {
            "type": "object",
            "properties": {
                "cpu_request": {
                    "description": "sum of memory requests",
                    "type": "integer"
                },
                "deployment_type": {
                    "$ref": "#/definitions/api-server_internal_enums.DeploymentType"
                },
                "gpu_request": {
                    "type": "integer"
                },
                "id": {
                    "type": "string"
                },
                "memory_bytes_request": {
                    "description": "sum of memory requests",
                    "type": "integer"
                },
                "name": {
                    "description": "format: namespace/name",
                    "type": "string"
                },
                "repo_id": {
                    "$ref": "#/definitions/api-server_internal_types.RepoID"
                },
                "status": {
                    "$ref": "#/definitions/api-server_internal_dto.DeploymentStatus"
                },
                "updated_at": {
                    "type": "string"
                },
                "user": {
                    "$ref": "#/definitions/User"
                }
            }
        },
        "FindOrganizationOutput": {
            "type": "object",
            "properties": {
                "avatar": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "interest": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "path_name": {
                    "type": "string"
                },
                "type": {
                    "$ref": "#/definitions/OrganizationType"
                },
                "updated_at": {
                    "type": "string"
                }
            }
        },
        "GPUNode": {
            "type": "object",
            "properties": {
                "deployments": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/DeploymentGPUInfo"
                    }
                },
                "gpu_count": {
                    "type": "integer"
                },
                "gpu_memory_gb": {
                    "type": "integer"
                },
                "gpu_model": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "node_name": {
                    "type": "string"
                }
            }
        },
        "GetRepositoryOutput": {
            "type": "object",
            "properties": {
                "avatar": {
                    "type": "string"
                },
                "default_branch": {
                    "type": "string"
                },
                "deployment": {
                    "$ref": "#/definitions/api-server_internal_dto.Deployment"
                },
                "hardware": {
                    "$ref": "#/definitions/api-server_internal_dto.Hardware"
                },
                "http_url": {
                    "type": "string"
                },
                "id": {
                    "description": "Id            uuid.UUID            ` + "`" + `json:\"id\"` + "`" + `",
                    "allOf": [
                        {
                            "$ref": "#/definitions/api-server_internal_types.RepoID"
                        }
                    ]
                },
                "name": {
                    "type": "string"
                },
                "owner": {
                    "$ref": "#/definitions/api-server_internal_dto.RepositoryOwner"
                },
                "ssh_url": {
                    "type": "string"
                },
                "tags": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/Tags"
                    }
                },
                "type": {
                    "$ref": "#/definitions/RepoType"
                },
                "user_id": {
                    "type": "string"
                },
                "visibility": {
                    "$ref": "#/definitions/RepoVisibility"
                }
            }
        },
        "GetSSHKeyOutput": {
            "type": "object",
            "properties": {
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/SSHKey"
                    }
                },
                "message": {
                    "type": "string"
                },
                "next": {
                    "type": "boolean"
                },
                "page_no": {
                    "type": "integer"
                },
                "page_size": {
                    "type": "integer"
                },
                "previous": {
                    "type": "boolean"
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "GetSingleSSHKeyOutput": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "expires_at": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "key": {
                    "type": "string"
                },
                "title": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                },
                "usage_type": {
                    "$ref": "#/definitions/UsageType"
                },
                "user_id": {
                    "type": "string"
                }
            }
        },
        "HTTPError": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "HTTPResp": {
            "type": "object",
            "properties": {
                "data": {},
                "message": {
                    "type": "string"
                },
                "next": {
                    "type": "boolean"
                },
                "page_no": {
                    "type": "integer"
                },
                "page_size": {
                    "type": "integer"
                },
                "previous": {
                    "type": "boolean"
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "ListAccessTokenRequest": {
            "type": "object",
            "properties": {
                "paginate": {
                    "$ref": "#/definitions/PaginateRequest"
                }
            }
        },
        "ListAccessTokenResponse": {
            "type": "object",
            "properties": {
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/AccessToken"
                    }
                },
                "message": {
                    "type": "string"
                },
                "next": {
                    "type": "boolean"
                },
                "page_no": {
                    "type": "integer"
                },
                "page_size": {
                    "type": "integer"
                },
                "previous": {
                    "type": "boolean"
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "ListOrgAccessTokenRequest": {
            "type": "object",
            "properties": {
                "paginate": {
                    "$ref": "#/definitions/PaginateRequest"
                }
            }
        },
        "ListOrgAccessTokenResponse": {
            "type": "object",
            "properties": {
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/OrgAccessToken"
                    }
                },
                "message": {
                    "type": "string"
                },
                "next": {
                    "type": "boolean"
                },
                "page_no": {
                    "type": "integer"
                },
                "page_size": {
                    "type": "integer"
                },
                "previous": {
                    "type": "boolean"
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "ListRepoAccessTokenRequest": {
            "type": "object",
            "properties": {
                "paginate": {
                    "$ref": "#/definitions/PaginateRequest"
                },
                "user_id": {
                    "type": "string"
                }
            }
        },
        "ListRepoAccessTokenResponse": {
            "type": "object",
            "properties": {
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/RepoAccessToken"
                    }
                },
                "message": {
                    "type": "string"
                },
                "next": {
                    "type": "boolean"
                },
                "page_no": {
                    "type": "integer"
                },
                "page_size": {
                    "type": "integer"
                },
                "previous": {
                    "type": "boolean"
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "MergeRequest": {
            "type": "object",
            "properties": {
                "commit_id": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "updated_at": {
                    "type": "string"
                }
            }
        },
        "OrderByColumn": {
            "type": "string",
            "enum": [
                "expires_at",
                "created_at",
                "updated_at"
            ],
            "x-enum-varnames": [
                "OrderByColumn_ExpiresAt",
                "OrderByColumn_CreatedAt",
                "OrderByColumn_UpdatedAt"
            ]
        },
        "OrderByDirection": {
            "type": "string",
            "enum": [
                "asc",
                "desc"
            ],
            "x-enum-varnames": [
                "OrderByDirection_Asc",
                "OrderByDirection_Desc"
            ]
        },
        "OrgAccessToken": {
            "type": "object",
            "properties": {
                "access_token": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "expires_at": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "revoked": {
                    "type": "boolean"
                },
                "scopes": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                }
            }
        },
        "OrgRole": {
            "type": "string",
            "enum": [
                "owner",
                "developer"
            ],
            "x-enum-varnames": [
                "OrgRole_Owner",
                "OrgRole_Developer"
            ]
        },
        "Organization": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "organization_type": {
                    "$ref": "#/definitions/OrganizationType"
                },
                "path_name": {
                    "type": "string"
                }
            }
        },
        "OrganizationType": {
            "type": "string",
            "enum": [
                "company",
                "university",
                "project",
                "open_source"
            ],
            "x-enum-varnames": [
                "OrganizationType_Company",
                "OrganizationType_University",
                "OrganizationType_Project",
                "OrganizationType_OpenSource"
            ]
        },
        "PaginateRequest": {
            "type": "object",
            "properties": {
                "order_by": {
                    "enum": [
                        "expires_at",
                        "created_at",
                        "updated_at"
                    ],
                    "allOf": [
                        {
                            "$ref": "#/definitions/OrderByColumn"
                        }
                    ]
                },
                "page": {
                    "type": "integer",
                    "minimum": 1
                },
                "per_page": {
                    "type": "integer",
                    "maximum": 100,
                    "minimum": 1
                },
                "sort": {
                    "enum": [
                        "asc",
                        "desc"
                    ],
                    "allOf": [
                        {
                            "$ref": "#/definitions/OrderByDirection"
                        }
                    ]
                }
            }
        },
        "RepoAccessToken": {
            "type": "object",
            "properties": {
                "access_token": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "expires_at": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "revoked": {
                    "type": "boolean"
                },
                "scopes": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                }
            }
        },
        "RepoRole": {
            "type": "string",
            "enum": [
                "owner",
                "developer"
            ],
            "x-enum-varnames": [
                "RepoRole_Owner",
                "RepoRole_Developer"
            ]
        },
        "RepoType": {
            "type": "string",
            "enum": [
                "spaces",
                "models",
                "datasets",
                "composes"
            ],
            "x-enum-varnames": [
                "RepoType_Spaces",
                "RepoType_Models",
                "RepoType_Datasets",
                "RepoType_Composes"
            ]
        },
        "RepoVisibility": {
            "type": "string",
            "enum": [
                "private",
                "internal"
            ],
            "x-enum-varnames": [
                "RepoVisibility_Private",
                "RepoVisibility_Internal"
            ]
        },
        "Repository": {
            "type": "object",
            "properties": {
                "avatar": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "deployment_status": {
                    "$ref": "#/definitions/api-server_internal_dto.DeploymentStatus"
                },
                "hardware": {
                    "$ref": "#/definitions/api-server_internal_dto.Hardware"
                },
                "id": {
                    "description": "ID          string               ` + "`" + `json:\"id\"` + "`" + `",
                    "allOf": [
                        {
                            "$ref": "#/definitions/api-server_internal_types.RepoID"
                        }
                    ]
                },
                "name": {
                    "type": "string",
                    "example": "leaderboard"
                },
                "org": {
                    "$ref": "#/definitions/Organization"
                },
                "tags": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/Tags"
                    }
                },
                "type": {
                    "$ref": "#/definitions/RepoType"
                },
                "updated_at": {
                    "type": "string"
                },
                "user": {
                    "$ref": "#/definitions/User"
                },
                "visibility": {
                    "$ref": "#/definitions/RepoVisibility"
                }
            }
        },
        "RepositoryBranchInfo": {
            "type": "object",
            "properties": {
                "commit": {
                    "$ref": "#/definitions/RepositoryCommit"
                },
                "default": {
                    "type": "boolean"
                },
                "name": {
                    "type": "string"
                },
                "protected": {
                    "type": "boolean"
                }
            }
        },
        "RepositoryCommit": {
            "type": "object",
            "properties": {
                "author_email": {
                    "type": "string"
                },
                "author_name": {
                    "type": "string"
                },
                "authored_date": {
                    "type": "string"
                },
                "committer_email": {
                    "type": "string"
                },
                "committer_name": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "merge_request": {
                    "$ref": "#/definitions/MergeRequest"
                },
                "message": {
                    "type": "string"
                },
                "parent_ids": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "short_id": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                },
                "title": {
                    "type": "string"
                },
                "user": {
                    "$ref": "#/definitions/User"
                }
            }
        },
        "RepositoryContributor": {
            "type": "object",
            "properties": {
                "avatar": {
                    "type": "string"
                },
                "commits": {
                    "type": "integer"
                },
                "email": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                }
            }
        },
        "RepositoryCreateInput": {
            "type": "object",
            "required": [
                "name",
                "owner",
                "type",
                "visibility"
            ],
            "properties": {
                "hardware": {
                    "$ref": "#/definitions/api-server_internal_dto.RepositoryHardwareInput"
                },
                "name": {
                    "type": "string"
                },
                "owner": {
                    "$ref": "#/definitions/api-server_internal_dto.RepositoryOwner"
                },
                "sdk_id": {
                    "type": "string"
                },
                "type": {
                    "enum": [
                        "spaces",
                        "models",
                        "datasets",
                        "composes"
                    ],
                    "allOf": [
                        {
                            "$ref": "#/definitions/RepoType"
                        }
                    ]
                },
                "visibility": {
                    "enum": [
                        "private",
                        "internal"
                    ],
                    "allOf": [
                        {
                            "$ref": "#/definitions/RepoVisibility"
                        }
                    ]
                }
            }
        },
        "RepositoryCreateResponse": {
            "type": "object",
            "properties": {
                "id": {
                    "$ref": "#/definitions/api-server_internal_types.RepoID"
                },
                "uuid": {
                    "type": "string"
                }
            }
        },
        "RepositoryFile": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "string"
                },
                "last_commit": {
                    "description": "additional fields",
                    "allOf": [
                        {
                            "$ref": "#/definitions/RepositoryCommit"
                        }
                    ]
                },
                "mode": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "path": {
                    "type": "string"
                },
                "type": {
                    "type": "string"
                }
            }
        },
        "RepositoryFileContent": {
            "type": "object",
            "properties": {
                "commit_id": {
                    "type": "string"
                },
                "content": {
                    "type": "string"
                },
                "content_sha256": {
                    "type": "string"
                },
                "encoding": {
                    "type": "string"
                },
                "last_commit_id": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "path": {
                    "type": "string"
                },
                "ref": {
                    "type": "string"
                },
                "size": {
                    "type": "string"
                }
            }
        },
        "RepositoryOwnerType": {
            "type": "string",
            "enum": [
                "User",
                "Organization"
            ],
            "x-enum-varnames": [
                "RepoOwnerType_User",
                "RepoOwnerType_Org"
            ]
        },
        "SSHKey": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "expires_at": {
                    "description": "Expiration date of the SSH key in ISO 8601 format (YYYY-MM-DDTHH:MM:SSZ)",
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "key": {
                    "type": "string"
                },
                "ref_git_ssh_key_id": {
                    "type": "integer"
                },
                "title": {
                    "type": "string"
                },
                "usage_type": {
                    "description": "Usage type of the SSH key. Possible values are: \"auth\", \"signing\", \"auth_and_signing\". Default: auth_and_signing",
                    "allOf": [
                        {
                            "$ref": "#/definitions/UsageType"
                        }
                    ]
                }
            }
        },
        "Tags": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "icon_url": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "query": {
                    "type": "string"
                },
                "repo_types": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "sub_type": {
                    "type": "string"
                },
                "type": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                },
                "value": {
                    "type": "string"
                }
            }
        },
        "UpdateMemberOrganizaionInput": {
            "type": "object",
            "required": [
                "role"
            ],
            "properties": {
                "expire_at": {
                    "type": "string"
                },
                "role": {
                    "enum": [
                        "owner",
                        "developer"
                    ],
                    "allOf": [
                        {
                            "$ref": "#/definitions/OrgRole"
                        }
                    ]
                }
            }
        },
        "UpdateMemberRepositoryInput": {
            "type": "object",
            "required": [
                "role"
            ],
            "properties": {
                "expire_at": {
                    "type": "string"
                },
                "role": {
                    "enum": [
                        "owner",
                        "developer"
                    ],
                    "allOf": [
                        {
                            "$ref": "#/definitions/RepoRole"
                        }
                    ]
                }
            }
        },
        "UpdateRepositoryEnvRequest": {
            "type": "object",
            "required": [
                "key",
                "old_key",
                "value"
            ],
            "properties": {
                "key": {
                    "type": "string",
                    "maxLength": 50,
                    "minLength": 1
                },
                "old_key": {
                    "type": "string",
                    "maxLength": 50,
                    "minLength": 1
                },
                "value": {
                    "type": "string",
                    "minLength": 1
                }
            }
        },
        "UsageType": {
            "type": "string",
            "enum": [
                "auth",
                "signing",
                "auth_and_signing"
            ],
            "x-enum-varnames": [
                "UsageType_Auth",
                "UsageType_Signing",
                "UsageType_AuthAndSigning"
            ]
        },
        "User": {
            "type": "object",
            "properties": {
                "avatar": {
                    "type": "string"
                },
                "email": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "role": {
                    "$ref": "#/definitions/UserRole"
                },
                "user_status": {
                    "$ref": "#/definitions/UserStatus"
                },
                "username": {
                    "type": "string"
                }
            }
        },
        "UserRole": {
            "type": "string",
            "enum": [
                "admin",
                "user",
                "guest"
            ],
            "x-enum-varnames": [
                "UserRole_Admin",
                "UserRole_User",
                "UserRole_Guest"
            ]
        },
        "UserStatus": {
            "type": "string",
            "enum": [
                "email_unverified",
                "assigned"
            ],
            "x-enum-varnames": [
                "UserStatus_Email_Unverified",
                "UserStatus_Assigned"
            ]
        },
        "UserUpdateRoleRequest": {
            "type": "object",
            "properties": {
                "role": {
                    "$ref": "#/definitions/UserRole"
                }
            }
        },
        "VerifyAccessTokenHTTPResponse": {
            "type": "object",
            "properties": {
                "data": {
                    "$ref": "#/definitions/VerifyAccessTokenResponse"
                },
                "message": {
                    "type": "string"
                },
                "next": {
                    "type": "boolean"
                },
                "page_no": {
                    "type": "integer"
                },
                "page_size": {
                    "type": "integer"
                },
                "previous": {
                    "type": "boolean"
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "VerifyAccessTokenRequest": {
            "type": "object",
            "required": [
                "access_token"
            ],
            "properties": {
                "access_token": {
                    "type": "string",
                    "maxLength": 1024
                }
            }
        },
        "VerifyAccessTokenResponse": {
            "type": "object",
            "properties": {
                "valid": {
                    "type": "boolean"
                }
            }
        },
        "VerifyOrgAccessTokenHTTPResponse": {
            "type": "object",
            "properties": {
                "data": {
                    "$ref": "#/definitions/VerifyOrgAccessTokenResponse"
                },
                "message": {
                    "type": "string"
                },
                "next": {
                    "type": "boolean"
                },
                "page_no": {
                    "type": "integer"
                },
                "page_size": {
                    "type": "integer"
                },
                "previous": {
                    "type": "boolean"
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "VerifyOrgAccessTokenRequest": {
            "type": "object",
            "required": [
                "access_token"
            ],
            "properties": {
                "access_token": {
                    "type": "string",
                    "maxLength": 1024
                }
            }
        },
        "VerifyOrgAccessTokenResponse": {
            "type": "object",
            "properties": {
                "valid": {
                    "type": "boolean"
                }
            }
        },
        "api-server_internal_dto.ChangePasswordInput": {
            "type": "object",
            "required": [
                "current_password",
                "new_password"
            ],
            "properties": {
                "current_password": {
                    "type": "string",
                    "minLength": 8
                },
                "new_password": {
                    "type": "string",
                    "minLength": 8
                }
            }
        },
        "api-server_internal_dto.ComposeService": {
            "type": "object",
            "properties": {
                "author": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "image": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "ports": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "restart_policy": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                },
                "volumes": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                }
            }
        },
        "api-server_internal_dto.CreateECRDeploymentEnvInput": {
            "type": "object",
            "required": [
                "key",
                "value"
            ],
            "properties": {
                "key": {
                    "type": "string",
                    "maxLength": 50,
                    "minLength": 1
                },
                "value": {
                    "type": "string",
                    "minLength": 1
                }
            }
        },
        "api-server_internal_dto.CreateECRDeploymentInput": {
            "type": "object",
            "required": [
                "deployment_name",
                "env",
                "image_uri",
                "node_name",
                "port"
            ],
            "properties": {
                "deployment_name": {
                    "type": "string"
                },
                "env": {
                    "type": "object",
                    "additionalProperties": {
                        "type": "string"
                    }
                },
                "image_uri": {
                    "type": "string"
                },
                "node_name": {
                    "type": "string"
                },
                "port": {
                    "type": "integer"
                },
                "type": {
                    "type": "string",
                    "enum": [
                        "user",
                        "org"
                    ],
                    "example": "user"
                }
            }
        },
        "api-server_internal_dto.CreateECRDeploymentResponse": {
            "type": "object",
            "properties": {
                "data": {
                    "$ref": "#/definitions/api-server_internal_dto.ECRDeployment"
                },
                "message": {
                    "type": "string"
                },
                "next": {
                    "type": "boolean"
                },
                "page_no": {
                    "type": "integer"
                },
                "page_size": {
                    "type": "integer"
                },
                "previous": {
                    "type": "boolean"
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "api-server_internal_dto.CreateRepositoryCommitInput": {
            "type": "object",
            "required": [
                "actions",
                "branch",
                "commit_message",
                "repo_id"
            ],
            "properties": {
                "actions": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/api-server_internal_dto.GitLabAction"
                    }
                },
                "author_email": {
                    "type": "string"
                },
                "author_name": {
                    "type": "string"
                },
                "branch": {
                    "type": "string"
                },
                "commit_message": {
                    "type": "string"
                },
                "force": {
                    "type": "boolean"
                },
                "repo_id": {
                    "$ref": "#/definitions/api-server_internal_types.RepoID"
                },
                "start_branch": {
                    "type": "string"
                },
                "start_project": {
                    "type": "integer"
                },
                "start_sha": {
                    "type": "string"
                },
                "stats": {
                    "type": "boolean"
                }
            }
        },
        "api-server_internal_dto.DeleteECRDeploymentEnvInput": {
            "type": "object",
            "required": [
                "key"
            ],
            "properties": {
                "key": {
                    "type": "string",
                    "maxLength": 50,
                    "minLength": 1
                }
            }
        },
        "api-server_internal_dto.Deployment": {
            "type": "object",
            "properties": {
                "commit": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "duration": {
                    "type": "number"
                },
                "hardware": {
                    "$ref": "#/definitions/api-server_internal_dto.Hardware"
                },
                "id": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "repo_id": {
                    "$ref": "#/definitions/api-server_internal_types.RepoID"
                },
                "revision": {
                    "type": "string"
                },
                "status": {
                    "$ref": "#/definitions/api-server_internal_dto.DeploymentStatus"
                },
                "updated_at": {
                    "type": "string"
                },
                "url": {
                    "type": "string"
                },
                "user": {
                    "$ref": "#/definitions/User"
                },
                "workflow_name": {
                    "type": "string"
                }
            }
        },
        "api-server_internal_dto.DeploymentStatus": {
            "type": "object",
            "properties": {
                "message": {
                    "type": "string"
                },
                "reason": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                }
            }
        },
        "api-server_internal_dto.ECRDeployment": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "deployment_name": {
                    "type": "string"
                },
                "env": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/api-server_internal_dto.EnvKV"
                    }
                },
                "hardware": {
                    "$ref": "#/definitions/api-server_internal_dto.Hardware"
                },
                "id": {
                    "type": "string"
                },
                "image_uri": {
                    "type": "string"
                },
                "node_name": {
                    "type": "string"
                },
                "org_id": {
                    "type": "string"
                },
                "port": {
                    "type": "integer"
                },
                "proxy_body_size": {
                    "description": "in megabytes",
                    "type": "integer"
                },
                "status": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                },
                "url": {
                    "type": "string"
                },
                "user": {
                    "$ref": "#/definitions/User"
                }
            }
        },
        "api-server_internal_dto.EcrIngressConfig": {
            "type": "object",
            "properties": {
                "proxy_body_size": {
                    "description": "INFO: for 413 Request Entity Too Large HTTP error, in Megabytes",
                    "type": "integer",
                    "minimum": 1,
                    "example": 100
                }
            }
        },
        "api-server_internal_dto.EnvKV": {
            "type": "object",
            "properties": {
                "key": {
                    "type": "string"
                },
                "value": {
                    "type": "string"
                }
            }
        },
        "api-server_internal_dto.GetAllSignUpRequestsOutput": {
            "type": "object",
            "properties": {
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/api-server_internal_dto.SignUpRequest"
                    }
                },
                "message": {
                    "type": "string"
                },
                "next": {
                    "type": "boolean"
                },
                "page_no": {
                    "type": "integer"
                },
                "page_size": {
                    "type": "integer"
                },
                "previous": {
                    "type": "boolean"
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "api-server_internal_dto.GetComposeServicesResponse": {
            "type": "object",
            "properties": {
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/api-server_internal_dto.ComposeService"
                    }
                },
                "message": {
                    "type": "string"
                },
                "next": {
                    "type": "boolean"
                },
                "page_no": {
                    "type": "integer"
                },
                "page_size": {
                    "type": "integer"
                },
                "previous": {
                    "type": "boolean"
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "api-server_internal_dto.GetECRDeploymentResponse": {
            "type": "object",
            "properties": {
                "data": {
                    "$ref": "#/definitions/api-server_internal_dto.ECRDeployment"
                },
                "message": {
                    "type": "string"
                },
                "next": {
                    "type": "boolean"
                },
                "page_no": {
                    "type": "integer"
                },
                "page_size": {
                    "type": "integer"
                },
                "previous": {
                    "type": "boolean"
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "api-server_internal_dto.GitLabAction": {
            "type": "object",
            "required": [
                "action"
            ],
            "properties": {
                "action": {
                    "type": "string",
                    "enum": [
                        "create",
                        "update",
                        "delete",
                        "move",
                        "chmod"
                    ]
                },
                "content": {
                    "type": "string"
                },
                "encoding": {
                    "type": "string",
                    "enum": [
                        "base64",
                        "text"
                    ]
                },
                "execute_filemode": {
                    "type": "boolean"
                },
                "file_path": {
                    "type": "string"
                },
                "previous_path": {
                    "type": "string"
                }
            }
        },
        "api-server_internal_dto.Hardware": {
            "type": "object",
            "properties": {
                "cpu": {
                    "type": "integer"
                },
                "created_at": {
                    "type": "string"
                },
                "gpu_mem": {
                    "$ref": "#/definitions/api-server_internal_types.HardwareMem"
                },
                "gpu_model": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "mem": {
                    "$ref": "#/definitions/api-server_internal_types.HardwareMem"
                },
                "name": {
                    "type": "string"
                },
                "node_name": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                }
            }
        },
        "api-server_internal_dto.InviteOrgMembersInput": {
            "type": "object",
            "required": [
                "members"
            ],
            "properties": {
                "members": {
                    "type": "array",
                    "maxItems": 15,
                    "minItems": 1,
                    "items": {
                        "$ref": "#/definitions/api-server_internal_dto.OrgMemberInviteInput"
                    }
                }
            }
        },
        "api-server_internal_dto.InviteRepoMemberInput": {
            "type": "object",
            "required": [
                "role",
                "user_id"
            ],
            "properties": {
                "expire_at": {
                    "type": "string"
                },
                "role": {
                    "enum": [
                        "owner",
                        "developer"
                    ],
                    "allOf": [
                        {
                            "$ref": "#/definitions/RepoRole"
                        }
                    ]
                },
                "user_id": {
                    "type": "string"
                }
            }
        },
        "api-server_internal_dto.InviteRepoMembersInput": {
            "type": "object",
            "required": [
                "members"
            ],
            "properties": {
                "members": {
                    "type": "array",
                    "maxItems": 15,
                    "minItems": 1,
                    "items": {
                        "$ref": "#/definitions/api-server_internal_dto.RepoMemberInviteInput"
                    }
                }
            }
        },
        "api-server_internal_dto.InviteUserInput": {
            "type": "object",
            "required": [
                "email",
                "name",
                "role"
            ],
            "properties": {
                "email": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "role": {
                    "enum": [
                        "admin",
                        "user",
                        "guest"
                    ],
                    "allOf": [
                        {
                            "$ref": "#/definitions/UserRole"
                        }
                    ]
                }
            }
        },
        "api-server_internal_dto.ListECRDeploymentResponse": {
            "type": "object",
            "properties": {
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/api-server_internal_dto.ECRDeployment"
                    }
                },
                "message": {
                    "type": "string"
                },
                "next": {
                    "type": "boolean"
                },
                "page_no": {
                    "type": "integer"
                },
                "page_size": {
                    "type": "integer"
                },
                "previous": {
                    "type": "boolean"
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "api-server_internal_dto.ListGPUNodesResponse": {
            "type": "object",
            "properties": {
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/GPUNode"
                    }
                },
                "message": {
                    "type": "string"
                },
                "next": {
                    "type": "boolean"
                },
                "page_no": {
                    "type": "integer"
                },
                "page_size": {
                    "type": "integer"
                },
                "previous": {
                    "type": "boolean"
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "api-server_internal_dto.ListHardwareResponse": {
            "type": "object",
            "properties": {
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/api-server_internal_dto.Hardware"
                    }
                },
                "message": {
                    "type": "string"
                },
                "next": {
                    "type": "boolean"
                },
                "page_no": {
                    "type": "integer"
                },
                "page_size": {
                    "type": "integer"
                },
                "previous": {
                    "type": "boolean"
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "api-server_internal_dto.ListOrgMembersOutput": {
            "type": "object",
            "properties": {
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/api-server_internal_entities.OrgMemberInfo"
                    }
                },
                "message": {
                    "type": "string"
                },
                "next": {
                    "type": "boolean"
                },
                "page_no": {
                    "type": "integer"
                },
                "page_size": {
                    "type": "integer"
                },
                "previous": {
                    "type": "boolean"
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "api-server_internal_dto.ListOrganizationsOutput": {
            "type": "object",
            "properties": {
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/api-server_internal_dto.OrgReposInfo"
                    }
                },
                "message": {
                    "type": "string"
                },
                "next": {
                    "type": "boolean"
                },
                "page_no": {
                    "type": "integer"
                },
                "page_size": {
                    "type": "integer"
                },
                "previous": {
                    "type": "boolean"
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "api-server_internal_dto.ListRepoTagsOutput": {
            "type": "object",
            "properties": {
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/Tags"
                    }
                },
                "message": {
                    "type": "string"
                },
                "next": {
                    "type": "boolean"
                },
                "page_no": {
                    "type": "integer"
                },
                "page_size": {
                    "type": "integer"
                },
                "previous": {
                    "type": "boolean"
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "api-server_internal_dto.ListRepositoryMembersOutput": {
            "type": "object",
            "properties": {
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/api-server_internal_entities.RepoMemberInfo"
                    }
                },
                "message": {
                    "type": "string"
                },
                "next": {
                    "type": "boolean"
                },
                "page_no": {
                    "type": "integer"
                },
                "page_size": {
                    "type": "integer"
                },
                "previous": {
                    "type": "boolean"
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "api-server_internal_dto.OrgMemberInviteInput": {
            "type": "object",
            "required": [
                "role",
                "user_id"
            ],
            "properties": {
                "expire_at": {
                    "type": "string"
                },
                "role": {
                    "enum": [
                        "owner",
                        "developer"
                    ],
                    "allOf": [
                        {
                            "$ref": "#/definitions/OrgRole"
                        }
                    ]
                },
                "user_id": {
                    "type": "string"
                }
            }
        },
        "api-server_internal_dto.OrgReposInfo": {
            "type": "object",
            "properties": {
                "org": {
                    "$ref": "#/definitions/api-server_internal_entities.Organization"
                },
                "repos": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/api-server_internal_entities.Repository"
                    }
                }
            }
        },
        "api-server_internal_dto.RepoMemberInviteInput": {
            "type": "object",
            "required": [
                "role",
                "user_id"
            ],
            "properties": {
                "expire_at": {
                    "type": "string"
                },
                "role": {
                    "enum": [
                        "owner",
                        "developer"
                    ],
                    "allOf": [
                        {
                            "$ref": "#/definitions/RepoRole"
                        }
                    ]
                },
                "user_id": {
                    "type": "string"
                }
            }
        },
        "api-server_internal_dto.RepositoryHardwareInput": {
            "type": "object",
            "properties": {
                "cpu": {
                    "type": "integer"
                },
                "mem": {
                    "$ref": "#/definitions/api-server_internal_types.HardwareMem"
                },
                "name": {
                    "type": "string"
                },
                "node_name": {
                    "type": "string"
                }
            }
        },
        "api-server_internal_dto.RepositoryOwner": {
            "type": "object",
            "required": [
                "id",
                "type"
            ],
            "properties": {
                "avatar": {
                    "type": "string"
                },
                "email": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "path": {
                    "type": "string"
                },
                "type": {
                    "$ref": "#/definitions/RepositoryOwnerType"
                }
            }
        },
        "api-server_internal_dto.SendConfirmEmailInput": {
            "type": "object",
            "required": [
                "email"
            ],
            "properties": {
                "email": {
                    "type": "string"
                }
            }
        },
        "api-server_internal_dto.SignUpRequest": {
            "type": "object",
            "properties": {
                "email": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                }
            }
        },
        "api-server_internal_dto.SignupInput": {
            "type": "object",
            "required": [
                "email",
                "name",
                "password",
                "username"
            ],
            "properties": {
                "email": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "password": {
                    "type": "string"
                },
                "username": {
                    "type": "string"
                }
            }
        },
        "api-server_internal_dto.StartDeploymentRequest": {
            "type": "object",
            "properties": {
                "revision": {
                    "type": "string"
                }
            }
        },
        "api-server_internal_dto.StartDeploymentResponse": {
            "type": "object",
            "properties": {
                "data": {
                    "$ref": "#/definitions/api-server_internal_dto.Deployment"
                },
                "message": {
                    "type": "string"
                },
                "next": {
                    "type": "boolean"
                },
                "page_no": {
                    "type": "integer"
                },
                "page_size": {
                    "type": "integer"
                },
                "previous": {
                    "type": "boolean"
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "api-server_internal_dto.StopDeploymentRequest": {
            "type": "object",
            "required": [
                "repo_id"
            ],
            "properties": {
                "repo_id": {
                    "$ref": "#/definitions/api-server_internal_types.RepoID"
                }
            }
        },
        "api-server_internal_dto.UpdateDeploymentStatusRequest": {
            "type": "object",
            "properties": {
                "duration": {
                    "description": "in second",
                    "type": "number"
                },
                "name": {
                    "type": "string"
                },
                "status": {
                    "enum": [
                        "Pending",
                        "Running",
                        "Succeeded",
                        "Failed",
                        "Error"
                    ],
                    "allOf": [
                        {
                            "$ref": "#/definitions/api-server_internal_enums.ArgoWorkflowStatus"
                        }
                    ]
                }
            }
        },
        "api-server_internal_dto.UpdateECRDeploymentEnvInput": {
            "type": "object",
            "required": [
                "key",
                "old_key",
                "value"
            ],
            "properties": {
                "key": {
                    "type": "string",
                    "maxLength": 50,
                    "minLength": 1
                },
                "old_key": {
                    "type": "string",
                    "maxLength": 50,
                    "minLength": 1
                },
                "value": {
                    "type": "string",
                    "minLength": 1
                }
            }
        },
        "api-server_internal_dto.UpdateECRDeploymentInput": {
            "type": "object",
            "properties": {
                "cpu": {
                    "type": "integer"
                },
                "ingress_config": {
                    "$ref": "#/definitions/api-server_internal_dto.EcrIngressConfig"
                },
                "mem": {
                    "$ref": "#/definitions/api-server_internal_types.HardwareMem"
                },
                "node_name": {
                    "type": "string"
                },
                "port": {
                    "type": "integer"
                }
            }
        },
        "api-server_internal_entities.Deployment": {
            "type": "object",
            "properties": {
                "commit": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "duration": {
                    "description": "WorkflowStatus enums.ArgoWorkflowStatus ` + "`" + `gorm:\"column:workflow_status\"` + "`" + `",
                    "type": "number"
                },
                "id": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "repo": {
                    "$ref": "#/definitions/api-server_internal_entities.Repository"
                },
                "repoID": {
                    "type": "string"
                },
                "revision": {
                    "type": "string"
                },
                "status": {
                    "$ref": "#/definitions/api-server_internal_enums.ArgoWorkflowStatus"
                },
                "updated_at": {
                    "type": "string"
                },
                "user": {
                    "$ref": "#/definitions/api-server_internal_entities.User"
                },
                "userID": {
                    "type": "string"
                },
                "workflowName": {
                    "type": "string"
                }
            }
        },
        "api-server_internal_entities.Hardware": {
            "type": "object",
            "properties": {
                "cpu": {
                    "type": "integer"
                },
                "created_at": {
                    "type": "string"
                },
                "gpu": {
                    "description": "in mebibytes",
                    "type": "integer"
                },
                "gpu_model": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "mem": {
                    "description": "in mebibytes",
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "repoID": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                }
            }
        },
        "api-server_internal_entities.OrgMemberInfo": {
            "type": "object",
            "properties": {
                "avatar": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "email": {
                    "type": "string"
                },
                "expires_at": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "org": {
                    "$ref": "#/definitions/api-server_internal_entities.Organization"
                },
                "org_id": {
                    "type": "string"
                },
                "role": {
                    "$ref": "#/definitions/OrgRole"
                },
                "updated_at": {
                    "type": "string"
                },
                "user": {
                    "$ref": "#/definitions/api-server_internal_entities.User"
                },
                "user_id": {
                    "type": "string"
                }
            }
        },
        "api-server_internal_entities.Organization": {
            "type": "object",
            "properties": {
                "avatar": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "interest": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "path_name": {
                    "type": "string"
                },
                "type": {
                    "$ref": "#/definitions/OrganizationType"
                },
                "updated_at": {
                    "type": "string"
                }
            }
        },
        "api-server_internal_entities.RepoMemberInfo": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "email": {
                    "type": "string"
                },
                "expires_at": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "repo_id": {
                    "type": "string"
                },
                "role": {
                    "description": "Repo      *Repository    ` + "`" + `json:\"repo\" gorm:\"foreignKey:RepoID;references:ID\"` + "`" + `",
                    "allOf": [
                        {
                            "$ref": "#/definitions/RepoRole"
                        }
                    ]
                },
                "updated_at": {
                    "type": "string"
                },
                "user": {
                    "$ref": "#/definitions/api-server_internal_entities.User"
                },
                "user_id": {
                    "type": "string"
                }
            }
        },
        "api-server_internal_entities.Repository": {
            "type": "object",
            "properties": {
                "avatar": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "deployment": {
                    "$ref": "#/definitions/api-server_internal_entities.Deployment"
                },
                "hardware": {
                    "description": "HardwareID *uuid.UUID  ` + "`" + `gorm:\"column:hardware_id\"` + "`" + `",
                    "allOf": [
                        {
                            "$ref": "#/definitions/api-server_internal_entities.Hardware"
                        }
                    ]
                },
                "id": {
                    "type": "string"
                },
                "metadata": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "name": {
                    "type": "string"
                },
                "org": {
                    "$ref": "#/definitions/api-server_internal_entities.Organization"
                },
                "org_id": {
                    "type": "string"
                },
                "readme_file": {
                    "type": "string"
                },
                "ref_git_repoid": {
                    "type": "integer"
                },
                "type": {
                    "$ref": "#/definitions/RepoType"
                },
                "updated_at": {
                    "type": "string"
                },
                "user": {
                    "$ref": "#/definitions/api-server_internal_entities.User"
                },
                "user_id": {
                    "type": "string"
                },
                "visibility": {
                    "$ref": "#/definitions/RepoVisibility"
                }
            }
        },
        "api-server_internal_entities.User": {
            "type": "object",
            "properties": {
                "avatar": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "email": {
                    "type": "string"
                },
                "git_access_token": {
                    "type": "string"
                },
                "git_access_token_expires_at": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "ref_git_userid": {
                    "type": "integer"
                },
                "role": {
                    "$ref": "#/definitions/UserRole"
                },
                "status": {
                    "$ref": "#/definitions/UserStatus"
                },
                "updated_at": {
                    "type": "string"
                },
                "username": {
                    "type": "string"
                }
            }
        },
        "api-server_internal_enums.ArgoWorkflowStatus": {
            "type": "string",
            "enum": [
                "Pending",
                "Running",
                "Error",
                "Succeeded",
                "Failed",
                "Not Running",
                "Terminated"
            ],
            "x-enum-varnames": [
                "ArgoWorkflowStatus_Pending",
                "ArgoWorkflowStatus_Running",
                "ArgoWorkflowStatus_Error",
                "ArgoWorkflowStatus_Succeeded",
                "ArgoWorkflowStatus_Failed",
                "ArgoWorkflowStatus_NotRunning",
                "ArgoWorkflowStatus_Terminated"
            ]
        },
        "api-server_internal_enums.DeploymentType": {
            "type": "string",
            "enum": [
                "space",
                "ecr"
            ],
            "x-enum-varnames": [
                "DeploymentType_Space",
                "DeploymentType_ECR"
            ]
        },
        "api-server_internal_enums.MemoryUnit": {
            "type": "string",
            "enum": [
                "MiB",
                "GiB"
            ],
            "x-enum-varnames": [
                "MemoryUnit_MiB",
                "MemoryUnit_GiB"
            ]
        },
        "api-server_internal_enums.RepoAccessTokenScope": {
            "type": "string",
            "enum": [
                "api",
                "api_read",
                "read_repository",
                "write_repository"
            ],
            "x-enum-varnames": [
                "RepoAccessToken_API",
                "RepoAccessToken_API_READ",
                "RepoAccessToken_READ_REPOSITORY",
                "RepoAccessToken_WRITE_REPOSITORY"
            ]
        },
        "api-server_internal_types.HardwareMem": {
            "type": "object",
            "required": [
                "amount",
                "unit"
            ],
            "properties": {
                "amount": {
                    "type": "integer",
                    "minimum": 1
                },
                "unit": {
                    "enum": [
                        "MiB",
                        "GiB"
                    ],
                    "allOf": [
                        {
                            "$ref": "#/definitions/api-server_internal_enums.MemoryUnit"
                        }
                    ]
                }
            }
        },
        "api-server_internal_types.RepoID": {
            "type": "object"
        },
        "github_com_golang-jwt_jwt_v5.NumericDate": {
            "type": "object",
            "properties": {
                "time.Time": {
                    "type": "string"
                }
            }
        },
        "multipart.FileHeader": {
            "type": "object",
            "properties": {
                "filename": {
                    "type": "string"
                },
                "header": {
                    "$ref": "#/definitions/textproto.MIMEHeader"
                },
                "size": {
                    "type": "integer"
                }
            }
        },
        "textproto.MIMEHeader": {
            "type": "object",
            "additionalProperties": {
                "type": "array",
                "items": {
                    "type": "string"
                }
            }
        }
    },
    "securityDefinitions": {
        "Bearer": {
            "description": "Type \"Bearer\" followed by a space and JWT token.",
            "type": "apiKey",
            "name": "Authorization",
            "in": "header"
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "1.0",
	Host:             "localhost:8080",
	BasePath:         "/api/v1",
	Schemes:          []string{"http", "https"},
	Title:            "AI Benchmarking Platform Service",
	Description:      "A machine learning benchmarking management service API developed in Go using Gin framework.",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
