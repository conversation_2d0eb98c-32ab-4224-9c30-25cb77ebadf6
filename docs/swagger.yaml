basePath: /api/v1
definitions:
  AccessToken:
    properties:
      access_token:
        type: string
      created_at:
        type: string
      expires_at:
        type: string
      id:
        type: string
      name:
        type: string
      revoked:
        type: boolean
      scopes:
        type: string
      updated_at:
        type: string
    type: object
  AccessTokenClaims:
    properties:
      aal:
        type: string
      app_metadata:
        additionalProperties: true
        type: object
      aud:
        description: the `aud` (Audience) claim. See https://datatracker.ietf.org/doc/html/rfc7519#section-4.1.3
        items:
          type: string
        type: array
      email:
        type: string
      exp:
        allOf:
        - $ref: '#/definitions/github_com_golang-jwt_jwt_v5.NumericDate'
        description: the `exp` (Expiration Time) claim. See https://datatracker.ietf.org/doc/html/rfc7519#section-4.1.4
      iat:
        allOf:
        - $ref: '#/definitions/github_com_golang-jwt_jwt_v5.NumericDate'
        description: the `iat` (Issued At) claim. See https://datatracker.ietf.org/doc/html/rfc7519#section-4.1.6
      is_anonymous:
        type: boolean
      iss:
        description: the `iss` (Issuer) claim. See https://datatracker.ietf.org/doc/html/rfc7519#section-4.1.1
        type: string
      jti:
        description: the `jti` (JWT ID) claim. See https://datatracker.ietf.org/doc/html/rfc7519#section-4.1.7
        type: string
      nbf:
        allOf:
        - $ref: '#/definitions/github_com_golang-jwt_jwt_v5.NumericDate'
        description: the `nbf` (Not Before) claim. See https://datatracker.ietf.org/doc/html/rfc7519#section-4.1.5
      phone:
        type: string
      role:
        type: string
      session_id:
        type: string
      sub:
        description: the `sub` (Subject) claim. See https://datatracker.ietf.org/doc/html/rfc7519#section-4.1.2
        type: string
      user_metadata:
        additionalProperties: true
        type: object
    type: object
  ApprovalRequest:
    properties:
      is_accepted:
        type: boolean
    required:
    - is_accepted
    type: object
  AuthHookError:
    properties:
      http_code:
        type: integer
      message:
        type: string
    type: object
  CreateAccessTokenRequest:
    properties:
      expires_at:
        type: string
      name:
        maxLength: 100
        type: string
      scopes:
        items:
          $ref: '#/definitions/api-server_internal_enums.RepoAccessTokenScope'
        minItems: 1
        type: array
    required:
    - name
    - scopes
    type: object
  CreateAccessTokenResponse:
    properties:
      data:
        $ref: '#/definitions/AccessToken'
      message:
        type: string
      next:
        type: boolean
      page_no:
        type: integer
      page_size:
        type: integer
      previous:
        type: boolean
      total:
        type: integer
    type: object
  CreateOrgAccessTokenRequest:
    properties:
      expires_at:
        type: string
      name:
        maxLength: 100
        type: string
      scopes:
        items:
          $ref: '#/definitions/api-server_internal_enums.RepoAccessTokenScope'
        minItems: 1
        type: array
    required:
    - name
    - scopes
    type: object
  CreateOrgAccessTokenResponse:
    properties:
      data:
        $ref: '#/definitions/OrgAccessToken'
      message:
        type: string
      next:
        type: boolean
      page_no:
        type: integer
      page_size:
        type: integer
      previous:
        type: boolean
      total:
        type: integer
    type: object
  CreateOrganizationInput:
    properties:
      file:
        $ref: '#/definitions/multipart.FileHeader'
      interest:
        maxLength: 1000
        type: string
      name:
        type: string
      organization_type:
        allOf:
        - $ref: '#/definitions/OrganizationType'
        enum:
        - company
        - university
        - project
        - open_source
      path_name:
        type: string
    required:
    - name
    - organization_type
    - path_name
    type: object
  CreateOrganizationOutput:
    properties:
      uuid:
        type: string
    type: object
  CreateRepoAccessTokenRequest:
    properties:
      expires_at:
        type: string
      name:
        maxLength: 50
        minLength: 1
        type: string
      scopes:
        items:
          $ref: '#/definitions/api-server_internal_enums.RepoAccessTokenScope'
        minItems: 1
        type: array
      user_id:
        type: string
    required:
    - expires_at
    - name
    - scopes
    - user_id
    type: object
  CreateRepoAccessTokenResponse:
    properties:
      data:
        $ref: '#/definitions/RepoAccessToken'
      message:
        type: string
      next:
        type: boolean
      page_no:
        type: integer
      page_size:
        type: integer
      previous:
        type: boolean
      total:
        type: integer
    type: object
  CreateRepoTagInput:
    properties:
      icon_url:
        type: string
      name:
        type: string
      repo_types:
        items:
          type: string
        minItems: 1
        type: array
      sub_type:
        type: string
      type:
        type: string
    required:
    - name
    - repo_types
    - type
    type: object
  CreateSSHKeyInput:
    properties:
      expires_at:
        description: Expiration date of the SSH key in ISO 8601 format (YYYY-MM-DDTHH:MM:SSZ)
        type: string
      key:
        maxLength: 1000
        type: string
      title:
        maxLength: 100
        type: string
      usage_type:
        allOf:
        - $ref: '#/definitions/UsageType'
        description: 'Usage type of the SSH key. Possible values are: "auth", "signing",
          "auth_and_signing". Default: auth_and_signing'
    required:
    - key
    - title
    - usage_type
    type: object
  CustomAccessTokenInput:
    properties:
      authentication_method:
        type: string
      claims:
        $ref: '#/definitions/AccessTokenClaims'
      user_id:
        type: string
    type: object
  CustomAccessTokenOutput:
    properties:
      claims:
        additionalProperties: true
        type: object
      error:
        $ref: '#/definitions/AuthHookError'
    type: object
  DeploymentGPUInfo:
    properties:
      cpu_request:
        description: sum of memory requests
        type: integer
      deployment_type:
        $ref: '#/definitions/api-server_internal_enums.DeploymentType'
      gpu_request:
        type: integer
      id:
        type: string
      memory_bytes_request:
        description: sum of memory requests
        type: integer
      name:
        description: 'format: namespace/name'
        type: string
      repo_id:
        $ref: '#/definitions/api-server_internal_types.RepoID'
      status:
        $ref: '#/definitions/api-server_internal_dto.DeploymentStatus'
      updated_at:
        type: string
      user:
        $ref: '#/definitions/User'
    type: object
  FindOrganizationOutput:
    properties:
      avatar:
        type: string
      created_at:
        type: string
      id:
        type: string
      interest:
        type: string
      name:
        type: string
      path_name:
        type: string
      type:
        $ref: '#/definitions/OrganizationType'
      updated_at:
        type: string
    type: object
  GPUNode:
    properties:
      deployments:
        items:
          $ref: '#/definitions/DeploymentGPUInfo'
        type: array
      gpu_count:
        type: integer
      gpu_memory_gb:
        type: integer
      gpu_model:
        type: string
      name:
        type: string
      node_name:
        type: string
    type: object
  GetRepositoryOutput:
    properties:
      avatar:
        type: string
      default_branch:
        type: string
      deployment:
        $ref: '#/definitions/api-server_internal_dto.Deployment'
      hardware:
        $ref: '#/definitions/api-server_internal_dto.Hardware'
      http_url:
        type: string
      id:
        allOf:
        - $ref: '#/definitions/api-server_internal_types.RepoID'
        description: Id            uuid.UUID            `json:"id"`
      name:
        type: string
      owner:
        $ref: '#/definitions/api-server_internal_dto.RepositoryOwner'
      ssh_url:
        type: string
      tags:
        items:
          $ref: '#/definitions/Tags'
        type: array
      type:
        $ref: '#/definitions/RepoType'
      user_id:
        type: string
      visibility:
        $ref: '#/definitions/RepoVisibility'
    type: object
  GetSSHKeyOutput:
    properties:
      data:
        items:
          $ref: '#/definitions/SSHKey'
        type: array
      message:
        type: string
      next:
        type: boolean
      page_no:
        type: integer
      page_size:
        type: integer
      previous:
        type: boolean
      total:
        type: integer
    type: object
  GetSingleSSHKeyOutput:
    properties:
      created_at:
        type: string
      expires_at:
        type: string
      id:
        type: string
      key:
        type: string
      title:
        type: string
      updated_at:
        type: string
      usage_type:
        $ref: '#/definitions/UsageType'
      user_id:
        type: string
    type: object
  HTTPError:
    properties:
      code:
        type: integer
      message:
        type: string
    type: object
  HTTPResp:
    properties:
      data: {}
      message:
        type: string
      next:
        type: boolean
      page_no:
        type: integer
      page_size:
        type: integer
      previous:
        type: boolean
      total:
        type: integer
    type: object
  ListAccessTokenRequest:
    properties:
      paginate:
        $ref: '#/definitions/PaginateRequest'
    type: object
  ListAccessTokenResponse:
    properties:
      data:
        items:
          $ref: '#/definitions/AccessToken'
        type: array
      message:
        type: string
      next:
        type: boolean
      page_no:
        type: integer
      page_size:
        type: integer
      previous:
        type: boolean
      total:
        type: integer
    type: object
  ListOrgAccessTokenRequest:
    properties:
      paginate:
        $ref: '#/definitions/PaginateRequest'
    type: object
  ListOrgAccessTokenResponse:
    properties:
      data:
        items:
          $ref: '#/definitions/OrgAccessToken'
        type: array
      message:
        type: string
      next:
        type: boolean
      page_no:
        type: integer
      page_size:
        type: integer
      previous:
        type: boolean
      total:
        type: integer
    type: object
  ListRepoAccessTokenRequest:
    properties:
      paginate:
        $ref: '#/definitions/PaginateRequest'
      user_id:
        type: string
    type: object
  ListRepoAccessTokenResponse:
    properties:
      data:
        items:
          $ref: '#/definitions/RepoAccessToken'
        type: array
      message:
        type: string
      next:
        type: boolean
      page_no:
        type: integer
      page_size:
        type: integer
      previous:
        type: boolean
      total:
        type: integer
    type: object
  MergeRequest:
    properties:
      commit_id:
        type: string
      created_at:
        type: string
      id:
        type: integer
      updated_at:
        type: string
    type: object
  OrderByColumn:
    enum:
    - expires_at
    - created_at
    - updated_at
    type: string
    x-enum-varnames:
    - OrderByColumn_ExpiresAt
    - OrderByColumn_CreatedAt
    - OrderByColumn_UpdatedAt
  OrderByDirection:
    enum:
    - asc
    - desc
    type: string
    x-enum-varnames:
    - OrderByDirection_Asc
    - OrderByDirection_Desc
  OrgAccessToken:
    properties:
      access_token:
        type: string
      created_at:
        type: string
      expires_at:
        type: string
      id:
        type: string
      name:
        type: string
      revoked:
        type: boolean
      scopes:
        type: string
      updated_at:
        type: string
    type: object
  OrgRole:
    enum:
    - owner
    - developer
    type: string
    x-enum-varnames:
    - OrgRole_Owner
    - OrgRole_Developer
  Organization:
    properties:
      id:
        type: string
      name:
        type: string
      organization_type:
        $ref: '#/definitions/OrganizationType'
      path_name:
        type: string
    type: object
  OrganizationType:
    enum:
    - company
    - university
    - project
    - open_source
    type: string
    x-enum-varnames:
    - OrganizationType_Company
    - OrganizationType_University
    - OrganizationType_Project
    - OrganizationType_OpenSource
  PaginateRequest:
    properties:
      order_by:
        allOf:
        - $ref: '#/definitions/OrderByColumn'
        enum:
        - expires_at
        - created_at
        - updated_at
      page:
        minimum: 1
        type: integer
      per_page:
        maximum: 100
        minimum: 1
        type: integer
      sort:
        allOf:
        - $ref: '#/definitions/OrderByDirection'
        enum:
        - asc
        - desc
    type: object
  RepoAccessToken:
    properties:
      access_token:
        type: string
      created_at:
        type: string
      expires_at:
        type: string
      id:
        type: string
      name:
        type: string
      revoked:
        type: boolean
      scopes:
        type: string
      updated_at:
        type: string
    type: object
  RepoRole:
    enum:
    - owner
    - developer
    type: string
    x-enum-varnames:
    - RepoRole_Owner
    - RepoRole_Developer
  RepoType:
    enum:
    - spaces
    - models
    - datasets
    - composes
    type: string
    x-enum-varnames:
    - RepoType_Spaces
    - RepoType_Models
    - RepoType_Datasets
    - RepoType_Composes
  RepoVisibility:
    enum:
    - private
    - internal
    type: string
    x-enum-varnames:
    - RepoVisibility_Private
    - RepoVisibility_Internal
  Repository:
    properties:
      avatar:
        type: string
      created_at:
        type: string
      deployment_status:
        $ref: '#/definitions/api-server_internal_dto.DeploymentStatus'
      hardware:
        $ref: '#/definitions/api-server_internal_dto.Hardware'
      id:
        allOf:
        - $ref: '#/definitions/api-server_internal_types.RepoID'
        description: ID          string               `json:"id"`
      name:
        example: leaderboard
        type: string
      org:
        $ref: '#/definitions/Organization'
      tags:
        items:
          $ref: '#/definitions/Tags'
        type: array
      type:
        $ref: '#/definitions/RepoType'
      updated_at:
        type: string
      user:
        $ref: '#/definitions/User'
      visibility:
        $ref: '#/definitions/RepoVisibility'
    type: object
  RepositoryBranchInfo:
    properties:
      commit:
        $ref: '#/definitions/RepositoryCommit'
      default:
        type: boolean
      name:
        type: string
      protected:
        type: boolean
    type: object
  RepositoryCommit:
    properties:
      author_email:
        type: string
      author_name:
        type: string
      authored_date:
        type: string
      committer_email:
        type: string
      committer_name:
        type: string
      created_at:
        type: string
      id:
        type: string
      merge_request:
        $ref: '#/definitions/MergeRequest'
      message:
        type: string
      parent_ids:
        items:
          type: string
        type: array
      short_id:
        type: string
      status:
        type: string
      title:
        type: string
      user:
        $ref: '#/definitions/User'
    type: object
  RepositoryContributor:
    properties:
      avatar:
        type: string
      commits:
        type: integer
      email:
        type: string
      name:
        type: string
    type: object
  RepositoryCreateInput:
    properties:
      hardware:
        $ref: '#/definitions/api-server_internal_dto.RepositoryHardwareInput'
      name:
        type: string
      owner:
        $ref: '#/definitions/api-server_internal_dto.RepositoryOwner'
      sdk_id:
        type: string
      type:
        allOf:
        - $ref: '#/definitions/RepoType'
        enum:
        - spaces
        - models
        - datasets
        - composes
      visibility:
        allOf:
        - $ref: '#/definitions/RepoVisibility'
        enum:
        - private
        - internal
    required:
    - name
    - owner
    - type
    - visibility
    type: object
  RepositoryCreateResponse:
    properties:
      id:
        $ref: '#/definitions/api-server_internal_types.RepoID'
      uuid:
        type: string
    type: object
  RepositoryFile:
    properties:
      id:
        type: string
      last_commit:
        allOf:
        - $ref: '#/definitions/RepositoryCommit'
        description: additional fields
      mode:
        type: string
      name:
        type: string
      path:
        type: string
      type:
        type: string
    type: object
  RepositoryFileContent:
    properties:
      commit_id:
        type: string
      content:
        type: string
      content_sha256:
        type: string
      encoding:
        type: string
      last_commit_id:
        type: string
      name:
        type: string
      path:
        type: string
      ref:
        type: string
      size:
        type: string
    type: object
  RepositoryOwnerType:
    enum:
    - User
    - Organization
    type: string
    x-enum-varnames:
    - RepoOwnerType_User
    - RepoOwnerType_Org
  SSHKey:
    properties:
      created_at:
        type: string
      expires_at:
        description: Expiration date of the SSH key in ISO 8601 format (YYYY-MM-DDTHH:MM:SSZ)
        type: string
      id:
        type: string
      key:
        type: string
      ref_git_ssh_key_id:
        type: integer
      title:
        type: string
      usage_type:
        allOf:
        - $ref: '#/definitions/UsageType'
        description: 'Usage type of the SSH key. Possible values are: "auth", "signing",
          "auth_and_signing". Default: auth_and_signing'
    type: object
  Tags:
    properties:
      created_at:
        type: string
      icon_url:
        type: string
      id:
        type: string
      name:
        type: string
      query:
        type: string
      repo_types:
        items:
          type: string
        type: array
      sub_type:
        type: string
      type:
        type: string
      updated_at:
        type: string
      value:
        type: string
    type: object
  UpdateMemberOrganizaionInput:
    properties:
      expire_at:
        type: string
      role:
        allOf:
        - $ref: '#/definitions/OrgRole'
        enum:
        - owner
        - developer
    required:
    - role
    type: object
  UpdateMemberRepositoryInput:
    properties:
      expire_at:
        type: string
      role:
        allOf:
        - $ref: '#/definitions/RepoRole'
        enum:
        - owner
        - developer
    required:
    - role
    type: object
  UpdateRepositoryEnvRequest:
    properties:
      key:
        maxLength: 50
        minLength: 1
        type: string
      old_key:
        maxLength: 50
        minLength: 1
        type: string
      value:
        minLength: 1
        type: string
    required:
    - key
    - old_key
    - value
    type: object
  UsageType:
    enum:
    - auth
    - signing
    - auth_and_signing
    type: string
    x-enum-varnames:
    - UsageType_Auth
    - UsageType_Signing
    - UsageType_AuthAndSigning
  User:
    properties:
      avatar:
        type: string
      email:
        type: string
      id:
        type: string
      name:
        type: string
      role:
        $ref: '#/definitions/UserRole'
      user_status:
        $ref: '#/definitions/UserStatus'
      username:
        type: string
    type: object
  UserRole:
    enum:
    - admin
    - user
    - guest
    type: string
    x-enum-varnames:
    - UserRole_Admin
    - UserRole_User
    - UserRole_Guest
  UserStatus:
    enum:
    - email_unverified
    - assigned
    type: string
    x-enum-varnames:
    - UserStatus_Email_Unverified
    - UserStatus_Assigned
  UserUpdateRoleRequest:
    properties:
      role:
        $ref: '#/definitions/UserRole'
    type: object
  VerifyAccessTokenHTTPResponse:
    properties:
      data:
        $ref: '#/definitions/VerifyAccessTokenResponse'
      message:
        type: string
      next:
        type: boolean
      page_no:
        type: integer
      page_size:
        type: integer
      previous:
        type: boolean
      total:
        type: integer
    type: object
  VerifyAccessTokenRequest:
    properties:
      access_token:
        maxLength: 1024
        type: string
    required:
    - access_token
    type: object
  VerifyAccessTokenResponse:
    properties:
      valid:
        type: boolean
    type: object
  VerifyOrgAccessTokenHTTPResponse:
    properties:
      data:
        $ref: '#/definitions/VerifyOrgAccessTokenResponse'
      message:
        type: string
      next:
        type: boolean
      page_no:
        type: integer
      page_size:
        type: integer
      previous:
        type: boolean
      total:
        type: integer
    type: object
  VerifyOrgAccessTokenRequest:
    properties:
      access_token:
        maxLength: 1024
        type: string
    required:
    - access_token
    type: object
  VerifyOrgAccessTokenResponse:
    properties:
      valid:
        type: boolean
    type: object
  api-server_internal_dto.ChangePasswordInput:
    properties:
      current_password:
        minLength: 8
        type: string
      new_password:
        minLength: 8
        type: string
    required:
    - current_password
    - new_password
    type: object
  api-server_internal_dto.ComposeService:
    properties:
      author:
        type: string
      id:
        type: string
      image:
        type: string
      name:
        type: string
      ports:
        items:
          type: string
        type: array
      restart_policy:
        type: string
      status:
        type: string
      volumes:
        items:
          type: string
        type: array
    type: object
  api-server_internal_dto.CreateECRDeploymentEnvInput:
    properties:
      key:
        maxLength: 50
        minLength: 1
        type: string
      value:
        minLength: 1
        type: string
    required:
    - key
    - value
    type: object
  api-server_internal_dto.CreateECRDeploymentInput:
    properties:
      deployment_name:
        type: string
      env:
        additionalProperties:
          type: string
        type: object
      image_uri:
        type: string
      node_name:
        type: string
      port:
        type: integer
      type:
        enum:
        - user
        - org
        example: user
        type: string
    required:
    - deployment_name
    - env
    - image_uri
    - node_name
    - port
    type: object
  api-server_internal_dto.CreateECRDeploymentResponse:
    properties:
      data:
        $ref: '#/definitions/api-server_internal_dto.ECRDeployment'
      message:
        type: string
      next:
        type: boolean
      page_no:
        type: integer
      page_size:
        type: integer
      previous:
        type: boolean
      total:
        type: integer
    type: object
  api-server_internal_dto.CreateRepositoryCommitInput:
    properties:
      actions:
        items:
          $ref: '#/definitions/api-server_internal_dto.GitLabAction'
        type: array
      author_email:
        type: string
      author_name:
        type: string
      branch:
        type: string
      commit_message:
        type: string
      force:
        type: boolean
      repo_id:
        $ref: '#/definitions/api-server_internal_types.RepoID'
      start_branch:
        type: string
      start_project:
        type: integer
      start_sha:
        type: string
      stats:
        type: boolean
    required:
    - actions
    - branch
    - commit_message
    - repo_id
    type: object
  api-server_internal_dto.DeleteECRDeploymentEnvInput:
    properties:
      key:
        maxLength: 50
        minLength: 1
        type: string
    required:
    - key
    type: object
  api-server_internal_dto.Deployment:
    properties:
      commit:
        type: string
      created_at:
        type: string
      duration:
        type: number
      hardware:
        $ref: '#/definitions/api-server_internal_dto.Hardware'
      id:
        type: string
      name:
        type: string
      repo_id:
        $ref: '#/definitions/api-server_internal_types.RepoID'
      revision:
        type: string
      status:
        $ref: '#/definitions/api-server_internal_dto.DeploymentStatus'
      updated_at:
        type: string
      url:
        type: string
      user:
        $ref: '#/definitions/User'
      workflow_name:
        type: string
    type: object
  api-server_internal_dto.DeploymentStatus:
    properties:
      message:
        type: string
      reason:
        type: string
      status:
        type: string
    type: object
  api-server_internal_dto.ECRDeployment:
    properties:
      created_at:
        type: string
      deployment_name:
        type: string
      env:
        items:
          $ref: '#/definitions/api-server_internal_dto.EnvKV'
        type: array
      hardware:
        $ref: '#/definitions/api-server_internal_dto.Hardware'
      id:
        type: string
      image_uri:
        type: string
      node_name:
        type: string
      org_id:
        type: string
      port:
        type: integer
      proxy_body_size:
        description: in megabytes
        type: integer
      status:
        type: string
      updated_at:
        type: string
      url:
        type: string
      user:
        $ref: '#/definitions/User'
    type: object
  api-server_internal_dto.EcrIngressConfig:
    properties:
      proxy_body_size:
        description: 'INFO: for 413 Request Entity Too Large HTTP error, in Megabytes'
        example: 100
        minimum: 1
        type: integer
    type: object
  api-server_internal_dto.EnvKV:
    properties:
      key:
        type: string
      value:
        type: string
    type: object
  api-server_internal_dto.GetAllSignUpRequestsOutput:
    properties:
      data:
        items:
          $ref: '#/definitions/api-server_internal_dto.SignUpRequest'
        type: array
      message:
        type: string
      next:
        type: boolean
      page_no:
        type: integer
      page_size:
        type: integer
      previous:
        type: boolean
      total:
        type: integer
    type: object
  api-server_internal_dto.GetComposeServicesResponse:
    properties:
      data:
        items:
          $ref: '#/definitions/api-server_internal_dto.ComposeService'
        type: array
      message:
        type: string
      next:
        type: boolean
      page_no:
        type: integer
      page_size:
        type: integer
      previous:
        type: boolean
      total:
        type: integer
    type: object
  api-server_internal_dto.GetECRDeploymentResponse:
    properties:
      data:
        $ref: '#/definitions/api-server_internal_dto.ECRDeployment'
      message:
        type: string
      next:
        type: boolean
      page_no:
        type: integer
      page_size:
        type: integer
      previous:
        type: boolean
      total:
        type: integer
    type: object
  api-server_internal_dto.GitLabAction:
    properties:
      action:
        enum:
        - create
        - update
        - delete
        - move
        - chmod
        type: string
      content:
        type: string
      encoding:
        enum:
        - base64
        - text
        type: string
      execute_filemode:
        type: boolean
      file_path:
        type: string
      previous_path:
        type: string
    required:
    - action
    type: object
  api-server_internal_dto.Hardware:
    properties:
      cpu:
        type: integer
      created_at:
        type: string
      gpu_mem:
        $ref: '#/definitions/api-server_internal_types.HardwareMem'
      gpu_model:
        type: string
      id:
        type: string
      mem:
        $ref: '#/definitions/api-server_internal_types.HardwareMem'
      name:
        type: string
      node_name:
        type: string
      updated_at:
        type: string
    type: object
  api-server_internal_dto.InviteOrgMembersInput:
    properties:
      members:
        items:
          $ref: '#/definitions/api-server_internal_dto.OrgMemberInviteInput'
        maxItems: 15
        minItems: 1
        type: array
    required:
    - members
    type: object
  api-server_internal_dto.InviteRepoMemberInput:
    properties:
      expire_at:
        type: string
      role:
        allOf:
        - $ref: '#/definitions/RepoRole'
        enum:
        - owner
        - developer
      user_id:
        type: string
    required:
    - role
    - user_id
    type: object
  api-server_internal_dto.InviteRepoMembersInput:
    properties:
      members:
        items:
          $ref: '#/definitions/api-server_internal_dto.RepoMemberInviteInput'
        maxItems: 15
        minItems: 1
        type: array
    required:
    - members
    type: object
  api-server_internal_dto.InviteUserInput:
    properties:
      email:
        type: string
      name:
        type: string
      role:
        allOf:
        - $ref: '#/definitions/UserRole'
        enum:
        - admin
        - user
        - guest
    required:
    - email
    - name
    - role
    type: object
  api-server_internal_dto.ListECRDeploymentResponse:
    properties:
      data:
        items:
          $ref: '#/definitions/api-server_internal_dto.ECRDeployment'
        type: array
      message:
        type: string
      next:
        type: boolean
      page_no:
        type: integer
      page_size:
        type: integer
      previous:
        type: boolean
      total:
        type: integer
    type: object
  api-server_internal_dto.ListGPUNodesResponse:
    properties:
      data:
        items:
          $ref: '#/definitions/GPUNode'
        type: array
      message:
        type: string
      next:
        type: boolean
      page_no:
        type: integer
      page_size:
        type: integer
      previous:
        type: boolean
      total:
        type: integer
    type: object
  api-server_internal_dto.ListHardwareResponse:
    properties:
      data:
        items:
          $ref: '#/definitions/api-server_internal_dto.Hardware'
        type: array
      message:
        type: string
      next:
        type: boolean
      page_no:
        type: integer
      page_size:
        type: integer
      previous:
        type: boolean
      total:
        type: integer
    type: object
  api-server_internal_dto.ListOrgMembersOutput:
    properties:
      data:
        items:
          $ref: '#/definitions/api-server_internal_entities.OrgMemberInfo'
        type: array
      message:
        type: string
      next:
        type: boolean
      page_no:
        type: integer
      page_size:
        type: integer
      previous:
        type: boolean
      total:
        type: integer
    type: object
  api-server_internal_dto.ListOrganizationsOutput:
    properties:
      data:
        items:
          $ref: '#/definitions/api-server_internal_dto.OrgReposInfo'
        type: array
      message:
        type: string
      next:
        type: boolean
      page_no:
        type: integer
      page_size:
        type: integer
      previous:
        type: boolean
      total:
        type: integer
    type: object
  api-server_internal_dto.ListRepoTagsOutput:
    properties:
      data:
        items:
          $ref: '#/definitions/Tags'
        type: array
      message:
        type: string
      next:
        type: boolean
      page_no:
        type: integer
      page_size:
        type: integer
      previous:
        type: boolean
      total:
        type: integer
    type: object
  api-server_internal_dto.ListRepositoryMembersOutput:
    properties:
      data:
        items:
          $ref: '#/definitions/api-server_internal_entities.RepoMemberInfo'
        type: array
      message:
        type: string
      next:
        type: boolean
      page_no:
        type: integer
      page_size:
        type: integer
      previous:
        type: boolean
      total:
        type: integer
    type: object
  api-server_internal_dto.OrgMemberInviteInput:
    properties:
      expire_at:
        type: string
      role:
        allOf:
        - $ref: '#/definitions/OrgRole'
        enum:
        - owner
        - developer
      user_id:
        type: string
    required:
    - role
    - user_id
    type: object
  api-server_internal_dto.OrgReposInfo:
    properties:
      org:
        $ref: '#/definitions/api-server_internal_entities.Organization'
      repos:
        items:
          $ref: '#/definitions/api-server_internal_entities.Repository'
        type: array
    type: object
  api-server_internal_dto.RepoMemberInviteInput:
    properties:
      expire_at:
        type: string
      role:
        allOf:
        - $ref: '#/definitions/RepoRole'
        enum:
        - owner
        - developer
      user_id:
        type: string
    required:
    - role
    - user_id
    type: object
  api-server_internal_dto.RepositoryHardwareInput:
    properties:
      cpu:
        type: integer
      mem:
        $ref: '#/definitions/api-server_internal_types.HardwareMem'
      name:
        type: string
      node_name:
        type: string
    type: object
  api-server_internal_dto.RepositoryOwner:
    properties:
      avatar:
        type: string
      email:
        type: string
      id:
        type: string
      name:
        type: string
      path:
        type: string
      type:
        $ref: '#/definitions/RepositoryOwnerType'
    required:
    - id
    - type
    type: object
  api-server_internal_dto.SendConfirmEmailInput:
    properties:
      email:
        type: string
    required:
    - email
    type: object
  api-server_internal_dto.SignUpRequest:
    properties:
      email:
        type: string
      id:
        type: string
      name:
        type: string
    type: object
  api-server_internal_dto.SignupInput:
    properties:
      email:
        type: string
      name:
        type: string
      password:
        type: string
      username:
        type: string
    required:
    - email
    - name
    - password
    - username
    type: object
  api-server_internal_dto.StartDeploymentRequest:
    properties:
      revision:
        type: string
    type: object
  api-server_internal_dto.StartDeploymentResponse:
    properties:
      data:
        $ref: '#/definitions/api-server_internal_dto.Deployment'
      message:
        type: string
      next:
        type: boolean
      page_no:
        type: integer
      page_size:
        type: integer
      previous:
        type: boolean
      total:
        type: integer
    type: object
  api-server_internal_dto.StopDeploymentRequest:
    properties:
      repo_id:
        $ref: '#/definitions/api-server_internal_types.RepoID'
    required:
    - repo_id
    type: object
  api-server_internal_dto.UpdateDeploymentStatusRequest:
    properties:
      duration:
        description: in second
        type: number
      name:
        type: string
      status:
        allOf:
        - $ref: '#/definitions/api-server_internal_enums.ArgoWorkflowStatus'
        enum:
        - Pending
        - Running
        - Succeeded
        - Failed
        - Error
    type: object
  api-server_internal_dto.UpdateECRDeploymentEnvInput:
    properties:
      key:
        maxLength: 50
        minLength: 1
        type: string
      old_key:
        maxLength: 50
        minLength: 1
        type: string
      value:
        minLength: 1
        type: string
    required:
    - key
    - old_key
    - value
    type: object
  api-server_internal_dto.UpdateECRDeploymentInput:
    properties:
      cpu:
        type: integer
      ingress_config:
        $ref: '#/definitions/api-server_internal_dto.EcrIngressConfig'
      mem:
        $ref: '#/definitions/api-server_internal_types.HardwareMem'
      node_name:
        type: string
      port:
        type: integer
    type: object
  api-server_internal_entities.Deployment:
    properties:
      commit:
        type: string
      created_at:
        type: string
      duration:
        description: WorkflowStatus enums.ArgoWorkflowStatus `gorm:"column:workflow_status"`
        type: number
      id:
        type: string
      name:
        type: string
      repo:
        $ref: '#/definitions/api-server_internal_entities.Repository'
      repoID:
        type: string
      revision:
        type: string
      status:
        $ref: '#/definitions/api-server_internal_enums.ArgoWorkflowStatus'
      updated_at:
        type: string
      user:
        $ref: '#/definitions/api-server_internal_entities.User'
      userID:
        type: string
      workflowName:
        type: string
    type: object
  api-server_internal_entities.Hardware:
    properties:
      cpu:
        type: integer
      created_at:
        type: string
      gpu:
        description: in mebibytes
        type: integer
      gpu_model:
        type: string
      id:
        type: string
      mem:
        description: in mebibytes
        type: integer
      name:
        type: string
      repoID:
        type: string
      updated_at:
        type: string
    type: object
  api-server_internal_entities.OrgMemberInfo:
    properties:
      avatar:
        type: string
      created_at:
        type: string
      email:
        type: string
      expires_at:
        type: string
      id:
        type: string
      name:
        type: string
      org:
        $ref: '#/definitions/api-server_internal_entities.Organization'
      org_id:
        type: string
      role:
        $ref: '#/definitions/OrgRole'
      updated_at:
        type: string
      user:
        $ref: '#/definitions/api-server_internal_entities.User'
      user_id:
        type: string
    type: object
  api-server_internal_entities.Organization:
    properties:
      avatar:
        type: string
      created_at:
        type: string
      id:
        type: string
      interest:
        type: string
      name:
        type: string
      path_name:
        type: string
      type:
        $ref: '#/definitions/OrganizationType'
      updated_at:
        type: string
    type: object
  api-server_internal_entities.RepoMemberInfo:
    properties:
      created_at:
        type: string
      email:
        type: string
      expires_at:
        type: string
      id:
        type: string
      name:
        type: string
      repo_id:
        type: string
      role:
        allOf:
        - $ref: '#/definitions/RepoRole'
        description: Repo      *Repository    `json:"repo" gorm:"foreignKey:RepoID;references:ID"`
      updated_at:
        type: string
      user:
        $ref: '#/definitions/api-server_internal_entities.User'
      user_id:
        type: string
    type: object
  api-server_internal_entities.Repository:
    properties:
      avatar:
        type: string
      created_at:
        type: string
      deployment:
        $ref: '#/definitions/api-server_internal_entities.Deployment'
      hardware:
        allOf:
        - $ref: '#/definitions/api-server_internal_entities.Hardware'
        description: HardwareID *uuid.UUID  `gorm:"column:hardware_id"`
      id:
        type: string
      metadata:
        items:
          type: integer
        type: array
      name:
        type: string
      org:
        $ref: '#/definitions/api-server_internal_entities.Organization'
      org_id:
        type: string
      readme_file:
        type: string
      ref_git_repoid:
        type: integer
      type:
        $ref: '#/definitions/RepoType'
      updated_at:
        type: string
      user:
        $ref: '#/definitions/api-server_internal_entities.User'
      user_id:
        type: string
      visibility:
        $ref: '#/definitions/RepoVisibility'
    type: object
  api-server_internal_entities.User:
    properties:
      avatar:
        type: string
      created_at:
        type: string
      email:
        type: string
      git_access_token:
        type: string
      git_access_token_expires_at:
        type: string
      id:
        type: string
      name:
        type: string
      ref_git_userid:
        type: integer
      role:
        $ref: '#/definitions/UserRole'
      status:
        $ref: '#/definitions/UserStatus'
      updated_at:
        type: string
      username:
        type: string
    type: object
  api-server_internal_enums.ArgoWorkflowStatus:
    enum:
    - Pending
    - Running
    - Error
    - Succeeded
    - Failed
    - Not Running
    - Terminated
    type: string
    x-enum-varnames:
    - ArgoWorkflowStatus_Pending
    - ArgoWorkflowStatus_Running
    - ArgoWorkflowStatus_Error
    - ArgoWorkflowStatus_Succeeded
    - ArgoWorkflowStatus_Failed
    - ArgoWorkflowStatus_NotRunning
    - ArgoWorkflowStatus_Terminated
  api-server_internal_enums.DeploymentType:
    enum:
    - space
    - ecr
    type: string
    x-enum-varnames:
    - DeploymentType_Space
    - DeploymentType_ECR
  api-server_internal_enums.MemoryUnit:
    enum:
    - MiB
    - GiB
    type: string
    x-enum-varnames:
    - MemoryUnit_MiB
    - MemoryUnit_GiB
  api-server_internal_enums.RepoAccessTokenScope:
    enum:
    - api
    - api_read
    - read_repository
    - write_repository
    type: string
    x-enum-varnames:
    - RepoAccessToken_API
    - RepoAccessToken_API_READ
    - RepoAccessToken_READ_REPOSITORY
    - RepoAccessToken_WRITE_REPOSITORY
  api-server_internal_types.HardwareMem:
    properties:
      amount:
        minimum: 1
        type: integer
      unit:
        allOf:
        - $ref: '#/definitions/api-server_internal_enums.MemoryUnit'
        enum:
        - MiB
        - GiB
    required:
    - amount
    - unit
    type: object
  api-server_internal_types.RepoID:
    type: object
  github_com_golang-jwt_jwt_v5.NumericDate:
    properties:
      time.Time:
        type: string
    type: object
  multipart.FileHeader:
    properties:
      filename:
        type: string
      header:
        $ref: '#/definitions/textproto.MIMEHeader'
      size:
        type: integer
    type: object
  textproto.MIMEHeader:
    additionalProperties:
      items:
        type: string
      type: array
    type: object
host: localhost:8080
info:
  contact:
    email: <EMAIL>
    name: AI Benchmarking Maintain team
  description: A machine learning benchmarking management service API developed in
    Go using Gin framework.
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html
  title: AI Benchmarking Platform Service
  version: "1.0"
paths:
  /admin/invite:
    post:
      consumes:
      - application/json
      parameters:
      - description: Invite user to sign up
        in: body
        name: model
        required: true
        schema:
          $ref: '#/definitions/api-server_internal_dto.InviteUserInput'
      produces:
      - application/json
      responses:
        "200":
          description: OK
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "401":
          description: Unauthorized - Authentication is required to access this resource
          schema:
            $ref: '#/definitions/HTTPError'
        "403":
          description: Forbidden - User does not have permission to access this resource
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Invite user to sign up
      tags:
      - Sign Up Request
  /admin/signups:
    get:
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api-server_internal_dto.GetAllSignUpRequestsOutput'
        "500":
          description: Internal Server Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: List all sign up requests
      tags:
      - Sign Up Request
  /admin/signups/{id}/approval:
    patch:
      consumes:
      - application/json
      parameters:
      - description: Sign up request ID
        in: path
        name: id
        required: true
        type: string
      - description: Approve or Reject user
        in: body
        name: model
        required: true
        schema:
          $ref: '#/definitions/ApprovalRequest'
      produces:
      - application/json
      responses:
        "204":
          description: No Content - Request has been successfully completed, but no
            response payload body
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Approve or reject user's sign up requests
      tags:
      - Sign Up Request
  /custom_claims:
    post:
      consumes:
      - application/json
      description: Generate custom claims for Auth hook
      parameters:
      - description: Custom access token input
        in: body
        name: model
        required: true
        schema:
          $ref: '#/definitions/CustomAccessTokenInput'
      produces:
      - application/json
      responses:
        "201":
          description: Custom access token output
          schema:
            $ref: '#/definitions/CustomAccessTokenOutput'
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      summary: Generate custom claims for Auth hook
  /ecr/deployments:
    get:
      consumes:
      - application/json
      parameters:
      - default: 1
        description: Page number
        in: query
        name: page
        type: integer
      - default: 10
        description: Items per page
        in: query
        name: per_page
        type: integer
      - default: created_at
        description: Order by field
        in: query
        name: order_by
        type: string
      - description: Order direction
        enum:
        - asc
        - desc
        in: query
        name: direction
        type: string
      - description: Filter by user ID
        in: query
        name: user_id
        type: string
      - description: Filter by org ID
        in: query
        name: org_id
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api-server_internal_dto.ListECRDeploymentResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/HTTPError'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: List all ECR deployments
      tags:
      - ECR
    post:
      consumes:
      - application/json
      parameters:
      - description: Deployment configuration
        in: body
        name: input
        required: true
        schema:
          $ref: '#/definitions/api-server_internal_dto.CreateECRDeploymentInput'
      produces:
      - application/json
      responses:
        "201":
          description: Return deployment information
          schema:
            $ref: '#/definitions/api-server_internal_dto.CreateECRDeploymentResponse'
        "400":
          description: Bad Request - Invalid input
          schema:
            $ref: '#/definitions/HTTPError'
        "401":
          description: Unauthorized - Invalid or expired token
          schema:
            $ref: '#/definitions/HTTPError'
        "403":
          description: Forbidden - User does not have required permissions
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Create a new ECR deployment
      tags:
      - ECR
  /ecr/deployments/{id}:
    delete:
      consumes:
      - application/json
      parameters:
      - description: Deployment ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "204":
          description: No Content - Deployment deleted successfully
        "400":
          description: Bad Request - Invalid input
          schema:
            $ref: '#/definitions/HTTPError'
        "401":
          description: Unauthorized - Invalid or expired token
          schema:
            $ref: '#/definitions/HTTPError'
        "403":
          description: Forbidden - User does not have required permissions
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found - Deployment not found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Delete an ECR deployment
      tags:
      - ECR
    get:
      consumes:
      - application/json
      parameters:
      - description: Deployment ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api-server_internal_dto.GetECRDeploymentResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/HTTPError'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/HTTPError'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Get ECR deployment by ID
      tags:
      - ECR
    put:
      consumes:
      - application/json
      parameters:
      - description: Deployment ID
        in: path
        name: id
        required: true
        type: string
      - description: Deployment update configuration
        in: body
        name: input
        required: true
        schema:
          $ref: '#/definitions/api-server_internal_dto.UpdateECRDeploymentInput'
      produces:
      - application/json
      responses:
        "204":
          description: Return updated deployment information
          schema:
            type: string
        "400":
          description: Bad Request - Invalid input
          schema:
            $ref: '#/definitions/HTTPError'
        "401":
          description: Unauthorized - Invalid or expired token
          schema:
            $ref: '#/definitions/HTTPError'
        "403":
          description: Forbidden - User does not have required permissions
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found - Deployment not found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Update an existing ECR deployment
      tags:
      - ECR
  /ecr/deployments/{id}/deploy:
    post:
      consumes:
      - application/json
      parameters:
      - description: Deployment ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "204":
          description: No Content
          schema:
            type: string
        "400":
          description: Bad Request - Invalid input
          schema:
            $ref: '#/definitions/HTTPError'
        "401":
          description: Unauthorized - Invalid or expired token
          schema:
            $ref: '#/definitions/HTTPError'
        "403":
          description: Forbidden - User does not have required permissions
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found - Deployment not found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Deploy an ECR image to Kubernetes
      tags:
      - ECR
  /ecr/deployments/{id}/env:
    delete:
      consumes:
      - application/json
      parameters:
      - description: Deployment ID
        in: path
        name: id
        required: true
        type: string
      - description: Environment variable input
        in: body
        name: input
        required: true
        schema:
          $ref: '#/definitions/api-server_internal_dto.DeleteECRDeploymentEnvInput'
      produces:
      - application/json
      responses:
        "204":
          description: No Content - Environment variable deleted successfully
        "400":
          description: Bad Request - Invalid input
          schema:
            $ref: '#/definitions/HTTPError'
        "401":
          description: Unauthorized - Invalid or expired token
          schema:
            $ref: '#/definitions/HTTPError'
        "403":
          description: Forbidden - User does not have required permissions
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Delete an environment variable from an ECR deployment
      tags:
      - ECR
    post:
      consumes:
      - application/json
      parameters:
      - description: Deployment ID
        in: path
        name: id
        required: true
        type: string
      - description: Environment variable input
        in: body
        name: input
        required: true
        schema:
          $ref: '#/definitions/api-server_internal_dto.CreateECRDeploymentEnvInput'
      produces:
      - application/json
      responses:
        "204":
          description: No Content - Environment variable created successfully
        "400":
          description: Bad Request - Invalid input
          schema:
            $ref: '#/definitions/HTTPError'
        "401":
          description: Unauthorized - Invalid or expired token
          schema:
            $ref: '#/definitions/HTTPError'
        "403":
          description: Forbidden - User does not have required permissions
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Create a new environment variable for an ECR deployment
      tags:
      - ECR
    put:
      consumes:
      - application/json
      parameters:
      - description: Deployment ID
        in: path
        name: id
        required: true
        type: string
      - description: Environment variable input
        in: body
        name: input
        required: true
        schema:
          $ref: '#/definitions/api-server_internal_dto.UpdateECRDeploymentEnvInput'
      produces:
      - application/json
      responses:
        "204":
          description: No Content - Environment variable updated successfully
        "400":
          description: Bad Request - Invalid input
          schema:
            $ref: '#/definitions/HTTPError'
        "401":
          description: Unauthorized - Invalid or expired token
          schema:
            $ref: '#/definitions/HTTPError'
        "403":
          description: Forbidden - User does not have required permissions
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Update an environment variable for an ECR deployment
      tags:
      - ECR
  /ecr/deployments/{id}/stop:
    post:
      consumes:
      - application/json
      parameters:
      - description: Deployment ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "204":
          description: No Content - Deployment stopped successfully
        "400":
          description: Bad Request - Invalid input
          schema:
            $ref: '#/definitions/HTTPError'
        "401":
          description: Unauthorized - Invalid or expired token
          schema:
            $ref: '#/definitions/HTTPError'
        "403":
          description: Forbidden - User does not have required permissions
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found - Deployment not found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Stop an ECR deployment
      tags:
      - ECR
  /explore:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: string
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Health check
      tags:
      - Health
  /hardwares:
    get:
      consumes:
      - application/json
      parameters:
      - description: Return short version of the response, default is true
        in: query
        name: short
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: Return list of gpu nodes
          schema:
            items:
              $ref: '#/definitions/api-server_internal_dto.ListGPUNodesResponse'
            type: array
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: List GPU nodes
      tags:
      - Machine
  /organizations:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: Return list of organizations
          schema:
            $ref: '#/definitions/api-server_internal_dto.ListOrganizationsOutput'
        "403":
          description: Forbidden - invalid token
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Host Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: List organizations
      tags:
      - Organization
    post:
      consumes:
      - application/json
      parameters:
      - description: Organization information
        in: body
        name: model
        required: true
        schema:
          $ref: '#/definitions/CreateOrganizationInput'
      produces:
      - application/json
      responses:
        "201":
          description: Return Organization information
          schema:
            $ref: '#/definitions/CreateOrganizationOutput'
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Host Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Create organization
      tags:
      - Organization
  /organizations/:org_id:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: Return organization entity info
          schema:
            $ref: '#/definitions/FindOrganizationOutput'
        "403":
          description: Forbidden - invalid token
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Host Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Get organization info
      tags:
      - Organization
  /organizations/:org_id/members:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: Return organization members
          schema:
            $ref: '#/definitions/api-server_internal_dto.ListOrgMembersOutput'
        "403":
          description: Forbidden - invalid token
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Host Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: List organization members info
      tags:
      - Organization
  /organizations/{id}:
    delete:
      consumes:
      - application/json
      parameters:
      - description: Organization ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Return message
          schema:
            type: string
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "401":
          description: Unauthorized - invalid token
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Host Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Delete organization
      tags:
      - Organization
  /organizations/{id}/invite:
    post:
      consumes:
      - application/json
      parameters:
      - description: Organization ID
        in: path
        name: id
        required: true
        type: string
      - description: Invite Users to Organization information
        in: body
        name: model
        required: true
        schema:
          $ref: '#/definitions/api-server_internal_dto.InviteOrgMembersInput'
      produces:
      - application/json
      responses:
        "200":
          description: Return message invite organization
          schema:
            type: string
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Host Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Invite users to organization
      tags:
      - Organization
  /organizations/{org_id}/avatar:
    delete:
      parameters:
      - description: Organization ID
        in: path
        name: org_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Delete organization avatar
      tags:
      - Organization
    post:
      consumes:
      - multipart/form-data
      parameters:
      - description: Organization ID
        in: path
        name: org_id
        required: true
        type: string
      - description: Avatar file
        in: formData
        name: file
        required: true
        type: file
      produces:
      - application/json
      responses:
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Upload organization avatar
      tags:
      - Organization
  /organizations/{org_id}/members/{member_id}:
    delete:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "403":
          description: Forbidden - invalid token
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Host Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Remove Member in Organizanion
      tags:
      - Organization
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "403":
          description: Forbidden - invalid token
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Host Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Get Member in Organizanion
      tags:
      - Organization
    put:
      consumes:
      - application/json
      parameters:
      - description: Invite User to Organization information
        in: body
        name: model
        required: true
        schema:
          $ref: '#/definitions/UpdateMemberOrganizaionInput'
      produces:
      - application/json
      responses:
        "403":
          description: Forbidden - invalid token
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Host Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Update Member in Organizanion
      tags:
      - Organization
  /organizations/access_tokens:
    get:
      consumes:
      - application/json
      parameters:
      - description: Access token
        in: body
        name: model
        required: true
        schema:
          $ref: '#/definitions/ListOrgAccessTokenRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Return list token
          schema:
            $ref: '#/definitions/ListOrgAccessTokenResponse'
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: List org access token
      tags:
      - Access Token
    post:
      consumes:
      - application/json
      parameters:
      - description: Access token
        in: body
        name: model
        required: true
        schema:
          $ref: '#/definitions/CreateOrgAccessTokenRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Return created token
          schema:
            $ref: '#/definitions/CreateOrgAccessTokenResponse'
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Create new org access token
      tags:
      - Access Token
  /organizations/access_tokens/{id}:
    delete:
      consumes:
      - application/json
      parameters:
      - description: Access token ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "204":
          description: No content
          schema:
            type: string
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Delete org access token
      tags:
      - Access Token
  /organizations/access_tokens/verify:
    post:
      consumes:
      - application/json
      parameters:
      - description: Access token to verify
        in: body
        name: model
        required: true
        schema:
          $ref: '#/definitions/VerifyOrgAccessTokenRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Return verification result
          schema:
            $ref: '#/definitions/VerifyOrgAccessTokenHTTPResponse'
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      summary: Verify org access token validity
      tags:
      - Access Token
  /organizations/me:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: Return list of organizations
          schema:
            $ref: '#/definitions/api-server_internal_dto.ListOrganizationsOutput'
        "403":
          description: Forbidden - invalid token
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Host Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: List organizations
      tags:
      - Organization
  /repositories:
    get:
      consumes:
      - application/json
      parameters:
      - description: search by repos name , user 's name , org name;
        in: query
        name: keyword
        type: string
      - description: filter by repository type
        enum:
        - spaces
        - models
        - datasets
        - composes
        in: query
        name: repository_type
        type: string
      - description: filter by tags; multi-values split by comma
        in: query
        name: tags
        type: string
      - description: pagination page number
        in: query
        minimum: 1
        name: page
        type: integer
      - description: number of results per page
        in: query
        name: per_page
        type: integer
      - description: order the result by field name
        enum:
        - created_at
        - updated_at
        in: query
        name: order_by
        type: string
      - description: Sort result by ascending or descending
        enum:
        - asc
        - desc
        in: query
        name: sort
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: List of matched repository results
          schema:
            items:
              $ref: '#/definitions/Repository'
            type: array
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: List and filter repositories
      tags:
      - Repository
    post:
      consumes:
      - application/json
      parameters:
      - description: Add repository
        in: body
        name: model
        required: true
        schema:
          $ref: '#/definitions/RepositoryCreateInput'
      produces:
      - application/json
      responses:
        "201":
          description: Return repository information
          schema:
            $ref: '#/definitions/RepositoryCreateResponse'
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Add new repository
      tags:
      - Repository
  /repositories/{repo_type}/{namespace}/{repo_name}:
    delete:
      consumes:
      - application/json
      parameters:
      - description: Repository type
        enum:
        - spaces
        - datasets
        - models
        - composes
        in: path
        name: repo_type
        required: true
        type: string
      - description: Repository namespace
        in: path
        name: namespace
        required: true
        type: string
      - description: Repository name
        in: path
        name: repo_name
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Return repository information
          schema:
            type: string
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Delete a repository
      tags:
      - Repository
    get:
      consumes:
      - application/json
      parameters:
      - description: Repository type
        enum:
        - spaces
        - datasets
        - models
        - composes
        in: path
        name: repo_type
        required: true
        type: string
      - description: Repository namespace
        in: path
        name: namespace
        required: true
        type: string
      - description: Repository name
        in: path
        name: repo_name
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Ok
          schema:
            items:
              $ref: '#/definitions/GetRepositoryOutput'
            type: array
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Get repo information
      tags:
      - Repository
    patch:
      consumes:
      - application/json
      parameters:
      - description: Repository type
        enum:
        - spaces
        - datasets
        - models
        - composes
        in: path
        name: repo_type
        required: true
        type: string
      - description: Repository namespace
        in: path
        name: namespace
        required: true
        type: string
      - description: Repository name
        in: path
        name: repo_name
        required: true
        type: string
      produces:
      - application/json
      responses:
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Update a repository
      tags:
      - Repository
  /repositories/{repo_type}/{namespace}/{repo_name}/access_tokens:
    get:
      consumes:
      - application/json
      parameters:
      - description: Access token
        in: body
        name: model
        required: true
        schema:
          $ref: '#/definitions/ListRepoAccessTokenRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Return list token
          schema:
            $ref: '#/definitions/ListRepoAccessTokenResponse'
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: List repo access token
      tags:
      - Repository
    post:
      consumes:
      - application/json
      parameters:
      - description: Access token
        in: body
        name: model
        required: true
        schema:
          $ref: '#/definitions/CreateRepoAccessTokenRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Return created token
          schema:
            $ref: '#/definitions/CreateRepoAccessTokenResponse'
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Create new repo access token
      tags:
      - Repository
  /repositories/{repo_type}/{namespace}/{repo_name}/access_tokens/{id}:
    delete:
      consumes:
      - application/json
      parameters:
      - description: Access token ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "204":
          description: No content
          schema:
            type: string
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Delete repo access token
      tags:
      - Repository
  /repositories/{repo_type}/{namespace}/{repo_name}/archive:
    get:
      consumes:
      - application/json
      parameters:
      - description: Repository type
        enum:
        - spaces
        - datasets
        - models
        - composes
        in: path
        name: repo_type
        required: true
        type: string
      - description: Repository namespace
        in: path
        name: namespace
        required: true
        type: string
      - description: Repository name
        in: path
        name: repo_name
        required: true
        type: string
      - description: Archive type
        in: query
        name: type
        type: string
      - description: Branch name
        in: query
        name: ref
        type: string
      produces:
      - application/json
      responses:
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Archive repository
      tags:
      - Repository
  /repositories/{repo_type}/{namespace}/{repo_name}/avatar:
    delete:
      parameters:
      - description: Repository type
        enum:
        - spaces
        - datasets
        - models
        in: path
        name: repo_type
        required: true
        type: string
      - description: Repository namespace
        in: path
        name: namespace
        required: true
        type: string
      - description: Repository name
        in: path
        name: repo_name
        required: true
        type: string
      produces:
      - application/json
      responses:
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Delete repository avatar
      tags:
      - Repository
    post:
      consumes:
      - multipart/form-data
      parameters:
      - description: Repository type
        enum:
        - spaces
        - datasets
        - models
        in: path
        name: repo_type
        required: true
        type: string
      - description: Repository namespace
        in: path
        name: namespace
        required: true
        type: string
      - description: Repository name
        in: path
        name: repo_name
        required: true
        type: string
      - description: Avatar file
        in: formData
        name: file
        required: true
        type: file
      produces:
      - application/json
      responses:
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Upload repository avatar
      tags:
      - Repository
  /repositories/{repo_type}/{namespace}/{repo_name}/branch/{branch}:
    get:
      consumes:
      - application/json
      parameters:
      - description: Repository type
        enum:
        - spaces
        - datasets
        - models
        - composes
        in: path
        name: repo_type
        required: true
        type: string
      - description: Repository namespace
        in: path
        name: namespace
        required: true
        type: string
      - description: Repository name
        in: path
        name: repo_name
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Ok
          schema:
            $ref: '#/definitions/RepositoryBranchInfo'
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Get single repository branch
      tags:
      - Repository
  /repositories/{repo_type}/{namespace}/{repo_name}/branches:
    get:
      consumes:
      - application/json
      parameters:
      - description: Repository type
        enum:
        - spaces
        - datasets
        - models
        - composes
        in: path
        name: repo_type
        required: true
        type: string
      - description: Repository namespace
        in: path
        name: namespace
        required: true
        type: string
      - description: Repository name
        in: path
        name: repo_name
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Ok
          schema:
            items:
              $ref: '#/definitions/RepositoryBranchInfo'
            type: array
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Get repository branches
      tags:
      - Repository
  /repositories/{repo_type}/{namespace}/{repo_name}/commits:
    get:
      consumes:
      - application/json
      parameters:
      - description: Repository type
        enum:
        - spaces
        - datasets
        - models
        - composes
        in: path
        name: repo_type
        required: true
        type: string
      - description: Repository namespace
        in: path
        name: namespace
        required: true
        type: string
      - description: Repository name
        in: path
        name: repo_name
        required: true
        type: string
      - description: File path
        in: query
        name: path
        type: string
      - description: Branch name
        in: query
        name: ref
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Ok
          schema:
            items:
              $ref: '#/definitions/RepositoryCommit'
            type: array
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Get repository commits
      tags:
      - Repository
    post:
      consumes:
      - application/json
      parameters:
      - description: Repository type
        enum:
        - spaces
        - datasets
        - models
        - composes
        in: path
        name: repo_type
        required: true
        type: string
      - description: Repository namespace
        in: path
        name: namespace
        required: true
        type: string
      - description: Repository name
        in: path
        name: repo_name
        required: true
        type: string
      - description: File path
        in: query
        name: path
        type: string
      - description: Branch name
        in: query
        name: ref
        type: string
      - description: Create commit to repository
        in: body
        name: model
        required: true
        schema:
          $ref: '#/definitions/api-server_internal_dto.CreateRepositoryCommitInput'
      produces:
      - application/json
      responses:
        "200":
          description: Ok
          schema:
            items:
              $ref: '#/definitions/RepositoryCommit'
            type: array
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Create repository commit
      tags:
      - Repository
  /repositories/{repo_type}/{namespace}/{repo_name}/composes:
    get:
      consumes:
      - application/json
      parameters:
      - description: Repository type
        enum:
        - spaces
        - datasets
        - models
        - composes
        in: path
        name: repo_type
        required: true
        type: string
      - description: Repository namespace
        in: path
        name: namespace
        required: true
        type: string
      - description: Repository name
        in: path
        name: repo_name
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Ok
          schema:
            items:
              $ref: '#/definitions/api-server_internal_dto.GetComposeServicesResponse'
            type: array
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Get compose services
      tags:
      - Repository Kompose
  /repositories/{repo_type}/{namespace}/{repo_name}/contributors:
    get:
      consumes:
      - application/json
      parameters:
      - description: Repository type
        enum:
        - spaces
        - datasets
        - models
        - composes
        in: path
        name: repo_type
        required: true
        type: string
      - description: Repository namespace
        in: path
        name: namespace
        required: true
        type: string
      - description: Repository name
        in: path
        name: repo_name
        required: true
        type: string
      - description: Branch name
        in: query
        name: ref
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Ok
          schema:
            items:
              $ref: '#/definitions/RepositoryContributor'
            type: array
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Get repository contributors
      tags:
      - Repository
  /repositories/{repo_type}/{namespace}/{repo_name}/deployments/logs:
    get:
      consumes:
      - application/json
      parameters:
      - description: Repository type
        enum:
        - spaces
        - datasets
        - models
        in: path
        name: repo_type
        required: true
        type: string
      - description: Repository namespace
        in: path
        name: namespace
        required: true
        type: string
      - description: Repository name
        in: path
        name: repo_name
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Return deployment build logs
          schema:
            items:
              type: string
            type: array
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Retrieve deployment build logs by repository ID
      tags:
      - Repository Deployment
  /repositories/{repo_type}/{namespace}/{repo_name}/deployments/pods/logs:
    get:
      consumes:
      - application/json
      parameters:
      - description: Repository type
        enum:
        - spaces
        - datasets
        - models
        in: path
        name: repo_type
        required: true
        type: string
      - description: Repository namespace
        in: path
        name: namespace
        required: true
        type: string
      - description: Repository name
        in: path
        name: repo_name
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Return deployment pod logs
          schema:
            items:
              type: string
            type: array
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Retrieve deployment pod logs by repository ID
      tags:
      - Repository Deployment
  /repositories/{repo_type}/{namespace}/{repo_name}/deployments/restart:
    get:
      consumes:
      - application/json
      parameters:
      - description: Repository type
        enum:
        - spaces
        - datasets
        - models
        in: path
        name: repo_type
        required: true
        type: string
      - description: Repository namespace
        in: path
        name: namespace
        required: true
        type: string
      - description: Repository name
        in: path
        name: repo_name
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Restart space successfully
          schema:
            $ref: '#/definitions/HTTPResp'
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Restart space's Kubernetes deployment
      tags:
      - Repository Deployment
  /repositories/{repo_type}/{namespace}/{repo_name}/deployments/start:
    post:
      consumes:
      - application/json
      parameters:
      - description: Repository type
        enum:
        - spaces
        - datasets
        - models
        in: path
        name: repo_type
        required: true
        type: string
      - description: Repository namespace
        in: path
        name: namespace
        required: true
        type: string
      - description: Repository name
        in: path
        name: repo_name
        required: true
        type: string
      - description: Start deployment request
        in: body
        name: model
        required: true
        schema:
          $ref: '#/definitions/api-server_internal_dto.StartDeploymentRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Start deployment successfully
          schema:
            $ref: '#/definitions/api-server_internal_dto.StartDeploymentResponse'
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Start deployment
      tags:
      - Repository Deployment
  /repositories/{repo_type}/{namespace}/{repo_name}/deployments/status:
    get:
      consumes:
      - application/json
      parameters:
      - description: Repository type
        enum:
        - spaces
        - datasets
        - models
        in: path
        name: repo_type
        required: true
        type: string
      - description: Repository namespace
        in: path
        name: namespace
        required: true
        type: string
      - description: Repository name
        in: path
        name: repo_name
        required: true
        type: string
      - description: Start deployment request
        in: body
        name: model
        required: true
        schema:
          $ref: '#/definitions/api-server_internal_dto.StartDeploymentRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Start deployment successfully
          schema:
            $ref: '#/definitions/api-server_internal_dto.StartDeploymentResponse'
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Get deployment status
      tags:
      - Repository Deployment
  /repositories/{repo_type}/{namespace}/{repo_name}/deployments/stop:
    post:
      consumes:
      - application/json
      parameters:
      - description: Repository type
        enum:
        - spaces
        - datasets
        - models
        in: path
        name: repo_type
        required: true
        type: string
      - description: Repository namespace
        in: path
        name: namespace
        required: true
        type: string
      - description: Repository name
        in: path
        name: repo_name
        required: true
        type: string
      - description: Stop deployment request
        in: body
        name: model
        required: true
        schema:
          $ref: '#/definitions/api-server_internal_dto.StopDeploymentRequest'
      produces:
      - application/json
      responses:
        "204":
          description: No Content - Request has been successfully completed, but no
            response payload body
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Stop deployment
      tags:
      - Repository Deployment
  /repositories/{repo_type}/{namespace}/{repo_name}/envs:
    delete:
      consumes:
      - application/json
      parameters:
      - description: Repository type
        enum:
        - spaces
        - datasets
        - models
        - composes
        in: path
        name: repo_type
        required: true
        type: string
      - description: Repository namespace
        in: path
        name: namespace
        required: true
        type: string
      - description: Repository name
        in: path
        name: repo_name
        required: true
        type: string
      - description: Request body
        in: query
        name: key
        required: true
        type: string
      produces:
      - application/json
      responses:
        "403":
          description: Forbidden - invalid token
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Host Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Delete environment variables in Repository
      tags:
      - Repository
    get:
      consumes:
      - application/json
      parameters:
      - description: Repository type
        enum:
        - spaces
        - datasets
        - models
        - composes
        in: path
        name: repo_type
        required: true
        type: string
      - description: Repository namespace
        in: path
        name: namespace
        required: true
        type: string
      - description: Repository name
        in: path
        name: repo_name
        required: true
        type: string
      produces:
      - application/json
      responses:
        "403":
          description: Forbidden - invalid token
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Host Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Get environment variables in Repository
      tags:
      - Repository
    post:
      consumes:
      - application/json
      parameters:
      - description: Repository type
        enum:
        - spaces
        - datasets
        - models
        - composes
        in: path
        name: repo_type
        required: true
        type: string
      - description: Repository namespace
        in: path
        name: namespace
        required: true
        type: string
      - description: Repository name
        in: path
        name: repo_name
        required: true
        type: string
      - description: Request body - can be either CreateRepositoryEnvRequest for single
          env or BulkCreateRepositoryEnvRequest for multiple envs
        in: body
        name: model
        required: true
        schema:
          type: object
      produces:
      - application/json
      responses:
        "403":
          description: Forbidden - invalid token
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Host Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Create environment variables in Repository
      tags:
      - Repository
    put:
      consumes:
      - application/json
      parameters:
      - description: Repository type
        enum:
        - spaces
        - datasets
        - models
        - composes
        in: path
        name: repo_type
        required: true
        type: string
      - description: Repository namespace
        in: path
        name: namespace
        required: true
        type: string
      - description: Repository name
        in: path
        name: repo_name
        required: true
        type: string
      - description: Request body
        in: body
        name: model
        required: true
        schema:
          $ref: '#/definitions/UpdateRepositoryEnvRequest'
      produces:
      - application/json
      responses:
        "403":
          description: Forbidden - invalid token
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Host Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Update environment variables in Repository
      tags:
      - Repository
  /repositories/{repo_type}/{namespace}/{repo_name}/files:
    get:
      consumes:
      - application/json
      parameters:
      - description: Repository type
        enum:
        - spaces
        - datasets
        - models
        - composes
        in: path
        name: repo_type
        required: true
        type: string
      - description: Repository namespace
        in: path
        name: namespace
        required: true
        type: string
      - description: Repository name
        in: path
        name: repo_name
        required: true
        type: string
      - description: Branch name
        in: query
        name: ref
        type: string
      - description: File path
        in: query
        name: path
        type: string
      - description: Used to get a recursive tree. Default is false
        in: query
        name: recursive
        type: boolean
      - description: Used to get all results without pagination. Default is false
        in: query
        name: all
        type: boolean
      - description: Used to get next page of results. Get from X-Next-Page-Token
          header
        in: query
        name: page_token
        type: string
      - description: Number of result per page. Default is 20. Max is 100
        in: query
        name: per_page
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Ok
          headers:
            X-Next-Page-Token:
              description: Token to include when requesting the next page of results
                if there are more results
              type: string
          schema:
            items:
              $ref: '#/definitions/RepositoryFile'
            type: array
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: List repository files
      tags:
      - Repository File
  /repositories/{repo_type}/{namespace}/{repo_name}/files/{file_path}:
    get:
      consumes:
      - application/json
      parameters:
      - description: Repository type
        enum:
        - spaces
        - datasets
        - models
        - composes
        in: path
        name: repo_type
        required: true
        type: string
      - description: Repository namespace
        in: path
        name: namespace
        required: true
        type: string
      - description: Repository name
        in: path
        name: repo_name
        required: true
        type: string
      - description: File path
        in: path
        name: file_path
        required: true
        type: string
      - description: Branch name
        in: query
        name: ref
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Ok
          schema:
            items:
              $ref: '#/definitions/RepositoryFileContent'
            type: array
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Get file contents
      tags:
      - Repository File
  /repositories/{repo_type}/{namespace}/{repo_name}/files/raw/{file_path}:
    get:
      consumes:
      - application/json
      parameters:
      - description: Repository type
        enum:
        - spaces
        - datasets
        - models
        - composes
        in: path
        name: repo_type
        required: true
        type: string
      - description: Repository namespace
        in: path
        name: namespace
        required: true
        type: string
      - description: Repository name
        in: path
        name: repo_name
        required: true
        type: string
      - description: File path
        in: path
        name: file_path
        required: true
        type: string
      - description: Branch name
        in: query
        name: ref
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Raw file content
          schema:
            type: string
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Get raw file contents
      tags:
      - Repository File
    head:
      consumes:
      - application/json
      parameters:
      - description: Repository type
        enum:
        - spaces
        - datasets
        - models
        - composes
        in: path
        name: repo_type
        required: true
        type: string
      - description: Repository namespace
        in: path
        name: namespace
        required: true
        type: string
      - description: Repository name
        in: path
        name: repo_name
        required: true
        type: string
      - description: File path
        in: path
        name: file_path
        required: true
        type: string
      - description: Branch name
        in: query
        name: ref
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Raw file content
          schema:
            type: string
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Get header raw file contents
      tags:
      - Repository File
  /repositories/{repo_type}/{namespace}/{repo_name}/invite:
    post:
      consumes:
      - application/json
      parameters:
      - description: Repository type
        enum:
        - spaces
        - datasets
        - models
        - composes
        in: path
        name: repo_type
        required: true
        type: string
      - description: Repository namespace
        in: path
        name: namespace
        required: true
        type: string
      - description: Repository name
        in: path
        name: repo_name
        required: true
        type: string
      - description: Invite User to Repository information
        in: body
        name: model
        required: true
        schema:
          $ref: '#/definitions/api-server_internal_dto.InviteRepoMembersInput'
      produces:
      - application/json
      responses:
        "200":
          description: Return message invite organization
          schema:
            type: string
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Host Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Invite users to repository
      tags:
      - Repository
  /repositories/{repo_type}/{namespace}/{repo_name}/members:
    get:
      consumes:
      - application/json
      parameters:
      - description: Repository type
        enum:
        - spaces
        - datasets
        - models
        - composes
        in: path
        name: repo_type
        required: true
        type: string
      - description: Repository namespace
        in: path
        name: namespace
        required: true
        type: string
      - description: Repository name
        in: path
        name: repo_name
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Ok
          schema:
            items:
              $ref: '#/definitions/api-server_internal_dto.ListRepositoryMembersOutput'
            type: array
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Get repository members
      tags:
      - Repository
  /repositories/{repo_type}/{namespace}/{repo_name}/members/{member_id}:
    delete:
      consumes:
      - application/json
      parameters:
      - description: Repository type
        enum:
        - spaces
        - datasets
        - models
        - composes
        in: path
        name: repo_type
        required: true
        type: string
      - description: Repository namespace
        in: path
        name: namespace
        required: true
        type: string
      - description: Repository name
        in: path
        name: repo_name
        required: true
        type: string
      - description: Member id
        in: path
        name: member_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "403":
          description: Forbidden - invalid token
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Host Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Remove Member in Repository
      tags:
      - Organization
    get:
      consumes:
      - application/json
      parameters:
      - description: Repository type
        enum:
        - spaces
        - datasets
        - models
        - composes
        in: path
        name: repo_type
        required: true
        type: string
      - description: Repository namespace
        in: path
        name: namespace
        required: true
        type: string
      - description: Repository name
        in: path
        name: repo_name
        required: true
        type: string
      - description: Member id
        in: path
        name: member_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "403":
          description: Forbidden - invalid token
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Host Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Get Member in Repository
      tags:
      - Organization
    put:
      consumes:
      - application/json
      parameters:
      - description: Repository type
        enum:
        - spaces
        - datasets
        - models
        - composes
        in: path
        name: repo_type
        required: true
        type: string
      - description: Repository namespace
        in: path
        name: namespace
        required: true
        type: string
      - description: Repository name
        in: path
        name: repo_name
        required: true
        type: string
      - description: Member id
        in: path
        name: member_id
        required: true
        type: string
      - description: Invite User to Organization information
        in: body
        name: model
        required: true
        schema:
          $ref: '#/definitions/UpdateMemberRepositoryInput'
      produces:
      - application/json
      responses:
        "403":
          description: Forbidden - invalid token
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Host Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Update Member in Repository
      tags:
      - Organization
  /repositories/deployments:
    get:
      consumes:
      - application/json
      parameters:
      - description: Repository type
        enum:
        - spaces
        - datasets
        - models
        in: path
        name: repo_type
        required: true
        type: string
      - description: Repository namespace
        in: path
        name: namespace
        required: true
        type: string
      - description: Repository name
        in: path
        name: repo_name
        required: true
        type: string
      - description: Start deployment request
        in: body
        name: model
        required: true
        schema:
          $ref: '#/definitions/api-server_internal_dto.StartDeploymentRequest'
      produces:
      - application/json
      responses:
        "204":
          description: No Content - Request has been successfully completed, but no
            response payload body
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: List all deployments
      tags:
      - Repository Deployment
  /repositories/deployments/status:
    post:
      consumes:
      - application/json
      parameters:
      - description: Deployment Data
        in: body
        name: model
        required: true
        schema:
          $ref: '#/definitions/api-server_internal_dto.UpdateDeploymentStatusRequest'
      produces:
      - application/json
      responses:
        "204":
          description: no content
          schema:
            type: string
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      summary: Update deployment status
      tags:
      - Repository Deployment
  /repositories/tags:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: Ok
          schema:
            $ref: '#/definitions/api-server_internal_dto.ListRepoTagsOutput'
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Get repository templates
      tags:
      - Repository
    post:
      consumes:
      - application/json
      parameters:
      - description: Create tag
        in: body
        name: model
        required: true
        schema:
          $ref: '#/definitions/CreateRepoTagInput'
      produces:
      - application/json
      responses:
        "200":
          description: Ok
          schema:
            type: string
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Create repository tags
      tags:
      - Repository
  /repositories/tags/{tag_id}:
    delete:
      consumes:
      - application/json
      parameters:
      - description: tag id
        in: path
        name: tag_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Create repository tags
      tags:
      - Repository
    get:
      consumes:
      - application/json
      parameters:
      - description: tag id
        in: path
        name: tag_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Get repository tag
      tags:
      - Repository
    patch:
      consumes:
      - application/json
      parameters:
      - description: tag id
        in: path
        name: tag_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Update repository tag
      tags:
      - Repository
  /send-confirm-email:
    post:
      consumes:
      - application/json
      parameters:
      - description: Resend confirm email
        in: body
        name: model
        required: true
        schema:
          $ref: '#/definitions/api-server_internal_dto.SendConfirmEmailInput'
      produces:
      - application/json
      responses:
        "200":
          description: OK
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "401":
          description: Unauthorized - Authentication is required to access this resource
          schema:
            $ref: '#/definitions/HTTPError'
        "403":
          description: Forbidden - User does not have permission to access this resource
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Send confirm email
      tags:
      - Sign Up Request
  /signup:
    post:
      consumes:
      - application/json
      description: Signup a new user and send email verification
      parameters:
      - description: Signup input
        in: body
        name: signupInput
        required: true
        schema:
          $ref: '#/definitions/api-server_internal_dto.SignupInput'
      produces:
      - application/json
      responses:
        "201":
          description: User created successfully and email verification sent
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      summary: Signup a new user
  /users:
    get:
      consumes:
      - application/json
      parameters:
      - description: pagination page number
        in: query
        minimum: 1
        name: page
        type: integer
      - description: number of results per page
        in: query
        name: per_page
        type: integer
      - description: order the result by field name
        enum:
        - created_at
        - updated_at
        in: query
        name: order_by
        type: string
      - description: Sort result by ascending or descending
        enum:
        - asc
        - desc
        in: query
        name: sort
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: List of matched user results
          schema:
            items:
              $ref: '#/definitions/User'
            type: array
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: List and filter users
      tags:
      - User
  /users/{user_id}:
    delete:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "403":
          description: Forbidden - user not authorized
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Delete user information
      tags:
      - User
  /users/{user_id}/avatar:
    delete:
      parameters:
      - description: User ID
        in: path
        name: user_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Delete user avatar
      tags:
      - User
    post:
      consumes:
      - multipart/form-data
      parameters:
      - description: Avatar file
        in: formData
        name: file
        required: true
        type: file
      - description: User ID
        in: path
        name: user_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Upload avatar
      tags:
      - User
  /users/{user_id}/role:
    patch:
      consumes:
      - application/json
      parameters:
      - description: User ID
        in: path
        name: user_id
        required: true
        type: string
      - description: Update user role
        in: body
        name: model
        required: true
        schema:
          $ref: '#/definitions/UserUpdateRoleRequest'
      produces:
      - application/json
      responses:
        "204":
          description: No Content - Request has been successfully completed, but no
            response payload body
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Update user role
      tags:
      - User
  /users/access_tokens:
    get:
      consumes:
      - application/json
      parameters:
      - description: Access token
        in: body
        name: model
        required: true
        schema:
          $ref: '#/definitions/ListAccessTokenRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Return list token
          schema:
            $ref: '#/definitions/ListAccessTokenResponse'
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Create new access token
      tags:
      - Access Token
    post:
      consumes:
      - application/json
      parameters:
      - description: Access token
        in: body
        name: model
        required: true
        schema:
          $ref: '#/definitions/CreateAccessTokenRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Return created token
          schema:
            $ref: '#/definitions/CreateAccessTokenResponse'
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Create new access token
      tags:
      - Access Token
  /users/access_tokens/{id}:
    delete:
      consumes:
      - application/json
      parameters:
      - description: Access token ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "204":
          description: No content
          schema:
            type: string
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Create new access token
      tags:
      - Access Token
  /users/access_tokens/verify:
    post:
      consumes:
      - application/json
      parameters:
      - description: Access token to verify
        in: body
        name: model
        required: true
        schema:
          $ref: '#/definitions/VerifyAccessTokenRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Return verification result
          schema:
            $ref: '#/definitions/VerifyAccessTokenHTTPResponse'
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      summary: Verify access token validity
      tags:
      - Access Token
  /users/keys:
    get:
      consumes:
      - application/json
      parameters:
      - description: pagination page number
        in: query
        minimum: 1
        name: page
        type: integer
      - description: number of results per page
        in: query
        name: per_page
        type: integer
      - description: order the result by field name
        enum:
        - expires_at
        - created_at
        - updated_at
        in: query
        name: order_by
        type: string
      - description: Sort result by ascending or descending
        enum:
        - asc
        - desc
        in: query
        name: sort
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: List of user's SSH keys
          schema:
            items:
              $ref: '#/definitions/GetSSHKeyOutput'
            type: array
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: List user's ssh keys
      tags:
      - SSH Key
    post:
      consumes:
      - application/json
      parameters:
      - description: SSH Key
        in: body
        name: model
        required: true
        schema:
          $ref: '#/definitions/CreateSSHKeyInput'
      produces:
      - application/json
      responses:
        "201":
          description: Return created ssh key
          schema:
            type: string
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Add new ssh keys
      tags:
      - SSH Key
  /users/keys/{key_id}:
    delete:
      consumes:
      - application/json
      parameters:
      - description: SSH key ID
        in: path
        name: key_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "204":
          description: No Content - Request has been successfully completed, but no
            response payload body
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Delete user ssh key
      tags:
      - SSH Key
    get:
      consumes:
      - application/json
      parameters:
      - description: SSH key ID
        in: path
        name: key_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Return ssh key info
          schema:
            $ref: '#/definitions/GetSingleSSHKeyOutput'
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Get user ssh key
      tags:
      - SSH Key
  /users/me:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: Current user information
          schema:
            $ref: '#/definitions/User'
        "403":
          description: Forbidden - user not authorized
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Get current user information
      tags:
      - User
  /users/me/password:
    put:
      consumes:
      - application/json
      parameters:
      - description: Change password request
        in: body
        name: model
        required: true
        schema:
          $ref: '#/definitions/api-server_internal_dto.ChangePasswordInput'
      produces:
      - application/json
      responses:
        "204":
          description: No Content - Password has been successfully changed
        "400":
          description: Bad Request - invalid request
          schema:
            $ref: '#/definitions/HTTPError'
        "403":
          description: Forbidden - user not authorized
          schema:
            $ref: '#/definitions/HTTPError'
        "500":
          description: Internal Server Error - Encountered an unexpected condition
          schema:
            $ref: '#/definitions/HTTPError'
      security:
      - Bearer: []
      summary: Change user password
      tags:
      - User
schemes:
- http
- https
securityDefinitions:
  Bearer:
    description: Type "Bearer" followed by a space and JWT token.
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
