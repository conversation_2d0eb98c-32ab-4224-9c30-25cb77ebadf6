apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: pg-sc
provisioner: ebs.csi.aws.com
parameters:
  type: gp3
  iopsPerGB: "10"
  fsType: ext4
reclaimPolicy: Retain
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: supabase-pg-pvc
  namespace: supabase
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: pg-sc
  resources:
    requests:
      storage: 15Gi
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: pg-init-configmap
  namespace: supabase
data:
  init.sql: |
    \set jwt_secret `echo "$JWT_SECRET"`
    \set jwt_exp `echo "$JWT_EXP"`
    \set pgpass `echo "$POSTGRES_PASSWORD"`

    ALTER DATABASE postgres SET "app.settings.jwt_secret" TO :'jwt_secret';
    ALTER DATABASE postgres SET "app.settings.jwt_exp" TO :'jwt_exp';
    ALTER USER authenticator WITH PASSWORD :'pgpass';
    ALTER USER pgbouncer WITH PASSWORD :'pgpass';
    ALTER USER supabase_auth_admin WITH PASSWORD :'pgpass';
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: supabase-postgres
  namespace: supabase
spec:
  replicas: 1
  selector:
    matchLabels:
      app: supabase-postgres
  template:
    metadata:
      labels:
        app: supabase-postgres
    spec:
      initContainers:
        - name: init-chown
          image: busybox
          command: ["sh", "-c", "chown -R 999:999 /var/lib/postgresql/data"]
          volumeMounts:
          - name: postgres-storage
            mountPath: /var/lib/postgresql/data
      securityContext:
        fsGroup: 999
      containers:
        - name: supabase-postgres
          image: supabase/postgres:*********
          ports:
            - containerPort: 5432
          envFrom:
            - configMapRef:
                name: staging-configmap
            - secretRef:
                name: staging-secret
          volumeMounts:
            - name: postgres-storage
              mountPath: /var/lib/postgresql/data
              subPath: volvo
              # TDDO: init-script currently not working
            - name: init-script
              mountPath: /docker-entrypoint-initdb.d/init-scripts/
      volumes:
        - name: postgres-storage
          persistentVolumeClaim:
            claimName: supabase-pg-pvc
        - name: init-script
          configMap:
            name: pg-init-configmap
---
apiVersion: v1
kind: Service
metadata:
  name: supabase-postgres
  namespace: supabase
spec:
  type: ClusterIP
  selector:
    app: supabase-postgres
  ports:
    - port: 5432
      targetPort: 5432
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: supabase-auth
  namespace: supabase
spec:
  replicas: 1
  selector:
    matchLabels:
      app: supabase-auth
  template:
    metadata:
      labels:
        app: supabase-auth
    spec:
      containers:
        - name: supabase-auth
          image: supabase/gotrue:v2.157.1
          ports:
            - containerPort: 9999
          envFrom:
            - configMapRef:
                name: staging-configmap
            - secretRef:
                name: staging-secret
---
apiVersion: v1
kind: Service
metadata:
  name: supabase-auth
  namespace: supabase
spec:
  type: ClusterIP
  selector:
    app: supabase-auth
  ports:
    - port: 9999
      targetPort: 9999
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: supabase-ingress
  namespace: supabase
spec:
  ingressClassName: external-nginx
  rules:
  - host: api.volvo-dev.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: supabase-auth
            port:
              number: 9999
