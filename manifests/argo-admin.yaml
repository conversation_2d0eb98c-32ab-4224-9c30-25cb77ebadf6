apiVersion: v1
kind: ServiceAccount
metadata:
  name: argo-admin
  namespace: argo
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: argo-admin-role
rules:
  - apiGroups:
      - ""
    resources:
      - pods
      - pods/log
      - configmaps
      - secrets
      - services
      - events
    verbs:
      - "*"
  - apiGroups:
      - "argoproj.io"
    resources:
      - "*"
    verbs:
      - "*"
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: argo-admin-role-binding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: argo-admin-role
subjects:
  - kind: ServiceAccount
    name: argo-admin
    namespace: argo
---
apiVersion: v1
kind: Secret
metadata:
  name: argo-admin.service-account-token
  namespace: argo
  annotations:
    kubernetes.io/service-account.name: argo-admin
type: kubernetes.io/service-account-token
