apiVersion: v1
kind: ConfigMap
metadata:
  name: staging-configmap
data:
  PGPORT: "5432"
  POSTGRES_PORT: "5432"
  PGDATABASE: volvo
  POSTGRES_DB: volvo
  JWT_EXP: "3600"

  GOTRUE_API_HOST: "0.0.0.0"
  GOTRUE_API_PORT: "9999"
  API_EXTERNAL_URL: https://api.volvo-dev.example.com

  GOTRUE_DB_DRIVER: postgres
  GOTRUE_DB_DATABASE_URL: ***************************************************/volvo?sslmode=disable&options=-csearch_path=auth

  GOTRUE_SITE_URL: https://front-end.volvo-dev.example.com
  GOTRUE_DISABLE_SIGNUP: "false"

  GOTRUE_JWT_ADMIN_ROLES: service_role
  GOTRUE_JWT_AUD: authenticated
  GOTRUE_JWT_DEFAULT_GROUP_NAME: authenticated
  GOTRUE_JWT_EXP: "3600"

  GOTRUE_EXTERNAL_EMAIL_ENABLED: "true"
  GOTRUE_EXTERNAL_ANONYMOUS_USERS_ENABLED: "false"
  GOTRUE_MAILER_AUTOCONFIRM: "false"

  ENABLE_ANONYMOUS_USERS: "false"
  GOTRUE_SMTP_ADMIN_EMAIL: <EMAIL>
  GOTRUE_SMTP_HOST: email-smtp.ap-southeast-1.amazonaws.com
  GOTRUE_SMTP_PORT: "465"
  GOTRUE_SMTP_USER: AKIATQZCSJTVAUJ4NC5Q
  GOTRUE_SMTP_PASS: BHuuaGR8Y+AdY+S05ZGy18yhG/UZ0KJFuyj1WWHL9AST
  GOTRUE_SMTP_SENDER_NAME: onboarding
  GOTRUE_MAILER_URLPATHS_INVITE: /verify
  GOTRUE_MAILER_URLPATHS_CONFIRMATION: /verify
  GOTRUE_MAILER_URLPATHS_RECOVERY: /verify
  GOTRUE_MAILER_URLPATHS_EMAIL_CHANGE: /verify
  # GOTRUE_MAILER_TEMPLATES_RECOVERY: http://api-server.volvo.svc.cluster.local:8080/templates/recovery.html
  # GOTRUE_MAILER_TEMPLATES_INVITE: http://api-server.volvo.svc.cluster.local:8080/templates/invite.html
  GOTRUE_MAILER_OTP_EXP: "600"

  GOTRUE_EXTERNAL_PHONE_ENABLED: "false"
  GOTRUE_SMS_AUTOCONFIRM: "false"

  GOTRUE_HOOK_CUSTOM_ACCESS_TOKEN_ENABLED: "true"
  GOTRUE_HOOK_CUSTOM_ACCESS_TOKEN_URI: pg-functions://postgres/public/custom_access_token_hook

  GITLAB_HTTP_PORT: "80"
  GITLAB_SSH_PORT: "22"
  GITLAB_EXTERNAL_URL: http://localhost:80
  GITLAB_OMNIBUS_CONFIG: |
    external_url ENV['GITLAB_EXTERNAL_URL']
    gitlab_rails['gitlab_shell_ssh_port'] = ENV['GITLAB_SSH_PORT']
    gitlab_rails['impersonation_enabled'] = false
    gitlab_rails['lfs_enabled'] = true

  VOLVO_POSTGRESQL_URL: postgres://postgres:<EMAIL>:5432/volvo?sslmode=disable
  VOLVO_AUTH_PWD_SECRET: fQ7LNzdwGXDkuUnZgrWjwXtTc7zf9wGb
  VOLVO_SUPABASE_HOST: http://supabase-auth.supabase.svc.cluster.local:9999
  VOLVO_SUPABASE_INVITE_REDIRECT_URL: https://front-end.volvo-dev.example.com/reset-password
  VOLVO_GITLAB_HOST: http://gitlab-service.gitlab.svc.cluster.local
  VOLVO_GITLAB_VOLVO_API_SERVER_HOST: http://api-server.volvo.svc.cluster.local:8080
  VOLVO_SERVICE_ROLE_KEY: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************.xOp-oHXFB9_AqWi_bBH4sCXRt1q2tTOD45dQ7KEYJ44
  VOLVO_SMTP_HOST: email-smtp.ap-southeast-1.amazonaws.com
  VOLVO_SMTP_PORT: "465"
  VOLVO_SMTP_USER: AKIATQZCSJTVAUJ4NC5Q
  VOLVO_SMTP_PASS: BHuuaGR8Y+AdY+S05ZGy18yhG/UZ0KJFuyj1WWHL9AST
  VOLVO_SMTP_ADMIN_EMAIL: <EMAIL>
  VOLVO_CORS_ALLOWED_ORIGINS: "https://front-end.volvo-dev.example.com,http://front-end.volvo-dev.example.com,http://localhost:5173"
  VOLVO_ARGO_WORKFLOW_HOST: argo-server.argo.svc.cluster.local:2746 # only host and port
  VOLVO_ARGO_WORKFLOW_ACCESS_TOKEN: *****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  VOLVO_ARGO_WORKFLOW_SPACE_IMAGE_REGISTRY: ************.dkr.ecr.ap-southeast-1.amazonaws.com/staging/space
  VOLVO_ARGO_WORKFLOW_VOLVO_API_SERVER_URL: http://api-server.volvo.svc.cluster.local:8080
  VOLVO_SPACE_DOMAIN: volvo-dev.example.com
  VOLVO_SPACE_BUILD_CONTEXT: https://argo:<EMAIL>/delivery/longclawx01/volvo/leaderboard-build-context.git
  VOLVO_SPACE_INGRESS_CLASS_NAME: external-nginx
  VOLVO_IN_CLUSTER: "true"
  VOLVO_MIGRATE: "false"
  VOLVO_CUSTOM_GITLAB_SSH_HOST: ssh.gitlab.volvo-dev.example.com
  VOLVO_AWS_REGION: ap-southeast-1
  VOLVO_AWS_AVATAR_BUCKET: volvo-dev-images
  VOLVO_AWS_PRE_SIGN_EXPIRE: "3600"
  VOLVO_IMAGE_MAX_SIZE: "5242880"

  VITE_API_BASE: https://api.volvo-dev.example.com/api/v1
  VITE_SUPABASE_ENDPOINT: https://api.volvo-dev.example.com
---
apiVersion: v1
kind: Secret
metadata:
  name: staging-secret
type: Opaque
data:
  JWT_SECRET: ZDJ5emFsSGwyd3cwN0dQeFZyNzdiTWIxRmFIazlSSmY=
  GOTRUE_JWT_SECRET: ZDJ5emFsSGwyd3cwN0dQeFZyNzdiTWIxRmFIazlSSmY=
  VOLVO_AUTH_JWT_SECRET: ZDJ5emFsSGwyd3cwN0dQeFZyNzdiTWIxRmFIazlSSmY=

  PGPASSWORD: cG9zdGdyZXM=
  POSTGRES_PASSWORD: cG9zdGdyZXM=

  GITLAB_ROOT_PASSWORD: bXlzdXBlcnN0cm9uZ3Bhc3N3b3Jk
