#!/bin/bash

# curl -i --header "Host: volvo-dev.example.com" http://ab0ff8c068c814be596eed77111b6ea8-8cedc4db148823c5.elb.ap-southeast-1.amazonaws.com/space/a
# curl -i http://volvo-dev.example.com/space/a

### check ingress-nginx controller version
# POD_NAMESPACE=ingress-nginx
# POD_NAME=$(kubectl get pods -n $POD_NAMESPACE -l app.kubernetes.io/name=ingress-nginx --field-selector=status.phase=Running -o name)
# kubectl exec $POD_NAME -n $POD_NAMESPACE -- /nginx-ingress-controller --version

# aws iam create-policy --profile volvo-staging --policy-name argo-executor-policy --policy-document file://argo-executor-policy.json

# eksctl create iamserviceaccount \
#     --name executor \
#     --namespace argo \
#     --cluster volvo-staging \
#     --role-name argo-executor-role \
#     --attach-policy-arn arn:aws:iam::************:policy/argo-executor-policy \
#     --profile volvo-staging \
#     --region ap-southeast-1 \
#     --approve

# aws iam get-role \
#     --role-name s3-artifact-role \
#     --query Role.AssumeRolePolicyDocument \
#     --profile volvo-staging \
#     --region ap-southeast-1

# aws iam list-attached-role-policies \
#     --role-name s3-artifact-role \
#     --query AttachedPolicies[].PolicyArn \
#     --profile volvo-staging \
#     --region ap-southeast-1

# aws iam get-policy --policy-arn arn:aws:iam::************:policy/artifact-s3-policy \
#     --profile volvo-staging \
#     --region ap-southeast-1

# aws iam get-policy-version --policy-arn arn:aws:iam::************:policy/artifact-s3-policy --version-id v1 \
#     --profile volvo-staging \
#     --region ap-southeast-1

# eksctl create iamserviceaccount \
#     --name ebs-csi-controller-sa \
#     --namespace kube-system \
#     --cluster volvo-staging \
#     --attach-policy-arn arn:aws:iam::aws:policy/service-role/AmazonEBSCSIDriverPolicy \
#     --role-only \
#     --role-name AmazonEKS_EBS_CSI_DriverRole \
#     --profile volvo-staging \
#     --region ap-southeast-1 \
#     --approve

# eksctl create addon \
#     --name aws-ebs-csi-driver \
#     --service-account-role-arn arn:aws:iam::************:role/AmazonEKS_EBS_CSI_DriverRole \
#     --cluster volvo-staging \
#     --profile volvo-staging \
#     --region ap-southeast-1 \
#     --force

# eksctl utils migrate-to-pod-identity \
#     --profile volvo-staging \
#     --region ap-southeast-1

# curl -ikL https://argo.volvo-dev.example.com/api/v1/workflow-templates/argo -H "Authorization: Bearer *****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
# curl -ikL https://localhost:2746/api/v1/workflow-templates/argo -H "Authorization: Bearer *****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
