apiVersion: eksctl.io/v1alpha5
kind: ClusterConfig

metadata:
  name: volvo-staging
  region: ap-southeast-1
  version: '1.31'

vpc:
  id: "vpc-0f2a9b941911a1e5c"
  securityGroup: "sg-01743d07193a30232"
  subnets:
    private:
      ap-southeast-1a: { id: "subnet-09848710ef0d82b05" }
      ap-southeast-1b: { id: "subnet-007bb6bcdbe2d129d" }

managedNodeGroups:
  - name: managed-ng-linux-arm
    instanceType: c7g.2xlarge # arm64
    desiredCapacity: 1
    minSize: 1
    maxSize: 1
    amiFamily: AmazonLinux2023
    privateNetworking: true
    volumeSize: 100
    iam:
       attachPolicyARNs:
          - arn:aws:iam::aws:policy/AmazonEKSWorkerNodePolicy
          - arn:aws:iam::aws:policy/AmazonEC2ContainerRegistryFullAccess
          - arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore
          - arn:aws:iam::aws:policy/AmazonEKS_CNI_Policy
    ssh:
        allow: true
        publicKeyName: ec2-ssh-key

  - name: managed-ng-linux-amd64-gpu
    instanceType: g4dn.xlarge
    desiredCapacity: 1
    minSize: 1
    maxSize: 1
    amiFamily: AmazonLinux2023
    privateNetworking: true
    volumeSize: 100
    labels:
      nvidia.com/gpu: "true"
    taints:
      nvidia.com/gpu: "true:NoSchedule"
    iam:
      attachPolicyARNs:
        - arn:aws:iam::aws:policy/AmazonEKSWorkerNodePolicy
        - arn:aws:iam::aws:policy/AmazonEC2ContainerRegistryFullAccess
        - arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore
        - arn:aws:iam::aws:policy/AmazonEKS_CNI_Policy
    ssh:
        allow: true
        publicKeyName: ec2-ssh-key
