apiVersion: v1
kind: ServiceAccount
metadata:
  name: argo-executor
  namespace: argo
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/argo-executor-role
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: argo-executor-role
  namespace: argo
rules:
  - apiGroups:
      - argoproj.io
    resources:
      - workflowtaskresults
    verbs:
      - create
      - patch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: argo-executor-role-binding
  namespace: argo
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: argo-executor-role
subjects:
  - kind: ServiceAccount
    name: argo-executor
    namespace: argo
