apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: gitlab-data-pvc
  namespace: gitlab
spec:
  accessModes:
  - ReadWriteOnce
  storageClassName: pg-sc
  resources:
    requests:
      storage: 10Gi
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: gitlab-logs-pvc
  namespace: gitlab
spec:
  accessModes:
  - ReadWriteOnce
  storageClassName: pg-sc
  resources:
    requests:
      storage: 10Gi
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: gitlab-config-pvc
  namespace: gitlab
spec:
  accessModes:
  - ReadWriteOnce
  storageClassName: pg-sc
  resources:
    requests:
      storage: 10Gi
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: gitlab
  namespace: gitlab
spec:
  replicas: 1
  selector:
    matchLabels:
      app: gitlab
  template:
    metadata:
      labels:
        app: gitlab
    spec:
      containers:
        - name: gitlab
          image: zengxs/gitlab:17.5.1-ce
          ports:
            - containerPort: 80
            - containerPort: 443
            - containerPort: 22
          envFrom:
            - configMapRef:
                name: staging-configmap
            - secretRef:
                name: staging-secret
          volumeMounts:
            - name: gitlab-data
              mountPath: /var/opt/gitlab
            - name: gitlab-logs
              mountPath: /var/log/gitlab
            - name: gitlab-config
              mountPath: /etc/gitlab
      volumes:
        - name: gitlab-data
          persistentVolumeClaim:
            claimName: gitlab-data-pvc
        - name: gitlab-logs
          persistentVolumeClaim:
            claimName: gitlab-logs-pvc
        - name: gitlab-config
          persistentVolumeClaim:
            claimName: gitlab-config-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: gitlab-service
  namespace: gitlab
spec:
  type: ClusterIP
  selector:
    app: gitlab
  ports:
  - port: 80
    targetPort: 80
    name: http
  - port: 443
    targetPort: 443
    name: https
---
apiVersion: v1
kind: Service
metadata:
  name: gitlab-ssh
  namespace: gitlab
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: nlb
    service.beta.kubernetes.io/aws-load-balancer-nlb-target-type: ip
    service.beta.kubernetes.io/aws-load-balancer-scheme: internet-facing
    service.beta.kubernetes.io/aws-load-balancer-backend-protocol: tcp
    service.beta.kubernetes.io/aws-load-balancer-cross-zone-load-balancing-enabled: "true"
spec:
  type: LoadBalancer
  selector:
    app: gitlab
  ports:
  - port: 22
    targetPort: 22
    name: ssh
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: gitlab-ingress
  namespace: gitlab
  # annotations:
  #   nginx.ingress.kubernetes.io/ssl-redirect: "true"
spec:
  ingressClassName: external-nginx
  rules:
  - host: gitlab.volvo-dev.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: gitlab-service
            port:
              number: 80
  # tls:
  # - hosts:
  #   - gitlab.example.com
  #   secretName: gitlab-tls
