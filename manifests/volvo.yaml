apiVersion: apps/v1
kind: Deployment
metadata:
  name: api-server
  namespace: volvo
spec:
  replicas: 1
  selector:
    matchLabels:
      app: api-server
  template:
    metadata:
      labels:
        app: api-server
    spec:
      containers:
        - name: api-server
          image: 242201283818.dkr.ecr.ap-southeast-1.amazonaws.com/staging/api-server:latest
          ports:
            - containerPort: 8080
          envFrom:
            - configMapRef:
                name: staging-configmap
            - secretRef:
                name: staging-secret
---
apiVersion: v1
kind: Service
metadata:
  name: api-server
  namespace: volvo
spec:
  type: ClusterIP
  selector:
    app: api-server
  ports:
  - port: 8080
    targetPort: 8080
    name: http
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: front-end
  namespace: volvo
spec:
  replicas: 1
  selector:
    matchLabels:
      app: front-end
  template:
    metadata:
      labels:
        app: front-end
    spec:
      containers:
        - name: front-end
          image: 242201283818.dkr.ecr.ap-southeast-1.amazonaws.com/staging/front-end:latest
          ports:
            - containerPort: 80
          envFrom:
            - configMapRef:
                name: staging-configmap
            - secretRef:
                name: staging-secret
---
apiVersion: v1
kind: Service
metadata:
  name: front-end
  namespace: volvo
spec:
  type: ClusterIP
  selector:
    app: front-end
  ports:
  - port: 8081
    targetPort: 80
    name: http
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: volvo
  namespace: volvo
spec:
  ingressClassName: external-nginx
  rules:
  - host: api.volvo-dev.example.com
    http:
      paths:
      - path: /api
        pathType: Prefix
        backend:
          service:
            name: api-server
            port:
              number: 8080
      - path: /templates
        pathType: Prefix
        backend:
          service:
            name: api-server
            port:
              number: 8080
  - host: front-end.volvo-dev.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: front-end
            port:
              number: 8081
