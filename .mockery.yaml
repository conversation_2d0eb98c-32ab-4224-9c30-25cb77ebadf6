with-expecter: true
dir: "{{.InterfaceDir}}/mocks"
mockname: "Mock{{.InterfaceName}}"
outpkg: "mocks"
filename: "mock_{{.InterfaceName}}.go"
all: true
packages:
  api-server/cmd/api-server:
  api-server/internal/repositories:
    config:
      recursive: true

  api-server/internal/usecase:
    config:
      recursive: true

  api-server/internal/handlers:
    config:
      recursive: true

  api-server/internal/gateways/aws:
  api-server/internal/gateways/gitlab:
  api-server/internal/gateways/mail:
  api-server/internal/gateways/supabase:
  api-server/pkg/validator:

  github.com/argoproj/argo-workflows/v3/pkg/apiclient:
    config:
      recursive: false
      all: false
      dir: "{{.ConfigDir}}/pkg/argo/mocks"
      mockname: "Mock{{.InterfaceName}}"
      outpkg: "mocks"
      filename: "mock_{{.InterfaceName}}.go"
    interfaces:
      Client:
  github.com/argoproj/argo-workflows/v3/pkg/apiclient/workflow:
    config:
      recursive: false
      all: false
      dir: "{{.ConfigDir}}/pkg/argo/mocks"
      mockname: "Mock{{.InterfaceName}}"
      outpkg: "mocks"
      filename: "mock_{{.InterfaceName}}.go"
    interfaces:
      WorkflowServiceClient:
