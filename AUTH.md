# Supabase Auth

## Supabase Auth Endpoints

[More on Supabase Auth endpoints](https://github.com/supabase/auth?tab=readme-ov-file#endpoints)

### Sign up with email and password
**POST /signup**

Body:
```json
{
  "email": "<EMAIL>",
  "password": "qwe123"
}
```

### Verify with token code
**POST /verify**

Body:
```json
{
  "type": "signup",
  "token": "602144",
  "email": "<EMAIL>"
}
```

### Sign in with email and password
**POST /token?grant_type=password**

Body:
```json
{
  "email": "<EMAIL>",
  "password": "qwe123"
}
```

### Sign in with magiclink
**POST /otp**

Body:
```json
{
  "email": "<EMAIL>",
  "create_user": false
}
```

### Sign in with Azure
**GET /authorize?provider=azure&scopes=email**

Returns:
```text
http://localhost:3000/#access_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.pyfvTevDV7vzKmyF1GJ4NSGxEOkOkQP8ZgPcE2Pcf4k&expires_at=**********&expires_in=3600&provider_token=EwB4A8l6BAAUbDba3x2OMJElkF7gJ4z%2FVbCPEz0AAf4mPGB%2FjpCVAKB3zv1%2BMZPfJ8riBqEHEMLUfdb5%2BaWEZckOuIJYoIMd6uiJa5nOTkSZFq7%2F2EKnSvbbll117fkW5jFKXKK%2FWjWa6UMcJG32WCiYxIbhBocOtdkzcDVsR3CG7dQZutJOWRkJj3%2FqUHRXghb9aDBaYoovGE90fWZMBVZG%2FyKNaG65re6%2BTN5BWWIs0PuS9nxC%2FaFmaRneEjHzzQlnBqe%2FluFuUYQJEjsOjKsbPrBgVCgWfaf%2BLKE3Bt3BeRRkTvaL2GJibSKCCltXxFWovH5b3zwhadB%2Fp%2FSWOcayomMr8HSG4FZSPC7Up5aU%2BGt6%2B76TNLQvsCw%2FDI8QZgAAEBM44Y798u%2FnXIfbiT%2FSMrlAAvtI%2F%2FlikchRIr6tf2t2Vl34362PKT9DrNbng3WATQlMt5HD4YGxfNWFpbEPp3dQk%2Fc3nF5TF6uGf0ezaGSvC49Q0imUV3RE7fZKge28VtlIESgp3Wz1nQDftDwAYS1UCJG%2FwpnUQxsESqVGhaj02qDZQbhRfOOXmy5fx98iSwKLbp%2BWp7ZbT36n%2BdRfhbQCswXeKNbOUS9ipYj6kx4iYq5Eq%2FhYMFNsLkPGHNZCghQYPcrty2z3H%2FwUTAh9n6mGN7Vig%2FWc0Iva1%2Fu390nYhwRODeJ%2F6c8%2FN6pQKwMCFPno7c1hSOrC8eLYOXNdRTOb0yRMRWCF8TSJ6JHcoI4FatktmmLrHvRLZwtfGiMpjF7wcABoWbcwMvvpu2yH47Vr82ZhFeu7I3iUtIVVjTaZJdOt9gRZUDfEErKFkULQcC5luyaNdUoJY%2FdgsaTNjRnhtuHMOe6kXBvJcWcnP2KCzuB4D4X1Stxi%2BSZAhRQj6QK%2BP0KeqIkNxCMtq%2BHcmUqkxFVREVueK5jxCJrRZvGy52rwVhXDEAK80pNNXMLveMyGlpdfWvGR3FxYKJeR%2F14gRajAa2ou%2FR2rlAXHVT05GX%2F93Hb5xaFItUVoOCSdKM5utYQkv00qBJ5v2nUd89NnIlMiX3lTfA%2FlhBo5wF8ul3icr1TUzacOnYGML94a83%2BMi%2FN7L6K0qJFFtFLNmElR20%2F1DEuiaDH91%2FQDGZFjA%2Ffm1tIIHJs7j70BNPxxDjRrsKysr5cldn6FO8ZKBXHsj3cC&refresh_token=H7nxV64aWB4xZdh9L4Tmog&token_type=bearer
```

### Get new access token with refresh token
**POST /token?grant_type=refresh_token**

Body:
```json
{
  "refresh_token": "6wSlFn8fK6MPpMHeE4PZ6w"
}
```

### Logout
**POST /logout**

Header:
```text
"Authorization" : "Bearer eyJhbGciOiJI...M3A90LCkxxtX9oNP9KZO"
```

### Recover
**POST /recover**

Body:
```json
{
  "email": "<EMAIL>"
}
```

### Get user data
**GET /user**

Header:
```text
"Authorization" : "Bearer eyJhbGciOiJI...M3A90LCkxxtX9oNP9KZO"
```

### Update user data
**PUT /user**

Header:
```text
"Authorization" : "Bearer eyJhbGciOiJI...M3A90LCkxxtX9oNP9KZO"
```

Body:
```json
{
  "data": {
    "key": "value",
    "number": 10
  }
}
```

### Change user password
**PUT /user**

Header:
```text
"Authorization" : "Bearer eyJhbGciOiJI...M3A90LCkxxtX9oNP9KZO"
```

Body:
```json
{
  "password": "new_password"
}
```

### Change user email
**PUT /user**

Header:
```text
"Authorization" : "Bearer eyJhbGciOiJI...M3A90LCkxxtX9oNP9KZO"
```

Body:
```json
{
  "email": "<EMAIL>"
}
```
