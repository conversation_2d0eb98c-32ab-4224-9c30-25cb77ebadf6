package configs

import (
	"errors"
	"net/url"
	"strings"

	"github.com/spf13/viper"
	"go.uber.org/zap"

	"api-server/pkg/otelzap"
)

type GlobalConfig struct {
	// database configuration
	// Database contains the configuration for connecting to the PostgreSQL database
	// It includes the database URL, which is used to establish a connection.
	Database *DatabaseConfig

	// gitlab configuration
	// Gitlab contains the configuration for connecting to the Gitlab server
	Gitlab *GitlabConfig

	// supabase server
	// Supabase contains the configuration for connecting to the Supabase server
	Supabase *SupabaseConfig

	// mail
	// Mail contains the configuration for sending emails via SMTP
	Mail *MailConfig

	// argo workflow
	// ArgoWorkflow contains the configuration for connecting to the Argo Workflow server
	ArgoWorkflow *ArgoWorkflowConfig

	// space
	// Space contains the configuration for the space service
	Space *SpaceConfig

	// aws
	// AwsConfig contains the configuration for AWS services, such as S3
	// It includes credentials, region, and bucket information.
	AwsConfig *AWSConfig

	// file
	// FileConfig contains the configuration for file uploads, such as maximum file size
	FileConfig *FileConfig

	// Supabase Auth config
	// Auth contains the configuration for authentication, including JWT secret and password encryption
	Auth *AuthConfig

	// cors
	// Cors contains the configuration for Cross-Origin Resource Sharing (CORS) settings
	Cors []string

	// is the app deployed inside Kubernetes cluster
	// InCluster indicates whether the application is running inside a Kubernetes cluster
	// It is used to determine how to handle AWS credentials and other configurations.
	InCluster bool

	// migrate mode
	// Migrate indicates whether the application should run database migrations on startup
	// This is useful for development and deployment scenarios where the database schema needs to be updated.
	Migrate bool

	// use for email template
	// platform name
	// PlatformName is the name of the platform, used in email templates and other configurations
	PlatformName string

	// platform support email
	// SupportEmail is the email address for platform support, used in email templates and notifications
	// It is typically used to provide users with a way to contact support for assistance.
	SupportEmail string
}

func NewGlobalConfig() *GlobalConfig {
	// Initialize viper to read environment variables
	viper.AutomaticEnv()
	// Set the environment variable prefix for Volvo platform
	viper.SetEnvPrefix("VOLVO")
	// Set default values for environment variables
	viper.SetDefault("PLATFORM_NAME", "VOLVO")
	// Set default support email
	viper.SetDefault("SUPPORT_EMAIL", "<EMAIL>")

	inCluster := viper.GetBool("IN_CLUSTER")

	config := &GlobalConfig{
		Database:     NewDatabaseConfig(),
		Gitlab:       NewGitlabConfig(),
		Auth:         NewAuthConfig(),
		Supabase:     NewSupabaseConfig(),
		Mail:         NewMailConfig(),
		Cors:         NewCORSConfig(),
		ArgoWorkflow: NewArgoWorkflowConfig(),
		Space:        NewSpaceConfig(),
		AwsConfig:    NewAWSConfig(inCluster),
		FileConfig:   NewFileConfig(),
		InCluster:    inCluster,
		Migrate:      viper.GetBool("MIGRATE"),
		PlatformName: viper.GetString("PLATFORM_NAME"),
		SupportEmail: viper.GetString("SUPPORT_EMAIL"),
	}

	err := config.Validate()
	if err != nil {
		otelzap.Logger.Fatal("validate global config", zap.String("error", err.Error()))
	}

	return config
}

func (c *GlobalConfig) Validate() error {
	validatables := []interface {
		Validate() error
	}{
		c.Database,
		c.Gitlab,
		c.Auth,
		c.Supabase,
		c.Mail,
		c.ArgoWorkflow,
		c.Space,
		c.AwsConfig,
		c.FileConfig,
	}

	for _, validatable := range validatables {
		err := validatable.Validate()
		if err != nil {
			return err
		}
	}

	return nil
}

type DatabaseConfig struct {
	// postgresql url
	Url string `mapstructure:"POSTGRESQL_URL"`
}

func NewDatabaseConfig() *DatabaseConfig {
	config := &DatabaseConfig{}

	err := viper.BindEnv("POSTGRESQL_URL")
	if err != nil {
		otelzap.Logger.Fatal("bind POSTGRESQL_URL env", zap.String("error", err.Error()))
	}

	err = viper.Unmarshal(&config)
	if err != nil {
		otelzap.Logger.Fatal("unmarshal database config", zap.String("error", err.Error()))
	}

	return config
}

func (c *DatabaseConfig) Validate() error {
	if c.Url == "" {
		return errors.New("POSTGRESQL_URL is empty")
	}

	return nil
}

type GitlabConfig struct {
	// gitlab host
	Host string `mapstructure:"GITLAB_HOST"`

	// SSH host for Volvo platform
	CustomSSHHost string `mapstructure:"CUSTOM_GITLAB_SSH_HOST"`

	// Volvo API server host to create Gitlab webhook
	VolvoApiServerHost string `mapstructure:"GITLAB_VOLVO_API_SERVER_HOST"`
}

func NewGitlabConfig() *GitlabConfig {
	config := &GitlabConfig{}

	err := viper.BindEnv("GITLAB_HOST")
	if err != nil {
		otelzap.Logger.Fatal("bind GITLAB_HOST env", zap.String("error", err.Error()))
	}

	err = viper.BindEnv("CUSTOM_GITLAB_SSH_HOST")
	if err != nil {
		otelzap.Logger.Fatal("bind CUSTOM_GITLAB_SSH_HOST env", zap.String("error", err.Error()))
	}

	err = viper.BindEnv("GITLAB_VOLVO_API_SERVER_HOST")
	if err != nil {
		otelzap.Logger.Fatal("bind GITLAB_VOLVO_API_SERVER_HOST env", zap.String("error", err.Error()))
	}

	err = viper.Unmarshal(&config)
	if err != nil {
		otelzap.Logger.Fatal("unmarshal gitlab config", zap.String("error", err.Error()))
	}

	return config
}

func (c *GitlabConfig) Validate() error {
	if c.Host == "" {
		return errors.New("GITLAB_HOST is empty")
	}

	if c.CustomSSHHost == "" {
		return errors.New("CUSTOM_GITLAB_SSH_HOST is empty")
	}

	if c.VolvoApiServerHost == "" {
		return errors.New("GITLAB_VOLVO_API_SERVER_HOST is empty")
	}

	return nil
}

type AuthConfig struct {
	// must be identical to Supabase Auth JWT secret
	JwtSecret string `mapstructure:"AUTH_JWT_SECRET"`
	// secret key for encrypting password string in our database
	PasswordEncryptSecret string `mapstructure:"AUTH_PWD_SECRET"`
}

func NewAuthConfig() *AuthConfig {
	config := &AuthConfig{}

	err := viper.BindEnv("AUTH_JWT_SECRET")
	if err != nil {
		otelzap.Logger.Fatal("bind AUTH_JWT_SECRET env", zap.String("error", err.Error()))
	}

	err = viper.BindEnv("AUTH_PWD_SECRET")
	if err != nil {
		otelzap.Logger.Fatal("bind AUTH_PWD_SECRET env", zap.String("error", err.Error()))
	}

	err = viper.Unmarshal(&config)
	if err != nil {
		otelzap.Logger.Fatal("unmarshal auth config", zap.String("error", err.Error()))
	}

	return config
}

func (c *AuthConfig) Validate() error {
	if len(c.JwtSecret) == 0 {
		return errors.New("AUTH_JWT_SECRET is empty")
	}

	if len(c.PasswordEncryptSecret) == 0 {
		return errors.New("AUTH_PWD_SECRET is empty")
	}

	return nil
}

type SupabaseConfig struct {
	// Supabase host
	Host string `mapstructure:"SUPABASE_HOST"`
	// Supabase service role key
	ServiceToken string `mapstructure:"SERVICE_ROLE_KEY"`
	// URL to redirect users after they click the invitation link
	InviteRedirectToURL string `mapstructure:"SUPABASE_INVITE_REDIRECT_URL"`
}

func NewSupabaseConfig() *SupabaseConfig {
	config := &SupabaseConfig{}

	err := viper.BindEnv("SUPABASE_HOST")
	if err != nil {
		otelzap.Logger.Fatal("bind SUPABASE_HOST env", zap.String("error", err.Error()))
	}

	err = viper.BindEnv("SERVICE_ROLE_KEY")
	if err != nil {
		otelzap.Logger.Fatal("bind SERVICE_ROLE_KEY env", zap.String("error", err.Error()))
	}

	err = viper.BindEnv("SUPABASE_INVITE_REDIRECT_URL")
	if err != nil {
		otelzap.Logger.Fatal("bind SUPABASE_INVITE_REDIRECT_URL env", zap.String("error", err.Error()))
	}

	err = viper.Unmarshal(&config)
	if err != nil {
		otelzap.Logger.Fatal("unmarshal supabase config", zap.String("error", err.Error()))
	}

	return config
}

func (c *SupabaseConfig) Validate() error {
	if c.Host == "" {
		return errors.New("SUPABASE_HOST is empty")
	}

	if c.ServiceToken == "" {
		return errors.New("SERVICE_ROLE_KEY is empty")
	}

	_, err := url.ParseRequestURI(c.InviteRedirectToURL)
	if err != nil {
		return errors.New("SUPABASE_INVITE_REDIRECT_URL invalid for")
	}

	return nil
}

type MailConfig struct {
	// SMTP server configuration
	Host string `mapstructure:"SMTP_HOST"`
	// SMTP server port
	Port string `mapstructure:"SMTP_PORT"`
	// SMTP username for authentication
	Username string `mapstructure:"SMTP_USER"`
	// SMTP password for authentication
	Password string `mapstructure:"SMTP_PASS"`
	// Email address from which the emails will be sent
	From string `mapstructure:"SMTP_ADMIN_EMAIL"`
}

func NewMailConfig() *MailConfig {
	config := &MailConfig{}

	err := viper.BindEnv("SMTP_HOST")
	if err != nil {
		otelzap.Logger.Fatal("bind SMTP_HOST env", zap.String("error", err.Error()))
	}

	err = viper.BindEnv("SMTP_PORT")
	if err != nil {
		otelzap.Logger.Fatal("bind SMTP_PORT env", zap.String("error", err.Error()))
	}

	err = viper.BindEnv("SMTP_USER")
	if err != nil {
		otelzap.Logger.Fatal("bind SMTP_USER env", zap.String("error", err.Error()))
	}

	err = viper.BindEnv("SMTP_PASS")
	if err != nil {
		otelzap.Logger.Fatal("bind SMTP_PASS env", zap.String("error", err.Error()))
	}

	err = viper.BindEnv("SMTP_ADMIN_EMAIL")
	if err != nil {
		otelzap.Logger.Fatal("bind SMTP_ADMIN_EMAIL env", zap.String("error", err.Error()))
	}

	err = viper.Unmarshal(&config)
	if err != nil {
		otelzap.Logger.Fatal("unmarshal mail config", zap.String("error", err.Error()))
	}

	return config
}

func (c *MailConfig) Validate() error {
	if c.Host == "" {
		return errors.New("SMTP_HOST is empty")
	}

	if c.Port == "" {
		return errors.New("SMTP_PORT is empty")
	}

	if c.Username == "" {
		return errors.New("SMTP_USER is empty")
	}

	if c.Password == "" {
		return errors.New("SMTP_PASS is empty")
	}

	if c.From == "" {
		return errors.New("SMTP_ADMIN_EMAIL is empty")
	}

	return nil
}

type CorsConfig struct {
	// CORS_ALLOWED_ORIGINS is a comma-separated list of allowed origins for CORS requests.
	CorsAllowedOrigins string `mapstructure:"CORS_ALLOWED_ORIGINS"`
}

// NewCORSConfig retrieves the CORS allowed origins configuration from the environment
// and returns them as a slice of strings. If no configuration is specified,
// it defaults to allowing all origins by returning a slice containing a single wildcard origin ("*")
//
// Returns:[]string: A slice of allowed origins for CORS
//
// Example:
//
//	If CORS_ALLOWED_ORIGINS="https://example.com,https://another.com"
//		the function will return a slice: ["https://example.com", "https://another.com"]
//	If the CORS_ALLOWED_ORIGINS variable is not specified
//		the function will return a slice with a single element: ["*"]
func NewCORSConfig() []string {
	config := &CorsConfig{}
	err := viper.BindEnv("CORS_ALLOWED_ORIGINS")
	if err != nil {
		otelzap.Logger.Fatal("bind CORS_ALLOWED_ORIGINS env", zap.String("error", err.Error()))
	}

	err = viper.Unmarshal(&config)
	if err != nil {
		otelzap.Logger.Fatal("unmarshal cors allowed origins", zap.String("error", err.Error()))
	}

	if config.CorsAllowedOrigins == "" {
		return []string{"*"}
	}

	return strings.Split(config.CorsAllowedOrigins, ",")
}

type ArgoWorkflowConfig struct {
	// argo workflow host
	Host string `mapstructure:"ARGO_WORKFLOW_HOST"`
	// access token for argo workflow
	AccessToken string `mapstructure:"ARGO_WORKFLOW_ACCESS_TOKEN"`
	// space image registry for argo workflow
	SpaceImageRegistry string `mapstructure:"ARGO_WORKFLOW_SPACE_IMAGE_REGISTRY"`
	// volvo api-server url
	ApiServerURL string `mapstructure:"ARGO_WORKFLOW_VOLVO_API_SERVER_URL"`
}

func NewArgoWorkflowConfig() *ArgoWorkflowConfig {
	config := &ArgoWorkflowConfig{}

	err := viper.BindEnv("ARGO_WORKFLOW_HOST")
	if err != nil {
		otelzap.Logger.Fatal("bind ARGO_WORKFLOW_HOST env", zap.String("error", err.Error()))
	}

	err = viper.BindEnv("ARGO_WORKFLOW_ACCESS_TOKEN")
	if err != nil {
		otelzap.Logger.Fatal("bind ARGO_WORKFLOW_ACCESS_TOKEN env", zap.String("error", err.Error()))
	}

	err = viper.BindEnv("ARGO_WORKFLOW_SPACE_IMAGE_REGISTRY")
	if err != nil {
		otelzap.Logger.Fatal("bind ARGO_WORKFLOW_SPACE_IMAGE_REGISTRY env", zap.String("error", err.Error()))
	}

	err = viper.BindEnv("ARGO_WORKFLOW_VOLVO_API_SERVER_URL")
	if err != nil {
		otelzap.Logger.Fatal("bind ARGO_WORKFLOW_VOLVO_API_SERVER_URL env", zap.String("error", err.Error()))
	}

	err = viper.Unmarshal(&config)
	if err != nil {
		otelzap.Logger.Fatal("unmarshal Argo Workflow config", zap.String("error", err.Error()))
	}

	return config
}

func (c *ArgoWorkflowConfig) Validate() error {
	if c.Host == "" {
		return errors.New("ARGO_WORKFLOW_HOST is empty")
	}

	if c.AccessToken == "" {
		return errors.New("ARGO_WORKFLOW_ACCESS_TOKEN is empty")
	}

	if c.SpaceImageRegistry == "" {
		return errors.New("ARGO_WORKFLOW_SPACE_IMAGE_REGISTRY is empty")
	}

	if c.ApiServerURL == "" {
		return errors.New("ARGO_WORKFLOW_VOLVO_API_SERVER_URL is empty")
	}

	return nil
}

type SpaceConfig struct {
	// domain for space
	SpaceDomain string `mapstructure:"SPACE_DOMAIN"`
	// Build context for space, used to build Docker images
	SpaceBuildContext string `mapstructure:"SPACE_BUILD_CONTEXT"`
	// Ingress class name for space
	SpaceIngressClassName string `mapstructure:"SPACE_INGRESS_CLASS_NAME"`
}

func NewSpaceConfig() *SpaceConfig {
	config := &SpaceConfig{}

	err := viper.BindEnv("SPACE_DOMAIN")
	if err != nil {
		otelzap.Logger.Fatal("bind SPACE_DOMAIN env", zap.Error(err))
	}

	err = viper.BindEnv("SPACE_BUILD_CONTEXT")
	if err != nil {
		otelzap.Logger.Fatal("bind SPACE_BUILD_CONTEXT env", zap.Error(err))
	}

	err = viper.BindEnv("SPACE_INGRESS_CLASS_NAME")
	if err != nil {
		otelzap.Logger.Fatal("bind SPACE_INGRESS_CLASS_NAME env", zap.Error(err))
	}

	err = viper.Unmarshal(&config)
	if err != nil {
		otelzap.Logger.Fatal("unmarshal Space config", zap.Error(err))
	}

	return config
}

func (c *SpaceConfig) Validate() error {
	if c.SpaceDomain == "" {
		return errors.New("SPACE_DOMAIN is empty")
	}

	if c.SpaceBuildContext == "" {
		return errors.New("SPACE_BUILD_CONTEXT is empty")
	}

	return nil
}

type AWSConfig struct {
	// InCluster indicates if the application is running inside a Kubernetes cluster
	InCluster bool

	// AWS credentials and configuration
	AccessKey string `mapstructure:"AWS_ACCESS_KEY"`
	// AWS secret key for authentication
	SecretKey string `mapstructure:"AWS_SECRET_KEY"`
	// AWS region where the resources are located
	Region string `mapstructure:"AWS_REGION"`
	// ImageBucket is the name of the S3 bucket used for storing images

	ImageBucket string `mapstructure:"AWS_AVATAR_BUCKET"`
	// RoleName  string `mapstructure:"AWS_ROLE_NAME"`

	// PreSignExpire is the expiration time for pre-signed URLs in seconds
	PreSignExpire int64 `mapstructure:"AWS_PRE_SIGN_EXPIRE"`
}

func NewAWSConfig(inCluster bool) *AWSConfig {
	config := &AWSConfig{InCluster: inCluster}

	if !inCluster {
		err := viper.BindEnv("AWS_ACCESS_KEY")
		if err != nil {
			otelzap.Logger.Fatal("bind AWS_ACCESS_KEY env", zap.Error(err))
		}

		err = viper.BindEnv("AWS_SECRET_KEY")
		if err != nil {
			otelzap.Logger.Fatal("bind AWS_SECRET_KEY env", zap.Error(err))
		}
	}

	err := viper.BindEnv("AWS_REGION")
	if err != nil {
		otelzap.Logger.Fatal("bind AWS_REGION env", zap.Error(err))
	}

	err = viper.BindEnv("AWS_AVATAR_BUCKET")
	if err != nil {
		otelzap.Logger.Fatal("bind AWS_AVATAR_BUCKET env", zap.Error(err))
	}

	err = viper.BindEnv("AWS_PRE_SIGN_EXPIRE")
	if err != nil {
		otelzap.Logger.Fatal("bind AWS_PRE_SIGN_EXPIRE env", zap.Error(err))
	}

	// err = viper.BindEnv("AWS_ECR_REPOSITORY_NAMESPACE")
	// if err != nil {
	// 	otelzap.Logger.Fatal("bind AWS_ECR_REPOSITORY_NAMESPACE env", zap.Error(err))
	// }
	//
	// err = viper.BindEnv("AWS_ACCOUNT_ID")
	// if err != nil {
	// 	otelzap.Logger.Fatal("bind AWS_ACCOUNT_ID env", zap.Error(err))
	// }

	// err = viper.BindEnv("AWS_ROLE_NAME")
	// if err != nil {
	// 	otelzap.Logger.Fatal("bind AWS_ROLE_NAME env", zap.Error(err))
	// }

	err = viper.Unmarshal(&config)
	if err != nil {
		otelzap.Logger.Fatal("unmarshal AWS config", zap.Error(err))
	}

	return config
}

func (c *AWSConfig) Validate() error {
	if !c.InCluster {
		if c.AccessKey == "" {
			return errors.New("AWS_ACCESS_KEY is empty")
		}

		if c.SecretKey == "" {
			return errors.New("AWS_SECRET_KEY is empty")
		}
	}

	if c.Region == "" {
		return errors.New("AWS_REGION is empty")
	}

	if c.ImageBucket == "" {
		return errors.New("AWS_AVATAR_BUCKET is empty")
	}

	if c.PreSignExpire == 0 {
		return errors.New("AWS_PRE_SIGN_EXPIRE is empty")
	}

	// if c.EcrRepositoryNamespace == "" {
	// 	return errors.New("AWS_ECR_REPOSITORY_NAMESPACE is empty")
	// }
	//
	// if c.AccountID == "" {
	// 	return errors.New("AWS_ACCOUNT_ID is empty")
	// }

	// if c.RoleName == "" {
	// 	return errors.New("AWS_ROLE_NAME is empty")
	// }

	return nil
}

type FileConfig struct {
	// ImageMaxSize is the maximum allowed size for uploaded images in bytes
	ImageMaxSize int64 `mapstructure:"IMAGE_MAX_SIZE"`
}

func NewFileConfig() *FileConfig {
	config := &FileConfig{}

	err := viper.BindEnv("IMAGE_MAX_SIZE")
	if err != nil {
		otelzap.Logger.Fatal("bind IMAGE_MAX_SIZE env", zap.String("error", err.Error()))
	}

	err = viper.Unmarshal(&config)
	if err != nil {
		otelzap.Logger.Fatal("unmarshal AWS config", zap.String("error", err.Error()))
	}

	return config
}

func (c *FileConfig) Validate() error {
	if c.ImageMaxSize == 0 {
		return errors.New("IMAGE_MAX_SIZE is empty")
	}

	return nil
}
