package dto

import (
	"fmt"
	"time"

	"github.com/google/uuid"
	corev1 "k8s.io/api/core/v1"

	"api-server/internal/entities"
	"api-server/internal/enums"
	"api-server/internal/types"
)

// type DeploymentCreateRequest struct {
// 	MachineID string `json:"machine_id"`
// 	RepoID    int64  `swaggerignore:"true"`
// } // @name DeploymentCreateRequest
//
// type Deployment struct {
// 	Repo      Repository             `json:"repository"`
// 	Status    enums.DeploymentStatus `json:"status,omitempty" binding:"Enum=DeploymentStatus"`
// 	Message   string                 `json:"message,omitempty"`
// 	Machine   Machine                `json:"machine"`
// 	Endpoint  string                 `json:"endpoint,omitempty"`
// 	CreatedAt string                 `json:"created_at"`
// 	CreatedBy string                 `json:"created_by"`
// } // @name Deployment
//
// type DeploymentUpdateConfigRequest struct {
// 	Machine Machine `json:"machine,omitempty"`
// 	RepoID  int64   `swaggerignore:"true"`
// } // @name DeploymentUpdateConfigRequest
//
// type DeploymentUpdateStatusRequest struct {
// 	Status  enums.DeploymentStatus `json:"status" binding:"Enum=DeploymentStatus"`
// 	Message string                 `json:"message,omitempty"`
// 	RepoID  int64                  `swaggerignore:"true"`
// } // @name DeploymentUpdateStatusRequest
//
// type DeploymentGetTaskRequest struct {
// 	Machine Machine `json:"machine"`
// } // @name DeploymentGetTaskRequest

type Deployment struct {
	ID           uuid.UUID        `json:"id"`
	Name         string           `json:"name"`
	URL          string           `json:"url"`
	Status       DeploymentStatus `json:"status"`
	RepoID       types.RepoID     `json:"repo_id"`
	Revision     string           `json:"revision"`
	Commit       string           `json:"commit"`
	CreatedAt    time.Time        `json:"created_at"`
	UpdatedAt    time.Time        `json:"updated_at"`
	WorkflowName string           `json:"workflow_name"`
	Duration     float32          `json:"duration"`
	Hardware     Hardware         `json:"hardware,omitempty"`
	User         *User            `json:"user,omitempty"`
}

var _ FromEntity[entities.Deployment, Deployment] = (*Deployment)(nil)

func (u Deployment) FromEntity(e entities.Deployment) Deployment {
	u.ID = e.ID
	u.Name = e.Name
	u.Status = DeploymentStatus{
		Status: string(e.Status),
	}
	u.Revision = e.Revision
	u.Commit = e.Commit

	if e.Repo != nil && e.Repo.Hardware != nil {
		var hardware Hardware
		hardware = hardware.FromEntity(*e.Repo.Hardware)
		u.Hardware = hardware
	}

	u.CreatedAt = e.CreatedAt
	u.UpdatedAt = e.UpdatedAt
	u.WorkflowName = e.WorkflowName
	u.Duration = e.Duration

	if e.User != nil {
		u.User = &User{}
		user := u.User.FromEntity(*e.User)
		u.User = &user
	} else {
		u.User = &User{
			Name:     "user has been removed",
			Username: "user has been removed",
		}
	}

	if e.Repo != nil {
		var namespace string
		if e.Repo.Org != nil {
			// use e.Org.PathName as namespace
			namespace = e.Repo.Org.PathName
		} else if e.Repo.User != nil {
			// use e.User.Username as namespace
			namespace = e.Repo.User.Username
		}
		u.RepoID = *types.NewRepoID(e.Repo.Type, namespace, e.Repo.Name)
	}

	return u
}

func (u *Deployment) WithDomain(domain string) {
	u.URL = fmt.Sprintf("https://%s.%s", u.Name, domain)
}

type StartDeploymentRequest struct {
	Revision *string `json:"revision"`
}

type StartDeploymentResponse HTTPResponse[Deployment]

type ListDeploymentRequest struct {
	Paginate PaginateRequest
	Keyword  string `form:"keyword"`
	RepoID   uuid.UUID
}

type ListDeploymentResponse HTTPResponse[[]Deployment]

type StopDeploymentRequest struct {
	RepoID *types.RepoID `json:"repo_id" validate:"required"`
}

type UpdateDeploymentStatusRequest struct {
	Name     string                   `json:"name"`
	Status   enums.ArgoWorkflowStatus `json:"status"   binding:"oneof=Pending Running Succeeded Failed Error"`
	Duration float32                  `json:"duration"` // in second
}

type GetDeploymentStatusRequest struct {
	RepoID types.RepoID `json:"repo_id" validate:"required"`
}

type DeploymentStatus struct {
	Status  string  `json:"status"`
	Message *string `json:"message,omitempty"`
	Reason  *string `json:"reason,omitempty"`
}

// mapContainerStatus maps Kubernetes container state to a simplified DTO status.
// It sets the status string and optionally message and reason based on the container state.
//
// Parameters:
//   - containerState: The Kubernetes container state.
//   - status: The DTO deployment status to populate.
func MapContainerStatus(containerState *corev1.ContainerState, status *DeploymentStatus) {
	switch {
	case containerState.Waiting != nil:
		status.Status = "Waiting"
		status.Message = &containerState.Waiting.Message
		status.Reason = &containerState.Waiting.Reason
	case containerState.Running != nil:
		status.Status = "Running"
	case containerState.Terminated != nil:
		status.Status = "Terminated"
		status.Message = &containerState.Terminated.Message
		status.Reason = &containerState.Terminated.Reason
	default:
		status.Status = "Unknown"
	}
}

type GetDeploymentStatusResponse HTTPResponse[DeploymentStatus]

type GetDeploymentLogsRequest struct {
	RepoID types.RepoID `json:"repo_id" validate:"required"`
}

type GetDeploymentLogsResponse struct {
	PodName string `json:"pod_name"`
	Content string `json:"content"`
}

type RestartDeploymentRequest struct {
	RepoID types.RepoID `json:"repo_id"`
}

type TerminateDeploymentRequest struct {
	RepoID *uuid.UUID `json:"repo_id"`
	ID     *types.RepoID
}
