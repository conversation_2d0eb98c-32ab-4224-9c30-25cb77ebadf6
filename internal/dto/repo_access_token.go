package dto

import (
	"time"

	"github.com/google/uuid"

	"api-server/internal/entities"
	"api-server/internal/enums"
	"api-server/internal/types"
)

type RepoAccessToken struct {
	ID          uuid.UUID `json:"id"`
	Name        string    `json:"name"`
	AccessToken string    `json:"access_token,omitempty"`
	Scopes      string    `json:"scopes"`
	Revoked     bool      `json:"revoked"`
	ExpiresAt   time.Time `json:"expires_at"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
} // @name RepoAccessToken

var _ FromEntity[entities.RepoAccessToken, RepoAccessToken] = (*RepoAccessToken)(nil)

func (u RepoAccessToken) FromEntity(e entities.RepoAccessToken) RepoAccessToken {
	u.ID = e.ID
	u.Name = e.Name
	u.Revoked = e.Revoked
	u.ExpiresAt = e.ExpiresAt
	u.Scopes = e.Scopes
	u.CreatedAt = e.CreatedAt
	u.UpdatedAt = e.UpdatedAt

	return u
}

type CreateRepoAccessTokenRequest struct {
	Name      string                       `json:"name" validate:"required,min=1,max=50"`
	Scopes    []enums.RepoAccessTokenScope `json:"scopes" validate:"required,min=1,dive,oneof=api read_api read_repository write_repository"`
	ExpiresAt time.Time                    `json:"expires_at" validate:"required"`
	UserID    uuid.UUID                    `json:"user_id" validate:"required" swaggerignore:"-"`
	RepoID    types.RepoID                 `json:"repo_id" validate:"required" swaggerignore:"true"`
} // @name CreateRepoAccessTokenRequest

type CreateRepoAccessTokenResponse HTTPResponse[RepoAccessToken] // @name CreateRepoAccessTokenResponse

type ListRepoAccessTokenRequest struct {
	UserID   uuid.UUID    `json:"user_id" swaggerignore:"-"`
	RepoID   types.RepoID `json:"repo_id" validate:"required" swaggerignore:"true"`
	Paginate PaginateRequest
} // @name ListRepoAccessTokenRequest

type ListRepoAccessTokenResponse HTTPResponse[[]RepoAccessToken] // @name ListRepoAccessTokenResponse
