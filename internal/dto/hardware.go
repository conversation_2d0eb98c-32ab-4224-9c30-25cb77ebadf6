package dto

import (
	"time"

	"github.com/google/uuid"

	"api-server/internal/entities"
	"api-server/internal/types"
)

type Hardware struct {
	ID        uuid.UUID          `json:"id,omitempty"`
	Name      string             `json:"name"`
	NodeName  string             `json:"node_name,omitempty"`
	CPU       int                `json:"cpu"`
	Mem       types.HardwareMem  `json:"mem"`
	GPUMem    *types.HardwareMem `json:"gpu_mem"`
	GPUModel  string             `json:"gpu_model,omitempty"`
	CreatedAt time.Time          `json:"created_at,omitempty"`
	UpdatedAt time.Time          `json:"updated_at,omitempty"`
}

var _ FromEntity[entities.Hardware, Hardware] = (*Hardware)(nil)

func (u Hardware) FromEntity(e entities.Hardware) Hardware {
	u.ID = e.ID
	u.CPU = e.CPU
	u.Mem = *types.HardwareMem{}.FromMiB(uint(e.Mem))

	if e.GPUMem != nil {
		u.GPUMem = types.HardwareMem{}.FromMiB(uint(*e.GPUMem))
	}

	if e.GPUModel != nil {
		u.GPUModel = *e.GPUModel
		u.Name = *e.GPUModel
	} else {
		u.Name = e.Name
	}
	u.NodeName = e.Name

	u.CreatedAt = e.CreatedAt
	u.UpdatedAt = e.UpdatedAt

	return u
}

type ListHardwareRequest struct {
	Paginate PaginateRequest
}

type ListHardwareResponse HTTPResponse[[]Hardware]
