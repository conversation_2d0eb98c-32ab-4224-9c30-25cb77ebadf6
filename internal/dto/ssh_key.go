package dto

import (
	"time"

	"api-server/internal/entities"
	"api-server/internal/enums"

	"github.com/google/uuid"
)

type CreateSSHKeyInput struct {
	UserRefId int64           `json:"user_ref_id" swaggerignore:"true"`
	UserId    uuid.UUID       `json:"user_id" swaggerignore:"true" validate:"required"`
	Title     string          `json:"title" validate:"required,max=100"`
	Key       string          `json:"key" validate:"required,max=1000"`
	ExpiresAt *time.Time      `json:"expires_at"`                     // Expiration date of the SSH key in ISO 8601 format (YYYY-MM-DDTHH:MM:SSZ)
	UsageType enums.UsageType `json:"usage_type" validate:"required"` // Usage type of the SSH key. Possible values are: "auth", "signing", "auth_and_signing". Default: auth_and_signing
} // @name CreateSSHKeyInput

type SSHKey struct {
	Id             uuid.UUID       `json:"id"`
	Title          string          `json:"title" required:"true"`
	Key            string          `json:"key" required:"true"`
	ExpiresAt      *time.Time      `json:"expires_at,omitempty"` // Expiration date of the SSH key in ISO 8601 format (YYYY-MM-DDTHH:MM:SSZ)
	UsageType      enums.UsageType `json:"usage_type"`           // Usage type of the SSH key. Possible values are: "auth", "signing", "auth_and_signing". Default: auth_and_signing
	RefGitSSHKeyID int64           `json:"ref_git_ssh_key_id"`
	CreatedAt      time.Time       `json:"created_at"`
} // @name SSHKey

var _ FromEntity[entities.SSHKey, SSHKey] = (*SSHKey)(nil)

func (u SSHKey) FromEntity(e entities.SSHKey) SSHKey {
	u.Id = e.ID
	u.Title = e.Name
	u.Key = e.PublicKey

	if e.ExpiresAt != nil {
		u.ExpiresAt = e.ExpiresAt
	} else {
		u.ExpiresAt = nil
	}

	u.RefGitSSHKeyID = e.RefGitSSHKeyID
	u.UsageType = e.UsageType
	u.CreatedAt = e.CreatedAt

	return u
}

type GetSSHKeyInput struct {
	UserId   uuid.UUID       `json:"user_id" required:"true" swaggerignore:"true" validate:"required"`
	Paginate PaginateRequest `json:"paginate"`
} // @name GetSSHKeyInput

type GetSSHKeyOutput HTTPResponse[[]SSHKey] // @name GetSSHKeyOutput

type DeleteSSHKeyInput struct {
	Id     uuid.UUID `json:"id" swaggerignore:"true" validate:"required"`
	UserId uuid.UUID `json:"user_id" swaggerignore:"true" validate:"required"`
} // @name DeleteSSHKeyInput

type GetSingleSSHKeyInput struct {
	Id     uuid.UUID `json:"id" swaggerignore:"true" validate:"required"`
	UserId uuid.UUID `json:"user_id" swaggerignore:"true" validate:"required"`
} // @name DeleteSSHKeyInput

type GetSingleSSHKeyOutput struct {
	Id        uuid.UUID       `json:"id"`
	UserId    uuid.UUID       `json:"user_id"`
	Title     string          `json:"title"`
	PublicKey string          `json:"key"`
	UsageType enums.UsageType `json:"usage_type"`
	ExpiresAt *time.Time      `json:"expires_at,omitempty"`
	CreatedAt time.Time       `json:"created_at"`
	UpdatedAt time.Time       `json:"updated_at"`
} // @name GetSingleSSHKeyOutput
