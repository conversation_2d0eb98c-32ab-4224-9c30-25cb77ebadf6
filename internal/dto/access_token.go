package dto

import (
	"time"

	"github.com/google/uuid"

	"api-server/internal/entities"
	"api-server/internal/enums"
)

type AccessToken struct {
	ID          uuid.UUID  `json:"id"`
	Name        string     `json:"name"`
	AccessToken string     `json:"access_token,omitempty"`
	Scopes      string     `json:"scopes"`
	Revoked     bool       `json:"revoked"`
	ExpiresAt   *time.Time `json:"expires_at"`
	CreatedAt   time.Time  `json:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at"`
} // @name AccessToken

var _ FromEntity[entities.UserToken, AccessToken] = (*AccessToken)(nil)

func (u AccessToken) FromEntity(e entities.UserToken) AccessToken {
	u.ID = e.ID
	u.Name = e.Name
	u.Scopes = e.Scopes
	u.Revoked = e.Revoked
	u.ExpiresAt = e.ExpiresAt
	u.CreatedAt = e.CreatedAt
	u.UpdatedAt = e.UpdatedAt

	return u
}

type CreateAccessTokenRequest struct {
	Name      string                       `json:"name" validate:"required,max=100"`
	Scopes    []enums.RepoAccessTokenScope `json:"scopes" validate:"required,min=1,dive,oneof=api read_api read_repository write_repository"`
	ExpiresAt *time.Time                   `json:"expires_at"`
	UserID    uuid.UUID                    `json:"user_id" swaggerignore:"true" validate:"required"`
} // @name CreateAccessTokenRequest

type CreateAccessTokenResponse HTTPResponse[AccessToken] // @name CreateAccessTokenResponse

type ListAccessTokenRequest struct {
	UserID   uuid.UUID `json:"user_id" swaggerignore:"true" validate:"required"`
	Paginate PaginateRequest
} // @name ListAccessTokenRequest

type ListAccessTokenResponse HTTPResponse[[]AccessToken] // @name ListAccessTokenResponse

type VerifyAccessTokenRequest struct {
	AccessToken string `json:"access_token" binding:"required,max=1024"`
} // @name VerifyAccessTokenRequest

type VerifyAccessTokenResponse struct {
	Valid bool `json:"valid"`
} // @name VerifyAccessTokenResponse

type VerifyAccessTokenHTTPResponse HTTPResponse[VerifyAccessTokenResponse] // @name VerifyAccessTokenHTTPResponse
