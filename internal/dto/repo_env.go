package dto

import (
	"encoding/json"

	"github.com/google/uuid"

	"api-server/internal/entities"
)

type RepoEnv struct {
	RepoID uuid.UUID `json:"repo_id"`
	Envs   []EnvKV   `json:"envs"`
	// CreatedAt time.Time `json:"created_at"`
	// UpdatedAt time.Time `json:"updated_at"`
}

func (r RepoEnv) FromEntity(e entities.RepoEnv) RepoEnv {
	r.RepoID = e.RepoID

	var envMap map[string]string
	_ = json.Unmarshal(e.Env, &envMap)
	envs := []EnvKV{}
	for k, v := range envMap {
		envs = append(envs, EnvKV{
			Key:   k,
			Value: v,
		})
	}
	r.Envs = envs

	// r.CreatedAt = e.CreatedAt
	// r.UpdatedAt = e.UpdatedAt

	return r
}

type EnvKV struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}

type GetRepositoryEnvResponse HTTPResponse[RepoEnv] // @name GetRepositoryEnvResponse

type CreateRepositoryEnvRequest struct {
	Key   string  `json:"key" validate:"required,min=1,max=50,alphanumericUnderscore"`
	Value *string `json:"value" validate:"required,min=1"`
} // @name CreateRepositoryEnvRequest

type BulkCreateRepositoryEnvRequest struct {
	Envs []CreateRepositoryEnvRequest `json:"envs" validate:"required,min=1,max=100,dive"`
} // @name BulkCreateRepositoryEnvRequest

type UpdateRepositoryEnvRequest struct {
	OldKey string  `json:"old_key" validate:"required,min=1,max=50,alphanumericUnderscore"`
	Key    string  `json:"key" validate:"required,min=1,max=50,alphanumericUnderscore"`
	Value  *string `json:"value" validate:"required,min=1"`
} // @name UpdateRepositoryEnvRequest

type DeleteRepositoryEnvRequest struct {
	Key string `form:"key" validate:"required,min=1,max=50,alphanumericUnderscore"`
} // @name DeleteRepositoryEnvRequest
