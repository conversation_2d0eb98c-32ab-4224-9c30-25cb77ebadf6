package dto

import (
	"mime/multipart"

	"github.com/google/uuid"

	"api-server/internal/entities"
	"api-server/internal/enums"
)

type UserUpdateRoleRequest struct {
	Role   enums.UserRole `json:"role" binding:"Enum=UserRole"`
	UserID string         `swaggerignore:"true"`
} // @name UserUpdateRoleRequest

type UserInviteRequest struct {
	Email  string `json:"email"`
	UserID string `swaggerignore:"true"`
} // @name UserInviteRequest

type User struct {
	ID                string           `json:"id"`
	Role              enums.UserRole   `json:"role" binding:"Enum=UserRole"`
	Email             string           `json:"email"`
	Name              string           `json:"name"`
	Username          string           `json:"username"`
	UserStatus        enums.UserStatus `json:"user_status"`
	Avatar            *string          `json:"avatar"`
	GitlabAccessToken *string          `json:"-"`
} // @name User

type ListUsersInput struct {
	Paginate PaginateRequest
	Keyword  string `form:"keyword"`
	Except   ExceptFilter
}

type ExceptFilter struct {
	NotInRepo  *string    `form:"not_in_repo"`
	NotInOrgId *uuid.UUID `json:"not_in_org"`
}

type ListUsersOutput HTTPResponse[[]User]

func (u User) FromEntity(e entities.User) User {
	u.ID = e.ID.String()
	u.Name = e.Name
	u.Email = e.Email
	u.Username = e.Username
	u.Role = e.Role
	u.UserStatus = e.Status
	u.Avatar = e.Avatar
	u.GitlabAccessToken = e.GitlabAccessToken
	return u
}

type UpdateRoleInput struct {
	UserID uuid.UUID      `json:"user_id" validate:"required"`
	Role   enums.UserRole `json:"role" validate:"required,oneof=admin user guest"`
}

type AuthUserFilter struct {
	Email *string
	Id    *uuid.UUID
}

type DeleteUserInput struct {
	UserID string `swaggerignore:"true"`
}

type UploadUserAvatarInput struct {
	UserId uuid.UUID             `json:"current_user_id" swaggerignore:"true"`
	File   *multipart.FileHeader `json:"file" required:"true" binding:"required"`
} // @name UploadUserAvatarInput

type ChangePasswordInput struct {
	CurrentPassword string `json:"current_password" validate:"required,min=8"`
	NewPassword     string `json:"new_password" validate:"required,min=8"`
}
