package dto

import (
	"encoding/json"
	"mime/multipart"
	"strings"
	"time"

	"github.com/google/uuid"

	"api-server/internal/entities"
	"api-server/internal/enums"
	"api-server/internal/gateways/gitlab"
	"api-server/internal/types"
)

// type ListMachinesResponse HTTPResponse[[]types.SpaceHardware]

type RepositoryOwner struct {
	ID     uuid.UUID                 `json:"id"    validate:"required"`
	Name   string                    `json:"name"`
	Path   string                    `json:"path"`
	Type   enums.RepositoryOwnerType `json:"type"  validate:"required"`
	Email  *string                   `json:"email,omitempty"`
	Avatar *string                   `json:"avatar,omitempty"`
}

type RepositoryCreateInput struct {
	Name          string                  `json:"name"            validate:"required,repoName,gitlabReservedProjects"`
	Type          enums.RepoType          `json:"type"            validate:"required,oneof=spaces models datasets composes"`
	Visibility    enums.RepoVisibility    `json:"visibility"      validate:"required,oneof=private internal"`
	SDKID         *uuid.UUID              `json:"sdk_id"`
	Owner         RepositoryOwner         `json:"owner"           validate:"required"`
	Hardware      RepositoryHardwareInput `json:"hardware"`
	CurrentUserId uuid.UUID               `json:"current_user_id" validate:"required"                                 swaggerignore:"true"`
} // @name RepositoryCreateInput

type RepositoryHardwareInput struct {
	NodeName string             `json:"node_name"`
	Name     string             `json:"name"`
	NumCpu   *uint              `json:"cpu" validate:"omitempty,gt=0"`
	Mem      *types.HardwareMem `json:"mem" validate:"omitempty"`
}

type RepositoryCreateResponse struct {
	ID     uuid.UUID    `json:"uuid"`
	RepoID types.RepoID `json:"id"`
} // @name RepositoryCreateResponse

type RepositoryUpdateRequest struct {
	Visibility enums.RepoVisibility `json:"visibility" binding:"Enum=RepoVisibility"`
} // @name RepositoryUpdateRequest

type Repository struct {
	// ID          string               `json:"id"`
	RepoID     types.RepoID         `json:"id"`
	Name       string               `json:"name"               example:"leaderboard"`
	Avatar     *string              `json:"avatar"`
	Type       enums.RepoType       `json:"type"                                     binding:"Enum=RepoType"`
	Visibility enums.RepoVisibility `json:"visibility"                               binding:"Enum=RepoVisibility"`
	User       *User                `json:"user,omitempty"`
	Org        *Organization        `json:"org,omitempty"`
	Hardware   *Hardware            `json:"hardware,omitempty"`
	CreatedAt  time.Time            `json:"created_at"`
	UpdatedAt  time.Time            `json:"updated_at"`
	Metadata   any                  `json:"-"`
	Tags       []Tag                `json:"tags"`
	Deployment *DeploymentStatus    `json:"deployment_status"`
} // @name Repository

type Tag struct {
	Id        uuid.UUID `json:"id"`
	Type      string    `json:"type"`
	SubType   string    `json:"sub_type"`
	Value     string    `json:"value"`
	Name      string    `json:"name"`
	IconUrl   string    `json:"icon_url"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	Query     string    `json:"query"`
	RepoTypes []string  `json:"repo_types"`
} // @name Tags

func (r Repository) FromEntity(e entities.Repository) Repository {
	// r.ID = e.ID.String()
	r.Name = e.Name
	r.Type = e.Type
	r.Visibility = e.Visibility
	if e.Avatar != nil {
		r.Avatar = e.Avatar
	}

	if e.Org != nil {
		r.Org = &Organization{}
		org := r.Org.FromEntity(*e.Org)
		r.Org = &org

		// use e.Org.PathName as namespace
		r.RepoID = *types.NewRepoID(e.Type, e.Org.PathName, e.Name)
	} else if e.User != nil {
		r.User = &User{}
		user := r.User.FromEntity(*e.User)
		r.User = &user

		// use e.User.Username as namespace
		r.RepoID = *types.NewRepoID(e.Type, e.User.Username, e.Name)
	}

	if e.Hardware != nil {
		var hardware Hardware
		hardware = hardware.FromEntity(*e.Hardware)
		r.Hardware = &hardware
	}

	r.CreatedAt = e.CreatedAt
	r.UpdatedAt = e.UpdatedAt
	r.Metadata = e.Metadata

	return r
}

func (r Tag) FromEntity(e entities.Tag) Tag {
	r.Id = e.ID
	r.IconUrl = e.IconUrl
	r.Name = e.Name
	r.Value = e.Value
	r.SubType = e.SubType
	r.Type = e.Type
	r.CreatedAt = e.CreatedAt
	r.UpdatedAt = e.UpdatedAt
	r.Query = e.Query
	r.RepoTypes = e.RepoTypes

	return r
}

type RepositoryFile struct {
	ID   string `json:"id"`
	Name string `json:"name"`
	Type string `json:"type"`
	Path string `json:"path"`
	Mode string `json:"mode"`

	// additional fields
	LastCommit *RepositoryCommit `json:"last_commit,omitempty"`
} // @name RepositoryFile

func (r RepositoryFile) FromGitlab(data gitlab.RepositoryFile) *RepositoryFile {
	return &RepositoryFile{
		ID:   data.ID,
		Name: data.Name,
		Type: data.Type,
		Path: data.Path,
		Mode: data.Mode,
	}
}

type RepositoryFileContent struct {
	Name          string `json:"name"`
	Path          string `json:"path"`
	Size          string `json:"size"`
	Content       string `json:"content"`
	ContentSha256 string `json:"content_sha256"`
	CommitID      string `json:"commit_id"`
	LastCommitID  string `json:"last_commit_id"`
	Ref           string `json:"ref"`
	Encoding      string `json:"encoding"`
} // @name RepositoryFileContent

type RepositoryBlameFile struct {
	Commit RepositoryCommit `json:"commit"`
	Lines  []string         `json:"lines"`
} // @name RepositoryBlameFile

type RepositoryBranch struct {
	Name      string `json:"name"`
	Protected bool   `json:"protected"`
	Default   bool   `json:"default"`
} // @name RepositoryBranch

type RepositoryCommit struct {
	ID             string        `json:"id"`
	ShortID        string        `json:"short_id,omitempty"`
	CreatedAt      string        `json:"created_at,omitempty"`
	Title          string        `json:"title,omitempty"`
	Message        string        `json:"message"`
	AuthorName     string        `json:"author_name"`
	AuthorEmail    string        `json:"author_email"`
	AuthoredDate   string        `json:"authored_date"`
	CommitterName  string        `json:"committer_name"`
	CommitterEmail string        `json:"committer_email"`
	ParentIDs      []string      `json:"parent_ids"`
	User           User          `json:"user"`
	MergeRequest   *MergeRequest `json:"merge_request,omitempty"`
	Status         *string       `json:"status,omitempty"`
} // @name RepositoryCommit

func (r RepositoryCommit) FromGitlab(data gitlab.RepositoryCommit) *RepositoryCommit {
	return &RepositoryCommit{
		ID:             data.ID,
		ShortID:        data.ShortID,
		CreatedAt:      data.CreatedAt,
		Title:          data.Title,
		Message:        data.Message,
		AuthorName:     data.AuthorName,
		AuthorEmail:    data.AuthorEmail,
		AuthoredDate:   data.AuthoredDate,
		CommitterName:  data.CommitterName,
		CommitterEmail: data.CommitterEmail,
		ParentIDs:      data.ParentIDs,
		Status:         data.Status,
	}
}

type MergeRequest struct {
	ID        int       `json:"id"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	CommitId  string    `json:"commit_id"`
} // @name MergeRequest

type DeleteRepositoryInput struct {
	RepoID        types.RepoID `json:"id"              required:"true" swaggerignore:"true"`
	CurrentUserId uuid.UUID    `json:"current_user_id" required:"true" swaggerignore:"true"`
}

type GetRepositoryInput struct {
	RepoID        types.RepoID `json:"id"              swaggerignore:"true" validate:"required"`
	CurrentUserId uuid.UUID    `json:"current_user_id" swaggerignore:"true" validate:"required"`
} // @name GetRepositoryInput

type GetRepositoryOutput struct {
	// Id            uuid.UUID            `json:"id"`
	RepoID        types.RepoID         `json:"id"`
	UserID        uuid.UUID            `json:"user_id"`
	DefaultBranch string               `json:"default_branch"`
	Owner         RepositoryOwner      `json:"owner"`
	Name          string               `json:"name"`
	Avatar        *string              `json:"avatar,omitempty"`
	Visibility    enums.RepoVisibility `json:"visibility"`
	Type          enums.RepoType       `json:"type"`
	SSHURL        string               `json:"ssh_url"`
	HTTPURL       string               `json:"http_url"`
	Deployment    *Deployment          `json:"deployment"`
	Metadata      json.RawMessage      `json:"-"`
	Tags          []Tag                `json:"tags"`
	Hardware      *Hardware            `json:"hardware,omitempty"`
} // @name GetRepositoryOutput

type GetRepositoriesInput struct {
	Paginate       PaginateRequest
	RepositoryType enums.RepoType `form:"repository_type" json:"repository_type" validate:"omitempty,oneof=spaces models datasets composes"`
	Keyword        string         `form:"keyword"         json:"keyword"         validate:"omitempty,keyword"`
	TagsQuery

	UserID string `form:"user_id"`
	OrgID  string `form:"org_id"`
} // @name GetRepositoriesInput

type TagsQuery struct {
	Tags       string `form:"tags"            json:"tags,omitempty"            validate:"omitempty,maxItems=100"`
	Tasks      string `form:"task_categories" json:"task_categories,omitempty" validate:"omitempty,maxItems=100"`
	Libraries  string `form:"libraries"       json:"libraries,omitempty"       validate:"omitempty,maxItems=100"`
	Languages  string `form:"languages"       json:"languages,omitempty"       validate:"omitempty,maxItems=100"`
	License    string `form:"license"         json:"license,omitempty"         validate:"omitempty,maxItems=100"`
	Other      string `form:"other"           json:"other,omitempty"           validate:"omitempty,maxItems=100"`
	Sizes      string `form:"size_categories" json:"size_categories,omitempty" validate:"omitempty,maxItems=100"`
	Datasets   string `form:"datasets"        json:"datasets,omitempty"        validate:"omitempty,maxItems=100"`
	PrettyName string `form:"pretty_name"     json:"pretty_name,omitempty"     validate:"omitempty"`
}

func (t *TagsQuery) GetValues(input []string) []string {
	result := make([]string, 0, len(input))
	for _, s := range input {
		str := strings.Split(s, ":")
		if len(str) > 1 {
			result = append(result, str[1])
		}
	}

	return result
}

func (t *TagsQuery) GetValue(input string) string {
	str := strings.Split(input, ":")
	if len(str) > 1 {
		return str[1]
	}

	return ""
}

type GetFileFromRepositoryInput struct {
	LFS                  *bool        `json:"lfs"             form:"lfs"`
	Path                 string       `json:"path"            form:"path" validate:"required"`
	Ref                  string       `json:"ref"             form:"ref"`
	RepoID               types.RepoID `json:"repo_id"                     validate:"required" swaggerignore:"true"`
	CurrentUserId        uuid.UUID    `json:"current_user_id"             swaggerignore:"true"`
	FileContentLimitSize *int64       `form:"content_limit_size" validate:"omitempty,min=0"`
}

type GetFileFromRepositoryOutput struct {
	Content       string           `json:"content"`
	FileName      string           `json:"file_name"`
	FilePath      string           `json:"file_path"`
	Size          int64            `json:"size"`
	Encoding      string           `json:"encoding"`
	ContentSHA256 string           `json:"content_sha256"`
	Ref           string           `json:"ref"`
	BlobId        string           `json:"blob_id"`
	CommitId      string           `json:"commit_id"`
	LastCommitId  string           `json:"last_commit_id"`
	LastCommit    RepositoryCommit `json:"last_commit"`
}

type CheckExistRepositoryInput struct {
	Name string `json:"name"`
}
type GetRepositoriesOutput HTTPResponse[[]Repository]

var _ FromEntity[entities.Repository, Repository] = (*Repository)(nil)

type UploadRepositoryAvatarInput struct {
	RepoID        types.RepoID          `json:"repo_id"         required:"true" swaggerignore:"true" validate:"required"`
	CurrentUserId uuid.UUID             `json:"current_user_id"                 swaggerignore:"true" validate:"required"`
	File          *multipart.FileHeader `json:"file"            required:"true"                      validate:"required" binding:"required"`
}

type DeleteRepositoryAvatarInput struct {
	RepoID        types.RepoID `json:"repo_id"         required:"true" swaggerignore:"true" validate:"required"`
	CurrentUserId uuid.UUID    `json:"current_user_id"                 swaggerignore:"true" validate:"required"`
}

type GetRepositoryBranchesInput struct {
	RepoID        types.RepoID `json:"id"              swaggerignore:"true" validate:"required"`
	CurrentUserId uuid.UUID    `json:"current_user_id" swaggerignore:"true" validate:"required" required:"true"`
} // @name GetRepositoryBranchesInput

type GetSingleRepositoryBranchInput struct {
	Branch        string       `json:"branch" uri:"branch" required:"true" validate:"required"`
	RepoID        types.RepoID `                           required:"true" validate:"required" swaggerignore:"true"`
	CurrentUserId uuid.UUID    `                           required:"true" swaggerignore:"true"`
} // @name GetRepositoryBranchesInput

type RepositoryBranchInfo struct {
	RepositoryBranch
	Commit RepositoryCommit `json:"commit"`
} // @name RepositoryBranchInfo

func (r RepositoryBranchInfo) FromGitlab(data gitlab.RepositoryBranchInfo) *RepositoryBranchInfo {
	return &RepositoryBranchInfo{
		RepositoryBranch: RepositoryBranch{
			Name:      data.Name,
			Protected: data.Protected,
			Default:   data.Default,
		},
		Commit: RepositoryCommit{
			ID:             data.Commit.ID,
			ShortID:        data.Commit.ShortID,
			CreatedAt:      data.Commit.CreatedAt,
			Title:          data.Commit.Title,
			Message:        data.Commit.Message,
			AuthorName:     data.Commit.AuthorName,
			AuthorEmail:    data.Commit.AuthorEmail,
			AuthoredDate:   data.Commit.AuthoredDate,
			CommitterName:  data.Commit.CommitterName,
			CommitterEmail: data.Commit.CommitterEmail,
			ParentIDs:      data.Commit.ParentIDs,
			Status:         data.Commit.Status,
		},
	}
}

type GetRepositoryFilesInput struct {
	Ref       string  `form:"ref"`
	Path      string  `form:"path"`
	Recursive bool    `form:"recursive"`
	Short     bool    `form:"short"`      // return data without last_commit field
	All       bool    `form:"all"`        // return all data without key-set pagination
	PageToken *string `form:"page_token"` // page token for key-set pagination
	PerPage   int     `form:"per_page,default=20" validate:"max=100"`

	RepoID        types.RepoID `json:"repo_id"         swaggerignore:"true" validate:"required"`
	CurrentUserId uuid.UUID    `json:"current_user_id" swaggerignore:"true" validate:"required"`
}

type GetRepositoryCommitsInput struct {
	Paginate struct {
		Page    int `json:"page" form:"page,default=1" binding:"min=1"`
		PerPage int `json:"per_page" form:"per_page,default=20" binding:"min=1,max=100"`
	}
	Ref           string       `form:"ref"`
	Path          string       `form:"path"`
	RepoID        types.RepoID `            json:"repo_id"         swaggerignore:"true" validate:"required"`
	CurrentUserId uuid.UUID    `            json:"current_user_id" swaggerignore:"true" validate:"required"`
}

type GetRepositoryCommitsOutput HTTPResponse[[]RepositoryCommit]

type RepositoryContributor struct {
	Name    string  `json:"name"`
	Email   string  `json:"email"`
	Commits int     `json:"commits"`
	Avatar  *string `json:"avatar,omitempty"`
} // @name RepositoryContributor

func (r RepositoryContributor) FromGitlab(
	data gitlab.RepositoryContributor,
) *RepositoryContributor {
	return &RepositoryContributor{
		Name:    data.Name,
		Email:   data.Email,
		Commits: data.Commits,
		Avatar:  data.Avatar,
	}
}

type GetRepositoryContributorsInput struct {
	Ref           string       `form:"ref"`
	RepoID        types.RepoID `           json:"repo_id"         swaggerignore:"true" validate:"required"`
	CurrentUserId uuid.UUID    `           json:"current_user_id" swaggerignore:"true" validate:"required"`
}

type ListRepositoryMembersInput struct {
	Paginate      PaginateRequest
	RepoID        types.RepoID `json:"repo_id"         swaggerignore:"true" validate:"required"`
	Keyword       string       `json:"keyword"                              validate:"omitempty,keyword" form:"keyword"`
	CurrentUserId uuid.UUID    `json:"current_user_id" swaggerignore:"true" validate:"required"`
}

type ListRepositoryMembersOutput HTTPResponse[[]entities.RepoMemberInfo]

type CreateRepositoryCommitInput struct {
	RepoID types.RepoID `json:"repo_id" validate:"required"`
	RepoCommitInput
}

type RepoCommitInput struct {
	Branch        string         `json:"branch"                  validate:"required"`
	CommitMessage string         `json:"commit_message"          validate:"required"`
	Actions       []GitLabAction `json:"actions"                 validate:"required"`
	StartBranch   *string        `json:"start_branch,omitempty"`
	StartSha      *string        `json:"start_sha,omitempty"`
	StartProject  *int           `json:"start_project,omitempty"`
	AuthorEmail   *string        `json:"author_email,omitempty"`
	AuthorName    *string        `json:"author_name,omitempty"`
	Stats         *bool          `json:"stats,omitempty"`
	Force         *bool          `json:"force,omitempty"`
}

type GitLabAction struct {
	Action          string  `json:"action"                     validate:"required,oneof=create update delete move chmod"`
	FilePath        string  `json:"file_path"`
	PreviousPath    *string `json:"previous_path,omitempty"`
	Content         *string `json:"content,omitempty"`
	Encoding        *string `json:"encoding,omitempty"         validate:"oneof=base64 text"`
	ExecuteFileMode *bool   `json:"execute_filemode,omitempty"`
}

type GetMemberRepositoryInput struct {
	RepoID   types.RepoID `json:"repo_id"   swaggerignore:"true" validate:"required"`
	MemberId uuid.UUID    `json:"member_id"                      validate:"required" uri:"org_id" binding:"required"`
} // @name RemoveMemberRepositoryInput

type RemoveMemberRepositoryInput struct {
	RepoID        types.RepoID `json:"repo_id"   swaggerignore:"true" validate:"required"`
	MemberId      uuid.UUID    `json:"member_id"                      validate:"required" uri:"org_id" binding:"required"`
	CurrentUserID uuid.UUID    `json:"user_id"                        validate:"required"`
} // @name RemoveMemberRepositoryInput

type UpdateMemberRepositoryInput struct {
	RepoID        types.RepoID   `json:"repo_id"   swaggerignore:"true" validate:"required"`
	MemberId      uuid.UUID      `json:"member_id" swaggerignore:"true" validate:"required"                       uri:"org_id"`
	Role          enums.RepoRole `json:"role"                           validate:"required,oneof=owner developer"`
	ExpireAt      *time.Time     `json:"expire_at"`
	CurrentUserID uuid.UUID      `json:"user_id"   swaggerignore:"true" validate:"required"`
} // @name UpdateMemberRepositoryInput

type InviteRepoMemberInput struct {
	UserId        uuid.UUID      `json:"user_id"   validate:"required"`
	RepoID        types.RepoID   `json:"repo_id"   validate:"required"                       swaggerignore:"true"`
	Role          enums.RepoRole `json:"role"      validate:"required,oneof=owner developer"`
	ExpireAt      *time.Time     `json:"expire_at"`
	CurrentUserID uuid.UUID      `json:"-"         validate:"required"`
}

type InviteRepoMembersInput struct {
	RepoID        types.RepoID            `json:"repo_id" validate:"required"                   swaggerignore:"true"`
	Members       []RepoMemberInviteInput `json:"members" validate:"required,min=1,max=15,dive"`
	CurrentUserID uuid.UUID               `json:"-"`
}

// RepoMemberInviteInput defines the input for inviting a single member within the bulk invite
type RepoMemberInviteInput struct {
	UserId   uuid.UUID      `json:"user_id"   validate:"required"`
	Role     enums.RepoRole `json:"role"      validate:"required,oneof=owner developer"`
	ExpireAt *time.Time     `json:"expire_at"`
}

type ListRepoTagsInput struct {
	Paginate PaginateRequest
	Keyword  string `form:"keyword,keyword"`
	RepoType string `form:"repo_type"       validate:"oneof=spaces models datasets"`
	Type     string `form:"type"`
	SubType  string `form:"sub_type"`
} // @name ListRepoTagsInput

type ListRepoTagsOutput HTTPResponse[[]Tag]

type GitLabPushEvent struct {
	Ref               string                  `json:"ref"`
	ProjectID         int64                   `json:"project_id"`
	Project           GitlabPustEventProject  `json:"project"`
	TotalCommitsCount int64                   `json:"total_commits_count"`
	Commits           []GitlabPustEventCommit `json:"commits"`
}

type GitlabPustEventProject struct {
	Name              string `json:"name"`
	Namespace         string `json:"namespace"`
	PathWithNamespace string `json:"path_with_namespace"`
	DefaultBranch     string `json:"default_branch"`
}
type GitlabPustEventCommit struct {
	ID        string   `json:"id"`
	Message   string   `json:"message"`
	Timestamp string   `json:"timestamp"`
	Modified  []string `json:"modified"`
	Added     []string `json:"added"`
	Removed   []string `json:"removed"`
}

type UpdateRepositoryInput struct {
	RepoID        types.RepoID             `json:"id"              required:"true" swaggerignore:"true" validate:"required"`
	CurrentUserId uuid.UUID                `json:"current_user_id" required:"true" swaggerignore:"true" validate:"required"`
	Hardware      *RepositoryHardwareInput `json:"hardware"`
} // @name UpdateRepositoryInput

type ArchiveRepositoryInput struct {
	RepoID        types.RepoID `json:"id"              swaggerignore:"true" validate:"required"`
	Type          string       `json:"string"                               validate:"required" form:"type" required:"true"`
	Ref           string       `json:"ref"                                  validate:"required" form:"ref"  required:"true"`
	CurrentUserId uuid.UUID    `json:"current_user_id" swaggerignore:"true" validate:"required"             required:"true"`
} // @name ArchiveRepositoryInput

type CreateRepoTagInput struct {
	Type          string    `json:"type"            required:"true" validate:"required,name"`
	SubType       string    `json:"sub_type"                        validate:"omitempty,name"`
	Name          string    `json:"name"            required:"true" validate:"required,name"`
	IconUrl       string    `json:"icon_url"`
	RepoTypes     []string  `json:"repo_types"                      validate:"required,min=1,dive,oneof=spaces models datasets composes"`
	CurrentUserID uuid.UUID `json:"current_user_id"                 validate:"required"                                         swaggerignore:"true"`
} // @name CreateRepoTagInput

type DeleteRepoTagInput struct {
	Id            uuid.UUID `json:"id"              required:"true" validate:"required"`
	CurrentUserID uuid.UUID `json:"current_user_id"                 validate:"required" swaggerignore:"true"`
} // @name DeleteRepoTagInput

type GetRepoTagInput struct {
	Id            uuid.UUID `json:"id"              required:"true"`
	CurrentUserID uuid.UUID `json:"current_user_id"                 swaggerignore:"true"`
} // @name GetRepoTagInput

type UpdateRepoTagInput struct {
	Id            uuid.UUID `json:"id"              required:"true"`
	Type          string    `json:"type"            required:"true" validate:"required,name"`
	SubType       string    `json:"sub_type"                        validate:"omitempty,name"`
	Name          string    `json:"name"            required:"true" validate:"required,name"`
	IconUrl       string    `json:"icon_url"`
	RepoTypes     []string  `json:"repo_types"      required:"true" validate:"required,min=1,dive,oneof=spaces models datasets composes"`
	CurrentUserID uuid.UUID `json:"current_user_id"                 validate:"required"                                         swaggerignore:"true"`
} // @name CreateRepoTagInput
