package dto

import (
	"errors"
	"net/http"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"api-server/internal/gateways/gitlab"
	"api-server/internal/usecase"
	"api-server/internal/utils"
)

type HTTPError struct {
	Code       int    `json:"code"`
	Message    string `json:"message,omitempty"`
	DebugError error  `json:"-"` // Don't serialize this field
} // @name HTTPError

func (e HTTPError) Error() string {
	return e.Message
}

func NewHTTPError(code int, err error, msg ...string) *HTTPError {
	if len(msg) > 0 {
		return &HTTPError{
			Code:       code,
			Message:    utils.CapitalizeFirst(msg[0]),
			DebugError: err,
		}
	}

	return &HTTPError{
		Code:       code,
		Message:    utils.CapitalizeFirst(err.Error()),
		DebugError: err,
	}
}

func NewInternalError(err error, msg ...string) *HTTPError {
	if len(msg) > 0 {
		return &HTTPError{
			Code:       http.StatusInternalServerError,
			Message:    utils.CapitalizeFirst(msg[0]),
			DebugError: err,
		}
	}

	return &HTTPError{
		Code:       http.StatusInternalServerError,
		Message:    utils.CapitalizeFirst(err.Error()),
		DebugError: err,
	}
}

func NewBadRequestError(err error, msg ...string) *HTTPError {
	if len(msg) > 0 {
		return &HTTPError{
			Code:       http.StatusBadRequest,
			Message:    utils.CapitalizeFirst(msg[0]),
			DebugError: err,
		}
	}

	return &HTTPError{
		Code:       http.StatusBadRequest,
		Message:    utils.CapitalizeFirst(err.Error()),
		DebugError: err,
	}
}

func NewNotFoundError(err error, msg ...string) *HTTPError {
	if len(msg) > 0 {
		return &HTTPError{
			Code:       http.StatusNotFound,
			Message:    utils.CapitalizeFirst(msg[0]),
			DebugError: err,
		}
	}

	return &HTTPError{
		Code:       http.StatusNotFound,
		Message:    utils.CapitalizeFirst(err.Error()),
		DebugError: err,
	}
}

func NewForbiddenError(err error, msg ...string) *HTTPError {
	if len(msg) > 0 {
		return &HTTPError{
			Code:       http.StatusForbidden,
			Message:    utils.CapitalizeFirst(msg[0]),
			DebugError: err,
		}
	}

	return &HTTPError{
		Code:       http.StatusForbidden,
		Message:    utils.CapitalizeFirst(err.Error()),
		DebugError: err,
	}
}

func NewUnauthorizedError(err error, msg ...string) *HTTPError {
	if len(msg) > 0 {
		return &HTTPError{
			Code:       http.StatusUnauthorized,
			Message:    utils.CapitalizeFirst(msg[0]),
			DebugError: err,
		}
	}

	return &HTTPError{
		Code:       http.StatusUnauthorized,
		Message:    utils.CapitalizeFirst(err.Error()),
		DebugError: err,
	}
}

func ErrorResponse(ctx *gin.Context, err error) {
	if errors.Is(err, gorm.ErrRecordNotFound) {
		ctx.AbortWithStatusJSON(http.StatusNotFound, NewNotFoundError(err, "data not found"))
		return
	}

	var e *HTTPError
	ok := errors.As(err, &e)
	if ok {
		ctx.AbortWithStatusJSON(e.Code, e)
		return
	}

	ctx.AbortWithStatusJSON(http.StatusInternalServerError, NewInternalError(err))
}

func NewErrorFromUsecase(e error) *HTTPError {
	switch e {
	case usecase.ErrRecordNotFound, usecase.ErrSignUpRequestNotFound, usecase.ErrUserNotFound,
		usecase.ErrMemberNotFound, usecase.ErrRepositoryNotFound, usecase.ErrOrganizationNotFound,
		gitlab.ErrBranchNotFound, usecase.ErrECRRepositoryNotFound, usecase.ErrECRImageNotFound:
		return NewNotFoundError(e)
	case usecase.ErrInternal, usecase.ErrSendMail, usecase.ErrCreateWorkflow,
		usecase.ErrNoDeployment, usecase.ErrNoPod, usecase.ErrNoContainer,
		usecase.ErrCreateSpaceIngressResource, usecase.ErrGetSpaceIngressResource, usecase.ErrApplySpaceServiceResource:
		return NewInternalError(e)
	case usecase.ErrTimeIsPast, usecase.ErrInvalidTime, usecase.ErrDuplicatedOrganization,
		usecase.ErrRepositoryExist, usecase.ErrEmailExist, usecase.ErrTokenExpired,
		usecase.ErrInvalidToken, usecase.ErrNoHardware, usecase.ErrNotSpaceRepo,
		usecase.ErrCannotInviteUserAsOwner, usecase.ErrEmailHasBeenConfirmed, usecase.ErrEmailWaitAdminApproval,
		usecase.ErrDeploymentRunning, usecase.ErrUserHasBeenInvited, usecase.ErrHardwareNotFound, usecase.ErrDeploymentNotSucceeded,
		usecase.ErrCurrentPasswordIsIncorrect, usecase.ErrGPUNodeNameRequired, usecase.ErrDeploymentNameAlreadyExists,
		usecase.ErrInvalidECRImageURI, usecase.ErrNoImageIdentifierProvided, usecase.ErrNotComposeRepo, usecase.ErrNotSpaceOrComposeRepo,
		usecase.ErrDockerComposeFileNotFound:
		return NewBadRequestError(e)
	case usecase.ErrNoPermission, usecase.ErrCannotDeleteRootUser:
		return NewForbiddenError(e)
	default:
		return NewInternalError(e)
	}
}
