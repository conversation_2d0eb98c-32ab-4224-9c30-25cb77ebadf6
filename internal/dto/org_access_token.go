package dto

import (
	"time"

	"github.com/google/uuid"

	"api-server/internal/entities"
	"api-server/internal/enums"
)

type OrgAccessToken struct {
	ID          uuid.UUID  `json:"id"`
	Name        string     `json:"name"`
	AccessToken string     `json:"access_token,omitempty"`
	Scopes      string     `json:"scopes"`
	Revoked     bool       `json:"revoked"`
	ExpiresAt   *time.Time `json:"expires_at"`
	CreatedAt   time.Time  `json:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at"`
} // @name OrgAccessToken

var _ FromEntity[entities.OrgToken, OrgAccessToken] = (*OrgAccessToken)(nil)

func (u OrgAccessToken) FromEntity(e entities.OrgToken) OrgAccessToken {
	u.ID = e.ID
	u.Name = e.Name
	u.Scopes = e.Scopes
	u.Revoked = e.Revoked
	u.ExpiresAt = e.ExpiresAt
	u.CreatedAt = e.CreatedAt
	u.UpdatedAt = e.UpdatedAt

	return u
}

type CreateOrgAccessTokenRequest struct {
	Name      string                       `json:"name" validate:"required,max=100"`
	Scopes    []enums.RepoAccessTokenScope `json:"scopes" validate:"required,min=1,dive,oneof=api read_api read_repository write_repository"`
	ExpiresAt *time.Time                   `json:"expires_at"`
	OrgID     uuid.UUID                    `json:"org_id" swaggerignore:"true" validate:"required"`
	UserID    uuid.UUID                    `json:"user_id" swaggerignore:"true" validate:"required"`
} // @name CreateOrgAccessTokenRequest

type CreateOrgAccessTokenResponse HTTPResponse[OrgAccessToken] // @name CreateOrgAccessTokenResponse

type ListOrgAccessTokenRequest struct {
	UserID   uuid.UUID `json:"user_id" swaggerignore:"true" validate:"required"`
	OrgID    uuid.UUID `json:"org_id" swaggerignore:"true" validate:"required"`
	Paginate PaginateRequest
} // @name ListOrgAccessTokenRequest

type ListOrgAccessTokenResponse HTTPResponse[[]OrgAccessToken] // @name ListOrgAccessTokenResponse

type VerifyOrgAccessTokenRequest struct {
	OrgAccessToken string `json:"access_token" binding:"required,max=1024"`
} // @name VerifyOrgAccessTokenRequest

type VerifyOrgAccessTokenResponse struct {
	Valid bool `json:"valid"`
} // @name VerifyOrgAccessTokenResponse

type VerifyOrgAccessTokenHTTPResponse HTTPResponse[VerifyOrgAccessTokenResponse] // @name VerifyOrgAccessTokenHTTPResponse
