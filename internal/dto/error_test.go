package dto_test

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"api-server/internal/dto"
	"api-server/internal/gateways/gitlab"
	"api-server/internal/usecase"
)

func TestNewErrorFromUsecase(t *testing.T) {
	tests := []struct {
		name     string
		input    error
		expected *dto.HTTPError
	}{
		{
			name:     "should return NotFound error for usecase.ErrRecordNotFound",
			input:    usecase.ErrRecordNotFound,
			expected: dto.NewNotFoundError(usecase.ErrRecordNotFound),
		},
		{
			name:     "should return NotFound error for gitlab.ErrBranchNotFound",
			input:    gitlab.ErrBranchNotFound,
			expected: dto.NewNotFoundError(gitlab.ErrBranchNotFound),
		},
		{
			name:     "should return Internal error for usecase.ErrInternal",
			input:    usecase.ErrInternal,
			expected: dto.NewInternalError(usecase.ErrInternal),
		},
		{
			name:     "should return BadRequest error for usecase.ErrTimeIsPast",
			input:    usecase.ErrTimeIsPast,
			expected: dto.NewBadRequestError(usecase.ErrTimeIsPast),
		},
		{
			name:     "should return Forbidden error for usecase.ErrNoPermission",
			input:    usecase.ErrNoPermission,
			expected: dto.NewForbiddenError(usecase.ErrNoPermission),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp := dto.NewErrorFromUsecase(tt.input)

			assert.Equal(t, tt.expected, resp)
		})
	}
}
