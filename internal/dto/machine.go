package dto

import (
	"api-server/internal/enums"
	"api-server/internal/types"
	"time"

	"github.com/google/uuid"
)

// type MachineRegistrationRequest struct {
// 	Name   string   `json:"name,omitempty"`
// 	Host   string   `json:"host" example:"hostname or ip address"`
// 	CPU    string   `json:"cpu" example:"2 vCPU"`
// 	Memory string   `json:"memory" example:"8 GB"`
// 	GPU    string   `json:"gpu" example:"A100"`
// 	Tags   []string `json:"tags"`
// } // @name MachineRegistrationRequest
//
// type Machine struct {
// 	Name   string   `json:"name,omitempty"`
// 	Host   string   `json:"host" example:"hostname or ip address"`
// 	CPU    string   `json:"cpu" example:"2 vCPU"`
// 	Memory string   `json:"memory" example:"8 GB"`
// 	GPU    string   `json:"gpu" example:"A100"`
// 	Tags   []string `json:"tags"`
// } // @name Machine

type ListGPUNodesResponse HTTPResponse[[]GPUNode]

type GPUNode struct {
	NodeName    string              `json:"node_name"`
	Name        string              `json:"name"`
	GPUModel    string              `json:"gpu_model,omitempty"`
	GPUCount    int64               `json:"gpu_count,omitempty"`
	GPUMemoryGB int                 `json:"gpu_memory_gb,omitempty"`
	Deployments []DeploymentGPUInfo `json:"deployments,omitempty"`
} // @name GPUNode

type DeploymentGPUInfo struct {
	ID                 *uuid.UUID           `json:"id,omitempty"`
	DeploymentType     enums.DeploymentType `json:"deployment_type"`
	Name               string               `json:"name"` // format: namespace/name
	GPURequest         int64                `json:"gpu_request,omitempty"`
	MemoryBytesRequest int64                `json:"memory_bytes_request,omitempty"` // sum of memory requests
	CPURequest         int64                `json:"cpu_request,omitempty"`          // sum of memory requests
	RepoID             *types.RepoID        `json:"repo_id,omitempty"`
	Status             *DeploymentStatus    `json:"status,omitempty"`
	User               *User                `json:"user,omitempty"`
	UpdatedAt          *time.Time           `json:"updated_at,omitempty"`
} // @name DeploymentGPUInfo
