package dto

import "time"

// KubernetesDeployInput represents input for deploying an ECR image to Kubernetes
type KubernetesDeployInput struct {
	// The namespace to deploy to
	Namespace string `json:"namespace" binding:"required" example:"default"`

	// The name for the deployment and service
	Name string `json:"name" binding:"required" example:"my-app"`

	// The ECR image URI to deploy
	ImageURI string `json:"image_uri" binding:"required" example:"123456789012.dkr.ecr.us-east-1.amazonaws.com/my-repo:latest"`

	// The container port to expose
	Port int32 `json:"port" binding:"required" example:"8080"`

	// Number of replicas to run
	Replicas int32 `json:"replicas" binding:"required,min=1" example:"1"`

	// Environment variables for the container
	Env map[string]string `json:"env,omitempty"`

	// Resource requests and limits
	Resources KubernetesResources `json:"resources,omitempty"`

	// Whether to create a service for the deployment
	CreateService bool `json:"create_service" example:"true"`

	// Type of service to create (ClusterIP, NodePort, LoadBalancer)
	ServiceType string `json:"service_type,omitempty" example:"ClusterIP"`
}

// KubernetesResources defines CPU and memory requests/limits
type KubernetesResources struct {
	RequestsCPU    string `json:"requests_cpu,omitempty" example:"100m"`
	RequestsMemory string `json:"requests_memory,omitempty" example:"128Mi"`
	LimitsCPU      string `json:"limits_cpu,omitempty" example:"200m"`
	LimitsMemory   string `json:"limits_memory,omitempty" example:"256Mi"`
}

// KubernetesDeploymentResult represents the result of a Kubernetes deployment
type KubernetesDeploymentResult struct {
	Namespace        string  `json:"namespace"`
	DeploymentName   string  `json:"deployment_name"`
	ServiceName      string  `json:"service_name,omitempty"`
	ServiceType      string  `json:"service_type,omitempty"`
	ServiceClusterIP string  `json:"service_cluster_ip,omitempty"`
	ServicePorts     []int32 `json:"service_ports,omitempty"`
}

// KubernetesDeploymentStatus represents the status of a Kubernetes deployment
type KubernetesDeploymentStatus struct {
	Namespace           string                          `json:"namespace"`
	DeploymentName      string                          `json:"deployment_name"`
	AvailableReplicas   int32                           `json:"available_replicas"`
	DesiredReplicas     int32                           `json:"desired_replicas"`
	ReadyReplicas       int32                           `json:"ready_replicas"`
	UpdatedReplicas     int32                           `json:"updated_replicas"`
	UnavailableReplicas int32                           `json:"unavailable_replicas"`
	Conditions          []KubernetesDeploymentCondition `json:"conditions,omitempty"`
}

// KubernetesDeploymentCondition represents a condition of a Kubernetes deployment
type KubernetesDeploymentCondition struct {
	Type               string    `json:"type"`
	Status             string    `json:"status"`
	LastUpdateTime     time.Time `json:"last_update_time"`
	LastTransitionTime time.Time `json:"last_transition_time"`
	Reason             string    `json:"reason,omitempty"`
	Message            string    `json:"message,omitempty"`
}

// KubernetesDeploymentDeleteRequest represents a request to delete a Kubernetes deployment
type KubernetesDeploymentDeleteRequest struct {
	Namespace string `form:"namespace" binding:"required" example:"default"`
	Name      string `form:"name" binding:"required" example:"my-app"`
}

// KubernetesDeploymentStatusRequest represents a request to get the status of a Kubernetes deployment
type KubernetesDeploymentStatusRequest struct {
	Namespace string `form:"namespace" binding:"required" example:"default"`
	Name      string `form:"name" binding:"required" example:"my-app"`
}

// KubernetesDeployResultResponse represents the response for deploying to Kubernetes
type KubernetesDeployResultResponse HTTPResponse[KubernetesDeploymentResult]

// KubernetesDeploymentStatusResponse represents the response for getting deployment status
type KubernetesDeploymentStatusResponse HTTPResponse[KubernetesDeploymentStatus]
