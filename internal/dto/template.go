package dto

import (
	"api-server/internal/entities"
	"time"

	"github.com/google/uuid"
)

type RepoTemplate struct {
	ID        uuid.UUID `json:"id"`
	Type      string    `json:"type"`
	Name      string    `json:"name"`
	Icon      string    `json:"icon"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

func (r RepoTemplate) FromEntity(e entities.RepoTemplate) RepoTemplate {
	r.ID = e.ID
	r.Name = e.Name
	r.Type = e.Type
	r.Icon = e.Icon
	r.CreatedAt = e.CreatedAt
	r.UpdatedAt = e.UpdatedAt

	return r
}

type ListRepoTemplatesOutput []RepoTemplate
