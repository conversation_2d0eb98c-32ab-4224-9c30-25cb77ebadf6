package dto

import "api-server/internal/enums"

// PaginateRequest represents a request to paginate a list of items.
type PaginateRequest struct {
	Page    int                    `json:"page" form:"page,default=1" validate:"numeric,min=1" binding:"omitempty,min=1"`
	PerPage int                    `json:"per_page" form:"per_page,default=10" validate:"numeric,min=1,max=100" binding:"omitempty,min=0,max=100"`
	OrderBy enums.OrderByColumn    `json:"order_by" form:"order_by,default=created_at" validate:"oneof=created_at updated_at" binding:"omitempty,oneof=expires_at created_at updated_at"`
	Sort    enums.OrderByDirection `json:"sort" form:"sort,default=desc" validate:"oneof=asc desc" binding:"omitempty,oneof=asc desc"`
} // @name PaginateRequest
