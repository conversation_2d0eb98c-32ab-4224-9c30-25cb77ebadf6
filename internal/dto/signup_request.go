package dto

import (
	"github.com/google/uuid"

	"api-server/internal/entities"
	"api-server/internal/enums"
)

type GetAllSignUpRequestsOutput HTTPResponse[[]SignUpRequest]

type SignUpRequest struct {
	Id    uuid.UUID `json:"id"`
	Email string    `json:"email"`
	Name  string    `json:"name"`
}

type ApprovalRequest struct {
	IsAccepted    *bool     `json:"is_accepted" validate:"required"`
	ID            uuid.UUID `json:"id" validate:"required" swaggerignore:"true"`
	CurrentUserID uuid.UUID `validate:"required" swaggerignore:"true"`
} // @name ApprovalRequest

func (u SignUpRequest) FromEntity(e entities.SignupRequest) SignUpRequest {
	return SignUpRequest{
		Id:    e.ID,
		Email: e.Email,
		Name:  e.Name,
	}
}

type InviteUserInput struct {
	Email         string         `json:"email" validate:"required,email"`
	Name          string         `json:"name" validate:"required,name"`
	Role          enums.UserRole `json:"role" validate:"required,oneof=admin user guest"`
	CurrentUserID uuid.UUID      `json:"current_user_id" validate:"required" swaggerignore:"true"`
}

type SendConfirmEmailInput struct {
	Email string `json:"email" validate:"required,email"`
}
