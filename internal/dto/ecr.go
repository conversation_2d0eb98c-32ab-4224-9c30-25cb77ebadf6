package dto

import (
	"strings"
	"time"

	"github.com/google/uuid"

	"api-server/internal/entities"
	"api-server/internal/enums"
	"api-server/internal/types"
	"api-server/internal/utils"
)

type ECRDeployment struct {
	ID             uuid.UUID  `json:"id"`
	URL            string     `json:"url"`
	PrivateURL     string     `json:"private_url"`
	DeploymentName string     `json:"deployment_name"`
	ImageURI       string     `json:"image_uri"`
	NodeName       string     `json:"node_name"`
	Port           int32      `json:"port"`
	Env            []EnvKV    `json:"env"`
	User           User       `json:"user,omitempty"`
	OrgID          *uuid.UUID `json:"org_id,omitempty"`
	Status         string     `json:"status"`
	Hardware       Hardware   `json:"hardware,omitempty"`
	ProxyBodySize  int        `json:"proxy_body_size"` // in megabytes
	CreatedAt      time.Time  `json:"created_at"`
	UpdatedAt      time.Time  `json:"updated_at"`
}

var _ FromEntity[entities.CustomImageDeployment, ECRDeployment] = (*ECRDeployment)(nil)

func (u ECRDeployment) FromEntity(e entities.CustomImageDeployment) ECRDeployment {
	u.ID = e.ID

	u.DeploymentName = e.DeploymentName
	u.ImageURI = e.ImageURI
	u.NodeName = e.NodeName
	u.Port = e.Port
	u.OrgID = e.OrgID
	u.ProxyBodySize = e.ProxyBodySize

	if e.User != nil {
		var user User
		u.User = user.FromEntity(*e.User)
	}

	u.Hardware = Hardware{
		Name: func() string {
			if strings.ToLower(e.NodeName) != "cpu" {
				if e.GPUModel == nil {
					return ""
				}
				return *e.GPUModel
			}
			return "CPU"
		}(),
		CPU: e.CPU,
		Mem: *types.HardwareMem{}.FromMiB(uint(e.Mem)),
		GPUMem: func() *types.HardwareMem {
			if e.GPUMem == nil {
				return nil
			}
			return types.HardwareMem{}.FromMiB(uint(*e.GPUMem))
		}(),
		GPUModel: func() string {
			if e.GPUModel == nil {
				return ""
			}
			return *e.GPUModel
		}(),
	}

	if e.Namespace != nil {
		u.PrivateURL = utils.GetPrivateDeploymentURL(e.DeploymentName, *e.Namespace)
	}

	u.CreatedAt = e.CreatedAt
	u.UpdatedAt = e.UpdatedAt

	return u
}

// // ECRRepository represents an ECR repository
// type ECRRepository struct {
// 	RepositoryName string    `json:"repository_name"`
// 	RepositoryURI  string    `json:"repository_uri"`
// 	RegistryID     string    `json:"registry_id"`
// 	CreatedAt      time.Time `json:"created_at"`
// }
//
// // ECRRepositoryCreateInput represents the input for creating an ECR repository
// type ECRRepositoryCreateInput struct {
// 	// RepositoryName     string                   `json:"repository_name" binding:"required" example:"my-repo"`
// 	ImageTagMutability enums.ImageTagMutability `json:"image_tag_mutability" binding:"omitempty,oneof=MUTABLE IMMUTABLE" example:"MUTABLE"`
// }
//
// // ECRRepositoriesResponse represents the response for listing ECR repositories
// type ECRRepositoriesResponse HTTPResponse[[]ECRRepository]
//
// // // ECRRepositoryResponse represents the response for getting a single ECR repository
// type ECRRepositoryResponse HTTPResponse[ECRRepository]
//
// // ECRImage represents an ECR image
// type ECRImage struct {
// 	ImageDigest      *string    `json:"image_digest,omitempty"`
// 	RegistryID       *string    `json:"registry_id,omitempty"`
// 	RepositoryName   *string    `json:"repository_name,omitempty"`
// 	ImageTags        []string   `json:"image_tags,omitempty"`
// 	ImageSizeInBytes *int64     `json:"image_size_in_bytes,omitempty"`
// 	PushedAt         *time.Time `json:"pushed_at,omitempty"`
// }
//
// // ECRImagesResponse represents the response for listing ECR images
// type ECRImagesResponse HTTPResponse[[]ECRImage]
//
// // ECRAuthorizationToken represents an ECR authorization token
// type ECRAuthorizationToken struct {
// 	Password           string    `json:"password"`
// 	AuthorizationToken string    `json:"authorization_token"`
// 	ExpiresAt          time.Time `json:"expires_at"`
// 	ProxyEndpoint      string    `json:"proxy_endpoint"`
// }
//
// // ECRAuthorizationTokenResponse represents the response for getting an ECR authorization token
// type ECRAuthorizationTokenResponse HTTPResponse[ECRAuthorizationToken]
//
// type GetECRRepositoryRequest struct {
// 	Type string `form:"type,default=user" binding:"oneof=user org" validate:"oneof=user org"`
// }
//
// type DeleteECRRepositoryRequest struct {
// 	Type string `form:"type,default=user" binding:"oneof=user org" validate:"oneof=user org"`
// }
//
// type ListECRImagesRequest struct {
// 	Type          string  `form:"type,default=user" binding:"oneof=user org" validate:"oneof=user org"`
// 	NextPageToken *string `form:"page_token,omitempty"`
// 	PerPage       int32   `form:"per_page,default=100" validate:"numeric,min=1,max=100" binding:"numeric,min=1,max=100"`
// }
//
// type TempECRCredentials struct {
// 	AccessKeyId     string    `json:"access_key_id"`
// 	SecretAccessKey string    `json:"secret_access_key"`
// 	SessionToken    string    `json:"session_token"`
// 	Expiration      time.Time `json:"expiration"`
// 	Region          string    `json:"region"`
// }
//
// // TempECRCredentialsResponse represents the response for getting temporary ECR credentials
// type TempECRCredentialsResponse HTTPResponse[TempECRCredentials]
//
// // ECRImageIdentifier identifies an ECR image either by digest or tags
// type DeleteECRImageRequest struct {
// 	ImageDigest string   `form:"image_digest"` // The sha256 digest of the image manifest
// 	ImageTags   []string `form:"image_tags"`   // The tags associated with the image
// 	Type        string   `form:"type,default=user" binding:"oneof=user org" validate:"oneof=user org"`
// }

type CreateECRDeploymentEnvInput struct {
	Key   string `json:"key" validate:"required,min=1,max=50,alphanumericUnderscore"`
	Value string `json:"value" validate:"required,min=1"`

	ID uuid.UUID `json:"-" validate:"required" swaggerignore:"true"`
}

type UpdateECRDeploymentEnvInput struct {
	OldKey string `json:"old_key" validate:"required,min=1,max=50,alphanumericUnderscore"`
	Key    string `json:"key" form:"key" validate:"required,min=1,max=50,alphanumericUnderscore"`
	Value  string `json:"value" validate:"required,min=1"`

	ID uuid.UUID `json:"-" validate:"required" swaggerignore:"true"`
}

type DeleteECRDeploymentEnvInput struct {
	Key string `form:"key" validate:"required,min=1,max=50,alphanumericUnderscore"`

	ID uuid.UUID `json:"-" validate:"required" swaggerignore:"true"`
}

type CreateECRDeploymentInput struct {
	DeploymentName string            `json:"deployment_name" validate:"required,isValidSubdomain"`
	ImageURI       string            `json:"image_uri" validate:"required"`
	NodeName       string            `json:"node_name" validate:"required"`
	Port           int32             `json:"port" validate:"required"`
	Env            map[string]string `json:"env" validate:"required"`
	// IngressConfig  *EcrIngressConfig `json:"ingress_config"`

	OwnerID   uuid.UUID `swaggerignore:"true"`
	OwnerType string    `form:"type,default=user" validate:"oneof=user org" binding:"oneof=user org" example:"user"`
}

type EcrIngressConfig struct {
	ProxyBodySize *int `json:"proxy_body_size" validate:"min=1" example:"100"` // INFO: for 413 Request Entity Too Large HTTP error, in Megabytes
}

type CreateECRDeploymentResponse HTTPResponse[ECRDeployment]

type UpdateECRDeploymentInput struct {
	Port          *int32             `json:"port,omitempty"`
	NodeName      *string            `json:"node_name,omitempty"`
	NumCpu        *uint              `json:"cpu" validate:"omitempty,gt=0"`
	Mem           *types.HardwareMem `json:"mem" validate:"omitempty"`
	IngressConfig *EcrIngressConfig  `json:"ingress_config"`
}

type ListECRDeploymentInput struct {
	UserID         *uuid.UUID             `form:"user_id,omitempty"`
	OrgID          *uuid.UUID             `form:"org_id,omitempty"`
	Page           int                    `form:"page,default=1" validate:"numeric,min=1" binding:"numeric,min=1"`
	PerPage        int                    `form:"per_page,default=100" validate:"numeric,min=1,max=100" binding:"numeric,min=1,max=100"`
	OrderBy        enums.OrderByColumn    `form:"order_by,default=created_at" validate:"oneof=created_at updated_at" binding:"oneof=created_at updated_at"`
	OrderDirection enums.OrderByDirection `form:"sort,default=desc" validate:"oneof=asc desc" binding:"oneof=asc desc"`
	Search         *string                `form:"search,omitempty" validate:"omitempty,min=1,max=100,keyword" binding:"omitempty,min=1,max=100"`
}

type ListECRDeploymentResponse HTTPResponse[[]ECRDeployment]

type GetECRDeploymentResponse HTTPResponse[ECRDeployment]
