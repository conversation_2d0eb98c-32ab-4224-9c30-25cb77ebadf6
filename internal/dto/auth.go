package dto

import (
	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
)

type CustomAccessTokenInput struct {
	UserID               uuid.UUID          `json:"user_id"`
	Claims               *AccessTokenClaims `json:"claims"`
	AuthenticationMethod string             `json:"authentication_method"`
} // @name CustomAccessTokenInput

type AccessTokenClaims struct {
	jwt.RegisteredClaims
	Email                       string                 `json:"email"`
	Phone                       string                 `json:"phone"`
	AppMetaData                 map[string]interface{} `json:"app_metadata"`
	UserMetaData                map[string]interface{} `json:"user_metadata"`
	Role                        string                 `json:"role"`
	AuthenticatorAssuranceLevel string                 `json:"aal,omitempty"`
	SessionId                   string                 `json:"session_id,omitempty"`
	IsAnonymous                 bool                   `json:"is_anonymous"`
} // @name AccessTokenClaims

type CustomAccessTokenOutput struct {
	Claims    map[string]interface{} `json:"claims"`
	HookError AuthHookError          `json:"error,omitempty"`
} // @name CustomAccessTokenOutput

type AuthHookError struct {
	HTTPCode int    `json:"http_code,omitempty"`
	Message  string `json:"message,omitempty"`
} // @name AuthHookError

type SignupInput struct {
	Email    string `json:"email" validate:"required,email"`
	Password string `json:"password" validate:"required"`
	Username string `json:"username" validate:"required,username"`
	Name     string `json:"name" validate:"required,name"`
}
