// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	gin "github.com/gin-gonic/gin"

	mock "github.com/stretchr/testify/mock"
)

// MockUserHandler is an autogenerated mock type for the UserHandler type
type MockUserHandler struct {
	mock.Mock
}

type <PERSON>ck<PERSON>ser<PERSON>andler_Expecter struct {
	mock *mock.Mock
}

func (_m *MockUserHandler) EXPECT() *MockUserHandler_Expecter {
	return &MockUserHandler_Expecter{mock: &_m.Mock}
}

// ChangePassword provides a mock function with given fields: ctx
func (_m *MockUserHandler) ChangePassword(ctx *gin.Context) {
	_m.Called(ctx)
}

// MockUserHandler_ChangePassword_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ChangePassword'
type MockUserHandler_ChangePassword_Call struct {
	*mock.Call
}

// ChangePassword is a helper method to define mock.On call
//   - ctx *gin.Context
func (_e *MockUserHandler_Expecter) ChangePassword(ctx interface{}) *MockUserHandler_ChangePassword_Call {
	return &MockUserHandler_ChangePassword_Call{Call: _e.mock.On("ChangePassword", ctx)}
}

func (_c *MockUserHandler_ChangePassword_Call) Run(run func(ctx *gin.Context)) *MockUserHandler_ChangePassword_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockUserHandler_ChangePassword_Call) Return() *MockUserHandler_ChangePassword_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockUserHandler_ChangePassword_Call) RunAndReturn(run func(*gin.Context)) *MockUserHandler_ChangePassword_Call {
	_c.Run(run)
	return _c
}

// DeleteUser provides a mock function with given fields: ctx
func (_m *MockUserHandler) DeleteUser(ctx *gin.Context) {
	_m.Called(ctx)
}

// MockUserHandler_DeleteUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteUser'
type MockUserHandler_DeleteUser_Call struct {
	*mock.Call
}

// DeleteUser is a helper method to define mock.On call
//   - ctx *gin.Context
func (_e *MockUserHandler_Expecter) DeleteUser(ctx interface{}) *MockUserHandler_DeleteUser_Call {
	return &MockUserHandler_DeleteUser_Call{Call: _e.mock.On("DeleteUser", ctx)}
}

func (_c *MockUserHandler_DeleteUser_Call) Run(run func(ctx *gin.Context)) *MockUserHandler_DeleteUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockUserHandler_DeleteUser_Call) Return() *MockUserHandler_DeleteUser_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockUserHandler_DeleteUser_Call) RunAndReturn(run func(*gin.Context)) *MockUserHandler_DeleteUser_Call {
	_c.Run(run)
	return _c
}

// DeleteUserAvatar provides a mock function with given fields: ctx
func (_m *MockUserHandler) DeleteUserAvatar(ctx *gin.Context) {
	_m.Called(ctx)
}

// MockUserHandler_DeleteUserAvatar_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteUserAvatar'
type MockUserHandler_DeleteUserAvatar_Call struct {
	*mock.Call
}

// DeleteUserAvatar is a helper method to define mock.On call
//   - ctx *gin.Context
func (_e *MockUserHandler_Expecter) DeleteUserAvatar(ctx interface{}) *MockUserHandler_DeleteUserAvatar_Call {
	return &MockUserHandler_DeleteUserAvatar_Call{Call: _e.mock.On("DeleteUserAvatar", ctx)}
}

func (_c *MockUserHandler_DeleteUserAvatar_Call) Run(run func(ctx *gin.Context)) *MockUserHandler_DeleteUserAvatar_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockUserHandler_DeleteUserAvatar_Call) Return() *MockUserHandler_DeleteUserAvatar_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockUserHandler_DeleteUserAvatar_Call) RunAndReturn(run func(*gin.Context)) *MockUserHandler_DeleteUserAvatar_Call {
	_c.Run(run)
	return _c
}

// GetCurrentUser provides a mock function with given fields: ctx
func (_m *MockUserHandler) GetCurrentUser(ctx *gin.Context) {
	_m.Called(ctx)
}

// MockUserHandler_GetCurrentUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetCurrentUser'
type MockUserHandler_GetCurrentUser_Call struct {
	*mock.Call
}

// GetCurrentUser is a helper method to define mock.On call
//   - ctx *gin.Context
func (_e *MockUserHandler_Expecter) GetCurrentUser(ctx interface{}) *MockUserHandler_GetCurrentUser_Call {
	return &MockUserHandler_GetCurrentUser_Call{Call: _e.mock.On("GetCurrentUser", ctx)}
}

func (_c *MockUserHandler_GetCurrentUser_Call) Run(run func(ctx *gin.Context)) *MockUserHandler_GetCurrentUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockUserHandler_GetCurrentUser_Call) Return() *MockUserHandler_GetCurrentUser_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockUserHandler_GetCurrentUser_Call) RunAndReturn(run func(*gin.Context)) *MockUserHandler_GetCurrentUser_Call {
	_c.Run(run)
	return _c
}

// ListUsers provides a mock function with given fields: ctx
func (_m *MockUserHandler) ListUsers(ctx *gin.Context) {
	_m.Called(ctx)
}

// MockUserHandler_ListUsers_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListUsers'
type MockUserHandler_ListUsers_Call struct {
	*mock.Call
}

// ListUsers is a helper method to define mock.On call
//   - ctx *gin.Context
func (_e *MockUserHandler_Expecter) ListUsers(ctx interface{}) *MockUserHandler_ListUsers_Call {
	return &MockUserHandler_ListUsers_Call{Call: _e.mock.On("ListUsers", ctx)}
}

func (_c *MockUserHandler_ListUsers_Call) Run(run func(ctx *gin.Context)) *MockUserHandler_ListUsers_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockUserHandler_ListUsers_Call) Return() *MockUserHandler_ListUsers_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockUserHandler_ListUsers_Call) RunAndReturn(run func(*gin.Context)) *MockUserHandler_ListUsers_Call {
	_c.Run(run)
	return _c
}

// UpdateUserRole provides a mock function with given fields: ctx
func (_m *MockUserHandler) UpdateUserRole(ctx *gin.Context) {
	_m.Called(ctx)
}

// MockUserHandler_UpdateUserRole_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateUserRole'
type MockUserHandler_UpdateUserRole_Call struct {
	*mock.Call
}

// UpdateUserRole is a helper method to define mock.On call
//   - ctx *gin.Context
func (_e *MockUserHandler_Expecter) UpdateUserRole(ctx interface{}) *MockUserHandler_UpdateUserRole_Call {
	return &MockUserHandler_UpdateUserRole_Call{Call: _e.mock.On("UpdateUserRole", ctx)}
}

func (_c *MockUserHandler_UpdateUserRole_Call) Run(run func(ctx *gin.Context)) *MockUserHandler_UpdateUserRole_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockUserHandler_UpdateUserRole_Call) Return() *MockUserHandler_UpdateUserRole_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockUserHandler_UpdateUserRole_Call) RunAndReturn(run func(*gin.Context)) *MockUserHandler_UpdateUserRole_Call {
	_c.Run(run)
	return _c
}

// UploadUserAvatar provides a mock function with given fields: ctx
func (_m *MockUserHandler) UploadUserAvatar(ctx *gin.Context) {
	_m.Called(ctx)
}

// MockUserHandler_UploadUserAvatar_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UploadUserAvatar'
type MockUserHandler_UploadUserAvatar_Call struct {
	*mock.Call
}

// UploadUserAvatar is a helper method to define mock.On call
//   - ctx *gin.Context
func (_e *MockUserHandler_Expecter) UploadUserAvatar(ctx interface{}) *MockUserHandler_UploadUserAvatar_Call {
	return &MockUserHandler_UploadUserAvatar_Call{Call: _e.mock.On("UploadUserAvatar", ctx)}
}

func (_c *MockUserHandler_UploadUserAvatar_Call) Run(run func(ctx *gin.Context)) *MockUserHandler_UploadUserAvatar_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockUserHandler_UploadUserAvatar_Call) Return() *MockUserHandler_UploadUserAvatar_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockUserHandler_UploadUserAvatar_Call) RunAndReturn(run func(*gin.Context)) *MockUserHandler_UploadUserAvatar_Call {
	_c.Run(run)
	return _c
}

// NewMockUserHandler creates a new instance of MockUserHandler. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockUserHandler(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockUserHandler {
	mock := &MockUserHandler{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
