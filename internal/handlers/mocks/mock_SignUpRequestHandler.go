// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	gin "github.com/gin-gonic/gin"

	mock "github.com/stretchr/testify/mock"
)

// MockSignUpRequestHandler is an autogenerated mock type for the SignUpRequestHandler type
type MockSignUpRequestHandler struct {
	mock.Mock
}

type MockSignUpRequestHandler_Expecter struct {
	mock *mock.Mock
}

func (_m *MockSignUpRequestHandler) EXPECT() *MockSignUpRequestHandler_Expecter {
	return &MockSignUpRequestHandler_Expecter{mock: &_m.Mock}
}

// InviteUser provides a mock function with given fields: ctx
func (_m *MockSignUpRequestHandler) InviteUser(ctx *gin.Context) {
	_m.Called(ctx)
}

// MockSignUpRequestHandler_InviteUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'InviteUser'
type MockSignUpRequestHandler_InviteUser_Call struct {
	*mock.Call
}

// InviteUser is a helper method to define mock.On call
//   - ctx *gin.Context
func (_e *MockSignUpRequestHandler_Expecter) InviteUser(ctx interface{}) *MockSignUpRequestHandler_InviteUser_Call {
	return &MockSignUpRequestHandler_InviteUser_Call{Call: _e.mock.On("InviteUser", ctx)}
}

func (_c *MockSignUpRequestHandler_InviteUser_Call) Run(run func(ctx *gin.Context)) *MockSignUpRequestHandler_InviteUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockSignUpRequestHandler_InviteUser_Call) Return() *MockSignUpRequestHandler_InviteUser_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockSignUpRequestHandler_InviteUser_Call) RunAndReturn(run func(*gin.Context)) *MockSignUpRequestHandler_InviteUser_Call {
	_c.Run(run)
	return _c
}

// ListApprovalRequest provides a mock function with given fields: ctx
func (_m *MockSignUpRequestHandler) ListApprovalRequest(ctx *gin.Context) {
	_m.Called(ctx)
}

// MockSignUpRequestHandler_ListApprovalRequest_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListApprovalRequest'
type MockSignUpRequestHandler_ListApprovalRequest_Call struct {
	*mock.Call
}

// ListApprovalRequest is a helper method to define mock.On call
//   - ctx *gin.Context
func (_e *MockSignUpRequestHandler_Expecter) ListApprovalRequest(ctx interface{}) *MockSignUpRequestHandler_ListApprovalRequest_Call {
	return &MockSignUpRequestHandler_ListApprovalRequest_Call{Call: _e.mock.On("ListApprovalRequest", ctx)}
}

func (_c *MockSignUpRequestHandler_ListApprovalRequest_Call) Run(run func(ctx *gin.Context)) *MockSignUpRequestHandler_ListApprovalRequest_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockSignUpRequestHandler_ListApprovalRequest_Call) Return() *MockSignUpRequestHandler_ListApprovalRequest_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockSignUpRequestHandler_ListApprovalRequest_Call) RunAndReturn(run func(*gin.Context)) *MockSignUpRequestHandler_ListApprovalRequest_Call {
	_c.Run(run)
	return _c
}

// ProcessSignUpRequest provides a mock function with given fields: ctx
func (_m *MockSignUpRequestHandler) ProcessSignUpRequest(ctx *gin.Context) {
	_m.Called(ctx)
}

// MockSignUpRequestHandler_ProcessSignUpRequest_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ProcessSignUpRequest'
type MockSignUpRequestHandler_ProcessSignUpRequest_Call struct {
	*mock.Call
}

// ProcessSignUpRequest is a helper method to define mock.On call
//   - ctx *gin.Context
func (_e *MockSignUpRequestHandler_Expecter) ProcessSignUpRequest(ctx interface{}) *MockSignUpRequestHandler_ProcessSignUpRequest_Call {
	return &MockSignUpRequestHandler_ProcessSignUpRequest_Call{Call: _e.mock.On("ProcessSignUpRequest", ctx)}
}

func (_c *MockSignUpRequestHandler_ProcessSignUpRequest_Call) Run(run func(ctx *gin.Context)) *MockSignUpRequestHandler_ProcessSignUpRequest_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockSignUpRequestHandler_ProcessSignUpRequest_Call) Return() *MockSignUpRequestHandler_ProcessSignUpRequest_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockSignUpRequestHandler_ProcessSignUpRequest_Call) RunAndReturn(run func(*gin.Context)) *MockSignUpRequestHandler_ProcessSignUpRequest_Call {
	_c.Run(run)
	return _c
}

// SendConfirmEmail provides a mock function with given fields: ctx
func (_m *MockSignUpRequestHandler) SendConfirmEmail(ctx *gin.Context) {
	_m.Called(ctx)
}

// MockSignUpRequestHandler_SendConfirmEmail_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SendConfirmEmail'
type MockSignUpRequestHandler_SendConfirmEmail_Call struct {
	*mock.Call
}

// SendConfirmEmail is a helper method to define mock.On call
//   - ctx *gin.Context
func (_e *MockSignUpRequestHandler_Expecter) SendConfirmEmail(ctx interface{}) *MockSignUpRequestHandler_SendConfirmEmail_Call {
	return &MockSignUpRequestHandler_SendConfirmEmail_Call{Call: _e.mock.On("SendConfirmEmail", ctx)}
}

func (_c *MockSignUpRequestHandler_SendConfirmEmail_Call) Run(run func(ctx *gin.Context)) *MockSignUpRequestHandler_SendConfirmEmail_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockSignUpRequestHandler_SendConfirmEmail_Call) Return() *MockSignUpRequestHandler_SendConfirmEmail_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockSignUpRequestHandler_SendConfirmEmail_Call) RunAndReturn(run func(*gin.Context)) *MockSignUpRequestHandler_SendConfirmEmail_Call {
	_c.Run(run)
	return _c
}

// Signup provides a mock function with given fields: c
func (_m *MockSignUpRequestHandler) Signup(c *gin.Context) {
	_m.Called(c)
}

// MockSignUpRequestHandler_Signup_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Signup'
type MockSignUpRequestHandler_Signup_Call struct {
	*mock.Call
}

// Signup is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockSignUpRequestHandler_Expecter) Signup(c interface{}) *MockSignUpRequestHandler_Signup_Call {
	return &MockSignUpRequestHandler_Signup_Call{Call: _e.mock.On("Signup", c)}
}

func (_c *MockSignUpRequestHandler_Signup_Call) Run(run func(c *gin.Context)) *MockSignUpRequestHandler_Signup_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockSignUpRequestHandler_Signup_Call) Return() *MockSignUpRequestHandler_Signup_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockSignUpRequestHandler_Signup_Call) RunAndReturn(run func(*gin.Context)) *MockSignUpRequestHandler_Signup_Call {
	_c.Run(run)
	return _c
}

// NewMockSignUpRequestHandler creates a new instance of MockSignUpRequestHandler. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockSignUpRequestHandler(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockSignUpRequestHandler {
	mock := &MockSignUpRequestHandler{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
