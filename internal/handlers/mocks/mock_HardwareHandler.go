// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	gin "github.com/gin-gonic/gin"

	mock "github.com/stretchr/testify/mock"
)

// MockHardwareHandler is an autogenerated mock type for the HardwareHandler type
type MockHardwareHandler struct {
	mock.Mock
}

type MockHard<PERSON><PERSON>andler_Expecter struct {
	mock *mock.Mock
}

func (_m *MockHardwareHandler) EXPECT() *MockHardwareHandler_Expecter {
	return &MockHardwareHandler_Expecter{mock: &_m.Mock}
}

// ListGPUNodes provides a mock function with given fields: c
func (_m *MockHardwareHandler) ListGPUNodes(c *gin.Context) {
	_m.Called(c)
}

// MockHardwareHandler_ListGPUNodes_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListGPUNodes'
type MockHardwareHandler_ListGPUNodes_Call struct {
	*mock.Call
}

// ListGPUNodes is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockHardwareHandler_Expecter) ListGPUNodes(c interface{}) *MockHardwareHandler_ListGPUNodes_Call {
	return &MockHardwareHandler_ListGPUNodes_Call{Call: _e.mock.On("ListGPUNodes", c)}
}

func (_c *MockHardwareHandler_ListGPUNodes_Call) Run(run func(c *gin.Context)) *MockHardwareHandler_ListGPUNodes_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockHardwareHandler_ListGPUNodes_Call) Return() *MockHardwareHandler_ListGPUNodes_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockHardwareHandler_ListGPUNodes_Call) RunAndReturn(run func(*gin.Context)) *MockHardwareHandler_ListGPUNodes_Call {
	_c.Run(run)
	return _c
}

// ListHardwares provides a mock function with given fields: c
func (_m *MockHardwareHandler) ListHardwares(c *gin.Context) {
	_m.Called(c)
}

// MockHardwareHandler_ListHardwares_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListHardwares'
type MockHardwareHandler_ListHardwares_Call struct {
	*mock.Call
}

// ListHardwares is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockHardwareHandler_Expecter) ListHardwares(c interface{}) *MockHardwareHandler_ListHardwares_Call {
	return &MockHardwareHandler_ListHardwares_Call{Call: _e.mock.On("ListHardwares", c)}
}

func (_c *MockHardwareHandler_ListHardwares_Call) Run(run func(c *gin.Context)) *MockHardwareHandler_ListHardwares_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockHardwareHandler_ListHardwares_Call) Return() *MockHardwareHandler_ListHardwares_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockHardwareHandler_ListHardwares_Call) RunAndReturn(run func(*gin.Context)) *MockHardwareHandler_ListHardwares_Call {
	_c.Run(run)
	return _c
}

// NewMockHardwareHandler creates a new instance of MockHardwareHandler. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockHardwareHandler(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockHardwareHandler {
	mock := &MockHardwareHandler{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
