// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	gin "github.com/gin-gonic/gin"

	mock "github.com/stretchr/testify/mock"
)

// MockAccessTokenHandler is an autogenerated mock type for the AccessTokenHandler type
type MockAccessTokenHandler struct {
	mock.Mock
}

type MockAccessTokenHandler_Expecter struct {
	mock *mock.Mock
}

func (_m *MockAccessTokenHandler) EXPECT() *MockAccessTokenHandler_Expecter {
	return &MockAccessTokenHandler_Expecter{mock: &_m.Mock}
}

// CreateNewAccessToken provides a mock function with given fields: ctx
func (_m *MockAccessTokenHandler) CreateNewAccessToken(ctx *gin.Context) {
	_m.Called(ctx)
}

// MockAccessTokenHandler_CreateNewAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateNewAccessToken'
type MockAccessTokenHandler_CreateNewAccessToken_Call struct {
	*mock.Call
}

// CreateNewAccessToken is a helper method to define mock.On call
//   - ctx *gin.Context
func (_e *MockAccessTokenHandler_Expecter) CreateNewAccessToken(ctx interface{}) *MockAccessTokenHandler_CreateNewAccessToken_Call {
	return &MockAccessTokenHandler_CreateNewAccessToken_Call{Call: _e.mock.On("CreateNewAccessToken", ctx)}
}

func (_c *MockAccessTokenHandler_CreateNewAccessToken_Call) Run(run func(ctx *gin.Context)) *MockAccessTokenHandler_CreateNewAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockAccessTokenHandler_CreateNewAccessToken_Call) Return() *MockAccessTokenHandler_CreateNewAccessToken_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockAccessTokenHandler_CreateNewAccessToken_Call) RunAndReturn(run func(*gin.Context)) *MockAccessTokenHandler_CreateNewAccessToken_Call {
	_c.Run(run)
	return _c
}

// DeleteAccessToken provides a mock function with given fields: ctx
func (_m *MockAccessTokenHandler) DeleteAccessToken(ctx *gin.Context) {
	_m.Called(ctx)
}

// MockAccessTokenHandler_DeleteAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteAccessToken'
type MockAccessTokenHandler_DeleteAccessToken_Call struct {
	*mock.Call
}

// DeleteAccessToken is a helper method to define mock.On call
//   - ctx *gin.Context
func (_e *MockAccessTokenHandler_Expecter) DeleteAccessToken(ctx interface{}) *MockAccessTokenHandler_DeleteAccessToken_Call {
	return &MockAccessTokenHandler_DeleteAccessToken_Call{Call: _e.mock.On("DeleteAccessToken", ctx)}
}

func (_c *MockAccessTokenHandler_DeleteAccessToken_Call) Run(run func(ctx *gin.Context)) *MockAccessTokenHandler_DeleteAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockAccessTokenHandler_DeleteAccessToken_Call) Return() *MockAccessTokenHandler_DeleteAccessToken_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockAccessTokenHandler_DeleteAccessToken_Call) RunAndReturn(run func(*gin.Context)) *MockAccessTokenHandler_DeleteAccessToken_Call {
	_c.Run(run)
	return _c
}

// ListAccessToken provides a mock function with given fields: ctx
func (_m *MockAccessTokenHandler) ListAccessToken(ctx *gin.Context) {
	_m.Called(ctx)
}

// MockAccessTokenHandler_ListAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListAccessToken'
type MockAccessTokenHandler_ListAccessToken_Call struct {
	*mock.Call
}

// ListAccessToken is a helper method to define mock.On call
//   - ctx *gin.Context
func (_e *MockAccessTokenHandler_Expecter) ListAccessToken(ctx interface{}) *MockAccessTokenHandler_ListAccessToken_Call {
	return &MockAccessTokenHandler_ListAccessToken_Call{Call: _e.mock.On("ListAccessToken", ctx)}
}

func (_c *MockAccessTokenHandler_ListAccessToken_Call) Run(run func(ctx *gin.Context)) *MockAccessTokenHandler_ListAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockAccessTokenHandler_ListAccessToken_Call) Return() *MockAccessTokenHandler_ListAccessToken_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockAccessTokenHandler_ListAccessToken_Call) RunAndReturn(run func(*gin.Context)) *MockAccessTokenHandler_ListAccessToken_Call {
	_c.Run(run)
	return _c
}

// VerifyAccessToken provides a mock function with given fields: ctx
func (_m *MockAccessTokenHandler) VerifyAccessToken(ctx *gin.Context) {
	_m.Called(ctx)
}

// MockAccessTokenHandler_VerifyAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'VerifyAccessToken'
type MockAccessTokenHandler_VerifyAccessToken_Call struct {
	*mock.Call
}

// VerifyAccessToken is a helper method to define mock.On call
//   - ctx *gin.Context
func (_e *MockAccessTokenHandler_Expecter) VerifyAccessToken(ctx interface{}) *MockAccessTokenHandler_VerifyAccessToken_Call {
	return &MockAccessTokenHandler_VerifyAccessToken_Call{Call: _e.mock.On("VerifyAccessToken", ctx)}
}

func (_c *MockAccessTokenHandler_VerifyAccessToken_Call) Run(run func(ctx *gin.Context)) *MockAccessTokenHandler_VerifyAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockAccessTokenHandler_VerifyAccessToken_Call) Return() *MockAccessTokenHandler_VerifyAccessToken_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockAccessTokenHandler_VerifyAccessToken_Call) RunAndReturn(run func(*gin.Context)) *MockAccessTokenHandler_VerifyAccessToken_Call {
	_c.Run(run)
	return _c
}

// NewMockAccessTokenHandler creates a new instance of MockAccessTokenHandler. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockAccessTokenHandler(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockAccessTokenHandler {
	mock := &MockAccessTokenHandler{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
