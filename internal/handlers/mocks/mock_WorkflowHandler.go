// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import mock "github.com/stretchr/testify/mock"

// MockWorkflowHandler is an autogenerated mock type for the WorkflowHandler type
type MockWorkflowHandler struct {
	mock.Mock
}

type <PERSON>ck<PERSON>or<PERSON>flowHandler_Expecter struct {
	mock *mock.Mock
}

func (_m *MockWorkflowHandler) EXPECT() *MockWorkflowHandler_Expecter {
	return &MockWorkflowHandler_Expecter{mock: &_m.Mock}
}

// NewMockWorkflowHandler creates a new instance of MockWorkflowHandler. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockWorkflowHandler(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockWorkflowHandler {
	mock := &MockWorkflowHandler{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
