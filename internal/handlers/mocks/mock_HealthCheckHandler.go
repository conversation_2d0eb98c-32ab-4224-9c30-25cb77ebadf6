// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	gin "github.com/gin-gonic/gin"

	mock "github.com/stretchr/testify/mock"
)

// MockHealthCheckHandler is an autogenerated mock type for the HealthCheckHandler type
type MockHealthCheckHandler struct {
	mock.Mock
}

type MockHealthCheckHandler_Expecter struct {
	mock *mock.Mock
}

func (_m *MockHealthCheckHandler) EXPECT() *MockHealthCheckHandler_Expecter {
	return &MockHealthCheckHandler_Expecter{mock: &_m.Mock}
}

// Explore provides a mock function with given fields: c
func (_m *MockHealthCheckHandler) Explore(c *gin.Context) {
	_m.Called(c)
}

// MockHealthCheckHandler_Explore_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Explore'
type MockHealthCheckHandler_Explore_Call struct {
	*mock.Call
}

// Explore is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockHealthCheckHandler_Expecter) Explore(c interface{}) *MockHealthCheckHandler_Explore_Call {
	return &MockHealthCheckHandler_Explore_Call{Call: _e.mock.On("Explore", c)}
}

func (_c *MockHealthCheckHandler_Explore_Call) Run(run func(c *gin.Context)) *MockHealthCheckHandler_Explore_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockHealthCheckHandler_Explore_Call) Return() *MockHealthCheckHandler_Explore_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockHealthCheckHandler_Explore_Call) RunAndReturn(run func(*gin.Context)) *MockHealthCheckHandler_Explore_Call {
	_c.Run(run)
	return _c
}

// NewMockHealthCheckHandler creates a new instance of MockHealthCheckHandler. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockHealthCheckHandler(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockHealthCheckHandler {
	mock := &MockHealthCheckHandler{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
