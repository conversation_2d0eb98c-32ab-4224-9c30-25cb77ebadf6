// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	gin "github.com/gin-gonic/gin"

	mock "github.com/stretchr/testify/mock"
)

// MockOrgAccessTokenHandler is an autogenerated mock type for the OrgAccessTokenHandler type
type MockOrgAccessTokenHandler struct {
	mock.Mock
}

type MockOrgAccessTokenHandler_Expecter struct {
	mock *mock.Mock
}

func (_m *MockOrgAccessTokenHandler) EXPECT() *MockOrgAccessTokenHandler_Expecter {
	return &MockOrgAccessTokenHandler_Expecter{mock: &_m.Mock}
}

// CreateNewOrgAccessToken provides a mock function with given fields: ctx
func (_m *MockOrgAccessTokenHandler) CreateNewOrgAccessToken(ctx *gin.Context) {
	_m.Called(ctx)
}

// MockOrgAccessTokenHandler_CreateNewOrgAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateNewOrgAccessToken'
type MockOrgAccessTokenHandler_CreateNewOrgAccessToken_Call struct {
	*mock.Call
}

// CreateNewOrgAccessToken is a helper method to define mock.On call
//   - ctx *gin.Context
func (_e *MockOrgAccessTokenHandler_Expecter) CreateNewOrgAccessToken(ctx interface{}) *MockOrgAccessTokenHandler_CreateNewOrgAccessToken_Call {
	return &MockOrgAccessTokenHandler_CreateNewOrgAccessToken_Call{Call: _e.mock.On("CreateNewOrgAccessToken", ctx)}
}

func (_c *MockOrgAccessTokenHandler_CreateNewOrgAccessToken_Call) Run(run func(ctx *gin.Context)) *MockOrgAccessTokenHandler_CreateNewOrgAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockOrgAccessTokenHandler_CreateNewOrgAccessToken_Call) Return() *MockOrgAccessTokenHandler_CreateNewOrgAccessToken_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockOrgAccessTokenHandler_CreateNewOrgAccessToken_Call) RunAndReturn(run func(*gin.Context)) *MockOrgAccessTokenHandler_CreateNewOrgAccessToken_Call {
	_c.Run(run)
	return _c
}

// DeleteOrgAccessToken provides a mock function with given fields: ctx
func (_m *MockOrgAccessTokenHandler) DeleteOrgAccessToken(ctx *gin.Context) {
	_m.Called(ctx)
}

// MockOrgAccessTokenHandler_DeleteOrgAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteOrgAccessToken'
type MockOrgAccessTokenHandler_DeleteOrgAccessToken_Call struct {
	*mock.Call
}

// DeleteOrgAccessToken is a helper method to define mock.On call
//   - ctx *gin.Context
func (_e *MockOrgAccessTokenHandler_Expecter) DeleteOrgAccessToken(ctx interface{}) *MockOrgAccessTokenHandler_DeleteOrgAccessToken_Call {
	return &MockOrgAccessTokenHandler_DeleteOrgAccessToken_Call{Call: _e.mock.On("DeleteOrgAccessToken", ctx)}
}

func (_c *MockOrgAccessTokenHandler_DeleteOrgAccessToken_Call) Run(run func(ctx *gin.Context)) *MockOrgAccessTokenHandler_DeleteOrgAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockOrgAccessTokenHandler_DeleteOrgAccessToken_Call) Return() *MockOrgAccessTokenHandler_DeleteOrgAccessToken_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockOrgAccessTokenHandler_DeleteOrgAccessToken_Call) RunAndReturn(run func(*gin.Context)) *MockOrgAccessTokenHandler_DeleteOrgAccessToken_Call {
	_c.Run(run)
	return _c
}

// ListOrgAccessToken provides a mock function with given fields: ctx
func (_m *MockOrgAccessTokenHandler) ListOrgAccessToken(ctx *gin.Context) {
	_m.Called(ctx)
}

// MockOrgAccessTokenHandler_ListOrgAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListOrgAccessToken'
type MockOrgAccessTokenHandler_ListOrgAccessToken_Call struct {
	*mock.Call
}

// ListOrgAccessToken is a helper method to define mock.On call
//   - ctx *gin.Context
func (_e *MockOrgAccessTokenHandler_Expecter) ListOrgAccessToken(ctx interface{}) *MockOrgAccessTokenHandler_ListOrgAccessToken_Call {
	return &MockOrgAccessTokenHandler_ListOrgAccessToken_Call{Call: _e.mock.On("ListOrgAccessToken", ctx)}
}

func (_c *MockOrgAccessTokenHandler_ListOrgAccessToken_Call) Run(run func(ctx *gin.Context)) *MockOrgAccessTokenHandler_ListOrgAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockOrgAccessTokenHandler_ListOrgAccessToken_Call) Return() *MockOrgAccessTokenHandler_ListOrgAccessToken_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockOrgAccessTokenHandler_ListOrgAccessToken_Call) RunAndReturn(run func(*gin.Context)) *MockOrgAccessTokenHandler_ListOrgAccessToken_Call {
	_c.Run(run)
	return _c
}

// VerifyOrgAccessToken provides a mock function with given fields: ctx
func (_m *MockOrgAccessTokenHandler) VerifyOrgAccessToken(ctx *gin.Context) {
	_m.Called(ctx)
}

// MockOrgAccessTokenHandler_VerifyOrgAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'VerifyOrgAccessToken'
type MockOrgAccessTokenHandler_VerifyOrgAccessToken_Call struct {
	*mock.Call
}

// VerifyOrgAccessToken is a helper method to define mock.On call
//   - ctx *gin.Context
func (_e *MockOrgAccessTokenHandler_Expecter) VerifyOrgAccessToken(ctx interface{}) *MockOrgAccessTokenHandler_VerifyOrgAccessToken_Call {
	return &MockOrgAccessTokenHandler_VerifyOrgAccessToken_Call{Call: _e.mock.On("VerifyOrgAccessToken", ctx)}
}

func (_c *MockOrgAccessTokenHandler_VerifyOrgAccessToken_Call) Run(run func(ctx *gin.Context)) *MockOrgAccessTokenHandler_VerifyOrgAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockOrgAccessTokenHandler_VerifyOrgAccessToken_Call) Return() *MockOrgAccessTokenHandler_VerifyOrgAccessToken_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockOrgAccessTokenHandler_VerifyOrgAccessToken_Call) RunAndReturn(run func(*gin.Context)) *MockOrgAccessTokenHandler_VerifyOrgAccessToken_Call {
	_c.Run(run)
	return _c
}

// NewMockOrgAccessTokenHandler creates a new instance of MockOrgAccessTokenHandler. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockOrgAccessTokenHandler(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockOrgAccessTokenHandler {
	mock := &MockOrgAccessTokenHandler{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
