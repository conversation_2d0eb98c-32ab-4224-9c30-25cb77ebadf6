// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	gin "github.com/gin-gonic/gin"

	mock "github.com/stretchr/testify/mock"
)

// MockSshKeysHandler is an autogenerated mock type for the SshKeysHandler type
type MockSshKeysHandler struct {
	mock.Mock
}

type MockSshKeysHandler_Expecter struct {
	mock *mock.Mock
}

func (_m *MockSshKeysHandler) EXPECT() *MockSshKeysHandler_Expecter {
	return &MockSshKeysHandler_Expecter{mock: &_m.Mock}
}

// AddNewSshKey provides a mock function with given fields: ctx
func (_m *MockSshKeysHandler) AddNewSshKey(ctx *gin.Context) {
	_m.Called(ctx)
}

// MockSshKeysHandler_AddNewSshKey_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AddNewSshKey'
type MockSshKeysHandler_AddNewSshKey_Call struct {
	*mock.Call
}

// AddNewSshKey is a helper method to define mock.On call
//   - ctx *gin.Context
func (_e *MockSshKeysHandler_Expecter) AddNewSshKey(ctx interface{}) *MockSshKeysHandler_AddNewSshKey_Call {
	return &MockSshKeysHandler_AddNewSshKey_Call{Call: _e.mock.On("AddNewSshKey", ctx)}
}

func (_c *MockSshKeysHandler_AddNewSshKey_Call) Run(run func(ctx *gin.Context)) *MockSshKeysHandler_AddNewSshKey_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockSshKeysHandler_AddNewSshKey_Call) Return() *MockSshKeysHandler_AddNewSshKey_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockSshKeysHandler_AddNewSshKey_Call) RunAndReturn(run func(*gin.Context)) *MockSshKeysHandler_AddNewSshKey_Call {
	_c.Run(run)
	return _c
}

// DeleteSshKey provides a mock function with given fields: ctx
func (_m *MockSshKeysHandler) DeleteSshKey(ctx *gin.Context) {
	_m.Called(ctx)
}

// MockSshKeysHandler_DeleteSshKey_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteSshKey'
type MockSshKeysHandler_DeleteSshKey_Call struct {
	*mock.Call
}

// DeleteSshKey is a helper method to define mock.On call
//   - ctx *gin.Context
func (_e *MockSshKeysHandler_Expecter) DeleteSshKey(ctx interface{}) *MockSshKeysHandler_DeleteSshKey_Call {
	return &MockSshKeysHandler_DeleteSshKey_Call{Call: _e.mock.On("DeleteSshKey", ctx)}
}

func (_c *MockSshKeysHandler_DeleteSshKey_Call) Run(run func(ctx *gin.Context)) *MockSshKeysHandler_DeleteSshKey_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockSshKeysHandler_DeleteSshKey_Call) Return() *MockSshKeysHandler_DeleteSshKey_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockSshKeysHandler_DeleteSshKey_Call) RunAndReturn(run func(*gin.Context)) *MockSshKeysHandler_DeleteSshKey_Call {
	_c.Run(run)
	return _c
}

// GetSshKey provides a mock function with given fields: ctx
func (_m *MockSshKeysHandler) GetSshKey(ctx *gin.Context) {
	_m.Called(ctx)
}

// MockSshKeysHandler_GetSshKey_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetSshKey'
type MockSshKeysHandler_GetSshKey_Call struct {
	*mock.Call
}

// GetSshKey is a helper method to define mock.On call
//   - ctx *gin.Context
func (_e *MockSshKeysHandler_Expecter) GetSshKey(ctx interface{}) *MockSshKeysHandler_GetSshKey_Call {
	return &MockSshKeysHandler_GetSshKey_Call{Call: _e.mock.On("GetSshKey", ctx)}
}

func (_c *MockSshKeysHandler_GetSshKey_Call) Run(run func(ctx *gin.Context)) *MockSshKeysHandler_GetSshKey_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockSshKeysHandler_GetSshKey_Call) Return() *MockSshKeysHandler_GetSshKey_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockSshKeysHandler_GetSshKey_Call) RunAndReturn(run func(*gin.Context)) *MockSshKeysHandler_GetSshKey_Call {
	_c.Run(run)
	return _c
}

// ListSshKeys provides a mock function with given fields: ctx
func (_m *MockSshKeysHandler) ListSshKeys(ctx *gin.Context) {
	_m.Called(ctx)
}

// MockSshKeysHandler_ListSshKeys_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListSshKeys'
type MockSshKeysHandler_ListSshKeys_Call struct {
	*mock.Call
}

// ListSshKeys is a helper method to define mock.On call
//   - ctx *gin.Context
func (_e *MockSshKeysHandler_Expecter) ListSshKeys(ctx interface{}) *MockSshKeysHandler_ListSshKeys_Call {
	return &MockSshKeysHandler_ListSshKeys_Call{Call: _e.mock.On("ListSshKeys", ctx)}
}

func (_c *MockSshKeysHandler_ListSshKeys_Call) Run(run func(ctx *gin.Context)) *MockSshKeysHandler_ListSshKeys_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockSshKeysHandler_ListSshKeys_Call) Return() *MockSshKeysHandler_ListSshKeys_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockSshKeysHandler_ListSshKeys_Call) RunAndReturn(run func(*gin.Context)) *MockSshKeysHandler_ListSshKeys_Call {
	_c.Run(run)
	return _c
}

// NewMockSshKeysHandler creates a new instance of MockSshKeysHandler. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockSshKeysHandler(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockSshKeysHandler {
	mock := &MockSshKeysHandler{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
