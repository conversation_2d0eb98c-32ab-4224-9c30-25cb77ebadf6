// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	gin "github.com/gin-gonic/gin"

	mock "github.com/stretchr/testify/mock"
)

// MockECRHandler is an autogenerated mock type for the ECRHandler type
type MockECRHandler struct {
	mock.Mock
}

type Mock<PERSON>RHandler_Expecter struct {
	mock *mock.Mock
}

func (_m *MockECRHandler) EXPECT() *MockECRHandler_Expecter {
	return &MockECRHandler_Expecter{mock: &_m.Mock}
}

// CreateECRDeployment provides a mock function with given fields: c
func (_m *MockECRHandler) CreateECRDeployment(c *gin.Context) {
	_m.Called(c)
}

// MockECRHandler_CreateECRDeployment_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateECRDeployment'
type MockECRHandler_CreateECRDeployment_Call struct {
	*mock.Call
}

// CreateECRDeployment is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockECRHandler_Expecter) CreateECRDeployment(c interface{}) *MockECRHandler_CreateECRDeployment_Call {
	return &MockECRHandler_CreateECRDeployment_Call{Call: _e.mock.On("CreateECRDeployment", c)}
}

func (_c *MockECRHandler_CreateECRDeployment_Call) Run(run func(c *gin.Context)) *MockECRHandler_CreateECRDeployment_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockECRHandler_CreateECRDeployment_Call) Return() *MockECRHandler_CreateECRDeployment_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockECRHandler_CreateECRDeployment_Call) RunAndReturn(run func(*gin.Context)) *MockECRHandler_CreateECRDeployment_Call {
	_c.Run(run)
	return _c
}

// CreateECRDeploymentEnv provides a mock function with given fields: c
func (_m *MockECRHandler) CreateECRDeploymentEnv(c *gin.Context) {
	_m.Called(c)
}

// MockECRHandler_CreateECRDeploymentEnv_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateECRDeploymentEnv'
type MockECRHandler_CreateECRDeploymentEnv_Call struct {
	*mock.Call
}

// CreateECRDeploymentEnv is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockECRHandler_Expecter) CreateECRDeploymentEnv(c interface{}) *MockECRHandler_CreateECRDeploymentEnv_Call {
	return &MockECRHandler_CreateECRDeploymentEnv_Call{Call: _e.mock.On("CreateECRDeploymentEnv", c)}
}

func (_c *MockECRHandler_CreateECRDeploymentEnv_Call) Run(run func(c *gin.Context)) *MockECRHandler_CreateECRDeploymentEnv_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockECRHandler_CreateECRDeploymentEnv_Call) Return() *MockECRHandler_CreateECRDeploymentEnv_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockECRHandler_CreateECRDeploymentEnv_Call) RunAndReturn(run func(*gin.Context)) *MockECRHandler_CreateECRDeploymentEnv_Call {
	_c.Run(run)
	return _c
}

// DeleteECRDeployment provides a mock function with given fields: c
func (_m *MockECRHandler) DeleteECRDeployment(c *gin.Context) {
	_m.Called(c)
}

// MockECRHandler_DeleteECRDeployment_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteECRDeployment'
type MockECRHandler_DeleteECRDeployment_Call struct {
	*mock.Call
}

// DeleteECRDeployment is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockECRHandler_Expecter) DeleteECRDeployment(c interface{}) *MockECRHandler_DeleteECRDeployment_Call {
	return &MockECRHandler_DeleteECRDeployment_Call{Call: _e.mock.On("DeleteECRDeployment", c)}
}

func (_c *MockECRHandler_DeleteECRDeployment_Call) Run(run func(c *gin.Context)) *MockECRHandler_DeleteECRDeployment_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockECRHandler_DeleteECRDeployment_Call) Return() *MockECRHandler_DeleteECRDeployment_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockECRHandler_DeleteECRDeployment_Call) RunAndReturn(run func(*gin.Context)) *MockECRHandler_DeleteECRDeployment_Call {
	_c.Run(run)
	return _c
}

// DeleteECRDeploymentEnv provides a mock function with given fields: c
func (_m *MockECRHandler) DeleteECRDeploymentEnv(c *gin.Context) {
	_m.Called(c)
}

// MockECRHandler_DeleteECRDeploymentEnv_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteECRDeploymentEnv'
type MockECRHandler_DeleteECRDeploymentEnv_Call struct {
	*mock.Call
}

// DeleteECRDeploymentEnv is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockECRHandler_Expecter) DeleteECRDeploymentEnv(c interface{}) *MockECRHandler_DeleteECRDeploymentEnv_Call {
	return &MockECRHandler_DeleteECRDeploymentEnv_Call{Call: _e.mock.On("DeleteECRDeploymentEnv", c)}
}

func (_c *MockECRHandler_DeleteECRDeploymentEnv_Call) Run(run func(c *gin.Context)) *MockECRHandler_DeleteECRDeploymentEnv_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockECRHandler_DeleteECRDeploymentEnv_Call) Return() *MockECRHandler_DeleteECRDeploymentEnv_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockECRHandler_DeleteECRDeploymentEnv_Call) RunAndReturn(run func(*gin.Context)) *MockECRHandler_DeleteECRDeploymentEnv_Call {
	_c.Run(run)
	return _c
}

// DeployECR provides a mock function with given fields: c
func (_m *MockECRHandler) DeployECR(c *gin.Context) {
	_m.Called(c)
}

// MockECRHandler_DeployECR_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeployECR'
type MockECRHandler_DeployECR_Call struct {
	*mock.Call
}

// DeployECR is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockECRHandler_Expecter) DeployECR(c interface{}) *MockECRHandler_DeployECR_Call {
	return &MockECRHandler_DeployECR_Call{Call: _e.mock.On("DeployECR", c)}
}

func (_c *MockECRHandler_DeployECR_Call) Run(run func(c *gin.Context)) *MockECRHandler_DeployECR_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockECRHandler_DeployECR_Call) Return() *MockECRHandler_DeployECR_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockECRHandler_DeployECR_Call) RunAndReturn(run func(*gin.Context)) *MockECRHandler_DeployECR_Call {
	_c.Run(run)
	return _c
}

// GetDeploymentPodLogs provides a mock function with given fields: c
func (_m *MockECRHandler) GetDeploymentPodLogs(c *gin.Context) {
	_m.Called(c)
}

// MockECRHandler_GetDeploymentPodLogs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDeploymentPodLogs'
type MockECRHandler_GetDeploymentPodLogs_Call struct {
	*mock.Call
}

// GetDeploymentPodLogs is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockECRHandler_Expecter) GetDeploymentPodLogs(c interface{}) *MockECRHandler_GetDeploymentPodLogs_Call {
	return &MockECRHandler_GetDeploymentPodLogs_Call{Call: _e.mock.On("GetDeploymentPodLogs", c)}
}

func (_c *MockECRHandler_GetDeploymentPodLogs_Call) Run(run func(c *gin.Context)) *MockECRHandler_GetDeploymentPodLogs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockECRHandler_GetDeploymentPodLogs_Call) Return() *MockECRHandler_GetDeploymentPodLogs_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockECRHandler_GetDeploymentPodLogs_Call) RunAndReturn(run func(*gin.Context)) *MockECRHandler_GetDeploymentPodLogs_Call {
	_c.Run(run)
	return _c
}

// GetDeploymentStatus provides a mock function with given fields: c
func (_m *MockECRHandler) GetDeploymentStatus(c *gin.Context) {
	_m.Called(c)
}

// MockECRHandler_GetDeploymentStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDeploymentStatus'
type MockECRHandler_GetDeploymentStatus_Call struct {
	*mock.Call
}

// GetDeploymentStatus is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockECRHandler_Expecter) GetDeploymentStatus(c interface{}) *MockECRHandler_GetDeploymentStatus_Call {
	return &MockECRHandler_GetDeploymentStatus_Call{Call: _e.mock.On("GetDeploymentStatus", c)}
}

func (_c *MockECRHandler_GetDeploymentStatus_Call) Run(run func(c *gin.Context)) *MockECRHandler_GetDeploymentStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockECRHandler_GetDeploymentStatus_Call) Return() *MockECRHandler_GetDeploymentStatus_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockECRHandler_GetDeploymentStatus_Call) RunAndReturn(run func(*gin.Context)) *MockECRHandler_GetDeploymentStatus_Call {
	_c.Run(run)
	return _c
}

// GetECRDeployment provides a mock function with given fields: c
func (_m *MockECRHandler) GetECRDeployment(c *gin.Context) {
	_m.Called(c)
}

// MockECRHandler_GetECRDeployment_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetECRDeployment'
type MockECRHandler_GetECRDeployment_Call struct {
	*mock.Call
}

// GetECRDeployment is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockECRHandler_Expecter) GetECRDeployment(c interface{}) *MockECRHandler_GetECRDeployment_Call {
	return &MockECRHandler_GetECRDeployment_Call{Call: _e.mock.On("GetECRDeployment", c)}
}

func (_c *MockECRHandler_GetECRDeployment_Call) Run(run func(c *gin.Context)) *MockECRHandler_GetECRDeployment_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockECRHandler_GetECRDeployment_Call) Return() *MockECRHandler_GetECRDeployment_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockECRHandler_GetECRDeployment_Call) RunAndReturn(run func(*gin.Context)) *MockECRHandler_GetECRDeployment_Call {
	_c.Run(run)
	return _c
}

// ListECRDeployment provides a mock function with given fields: c
func (_m *MockECRHandler) ListECRDeployment(c *gin.Context) {
	_m.Called(c)
}

// MockECRHandler_ListECRDeployment_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListECRDeployment'
type MockECRHandler_ListECRDeployment_Call struct {
	*mock.Call
}

// ListECRDeployment is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockECRHandler_Expecter) ListECRDeployment(c interface{}) *MockECRHandler_ListECRDeployment_Call {
	return &MockECRHandler_ListECRDeployment_Call{Call: _e.mock.On("ListECRDeployment", c)}
}

func (_c *MockECRHandler_ListECRDeployment_Call) Run(run func(c *gin.Context)) *MockECRHandler_ListECRDeployment_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockECRHandler_ListECRDeployment_Call) Return() *MockECRHandler_ListECRDeployment_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockECRHandler_ListECRDeployment_Call) RunAndReturn(run func(*gin.Context)) *MockECRHandler_ListECRDeployment_Call {
	_c.Run(run)
	return _c
}

// StopECRDeployment provides a mock function with given fields: c
func (_m *MockECRHandler) StopECRDeployment(c *gin.Context) {
	_m.Called(c)
}

// MockECRHandler_StopECRDeployment_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'StopECRDeployment'
type MockECRHandler_StopECRDeployment_Call struct {
	*mock.Call
}

// StopECRDeployment is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockECRHandler_Expecter) StopECRDeployment(c interface{}) *MockECRHandler_StopECRDeployment_Call {
	return &MockECRHandler_StopECRDeployment_Call{Call: _e.mock.On("StopECRDeployment", c)}
}

func (_c *MockECRHandler_StopECRDeployment_Call) Run(run func(c *gin.Context)) *MockECRHandler_StopECRDeployment_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockECRHandler_StopECRDeployment_Call) Return() *MockECRHandler_StopECRDeployment_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockECRHandler_StopECRDeployment_Call) RunAndReturn(run func(*gin.Context)) *MockECRHandler_StopECRDeployment_Call {
	_c.Run(run)
	return _c
}

// UpdateECRDeployment provides a mock function with given fields: c
func (_m *MockECRHandler) UpdateECRDeployment(c *gin.Context) {
	_m.Called(c)
}

// MockECRHandler_UpdateECRDeployment_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateECRDeployment'
type MockECRHandler_UpdateECRDeployment_Call struct {
	*mock.Call
}

// UpdateECRDeployment is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockECRHandler_Expecter) UpdateECRDeployment(c interface{}) *MockECRHandler_UpdateECRDeployment_Call {
	return &MockECRHandler_UpdateECRDeployment_Call{Call: _e.mock.On("UpdateECRDeployment", c)}
}

func (_c *MockECRHandler_UpdateECRDeployment_Call) Run(run func(c *gin.Context)) *MockECRHandler_UpdateECRDeployment_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockECRHandler_UpdateECRDeployment_Call) Return() *MockECRHandler_UpdateECRDeployment_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockECRHandler_UpdateECRDeployment_Call) RunAndReturn(run func(*gin.Context)) *MockECRHandler_UpdateECRDeployment_Call {
	_c.Run(run)
	return _c
}

// UpdateECRDeploymentEnv provides a mock function with given fields: c
func (_m *MockECRHandler) UpdateECRDeploymentEnv(c *gin.Context) {
	_m.Called(c)
}

// MockECRHandler_UpdateECRDeploymentEnv_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateECRDeploymentEnv'
type MockECRHandler_UpdateECRDeploymentEnv_Call struct {
	*mock.Call
}

// UpdateECRDeploymentEnv is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockECRHandler_Expecter) UpdateECRDeploymentEnv(c interface{}) *MockECRHandler_UpdateECRDeploymentEnv_Call {
	return &MockECRHandler_UpdateECRDeploymentEnv_Call{Call: _e.mock.On("UpdateECRDeploymentEnv", c)}
}

func (_c *MockECRHandler_UpdateECRDeploymentEnv_Call) Run(run func(c *gin.Context)) *MockECRHandler_UpdateECRDeploymentEnv_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockECRHandler_UpdateECRDeploymentEnv_Call) Return() *MockECRHandler_UpdateECRDeploymentEnv_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockECRHandler_UpdateECRDeploymentEnv_Call) RunAndReturn(run func(*gin.Context)) *MockECRHandler_UpdateECRDeploymentEnv_Call {
	_c.Run(run)
	return _c
}

// NewMockECRHandler creates a new instance of MockECRHandler. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockECRHandler(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockECRHandler {
	mock := &MockECRHandler{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
