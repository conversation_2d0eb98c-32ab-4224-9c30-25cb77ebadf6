package handlers

import (
	"errors"
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"

	"api-server/internal/dto"
	"api-server/internal/usecase"
	"api-server/internal/usecase/signup_request"
	"api-server/pkg/oteltrace"
	"api-server/pkg/validator"
)

type SignUpRequestHandler interface {
	Signup(c *gin.Context)
	ListApprovalRequest(ctx *gin.Context)
	ProcessSignUpRequest(ctx *gin.Context)
	InviteUser(ctx *gin.Context)
	SendConfirmEmail(ctx *gin.Context)
}

type signUpRequestHandlerImpl struct {
	usecase signup_request.SignupRequestUsecase
}

// NewSignUpRequestHandler creates a new instance of SignUpRequestHandler.
// It initializes the handler with the signup request use case.
//
// Parameters:
//   - usecase: The signup request use case.
//
// Returns:
//   - SignUpRequestHandler: A new SignUpRequestHandler instance.
func NewSignUpRequestHandler(usecase signup_request.SignupRequestUsecase) SignUpRequestHandler {
	return &signUpRequestHandlerImpl{
		usecase: usecase,
	}
}

// Signup
//
//	@Summary		Signup a new user
//	@Description	Signup a new user and send email verification
//	@Accept			json
//	@Produce		json
//	@Param			signupInput	body		dto.SignupInput			true	"Signup input"
//	@Success		201			{object}	map[string]interface{}	"User created successfully and email verification sent"
//	@Failure		400			{object}	dto.HTTPError			"Bad Request - invalid request"
//	@Failure		500			{object}	dto.HTTPError			"Internal Server Error - Encountered an unexpected condition"
//	@Router			/signup [post]
func (u *signUpRequestHandlerImpl) Signup(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(c.Request.Context(), "handlers.Signup")
	defer span.End()

	var input dto.SignupInput
	if err := c.ShouldBindJSON(&input); err != nil {
		span.SetStatus(codes.Error, "failed to bind json")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err, "invalid request input"))
		return
	}
	if err := validator.Validate(input); err != nil {
		span.SetStatus(codes.Error, "invalid request body")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	if err := u.usecase.Signup(ctx, input); err != nil {
		span.SetStatus(codes.Error, "failed to signup user")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewErrorFromUsecase(err))
		return
	}

	span.AddEvent("successfully sign up user", trace.WithAttributes(attribute.String("email", input.Email)))
	span.SetStatus(codes.Ok, fmt.Sprintf("successfully sign up user %s", input.Email))
	c.JSON(http.StatusCreated, gin.H{"message": "User created successfully"})
}

// ListApprovalRequest godoc
//
//	@Summary	List all sign up requests
//	@Tags		Sign Up Request
//	@Produce	json
//	@Success	200	{object}	dto.GetAllSignUpRequestsOutput	"OK"
//	@Failure	500	{object}	dto.HTTPError					"Internal Server Error - Encountered an unexpected condition"
//	@Router		/admin/signups [get]
//	@Security	Bearer
func (u *signUpRequestHandlerImpl) ListApprovalRequest(ctx *gin.Context) {
	c, span := oteltrace.Tracer.Start(ParseContext(ctx), "handlers.ListApprovalRequest")
	defer span.End()

	currentUserId, err := GetCurrentUserId(c)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user")
		span.RecordError(err)
		dto.ErrorResponse(ctx, dto.NewForbiddenError(err, "forbidden"))
		return
	}

	span.SetAttributes(attribute.String("user_id", currentUserId.String()))

	output, err := u.usecase.GetAllSignUpRequests(c)
	if err != nil {
		span.SetStatus(codes.Error, "failed to list all sign up requests")
		span.RecordError(err)
		dto.ErrorResponse(ctx, dto.NewErrorFromUsecase(err))
		return
	}

	span.AddEvent("successfully list all sign up requests")
	span.SetStatus(codes.Ok, "successfully list all sign up requests")
	ctx.JSON(http.StatusOK, output)
}

// ProcessSignUpRequest godoc
//
//	@Summary	Approve or reject user's sign up requests
//	@Tags		Sign Up Request
//	@Accept		json
//	@Produce	json
//	@Param		id		path	string				true	"Sign up request ID"
//	@Param		model	body	dto.ApprovalRequest	true	"Approve or Reject user"
//	@Success	204		"No Content - Request has been successfully completed, but no response payload body"
//	@Failure	400		{object}	dto.HTTPError	"Bad Request - invalid request"
//	@Failure	404		{object}	dto.HTTPError	"Not Found"
//	@Failure	500		{object}	dto.HTTPError	"Internal Server Error - Encountered an unexpected condition"
//	@Router		/admin/signups/{id}/approval [patch]
//
//	@Security	Bearer
func (u *signUpRequestHandlerImpl) ProcessSignUpRequest(ctx *gin.Context) {
	c, span := oteltrace.Tracer.Start(ParseContext(ctx), "handlers.ProcessSignUpRequest")
	defer span.End()

	currentUserId, err := GetCurrentUserId(c)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user")
		span.RecordError(err)
		dto.ErrorResponse(ctx, dto.NewForbiddenError(err, "forbidden"))
		return
	}

	span.SetAttributes(attribute.String("user_id", currentUserId.String()))

	var input dto.ApprovalRequest
	if err := ctx.ShouldBindJSON(&input); err != nil {
		span.SetStatus(codes.Error, "failed to bind json")
		span.RecordError(err)
		dto.ErrorResponse(ctx, dto.NewBadRequestError(err))
		return
	}

	id, err := uuid.Parse(ctx.Param("id"))
	if err != nil {
		span.SetStatus(codes.Error, "failed to parse id")
		span.RecordError(err)
		dto.ErrorResponse(ctx, dto.NewBadRequestError(err))
		return
	}

	input.ID = id
	input.CurrentUserID = currentUserId
	if err := validator.Validate(input); err != nil {
		span.SetStatus(codes.Error, "invalid request body")
		span.RecordError(err)
		dto.ErrorResponse(ctx, dto.NewBadRequestError(err))
		return
	}

	if err := u.usecase.ProcessSignUpRequest(c, input); err != nil {
		span.SetStatus(codes.Error, "failed to process signup request")
		span.RecordError(err)
		if errors.Is(err, usecase.ErrUserNotFound) {
			dto.ErrorResponse(ctx, dto.NewNotFoundError(err))
			return
		}

		if errors.Is(err, usecase.ErrSignUpRequestNotFound) {
			dto.ErrorResponse(ctx, dto.NewNotFoundError(err, "not found"))
			return
		}

		dto.ErrorResponse(ctx, dto.NewInternalError(err, "internal server error"))
		return
	}

	span.AddEvent(fmt.Sprintf("processed sign up request for %s and is_approved=%v", input.ID, input.IsAccepted))
	span.SetStatus(codes.Ok, fmt.Sprintf("processed sign up request for %s and is_approved=%v", input.ID, input.IsAccepted))
	ctx.JSON(http.StatusNoContent, nil)
}

// InviteUser godoc
//
//	@Summary	Invite user to sign up
//	@Tags		Sign Up Request
//	@Accept		json
//	@Produce	json
//	@Param		model	body	dto.InviteUserInput	true	"Invite user to sign up"
//	@Success	200		"OK"
//
// @Failure	401		{object}	dto.HTTPError	"Unauthorized - Authentication is required to access this resource"
// @Failure	403		{object}	dto.HTTPError	"Forbidden - User does not have permission to access this resource"
// @Failure	400		{object}	dto.HTTPError	"Bad Request - invalid request"
// @Failure	404		{object}	dto.HTTPError	"Not Found"
// @Failure	500		{object}	dto.HTTPError	"Internal Server Error - Encountered an unexpected condition"
// @Router		/admin/invite [post]
//
//	@Security	Bearer
func (u *signUpRequestHandlerImpl) InviteUser(ctx *gin.Context) {
	c, span := oteltrace.Tracer.Start(ParseContext(ctx), "handlers.InviteUser")
	defer span.End()

	currentUserId, err := GetCurrentUserId(c)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user id")
		span.RecordError(err)
		dto.ErrorResponse(ctx, dto.NewForbiddenError(err, "forbidden"))
		return
	}

	span.SetAttributes(attribute.String("user_id", currentUserId.String()))

	var input dto.InviteUserInput
	if err := ctx.ShouldBindJSON(&input); err != nil {
		span.SetStatus(codes.Error, "failed to bind json")
		span.RecordError(err)
		dto.ErrorResponse(ctx, dto.NewBadRequestError(err))
		return
	}

	input.CurrentUserID = currentUserId
	if err := validator.Validate(input); err != nil {
		span.SetStatus(codes.Error, "invalid request body")
		span.RecordError(err)
		dto.ErrorResponse(ctx, dto.NewBadRequestError(err))
		return
	}

	err = u.usecase.InviteUser(c, input)
	if err != nil {
		span.SetStatus(codes.Error, "failed to invite user")
		span.RecordError(err)
		dto.ErrorResponse(ctx, dto.NewErrorFromUsecase(err))
		return
	}

	span.AddEvent("successfully invited user", trace.WithAttributes(attribute.String("email", input.Email)))
	span.SetStatus(codes.Ok, fmt.Sprintf("successfully invited user %s", input.Email))
	ctx.JSON(http.StatusOK, gin.H{"message": "User has been invited"})
}

// SendConfirmEmail godoc
//
//	@Summary	Send confirm email
//	@Tags		Sign Up Request
//	@Accept		json
//	@Produce	json
//	@Param		model	body	dto.SendConfirmEmailInput	true	"Resend confirm email"
//	@Success	200		"OK"
//
// @Failure	401		{object}	dto.HTTPError	"Unauthorized - Authentication is required to access this resource"
// @Failure	403		{object}	dto.HTTPError	"Forbidden - User does not have permission to access this resource"
// @Failure	400		{object}	dto.HTTPError	"Bad Request - invalid request"
// @Failure	404		{object}	dto.HTTPError	"Not Found"
// @Failure	500		{object}	dto.HTTPError	"Internal Server Error - Encountered an unexpected condition"
// @Router		/send-confirm-email [post]
//
//	@Security	Bearer
func (u *signUpRequestHandlerImpl) SendConfirmEmail(ctx *gin.Context) {
	c, span := oteltrace.Tracer.Start(ParseContext(ctx), "handlers.InviteUser")
	defer span.End()

	var input dto.SendConfirmEmailInput
	if err := ctx.ShouldBindJSON(&input); err != nil {
		span.SetStatus(codes.Error, "failed to bind json")
		span.RecordError(err)
		dto.ErrorResponse(ctx, dto.NewBadRequestError(err))
		return
	}
	if err := validator.Validate(input); err != nil {
		span.SetStatus(codes.Error, "invalid request body")
		span.RecordError(err)
		dto.ErrorResponse(ctx, dto.NewBadRequestError(err))
		return
	}

	if err := u.usecase.SendConfirmEmail(c, input); err != nil {
		span.SetStatus(codes.Error, "failed to invite user")
		span.RecordError(err)
		dto.ErrorResponse(ctx, dto.NewErrorFromUsecase(err))
		return
	}

	span.AddEvent("successfully send confirm email", trace.WithAttributes(attribute.String("email", input.Email)))
	span.SetStatus(codes.Ok, fmt.Sprintf("successfully send confirm email %s", input.Email))
	ctx.JSON(http.StatusOK, gin.H{"message": "Email has been sent"})
}
