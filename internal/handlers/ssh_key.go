package handlers

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"

	"api-server/internal/dto"
	_ "api-server/internal/enums"
	user_keys "api-server/internal/usecase/ssh_key"
	"api-server/pkg/oteltrace"
	"api-server/pkg/otelzap"
	"api-server/pkg/validator"
)

type SshKeysHandler interface {
	ListSshKeys(ctx *gin.Context)
	AddNewSshKey(ctx *gin.Context)
	DeleteSshKey(ctx *gin.Context)
	GetSshKey(ctx *gin.Context)
}

type sshKeysHandlerImpl struct {
	usecase user_keys.SshKeyUsecase
}

func NewSshKeyHandler(usecase user_keys.SshKeyUsecase) SshKeysHandler {
	return &sshKeysHandlerImpl{
		usecase: usecase,
	}
}

// ListSshKeys godoc
//
//	@Summary	List user's ssh keys
//	@Tags		SSH Key
//	@Accept		json
//	@Produce	json
//	@Param		page		query		int						false	"pagination page number"	minimum(1)
//	@Param		per_page	query		int						false	"number of results per page"
//	@Param		order_by	query		enums.OrderByColumn		false	"order the result by field name"
//	@Param		sort		query		enums.OrderByDirection	false	"Sort result by ascending or descending"
//	@Success	200			{array}		dto.GetSSHKeyOutput		"List of user's SSH keys"
//	@Failure	400			{object}	dto.HTTPError			"Bad Request - invalid request"
//	@Failure	404			{object}	dto.HTTPError			"Not Found"
//	@Failure	500			{object}	dto.HTTPError			"Internal Server Error - Encountered an unexpected condition"
//	@Router		/users/keys [get]
//
//	@Security	Bearer
func (u *sshKeysHandlerImpl) ListSshKeys(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(ParseContext(c), "handlers.ListSshKeys",
		trace.WithAttributes(
			attribute.String("action", "LIST_SSH_KEYS"),
		))
	defer span.End()

	currentUserId, err := GetCurrentUserId(ctx)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "failed to get current user", err,
			zap.String("action", "LIST_SSH_KEYS"),
			zap.String("status", "failed"))
		dto.ErrorResponse(c, dto.NewForbiddenError(err, "forbidden"))
		return
	}

	var input dto.GetSSHKeyInput
	if err = c.ShouldBindQuery(&input); err != nil {
		span.SetStatus(codes.Error, "failed to bind query")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "failed to bind query", err,
			zap.String("action", "LIST_SSH_KEYS"),
			zap.String("status", "failed"))
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	otelzap.InfoWithContext(ctx, "retrieving all ssh keys",
		zap.String("action", "LIST_SSH_KEYS"))

	span.AddEvent("get all SSH keys")
	input.UserId = currentUserId
	if err := validator.Validate(input); err != nil {
		span.SetStatus(codes.Error, "failed to validate input")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	output, err := u.usecase.GetAllSSHKeys(ctx, input)
	if err != nil {
		span.SetStatus(codes.Error, "failed retrieve all ssh keys")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "failed retrieve all ssh keys", err,
			zap.String("action", "LIST_SSH_KEYS"),
			zap.String("status", "failed"))
		dto.ErrorResponse(c, dto.NewErrorFromUsecase(err))
		return
	}

	span.AddEvent("successfully retrieve all ssh keys")
	span.SetStatus(codes.Ok, "successfully retrieve all ssh keys")
	span.SetAttributes(attribute.String("status", "success"))
	otelzap.InfoWithContext(ctx, "successfully retrieve all ssh keys",
		zap.String("action", "LIST_SSH_KEYS"),
		zap.String("status", "success"))
	c.JSON(http.StatusOK, output)
}

// AddNewSshKey godoc
//
//	@Summary	Add new ssh keys
//	@Tags		SSH Key
//	@Accept		json
//	@Produce	json
//	@Param		model	body		dto.CreateSSHKeyInput	true	"SSH Key"
//	@Success	201		{object}	string					"Return created ssh key"
//	@Failure	400		{object}	dto.HTTPError			"Bad Request - invalid request"
//	@Failure	404		{object}	dto.HTTPError			"Not Found"
//	@Failure	500		{object}	dto.HTTPError			"Internal Server Error - Encountered an unexpected condition"
//	@Router		/users/keys [post]
//
//	@Security	Bearer
func (u *sshKeysHandlerImpl) AddNewSshKey(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(ParseContext(c), "handers.AddNewSshKey",
		trace.WithAttributes(
			attribute.String("action", "CREATE_SSH_KEY"),
		))
	defer span.End()

	currentUserId, err := GetCurrentUserId(ctx)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "failed to get current user", err,
			zap.String("action", "CREATE_SSH_KEY"),
			zap.String("status", "failed"))
		dto.ErrorResponse(c, dto.NewForbiddenError(err, "forbidden"))
		return
	}

	var input dto.CreateSSHKeyInput
	if err := c.ShouldBindJSON(&input); err != nil {
		span.SetStatus(codes.Error, "failed to bind json")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "failed to bind json", err,
			zap.String("action", "CREATE_SSH_KEY"),
			zap.String("status", "failed"))
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	input.UserId = currentUserId
	if err := validator.Validate(input); err != nil {
		span.SetStatus(codes.Error, "failed to validate input")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "invalid request body", err,
			zap.String("action", "CREATE_SSH_KEY"),
			zap.String("status", "failed"))
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	otelzap.InfoWithContext(ctx, "creating ssh key",
		zap.String("action", "CREATE_SSH_KEY"),
		zap.String("key_name", input.Title))

	input.UserId = currentUserId
	err = u.usecase.CreateSSHKey(c, input)
	if err != nil {
		span.SetStatus(codes.Error, "failed to create ssh key")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "failed to create ssh key", err,
			zap.String("action", "CREATE_SSH_KEY"),
			zap.String("key_name", input.Title),
			zap.String("status", "failed"))
		dto.ErrorResponse(c, dto.NewErrorFromUsecase(err))
		return
	}

	span.AddEvent(fmt.Sprintf("successfully created ssh key %s", input.Title))
	span.SetStatus(codes.Ok, fmt.Sprintf("successfully created ssh key %s", input.Title))
	span.SetAttributes(attribute.String("status", "success"))
	otelzap.InfoWithContext(ctx, fmt.Sprintf("successfully created ssh key %s", input.Title),
		zap.String("action", "CREATE_SSH_KEY"),
		zap.String("key_name", input.Title),
		zap.String("status", "success"))
	c.JSON(http.StatusOK, dto.NewHTTPResponse[dto.SSHKey]().WithMessage("SSH Key created successfully"))
}

// DeleteSshKey godoc
//
//	@Summary	Delete user ssh key
//	@Tags		SSH Key
//	@Accept		json
//	@Produce	json
//	@Param		key_id	path	string	true	"SSH key ID"
//	@Success	204		"No Content - Request has been successfully completed, but no response payload body"
//	@Failure	400		{object}	dto.HTTPError	"Bad Request - invalid request"
//	@Failure	404		{object}	dto.HTTPError	"Not Found"
//	Failure	500		{object}	dto.HTTPError	"Internal Server Error - Encountered an unexpected condition"
//	@Router		/users/keys/{key_id} [delete]
//
//	@Security	Bearer
func (u *sshKeysHandlerImpl) DeleteSshKey(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(ParseContext(c), "handlers.DeleteSshKey",
		trace.WithAttributes(
			attribute.String("action", "DELETE_SSH_KEY"),
		))
	defer span.End()

	currentUserId, err := GetCurrentUserId(ctx)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "failed to get current user", err,
			zap.String("action", "DELETE_SSH_KEY"),
			zap.String("status", "failed"))
		dto.ErrorResponse(c, dto.NewForbiddenError(err, "forbidden"))
		return
	}

	var input dto.DeleteSSHKeyInput
	keyId, err := uuid.Parse(c.Param("key_id"))
	if err != nil {
		span.SetStatus(codes.Error, "failed to parse key id from query param")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "failed to parse key id from query param", err,
			zap.String("action", "DELETE_SSH_KEY"),
			zap.String("key_id", c.Param("key_id")),
			zap.String("status", "failed"))
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	input.Id = keyId

	otelzap.InfoWithContext(ctx, "deleting ssh key",
		zap.String("action", "DELETE_SSH_KEY"),
		zap.String("key_id", input.Id.String()))

	input.UserId = currentUserId
	if err := validator.Validate(input); err != nil {
		span.SetStatus(codes.Error, "failed to validate input")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	err = u.usecase.DeleteSSHKey(c, input)
	if err != nil {
		span.SetStatus(codes.Error, "failed to delete ssh key")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "failed to delete ssh key", err,
			zap.String("action", "DELETE_SSH_KEY"),
			zap.String("key_id", input.Id.String()),
			zap.String("status", "failed"))
		dto.ErrorResponse(c, dto.NewErrorFromUsecase(err))
		return
	}

	span.AddEvent(fmt.Sprintf("successfully deleted ssh key %s", input.Id))
	span.SetStatus(codes.Ok, fmt.Sprintf("successfully deleted ssh key %s", input.Id))
	span.SetAttributes(attribute.String("status", "success"))
	otelzap.InfoWithContext(ctx, fmt.Sprintf("successfully deleted ssh key %s", input.Id),
		zap.String("action", "DELETE_SSH_KEY"),
		zap.String("key_id", input.Id.String()),
		zap.String("status", "success"))
	c.JSON(http.StatusOK, dto.NewHTTPResponse[dto.SSHKey]().WithMessage("SSH Key deleted successfully"))
}

// GetSshKey godoc
//
//	@Summary	Get user ssh key
//	@Tags		SSH Key
//	@Accept		json
//	@Produce	json
//	@Param		key_id	path		string						true	"SSH key ID"
//	@Success	200		{object}	dto.GetSingleSSHKeyOutput	"Return ssh key info"
//	@Failure	400		{object}	dto.HTTPError				"Bad Request - invalid request"
//	@Failure	404		{object}	dto.HTTPError				"Not Found"
//	@Failure	500		{object}	dto.HTTPError				"Internal Server Error - Encountered an unexpected condition"
//	@Router		/users/keys/{key_id} [get]
//
//	@Security	Bearer
func (u *sshKeysHandlerImpl) GetSshKey(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(ParseContext(c), "handlers.GetSshKey",
		trace.WithAttributes(
			attribute.String("action", "GET_SSH_KEY"),
		))
	defer span.End()

	currentUserId, err := GetCurrentUserId(ctx)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "failed to get current user", err,
			zap.String("action", "GET_SSH_KEY"),
			zap.String("status", "failed"))
		dto.ErrorResponse(c, dto.NewForbiddenError(err, "forbidden"))
		return
	}

	var input dto.GetSingleSSHKeyInput
	keyId, err := uuid.Parse(c.Param("key_id"))
	if err != nil {
		span.SetStatus(codes.Error, "failed to parse key id from query param")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "failed to parse key id from query param", err,
			zap.String("action", "GET_SSH_KEY"),
			zap.String("key_id", c.Param("key_id")),
			zap.String("status", "failed"))
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	input.Id = keyId

	otelzap.InfoWithContext(ctx, "getting ssh key",
		zap.String("action", "GET_SSH_KEY"),
		zap.String("key_id", input.Id.String()))

	input.UserId = currentUserId
	if err := validator.Validate(input); err != nil {
		span.SetStatus(codes.Error, "failed to validate input")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	sshKey, err := u.usecase.GetSSHKey(c, input)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get ssh key")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "failed to get ssh key", err,
			zap.String("action", "GET_SSH_KEY"),
			zap.String("key_id", input.Id.String()),
			zap.String("status", "failed"))
		dto.ErrorResponse(c, dto.NewErrorFromUsecase(err))
		return
	}

	span.AddEvent(fmt.Sprintf("successfully get ssh key %s", input.Id))
	span.SetStatus(codes.Ok, fmt.Sprintf("successfully get ssh key %s", input.Id))
	span.SetAttributes(attribute.String("status", "success"))
	otelzap.InfoWithContext(ctx, fmt.Sprintf("successfully get ssh key %s", input.Id),
		zap.String("action", "GET_SSH_KEY"),
		zap.String("key_id", input.Id.String()),
		zap.String("status", "success"))
	c.JSON(http.StatusOK, sshKey)
}
