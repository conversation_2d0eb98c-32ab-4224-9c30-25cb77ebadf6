package handlers_test

import (
	"bytes"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"api-server/internal/dto"
	"api-server/internal/enums"
	"api-server/internal/handlers"
	"api-server/internal/types"
	"api-server/internal/usecase/repository/mocks"
)

func TestCreateRepoAccessTokenHandler(t *testing.T) {
	gin.SetMode(gin.TestMode)

	mockUserID, _ := uuid.Parse("d2de43e6-fde6-4deb-a1d0-2d5c2c2e1038")

	repoType := enums.RepoType_Models
	namespace := "namespace"
	repoName := "repo-name"
	repoID := *types.NewRepoID(repoType, namespace, repoName)

	now, _ := time.Parse(time.RFC3339, "2024-12-15T15:30:00Z")

	testcases := []struct {
		name             string
		mockFn           func(u *mocks.MockRepoUsecase)
		requestBody      dto.CreateRepoAccessTokenRequest
		setUserIDContext bool
		expectBody       string
		expectHTTPCode   int
	}{
		{
			name:   "should return error if cannot get user id from context",
			mockFn: func(u *mocks.MockRepoUsecase) {},
			requestBody: dto.CreateRepoAccessTokenRequest{
				Name:      "test",
				Scopes:    []enums.RepoAccessTokenScope{"api"},
				ExpiresAt: now,
				RepoID:    repoID,
				UserID:    mockUserID,
			},
			setUserIDContext: false,
			expectBody:       "{\"code\":401,\"message\":\"Failed to get current user id\"}",
			expectHTTPCode:   http.StatusUnauthorized,
		},
		{
			name:   "should return error if validating Scopes fail",
			mockFn: func(u *mocks.MockRepoUsecase) {},
			requestBody: dto.CreateRepoAccessTokenRequest{
				Name:      "test",
				Scopes:    []enums.RepoAccessTokenScope{"invalid"},
				ExpiresAt: now,
				RepoID:    repoID,
				UserID:    mockUserID,
			},
			setUserIDContext: true,
			expectBody:       "{\"code\":400,\"message\":\"Key: 'CreateRepoAccessTokenRequest.Scopes[0]' Error:Field validation for 'Scopes[0]' failed on the 'oneof' tag\"}",
			expectHTTPCode:   http.StatusBadRequest,
		},
		{
			name: "should return Internal error if CreateRepoAccessToken return error",
			mockFn: func(u *mocks.MockRepoUsecase) {
				u.On("CreateRepoAccessToken", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			requestBody: dto.CreateRepoAccessTokenRequest{
				Name:      "test",
				Scopes:    []enums.RepoAccessTokenScope{"api"},
				ExpiresAt: now,
				RepoID:    repoID,
				UserID:    mockUserID,
			},
			setUserIDContext: true,
			expectBody:       "{\"code\":500,\"message\":\"Error\"}",
			expectHTTPCode:   http.StatusInternalServerError,
		},
		{
			name: "should pass",
			mockFn: func(u *mocks.MockRepoUsecase) {
				u.On("CreateRepoAccessToken", mock.Anything, mock.Anything).Return(&dto.CreateRepoAccessTokenResponse{
					Data: &dto.RepoAccessToken{
						Name:      "test",
						Scopes:    "api",
						ExpiresAt: now,
						Revoked:   false,
					},
				}, nil)
			},
			requestBody: dto.CreateRepoAccessTokenRequest{
				Name:      "test",
				Scopes:    []enums.RepoAccessTokenScope{"api"},
				ExpiresAt: now,
				RepoID:    repoID,
				UserID:    mockUserID,
			},
			setUserIDContext: true,
			expectBody:       "{\"data\":{\"id\":\"00000000-0000-0000-0000-000000000000\",\"name\":\"test\",\"scopes\":\"api\",\"revoked\":false,\"expires_at\":\"2024-12-15T15:30:00Z\",\"created_at\":\"0001-01-01T00:00:00Z\",\"updated_at\":\"0001-01-01T00:00:00Z\"}}",
			expectHTTPCode:   http.StatusOK,
		},
	}

	for _, testcase := range testcases {
		t.Run(testcase.name, func(t *testing.T) {
			usecase := &mocks.MockRepoUsecase{}
			testcase.mockFn(usecase)
			handler := handlers.NewRepositoryHandler(nil, usecase)

			rr := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(rr)
			c.Params = gin.Params{
				{Key: enums.REPO_TYPE, Value: repoType.String()},
				{Key: enums.NAMESPACE, Value: namespace},
				{Key: enums.REPO_NAME, Value: repoName},
			}
			if testcase.setUserIDContext {
				c.Set(enums.USER_ID, mockUserID)
			}
			body, _ := json.Marshal(testcase.requestBody)
			c.Request = httptest.NewRequest(http.MethodPost, "/repositories/:repo_type/:namespace/:repo_name/access_tokens", bytes.NewBuffer(body))
			handler.CreateRepoAccessToken(c)

			assert.Equal(t, testcase.expectHTTPCode, rr.Code)
			assert.Equal(t, testcase.expectBody, rr.Body.String())
		})
	}
}

func TestListRepoAccessTokenHandler(t *testing.T) {
	gin.SetMode(gin.TestMode)

	mockUserID, _ := uuid.Parse("d2de43e6-fde6-4deb-a1d0-2d5c2c2e1038")

	repoType := enums.RepoType_Models
	namespace := "namespace"
	repoName := "repo-name"
	repoID := *types.NewRepoID(repoType, namespace, repoName)

	now, _ := time.Parse(time.RFC3339, "2024-12-15T15:30:00Z")

	testcases := []struct {
		name             string
		mockFn           func(u *mocks.MockRepoUsecase)
		requestBody      dto.ListRepoAccessTokenRequest
		setUserIDContext bool
		expectBody       string
		expectHTTPCode   int
	}{
		{
			name:   "should return error if cannot get user id in context",
			mockFn: func(u *mocks.MockRepoUsecase) {},
			requestBody: dto.ListRepoAccessTokenRequest{
				UserID: mockUserID,
				RepoID: repoID,
			},
			setUserIDContext: false,
			expectBody:       "{\"code\":401,\"message\":\"Failed to get current user id\"}",
			expectHTTPCode:   http.StatusUnauthorized,
		},
		{
			name: "should return Internal error if ListRepoAccessToken return error",
			mockFn: func(u *mocks.MockRepoUsecase) {
				u.On("ListRepoAccessToken", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			requestBody: dto.ListRepoAccessTokenRequest{
				UserID: mockUserID,
				RepoID: repoID,
			},
			setUserIDContext: true,
			expectBody:       "{\"code\":500,\"message\":\"Error\"}",
			expectHTTPCode:   http.StatusInternalServerError,
		},
		{
			name: "should pass",
			mockFn: func(u *mocks.MockRepoUsecase) {
				u.On("ListRepoAccessToken", mock.Anything, mock.Anything).Return(&dto.ListRepoAccessTokenResponse{
					Data: &[]dto.RepoAccessToken{
						{
							ID:          mockUserID,
							Name:        "test",
							Scopes:      "api",
							AccessToken: "**************************",
							Revoked:     false,
							ExpiresAt:   now,
						},
					},
					Pagination: &dto.Pagination{
						Total:    1,
						PageNo:   1,
						PageSize: 10,
					},
				}, nil)
			},
			requestBody: dto.ListRepoAccessTokenRequest{
				UserID: mockUserID,
				RepoID: repoID,
			},
			setUserIDContext: true,
			expectBody:       "{\"data\":[{\"id\":\"d2de43e6-fde6-4deb-a1d0-2d5c2c2e1038\",\"name\":\"test\",\"access_token\":\"**************************\",\"scopes\":\"api\",\"revoked\":false,\"expires_at\":\"2024-12-15T15:30:00Z\",\"created_at\":\"0001-01-01T00:00:00Z\",\"updated_at\":\"0001-01-01T00:00:00Z\"}],\"total\":1,\"page_no\":1,\"page_size\":10}",
			expectHTTPCode:   http.StatusOK,
		},
	}

	for _, testcase := range testcases {
		t.Run(testcase.name, func(t *testing.T) {
			usecase := &mocks.MockRepoUsecase{}
			testcase.mockFn(usecase)
			handler := handlers.NewRepositoryHandler(nil, usecase)

			rr := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(rr)
			c.Params = gin.Params{
				{Key: enums.REPO_TYPE, Value: repoType.String()},
				{Key: enums.NAMESPACE, Value: namespace},
				{Key: enums.REPO_NAME, Value: repoName},
			}
			if testcase.setUserIDContext {
				c.Set(enums.USER_ID, mockUserID)
			}
			body, _ := json.Marshal(testcase.requestBody)
			c.Request = httptest.NewRequest(http.MethodGet, "/repositories/:repo_type/:namespace/:repo_name/access_tokens", bytes.NewBuffer(body))
			handler.ListRepoAccessToken(c)

			assert.Equal(t, testcase.expectHTTPCode, rr.Code)
			assert.Equal(t, testcase.expectBody, rr.Body.String())
		})
	}
}

func TestDeleteRepoAccessTokenHandler(t *testing.T) {
	gin.SetMode(gin.TestMode)

	mockUserID, _ := uuid.Parse("d2de43e6-fde6-4deb-a1d0-2d5c2c2e1038")

	repoType := enums.RepoType_Models
	namespace := "namespace"
	repoName := "repo-name"
	repoID := *types.NewRepoID(repoType, namespace, repoName)

	testcases := []struct {
		name             string
		mockFn           func(u *mocks.MockRepoUsecase)
		userID           string
		repoID           string
		accessTokenID    string
		setUserIDContext bool
		expectBody       string
		expectHTTPCode   int
	}{
		{
			name:             "should return error if cannot get user id in context",
			mockFn:           func(u *mocks.MockRepoUsecase) {},
			repoID:           repoID.String(),
			accessTokenID:    "d2de43e6-fde6-4deb-a1d0-2d5c2c2e1038",
			setUserIDContext: false,
			expectBody:       "{\"code\":401,\"message\":\"Failed to get current user id\"}",
			expectHTTPCode:   http.StatusUnauthorized,
		},
		{
			name: "should return Internal error if DeleteAccessToken return error",
			mockFn: func(u *mocks.MockRepoUsecase) {
				u.On("DeleteRepoAccessToken", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(errors.New("error"))
			},
			repoID:           repoID.String(),
			accessTokenID:    "d2de43e6-fde6-4deb-a1d0-2d5c2c2e1038",
			setUserIDContext: true,
			expectBody:       "{\"code\":500,\"message\":\"Error\"}",
			expectHTTPCode:   http.StatusInternalServerError,
		},
		{
			name: "should pass",
			mockFn: func(u *mocks.MockRepoUsecase) {
				u.On("DeleteRepoAccessToken", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil)
			},
			repoID:           repoID.String(),
			accessTokenID:    "d2de43e6-fde6-4deb-a1d0-2d5c2c2e1038",
			setUserIDContext: true,
			expectBody:       "",
			expectHTTPCode:   http.StatusNoContent,
		},
	}

	for _, testcase := range testcases {
		t.Run(testcase.name, func(t *testing.T) {
			usecase := &mocks.MockRepoUsecase{}
			testcase.mockFn(usecase)
			handler := handlers.NewRepositoryHandler(nil, usecase)

			rr := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(rr)
			c.Params = gin.Params{
				{Key: enums.REPO_TYPE, Value: repoType.String()},
				{Key: enums.NAMESPACE, Value: namespace},
				{Key: enums.REPO_NAME, Value: repoName},
				{Key: "id", Value: testcase.accessTokenID},
			}
			if testcase.setUserIDContext {
				c.Set(enums.USER_ID, mockUserID)
			}
			c.Request = httptest.NewRequest(http.MethodDelete, "/repositories/:repo_type/:namespace/:repo_name/access_tokens/:id", nil)

			handler.DeleteRepoAccessToken(c)

			assert.Equal(t, testcase.expectHTTPCode, rr.Code)
			assert.Equal(t, testcase.expectBody, rr.Body.String())
		})
	}
}
