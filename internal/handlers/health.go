package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"api-server/internal/dto"
	healthcheck_usecase "api-server/internal/usecase/healthcheck"

	"api-server/pkg/oteltrace"
)

type HealthCheckHandler interface {
	Explore(c *gin.Context)
}

type healthCheckHandlerImpl struct {
	healthUsecase healthcheck_usecase.HealthcheckUsecase
}

// NewHealthCheckHandler creates a new instance of HealthCheckHandler.
// It initializes the handler with the health check use case.
//
// Parameters:
//   - healthUsecase: The health check use case.
//
// Returns:
//   - HealthCheckHandler: A new HealthCheckHandler instance.
func NewHealthCheckHandler(healthUsecase healthcheck_usecase.HealthcheckUsecase) HealthCheckHandler {
	return &healthCheckHandlerImpl{
		healthUsecase: healthUsecase,
	}
}

// Explore performs a health check of the service.
// It uses the health check use case to verify the service status and returns a JSON response.
// It also includes OpenTelemetry tracing.
//
// Parameters:
//   - c: The Gin context for the HTTP request.
//
// Health check godoc
//
//	@Summary		Health check
//	@Decscription	Health check
//	@Tags			Health
//	@Accept			json
//	@Produce		json
//	@Success		200	string		string			Return	list	of	hardwares"
//	@Failure		400	{object}	dto.HTTPError	"Bad Request - invalid request"
//	@Failure		404	{object}	dto.HTTPError	"Not Found"
//	@Failure		500	{object}	dto.HTTPError	"Internal Server Error - Encountered an unexpected condition"
//	@Router			/explore [get]
//
//	@Security		Bearer
func (h *healthCheckHandlerImpl) Explore(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(ParseContext(c), "handers.ListHealths")
	defer span.End()

	if err := h.healthUsecase.Check(ctx); err != nil {
		dto.ErrorResponse(c, dto.NewInternalError(err))
		return
	}

	c.JSON(http.StatusOK, nil)
}
