package handlers_test

import (
	"bytes"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"api-server/internal/dto"
	"api-server/internal/enums"
	"api-server/internal/handlers"
	"api-server/internal/usecase/access_token/mocks"
)

func TestCreateAccessTokenHandler(t *testing.T) {
	gin.SetMode(gin.TestMode)

	mockUserID, _ := uuid.Parse("d2de43e6-fde6-4deb-a1d0-2d5c2c2e1038")

	now := time.Now()

	testcases := []struct {
		name             string
		mockFn           func(u *mocks.MockAccessTokenUsecase)
		requestBody      dto.CreateAccessTokenRequest
		setUserIDContext bool
		expectBody       string
		expectHTTPCode   int
	}{
		{
			name:             "should return Unauthorized error if missing userID from context",
			mockFn:           func(u *mocks.MockAccessTokenUsecase) {},
			requestBody:      dto.CreateAccessTokenRequest{},
			setUserIDContext: false,
			expectBody:       "{\"code\":401,\"message\":\"Unauthorized\"}",
			expectHTTPCode:   http.StatusUnauthorized,
		},
		{
			name:   "should fail validate Scopes in request body",
			mockFn: func(u *mocks.MockAccessTokenUsecase) {},
			requestBody: dto.CreateAccessTokenRequest{
				Name:      "name",
				Scopes:    []enums.RepoAccessTokenScope{},
				ExpiresAt: nil,
			},
			setUserIDContext: true,
			expectBody:       "{\"code\":400,\"message\":\"Key: 'CreateAccessTokenRequest.Scopes' Error:Field validation for 'Scopes' failed on the 'min' tag\"}",
			expectHTTPCode:   http.StatusBadRequest,
		},
		{
			name:   "should fail validate Scopes in request body",
			mockFn: func(u *mocks.MockAccessTokenUsecase) {},
			requestBody: dto.CreateAccessTokenRequest{
				Name:      "name",
				Scopes:    []enums.RepoAccessTokenScope{enums.RepoAccessTokenScope("placeholder")},
				ExpiresAt: nil,
			},
			setUserIDContext: true,
			expectBody:       "{\"code\":400,\"message\":\"Key: 'CreateAccessTokenRequest.Scopes[0]' Error:Field validation for 'Scopes[0]' failed on the 'oneof' tag\"}",
			expectHTTPCode:   http.StatusBadRequest,
		},
		{
			name: "should return Internal error if CreateAccessToken return error",
			mockFn: func(u *mocks.MockAccessTokenUsecase) {
				u.On("CreateAccessToken", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			requestBody: dto.CreateAccessTokenRequest{
				Name:      "name",
				Scopes:    []enums.RepoAccessTokenScope{enums.RepoAccessToken_API},
				ExpiresAt: &now,
			},
			setUserIDContext: true,
			expectBody:       "{\"code\":500,\"message\":\"Error\"}",
			expectHTTPCode:   http.StatusInternalServerError,
		},
		{
			name: "should pass",
			mockFn: func(u *mocks.MockAccessTokenUsecase) {
				u.On("CreateAccessToken", mock.Anything, mock.Anything).Return(&dto.CreateAccessTokenResponse{
					Data: &dto.AccessToken{
						ID:          mockUserID,
						Name:        "a",
						AccessToken: "@aqweasd123",
						Revoked:     false,
						ExpiresAt:   nil,
					},
				}, nil)
			},
			requestBody: dto.CreateAccessTokenRequest{
				Name:      "name",
				Scopes:    []enums.RepoAccessTokenScope{enums.RepoAccessToken_API},
				ExpiresAt: &now,
			},
			setUserIDContext: true,
			expectBody:       "{\"data\":{\"id\":\"d2de43e6-fde6-4deb-a1d0-2d5c2c2e1038\",\"name\":\"a\",\"access_token\":\"@aqweasd123\",\"scopes\":\"\",\"revoked\":false,\"expires_at\":null,\"created_at\":\"0001-01-01T00:00:00Z\",\"updated_at\":\"0001-01-01T00:00:00Z\"}}",
			expectHTTPCode:   http.StatusOK,
		},
	}

	for _, testcase := range testcases {
		t.Run(testcase.name, func(t *testing.T) {
			usecase := &mocks.MockAccessTokenUsecase{}
			testcase.mockFn(usecase)
			handler := handlers.NewAccessTokenHandler(usecase)

			rr := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(rr)
			if testcase.setUserIDContext {
				c.Set(enums.USER_ID, mockUserID)
			}
			body, _ := json.Marshal(testcase.requestBody)
			c.Request = httptest.NewRequest(http.MethodPost, "/", bytes.NewBuffer(body))
			handler.CreateNewAccessToken(c)

			assert.Equal(t, testcase.expectHTTPCode, rr.Code)
			assert.Equal(t, testcase.expectBody, rr.Body.String())
		})
	}
}

func TestListAccessTokenHandler(t *testing.T) {
	gin.SetMode(gin.TestMode)

	mockUserID, _ := uuid.Parse("d2de43e6-fde6-4deb-a1d0-2d5c2c2e1038")

	testcases := []struct {
		name             string
		mockFn           func(u *mocks.MockAccessTokenUsecase)
		requestBody      dto.ListAccessTokenRequest
		setUserIDContext bool
		expectBody       string
		expectHTTPCode   int
	}{
		{
			name:             "should return Unauthorized error if missing userID from context",
			mockFn:           func(u *mocks.MockAccessTokenUsecase) {},
			requestBody:      dto.ListAccessTokenRequest{},
			setUserIDContext: false,
			expectBody:       "{\"code\":401,\"message\":\"Unauthorized\"}",
			expectHTTPCode:   http.StatusUnauthorized,
		},
		{
			name: "should return Internal error if ListAccessToken return error",
			mockFn: func(u *mocks.MockAccessTokenUsecase) {
				u.On("ListAccessToken", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			requestBody:      dto.ListAccessTokenRequest{},
			setUserIDContext: true,
			expectBody:       "{\"code\":500,\"message\":\"Error\"}",
			expectHTTPCode:   http.StatusInternalServerError,
		},
		{
			name: "should pass",
			mockFn: func(u *mocks.MockAccessTokenUsecase) {
				u.On("ListAccessToken", mock.Anything, mock.Anything).Return(&dto.ListAccessTokenResponse{
					Data: &[]dto.AccessToken{
						{
							ID:          mockUserID,
							Name:        "a",
							AccessToken: "@aqweasd123",
							Scopes:      "api",
							Revoked:     false,
							ExpiresAt:   nil,
						},
					},
				}, nil)
			},
			requestBody:      dto.ListAccessTokenRequest{},
			setUserIDContext: true,
			expectBody:       "{\"data\":[{\"id\":\"d2de43e6-fde6-4deb-a1d0-2d5c2c2e1038\",\"name\":\"a\",\"access_token\":\"@aqweasd123\",\"scopes\":\"api\",\"revoked\":false,\"expires_at\":null,\"created_at\":\"0001-01-01T00:00:00Z\",\"updated_at\":\"0001-01-01T00:00:00Z\"}]}",
			expectHTTPCode:   http.StatusOK,
		},
	}

	for _, testcase := range testcases {
		t.Run(testcase.name, func(t *testing.T) {
			usecase := &mocks.MockAccessTokenUsecase{}
			testcase.mockFn(usecase)
			handler := handlers.NewAccessTokenHandler(usecase)

			rr := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(rr)
			if testcase.setUserIDContext {
				c.Set(enums.USER_ID, mockUserID)
			}
			body, _ := json.Marshal(testcase.requestBody)
			c.Request = httptest.NewRequest(http.MethodGet, "/", bytes.NewBuffer(body))
			handler.ListAccessToken(c)

			assert.Equal(t, testcase.expectHTTPCode, rr.Code)
			assert.Equal(t, testcase.expectBody, rr.Body.String())
		})
	}
}

func TestDeleteAccessTokenHandler(t *testing.T) {
	gin.SetMode(gin.TestMode)

	mockUserID, _ := uuid.Parse("d2de43e6-fde6-4deb-a1d0-2d5c2c2e1038")

	testcases := []struct {
		name             string
		mockFn           func(u *mocks.MockAccessTokenUsecase)
		accessTokenID    string
		setUserIDContext bool
		expectBody       string
		expectHTTPCode   int
	}{
		{
			name:             "should return Unauthorized error if missing userID from context",
			mockFn:           func(u *mocks.MockAccessTokenUsecase) {},
			accessTokenID:    "d2de43e6-fde6-4deb-a1d0-2d5c2c2e1038",
			setUserIDContext: false,
			expectBody:       "{\"code\":401,\"message\":\"Unauthorized\"}",
			expectHTTPCode:   http.StatusUnauthorized,
		},
		{
			name: "should return Internal error if DeleteAccessToken return error",
			mockFn: func(u *mocks.MockAccessTokenUsecase) {
				u.On("DeleteAccessToken", mock.Anything, mock.Anything, mock.Anything).Return(errors.New("error"))
			},
			accessTokenID:    "d2de43e6-fde6-4deb-a1d0-2d5c2c2e1038",
			setUserIDContext: true,
			expectBody:       "{\"code\":500,\"message\":\"Error\"}",
			expectHTTPCode:   http.StatusInternalServerError,
		},
		{
			name: "should pass",
			mockFn: func(u *mocks.MockAccessTokenUsecase) {
				u.On("DeleteAccessToken", mock.Anything, mock.Anything, mock.Anything).Return(nil)
			},
			accessTokenID:    "d2de43e6-fde6-4deb-a1d0-2d5c2c2e1038",
			setUserIDContext: true,
			expectBody:       "",
			expectHTTPCode:   http.StatusNoContent,
		},
	}

	for _, testcase := range testcases {
		t.Run(testcase.name, func(t *testing.T) {
			usecase := &mocks.MockAccessTokenUsecase{}
			testcase.mockFn(usecase)
			handler := handlers.NewAccessTokenHandler(usecase)

			rr := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(rr)
			c.AddParam("id", testcase.accessTokenID)
			if testcase.setUserIDContext {
				c.Set(enums.USER_ID, mockUserID)
			}
			c.Request = httptest.NewRequest(http.MethodDelete, "/", nil)
			handler.DeleteAccessToken(c)

			assert.Equal(t, testcase.expectHTTPCode, rr.Code)
			assert.Equal(t, testcase.expectBody, rr.Body.String())
		})
	}
}

func TestVerifyAccessTokenHandler(t *testing.T) {
	gin.SetMode(gin.TestMode)

	testcases := []struct {
		name           string
		mockFn         func(u *mocks.MockAccessTokenUsecase)
		requestBody    dto.VerifyAccessTokenRequest
		expectBody     string
		expectHTTPCode int
	}{
		{
			name:   "should return Bad Request error if request body is invalid",
			mockFn: func(u *mocks.MockAccessTokenUsecase) {},
			requestBody: dto.VerifyAccessTokenRequest{
				AccessToken: "",
			},
			expectBody:     "{\"code\":400,\"message\":\"Key: 'VerifyAccessTokenRequest.AccessToken' Error:Field validation for 'AccessToken' failed on the 'required' tag\"}",
			expectHTTPCode: http.StatusBadRequest,
		},
		{
			name:   "should fail bind JSON body",
			mockFn: func(u *mocks.MockAccessTokenUsecase) {},
			requestBody: dto.VerifyAccessTokenRequest{
				AccessToken: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJeyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJeyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ999eyJhYWwiOiJhYWwxIiwiYW1yIjpbeyJtZXRob2QiOiJwYXNzd29yZCIsInRpbWVzdGFtcCI6MTc0Mzk5NjQyOX1dLCJhcHBfbWV0YWRhdGEiOnsicHJvdmlkZXIiOiJlbWFpbCIsInByb3ZpZGVycyI6WyJlbWFpbCJdfSwiYXVkIjpbImF1dGhlbnRpY2F0ZWQiXSwiZW1haWwiOiJtYWNyb2V4cGFuc2lvbkBnbWFpbC5jb20iLCJleHAiOjE3NDQwMDAwMjksImlhdCI6MTc0Mzk5NjQyOSwiaXNfYW5vbnltb3VzIjpmYWxzZSwicGhvbmUiOiIiLCJyb2xlIjoiYXV0aGVudGljYXRlZCIsInNlc3Npb25faWQiOiI3MzNmNjYzZi1hNTE2LTQxNmQtYTUzYy1jN2EyODQ0YTExOTQiLCJzdWIiOiI1Yzg3NDZjOC1hYzhkLTQ2MTAtODMzYi1kYjMwYWY4OWVlM2MiLCJ1c2VyX2lkIjoiNWM4NzQ2YzgtYWM4ZC00NjEwLTgzM2ItZGIzMGFmODllZTNjIiwidXNlcl9tZXRhZGF0YSI6eyJlbWFpbCI6Im1hY3JvZXhwYW5zaW9uQGdtYWlsLmNvbSIsImVtYWlsX3ZlcmlmaWVkIjpmYWxzZSwicGhvbmVfdmVyaWZpZWQiOmZhbHNlLCJzdWIiOiI1Yzg3NDZjOC1hYzhkLTQ2MTAtODMzYi1kYjMwYWY4OWVlM2MifX0.C_u0NUenIX2Ta_X82kN9xy4LOcwnx7IwskcXfnQ7INw",
			},
			expectBody:     "{\"code\":400,\"message\":\"Key: 'VerifyAccessTokenRequest.AccessToken' Error:Field validation for 'AccessToken' failed on the 'max' tag\"}",
			expectHTTPCode: http.StatusBadRequest,
		},
		{
			name: "should return Internal error if VerifyAccessToken returns error",
			mockFn: func(u *mocks.MockAccessTokenUsecase) {
				u.On("VerifyAccessToken", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			requestBody: dto.VerifyAccessTokenRequest{
				AccessToken: "test-token",
			},
			expectBody:     "{\"code\":500,\"message\":\"Error\"}",
			expectHTTPCode: http.StatusInternalServerError,
		},
		{
			name: "should pass with valid token",
			mockFn: func(u *mocks.MockAccessTokenUsecase) {
				u.On("VerifyAccessToken", mock.Anything, mock.Anything).Return(&dto.VerifyAccessTokenResponse{
					Valid: true,
				}, nil)
			},
			requestBody: dto.VerifyAccessTokenRequest{
				AccessToken: "valid-token",
			},
			expectBody:     "{\"data\":{\"valid\":true}}",
			expectHTTPCode: http.StatusOK,
		},
		{
			name: "should pass with invalid token",
			mockFn: func(u *mocks.MockAccessTokenUsecase) {
				u.On("VerifyAccessToken", mock.Anything, mock.Anything).Return(&dto.VerifyAccessTokenResponse{
					Valid: false,
				}, nil)
			},
			requestBody: dto.VerifyAccessTokenRequest{
				AccessToken: "invalid-token",
			},
			expectBody:     "{\"data\":{\"valid\":false}}",
			expectHTTPCode: http.StatusOK,
		},
	}

	for _, testcase := range testcases {
		t.Run(testcase.name, func(t *testing.T) {
			usecase := &mocks.MockAccessTokenUsecase{}
			testcase.mockFn(usecase)
			handler := handlers.NewAccessTokenHandler(usecase)

			rr := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(rr)
			body, _ := json.Marshal(testcase.requestBody)
			c.Request = httptest.NewRequest(http.MethodPost, "/", bytes.NewBuffer(body))
			handler.VerifyAccessToken(c)

			assert.Equal(t, testcase.expectHTTPCode, rr.Code)
			assert.Equal(t, testcase.expectBody, rr.Body.String())
		})
	}
}
