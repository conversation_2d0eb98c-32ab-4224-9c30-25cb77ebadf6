package handlers

import (
	"errors"
	"fmt"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"

	"api-server/configs"
	"api-server/internal/dto"
	"api-server/internal/usecase"
	"api-server/internal/usecase/user"
	"api-server/internal/utils"
	"api-server/pkg/oteltrace"
	"api-server/pkg/validator"
)

type UserHandler interface {
	ListUsers(ctx *gin.Context)
	UpdateUserRole(ctx *gin.Context)
	GetCurrentUser(ctx *gin.Context)
	DeleteUser(ctx *gin.Context)
	UploadUserAvatar(ctx *gin.Context)
	DeleteUserAvatar(ctx *gin.Context)
	ChangePassword(ctx *gin.Context)
}

type userHandlerImpl struct {
	config  *configs.GlobalConfig
	usecase user.UserUsecase
}

// NewUserUserHandler creates a new instance of UserHandler.
// It initializes the handler with the global configuration and user use case.
//
// Parameters:
//   - config: The global configuration.
//   - usecase: The user use case.
//
// Returns:
//   - UserHandler: A new UserHandler instance.
func NewUserUserHandler(config *configs.GlobalConfig, usecase user.UserUsecase) UserHandler {
	return &userHandlerImpl{
		usecase: usecase,
		config:  config,
	}
}

// ListUsers godoc
//
//	@Summary	List and filter users
//	@Tags		User
//	@Accept		json
//	@Produce	json
//	@Param		page		query		int				false	"pagination page number"	minimum(1)
//	@Param		per_page	query		int				false	"number of results per page"
//	@Param		order_by	query		string			false	"order the result by field name"			Enums(created_at, updated_at)
//	@Param		sort		query		string			false	"Sort result by ascending or descending"	Enums(asc, desc)
//	@Success	200			{array}		dto.User		"List of matched user results"
//	@Failure	400			{object}	dto.HTTPError	"Bad Request - invalid request"
//	@Failure	404			{object}	dto.HTTPError	"Not Found"
//	@Failure	500			{object}	dto.HTTPError	"Internal Server Error - Encountered an unexpected condition"
//	@Router		/users [get]
//
//	@Security	Bearer
func (u *userHandlerImpl) ListUsers(ctx *gin.Context) {
	c, span := oteltrace.Tracer.Start(ParseContext(ctx), "handlers.ListUsers")
	defer span.End()

	_, err := GetCurrentUserId(c)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user")
		span.RecordError(err)
		dto.ErrorResponse(ctx, dto.NewForbiddenError(err, "forbidden"))
		return
	}

	var input dto.ListUsersInput
	if err := ctx.ShouldBindQuery(&input); err != nil {
		span.SetStatus(codes.Error, "failed to bind query")
		span.RecordError(err)
		dto.ErrorResponse(ctx, dto.NewBadRequestError(err, "invalid request"))
		return
	}

	if ctx.Query("not_in_org") != "" {
		orgId, err := uuid.Parse(ctx.Query("not_in_org"))
		if err != nil {
			span.SetStatus(codes.Error, "failed to parse user id from path")
			span.RecordError(err)
			dto.ErrorResponse(ctx, dto.NewBadRequestError(err))
			return
		}

		input.Except.NotInOrgId = &orgId
	}

	users, err := u.usecase.ListUsers(c, input)
	if err != nil {
		span.SetStatus(codes.Error, "failed to retrieve users")
		span.RecordError(err)
		dto.ErrorResponse(ctx, dto.NewErrorFromUsecase(err))
		return
	}

	span.AddEvent("successfully retrieved list of users")
	span.SetStatus(codes.Ok, "successfully retrieved list of users")
	ctx.JSON(http.StatusOK, users)
}

// UpdateUserRole godoc
//
//	@Summary	Update user role
//	@Tags		User
//	@Accept		json
//	@Produce	json
//	@Param		user_id	path	string						true	"User ID"
//	@Param		model	body	dto.UserUpdateRoleRequest	true	"Update user role"
//	@Success	204		"No Content - Request has been successfully completed, but no response payload body"
//	@Failure	400		{object}	dto.HTTPError	"Bad Request - invalid request"
//	@Failure	404		{object}	dto.HTTPError	"Not Found"
//	@Failure	500		{object}	dto.HTTPError	"Internal Server Error - Encountered an unexpected condition"
//	@Router		/users/{user_id}/role [patch]
//
//	@Security	Bearer
func (u *userHandlerImpl) UpdateUserRole(ctx *gin.Context) {
	c, span := oteltrace.Tracer.Start(ParseContext(ctx), "handlers.UpdateUserRole")
	defer span.End()

	currentUserId, err := GetCurrentUserId(c)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user")
		span.RecordError(err)
		dto.ErrorResponse(ctx, dto.NewForbiddenError(err, "forbidden"))
		return
	}

	userId, err := uuid.Parse(ctx.Param("user_id"))
	if err != nil {
		span.SetStatus(codes.Error, "failed to parse user id from path")
		span.RecordError(err)
		dto.ErrorResponse(ctx, dto.NewBadRequestError(err))
		return
	}

	var input dto.UpdateRoleInput
	if err := ctx.ShouldBindJSON(&input); err != nil {
		span.SetStatus(codes.Error, "failed to bind json")
		span.RecordError(err)
		dto.ErrorResponse(ctx, dto.NewBadRequestError(err))
		return
	}

	input.UserID = userId
	if userId == currentUserId {
		err := fmt.Errorf("user should not update their own role")
		span.SetStatus(codes.Error, err.Error())
		span.RecordError(err)
		dto.ErrorResponse(ctx, dto.NewForbiddenError(err, "forbidden"))
		return
	}

	if err := validator.Validate(input); err != nil {
		span.SetStatus(codes.Error, "invalid request body")
		span.RecordError(err)
		dto.ErrorResponse(ctx, dto.NewBadRequestError(err))
		return
	}

	if err := u.usecase.UpdateRoleUser(c, input); err != nil {
		span.SetStatus(codes.Error, "failed to update user role")
		span.RecordError(err)
		dto.ErrorResponse(ctx, dto.NewErrorFromUsecase(err))
		return
	}

	span.AddEvent(
		fmt.Sprintf("successfully updated user role to %s for user_id: %s", input.Role, input.UserID),
		trace.WithAttributes(
			attribute.String("role", input.Role.String()),
			attribute.String("user_id", input.UserID.String()),
		),
	)
	span.SetStatus(codes.Ok, fmt.Sprintf("successfully updated user role to %s for user_id: %s", input.Role, input.UserID))
	ctx.Status(http.StatusNoContent)
}

// GetCurrentUser godoc
//
//	@Summary	Get current user information
//	@Tags		User
//	@Accept		json
//	@Produce	json
//	@Success	200	{object}	dto.User		"Current user information"
//	@Failure	403	{object}	dto.HTTPError	"Forbidden - user not authorized"
//	@Failure	500	{object}	dto.HTTPError	"Internal Server Error - Encountered an unexpected condition"
//	@Router		/users/me [get]
//
//	@Security	Bearer
func (u *userHandlerImpl) GetCurrentUser(ctx *gin.Context) {
	c, span := oteltrace.Tracer.Start(ParseContext(ctx), "handlers.GetCurrentUser")
	defer span.End()

	currentUserId, err := GetCurrentUserId(c)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user")
		span.RecordError(err)
		dto.ErrorResponse(ctx, dto.NewForbiddenError(err, "forbidden"))
		return
	}

	currentUser, err := u.usecase.GetCurrentUser(c, currentUserId)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get user info")
		span.RecordError(err)
		dto.ErrorResponse(ctx, dto.NewErrorFromUsecase(err))
		return
	}

	span.AddEvent("successfully get current user")
	span.SetStatus(codes.Ok, "successfully get current user")
	ctx.JSON(http.StatusOK, currentUser)
}

// DeleteUser godoc
//
//	@Summary	Delete user information
//	@Tags		User
//	@Accept		json
//	@Produce	json
//	@Failure	403	{object}	dto.HTTPError	"Forbidden - user not authorized"
//	@Failure	500	{object}	dto.HTTPError	"Internal Server Error - Encountered an unexpected condition"
//	@Router		/users/{user_id} [delete]
//
//	@Security	Bearer
func (u *userHandlerImpl) DeleteUser(ctx *gin.Context) {
	c, span := oteltrace.Tracer.Start(ParseContext(ctx), "handlers.DeleteUser")
	defer span.End()

	currentUserId, err := GetCurrentUserId(c)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user")
		span.RecordError(err)
		dto.ErrorResponse(ctx, dto.NewForbiddenError(err, "forbidden"))
		return
	}

	userId, err := uuid.Parse(ctx.Param("user_id"))
	if err != nil {
		span.SetStatus(codes.Error, "failed to parse user id")
		span.RecordError(err)
		dto.ErrorResponse(ctx, dto.NewBadRequestError(err))
		return
	}

	// Prevent self-deletion
	if userId == currentUserId {
		err := errors.New("cannot delete yourself")
		span.SetStatus(codes.Error, err.Error())
		span.RecordError(err)
		dto.ErrorResponse(ctx, dto.NewForbiddenError(err))
		return
	}

	if err := u.usecase.DeleteUser(c, currentUserId, userId); err != nil {
		span.SetStatus(codes.Error, "failed to delete user")
		span.RecordError(err)
		dto.ErrorResponse(ctx, dto.NewErrorFromUsecase(err))
		return
	}

	span.AddEvent(fmt.Sprintf("successfully deleted user %s", userId.String()))
	span.SetStatus(codes.Ok, fmt.Sprintf("successfully deleted user %s", userId.String()))
	ctx.JSON(http.StatusOK, gin.H{"message": "User deleted successfully"})
}

// UploadUserAvatar godoc
//
//	@Summary	Upload avatar
//	@Tags		User
//	@Accept		multipart/form-data
//	@Produce	json
//	@Param		file	formData	file			true	"Avatar file"
//	@Param		user_id	path		string			true	"User ID"
//	@Failure	400		{object}	dto.HTTPError	"Bad Request - invalid request"
//	@Failure	404		{object}	dto.HTTPError	"Not Found"
//	@Failure	500		{object}	dto.HTTPError	"Internal Server Error - Encountered an unexpected condition"
//	@Router		/users/{user_id}/avatar [post]
//
//	@Security	Bearer
func (o *userHandlerImpl) UploadUserAvatar(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(c.Request.Context(), "handlers.UploadUserAvatar")
	defer span.End()

	currentUserId, err := GetCurrentUserId(ParseContext(c))
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewForbiddenError(err, "forbidden - invalid user"))
		return
	}

	userId, err := uuid.Parse(c.Param("user_id"))
	if err != nil {
		span.SetStatus(codes.Error, "failed to parse user id")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err, "failed to parse user id"))
		return
	}

	if currentUserId != userId {
		span.SetStatus(codes.Error, usecase.ErrNoPermission.Error())
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewForbiddenError(usecase.ErrNoPermission, http.StatusText(http.StatusForbidden)))
		return
	}

	file, err := c.FormFile("file")
	if err != nil {
		span.SetStatus(codes.Error, "failed to parse avatar file")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err, "failed to parse avatar file"))
		return
	}

	err = utils.ValidateImage(o.config.FileConfig.ImageMaxSize, file)
	if err != nil {
		span.SetStatus(codes.Error, "invalid content file")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err, "invalid content file"))
		return
	}

	input := dto.UploadUserAvatarInput{
		UserId: userId,
		File:   file,
	}
	avatarUrl, err := o.usecase.UploadAvatarUser(ctx, input)
	if err != nil {
		span.SetStatus(codes.Error, "failed to upload user avatar")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewErrorFromUsecase(err))
		return
	}

	span.AddEvent("successfully updated user avatar")
	span.SetStatus(codes.Ok, "successfully updated user avatar")
	c.JSON(http.StatusOK, gin.H{"avatar_url": avatarUrl})
}

// DeleteuserAvatar godoc
//
//	@Summary	Delete user avatar
//	@Tags		User
//	@Produce	json
//	@Param		user_id	path		string			true	"User ID"
//	@Failure	400		{object}	dto.HTTPError	"Bad Request - invalid request"
//	@Failure	404		{object}	dto.HTTPError	"Not Found"
//	@Failure	500		{object}	dto.HTTPError	"Internal Server Error - Encountered an unexpected condition"
//	@Router		/users/{user_id}/avatar [delete]
//
//	@Security	Bearer
func (o *userHandlerImpl) DeleteUserAvatar(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(c.Request.Context(), "handlers.DeleteUserAvatar")
	defer span.End()

	currentUserId, err := GetCurrentUserId(ParseContext(c))
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewForbiddenError(err))
		return
	}

	userId, err := uuid.Parse(c.Param("user_id"))
	if err != nil {
		span.SetStatus(codes.Error, "failed to parse user id")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	if currentUserId != userId {
		span.SetStatus(codes.Error, usecase.ErrNoPermission.Error())
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewForbiddenError(usecase.ErrNoPermission, http.StatusText(http.StatusForbidden)))
		return
	}

	if err := o.usecase.DeleteAvatarUser(ctx, userId); err != nil {
		span.SetStatus(codes.Error, "failed to delete user avatar")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewErrorFromUsecase(err))
		return
	}

	span.AddEvent("successfully remvoed user avatar")
	span.SetStatus(codes.Ok, "successfully remvoed user avatar")
	c.JSON(http.StatusOK, gin.H{
		"message": "Remove Avatar successfully",
	})
}

// ChangePassword godoc
//
//	@Summary	Change user password
//	@Tags		User
//	@Accept		json
//	@Produce	json
//	@Param		model	body	dto.ChangePasswordInput	true	"Change password request"
//	@Success	204		"No Content - Password has been successfully changed"
//	@Failure	400		{object}	dto.HTTPError	"Bad Request - invalid request"
//	@Failure	403		{object}	dto.HTTPError	"Forbidden - user not authorized"
//	@Failure	500		{object}	dto.HTTPError	"Internal Server Error - Encountered an unexpected condition"
//	@Router		/users/me/password [put]
//
//	@Security	Bearer
func (u *userHandlerImpl) ChangePassword(ctx *gin.Context) {
	c, span := oteltrace.Tracer.Start(ParseContext(ctx), "handlers.ChangePassword")
	defer span.End()

	currentUserId, err := GetCurrentUserId(c)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user id")
		span.RecordError(err)
		dto.ErrorResponse(ctx, dto.NewForbiddenError(err, "failed to get current user id"))
		return
	}

	var input dto.ChangePasswordInput
	if err := ctx.ShouldBindJSON(&input); err != nil {
		span.SetStatus(codes.Error, "failed to bind json")
		span.RecordError(err)
		dto.ErrorResponse(ctx, dto.NewBadRequestError(err))
		return
	}

	if err := validator.Validate(input); err != nil {
		span.SetStatus(codes.Error, "invalid request body")
		span.RecordError(err)
		dto.ErrorResponse(ctx, dto.NewBadRequestError(err))
		return
	}

	userAccessToken := strings.TrimPrefix(ctx.GetHeader("Authorization"), "Bearer ")
	if err := u.usecase.ChangePassword(c, currentUserId, userAccessToken, input); err != nil {
		span.SetStatus(codes.Error, "failed to change password")
		span.RecordError(err)
		dto.ErrorResponse(ctx, dto.NewErrorFromUsecase(err))
		return
	}

	span.AddEvent("successfully changed password")
	span.SetStatus(codes.Ok, "successfully changed password")
	ctx.JSON(http.StatusNoContent, nil)
}
