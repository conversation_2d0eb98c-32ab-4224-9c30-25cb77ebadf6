package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"api-server/configs"
	"api-server/internal/dto"
	"api-server/internal/usecase/hardware"
	"api-server/pkg/oteltrace"
)

type HardwareHandler interface {
	ListHardwares(c *gin.Context)
	ListGPUNodes(c *gin.Context)
}

type hardwareHandlerImpl struct {
	config          *configs.GlobalConfig
	hardwareUsecase hardware.HardwareUsecase
}

func NewHardwareHandler(config *configs.GlobalConfig, hardwareUsecase hardware.HardwareUsecase) HardwareHandler {
	return &hardwareHandlerImpl{
		config:          config,
		hardwareUsecase: hardwareUsecase,
	}
}

// ListHardwares godoc
//
//	@Summary		List hardwares
//	@Decscription	To list benchmarking and deployment hardwares in the system
//	@Tags			Machine
//	@Accept			json
//	@Produce		json
//	@Success		200	{array}		dto.ListHardwareResponse	"Return list of hardwares"
//	@Failure		400	{object}	dto.HTTPError				"Bad Request - invalid request"
//	@Failure		404	{object}	dto.HTTPError				"Not Found"
//	@Failure		500	{object}	dto.HTTPError				"Internal Server Error - Encountered an unexpected condition"
//	@Router			/hardwares [get]
//
//	@Security		Bearer
func (h *hardwareHandlerImpl) ListHardwares(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(ParseContext(c), "handers.ListHardwares")
	defer span.End()

	var req dto.ListHardwareRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	resp, err := h.hardwareUsecase.ListHardware(ctx, req)
	if err != nil {
		dto.ErrorResponse(c, dto.NewInternalError(err))
		return
	}

	c.JSON(http.StatusOK, resp)
}

// ListGPUNodes godoc
//
//	@Summary		List GPU nodes
//	@Decscription	To list GPU nodes from k8s
//	@Tags			Machine
//	@Accept			json
//	@Produce		json
//	@Param			short	query		bool						false	"Return short version of the response, default is true"
//	@Success		200		{array}		dto.ListGPUNodesResponse	"Return list of gpu nodes"
//	@Failure		400		{object}	dto.HTTPError				"Bad Request - invalid request"
//	@Failure		404		{object}	dto.HTTPError				"Not Found"
//	@Failure		500		{object}	dto.HTTPError				"Internal Server Error - Encountered an unexpected condition"
//	@Router			/hardwares [get]
//
//	@Security		Bearer
func (h *hardwareHandlerImpl) ListGPUNodes(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(ParseContext(c), "handers.ListHardwares")
	defer span.End()

	short := c.Query("short") != "false" // default to true unless explicitly set to false

	resp, err := h.hardwareUsecase.ListGPUNodes(ctx, short)
	if err != nil {
		dto.ErrorResponse(c, dto.NewInternalError(err))
		return
	}

	c.JSON(http.StatusOK, resp)
}

// RegisterMachine godoc
//
//	@Summary		Register machine
//	@Description	For benchmarking and deployment to register themselves with the system
//	@Tags			Machine
//	@Accept			json
//	@Produce		json
//	@Param			model	body		dto.MachineRegistrationRequest	true	"Register machine"
//	@Success		201		{object}	dto.Machine						"Return created machine"
//	@Failure		400		{object}	dto.HTTPError					"Bad Request - invalid request"
//	@Failure		404		{object}	dto.HTTPError					"Not Found"
//	@Failure		500		{object}	dto.HTTPError					"Internal Server Error - Encountered an unexpected condition"
//	@Router			/machines [post]
//
//	@Security		Bearer
// func (h *Handler) RegisterMachine(ctx *gin.Context) {

// }

// DeregisterMachine godoc
//
//	@Summary		De-register machine
//	@Description	For benchmarking and deployment to register themselves with the system
//	@Tags			Machine
//	@Accept			json
//	@Produce		json
//	@Param			host	path	string	true	"Machine host"
//	@Success		204		"No Content - Request has been successfully completed, but no response payload body"
//	@Failure		400		{object}	dto.HTTPError	"Bad Request - invalid request"
//	@Failure		404		{object}	dto.HTTPError	"Not Found"
//	@Failure		500		{object}	dto.HTTPError	"Internal Server Error - Encountered an unexpected condition"
//	@Router			/machines/{host} [delete]
//
//	@Security		Bearer
// func (h *Handler) DeregisterMachine(ctx *gin.Context) {

// }
