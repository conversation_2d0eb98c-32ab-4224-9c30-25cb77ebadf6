package handlers_test

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"image"
	"image/color"
	"image/png"
	"log"
	"mime/multipart"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"api-server/configs"
	"api-server/internal/dto"
	"api-server/internal/entities"
	"api-server/internal/enums"
	"api-server/internal/handlers"
	"api-server/internal/usecase"
	"api-server/internal/usecase/organization/mocks"
)

func createMockContentFile() []byte {
	img := image.NewRGBA(image.Rect(0, 0, 100, 100))
	col := color.RGBA{R: 255, G: 0, B: 0, A: 255}
	for y := 0; y < 100; y++ {
		for x := 0; x < 100; x++ {
			img.Set(x, y, col)
		}
	}

	var buf bytes.Buffer
	png.Encode(&buf, img)

	return buf.Bytes()
}

func TestCreateOrganization(t *testing.T) {
	gin.SetMode(gin.TestMode)

	type dependencies struct {
		usecase *mocks.MockOrganizationUsecase
	}

	tests := []struct {
		name          string
		input         dto.CreateOrganizationInput
		ctxValue      uuid.UUID
		mockFn        func(d *dependencies)
		expectErr     error
		expStatusCode int
	}{
		{
			name: "should fail validate OrganizationType request body",
			input: dto.CreateOrganizationInput{
				Name:             "org",
				PathName:         "org",
				OrganizationType: enums.OrganizationType("placeholder"),
			},
			mockFn:        func(d *dependencies) {},
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusBadRequest,
			expectErr: errors.New(
				"Key: 'CreateOrganizationInput.OrganizationType' Error:Field validation for 'OrganizationType' failed on the 'oneof' tag",
			),
		},
		{
			name: "should fail validate image size",
			input: dto.CreateOrganizationInput{
				Name:             "org",
				PathName:         "org",
				OrganizationType: enums.OrganizationType_Company,
				File:             &multipart.FileHeader{},
			},
			mockFn:        func(d *dependencies) {},
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusBadRequest,
			expectErr:     errors.New("Invalid content file"),
		},
		{
			name: "should fail validate name because of Gitlab reserved keyword",
			input: dto.CreateOrganizationInput{
				Name:             "admin",
				PathName:         "org",
				OrganizationType: enums.OrganizationType_Company,
			},
			mockFn:        func(d *dependencies) {},
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusBadRequest,
			expectErr: errors.New(
				"Admin is a reserved keyword. Please choose different input",
			),
		},
		{
			name: "should return error when CreateOrganization returns error",
			input: dto.CreateOrganizationInput{
				Name:             "org",
				PathName:         "org",
				OrganizationType: enums.OrganizationType_Company,
			},
			mockFn: func(d *dependencies) {
				d.usecase.On("CreateOrganization", mock.Anything, mock.Anything).
					Return(nil, errors.New("error"))
			},
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusInternalServerError,
			expectErr:     errors.New("Error"),
		},
		{
			name: "should return error when GetCurrentUserId returns error",
			input: dto.CreateOrganizationInput{
				Name:             "Test Organization",
				PathName:         "org",
				OrganizationType: enums.OrganizationType_Company,
			},
			mockFn:        func(d *dependencies) {},
			expStatusCode: http.StatusUnauthorized,
			expectErr: dto.NewForbiddenError(
				errors.New("User id claims not found"),
				"Forbidden",
			),
		},
		{
			name: "should create organization successfully",
			input: dto.CreateOrganizationInput{
				Name:             "org",
				PathName:         "org",
				OrganizationType: enums.OrganizationType_Company,
			},
			mockFn: func(d *dependencies) {
				d.usecase.On("CreateOrganization", mock.Anything, mock.Anything).Return(&dto.CreateOrganizationOutput{}, nil)
			},
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusCreated,
			expectErr:     nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := dependencies{
				usecase: new(mocks.MockOrganizationUsecase),
			}
			tt.mockFn(&d)

			handler := handlers.NewOrganizationHandler(&configs.GlobalConfig{
				FileConfig: &configs.FileConfig{
					ImageMaxSize: 10,
				},
			}, d.usecase)

			// Setup Gin Context
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Set("user_id", tt.ctxValue)

			body := &bytes.Buffer{}
			writer := multipart.NewWriter(body)
			writer.WriteField("name", tt.input.Name)
			writer.WriteField("path_name", tt.input.PathName)
			writer.WriteField("organization_type", string(tt.input.OrganizationType))
			if tt.input.File != nil {
				part, _ := writer.CreateFormFile("file", "mock-avatar.png")
				part.Write(createMockContentFile())
			}
			writer.WriteField("interest", tt.input.Interest)
			writer.Close()

			c.Request = httptest.NewRequest(http.MethodPost, "/organizations", body)
			c.Request.Header.Set("Content-Type", writer.FormDataContentType())

			handler.CreateOrganization(c)

			assert.Equal(t, tt.expStatusCode, w.Code)
			if tt.expectErr != nil {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expectErr.Error(), response.Message)
				assert.Equal(t, tt.expStatusCode, w.Code)
			} else {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, http.StatusCreated, w.Code)
			}
		})
	}
}

func TestDeleteOrganization(t *testing.T) {
	gin.SetMode(gin.TestMode)

	type dependencies struct {
		usecase *mocks.MockOrganizationUsecase
	}

	tests := []struct {
		name          string
		input         dto.DeleteOrganizationInput
		ctxValue      uuid.UUID
		mockFn        func(d *dependencies)
		expectErr     error
		expStatusCode int
	}{
		{
			name: "should return error when DeleteOrganization returns error",

			input: dto.DeleteOrganizationInput{
				Id: uuid.New(),
			},
			mockFn: func(d *dependencies) {
				d.usecase.On("DeleteOrganization", mock.Anything, mock.Anything).
					Return(errors.New("Error"))
			},
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusInternalServerError,
			expectErr:     errors.New("Error"),
		},
		{
			name: "should delete organization successfully",
			input: dto.DeleteOrganizationInput{
				Id: uuid.New(),
			},
			mockFn: func(d *dependencies) {
				d.usecase.On("DeleteOrganization", mock.Anything, mock.Anything).Return(nil)
			},
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusOK,
			expectErr:     nil,
		},
		{
			name: "should return error when GetCurrentUserId returns error",
			input: dto.DeleteOrganizationInput{
				Id: uuid.New(),
			},
			mockFn:        func(d *dependencies) {},
			expStatusCode: http.StatusUnauthorized,
			expectErr: dto.NewForbiddenError(
				errors.New("User id claims not found"),
				"Forbidden",
			),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := dependencies{
				usecase: new(mocks.MockOrganizationUsecase),
			}
			tt.mockFn(&d)

			handler := handlers.NewOrganizationHandler(nil, d.usecase)

			// Setup Gin Context
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Set("user_id", tt.ctxValue)
			c.Params = gin.Params{
				{Key: "org_id", Value: tt.input.Id.String()},
			}
			c.Request = httptest.NewRequest(http.MethodDelete, "/organizations", nil)

			handler.DeleteOrganization(c)

			assert.Equal(t, tt.expStatusCode, w.Code)
			if tt.expectErr != nil {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expectErr.Error(), response.Message)
				assert.Equal(t, tt.expStatusCode, w.Code)
			} else {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expStatusCode, w.Code)
			}
		})
	}
}

func TestInviteUserOrg(t *testing.T) {
	gin.SetMode(gin.TestMode)

	type dependencies struct {
		usecase *mocks.MockOrganizationUsecase
	}

	userid := uuid.New()
	expireTime := time.Date(2026, 1, 2, 15, 4, 5, 0, time.UTC)
	tests := []struct {
		name          string
		input         dto.InviteOrgMemberInput
		ctxValue      uuid.UUID
		mockFn        func(d *dependencies)
		expectErr     error
		expStatusCode int
	}{
		{
			name: "should return error when InviteUser returns error",
			input: dto.InviteOrgMemberInput{
				OrgId:    uuid.New(),
				UserId:   uuid.New(),
				Role:     "owner",
				ExpireAt: &expireTime,
			},
			mockFn: func(d *dependencies) {
				d.usecase.On("InviteUser", mock.Anything, mock.Anything).Return(errors.New("error"))
			},
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusInternalServerError,
			expectErr:     errors.New("Error"),
		},
		{
			name: "should invite user successfully",
			input: dto.InviteOrgMemberInput{
				OrgId:    uuid.New(),
				UserId:   uuid.New(),
				Role:     "owner",
				ExpireAt: &expireTime,
			},
			mockFn: func(d *dependencies) {
				d.usecase.On("InviteUser", mock.Anything, mock.Anything).Return(nil)
			},
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusOK,
			expectErr:     nil,
		},
		{
			name: "should return error when self invite",
			input: dto.InviteOrgMemberInput{
				OrgId:    uuid.New(),
				UserId:   userid,
				Role:     "owner",
				ExpireAt: &expireTime,
			},
			mockFn: func(d *dependencies) {
			},
			ctxValue:      userid,
			expStatusCode: http.StatusBadRequest,
			expectErr:     errors.New("Cannot invite your self"),
		},
		{
			name: "should fail validate Role in request body",
			input: dto.InviteOrgMemberInput{
				OrgId:  uuid.New(),
				UserId: uuid.New(),
				Role:   "admin",
			},
			mockFn:        func(d *dependencies) {},
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusBadRequest,
			expectErr: errors.New(
				"Key: 'InviteOrgMemberInput.Role' Error:Field validation for 'Role' failed on the 'oneof' tag",
			),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := dependencies{
				usecase: new(mocks.MockOrganizationUsecase),
			}
			tt.mockFn(&d)

			handler := handlers.NewOrganizationHandler(nil, d.usecase)

			// Setup Gin Context
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Set("user_id", tt.ctxValue)
			inputJSON, _ := json.Marshal(tt.input)
			c.Request = httptest.NewRequest(
				http.MethodPost,
				"/organizations/:org_id/invite",
				strings.NewReader(string(inputJSON)),
			)
			c.Params = gin.Params{
				{Key: "org_id", Value: tt.input.OrgId.String()},
			}

			handler.InviteUser(c)
			assert.Equal(t, tt.expStatusCode, w.Code)
			if tt.expectErr != nil {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expectErr.Error(), response.Message)
				assert.Equal(t, tt.expStatusCode, w.Code)
			} else {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expStatusCode, w.Code)
			}
		})
	}
}

func TestRemoveUserOrg(t *testing.T) {
	gin.SetMode(gin.TestMode)

	type dependencies struct {
		usecase *mocks.MockOrganizationUsecase
	}

	orgId := uuid.New()
	memberId := uuid.New()
	tests := []struct {
		name          string
		input         dto.RemoveMemberOrganizaionInput
		ctxValue      uuid.UUID
		mockFn        func(d *dependencies)
		expectErr     error
		expStatusCode int
	}{
		{
			name:          "should return error when GetCurrentUserId returns error",
			mockFn:        func(d *dependencies) {},
			expStatusCode: http.StatusForbidden,
			expectErr:     errors.New("Forbidden"),
		},
		{
			name: "should return error when self remove",
			input: dto.RemoveMemberOrganizaionInput{
				OrgId:    orgId,
				MemberId: memberId,
			},
			mockFn: func(d *dependencies) {
				d.usecase.On("RemoveMember", mock.Anything, mock.Anything).
					Return(errors.New("error"))
			},
			ctxValue:      memberId,
			expStatusCode: http.StatusForbidden,
			expectErr:     errors.New("Cannot remove yourself"),
		},
		{
			name: "should return error when RemoveMember returns error",
			input: dto.RemoveMemberOrganizaionInput{
				OrgId:    orgId,
				MemberId: memberId,
			},
			mockFn: func(d *dependencies) {
				d.usecase.On("RemoveMember", mock.Anything, mock.Anything).
					Return(errors.New("Error"))
			},
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusInternalServerError,
			expectErr:     errors.New("Error"),
		},
		{
			name: "should remove user successfully",
			input: dto.RemoveMemberOrganizaionInput{
				OrgId:    orgId,
				MemberId: memberId,
			},
			mockFn: func(d *dependencies) {
				d.usecase.On("RemoveMember", mock.Anything, mock.Anything).Return(nil)
			},
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusOK,
			expectErr:     nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := dependencies{
				usecase: new(mocks.MockOrganizationUsecase),
			}
			tt.mockFn(&d)

			handler := handlers.NewOrganizationHandler(nil, d.usecase)

			// Setup Gin Context
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Set("user_id", tt.ctxValue)
			c.Request = httptest.NewRequest(
				http.MethodDelete,
				"/organizations/:org_id/members/:member_id",
				nil,
			)
			c.Params = gin.Params{
				{Key: "org_id", Value: orgId.String()},
				{Key: "member_id", Value: memberId.String()},
			}

			handler.RemoveMember(c)
			assert.Equal(t, tt.expStatusCode, w.Code)
		})
	}
}

func TestUpdateMemberOrg(t *testing.T) {
	gin.SetMode(gin.TestMode)

	type dependencies struct {
		usecase *mocks.MockOrganizationUsecase
	}

	expireTime := time.Date(2026, 1, 2, 15, 4, 5, 0, time.UTC)
	orgId := uuid.New()
	memberId := uuid.New()
	tests := []struct {
		name          string
		input         dto.UpdateMemberOrganizaionInput
		ctxValue      uuid.UUID
		mockFn        func(d *dependencies)
		expectErr     error
		expStatusCode int
	}{
		{
			name:          "should return error when GetCurrentUserId returns error",
			mockFn:        func(d *dependencies) {},
			expStatusCode: http.StatusForbidden,
			expectErr:     errors.New("Forbidden"),
		},
		{
			name: "should return error when self update",
			input: dto.UpdateMemberOrganizaionInput{
				OrgId:    orgId,
				MemberId: memberId,
				Role:     enums.OrgRole_Owner,
				ExpireAt: &expireTime,
			},
			mockFn:        func(d *dependencies) {},
			ctxValue:      memberId,
			expStatusCode: http.StatusForbidden,
			expectErr:     errors.New("Cannot update yourself"),
		},
		{
			name: "should return error when UpdateMember returns error",
			input: dto.UpdateMemberOrganizaionInput{
				OrgId:    orgId,
				MemberId: memberId,
				Role:     enums.OrgRole_Owner,
				ExpireAt: &expireTime,
			},
			mockFn: func(d *dependencies) {
				d.usecase.On("UpdateMember", mock.Anything, mock.Anything).
					Return(errors.New("Error"))
			},
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusInternalServerError,
			expectErr:     errors.New("Error"),
		},
		{
			name: "should update member org successfully",
			input: dto.UpdateMemberOrganizaionInput{
				OrgId:    orgId,
				MemberId: memberId,
				Role:     enums.OrgRole_Owner,
				ExpireAt: &expireTime,
			},
			mockFn: func(d *dependencies) {
				d.usecase.On("UpdateMember", mock.Anything, mock.Anything).Return(nil)
			},
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusOK,
			expectErr:     nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := dependencies{
				usecase: new(mocks.MockOrganizationUsecase),
			}
			tt.mockFn(&d)

			handler := handlers.NewOrganizationHandler(nil, d.usecase)

			// Setup Gin Context
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Set("user_id", tt.ctxValue)
			inputJSON, _ := json.Marshal(tt.input)
			c.Request = httptest.NewRequest(
				http.MethodPut,
				"/organizations/:org_id/members/:member_id",
				strings.NewReader(string(inputJSON)),
			)
			c.Params = gin.Params{
				{Key: "org_id", Value: orgId.String()},
				{Key: "member_id", Value: memberId.String()},
			}

			handler.UpdateMember(c)
			assert.Equal(t, tt.expStatusCode, w.Code)
		})
	}
}

func TestUploadOrganizationAvatar(t *testing.T) {
	gin.SetMode(gin.TestMode)

	userId := uuid.New()
	orgId := uuid.New()

	type dependencies struct {
		usecase *mocks.MockOrganizationUsecase
	}

	tests := []struct {
		name            string
		inp             dto.UploadOrganizationAvatarInput
		invalidFormFile bool
		imageMaxSize    int64
		mockFn          func(d *dependencies)
		expectErr       error
		ctxValue        uuid.UUID
		expStatusCode   int
	}{
		{
			name:            "should return error if no form file",
			ctxValue:        uuid.New(),
			invalidFormFile: true,
			inp: dto.UploadOrganizationAvatarInput{
				OrgID:         orgId,
				CurrentUserId: userId,
			},
			mockFn:        func(d *dependencies) {},
			expStatusCode: http.StatusBadRequest,
			expectErr:     errors.New("Failed to get file from form"),
		},
		{
			name:     "should return error if image file size exceeds limit",
			ctxValue: uuid.New(),
			inp: dto.UploadOrganizationAvatarInput{
				OrgID:         orgId,
				CurrentUserId: userId,
			},
			imageMaxSize:  10,
			mockFn:        func(d *dependencies) {},
			expStatusCode: http.StatusBadRequest,
			expectErr:     errors.New("Invalid content file"),
		},
		{
			name:          "should return error if upload organization avatar fail",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusInternalServerError,
			inp: dto.UploadOrganizationAvatarInput{
				OrgID:         orgId,
				CurrentUserId: userId,
			},
			imageMaxSize: 5 * 1024 * 1024,
			mockFn: func(d *dependencies) {
				d.usecase.On("UploadAvatarOrganization", mock.Anything, mock.Anything).
					Return("", errors.New("Internal server error"))
			},
			expectErr: errors.New("Internal server error"),
		},
		{
			name:          "should return error when GetCurrentUserId returns error",
			expStatusCode: http.StatusForbidden,
			mockFn: func(d *dependencies) {
				d.usecase.On("GetCurrentUserId", mock.Anything, mock.Anything).
					Return(nil, errors.New("Forbidden"))
			},
			expectErr: errors.New("Forbidden"),
		},
		{
			name:          "should upload organization avatar successfully",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusOK,
			imageMaxSize:  5 * 1024 * 1024,
			inp: dto.UploadOrganizationAvatarInput{
				OrgID:         orgId,
				CurrentUserId: userId,
			},
			mockFn: func(d *dependencies) {
				d.usecase.On("UploadAvatarOrganization", mock.Anything, mock.Anything).
					Return("http://localhost/sample.svg", nil)
			},
		},
		{
			name:          "should return error if UploadAvatarOrganization return ErrUserNotFound",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusNotFound,
			imageMaxSize:  5 * 1024 * 1024,
			inp: dto.UploadOrganizationAvatarInput{
				OrgID:         orgId,
				CurrentUserId: userId,
			},
			mockFn: func(d *dependencies) {
				d.usecase.On("UploadAvatarOrganization", mock.Anything, mock.Anything).
					Return("", usecase.ErrUserNotFound)
			},
			expectErr: usecase.ErrUserNotFound,
		},
		{
			name:          "should return error if UploadAvatarOrganization return ErrOrganizationNotFound",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusNotFound,
			imageMaxSize:  5 * 1024 * 1024,
			inp: dto.UploadOrganizationAvatarInput{
				OrgID:         orgId,
				CurrentUserId: userId,
			},
			mockFn: func(d *dependencies) {
				d.usecase.On("UploadAvatarOrganization", mock.Anything, mock.Anything).
					Return("", usecase.ErrOrganizationNotFound)
			},
			expectErr: errors.New("Organization not found"),
		},
		{
			name:          "should return error if UploadAvatarOrganization return ErrNoPermission",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusForbidden,
			imageMaxSize:  5 * 1024 * 1024,
			inp: dto.UploadOrganizationAvatarInput{
				OrgID:         orgId,
				CurrentUserId: userId,
			},
			mockFn: func(d *dependencies) {
				d.usecase.On("UploadAvatarOrganization", mock.Anything, mock.Anything).
					Return("", usecase.ErrNoPermission)
			},
			expectErr: usecase.ErrNoPermission,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup Gin context
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)

			// Set user_id in context
			c.Set("user_id", tt.ctxValue)
			log.Println(c.Value("user_id"))

			c.Params = gin.Params{
				{Key: "org_id", Value: orgId.String()},
			}

			body := &bytes.Buffer{}
			writer := multipart.NewWriter(body)
			writer.WriteField("org_id", orgId.String())
			writer.WriteField("current_user_id", userId.String())
			if !tt.invalidFormFile {
				part, _ := writer.CreateFormFile("file", "mock-avatar.png")
				part.Write(createMockContentFile())
			}
			writer.Close()

			// Set the request body
			// Create an HTTP POST request with the multipart body
			c.Request = httptest.NewRequest(
				http.MethodPost,
				fmt.Sprintf("/organizations/%s/avatar", orgId.String()),
				body,
			)
			c.Request.Header.Set("Content-Type", writer.FormDataContentType())

			d := &dependencies{
				usecase: &mocks.MockOrganizationUsecase{},
			}
			tt.mockFn(d)
			h := handlers.NewOrganizationHandler(&configs.GlobalConfig{
				FileConfig: &configs.FileConfig{
					ImageMaxSize: tt.imageMaxSize,
				},
			}, d.usecase)

			// Call the handler
			h.UploadOrganizationAvatar(c)

			// Assert the status code
			assert.Equal(t, tt.expStatusCode, w.Code)

			if tt.expectErr != nil {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expectErr.Error(), response.Message)
				assert.Equal(t, tt.expStatusCode, w.Code)
			} else {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
			}
		})
	}
}

func TestDeleteOrganizationAvatar(t *testing.T) {
	gin.SetMode(gin.TestMode)

	orgId := uuid.New()

	type dependencies struct {
		usecase *mocks.MockOrganizationUsecase
	}

	tests := []struct {
		name          string
		inp           dto.DeleteOrganizationAvatarInput
		mockFn        func(d *dependencies)
		expectErr     error
		ctxValue      uuid.UUID
		expStatusCode int
	}{
		{
			name:          "should return error if delete organization avatar fail",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusInternalServerError,
			inp: dto.DeleteOrganizationAvatarInput{
				OrgID: orgId,
			},
			mockFn: func(d *dependencies) {
				d.usecase.On("DeleteAvatarOrganization", mock.Anything, mock.Anything).
					Return(errors.New("Error"))
			},
			expectErr: errors.New("Error"),
		},
		{
			name:          "should return error when GetCurrentUserId returns error",
			expStatusCode: http.StatusForbidden,
			mockFn:        func(d *dependencies) {},
			expectErr:     errors.New("Forbidden"),
		},
		{
			name:          "should upload organization avatar successfully",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusOK,
			mockFn: func(d *dependencies) {
				d.usecase.On("DeleteAvatarOrganization", mock.Anything, mock.Anything).Return(nil)
			},
		},
		{
			name:          "should return error if DeleteAvatarOrganization return ErrUserNotFound",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusNotFound,
			inp: dto.DeleteOrganizationAvatarInput{
				OrgID: orgId,
			},
			mockFn: func(d *dependencies) {
				d.usecase.On("DeleteAvatarOrganization", mock.Anything, mock.Anything).
					Return(usecase.ErrUserNotFound)
			},
			expectErr: errors.New("User not found"),
		},
		{
			name:          "should return error if DeleteAvatarOrganization return ErrOrganizationNotFound",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusNotFound,
			inp: dto.DeleteOrganizationAvatarInput{
				OrgID: orgId,
			},
			mockFn: func(d *dependencies) {
				d.usecase.On("DeleteAvatarOrganization", mock.Anything, mock.Anything).
					Return(usecase.ErrOrganizationNotFound)
			},
			expectErr: errors.New("Organization not found"),
		},
		{
			name:          "should return error if DeleteAvatarOrganization return ErrNoPermission",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusForbidden,
			inp: dto.DeleteOrganizationAvatarInput{
				OrgID: orgId,
			},
			mockFn: func(d *dependencies) {
				d.usecase.On("DeleteAvatarOrganization", mock.Anything, mock.Anything).
					Return(usecase.ErrNoPermission)
			},
			expectErr: usecase.ErrNoPermission,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup Gin context
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)

			// Set user_id in context
			c.Set("user_id", tt.ctxValue)
			c.Request = httptest.NewRequest(
				http.MethodDelete,
				fmt.Sprintf("/organizations/%s/avatar", orgId.String()),
				nil,
			)
			c.Params = gin.Params{
				{Key: "org_id", Value: orgId.String()},
			}
			d := &dependencies{
				usecase: &mocks.MockOrganizationUsecase{},
			}
			tt.mockFn(d)
			h := handlers.NewOrganizationHandler(nil, d.usecase)

			// Call the handler
			h.DeleteOrganizationAvatar(c)

			// Assert the status code
			assert.Equal(t, tt.expStatusCode, w.Code)

			if tt.expectErr != nil {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expectErr.Error(), response.Message)
				assert.Equal(t, tt.expStatusCode, w.Code)
			} else {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
			}
		})
	}
}

func TestListCurrentUserOrganizations(t *testing.T) {
	gin.SetMode(gin.TestMode)

	type dependencies struct {
		usecase *mocks.MockOrganizationUsecase
	}

	tests := []struct {
		name          string
		inp           dto.ListOrganizationsInput
		mockFn        func(d *dependencies)
		expectErr     error
		ctxValue      uuid.UUID
		expStatusCode int
	}{
		{
			name:     "should fail validate Keyword",
			ctxValue: uuid.New(),
			inp: dto.ListOrganizationsInput{
				Keyword: strings.Repeat("a", 101),
			},
			mockFn:        func(d *dependencies) {},
			expStatusCode: http.StatusBadRequest,
			expectErr: errors.New(
				"Keyword must start and end with a letter or number, may contain letters, numbers, underscores, spaces, at and must be between 1 and 100 characters long",
			),
		},
		{
			name:     "should fail validate Role",
			ctxValue: uuid.New(),
			inp: dto.ListOrganizationsInput{
				Role: "placeholder",
			},
			mockFn:        func(d *dependencies) {},
			expStatusCode: http.StatusBadRequest,
			expectErr: errors.New(
				"Key: 'ListOrganizationsInput.Role' Error:Field validation for 'Role' failed on the 'oneof' tag",
			),
		},
		{
			name:          "should return error when GetCurrentUserId returns error",
			expStatusCode: http.StatusForbidden,
			mockFn:        func(d *dependencies) {},
			expectErr:     errors.New("Forbidden"),
		},
		{
			name:          "should list organization successfully",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusOK,
			mockFn: func(d *dependencies) {
				d.usecase.On("ListOrganizations", mock.Anything, mock.Anything).
					Return(&dto.ListOrganizationsOutput{}, nil)
			},
		},
		{
			name:     "should return error when ListOrganizations failed",
			ctxValue: uuid.New(),
			mockFn: func(d *dependencies) {
				d.usecase.On("ListOrganizations", mock.Anything, mock.Anything).
					Return(nil, errors.New("Error"))
			},
			expStatusCode: http.StatusInternalServerError,
			expectErr:     errors.New("Error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				usecase: &mocks.MockOrganizationUsecase{},
			}
			tt.mockFn(d)
			h := handlers.NewOrganizationHandler(nil, d.usecase)

			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			// Setup Gin context
			c.Set("user_id", tt.ctxValue)
			c.Request = httptest.NewRequest(http.MethodGet, "/organizations/me", nil)
			query := c.Request.URL.Query()
			query.Add("keyword", tt.inp.Keyword)
			query.Add("role", string(tt.inp.Role))
			c.Request.URL.RawQuery = query.Encode()

			// Call the handler
			h.ListCurrentUserOrganizations(c)

			// Assert the status code
			assert.Equal(t, tt.expStatusCode, w.Code)

			if tt.expectErr != nil {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expectErr.Error(), response.Message)
				assert.Equal(t, tt.expStatusCode, w.Code)
			} else {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
			}
		})
	}
}

func TestListAllOrganizations(t *testing.T) {
	gin.SetMode(gin.TestMode)

	type dependencies struct {
		usecase *mocks.MockOrganizationUsecase
	}

	tests := []struct {
		name          string
		inp           dto.ListOrganizationsInput
		mockFn        func(d *dependencies)
		expectErr     error
		ctxValue      uuid.UUID
		expStatusCode int
	}{
		{
			name:     "should fail validate Keyword",
			ctxValue: uuid.New(),
			inp: dto.ListOrganizationsInput{
				Keyword: strings.Repeat("a", 101),
			},
			mockFn:        func(d *dependencies) {},
			expStatusCode: http.StatusBadRequest,
			expectErr: errors.New(
				"Keyword must start and end with a letter or number, may contain letters, numbers, underscores, spaces, at and must be between 1 and 100 characters long",
			),
		},
		{
			name:     "should fail validate Role",
			ctxValue: uuid.New(),
			inp: dto.ListOrganizationsInput{
				Role: "placeholder",
			},
			mockFn:        func(d *dependencies) {},
			expStatusCode: http.StatusBadRequest,
			expectErr: errors.New(
				"Key: 'ListOrganizationsInput.Role' Error:Field validation for 'Role' failed on the 'oneof' tag",
			),
		},
		{
			name:          "should list organization successfully",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusOK,
			mockFn: func(d *dependencies) {
				d.usecase.On("ListOrganizations", mock.Anything, mock.Anything).
					Return(&dto.ListOrganizationsOutput{}, nil)
			},
		},
		{
			name:     "should return error when ListOrganizations failed",
			ctxValue: uuid.New(),
			mockFn: func(d *dependencies) {
				d.usecase.On("ListOrganizations", mock.Anything, mock.Anything).
					Return(nil, errors.New("Error"))
			},
			expStatusCode: http.StatusInternalServerError,
			expectErr:     errors.New("Error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				usecase: &mocks.MockOrganizationUsecase{},
			}
			tt.mockFn(d)
			h := handlers.NewOrganizationHandler(nil, d.usecase)

			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			// Setup Gin context
			c.Set("user_id", tt.ctxValue)
			c.Request = httptest.NewRequest(http.MethodGet, "/organizations", nil)
			query := c.Request.URL.Query()
			query.Add("keyword", tt.inp.Keyword)
			query.Add("role", string(tt.inp.Role))
			c.Request.URL.RawQuery = query.Encode()

			// Call the handler
			h.ListAllOrganizations(c)

			// Assert the status code
			assert.Equal(t, tt.expStatusCode, w.Code)

			if tt.expectErr != nil {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expectErr.Error(), response.Message)
				assert.Equal(t, tt.expStatusCode, w.Code)
			} else {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
			}
		})
	}
}

func TestGetOrganization(t *testing.T) {
	gin.SetMode(gin.TestMode)

	type dependencies struct {
		usecase *mocks.MockOrganizationUsecase
	}

	tests := []struct {
		name          string
		orgID         string
		mockFn        func(d *dependencies)
		expectErr     error
		ctxValue      uuid.UUID
		expStatusCode int
	}{
		{
			name:          "should return error when GetCurrentUserId returns error",
			mockFn:        func(d *dependencies) {},
			expStatusCode: http.StatusForbidden,
			expectErr:     errors.New("Forbidden"),
		},
		{
			name:          "should fail parse org_id",
			ctxValue:      uuid.New(),
			orgID:         "",
			mockFn:        func(d *dependencies) {},
			expStatusCode: http.StatusBadRequest,
			expectErr:     errors.New("Failed to parse organization id"),
		},
		{
			name:     "should get organization successfully",
			ctxValue: uuid.New(),
			orgID:    uuid.NewString(),
			mockFn: func(d *dependencies) {
				d.usecase.On("FindOrganizationByID", mock.Anything, mock.Anything).
					Return(&dto.FindOrganizationOutput{}, nil)
			},
			expStatusCode: http.StatusOK,
			expectErr:     nil,
		},
		{
			name:     "should return error when FindOrganizationByID failed",
			ctxValue: uuid.New(),
			orgID:    uuid.NewString(),
			mockFn: func(d *dependencies) {
				d.usecase.On("FindOrganizationByID", mock.Anything, mock.Anything).
					Return(nil, errors.New("Error"))
			},
			expStatusCode: http.StatusInternalServerError,
			expectErr:     errors.New("Error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				usecase: &mocks.MockOrganizationUsecase{},
			}
			tt.mockFn(d)
			h := handlers.NewOrganizationHandler(nil, d.usecase)

			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			// Setup Gin context
			c.Set("user_id", tt.ctxValue)
			c.Request = httptest.NewRequest(http.MethodGet, "/organizations/:org_id", nil)
			c.Params = gin.Params{
				{Key: "org_id", Value: tt.orgID},
			}

			// Call the handler
			h.GetOrganization(c)

			// Assert the status code
			assert.Equal(t, tt.expStatusCode, w.Code)

			if tt.expectErr != nil {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expectErr.Error(), response.Message)
				assert.Equal(t, tt.expStatusCode, w.Code)
			} else {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
			}
		})
	}
}

func TestListOrganizationMembers(t *testing.T) {
	gin.SetMode(gin.TestMode)

	type dependencies struct {
		usecase *mocks.MockOrganizationUsecase
	}

	tests := []struct {
		name          string
		orgID         string
		mockFn        func(d *dependencies)
		input         dto.ListOrgMembersInput
		expectErr     error
		ctxValue      uuid.UUID
		expStatusCode int
	}{
		{
			name:          "should return error when GetCurrentUserId returns error",
			mockFn:        func(d *dependencies) {},
			expStatusCode: http.StatusForbidden,
			expectErr:     errors.New("Forbidden"),
		},
		{
			name:          "should fail parse org_id",
			ctxValue:      uuid.New(),
			orgID:         "",
			mockFn:        func(d *dependencies) {},
			expStatusCode: http.StatusBadRequest,
			expectErr:     errors.New("Failed to parse organization id"),
		},
		{
			name:     "should fail validate Keyword",
			ctxValue: uuid.New(),
			orgID:    uuid.NewString(),
			input: dto.ListOrgMembersInput{
				Keyword: strings.Repeat("a", 101),
			},
			mockFn:        func(d *dependencies) {},
			expStatusCode: http.StatusBadRequest,
			expectErr: errors.New(
				"Keyword must start and end with a letter or number, may contain letters, numbers, underscores, spaces, at and must be between 1 and 100 characters long",
			),
		},
		{
			name:     "should list organization members successfully",
			ctxValue: uuid.New(),
			orgID:    uuid.NewString(),
			mockFn: func(d *dependencies) {
				d.usecase.On("ListOrganizationMembers", mock.Anything, mock.Anything).
					Return(&dto.ListOrgMembersOutput{}, nil)
			},
			expStatusCode: http.StatusOK,
			expectErr:     nil,
		},
		{
			name:     "should return error when ListOrganizationMembers failed",
			ctxValue: uuid.New(),
			orgID:    uuid.NewString(),
			mockFn: func(d *dependencies) {
				d.usecase.On("ListOrganizationMembers", mock.Anything, mock.Anything).
					Return(nil, errors.New("Error"))
			},
			expStatusCode: http.StatusInternalServerError,
			expectErr:     errors.New("Error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				usecase: &mocks.MockOrganizationUsecase{},
			}
			tt.mockFn(d)
			h := handlers.NewOrganizationHandler(nil, d.usecase)

			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			// Setup Gin context
			c.Set("user_id", tt.ctxValue)
			c.Request = httptest.NewRequest(http.MethodGet, "/organizations/:org_id/members", nil)
			c.Params = gin.Params{
				{Key: "org_id", Value: tt.orgID},
			}
			query := c.Request.URL.Query()
			query.Add("keyword", tt.input.Keyword)
			c.Request.URL.RawQuery = query.Encode()

			// Call the handler
			h.ListOrganizationMembers(c)

			// Assert the status code
			assert.Equal(t, tt.expStatusCode, w.Code)

			if tt.expectErr != nil {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expectErr.Error(), response.Message)
				assert.Equal(t, tt.expStatusCode, w.Code)
			} else {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
			}
		})
	}
}

func TestGetMember(t *testing.T) {
	gin.SetMode(gin.TestMode)

	type dependencies struct {
		usecase *mocks.MockOrganizationUsecase
	}

	tests := []struct {
		name          string
		orgID         string
		memberID      string
		mockFn        func(d *dependencies)
		input         dto.ListOrgMembersInput
		expectErr     error
		expStatusCode int
	}{
		{
			name:          "should fail parse org_id",
			orgID:         "",
			memberID:      uuid.NewString(),
			mockFn:        func(d *dependencies) {},
			expStatusCode: http.StatusBadRequest,
			expectErr:     errors.New("Failed to parse organization id"),
		},
		{
			name:          "should fail parse member_id",
			orgID:         uuid.NewString(),
			memberID:      "",
			mockFn:        func(d *dependencies) {},
			expStatusCode: http.StatusBadRequest,
			expectErr:     errors.New("Failed to parse member id"),
		},
		{
			name:     "should get member successfully",
			orgID:    uuid.NewString(),
			memberID: uuid.NewString(),
			mockFn: func(d *dependencies) {
				d.usecase.On("GetMember", mock.Anything, mock.Anything).
					Return(&entities.OrgMember{}, nil)
			},
			expStatusCode: http.StatusOK,
			expectErr:     nil,
		},
		{
			name:     "should return error when ListOrganizationMembers failed",
			orgID:    uuid.NewString(),
			memberID: uuid.NewString(),
			mockFn: func(d *dependencies) {
				d.usecase.On("GetMember", mock.Anything, mock.Anything).
					Return(nil, errors.New("Error"))
			},
			expStatusCode: http.StatusInternalServerError,
			expectErr:     errors.New("Error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				usecase: &mocks.MockOrganizationUsecase{},
			}
			tt.mockFn(d)
			h := handlers.NewOrganizationHandler(nil, d.usecase)

			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Request = httptest.NewRequest(
				http.MethodGet,
				"/organizations/:org_id/members/:member_id",
				nil,
			)
			c.Params = gin.Params{
				{Key: "org_id", Value: tt.orgID},
				{Key: "member_id", Value: tt.memberID},
			}

			// Call the handler
			h.GetMember(c)

			// Assert the status code
			assert.Equal(t, tt.expStatusCode, w.Code)

			if tt.expectErr != nil {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expectErr.Error(), response.Message)
				assert.Equal(t, tt.expStatusCode, w.Code)
			} else {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
			}
		})
	}
}

func TestInviteUsers(t *testing.T) {
	gin.SetMode(gin.TestMode)

	type dependencies struct {
		usecase *mocks.MockOrganizationUsecase
	}

	orgId := uuid.New()
	expireTime := time.Date(2026, 1, 2, 15, 4, 5, 0, time.UTC)

	tests := []struct {
		name          string
		input         dto.InviteOrgMembersInput
		ctxValue      uuid.UUID
		mockFn        func(d *dependencies)
		expectErr     error
		expStatusCode int
	}{
		{
			name:          "should return error when GetCurrentUserId returns error",
			mockFn:        func(d *dependencies) {},
			expStatusCode: http.StatusUnauthorized,
			expectErr:     errors.New("Forbidden"),
		},
		{
			name:     "should fail validate input",
			ctxValue: uuid.New(),
			input: dto.InviteOrgMembersInput{
				Members: []dto.OrgMemberInviteInput{
					{
						UserId:   uuid.New(),
						Role:     "invalid_role",
						ExpireAt: &expireTime,
					},
				},
			},
			mockFn:        func(d *dependencies) {},
			expStatusCode: http.StatusBadRequest,
			expectErr: errors.New(
				"Key: 'InviteOrgMembersInput.Members[0].Role' Error:Field validation for 'Role' failed on the 'oneof' tag",
			),
		},
		{
			name:     "should return error when InviteMultipleOrgMembers returns error",
			ctxValue: uuid.New(),
			input: dto.InviteOrgMembersInput{
				Members: []dto.OrgMemberInviteInput{
					{
						UserId:   uuid.New(),
						Role:     "owner",
						ExpireAt: &expireTime,
					},
				},
			},
			mockFn: func(d *dependencies) {
				d.usecase.On("InviteMultipleOrgMembers", mock.Anything, mock.Anything).
					Return(errors.New("error"))
			},
			expStatusCode: http.StatusInternalServerError,
			expectErr:     errors.New("Error"),
		},
		{
			name:     "should return error when user not found",
			ctxValue: uuid.New(),
			input: dto.InviteOrgMembersInput{
				Members: []dto.OrgMemberInviteInput{
					{
						UserId:   uuid.New(),
						Role:     "owner",
						ExpireAt: &expireTime,
					},
				},
			},
			mockFn: func(d *dependencies) {
				d.usecase.On("InviteMultipleOrgMembers", mock.Anything, mock.Anything).
					Return(usecase.ErrUserNotFound)
			},
			expStatusCode: http.StatusNotFound,
			expectErr:     errors.New("User not found"),
		},
		{
			name:     "should return error when organization not found",
			ctxValue: uuid.New(),
			input: dto.InviteOrgMembersInput{
				Members: []dto.OrgMemberInviteInput{
					{
						UserId:   uuid.New(),
						Role:     "owner",
						ExpireAt: &expireTime,
					},
				},
			},
			mockFn: func(d *dependencies) {
				d.usecase.On("InviteMultipleOrgMembers", mock.Anything, mock.Anything).
					Return(usecase.ErrOrganizationNotFound)
			},
			expStatusCode: http.StatusNotFound,
			expectErr:     errors.New("Organization not found"),
		},
		{
			name:     "should invite users successfully",
			ctxValue: uuid.New(),
			input: dto.InviteOrgMembersInput{
				Members: []dto.OrgMemberInviteInput{
					{
						UserId:   uuid.New(),
						Role:     "owner",
						ExpireAt: &expireTime,
					},
				},
			},
			mockFn: func(d *dependencies) {
				d.usecase.On("InviteMultipleOrgMembers", mock.Anything, mock.Anything).Return(nil)
			},
			expStatusCode: http.StatusOK,
			expectErr:     nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				usecase: &mocks.MockOrganizationUsecase{},
			}
			tt.mockFn(d)
			h := handlers.NewOrganizationHandler(nil, d.usecase)

			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Set("user_id", tt.ctxValue)

			// Set org_id parameter if not testing parse error
			if tt.name != "should fail parse org_id" {
				c.Params = gin.Params{
					{Key: "org_id", Value: orgId.String()},
				}
			}

			// Set request body
			inputJSON, _ := json.Marshal(tt.input)
			c.Request = httptest.NewRequest(
				http.MethodPost,
				"/organizations/:org_id/invite-multiple",
				strings.NewReader(string(inputJSON)),
			)

			// Call the handler
			h.InviteUsers(c)

			// Assert the status code
			assert.Equal(t, tt.expStatusCode, w.Code)

			if tt.expectErr != nil {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expectErr.Error(), response.Message)
				assert.Equal(t, tt.expStatusCode, w.Code)
			} else {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, "Users invited to organization successfully", response["message"])
			}
		})
	}
}
