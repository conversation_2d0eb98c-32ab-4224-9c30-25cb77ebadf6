package handlers_test

import (
	"api-server/internal/handlers"
	"api-server/internal/usecase/healthcheck/mocks"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

func TestHealthExplore(t *testing.T) {
	gin.SetMode(gin.TestMode)

	testcases := []struct {
		name           string
		mockFn         func(u *mocks.MockHealthcheckUsecase)
		expectHTTPCode int
	}{
		{
			name: "should fail binding query",
			mockFn: func(u *mocks.MockHealthcheckUsecase) {
				u.On("Check", mock.Anything, mock.Anything).Return(errors.New("error"))
			},
			expectHTTPCode: http.StatusInternalServerError,
		},
		{
			name: "should success",
			mockFn: func(u *mocks.MockHealthcheckUsecase) {
				u.On("Check", mock.Anything, mock.Anything).Return(nil)
			},
			expectHTTPCode: 200,
		},
	}

	for _, testcase := range testcases {
		t.Run(testcase.name, func(t *testing.T) {
			usecase := &mocks.MockHealthcheckUsecase{}
			testcase.mockFn(usecase)
			handler := handlers.NewHealthCheckHandler(usecase)

			rr := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(rr)
			c.Request = httptest.NewRequest(http.MethodGet, "/explore", nil)

			handler.Explore(c)

			assert.Equal(t, testcase.expectHTTPCode, rr.Code)
		})
	}
}
