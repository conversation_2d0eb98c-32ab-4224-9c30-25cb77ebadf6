package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"go.opentelemetry.io/otel/codes"

	"api-server/internal/dto"
	"api-server/pkg/oteltrace"
	"api-server/pkg/validator"
)

// CreateRepoTags godoc
//
//	@Summary	Create repository tags
//	@Tags		Repository
//	@Accept		json
//	@Produce	json
//	@Param		model	body		dto.CreateRepoTagInput	true	"Create tag"
//	@Success	200		{object}	string					"Ok"
//	@Failure	400		{object}	dto.HTTPError			"Bad Request - invalid request"
//	@Failure	404		{object}	dto.HTTPError			"Not Found"
//	@Failure	500		{object}	dto.HTTPError			"Internal Server Error - Encountered an unexpected condition"
//	@Router		/repositories/tags [post]
//
//	@Security	Bearer
func (h *repositoryHandlerImpl) CreateRepoTag(ctx *gin.Context) {
	c, span := oteltrace.Tracer.Start(ParseContext(ctx), "handlers.CreateRepoTag")
	defer span.End()

	currentUserID, err := GetCurrentUserId(c)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user id")
		span.RecordError(err)
		dto.ErrorResponse(ctx, dto.NewForbiddenError(err, "failed to get current user id"))
		return
	}

	var input dto.CreateRepoTagInput
	if err := ctx.ShouldBindJSON(&input); err != nil {
		span.SetStatus(codes.Error, "failed to bind JSON input")
		span.RecordError(err)
		dto.ErrorResponse(ctx, dto.NewBadRequestError(err))
		return
	}

	input.CurrentUserID = currentUserID
	if err := validator.Validate(input); err != nil {
		span.SetStatus(codes.Error, "failed to validate input")
		span.RecordError(err)
		dto.ErrorResponse(ctx, dto.NewBadRequestError(err))
		return
	}

	err = h.repoUseCase.CreateRepoTag(c, input)
	if err != nil {
		span.SetStatus(codes.Error, "failed to create repo tag")
		span.RecordError(err)
		dto.ErrorResponse(ctx, dto.NewErrorFromUsecase(err))
		return
	}

	span.SetStatus(codes.Ok, "create tag successfully")
	ctx.JSON(http.StatusCreated, gin.H{"message": "Tag created successfully"})
}

// GetRepoTag godoc
//
//	@Summary	Get repository tag
//	@Tags		Repository
//	@Accept		json
//	@Produce	json
//	@Param		tag_id	path		string			true	"tag id"
//	@Failure	400		{object}	dto.HTTPError	"Bad Request - invalid request"
//	@Failure	404		{object}	dto.HTTPError	"Not Found"
//	@Failure	500		{object}	dto.HTTPError	"Internal Server Error - Encountered an unexpected condition"
//	@Router		/repositories/tags/{tag_id} [get]
//
//	@Security	Bearer
func (h *repositoryHandlerImpl) GetRepoTag(ctx *gin.Context) {
	c, span := oteltrace.Tracer.Start(ParseContext(ctx), "handlers.GetRepoTag")
	defer span.End()

	currentUserID, err := GetCurrentUserId(c)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user id")
		span.RecordError(err)
		dto.ErrorResponse(ctx, dto.NewForbiddenError(err, "failed to get current user id"))
		return
	}

	var input dto.GetRepoTagInput
	tagId, err := uuid.Parse(ctx.Param("tag_id"))
	if err != nil {
		span.SetStatus(codes.Error, err.Error())
		span.RecordError(err)
		dto.ErrorResponse(ctx, dto.NewBadRequestError(err))
		return
	}

	input.Id = tagId
	input.CurrentUserID = currentUserID
	tag, err := h.repoUseCase.GetRepoTag(c, input)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get repo tag")
		span.RecordError(err)
		dto.ErrorResponse(ctx, dto.NewErrorFromUsecase(err))
		return
	}

	span.SetStatus(codes.Ok, "tag retrieved successfully")
	ctx.JSON(http.StatusOK, tag)
}

// UpdateRepoTag godoc
//
//	@Summary	Update repository tag
//	@Tags		Repository
//	@Accept		json
//	@Produce	json
//	@Param		tag_id	path		string			true	"tag id"
//	@Failure	400		{object}	dto.HTTPError	"Bad Request - invalid request"
//	@Failure	404		{object}	dto.HTTPError	"Not Found"
//	@Failure	500		{object}	dto.HTTPError	"Internal Server Error - Encountered an unexpected condition"
//	@Router		/repositories/tags/{tag_id} [patch]
//
//	@Security	Bearer
func (h *repositoryHandlerImpl) UpdateRepoTag(ctx *gin.Context) {
	c, span := oteltrace.Tracer.Start(ParseContext(ctx), "handlers.UpdateRepoTag")
	defer span.End()

	currentUserID, err := GetCurrentUserId(c)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user id")
		span.RecordError(err)
		dto.ErrorResponse(ctx, dto.NewForbiddenError(err, "failed to get current user id"))
		return
	}

	var input dto.UpdateRepoTagInput
	if err := ctx.ShouldBindJSON(&input); err != nil {
		span.SetStatus(codes.Error, "failed to bind JSON input")
		span.RecordError(err)
		dto.ErrorResponse(ctx, dto.NewBadRequestError(err))
		return
	}

	tagId, err := uuid.Parse(ctx.Param("tag_id"))
	if err != nil {
		span.SetStatus(codes.Error, err.Error())
		span.RecordError(err)
		dto.ErrorResponse(ctx, dto.NewBadRequestError(err))
		return
	}

	input.Id = tagId
	input.CurrentUserID = currentUserID
	if err := validator.Validate(input); err != nil {
		span.SetStatus(codes.Error, "failed to validate input")
		span.RecordError(err)
		dto.ErrorResponse(ctx, dto.NewBadRequestError(err))
		return
	}

	err = h.repoUseCase.UpdateRepoTag(c, input)
	if err != nil {
		span.SetStatus(codes.Error, "failed to update repo tag")
		span.RecordError(err)
		dto.ErrorResponse(ctx, dto.NewErrorFromUsecase(err))
		return
	}

	span.SetStatus(codes.Ok, "tag updated successfully")
	ctx.JSON(http.StatusOK, gin.H{"message": "Tag updated successfully"})
}

// DeleteRepoTag godoc
//
//	@Summary	Create repository tags
//	@Tags		Repository
//	@Accept		json
//	@Produce	json
//	@Param		tag_id	path		string			true	"tag id"
//	@Failure	400		{object}	dto.HTTPError	"Bad Request - invalid request"
//	@Failure	404		{object}	dto.HTTPError	"Not Found"
//	@Failure	500		{object}	dto.HTTPError	"Internal Server Error - Encountered an unexpected condition"
//	@Router		/repositories/tags/{tag_id} [delete]
//
//	@Security	Bearer
func (h *repositoryHandlerImpl) DeleteRepoTag(ctx *gin.Context) {
	c, span := oteltrace.Tracer.Start(ParseContext(ctx), "handlers.DeleteRepoTag")
	defer span.End()

	currentUserID, err := GetCurrentUserId(c)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user id")
		span.RecordError(err)
		dto.ErrorResponse(ctx, dto.NewForbiddenError(err, "failed to get current user id"))
		return
	}

	var input dto.DeleteRepoTagInput
	tagId, err := uuid.Parse(ctx.Param("tag_id"))
	if err != nil {
		span.SetStatus(codes.Error, err.Error())
		span.RecordError(err)
		dto.ErrorResponse(ctx, dto.NewBadRequestError(err))
		return
	}

	input.Id = tagId
	input.CurrentUserID = currentUserID
	if err := validator.Validate(input); err != nil {
		span.SetStatus(codes.Error, "failed to validate input")
		span.RecordError(err)
		dto.ErrorResponse(ctx, dto.NewBadRequestError(err))
		return
	}

	err = h.repoUseCase.DeleteRepoTag(c, input)
	if err != nil {
		span.SetStatus(codes.Error, "failed to delete repo tag")
		span.RecordError(err)
		dto.ErrorResponse(ctx, dto.NewErrorFromUsecase(err))
		return
	}

	span.SetStatus(codes.Ok, "tag deleted successfully")
	ctx.JSON(http.StatusOK, gin.H{"message": "Tag deleted successfully"})
}
