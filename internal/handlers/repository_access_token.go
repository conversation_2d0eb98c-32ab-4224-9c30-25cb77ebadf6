package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"

	"api-server/internal/dto"
	"api-server/internal/types"
	"api-server/pkg/oteltrace"
	"api-server/pkg/otelzap"
	"api-server/pkg/validator"
)

// CreateRepoAccessToken godoc
//
//	@Summary	Create new repo access token
//	@Tags		Repository
//	@Accept		json
//	@Produce	json
//	@Param		model	body		dto.CreateRepoAccessTokenRequest	true	"Access token"
//	@Success	201		{object}	dto.CreateRepoAccessTokenResponse	"Return created token"
//	@Failure	400		{object}	dto.HTTPError						"Bad Request - invalid request"
//	@Failure	404		{object}	dto.HTTPError						"Not Found"
//	@Failure	500		{object}	dto.HTTPError						"Internal Server Error - Encountered an unexpected condition"
//	@Router		/repositories/{repo_type}/{namespace}/{repo_name}/access_tokens [post]
//
//	@Security	Bearer
func (u *repositoryHandlerImpl) CreateRepoAccessToken(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(ParseContext(c), "handlers.repository_access_token.CreateRepoAccessToken",
		trace.WithAttributes(
			attribute.String("action", "CREATE_REPO_ACCESS_TOKEN"),
		))
	defer span.End()

	currentUserId, err := GetCurrentUserId(ctx)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user id")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "failed to get current user id", err,
			zap.String("action", "CREATE_REPO_ACCESS_TOKEN"),
			zap.String("status", "failed"))
		dto.ErrorResponse(c, dto.NewUnauthorizedError(err, "failed to get current user id"))
		return
	}

	var req dto.CreateRepoAccessTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		span.SetStatus(codes.Error, "failed to bind JSON")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "failed to bind JSON", err,
			zap.String("action", "CREATE_REPO_ACCESS_TOKEN"),
			zap.String("status", "failed"))
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	repoID, err := types.RepoID{}.FromGinContext(c)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get repository ID")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "failed to get repository ID", err,
			zap.String("action", "CREATE_REPO_ACCESS_TOKEN"),
			zap.String("status", "failed"))
		dto.ErrorResponse(c, dto.NewBadRequestError(err, err.Error()))
		return
	}

	req.UserID = currentUserId
	req.RepoID = repoID
	if err := validator.Validate(req); err != nil {
		span.SetStatus(codes.Error, "failed to validate request")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "failed to validate request", err,
			zap.String("action", "CREATE_REPO_ACCESS_TOKEN"),
			zap.String("status", "failed"))
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	otelzap.InfoWithContext(ctx, "creating repository access token",
		zap.String("action", "CREATE_REPO_ACCESS_TOKEN"))

	resp, err := u.repoUseCase.CreateRepoAccessToken(c, req)
	if err != nil {
		span.SetStatus(codes.Error, "failed to create repository access token")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "failed to create repository access token", err,
			zap.String("action", "CREATE_REPO_ACCESS_TOKEN"),
			zap.String("status", "failed"))
		dto.ErrorResponse(c, dto.NewErrorFromUsecase(err))
		return
	}

	span.AddEvent("successfully created repository access token")
	span.SetStatus(codes.Ok, "successfully created repository access token")
	span.SetAttributes(attribute.String("status", "success"))
	otelzap.InfoWithContext(ctx, "successfully created repository access token",
		zap.String("action", "CREATE_REPO_ACCESS_TOKEN"),
		zap.String("status", "success"))
	c.JSON(http.StatusOK, resp)
}

// ListRepoAccessToken godoc
//
//	@Summary	List repo access token
//	@Tags		Repository
//	@Accept		json
//	@Produce	json
//	@Param		model	body		dto.ListRepoAccessTokenRequest	true	"Access token"
//	@Success	200		{object}	dto.ListRepoAccessTokenResponse	"Return list token"
//	@Failure	400		{object}	dto.HTTPError					"Bad Request - invalid request"
//	@Failure	404		{object}	dto.HTTPError					"Not Found"
//	@Failure	500		{object}	dto.HTTPError					"Internal Server Error - Encountered an unexpected condition"
//	@Router		/repositories/{repo_type}/{namespace}/{repo_name}/access_tokens [get]
//
//	@Security	Bearer
func (u *repositoryHandlerImpl) ListRepoAccessToken(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(ParseContext(c), "handlers.repository_access_token.ListRepoAccessToken",
		trace.WithAttributes(
			attribute.String("action", "LIST_REPO_ACCESS_TOKENS"),
		))
	defer span.End()

	currentUserId, err := GetCurrentUserId(ParseContext(c))
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user id")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "failed to get current user id", err,
			zap.String("action", "LIST_REPO_ACCESS_TOKENS"),
			zap.String("status", "failed"))
		dto.ErrorResponse(c, dto.NewUnauthorizedError(err, "failed to get current user id"))
		return
	}

	var req dto.ListRepoAccessTokenRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		span.SetStatus(codes.Error, "failed to bind query parameters")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "failed to bind query parameters", err,
			zap.String("action", "LIST_REPO_ACCESS_TOKENS"),
			zap.String("status", "failed"))
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	repoID, err := types.RepoID{}.FromGinContext(c)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get repository ID")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "failed to get repository ID", err,
			zap.String("action", "LIST_REPO_ACCESS_TOKENS"),
			zap.String("status", "failed"))
		dto.ErrorResponse(c, dto.NewBadRequestError(err, err.Error()))
		return
	}

	otelzap.InfoWithContext(ctx, "listing repository access tokens",
		zap.String("action", "LIST_REPO_ACCESS_TOKENS"))

	req.UserID = currentUserId
	req.RepoID = repoID
	resp, err := u.repoUseCase.ListRepoAccessToken(ctx, req)
	if err != nil {
		span.SetStatus(codes.Error, "failed to list repository access tokens")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "failed to list repository access tokens", err,
			zap.String("action", "LIST_REPO_ACCESS_TOKENS"),
			zap.String("status", "failed"))
		dto.ErrorResponse(c, dto.NewErrorFromUsecase(err))
		return
	}

	span.AddEvent("successfully retrieved repository access tokens")
	span.SetStatus(codes.Ok, "successfully retrieved repository access tokens")
	span.SetAttributes(attribute.String("status", "success"))
	otelzap.InfoWithContext(ctx, "successfully retrieved repository access tokens",
		zap.String("action", "LIST_REPO_ACCESS_TOKENS"),
		zap.String("status", "success"))
	c.JSON(http.StatusOK, resp)
}

// DeleteRepoAccessToken godoc
//
//	@Summary	Delete repo access token
//	@Tags		Repository
//	@Accept		json
//	@Produce	json
//	@Param		id	path		string			true	"Access token ID"
//	@Success	204	string		string			"No content"
//	@Failure	400	{object}	dto.HTTPError	"Bad Request - invalid request"
//	@Failure	404	{object}	dto.HTTPError	"Not Found"
//	@Failure	500	{object}	dto.HTTPError	"Internal Server Error - Encountered an unexpected condition"
//	@Router		/repositories/{repo_type}/{namespace}/{repo_name}/access_tokens/{id} [delete]
//
//	@Security	Bearer
func (u *repositoryHandlerImpl) DeleteRepoAccessToken(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(ParseContext(c), "handlers.repository_access_token.DeleteRepoAccessToken",
		trace.WithAttributes(
			attribute.String("action", "DELETE_REPO_ACCESS_TOKEN"),
			attribute.String("access_token_id", c.Param("id")),
		))
	defer span.End()

	userID, err := GetCurrentUserId(ctx)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user id")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "failed to get current user id", err,
			zap.String("action", "DELETE_REPO_ACCESS_TOKEN"),
			zap.String("access_token_id", c.Param("id")),
			zap.String("status", "failed"))
		dto.ErrorResponse(c, dto.NewUnauthorizedError(err, "failed to get current user id"))
		return
	}

	accessTokenID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		span.SetStatus(codes.Error, "failed to parse access token ID")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "failed to parse access token ID", err,
			zap.String("action", "DELETE_REPO_ACCESS_TOKEN"),
			zap.String("access_token_id", c.Param("id")),
			zap.String("status", "failed"))
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	repoID, err := types.RepoID{}.FromGinContext(c)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get repository ID")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "failed to get repository ID", err,
			zap.String("action", "DELETE_REPO_ACCESS_TOKEN"),
			zap.String("access_token_id", c.Param("id")),
			zap.String("status", "failed"))
		dto.ErrorResponse(c, dto.NewBadRequestError(err, err.Error()))
		return
	}

	otelzap.InfoWithContext(ctx, "deleting repository access token",
		zap.String("action", "DELETE_REPO_ACCESS_TOKEN"),
		zap.String("access_token_id", accessTokenID.String()))

	err = u.repoUseCase.DeleteRepoAccessToken(ctx, userID, repoID, accessTokenID)
	if err != nil {
		span.SetStatus(codes.Error, "failed to delete repository access token")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "failed to delete repository access token", err,
			zap.String("action", "DELETE_REPO_ACCESS_TOKEN"),
			zap.String("access_token_id", accessTokenID.String()),
			zap.String("status", "failed"))
		dto.ErrorResponse(c, dto.NewErrorFromUsecase(err))
		return
	}

	span.AddEvent("successfully deleted repository access token")
	span.SetStatus(codes.Ok, "successfully deleted repository access token")
	span.SetAttributes(attribute.String("status", "success"))
	otelzap.InfoWithContext(ctx, "successfully deleted repository access token",
		zap.String("action", "DELETE_REPO_ACCESS_TOKEN"),
		zap.String("access_token_id", accessTokenID.String()),
		zap.String("status", "success"))
	c.JSON(http.StatusNoContent, nil)
}
