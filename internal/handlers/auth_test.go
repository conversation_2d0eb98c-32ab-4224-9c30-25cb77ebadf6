package handlers_test

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"

	"api-server/internal/dto"
	"api-server/internal/handlers"
	"api-server/pkg/oteltrace"
	"api-server/pkg/otelzap"
)

func TestNew(t *testing.T) {
	handler := handlers.New()
	assert.NotNil(t, handler, "New() should return a non-nil handler")
}

func TestHandleSupabaseAuthHook(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// Setup a no-op tracer for testing
	tp := trace.NewNoopTracerProvider()
	otel.SetTracerProvider(tp)
	oteltrace.Tracer = tp.Tracer("test")

	// Setup a no-op logger for testing
	logger, _ := zap.NewDevelopment()
	otelzap.Logger = logger

	userID := uuid.New()

	tests := []struct {
		name           string
		input          dto.CustomAccessTokenInput
		expectBody     dto.CustomAccessTokenOutput
		expectHTTPCode int
	}{
		{
			name: "should generate custom claims successfully",
			input: dto.CustomAccessTokenInput{
				UserID: userID,
				Claims: &dto.AccessTokenClaims{
					Email:                       "<EMAIL>",
					Phone:                       "+**********",
					Role:                        "authenticated",
					AuthenticatorAssuranceLevel: "aal1",
					SessionId:                   userID.String(),
					IsAnonymous:                 false,
				},
			},
			expectBody: dto.CustomAccessTokenOutput{
				Claims: map[string]interface{}{
					"email":        "<EMAIL>",
					"phone":        "+**********",
					"role":         "authenticated",
					"aal":          "aal1",
					"session_id":   userID.String(),
					"is_anonymous": false,
					"user_id":      userID.String(),
				},
			},
			expectHTTPCode: http.StatusOK,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup Gin context
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)

			// Create request body
			jsonInput, _ := json.Marshal(tt.input)
			c.Request = httptest.NewRequest(
				http.MethodPost,
				"/custom_claims",
				bytes.NewBuffer(jsonInput),
			)

			// Create handler and call function
			h := handlers.NewAuthHandler()
			h.HandleSupabaseAuthHook(c)

			// Assert response
			assert.Equal(t, tt.expectHTTPCode, w.Code)

			var response dto.CustomAccessTokenOutput
			err := json.Unmarshal(w.Body.Bytes(), &response)
			assert.NoError(t, err)

			if tt.expectHTTPCode == http.StatusOK {
				// For successful case, verify all claims are present
				assert.Equal(t, tt.expectBody.Claims["email"], response.Claims["email"])
				assert.Equal(t, tt.expectBody.Claims["phone"], response.Claims["phone"])
				assert.Equal(t, tt.expectBody.Claims["role"], response.Claims["role"])
				assert.Equal(t, tt.expectBody.Claims["aal"], response.Claims["aal"])
				assert.Equal(t, tt.expectBody.Claims["session_id"], response.Claims["session_id"])
				assert.Equal(t, tt.expectBody.Claims["is_anonymous"], response.Claims["is_anonymous"])
				assert.Equal(t, tt.expectBody.Claims["user_id"], response.Claims["user_id"])
			} else {
				// For error case, verify error response
				assert.Equal(t, tt.expectBody.HookError, response.HookError)
			}
		})
	}
}
