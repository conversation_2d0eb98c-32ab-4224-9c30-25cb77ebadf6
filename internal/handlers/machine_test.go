package handlers_test

import (
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"api-server/internal/dto"
	"api-server/internal/enums"
	"api-server/internal/handlers"
	"api-server/internal/types"
	"api-server/internal/usecase/hardware/mocks"
)

func TestListHardwares(t *testing.T) {
	gin.SetMode(gin.TestMode)

	mockUserID, _ := uuid.Parse("d2de43e6-fde6-4deb-a1d0-2d5c2c2e1038")
	mockUUID, _ := uuid.Parse("5784a981-1ceb-42ce-a33b-78d5ca2726e5")

	testcases := []struct {
		name           string
		mockFn         func(u *mocks.MockHardwareUsecase)
		requestQuery   *dto.ListHardwareRequest
		expectBody     string
		expectHTTPCode int
	}{
		{
			name: "should fail binding query",
			mockFn: func(u *mocks.MockHardwareUsecase) {
				u.On("ListHardware", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			requestQuery: &dto.ListHardwareRequest{
				Paginate: dto.PaginateRequest{
					Sort: enums.OrderByDirection("placeholder"),
				},
			},
			expectBody:     "{\"code\":400,\"message\":\"Key: 'ListHardwareRequest.Paginate.Sort' Error:Field validation for 'Sort' failed on the 'oneof' tag\"}",
			expectHTTPCode: http.StatusBadRequest,
		},
		{
			name: "should return Internal error if ListHardware return error",
			mockFn: func(u *mocks.MockHardwareUsecase) {
				u.On("ListHardware", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			requestQuery:   nil,
			expectBody:     "{\"code\":500,\"message\":\"Error\"}",
			expectHTTPCode: http.StatusInternalServerError,
		},
		{
			name: "should pass with empty pagination query",
			mockFn: func(u *mocks.MockHardwareUsecase) {
				u.On("ListHardware", mock.Anything, mock.Anything).Return(&dto.ListHardwareResponse{
					Message: "",
					Data: &[]dto.Hardware{
						{
							ID:   mockUUID,
							Name: "cpu",
							CPU:  1,
							Mem: types.HardwareMem{
								Amount: 1,
								Unit:   enums.MemoryUnit_GiB,
							},
							GPUMem: nil,
						},
						{
							ID:   mockUUID,
							Name: "gpu",
							CPU:  1,
							Mem: types.HardwareMem{
								Amount: 1,
								Unit:   enums.MemoryUnit_GiB,
							},
							GPUMem: &types.HardwareMem{
								Amount: 16,
								Unit:   enums.MemoryUnit_GiB,
							},
						},
					},
					Pagination: &dto.Pagination{},
				}, nil)
			},
			requestQuery:   nil,
			expectBody:     "{\"data\":[{\"id\":\"5784a981-1ceb-42ce-a33b-78d5ca2726e5\",\"name\":\"cpu\",\"cpu\":1,\"mem\":{\"amount\":1,\"unit\":\"GiB\"},\"gpu_mem\":null,\"created_at\":\"0001-01-01T00:00:00Z\",\"updated_at\":\"0001-01-01T00:00:00Z\"},{\"id\":\"5784a981-1ceb-42ce-a33b-78d5ca2726e5\",\"name\":\"gpu\",\"cpu\":1,\"mem\":{\"amount\":1,\"unit\":\"GiB\"},\"gpu_mem\":{\"amount\":16,\"unit\":\"GiB\"},\"created_at\":\"0001-01-01T00:00:00Z\",\"updated_at\":\"0001-01-01T00:00:00Z\"}],\"total\":0,\"page_no\":0,\"page_size\":0}",
			expectHTTPCode: http.StatusOK,
		},
	}

	for _, testcase := range testcases {
		t.Run(testcase.name, func(t *testing.T) {
			usecase := &mocks.MockHardwareUsecase{}
			testcase.mockFn(usecase)
			handler := handlers.NewHardwareHandler(nil, usecase)

			rr := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(rr)
			c.Set(enums.USER_ID, mockUserID)
			c.Request = httptest.NewRequest(http.MethodGet, "/machines", nil)
			if testcase.requestQuery != nil {
				query := c.Request.URL.Query()
				query.Add("sort", string(testcase.requestQuery.Paginate.Sort))
				c.Request.URL.RawQuery = query.Encode()
			}

			handler.ListHardwares(c)

			assert.Equal(t, testcase.expectHTTPCode, rr.Code)
			assert.Equal(t, testcase.expectBody, rr.Body.String())
		})
	}
}

func TestListGPUNodes(t *testing.T) {
	gin.SetMode(gin.TestMode)

	mockUserID, _ := uuid.Parse("d2de43e6-fde6-4deb-a1d0-2d5c2c2e1038")
	mockRepoID := types.NewRepoID(enums.RepoType_Spaces, "test-namespace", "test-repo")

	testcases := []struct {
		name           string
		mockFn         func(u *mocks.MockHardwareUsecase)
		expectBody     string
		expectHTTPCode int
	}{
		{
			name: "should return Internal error if ListGPUNodes return error",
			mockFn: func(u *mocks.MockHardwareUsecase) {
				u.On("ListGPUNodes", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectBody:     "{\"code\":500,\"message\":\"Error\"}",
			expectHTTPCode: http.StatusInternalServerError,
		},
		{
			name: "should return list of GPU nodes successfully",
			mockFn: func(u *mocks.MockHardwareUsecase) {
				u.On("ListGPUNodes", mock.Anything, mock.Anything).Return(&dto.ListGPUNodesResponse{
					Message: "",
					Data: &[]dto.GPUNode{
						{
							NodeName:    "gpu-node-1",
							Name:        "Tesla-T4",
							GPUModel:    "Tesla-T4",
							GPUCount:    1,
							GPUMemoryGB: 16,
							Deployments: []dto.DeploymentGPUInfo{
								{
									Name:               "space/test-deployment",
									GPURequest:         1,
									MemoryBytesRequest: 8589934592, // 8GB in bytes
									CPURequest:         2,
									RepoID:             mockRepoID,
								},
							},
						},
						{
							NodeName:    "gpu-node-2",
							Name:        "Tesla-A100",
							GPUModel:    "Tesla-A100",
							GPUCount:    2,
							GPUMemoryGB: 40,
						},
					},
					Pagination: &dto.Pagination{},
				}, nil)
			},
			expectBody:     "{\"data\":[{\"node_name\":\"gpu-node-1\",\"name\":\"Tesla-T4\",\"gpu_model\":\"Tesla-T4\",\"gpu_count\":1,\"gpu_memory_gb\":16,\"deployments\":[{\"deployment_type\":\"\",\"name\":\"space/test-deployment\",\"gpu_request\":1,\"memory_bytes_request\":8589934592,\"cpu_request\":2,\"repo_id\":\"spaces/test-namespace/test-repo\"}]},{\"node_name\":\"gpu-node-2\",\"name\":\"Tesla-A100\",\"gpu_model\":\"Tesla-A100\",\"gpu_count\":2,\"gpu_memory_gb\":40}],\"total\":0,\"page_no\":0,\"page_size\":0}",
			expectHTTPCode: http.StatusOK,
		},
	}

	for _, testcase := range testcases {
		t.Run(testcase.name, func(t *testing.T) {
			usecase := &mocks.MockHardwareUsecase{}
			testcase.mockFn(usecase)
			handler := handlers.NewHardwareHandler(nil, usecase)

			rr := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(rr)
			c.Set(enums.USER_ID, mockUserID)
			c.Request = httptest.NewRequest(http.MethodGet, "/gpu-nodes", nil)

			handler.ListGPUNodes(c)

			assert.Equal(t, testcase.expectHTTPCode, rr.Code)
			assert.Equal(t, testcase.expectBody, rr.Body.String())
		})
	}
}
