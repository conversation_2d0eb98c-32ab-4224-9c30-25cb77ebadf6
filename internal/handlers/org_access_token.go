package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"

	"api-server/internal/dto"
	"api-server/internal/usecase/org_access_token"
	"api-server/pkg/oteltrace"
	"api-server/pkg/otelzap"
	"api-server/pkg/validator"
)

type OrgAccessTokenHandler interface {
	CreateNewOrgAccessToken(ctx *gin.Context)
	ListOrgAccessToken(ctx *gin.Context)
	DeleteOrgAccessToken(ctx *gin.Context)
	VerifyOrgAccessToken(ctx *gin.Context)
}

type orgAccessTokenHandlerImpl struct {
	usecase org_access_token.OrgAccessTokenUsecase
}

func NewOrgAccessTokenHandler(usecase org_access_token.OrgAccessTokenUsecase) OrgAccessTokenHandler {
	return &orgAccessTokenHandlerImpl{
		usecase: usecase,
	}
}

// CreateNewOrgAccessToken godoc
//
//	@Summary	Create new org access token
//	@Tags		Access Token
//	@Accept		json
//	@Produce	json
//	@Param		model	body		dto.CreateOrgAccessTokenRequest		true	"Access token"
//	@Success	201		{object}	dto.CreateOrgAccessTokenResponse	"Return created token"
//	@Failure	400		{object}	dto.HTTPError						"Bad Request - invalid request"
//	@Failure	404		{object}	dto.HTTPError						"Not Found"
//	@Failure	500		{object}	dto.HTTPError						"Internal Server Error - Encountered an unexpected condition"
//	@Router		/organizations/access_tokens [post]
//
//	@Security	Bearer
func (u *orgAccessTokenHandlerImpl) CreateNewOrgAccessToken(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(ParseContext(c), "handlers.access_token.CreateNewOrgAccessToken",
		trace.WithAttributes(
			attribute.String("action", "CREATE_ACCESS_TOKEN"),
		))
	defer span.End()

	currentUserId, err := GetCurrentUserId(ctx)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "failed to get current user", err,
			zap.String("action", "LIST_ACCESS_TOKENS"),
			zap.String("status", "failed"))
		dto.ErrorResponse(c, dto.NewUnauthorizedError(err, http.StatusText(http.StatusUnauthorized)))
		return
	}

	var req dto.CreateOrgAccessTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		span.SetStatus(codes.Error, "failed to bind request")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "failed to bind request", err,
			zap.String("action", "CREATE_ACCESS_TOKEN"),
			zap.String("status", "failed"))
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	orgId, err := uuid.Parse(c.Param("org_id"))
	if err != nil {
		span.SetStatus(codes.Error, "failed to parse organization id")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	req.UserID = currentUserId
	req.OrgID = orgId
	if err := validator.Validate(req); err != nil {
		span.SetStatus(codes.Error, "invalid request")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "invalid request", err,
			zap.String("action", "CREATE_ACCESS_TOKEN"),
			zap.String("status", "failed"))
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	otelzap.InfoWithContext(ctx, "creating access token",
		zap.String("action", "CREATE_ACCESS_TOKEN"),
		zap.String("token_name", req.Name))

	resp, err := u.usecase.CreateOrgAccessToken(c, req)
	if err != nil {
		span.SetStatus(codes.Error, "usecase create access token failed")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "usecase create access token failed", err,
			zap.String("action", "CREATE_ACCESS_TOKEN"),
			zap.String("token_name", req.Name),
			zap.String("status", "failed"))
		dto.ErrorResponse(c, dto.NewInternalError(err))
		return
	}

	span.AddEvent("access token created successfully")
	span.SetStatus(codes.Ok, "access token created successfully")
	span.SetAttributes(attribute.String("status", "success"))
	otelzap.InfoWithContext(ctx, "access token created successfully",
		zap.String("action", "CREATE_ACCESS_TOKEN"),
		zap.String("token_name", req.Name),
		zap.String("status", "success"))
	c.JSON(http.StatusOK, resp)
}

// ListOrgAccessToken godoc
//
//	@Summary	List org access token
//	@Tags		Access Token
//	@Accept		json
//	@Produce	json
//	@Param		model	body		dto.ListOrgAccessTokenRequest	true	"Access token"
//	@Success	200		{object}	dto.ListOrgAccessTokenResponse	"Return list token"
//	@Failure	400		{object}	dto.HTTPError					"Bad Request - invalid request"
//	@Failure	404		{object}	dto.HTTPError					"Not Found"
//	@Failure	500		{object}	dto.HTTPError					"Internal Server Error - Encountered an unexpected condition"
//	@Router		/organizations/access_tokens [get]
//
//	@Security	Bearer
func (u *orgAccessTokenHandlerImpl) ListOrgAccessToken(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(ParseContext(c), "handlers.access_token.ListOrgAccessToken",
		trace.WithAttributes(
			attribute.String("action", "LIST_ACCESS_TOKENS"),
		))
	defer span.End()

	currentUserId, err := GetCurrentUserId(ctx)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "failed to get current user", err,
			zap.String("action", "LIST_ACCESS_TOKENS"),
			zap.String("status", "failed"))
		dto.ErrorResponse(c, dto.NewUnauthorizedError(err, http.StatusText(http.StatusUnauthorized)))
		return
	}

	var req dto.ListOrgAccessTokenRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		span.SetStatus(codes.Error, "failed to bind query")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "failed to bind query", err,
			zap.String("action", "LIST_ACCESS_TOKENS"),
			zap.String("status", "failed"))
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	orgId, err := uuid.Parse(c.Param("org_id"))
	if err != nil {
		span.SetStatus(codes.Error, "failed to parse organization id")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	req.UserID = currentUserId
	req.OrgID = orgId
	otelzap.InfoWithContext(ctx, "listing access tokens",
		zap.String("action", "LIST_ACCESS_TOKENS"))

	if err := validator.Validate(req); err != nil {
		span.SetStatus(codes.Error, "failed to validate input")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	resp, err := u.usecase.ListOrgAccessToken(c, req)
	if err != nil {
		span.SetStatus(codes.Error, "usecase list access token failed")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "usecase list access token failed", err,
			zap.String("action", "LIST_ACCESS_TOKENS"),
			zap.String("status", "failed"))
		dto.ErrorResponse(c, dto.NewInternalError(err))
		return
	}

	span.AddEvent("access tokens listed successfully")
	span.SetStatus(codes.Ok, "access tokens listed successfully")
	span.SetAttributes(attribute.String("status", "success"))
	otelzap.InfoWithContext(ctx, "access tokens listed successfully",
		zap.String("action", "LIST_ACCESS_TOKENS"),
		zap.String("status", "success"))
	c.JSON(http.StatusOK, resp)
}

// DeleteOrgAccessToken godoc
//
//	@Summary	Delete org access token
//	@Tags		Access Token
//	@Accept		json
//	@Produce	json
//	@Param		id	path		string			true	"Access token ID"
//	@Success	204	string		string			"No content"
//	@Failure	400	{object}	dto.HTTPError	"Bad Request - invalid request"
//	@Failure	404	{object}	dto.HTTPError	"Not Found"
//	@Failure	500	{object}	dto.HTTPError	"Internal Server Error - Encountered an unexpected condition"
//	@Router		/organizations/access_tokens/{id} [delete]
//
//	@Security	Bearer
func (u *orgAccessTokenHandlerImpl) DeleteOrgAccessToken(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(ParseContext(c), "handlers.access_token.DeleteOrgAccessToken",
		trace.WithAttributes(
			attribute.String("action", "DELETE_ACCESS_TOKEN"),
			attribute.String("access_token_id", c.Param("id")),
		))
	defer span.End()

	currentUserId, err := GetCurrentUserId(ctx)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "failed to get current user", err,
			zap.String("action", "LIST_ACCESS_TOKENS"),
			zap.String("status", "failed"))
		dto.ErrorResponse(c, dto.NewUnauthorizedError(err, http.StatusText(http.StatusUnauthorized)))
		return
	}

	orgId, err := uuid.Parse(c.Param("org_id"))
	if err != nil {
		span.SetStatus(codes.Error, "failed to parse organization id")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	accessTokenID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		span.SetStatus(codes.Error, "invalid access token ID format")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "invalid access token ID format", err,
			zap.String("action", "DELETE_ACCESS_TOKEN"),
			zap.String("access_token_id", c.Param("id")),
			zap.String("status", "failed"))
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	otelzap.InfoWithContext(ctx, "deleting access token",
		zap.String("action", "DELETE_ACCESS_TOKEN"),
		zap.String("access_token_id", accessTokenID.String()))

	err = u.usecase.DeleteOrgAccessToken(ctx, currentUserId, orgId, accessTokenID)
	if err != nil {
		span.SetStatus(codes.Error, "usecase delete access token failed")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "usecase delete access token failed", err,
			zap.String("action", "DELETE_ACCESS_TOKEN"),
			zap.String("access_token_id", accessTokenID.String()),
			zap.String("status", "failed"))
		dto.ErrorResponse(c, dto.NewInternalError(err))
		return
	}

	span.AddEvent("access token deleted successfully")
	span.SetStatus(codes.Ok, "access token deleted successfully")
	span.SetAttributes(attribute.String("status", "success"))
	otelzap.InfoWithContext(ctx, "access token deleted successfully",
		zap.String("action", "DELETE_ACCESS_TOKEN"),
		zap.String("access_token_id", accessTokenID.String()),
		zap.String("status", "success"))
	c.JSON(http.StatusNoContent, nil)
}

// VerifyOrgAccessToken godoc
//
//	@Summary	Verify org access token validity
//	@Tags		Access Token
//	@Accept		json
//	@Produce	json
//	@Param		model	body		dto.VerifyOrgAccessTokenRequest			true	"Access token to verify"
//	@Success	200		{object}	dto.VerifyOrgAccessTokenHTTPResponse	"Return verification result"
//	@Failure	400		{object}	dto.HTTPError							"Bad Request - invalid request"
//	@Failure	500		{object}	dto.HTTPError							"Internal Server Error - Encountered an unexpected condition"
//	@Router		/organizations/access_tokens/verify [post]
func (u *orgAccessTokenHandlerImpl) VerifyOrgAccessToken(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(ParseContext(c), "handlers.access_token.VerifyOrgAccessToken",
		trace.WithAttributes(
			attribute.String("action", "VERIFY_ACCESS_TOKEN"),
		))
	defer span.End()

	var req dto.VerifyOrgAccessTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		span.SetStatus(codes.Error, "failed to bind request")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "failed to bind request", err,
			zap.String("action", "VERIFY_ACCESS_TOKEN"),
			zap.String("status", "failed"))
		c.JSON(http.StatusBadRequest, dto.NewHTTPError(http.StatusBadRequest, err))
		return
	}

	otelzap.InfoWithContext(ctx, "verifying access token",
		zap.String("action", "VERIFY_ACCESS_TOKEN"))

	resp, err := u.usecase.VerifyOrgAccessToken(ctx, req.OrgAccessToken)
	if err != nil {
		span.SetStatus(codes.Error, "usecase verify access token failed")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "usecase verify access token failed", err,
			zap.String("action", "VERIFY_ACCESS_TOKEN"),
			zap.String("status", "failed"))
		c.JSON(http.StatusInternalServerError, dto.NewHTTPError(http.StatusInternalServerError, err))
		return
	}

	span.AddEvent("access token verified successfully")
	span.SetStatus(codes.Ok, "access token verified successfully")
	span.SetAttributes(attribute.String("status", "success"))
	otelzap.InfoWithContext(ctx, "access token verified successfully",
		zap.String("action", "VERIFY_ACCESS_TOKEN"),
		zap.String("status", "success"))
	c.JSON(http.StatusOK, dto.VerifyOrgAccessTokenHTTPResponse{
		Data: resp,
	})
}
