package handlers_test

import (
	"bytes"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"api-server/configs"
	"api-server/internal/dto"
	"api-server/internal/enums"
	"api-server/internal/handlers"
	"api-server/internal/types"
	"api-server/internal/usecase"
	"api-server/internal/usecase/ecr"
	ecrMocks "api-server/internal/usecase/ecr/mocks"
)

func TestNewECRHandler(t *testing.T) {
	gin.SetMode(gin.TestMode)

	config := &configs.GlobalConfig{}
	mockUsecase := &ecrMocks.MockECRUsecase{}

	handler := handlers.NewECRHandler(config, mockUsecase)

	assert.NotNil(t, handler)
}

func TestCreateECRDeployment(t *testing.T) {
	gin.SetMode(gin.TestMode)

	mockUserID := uuid.New()
	mockDeployment := &dto.ECRDeployment{
		ID:             uuid.New(),
		DeploymentName: "test-deployment",
		ImageURI:       "123456789012.dkr.ecr.us-west-2.amazonaws.com/my-repo:latest",
		NodeName:       "cpu",
		Port:           8080,
		Status:         "running",
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	type dependencies struct {
		usecase *ecrMocks.MockECRUsecase
	}

	tests := []struct {
		name          string
		input         dto.CreateECRDeploymentInput
		ctxValue      interface{}
		mockFn        func(d *dependencies)
		expStatusCode int
		expectErr     *dto.HTTPError
	}{
		{
			name: "should create deployment successfully",
			input: dto.CreateECRDeploymentInput{
				DeploymentName: "test-deployment",
				ImageURI:       "123456789012.dkr.ecr.us-west-2.amazonaws.com/my-repo:latest",
				NodeName:       "cpu",
				Port:           8080,
				Env:            map[string]string{"ENV": "test"},
				OwnerType:      "user",
			},
			ctxValue: mockUserID,
			mockFn: func(d *dependencies) {
				d.usecase.On("CreateECRDeployment", mock.Anything, mockUserID, mock.AnythingOfType("ecr.CreateECRDeploymentInput")).
					Return(mockDeployment, nil)
			},
			expStatusCode: http.StatusOK,
		},
		{
			name: "should fail when user ID not found",
			input: dto.CreateECRDeploymentInput{
				DeploymentName: "test-deployment",
				ImageURI:       "123456789012.dkr.ecr.us-west-2.amazonaws.com/my-repo:latest",
				NodeName:       "cpu",
				Port:           8080,
				Env:            map[string]string{"ENV": "test"},
				OwnerType:      "user",
			},
			ctxValue: nil,
			mockFn: func(d *dependencies) {
				// No mock calls expected
			},
			expStatusCode: http.StatusForbidden,
			expectErr:     dto.NewForbiddenError(errors.New("current user id not found")),
		},
		{
			name: "should fail when usecase returns error",
			input: dto.CreateECRDeploymentInput{
				DeploymentName: "test-deployment",
				ImageURI:       "123456789012.dkr.ecr.us-west-2.amazonaws.com/my-repo:latest",
				NodeName:       "cpu",
				Port:           8080,
				Env:            map[string]string{"ENV": "test"},
				OwnerType:      "user",
			},
			ctxValue: mockUserID,
			mockFn: func(d *dependencies) {
				d.usecase.On("CreateECRDeployment", mock.Anything, mockUserID, mock.AnythingOfType("ecr.CreateECRDeploymentInput")).
					Return(nil, usecase.ErrInternal)
			},
			expStatusCode: http.StatusInternalServerError,
			expectErr:     dto.NewErrorFromUsecase(usecase.ErrInternal),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				usecase: &ecrMocks.MockECRUsecase{},
			}
			tt.mockFn(d)

			handler := handlers.NewECRHandler(&configs.GlobalConfig{}, d.usecase)
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			if tt.ctxValue != nil {
				c.Set("user_id", tt.ctxValue)
			}

			inputJSON, _ := json.Marshal(tt.input)
			c.Request = httptest.NewRequest(http.MethodPost, "/ecr/deployments", bytes.NewBuffer(inputJSON))
			c.Request.Header.Set("Content-Type", "application/json")

			handler.CreateECRDeployment(c)

			assert.Equal(t, tt.expStatusCode, w.Code)
			if tt.expectErr != nil {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expectErr.Code, response.Code)
			}

			d.usecase.AssertExpectations(t)
		})
	}
}

func TestCreateECRDeploymentEnv(t *testing.T) {
	gin.SetMode(gin.TestMode)

	mockUserID := uuid.New()
	mockDeploymentID := uuid.New()

	type dependencies struct {
		usecase *ecrMocks.MockECRUsecase
	}

	tests := []struct {
		name          string
		deploymentID  string
		input         dto.CreateECRDeploymentEnvInput
		ctxValue      interface{}
		mockFn        func(d *dependencies)
		expStatusCode int
		expectErr     *dto.HTTPError
	}{
		{
			name:         "should create environment variable successfully",
			deploymentID: mockDeploymentID.String(),
			input: dto.CreateECRDeploymentEnvInput{
				Key:   "TEST_KEY",
				Value: "test_value",
			},
			ctxValue: mockUserID,
			mockFn: func(d *dependencies) {
				d.usecase.On("CreateECRDeploymentEnv", mock.Anything, mockUserID, mock.AnythingOfType("dto.CreateECRDeploymentEnvInput")).
					Return(nil)
			},
			expStatusCode: http.StatusNoContent,
		},
		{
			name:         "should fail when deployment ID is invalid",
			deploymentID: "invalid-uuid",
			input: dto.CreateECRDeploymentEnvInput{
				Key:   "TEST_KEY",
				Value: "test_value",
			},
			ctxValue: mockUserID,
			mockFn: func(d *dependencies) {
				// No mock calls expected
			},
			expStatusCode: http.StatusBadRequest,
		},
		{
			name:         "should fail when user ID not found",
			deploymentID: mockDeploymentID.String(),
			input: dto.CreateECRDeploymentEnvInput{
				Key:   "TEST_KEY",
				Value: "test_value",
			},
			ctxValue: nil,
			mockFn: func(d *dependencies) {
				// No mock calls expected
			},
			expStatusCode: http.StatusForbidden,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				usecase: &ecrMocks.MockECRUsecase{},
			}
			tt.mockFn(d)

			handler := handlers.NewECRHandler(&configs.GlobalConfig{}, d.usecase)
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			if tt.ctxValue != nil {
				c.Set("user_id", tt.ctxValue)
			}

			c.Params = gin.Params{
				{Key: "ecr_id", Value: tt.deploymentID},
			}

			inputJSON, _ := json.Marshal(tt.input)
			c.Request = httptest.NewRequest(http.MethodPost, "/ecr/deployments/"+tt.deploymentID+"/env", bytes.NewBuffer(inputJSON))
			c.Request.Header.Set("Content-Type", "application/json")

			handler.CreateECRDeploymentEnv(c)

			assert.Equal(t, tt.expStatusCode, w.Code)

			d.usecase.AssertExpectations(t)
		})
	}
}

func TestUpdateECRDeploymentEnv(t *testing.T) {
	gin.SetMode(gin.TestMode)

	mockUserID := uuid.New()
	mockDeploymentID := uuid.New()

	type dependencies struct {
		usecase *ecrMocks.MockECRUsecase
	}

	tests := []struct {
		name          string
		deploymentID  string
		input         dto.UpdateECRDeploymentEnvInput
		ctxValue      interface{}
		mockFn        func(d *dependencies)
		expStatusCode int
	}{
		{
			name:         "should update environment variable successfully",
			deploymentID: mockDeploymentID.String(),
			input: dto.UpdateECRDeploymentEnvInput{
				OldKey: "OLD_KEY",
				Key:    "NEW_KEY",
				Value:  "new_value",
			},
			ctxValue: mockUserID,
			mockFn: func(d *dependencies) {
				d.usecase.On("UpdateECRDeploymentEnv", mock.Anything, mockUserID, mock.AnythingOfType("dto.UpdateECRDeploymentEnvInput")).
					Return(nil)
			},
			expStatusCode: http.StatusNoContent,
		},
		{
			name:         "should fail when deployment ID is invalid",
			deploymentID: "invalid-uuid",
			input: dto.UpdateECRDeploymentEnvInput{
				OldKey: "OLD_KEY",
				Key:    "NEW_KEY",
				Value:  "new_value",
			},
			ctxValue: mockUserID,
			mockFn: func(d *dependencies) {
				// No mock calls expected
			},
			expStatusCode: http.StatusBadRequest,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				usecase: &ecrMocks.MockECRUsecase{},
			}
			tt.mockFn(d)

			handler := handlers.NewECRHandler(&configs.GlobalConfig{}, d.usecase)
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			if tt.ctxValue != nil {
				c.Set("user_id", tt.ctxValue)
			}

			c.Params = gin.Params{
				{Key: "ecr_id", Value: tt.deploymentID},
			}

			inputJSON, _ := json.Marshal(tt.input)
			c.Request = httptest.NewRequest(http.MethodPut, "/ecr/deployments/"+tt.deploymentID+"/env", bytes.NewBuffer(inputJSON))
			c.Request.Header.Set("Content-Type", "application/json")

			handler.UpdateECRDeploymentEnv(c)

			assert.Equal(t, tt.expStatusCode, w.Code)

			d.usecase.AssertExpectations(t)
		})
	}
}

func TestDeleteECRDeploymentEnv(t *testing.T) {
	gin.SetMode(gin.TestMode)

	mockUserID := uuid.New()
	mockDeploymentID := uuid.New()

	type dependencies struct {
		usecase *ecrMocks.MockECRUsecase
	}

	tests := []struct {
		name          string
		deploymentID  string
		queryKey      string
		ctxValue      interface{}
		mockFn        func(d *dependencies)
		expStatusCode int
	}{
		{
			name:         "should delete environment variable successfully",
			deploymentID: mockDeploymentID.String(),
			queryKey:     "TEST_KEY",
			ctxValue:     mockUserID,
			mockFn: func(d *dependencies) {
				d.usecase.On("DeleteECRDeploymentEnv", mock.Anything, mockUserID, mock.AnythingOfType("dto.DeleteECRDeploymentEnvInput")).
					Return(nil)
			},
			expStatusCode: http.StatusNoContent,
		},
		{
			name:         "should fail when deployment ID is invalid",
			deploymentID: "invalid-uuid",
			queryKey:     "TEST_KEY",
			ctxValue:     mockUserID,
			mockFn: func(d *dependencies) {
				// No mock calls expected
			},
			expStatusCode: http.StatusBadRequest,
		},
		{
			name:         "should fail when user ID not found",
			deploymentID: mockDeploymentID.String(),
			queryKey:     "TEST_KEY",
			ctxValue:     nil,
			mockFn: func(d *dependencies) {
				// No mock calls expected
			},
			expStatusCode: http.StatusForbidden,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				usecase: &ecrMocks.MockECRUsecase{},
			}
			tt.mockFn(d)

			handler := handlers.NewECRHandler(&configs.GlobalConfig{}, d.usecase)
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			if tt.ctxValue != nil {
				c.Set("user_id", tt.ctxValue)
			}

			c.Params = gin.Params{
				{Key: "ecr_id", Value: tt.deploymentID},
			}

			c.Request = httptest.NewRequest(http.MethodDelete, "/ecr/deployments/"+tt.deploymentID+"/env?key="+tt.queryKey, nil)

			handler.DeleteECRDeploymentEnv(c)

			assert.Equal(t, tt.expStatusCode, w.Code)

			d.usecase.AssertExpectations(t)
		})
	}
}

func TestUpdateECRDeployment(t *testing.T) {
	gin.SetMode(gin.TestMode)

	mockUserID := uuid.New()
	mockDeploymentID := uuid.New()
	mockResult := &dto.ECRDeployment{
		ID:             uuid.New(),
		DeploymentName: "test-deployment",
		ImageURI:       "123456789012.dkr.ecr.us-west-2.amazonaws.com/my-repo:latest",
		NodeName:       "cpu",
		Port:           8080,
		Status:         "running",
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	type dependencies struct {
		usecase *ecrMocks.MockECRUsecase
	}

	tests := []struct {
		name          string
		deploymentID  string
		input         dto.UpdateECRDeploymentInput
		ctxValue      interface{}
		mockFn        func(d *dependencies)
		expStatusCode int
	}{
		{
			name:         "should update deployment successfully",
			deploymentID: mockDeploymentID.String(),
			input: dto.UpdateECRDeploymentInput{
				Port:     &[]int32{8080}[0],
				NodeName: &[]string{"cpu"}[0],
				NumCpu:   &[]uint{2}[0],
				Mem:      &types.HardwareMem{Amount: 1024, Unit: enums.MemoryUnit_MiB},
			},
			ctxValue: mockUserID,
			mockFn: func(d *dependencies) {
				d.usecase.On("UpdateECRDeployment", mock.Anything, mockUserID, mock.AnythingOfType("ecr.UpdateECRDeploymentInput")).
					Return(mockResult, nil)
			},
			expStatusCode: http.StatusNoContent,
		},
		{
			name:         "should fail when deployment ID is invalid",
			deploymentID: "invalid-uuid",
			input: dto.UpdateECRDeploymentInput{
				Port: &[]int32{8080}[0],
			},
			ctxValue: mockUserID,
			mockFn: func(d *dependencies) {
				// No mock calls expected
			},
			expStatusCode: http.StatusBadRequest,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				usecase: &ecrMocks.MockECRUsecase{},
			}
			tt.mockFn(d)

			handler := handlers.NewECRHandler(&configs.GlobalConfig{}, d.usecase)
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			if tt.ctxValue != nil {
				c.Set("user_id", tt.ctxValue)
			}

			c.Params = gin.Params{
				{Key: "ecr_id", Value: tt.deploymentID},
			}

			inputJSON, _ := json.Marshal(tt.input)
			c.Request = httptest.NewRequest(http.MethodPut, "/ecr/deployments/"+tt.deploymentID, bytes.NewBuffer(inputJSON))
			c.Request.Header.Set("Content-Type", "application/json")

			handler.UpdateECRDeployment(c)

			assert.Equal(t, tt.expStatusCode, w.Code)

			d.usecase.AssertExpectations(t)
		})
	}
}

func TestListECRDeployment(t *testing.T) {
	gin.SetMode(gin.TestMode)

	mockUserID := uuid.New()
	mockDeployments := []dto.ECRDeployment{
		{
			ID:             uuid.New(),
			DeploymentName: "test-deployment-1",
			ImageURI:       "123456789012.dkr.ecr.us-west-2.amazonaws.com/my-repo:latest",
			NodeName:       "cpu",
			Port:           8080,
			Status:         "running",
			CreatedAt:      time.Now(),
			UpdatedAt:      time.Now(),
		},
	}

	type dependencies struct {
		usecase *ecrMocks.MockECRUsecase
	}

	tests := []struct {
		name          string
		queryParams   string
		ctxValue      interface{}
		mockFn        func(d *dependencies)
		expStatusCode int
	}{
		{
			name:        "should list deployments successfully",
			queryParams: "?page=1&per_page=10",
			ctxValue:    mockUserID,
			mockFn: func(d *dependencies) {
				d.usecase.On("ListECRDeployment", mock.Anything, mockUserID, mock.AnythingOfType("dto.ListECRDeploymentInput")).
					Return(mockDeployments, int64(1), nil)
			},
			expStatusCode: http.StatusOK,
		},
		{
			name:        "should fail when user ID not found",
			queryParams: "?page=1&per_page=10",
			ctxValue:    nil,
			mockFn: func(d *dependencies) {
				// No mock calls expected
			},
			expStatusCode: http.StatusForbidden,
		},
		{
			name:        "should fail with invalid query parameters",
			queryParams: "?page=0&per_page=10",
			ctxValue:    mockUserID,
			mockFn: func(d *dependencies) {
				// No mock calls expected
			},
			expStatusCode: http.StatusBadRequest,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				usecase: &ecrMocks.MockECRUsecase{},
			}
			tt.mockFn(d)

			handler := handlers.NewECRHandler(&configs.GlobalConfig{}, d.usecase)
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			if tt.ctxValue != nil {
				c.Set("user_id", tt.ctxValue)
			}

			c.Request = httptest.NewRequest(http.MethodGet, "/ecr/deployments"+tt.queryParams, nil)

			handler.ListECRDeployment(c)

			assert.Equal(t, tt.expStatusCode, w.Code)

			d.usecase.AssertExpectations(t)
		})
	}
}

func TestGetECRDeployment(t *testing.T) {
	gin.SetMode(gin.TestMode)

	mockUserID := uuid.New()
	mockDeploymentID := uuid.New()
	mockDeployment := &dto.ECRDeployment{
		ID:             mockDeploymentID,
		DeploymentName: "test-deployment",
		ImageURI:       "123456789012.dkr.ecr.us-west-2.amazonaws.com/my-repo:latest",
		NodeName:       "cpu",
		Port:           8080,
		Status:         "running",
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	type dependencies struct {
		usecase *ecrMocks.MockECRUsecase
	}

	tests := []struct {
		name          string
		deploymentID  string
		ctxValue      interface{}
		mockFn        func(d *dependencies)
		expStatusCode int
	}{
		{
			name:         "should get deployment successfully",
			deploymentID: mockDeploymentID.String(),
			ctxValue:     mockUserID,
			mockFn: func(d *dependencies) {
				d.usecase.On("GetECRDeployment", mock.Anything, mockUserID, mockDeploymentID).
					Return(mockDeployment, nil)
			},
			expStatusCode: http.StatusOK,
		},
		{
			name:         "should fail when deployment ID is invalid",
			deploymentID: "invalid-uuid",
			ctxValue:     mockUserID,
			mockFn: func(d *dependencies) {
				// No mock calls expected
			},
			expStatusCode: http.StatusBadRequest,
		},
		{
			name:         "should fail when user ID not found",
			deploymentID: mockDeploymentID.String(),
			ctxValue:     nil,
			mockFn: func(d *dependencies) {
				// No mock calls expected
			},
			expStatusCode: http.StatusForbidden,
		},
		{
			name:         "should fail when deployment not found",
			deploymentID: mockDeploymentID.String(),
			ctxValue:     mockUserID,
			mockFn: func(d *dependencies) {
				d.usecase.On("GetECRDeployment", mock.Anything, mockUserID, mockDeploymentID).
					Return(nil, usecase.ErrRecordNotFound)
			},
			expStatusCode: http.StatusNotFound,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				usecase: &ecrMocks.MockECRUsecase{},
			}
			tt.mockFn(d)

			handler := handlers.NewECRHandler(&configs.GlobalConfig{}, d.usecase)
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			if tt.ctxValue != nil {
				c.Set("user_id", tt.ctxValue)
			}

			c.Params = gin.Params{
				{Key: "ecr_id", Value: tt.deploymentID},
			}

			c.Request = httptest.NewRequest(http.MethodGet, "/ecr/deployments/"+tt.deploymentID, nil)

			handler.GetECRDeployment(c)

			assert.Equal(t, tt.expStatusCode, w.Code)

			d.usecase.AssertExpectations(t)
		})
	}
}

func TestDeployECR(t *testing.T) {
	gin.SetMode(gin.TestMode)

	mockUserID := uuid.New()
	mockDeploymentID := uuid.New()
	mockResult := &ecr.KubernetesDeploymentResult{
		Namespace:      "default",
		DeploymentName: "test-deployment",
	}

	type dependencies struct {
		usecase *ecrMocks.MockECRUsecase
	}

	tests := []struct {
		name          string
		deploymentID  string
		ctxValue      interface{}
		mockFn        func(d *dependencies)
		expStatusCode int
	}{
		{
			name:         "should deploy ECR successfully",
			deploymentID: mockDeploymentID.String(),
			ctxValue:     mockUserID,
			mockFn: func(d *dependencies) {
				d.usecase.On("DeployECR", mock.Anything, mockUserID, mockDeploymentID).
					Return(mockResult, nil)
			},
			expStatusCode: http.StatusNoContent,
		},
		{
			name:         "should fail when deployment ID is invalid",
			deploymentID: "invalid-uuid",
			ctxValue:     mockUserID,
			mockFn: func(d *dependencies) {
				// No mock calls expected
			},
			expStatusCode: http.StatusBadRequest,
		},
		{
			name:         "should fail when user ID not found",
			deploymentID: mockDeploymentID.String(),
			ctxValue:     nil,
			mockFn: func(d *dependencies) {
				// No mock calls expected
			},
			expStatusCode: http.StatusForbidden,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				usecase: &ecrMocks.MockECRUsecase{},
			}
			tt.mockFn(d)

			handler := handlers.NewECRHandler(&configs.GlobalConfig{}, d.usecase)
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			if tt.ctxValue != nil {
				c.Set("user_id", tt.ctxValue)
			}

			c.Params = gin.Params{
				{Key: "ecr_id", Value: tt.deploymentID},
			}

			c.Request = httptest.NewRequest(http.MethodPost, "/ecr/deployments/"+tt.deploymentID+"/deploy", nil)

			handler.DeployECR(c)

			assert.Equal(t, tt.expStatusCode, w.Code)

			d.usecase.AssertExpectations(t)
		})
	}
}

func TestDeleteECRDeployment(t *testing.T) {
	gin.SetMode(gin.TestMode)

	mockUserID := uuid.New()
	mockDeploymentID := uuid.New()

	type dependencies struct {
		usecase *ecrMocks.MockECRUsecase
	}

	tests := []struct {
		name          string
		deploymentID  string
		ctxValue      interface{}
		mockFn        func(d *dependencies)
		expStatusCode int
	}{
		{
			name:         "should delete deployment successfully",
			deploymentID: mockDeploymentID.String(),
			ctxValue:     mockUserID,
			mockFn: func(d *dependencies) {
				d.usecase.On("DeleteECRDeployment", mock.Anything, mockUserID, mockDeploymentID).
					Return(nil)
			},
			expStatusCode: http.StatusNoContent,
		},
		{
			name:         "should fail when deployment ID is invalid",
			deploymentID: "invalid-uuid",
			ctxValue:     mockUserID,
			mockFn: func(d *dependencies) {
				// No mock calls expected
			},
			expStatusCode: http.StatusBadRequest,
		},
		{
			name:         "should fail when user ID not found",
			deploymentID: mockDeploymentID.String(),
			ctxValue:     nil,
			mockFn: func(d *dependencies) {
				// No mock calls expected
			},
			expStatusCode: http.StatusForbidden,
		},
		{
			name:         "should fail when usecase returns error",
			deploymentID: mockDeploymentID.String(),
			ctxValue:     mockUserID,
			mockFn: func(d *dependencies) {
				d.usecase.On("DeleteECRDeployment", mock.Anything, mockUserID, mockDeploymentID).
					Return(usecase.ErrInternal)
			},
			expStatusCode: http.StatusInternalServerError,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				usecase: &ecrMocks.MockECRUsecase{},
			}
			tt.mockFn(d)

			handler := handlers.NewECRHandler(&configs.GlobalConfig{}, d.usecase)
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			if tt.ctxValue != nil {
				c.Set("user_id", tt.ctxValue)
			}

			c.Params = gin.Params{
				{Key: "ecr_id", Value: tt.deploymentID},
			}

			c.Request = httptest.NewRequest(http.MethodDelete, "/ecr/deployments/"+tt.deploymentID, nil)

			handler.DeleteECRDeployment(c)

			assert.Equal(t, tt.expStatusCode, w.Code)

			d.usecase.AssertExpectations(t)
		})
	}
}

func TestStopECRDeployment(t *testing.T) {
	gin.SetMode(gin.TestMode)

	mockDeploymentID := uuid.New()

	type dependencies struct {
		usecase *ecrMocks.MockECRUsecase
	}

	tests := []struct {
		name          string
		deploymentID  string
		mockFn        func(d *dependencies)
		expStatusCode int
	}{
		{
			name:         "should stop deployment successfully",
			deploymentID: mockDeploymentID.String(),
			mockFn: func(d *dependencies) {
				d.usecase.On("StopECRDeployment", mock.Anything, mockDeploymentID).
					Return(nil)
			},
			expStatusCode: http.StatusNoContent,
		},
		{
			name:         "should fail when deployment ID is invalid",
			deploymentID: "invalid-uuid",
			mockFn: func(d *dependencies) {
				// No mock calls expected
			},
			expStatusCode: http.StatusBadRequest,
		},
		{
			name:         "should fail when usecase returns error",
			deploymentID: mockDeploymentID.String(),
			mockFn: func(d *dependencies) {
				d.usecase.On("StopECRDeployment", mock.Anything, mockDeploymentID).
					Return(usecase.ErrInternal)
			},
			expStatusCode: http.StatusInternalServerError,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				usecase: &ecrMocks.MockECRUsecase{},
			}
			tt.mockFn(d)

			handler := handlers.NewECRHandler(&configs.GlobalConfig{}, d.usecase)
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)

			c.Params = gin.Params{
				{Key: "ecr_id", Value: tt.deploymentID},
			}

			c.Request = httptest.NewRequest(http.MethodPost, "/ecr/deployments/"+tt.deploymentID+"/stop", nil)

			handler.StopECRDeployment(c)

			assert.Equal(t, tt.expStatusCode, w.Code)

			d.usecase.AssertExpectations(t)
		})
	}
}

func TestGetECRDeploymentPodLogs(t *testing.T) {
	gin.SetMode(gin.TestMode)

	mockDeploymentID := uuid.New()
	mockLogChan := make(chan dto.GetDeploymentLogsResponse, 1)
	mockLogChan <- dto.GetDeploymentLogsResponse{
		PodName: "test-pod",
		Content: "test log message",
	}
	close(mockLogChan)

	type dependencies struct {
		usecase *ecrMocks.MockECRUsecase
	}

	tests := []struct {
		name          string
		deploymentID  string
		mockFn        func(d *dependencies)
		expStatusCode int
	}{
		// {
		// 	name:         "should get pod logs successfully",
		// 	deploymentID: mockDeploymentID.String(),
		// 	mockFn: func(d *dependencies) {
		// 		d.usecase.On("GetPodLogs", mock.Anything, mockDeploymentID).
		// 			Return(mockLogChan, nil)
		// 	},
		// 	expStatusCode: http.StatusOK,
		// },
		{
			name:         "should fail when deployment ID is invalid",
			deploymentID: "invalid-uuid",
			mockFn: func(d *dependencies) {
				// No mock calls expected
			},
			expStatusCode: http.StatusBadRequest,
		},
		{
			name:         "should fail when usecase returns error",
			deploymentID: mockDeploymentID.String(),
			mockFn: func(d *dependencies) {
				d.usecase.On("GetPodLogs", mock.Anything, mockDeploymentID).
					Return(nil, usecase.ErrInternal)
			},
			expStatusCode: http.StatusInternalServerError,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				usecase: &ecrMocks.MockECRUsecase{},
			}
			tt.mockFn(d)

			handler := handlers.NewECRHandler(&configs.GlobalConfig{}, d.usecase)
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)

			c.Params = gin.Params{
				{Key: "ecr_id", Value: tt.deploymentID},
			}

			c.Request = httptest.NewRequest(http.MethodGet, "/ecr/deployments/"+tt.deploymentID+"/logs", nil)

			// Note: This test is simplified as testing streaming responses requires more complex setup
			handler.GetDeploymentPodLogs(c)

			assert.Equal(t, tt.expStatusCode, w.Code)

			d.usecase.AssertExpectations(t)
		})
	}
}

func TestGetECRDeploymentStatus(t *testing.T) {
	gin.SetMode(gin.TestMode)

	mockDeploymentID := uuid.New()
	mockResult := &dto.GetDeploymentStatusResponse{
		Data: &dto.DeploymentStatus{
			Status: "running",
		},
	}

	type dependencies struct {
		usecase *ecrMocks.MockECRUsecase
	}

	tests := []struct {
		name          string
		deploymentID  string
		mockFn        func(d *dependencies)
		expStatusCode int
	}{
		{
			name:         "should get deployment status successfully",
			deploymentID: mockDeploymentID.String(),
			mockFn: func(d *dependencies) {
				d.usecase.On("GetDeploymentStatus", mock.Anything, mockDeploymentID).
					Return(mockResult, nil)
			},
			expStatusCode: http.StatusOK,
		},
		{
			name:         "should fail when deployment ID is invalid",
			deploymentID: "invalid-uuid",
			mockFn: func(d *dependencies) {
				// No mock calls expected
			},
			expStatusCode: http.StatusBadRequest,
		},
		{
			name:         "should fail when usecase returns error",
			deploymentID: mockDeploymentID.String(),
			mockFn: func(d *dependencies) {
				d.usecase.On("GetDeploymentStatus", mock.Anything, mockDeploymentID).
					Return(nil, usecase.ErrInternal)
			},
			expStatusCode: http.StatusInternalServerError,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				usecase: &ecrMocks.MockECRUsecase{},
			}
			tt.mockFn(d)

			handler := handlers.NewECRHandler(&configs.GlobalConfig{}, d.usecase)
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)

			c.Params = gin.Params{
				{Key: "ecr_id", Value: tt.deploymentID},
			}

			c.Request = httptest.NewRequest(http.MethodGet, "/ecr/deployments/"+tt.deploymentID+"/status", nil)

			handler.GetDeploymentStatus(c)

			assert.Equal(t, tt.expStatusCode, w.Code)

			d.usecase.AssertExpectations(t)
		})
	}
}
