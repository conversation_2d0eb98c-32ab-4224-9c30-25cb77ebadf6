package handlers_test

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"image"
	"image/color"
	"image/png"
	"mime/multipart"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"api-server/configs"
	"api-server/internal/dto"
	"api-server/internal/enums"
	"api-server/internal/handlers"
	"api-server/internal/types"
	"api-server/internal/usecase"
	"api-server/internal/usecase/repository/mocks"
)

func TestUploadRepoAvatar(t *testing.T) {
	gin.SetMode(gin.TestMode)

	repoType := enums.RepoType_Models
	namespace := "namespace"
	repoName := "repo-name"
	repoID := *types.NewRepoID(repoType, namespace, repoName)

	userId := uuid.New()
	repoUUID := uuid.New()

	type dependencies struct {
		usecase *mocks.MockRepoUsecase
	}

	createMockContentFile := func() []byte {
		img := image.NewRGBA(image.Rect(0, 0, 100, 100))
		col := color.RGBA{R: 255, G: 0, B: 0, A: 255}
		for y := range 100 {
			for x := range 100 {
				img.Set(x, y, col)
			}
		}

		var buf bytes.Buffer
		png.Encode(&buf, img)

		return buf.Bytes()
	}

	createInvalidImageFile := func() []byte {
		return []byte("not an image")
	}

	tests := []struct {
		name          string
		inp           dto.UploadRepositoryAvatarInput
		mockFn        func(d *dependencies)
		expectErr     error
		ctxValue      uuid.UUID
		expStatusCode int
		fileContent   []byte
		fileName      string
		expResponse   map[string]interface{}
		invalidRepoID bool
	}{
		{
			name:          "should return error when GetCurrentUserId returns error",
			expStatusCode: http.StatusForbidden,
			mockFn:        func(d *dependencies) {},
			expectErr:     errors.New("Failed to get current user id"),
		},
		{
			name:          "should return error if repository ID is invalid",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusBadRequest,
			mockFn:        func(d *dependencies) {},
			expectErr:     errors.New("Invalid repository type"),
			invalidRepoID: true,
		},
		{
			name:          "should return error if file is missing",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusBadRequest,
			mockFn:        func(d *dependencies) {},
			expectErr:     errors.New("Http: no such file"),
			fileContent:   nil,
		},
		{
			name:          "should return error if file is not an image",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusBadRequest,
			mockFn:        func(d *dependencies) {},
			expectErr:     errors.New("Invalid content file"),
			fileContent:   createInvalidImageFile(),
			fileName:      "not-an-image.txt",
		},
		{
			name:          "should return error if upload repository avatar fails",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusInternalServerError,
			inp: dto.UploadRepositoryAvatarInput{
				RepoID:        repoID,
				CurrentUserId: userId,
			},
			mockFn: func(d *dependencies) {
				d.usecase.On("UploadAvatarRepo", mock.Anything, mock.Anything).Return("", errors.New("internal server error"))
			},
			expectErr:   errors.New("Internal server error"),
			fileContent: createMockContentFile(),
			fileName:    "valid-image.png",
		},
		{
			name:          "should return error if UploadAvatarRepo returns ErrUserNotFound",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusNotFound,
			inp: dto.UploadRepositoryAvatarInput{
				RepoID:        repoID,
				CurrentUserId: userId,
			},
			mockFn: func(d *dependencies) {
				d.usecase.On("UploadAvatarRepo", mock.Anything, mock.Anything).Return("", usecase.ErrUserNotFound)
			},
			expectErr:   usecase.ErrUserNotFound,
			fileContent: createMockContentFile(),
			fileName:    "valid-image.png",
		},
		{
			name:          "should return error if UploadAvatarRepo returns ErrRepositoryNotFound",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusNotFound,
			inp: dto.UploadRepositoryAvatarInput{
				RepoID:        repoID,
				CurrentUserId: userId,
			},
			mockFn: func(d *dependencies) {
				d.usecase.On("UploadAvatarRepo", mock.Anything, mock.Anything).Return("", usecase.ErrRepositoryNotFound)
			},
			expectErr:   errors.New("Repository not found"),
			fileContent: createMockContentFile(),
			fileName:    "valid-image.png",
		},
		{
			name:          "should upload repository avatar successfully",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusOK,
			inp: dto.UploadRepositoryAvatarInput{
				RepoID:        repoID,
				CurrentUserId: userId,
			},
			mockFn: func(d *dependencies) {
				d.usecase.On("UploadAvatarRepo", mock.Anything, mock.Anything).Return("http://localhost/sample.svg", nil)
			},
			fileContent: createMockContentFile(),
			fileName:    "valid-image.png",
			expResponse: map[string]interface{}{
				"avatar_url": "http://localhost/sample.svg",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup Gin context
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)

			// Set user_id in context
			c.Set("user_id", tt.ctxValue)

			// Setting repository ID in parameters
			if tt.invalidRepoID {
				c.Params = gin.Params{
					{Key: enums.REPO_TYPE, Value: "invalid-type"},
					{Key: enums.NAMESPACE, Value: namespace},
					{Key: enums.REPO_NAME, Value: repoName},
				}
			} else {
				c.Params = gin.Params{
					{Key: enums.REPO_TYPE, Value: repoType.String()},
					{Key: enums.NAMESPACE, Value: namespace},
					{Key: enums.REPO_NAME, Value: repoName},
				}
			}

			body := &bytes.Buffer{}
			writer := multipart.NewWriter(body)
			writer.WriteField("project_id", repoUUID.String())
			writer.WriteField("current_user_id", userId.String())

			if tt.fileContent != nil {
				part, _ := writer.CreateFormFile("file", tt.fileName)
				part.Write(tt.fileContent)
			}

			writer.Close()

			// Create an HTTP POST request with the multipart body
			c.Request = httptest.NewRequest(http.MethodPost, fmt.Sprintf("/repositories/%s/avatar", repoID.String()), body)
			c.Request.Header.Set("Content-Type", writer.FormDataContentType())

			d := &dependencies{
				usecase: &mocks.MockRepoUsecase{},
			}
			tt.mockFn(d)
			h := handlers.NewRepositoryHandler(&configs.GlobalConfig{
				FileConfig: &configs.FileConfig{
					ImageMaxSize: 5 * 1024 * 1024,
				},
			}, d.usecase)

			// Call the handler
			h.UploadRepoAvatar(c)

			// Assert the status code
			assert.Equal(t, tt.expStatusCode, w.Code)

			if tt.expectErr != nil {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Contains(t, response.Message, tt.expectErr.Error())
				assert.Equal(t, tt.expStatusCode, w.Code)
			} else if tt.expResponse != nil {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expResponse, response)
			}
		})
	}
}

func TestDeleteRepoAvatar(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// repoUUID := uuid.New()

	repoType := enums.RepoType_Models
	namespace := "namespace"
	repoName := "repo-name"
	repoID := *types.NewRepoID(repoType, namespace, repoName)

	type dependencies struct {
		usecase *mocks.MockRepoUsecase
	}

	tests := []struct {
		name          string
		inp           dto.DeleteRepositoryAvatarInput
		mockFn        func(d *dependencies)
		expectErr     error
		ctxValue      uuid.UUID
		expStatusCode int
		invalidRepoID bool
	}{
		{
			name:          "should return error if upload repository avatar fail",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusInternalServerError,
			inp: dto.DeleteRepositoryAvatarInput{
				RepoID: repoID,
			},
			mockFn: func(d *dependencies) {
				d.usecase.On("DeleteAvatarRepo", mock.Anything, mock.Anything).Return(errors.New("error"))
			},
			expectErr: errors.New("Error"),
		},
		{
			name:          "should return error if repository ID is invalid",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusBadRequest,
			mockFn:        func(d *dependencies) {},
			expectErr:     errors.New("Invalid repository type"),
			invalidRepoID: true,
		},
		{
			name:          "should return error when GetCurrentUserId returns error",
			expStatusCode: http.StatusForbidden,
			mockFn:        func(d *dependencies) {},
			expectErr:     errors.New("Failed to get current user id"),
		},
		{
			name:          "should upload repository avatar successfully",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusOK,
			mockFn: func(d *dependencies) {
				d.usecase.On("DeleteAvatarRepo", mock.Anything, mock.Anything).Return(nil)
			},
		},
		{
			name:          "should return error if DeleteAvatarRepo return ErrUserNotFound",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusNotFound,
			inp: dto.DeleteRepositoryAvatarInput{
				RepoID: repoID,
			},
			mockFn: func(d *dependencies) {
				d.usecase.On("DeleteAvatarRepo", mock.Anything, mock.Anything).Return(usecase.ErrUserNotFound)
			},
			expectErr: errors.New("User not found"),
		},
		{
			name:          "should return error if DeleteAvatarRepo return ErrRepositoryNotFound",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusNotFound,
			inp: dto.DeleteRepositoryAvatarInput{
				RepoID: repoID,
			},
			mockFn: func(d *dependencies) {
				d.usecase.On("DeleteAvatarRepo", mock.Anything, mock.Anything).Return(usecase.ErrRepositoryNotFound)
			},
			expectErr: errors.New("Repository not found"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup Gin context
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)

			// Set user_id in context
			c.Set("user_id", tt.ctxValue)

			// Setting repository ID in parameters
			// Setting repository ID in parameters
			if tt.invalidRepoID {
				c.Params = gin.Params{
					{Key: enums.REPO_TYPE, Value: "invalid-type"},
					{Key: enums.NAMESPACE, Value: namespace},
					{Key: enums.REPO_NAME, Value: repoName},
				}
			} else {
				c.Params = gin.Params{
					{Key: enums.REPO_TYPE, Value: repoType.String()},
					{Key: enums.NAMESPACE, Value: namespace},
					{Key: enums.REPO_NAME, Value: repoName},
				}
			}

			c.Request = httptest.NewRequest(http.MethodDelete, "/repositories/:repo_id/avatar", nil)

			d := &dependencies{
				usecase: &mocks.MockRepoUsecase{},
			}
			tt.mockFn(d)
			h := handlers.NewRepositoryHandler(nil, d.usecase)

			// Call the handler
			h.DeleteRepoAvatar(c)

			// Assert the status code
			assert.Equal(t, tt.expStatusCode, w.Code)

			if tt.expectErr != nil {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expectErr.Error(), response.Message)
				assert.Equal(t, tt.expStatusCode, w.Code)
			} else {
				var response map[string]any
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
			}
		})
	}
}
