package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"

	"api-server/internal/dto"
	"api-server/internal/usecase/access_token"
	"api-server/pkg/oteltrace"
	"api-server/pkg/otelzap"
	"api-server/pkg/validator"
)

type AccessTokenHandler interface {
	CreateNewAccessToken(ctx *gin.Context)
	ListAccessToken(ctx *gin.Context)
	DeleteAccessToken(ctx *gin.Context)
	VerifyAccessToken(ctx *gin.Context)
}

type accessTokenHandlerImpl struct {
	usecase access_token.AccessTokenUsecase
}

func NewAccessTokenHandler(usecase access_token.AccessTokenUsecase) AccessTokenHandler {
	return &accessTokenHandlerImpl{
		usecase: usecase,
	}
}

// CreateNewAccessToken godoc
//
//	@Summary	Create new access token
//	@Tags		Access Token
//	@Accept		json
//	@Produce	json
//	@Param		model	body		dto.CreateAccessTokenRequest	true	"Access token"
//	@Success	201		{object}	dto.CreateAccessTokenResponse	"Return created token"
//	@Failure	400		{object}	dto.HTTPError					"Bad Request - invalid request"
//	@Failure	404		{object}	dto.HTTPError					"Not Found"
//	@Failure	500		{object}	dto.HTTPError					"Internal Server Error - Encountered an unexpected condition"
//	@Router		/users/access_tokens [post]
//
//	@Security	Bearer
func (u *accessTokenHandlerImpl) CreateNewAccessToken(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(ParseContext(c), "handlers.access_token.CreateNewAccessToken",
		trace.WithAttributes(
			attribute.String("action", "CREATE_ACCESS_TOKEN"),
		))
	defer span.End()

	currentUserId, err := GetCurrentUserId(ctx)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "failed to get current user", err,
			zap.String("action", "CREATE_ACCESS_TOKEN"),
			zap.String("status", "failed"))
		dto.ErrorResponse(c, dto.NewUnauthorizedError(err, http.StatusText(http.StatusUnauthorized)))
		return
	}

	var req dto.CreateAccessTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		span.SetStatus(codes.Error, "failed to bind request")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "failed to bind request", err,
			zap.String("action", "CREATE_ACCESS_TOKEN"),
			zap.String("status", "failed"))
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	req.UserID = currentUserId
	if err := validator.Validate(req); err != nil {
		span.SetStatus(codes.Error, "invalid request")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "invalid request", err,
			zap.String("action", "CREATE_ACCESS_TOKEN"),
			zap.String("status", "failed"))
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	otelzap.InfoWithContext(ctx, "creating access token",
		zap.String("action", "CREATE_ACCESS_TOKEN"),
		zap.String("token_name", req.Name))

	req.UserID = currentUserId
	resp, err := u.usecase.CreateAccessToken(c, req)
	if err != nil {
		span.SetStatus(codes.Error, "usecase create access token failed")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "usecase create access token failed", err,
			zap.String("action", "CREATE_ACCESS_TOKEN"),
			zap.String("token_name", req.Name),
			zap.String("status", "failed"))
		dto.ErrorResponse(c, dto.NewInternalError(err))
		return
	}

	span.AddEvent("access token created successfully")
	span.SetStatus(codes.Ok, "access token created successfully")
	span.SetAttributes(attribute.String("status", "success"))
	otelzap.InfoWithContext(ctx, "access token created successfully",
		zap.String("action", "CREATE_ACCESS_TOKEN"),
		zap.String("token_name", req.Name),
		zap.String("status", "success"))
	c.JSON(http.StatusOK, resp)
}

// ListAccessToken godoc
//
//	@Summary	Create new access token
//	@Tags		Access Token
//	@Accept		json
//	@Produce	json
//	@Param		model	body		dto.ListAccessTokenRequest	true	"Access token"
//	@Success	200		{object}	dto.ListAccessTokenResponse	"Return list token"
//	@Failure	400		{object}	dto.HTTPError				"Bad Request - invalid request"
//	@Failure	404		{object}	dto.HTTPError				"Not Found"
//	@Failure	500		{object}	dto.HTTPError				"Internal Server Error - Encountered an unexpected condition"
//	@Router		/users/access_tokens [get]
//
//	@Security	Bearer
func (u *accessTokenHandlerImpl) ListAccessToken(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(ParseContext(c), "handlers.access_token.ListAccessToken",
		trace.WithAttributes(
			attribute.String("action", "LIST_ACCESS_TOKENS"),
		))
	defer span.End()

	currentUserId, err := GetCurrentUserId(ctx)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "failed to get current user", err,
			zap.String("action", "LIST_ACCESS_TOKENS"),
			zap.String("status", "failed"))
		dto.ErrorResponse(c, dto.NewUnauthorizedError(err, http.StatusText(http.StatusUnauthorized)))
		return
	}

	var req dto.ListAccessTokenRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		span.SetStatus(codes.Error, "failed to bind query")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "failed to bind query", err,
			zap.String("action", "LIST_ACCESS_TOKENS"),
			zap.String("status", "failed"))
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	otelzap.InfoWithContext(ctx, "listing access tokens",
		zap.String("action", "LIST_ACCESS_TOKENS"))

	req.UserID = currentUserId
	if err := validator.Validate(req); err != nil {
		span.SetStatus(codes.Error, "failed to validate input")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	resp, err := u.usecase.ListAccessToken(c, req)
	if err != nil {
		span.SetStatus(codes.Error, "usecase list access token failed")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "usecase list access token failed", err,
			zap.String("action", "LIST_ACCESS_TOKENS"),
			zap.String("status", "failed"))
		dto.ErrorResponse(c, dto.NewInternalError(err))
		return
	}

	span.AddEvent("access tokens listed successfully")
	span.SetStatus(codes.Ok, "access tokens listed successfully")
	span.SetAttributes(attribute.String("status", "success"))
	otelzap.InfoWithContext(ctx, "access tokens listed successfully",
		zap.String("action", "LIST_ACCESS_TOKENS"),
		zap.String("status", "success"))
	c.JSON(http.StatusOK, resp)
}

// DeleteAccessToken godoc
//
//	@Summary	Create new access token
//	@Tags		Access Token
//	@Accept		json
//	@Produce	json
//	@Param		id	path		string			true	"Access token ID"
//	@Success	204	string		string			"No content"
//	@Failure	400	{object}	dto.HTTPError	"Bad Request - invalid request"
//	@Failure	404	{object}	dto.HTTPError	"Not Found"
//	@Failure	500	{object}	dto.HTTPError	"Internal Server Error - Encountered an unexpected condition"
//	@Router		/users/access_tokens/{id} [delete]
//
//	@Security	Bearer
func (u *accessTokenHandlerImpl) DeleteAccessToken(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(ParseContext(c), "handlers.access_token.DeleteAccessToken",
		trace.WithAttributes(
			attribute.String("action", "DELETE_ACCESS_TOKEN"),
			attribute.String("access_token_id", c.Param("id")),
		))
	defer span.End()

	currentUserId, err := GetCurrentUserId(ctx)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "failed to get current user", err,
			zap.String("action", "DELETE_ACCESS_TOKEN"),
			zap.String("access_token_id", c.Param("id")),
			zap.String("status", "failed"))
		dto.ErrorResponse(c, dto.NewUnauthorizedError(err, http.StatusText(http.StatusUnauthorized)))
		return
	}

	id := c.Param("id")
	accessTokenID, err := uuid.Parse(id)
	if err != nil {
		span.SetStatus(codes.Error, "invalid access token ID format")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "invalid access token ID format", err,
			zap.String("action", "DELETE_ACCESS_TOKEN"),
			zap.String("access_token_id", c.Param("id")),
			zap.String("status", "failed"))
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	otelzap.InfoWithContext(ctx, "deleting access token",
		zap.String("action", "DELETE_ACCESS_TOKEN"),
		zap.String("access_token_id", accessTokenID.String()))

	err = u.usecase.DeleteAccessToken(ctx, currentUserId, accessTokenID)
	if err != nil {
		span.SetStatus(codes.Error, "usecase delete access token failed")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "usecase delete access token failed", err,
			zap.String("action", "DELETE_ACCESS_TOKEN"),
			zap.String("access_token_id", accessTokenID.String()),
			zap.String("status", "failed"))
		dto.ErrorResponse(c, dto.NewInternalError(err))
		return
	}

	span.AddEvent("access token deleted successfully")
	span.SetStatus(codes.Ok, "access token deleted successfully")
	span.SetAttributes(attribute.String("status", "success"))
	otelzap.InfoWithContext(ctx, "access token deleted successfully",
		zap.String("action", "DELETE_ACCESS_TOKEN"),
		zap.String("access_token_id", accessTokenID.String()),
		zap.String("status", "success"))
	c.JSON(http.StatusNoContent, nil)
}

// VerifyAccessToken godoc
//
//	@Summary	Verify access token validity
//	@Tags		Access Token
//	@Accept		json
//	@Produce	json
//	@Param		model	body		dto.VerifyAccessTokenRequest		true	"Access token to verify"
//	@Success	200		{object}	dto.VerifyAccessTokenHTTPResponse	"Return verification result"
//	@Failure	400		{object}	dto.HTTPError						"Bad Request - invalid request"
//	@Failure	500		{object}	dto.HTTPError						"Internal Server Error - Encountered an unexpected condition"
//	@Router		/users/access_tokens/verify [post]
func (u *accessTokenHandlerImpl) VerifyAccessToken(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(ParseContext(c), "handlers.access_token.VerifyAccessToken",
		trace.WithAttributes(
			attribute.String("action", "VERIFY_ACCESS_TOKEN"),
		))
	defer span.End()

	var req dto.VerifyAccessTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		span.SetStatus(codes.Error, "failed to bind request")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "failed to bind request", err,
			zap.String("action", "VERIFY_ACCESS_TOKEN"),
			zap.String("status", "failed"))
		c.JSON(http.StatusBadRequest, dto.NewHTTPError(http.StatusBadRequest, err))
		return
	}

	otelzap.InfoWithContext(ctx, "verifying access token",
		zap.String("action", "VERIFY_ACCESS_TOKEN"))

	resp, err := u.usecase.VerifyAccessToken(ctx, req.AccessToken)
	if err != nil {
		span.SetStatus(codes.Error, "usecase verify access token failed")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "usecase verify access token failed", err,
			zap.String("action", "VERIFY_ACCESS_TOKEN"),
			zap.String("status", "failed"))
		c.JSON(http.StatusInternalServerError, dto.NewHTTPError(http.StatusInternalServerError, err))
		return
	}

	span.AddEvent("access token verified successfully")
	span.SetStatus(codes.Ok, "access token verified successfully")
	span.SetAttributes(attribute.String("status", "success"))
	otelzap.InfoWithContext(ctx, "access token verified successfully",
		zap.String("action", "VERIFY_ACCESS_TOKEN"),
		zap.String("status", "success"))
	c.JSON(http.StatusOK, dto.VerifyAccessTokenHTTPResponse{
		Data: resp,
	})
}
