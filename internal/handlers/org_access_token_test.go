package handlers_test

import (
	"bytes"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"api-server/internal/dto"
	"api-server/internal/enums"
	"api-server/internal/handlers"
	"api-server/internal/usecase/org_access_token/mocks"
)

func TestCreateOrgAccessTokenHandler(t *testing.T) {
	gin.SetMode(gin.TestMode)

	mockUserID, _ := uuid.Parse("d2de43e6-fde6-4deb-a1d0-2d5c2c2e1038")
	orgId := uuid.New()
	now := time.Now()

	testcases := []struct {
		name             string
		mockFn           func(u *mocks.MockOrgAccessTokenUsecase)
		requestBody      dto.CreateOrgAccessTokenRequest
		setUserIDContext bool
		expectBody       string
		expectHTTPCode   int
	}{
		{
			name:             "should return Unauthorized error if missing userID from context",
			mockFn:           func(u *mocks.MockOrgAccessTokenUsecase) {},
			requestBody:      dto.CreateOrgAccessTokenRequest{},
			setUserIDContext: false,
			expectBody:       "{\"code\":401,\"message\":\"Unauthorized\"}",
			expectHTTPCode:   http.StatusUnauthorized,
		},
		{
			name:   "should fail validate Scopes in request body",
			mockFn: func(u *mocks.MockOrgAccessTokenUsecase) {},
			requestBody: dto.CreateOrgAccessTokenRequest{
				Name:      "name",
				Scopes:    []enums.RepoAccessTokenScope{},
				ExpiresAt: nil,
			},
			setUserIDContext: true,
			expectBody:       "{\"code\":400,\"message\":\"Key: 'CreateOrgAccessTokenRequest.Scopes' Error:Field validation for 'Scopes' failed on the 'min' tag\"}",
			expectHTTPCode:   http.StatusBadRequest,
		},
		{
			name:   "should fail validate Scopes in request body",
			mockFn: func(u *mocks.MockOrgAccessTokenUsecase) {},
			requestBody: dto.CreateOrgAccessTokenRequest{
				Name:      "name",
				Scopes:    []enums.RepoAccessTokenScope{enums.RepoAccessTokenScope("placeholder")},
				ExpiresAt: nil,
			},
			setUserIDContext: true,
			expectBody:       "{\"code\":400,\"message\":\"Key: 'CreateOrgAccessTokenRequest.Scopes[0]' Error:Field validation for 'Scopes[0]' failed on the 'oneof' tag\"}",
			expectHTTPCode:   http.StatusBadRequest,
		},
		{
			name: "should return Internal error if CreateOrgAccessToken return error",
			mockFn: func(u *mocks.MockOrgAccessTokenUsecase) {
				u.On("CreateOrgAccessToken", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			requestBody: dto.CreateOrgAccessTokenRequest{
				Name:      "name",
				Scopes:    []enums.RepoAccessTokenScope{enums.RepoAccessToken_API},
				ExpiresAt: &now,
			},
			setUserIDContext: true,
			expectBody:       "{\"code\":500,\"message\":\"Error\"}",
			expectHTTPCode:   http.StatusInternalServerError,
		},
		{
			name: "should pass",
			mockFn: func(u *mocks.MockOrgAccessTokenUsecase) {
				u.On("CreateOrgAccessToken", mock.Anything, mock.Anything).Return(&dto.CreateOrgAccessTokenResponse{
					Data: &dto.OrgAccessToken{
						ID:          mockUserID,
						Name:        "a",
						AccessToken: "@aqweasd123",
						Revoked:     false,
						ExpiresAt:   nil,
					},
				}, nil)
			},
			requestBody: dto.CreateOrgAccessTokenRequest{
				Name:      "name",
				Scopes:    []enums.RepoAccessTokenScope{enums.RepoAccessToken_API},
				ExpiresAt: &now,
			},
			setUserIDContext: true,
			expectBody:       "{\"data\":{\"id\":\"d2de43e6-fde6-4deb-a1d0-2d5c2c2e1038\",\"name\":\"a\",\"access_token\":\"@aqweasd123\",\"scopes\":\"\",\"revoked\":false,\"expires_at\":null,\"created_at\":\"0001-01-01T00:00:00Z\",\"updated_at\":\"0001-01-01T00:00:00Z\"}}",
			expectHTTPCode:   http.StatusOK,
		},
	}

	for _, testcase := range testcases {
		t.Run(testcase.name, func(t *testing.T) {
			usecase := &mocks.MockOrgAccessTokenUsecase{}
			testcase.mockFn(usecase)
			handler := handlers.NewOrgAccessTokenHandler(usecase)

			rr := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(rr)
			if testcase.setUserIDContext {
				c.Set(enums.USER_ID, mockUserID)
			}
			body, _ := json.Marshal(testcase.requestBody)
			c.Request = httptest.NewRequest(http.MethodPost, "/organizations/:org_id/access_tokens", bytes.NewBuffer(body))
			c.Params = gin.Params{
				{Key: "org_id", Value: orgId.String()},
			}
			handler.CreateNewOrgAccessToken(c)

			assert.Equal(t, testcase.expectHTTPCode, rr.Code)
			assert.Equal(t, testcase.expectBody, rr.Body.String())
		})
	}
}

func TestListOrgAccessTokenHandler(t *testing.T) {
	gin.SetMode(gin.TestMode)

	mockUserID, _ := uuid.Parse("d2de43e6-fde6-4deb-a1d0-2d5c2c2e1038")

	testcases := []struct {
		name             string
		mockFn           func(u *mocks.MockOrgAccessTokenUsecase)
		requestBody      dto.ListOrgAccessTokenRequest
		setUserIDContext bool
		expectBody       string
		expectHTTPCode   int
	}{
		{
			name:             "should return Unauthorized error if missing userID from context",
			mockFn:           func(u *mocks.MockOrgAccessTokenUsecase) {},
			requestBody:      dto.ListOrgAccessTokenRequest{},
			setUserIDContext: false,
			expectBody:       "{\"code\":401,\"message\":\"Unauthorized\"}",
			expectHTTPCode:   http.StatusUnauthorized,
		},
		{
			name: "should return Internal error if ListAccessToken return error",
			mockFn: func(u *mocks.MockOrgAccessTokenUsecase) {
				u.On("ListOrgAccessToken", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			requestBody:      dto.ListOrgAccessTokenRequest{},
			setUserIDContext: true,
			expectBody:       "{\"code\":500,\"message\":\"Error\"}",
			expectHTTPCode:   http.StatusInternalServerError,
		},
		{
			name: "should pass",
			mockFn: func(u *mocks.MockOrgAccessTokenUsecase) {
				u.On("ListOrgAccessToken", mock.Anything, mock.Anything).Return(&dto.ListOrgAccessTokenResponse{
					Data: &[]dto.OrgAccessToken{
						{
							ID:          mockUserID,
							Name:        "a",
							AccessToken: "@aqweasd123",
							Scopes:      "api",
							Revoked:     false,
							ExpiresAt:   nil,
						},
					},
				}, nil)
			},
			requestBody:      dto.ListOrgAccessTokenRequest{},
			setUserIDContext: true,
			expectBody:       "{\"data\":[{\"id\":\"d2de43e6-fde6-4deb-a1d0-2d5c2c2e1038\",\"name\":\"a\",\"access_token\":\"@aqweasd123\",\"scopes\":\"api\",\"revoked\":false,\"expires_at\":null,\"created_at\":\"0001-01-01T00:00:00Z\",\"updated_at\":\"0001-01-01T00:00:00Z\"}]}",
			expectHTTPCode:   http.StatusOK,
		},
	}

	orgId := uuid.New()
	for _, testcase := range testcases {
		t.Run(testcase.name, func(t *testing.T) {
			usecase := &mocks.MockOrgAccessTokenUsecase{}
			testcase.mockFn(usecase)
			handler := handlers.NewOrgAccessTokenHandler(usecase)

			rr := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(rr)
			if testcase.setUserIDContext {
				c.Set(enums.USER_ID, mockUserID)
			}
			c.Request = httptest.NewRequest(http.MethodGet, "/organizations/:org_id/access_tokens", nil)
			c.Params = gin.Params{
				{Key: "org_id", Value: orgId.String()},
			}
			handler.ListOrgAccessToken(c)

			assert.Equal(t, testcase.expectHTTPCode, rr.Code)
			assert.Equal(t, testcase.expectBody, rr.Body.String())
		})
	}
}

func TestDeleteOrgAccessTokenHandler(t *testing.T) {
	gin.SetMode(gin.TestMode)

	mockUserID, _ := uuid.Parse("d2de43e6-fde6-4deb-a1d0-2d5c2c2e1038")

	orgId := uuid.New()
	testcases := []struct {
		name             string
		mockFn           func(u *mocks.MockOrgAccessTokenUsecase)
		accessTokenID    string
		setUserIDContext bool
		expectBody       string
		expectHTTPCode   int
	}{
		{
			name:             "should return Unauthorized error if missing userID from context",
			mockFn:           func(u *mocks.MockOrgAccessTokenUsecase) {},
			accessTokenID:    "d2de43e6-fde6-4deb-a1d0-2d5c2c2e1038",
			setUserIDContext: false,
			expectBody:       "{\"code\":401,\"message\":\"Unauthorized\"}",
			expectHTTPCode:   http.StatusUnauthorized,
		},
		{
			name: "should return Internal error if DeleteAccessToken return error",
			mockFn: func(u *mocks.MockOrgAccessTokenUsecase) {
				u.On("DeleteOrgAccessToken", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(errors.New("error"))
			},
			accessTokenID:    "d2de43e6-fde6-4deb-a1d0-2d5c2c2e1038",
			setUserIDContext: true,
			expectBody:       "{\"code\":500,\"message\":\"Error\"}",
			expectHTTPCode:   http.StatusInternalServerError,
		},
		{
			name: "should pass",
			mockFn: func(u *mocks.MockOrgAccessTokenUsecase) {
				u.On("DeleteOrgAccessToken", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil)
			},
			accessTokenID:    "d2de43e6-fde6-4deb-a1d0-2d5c2c2e1038",
			setUserIDContext: true,
			expectBody:       "",
			expectHTTPCode:   http.StatusNoContent,
		},
	}

	for _, testcase := range testcases {
		t.Run(testcase.name, func(t *testing.T) {
			usecase := &mocks.MockOrgAccessTokenUsecase{}
			testcase.mockFn(usecase)
			handler := handlers.NewOrgAccessTokenHandler(usecase)

			rr := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(rr)
			if testcase.setUserIDContext {
				c.Set(enums.USER_ID, mockUserID)
			}
			c.Request = httptest.NewRequest(http.MethodDelete, "/organizations/:org_id/access_tokens/:id", nil)
			c.Params = gin.Params{
				{Key: "org_id", Value: orgId.String()},
				{Key: "id", Value: testcase.accessTokenID},
			}
			handler.DeleteOrgAccessToken(c)

			assert.Equal(t, testcase.expectHTTPCode, rr.Code)
			assert.Equal(t, testcase.expectBody, rr.Body.String())
		})
	}
}

func TestVerifyOrgAccessTokenHandler(t *testing.T) {
	gin.SetMode(gin.TestMode)

	testcases := []struct {
		name           string
		mockFn         func(u *mocks.MockOrgAccessTokenUsecase)
		requestBody    dto.VerifyOrgAccessTokenRequest
		expectBody     string
		expectHTTPCode int
	}{
		{
			name:   "should return Bad Request error if request body is invalid",
			mockFn: func(u *mocks.MockOrgAccessTokenUsecase) {},
			requestBody: dto.VerifyOrgAccessTokenRequest{
				OrgAccessToken: "",
			},
			expectBody:     "{\"code\":400,\"message\":\"Key: 'VerifyOrgAccessTokenRequest.OrgAccessToken' Error:Field validation for 'OrgAccessToken' failed on the 'required' tag\"}",
			expectHTTPCode: http.StatusBadRequest,
		},
		{
			name:   "should fail bind JSON body",
			mockFn: func(u *mocks.MockOrgAccessTokenUsecase) {},
			requestBody: dto.VerifyOrgAccessTokenRequest{
				OrgAccessToken: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJeyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJeyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ999eyJhYWwiOiJhYWwxIiwiYW1yIjpbeyJtZXRob2QiOiJwYXNzd29yZCIsInRpbWVzdGFtcCI6MTc0Mzk5NjQyOX1dLCJhcHBfbWV0YWRhdGEiOnsicHJvdmlkZXIiOiJlbWFpbCIsInByb3ZpZGVycyI6WyJlbWFpbCJdfSwiYXVkIjpbImF1dGhlbnRpY2F0ZWQiXSwiZW1haWwiOiJtYWNyb2V4cGFuc2lvbkBnbWFpbC5jb20iLCJleHAiOjE3NDQwMDAwMjksImlhdCI6MTc0Mzk5NjQyOSwiaXNfYW5vbnltb3VzIjpmYWxzZSwicGhvbmUiOiIiLCJyb2xlIjoiYXV0aGVudGljYXRlZCIsInNlc3Npb25faWQiOiI3MzNmNjYzZi1hNTE2LTQxNmQtYTUzYy1jN2EyODQ0YTExOTQiLCJzdWIiOiI1Yzg3NDZjOC1hYzhkLTQ2MTAtODMzYi1kYjMwYWY4OWVlM2MiLCJ1c2VyX2lkIjoiNWM4NzQ2YzgtYWM4ZC00NjEwLTgzM2ItZGIzMGFmODllZTNjIiwidXNlcl9tZXRhZGF0YSI6eyJlbWFpbCI6Im1hY3JvZXhwYW5zaW9uQGdtYWlsLmNvbSIsImVtYWlsX3ZlcmlmaWVkIjpmYWxzZSwicGhvbmVfdmVyaWZpZWQiOmZhbHNlLCJzdWIiOiI1Yzg3NDZjOC1hYzhkLTQ2MTAtODMzYi1kYjMwYWY4OWVlM2MifX0.C_u0NUenIX2Ta_X82kN9xy4LOcwnx7IwskcXfnQ7INw",
			},
			expectBody:     "{\"code\":400,\"message\":\"Key: 'VerifyOrgAccessTokenRequest.OrgAccessToken' Error:Field validation for 'OrgAccessToken' failed on the 'max' tag\"}",
			expectHTTPCode: http.StatusBadRequest,
		},
		{
			name: "should return Internal error if VerifyAccessToken returns error",
			mockFn: func(u *mocks.MockOrgAccessTokenUsecase) {
				u.On("VerifyOrgAccessToken", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			requestBody: dto.VerifyOrgAccessTokenRequest{
				OrgAccessToken: "test-token",
			},
			expectBody:     "{\"code\":500,\"message\":\"Error\"}",
			expectHTTPCode: http.StatusInternalServerError,
		},
		{
			name: "should pass with valid token",
			mockFn: func(u *mocks.MockOrgAccessTokenUsecase) {
				u.On("VerifyOrgAccessToken", mock.Anything, mock.Anything).Return(&dto.VerifyOrgAccessTokenResponse{
					Valid: true,
				}, nil)
			},
			requestBody: dto.VerifyOrgAccessTokenRequest{
				OrgAccessToken: "valid-token",
			},
			expectBody:     "{\"data\":{\"valid\":true}}",
			expectHTTPCode: http.StatusOK,
		},
		{
			name: "should pass with invalid token",
			mockFn: func(u *mocks.MockOrgAccessTokenUsecase) {
				u.On("VerifyOrgAccessToken", mock.Anything, mock.Anything).Return(&dto.VerifyOrgAccessTokenResponse{
					Valid: false,
				}, nil)
			},
			requestBody: dto.VerifyOrgAccessTokenRequest{
				OrgAccessToken: "invalid-token",
			},
			expectBody:     "{\"data\":{\"valid\":false}}",
			expectHTTPCode: http.StatusOK,
		},
	}

	orgId := uuid.New()
	for _, testcase := range testcases {
		t.Run(testcase.name, func(t *testing.T) {
			usecase := &mocks.MockOrgAccessTokenUsecase{}
			testcase.mockFn(usecase)
			handler := handlers.NewOrgAccessTokenHandler(usecase)

			rr := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(rr)
			body, _ := json.Marshal(testcase.requestBody)
			c.Request = httptest.NewRequest(http.MethodPost, "/organizations/:org_id/access_tokens/verify", bytes.NewBuffer(body))
			c.Params = gin.Params{
				{Key: "org_id", Value: orgId.String()},
			}
			handler.VerifyOrgAccessToken(c)

			assert.Equal(t, testcase.expectHTTPCode, rr.Code)
			assert.Equal(t, testcase.expectBody, rr.Body.String())
		})
	}
}
