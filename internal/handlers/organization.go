package handlers

import (
	"errors"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"go.opentelemetry.io/otel/codes"

	"api-server/configs"
	"api-server/internal/dto"
	"api-server/internal/usecase"
	"api-server/internal/usecase/organization"
	"api-server/internal/utils"
	"api-server/pkg/oteltrace"
	"api-server/pkg/validator"
)

type OrganizationHandler interface {
	CreateOrganization(c *gin.Context)
	DeleteOrganization(c *gin.Context)
	InviteUser(c *gin.Context)
	InviteUsers(c *gin.Context)
	ListCurrentUserOrganizations(c *gin.Context)
	ListAllOrganizations(c *gin.Context)
	GetOrganization(c *gin.Context)
	ListOrganizationMembers(c *gin.Context)
	GetMember(c *gin.Context)
	RemoveMember(c *gin.Context)
	UpdateMember(c *gin.Context)
	UploadOrganizationAvatar(c *gin.Context)
	DeleteOrganizationAvatar(c *gin.Context)
}

type organizationHandlerImpl struct {
	config  *configs.GlobalConfig
	usecase organization.OrganizationUsecase
}

func NewOrganizationHandler(config *configs.GlobalConfig, usecase organization.OrganizationUsecase) OrganizationHandler {
	return &organizationHandlerImpl{
		usecase: usecase,
		config:  config,
	}
}

// CreateOrganization godoc
//
//	@Summary	Create organization
//	@Tags		Organization
//	@Accept		json
//	@Produce	json
//	@Param		model	body		dto.CreateOrganizationInput		true	"Organization information"
//	@Success	201		{object}	dto.CreateOrganizationOutput	"Return Organization information"
//	@Failure	400		{object}	dto.HTTPError					"Bad Request - invalid request"
//	@Failure	404		{object}	dto.HTTPError					"Not Found"
//	@Failure	500		{object}	dto.HTTPError					"Internal Host Error - Encountered an unexpected condition"
//	@Router		/organizations [post]
//
//	@Security	Bearer
func (o *organizationHandlerImpl) CreateOrganization(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(ParseContext(c), "handlers.CreateOrganization")
	defer span.End()

	currentUserId, err := GetCurrentUserId(ctx)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewUnauthorizedError(err))
		return
	}

	var input dto.CreateOrganizationInput
	if err := c.ShouldBind(&input); err != nil {
		span.SetStatus(codes.Error, "failed to bind input")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	if input.File != nil {
		err = utils.ValidateImage(o.config.FileConfig.ImageMaxSize, input.File)
		if err != nil {
			span.SetStatus(codes.Error, "invalid image file")
			span.RecordError(err)
			dto.ErrorResponse(c, dto.NewBadRequestError(err, "invalid content file"))
			return
		}
	}

	input.UserCurrentId = currentUserId

	if err := validator.Validate(input); err != nil {
		span.SetStatus(codes.Error, "failed to validate input")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	resp, err := o.usecase.CreateOrganization(ctx, input)
	if err != nil {
		span.SetStatus(codes.Error, "failed to create organization")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewInternalError(err))
		return
	}

	span.AddEvent("organization created successfully")
	span.SetStatus(codes.Ok, "organization created successfully")
	c.JSON(http.StatusCreated, resp)
}

// DeleteOrganization godoc
//
//	@Summary	Delete organization
//	@Tags		Organization
//	@Accept		json
//	@Produce	json
//	@Param		id	path		string			true	"Organization ID"
//	@Success	200	{object}	string			"Return message"
//	@Failure	400	{object}	dto.HTTPError	"Bad Request - invalid request"
//	@Failure	401	{object}	dto.HTTPError	"Unauthorized - invalid token"
//	@Failure	404	{object}	dto.HTTPError	"Not Found"
//	@Failure	500	{object}	dto.HTTPError	"Internal Host Error - Encountered an unexpected condition"
//	@Router		/organizations/{id} [delete]
//
//	@Security	Bearer
func (o *organizationHandlerImpl) DeleteOrganization(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(ParseContext(c), "handlers.DeleteOrganization")
	defer span.End()

	currentUserId, err := GetCurrentUserId(ctx)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewUnauthorizedError(err))
		return
	}

	id := c.Param("org_id")
	if id == "" {
		span.SetStatus(codes.Error, "organization id is required")
		span.RecordError(errors.New("organization id is required"))
		dto.ErrorResponse(c, dto.NewBadRequestError(errors.New("organization id is required")))
		return
	}

	organizationId, err := uuid.Parse(id)
	if err != nil {
		span.SetStatus(codes.Error, "failed to parse organization id")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	if err := o.usecase.DeleteOrganization(ctx, dto.DeleteOrganizationInput{
		Id:            organizationId,
		CurrentUserId: currentUserId,
	}); err != nil {
		span.SetStatus(codes.Error, "failed to delete organization")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewInternalError(err))
		return
	}

	span.AddEvent("organization deleted successfully")
	span.SetStatus(codes.Ok, "organization deleted successfully")
	c.JSON(http.StatusOK, gin.H{"message": "Organization deleted successfully"})
}

// // InviteUser godoc
// //
// //	@Summary	Invite user to organization
// //	@Tags		Organization
// //	@Accept		json
// //	@Produce	json
// //	@Param		id		path		string						true	"Organization ID"
// //	@Param		model	body		dto.InviteOrgMemberInput	true	"Invite User to Organization information"
// //	@Success	200		{object}	string						"Return message invite organization"
// //	@Failure	400		{object}	dto.HTTPError				"Bad Request - invalid request"
// //	@Failure	404		{object}	dto.HTTPError				"Not Found"
// //	@Failure	500		{object}	dto.HTTPError				"Internal Host Error - Encountered an unexpected condition"
// //	@Router		/organizations/{id}/invite [post]
// //
// //	@Security	Bearer

func (o *organizationHandlerImpl) InviteUser(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(ParseContext(c), "handlers.InviteUser")
	defer span.End()

	currentUserId, err := GetCurrentUserId(ctx)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewUnauthorizedError(err))
		return
	}

	id, err := uuid.Parse(c.Param("org_id"))
	if err != nil {
		span.SetStatus(codes.Error, "failed to parse organization id")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	var input dto.InviteOrgMemberInput
	if err := c.ShouldBindJSON(&input); err != nil {
		span.SetStatus(codes.Error, "failed to bind input")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	input.OrgId = id
	input.CurrentUserID = currentUserId
	if err := validator.Validate(input); err != nil {
		span.SetStatus(codes.Error, "failed to validate input")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	if input.CurrentUserID == input.UserId {
		err := errors.New("cannot invite your self")
		span.SetStatus(codes.Error, err.Error())
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	if err := o.usecase.InviteUser(ctx, input); err != nil {
		span.SetStatus(codes.Error, "failed to invite user")
		span.RecordError(err)
		if errors.Is(err, usecase.ErrUserNotFound) || errors.Is(err, usecase.ErrOrganizationNotFound) {
			dto.ErrorResponse(c, dto.NewNotFoundError(err))
			return
		}
		dto.ErrorResponse(c, dto.NewInternalError(err))
		return
	}

	span.AddEvent("user invited to organization successfully")
	span.SetStatus(codes.Ok, "user invited to organization successfully")
	c.JSON(http.StatusOK, gin.H{"message": "User invited to organization successfully"})
}

// InviteUsers godoc
//
//	@Summary	Invite users to organization
//	@Tags		Organization
//	@Accept		json
//	@Produce	json
//	@Param		id		path		string						true	"Organization ID"
//	@Param		model	body		dto.InviteOrgMembersInput	true	"Invite Users to Organization information"
//	@Success	200		{object}	string						"Return message invite organization"
//	@Failure	400		{object}	dto.HTTPError				"Bad Request - invalid request"
//	@Failure	404		{object}	dto.HTTPError				"Not Found"
//	@Failure	500		{object}	dto.HTTPError				"Internal Host Error - Encountered an unexpected condition"
//	@Router		/organizations/{id}/invite [post]
//
//	@Security	Bearer
func (o *organizationHandlerImpl) InviteUsers(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(ParseContext(c), "handlers.InviteUser")
	defer span.End()

	currentUserId, err := GetCurrentUserId(ctx)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewUnauthorizedError(err))
		return
	}

	id, err := uuid.Parse(c.Param("org_id"))
	if err != nil {
		span.SetStatus(codes.Error, "failed to parse organization id")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	var input dto.InviteOrgMembersInput
	if err := c.ShouldBindJSON(&input); err != nil {
		span.SetStatus(codes.Error, "failed to bind input")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	input.OrgId = id
	input.CurrentUserID = currentUserId
	if err := validator.Validate(input); err != nil {
		span.SetStatus(codes.Error, "failed to validate input")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	if err := o.usecase.InviteMultipleOrgMembers(ctx, input); err != nil {
		span.SetStatus(codes.Error, "failed to invite user")
		span.RecordError(err)
		if errors.Is(err, usecase.ErrUserNotFound) || errors.Is(err, usecase.ErrOrganizationNotFound) {
			dto.ErrorResponse(c, dto.NewNotFoundError(err))
			return
		}
		dto.ErrorResponse(c, dto.NewInternalError(err))
		return
	}

	span.AddEvent("users invited to organization successfully")
	span.SetStatus(codes.Ok, "users invited to organization successfully")
	c.JSON(http.StatusOK, gin.H{"message": "Users invited to organization successfully"})
}

// ListCurrentUserOrganizations godoc
//
//	@Summary	List organizations
//	@Tags		Organization
//	@Accept		json
//	@Produce	json
//	@Success	200	{object}	dto.ListOrganizationsOutput	"Return list of organizations"
//	@Failure	403	{object}	dto.HTTPError				"Forbidden - invalid token"
//	@Failure	500	{object}	dto.HTTPError				"Internal Host Error - Encountered an unexpected condition"
//	@Router		/organizations/me [get]
//	@Security	Bearer
func (o *organizationHandlerImpl) ListCurrentUserOrganizations(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(ParseContext(c), "handlers.ListCurrentUserOrganizations")
	defer span.End()

	currentUserId, err := GetCurrentUserId(ctx)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewForbiddenError(err, "forbidden"))
		return
	}

	var input dto.ListOrganizationsInput
	if err := c.ShouldBindQuery(&input); err != nil {
		span.SetStatus(codes.Error, "failed to bind query")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	input.UserId = currentUserId
	if err := validator.Validate(input); err != nil {
		span.SetStatus(codes.Error, "failed to validate input")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	organizations, err := o.usecase.ListOrganizations(ctx, input)
	if err != nil {
		span.SetStatus(codes.Error, "failed to list organizations")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewInternalError(err))
		return
	}

	span.AddEvent("successfully retrieved list of organizations")
	span.SetStatus(codes.Ok, "successfully retrieved list of organizations")
	c.JSON(http.StatusOK, organizations)
}

// ListAllOrganizations godoc
//
//	@Summary	List organizations
//	@Tags		Organization
//	@Accept		json
//	@Produce	json
//	@Success	200	{object}	dto.ListOrganizationsOutput	"Return list of organizations"
//	@Failure	403	{object}	dto.HTTPError				"Forbidden - invalid token"
//	@Failure	500	{object}	dto.HTTPError				"Internal Host Error - Encountered an unexpected condition"
//	@Router		/organizations [get]
//	@Security	Bearer
func (o *organizationHandlerImpl) ListAllOrganizations(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(ParseContext(c), "handlers.ListOrganizations")
	defer span.End()

	var input dto.ListOrganizationsInput
	if err := c.ShouldBindQuery(&input); err != nil {
		span.SetStatus(codes.Error, "failed to bind query")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	if err := validator.Validate(input); err != nil {
		span.SetStatus(codes.Error, "failed to validate input")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	organizations, err := o.usecase.ListOrganizations(ctx, input)
	if err != nil {
		span.SetStatus(codes.Error, "failed to list organizations")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewInternalError(err))
		return
	}

	span.AddEvent("successfully retrieved list of organizations")
	span.SetStatus(codes.Ok, "successfully retrieved list of organizations")
	c.JSON(http.StatusOK, organizations)
}

// GetOrganization godoc
//
//	@Summary	Get organization info
//	@Tags		Organization
//	@Accept		json
//	@Produce	json
//	@Success	200	{object}	dto.FindOrganizationOutput	"Return organization entity info"
//	@Failure	403	{object}	dto.HTTPError				"Forbidden - invalid token"
//	@Failure	500	{object}	dto.HTTPError				"Internal Host Error - Encountered an unexpected condition"
//	@Router		/organizations/:org_id [get]
//	@Security	Bearer
func (o *organizationHandlerImpl) GetOrganization(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(ParseContext(c), "handlers.GetOrganization")
	defer span.End()

	currentUserId, err := GetCurrentUserId(ctx)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewForbiddenError(err, "forbidden"))
		return
	}

	id, err := uuid.Parse(c.Param("org_id"))
	if err != nil {
		span.SetStatus(codes.Error, "failed to parse organization id")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err, "failed to parse organization id"))
		return
	}

	org, err := o.usecase.FindOrganizationByID(ctx, dto.FindOrganizationInput{
		OrgId:         id,
		CurrentUserID: currentUserId,
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find organization")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewInternalError(err))
		return
	}

	span.AddEvent("successfully retrieved organization")
	span.SetStatus(codes.Ok, "successfully retrieved organization")
	c.JSON(http.StatusOK, org)
}

// ListOrganizationMembers godoc
//
//	@Summary	List organization members info
//	@Tags		Organization
//	@Accept		json
//	@Produce	json
//	@Success	200	{object}	dto.ListOrgMembersOutput	"Return organization members"
//	@Failure	403	{object}	dto.HTTPError				"Forbidden - invalid token"
//	@Failure	500	{object}	dto.HTTPError				"Internal Host Error - Encountered an unexpected condition"
//	@Router		/organizations/:org_id/members [get]
//	@Security	Bearer
func (o *organizationHandlerImpl) ListOrganizationMembers(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(ParseContext(c), "handlers.ListOrganizationMembers")
	defer span.End()

	currentUserId, err := GetCurrentUserId(ctx)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewForbiddenError(err, "forbidden"))
		return
	}

	var input dto.ListOrgMembersInput
	if err := c.ShouldBindQuery(&input); err != nil {
		span.SetStatus(codes.Error, "failed to bind query")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	if err := validator.Validate(input); err != nil {
		span.SetStatus(codes.Error, "failed to validate input")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	id, err := uuid.Parse(c.Param("org_id"))
	if err != nil {
		span.SetStatus(codes.Error, "failed to parse organization id")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err, "failed to parse organization id"))
		return
	}

	input.OrgId = id
	input.CurrentUserID = currentUserId

	org, err := o.usecase.ListOrganizationMembers(ctx, input)
	if err != nil {
		span.SetStatus(codes.Error, "failed to list organization members")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewInternalError(err))
		return
	}

	span.AddEvent("successfully retrieved organization members")
	span.SetStatus(codes.Ok, "successfully retrieved organization members")
	c.JSON(http.StatusOK, org)
}

// GetMember godoc
//
//	@Summary	Get Member in Organizanion
//	@Tags		Organization
//	@Accept		json
//	@Produce	json
//	@Failure	403	{object}	dto.HTTPError	"Forbidden - invalid token"
//	@Failure	500	{object}	dto.HTTPError	"Internal Host Error - Encountered an unexpected condition"
//	@Router		/organizations/{org_id}/members/{member_id} [get]
//	@Security	Bearer
func (o *organizationHandlerImpl) GetMember(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(ParseContext(c), "handlers.GetMember")
	defer span.End()

	orgId, err := uuid.Parse(c.Param("org_id"))
	if err != nil {
		span.SetStatus(codes.Error, "failed to parse organization id")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err, "failed to parse organization id"))
		return
	}

	memberId, err := uuid.Parse(c.Param("member_id"))
	if err != nil {
		span.SetStatus(codes.Error, "failed to parse member id")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err, "failed to parse member id"))
		return
	}

	var input dto.GetMemberOrganizaionInput
	input.OrgId = orgId
	input.MemberId = memberId
	member, err := o.usecase.GetMember(ctx, input)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get member")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewInternalError(err))
		return
	}

	span.AddEvent("successfully retrieved member")
	span.SetStatus(codes.Ok, "successfully retrieved member")
	c.JSON(http.StatusOK, member)
}

// RemoveMember godoc
//
//	@Summary	Remove Member in Organizanion
//	@Tags		Organization
//	@Accept		json
//	@Produce	json
//	@Failure	403	{object}	dto.HTTPError	"Forbidden - invalid token"
//	@Failure	500	{object}	dto.HTTPError	"Internal Host Error - Encountered an unexpected condition"
//	@Router		/organizations/{org_id}/members/{member_id} [delete]
//	@Security	Bearer
func (o *organizationHandlerImpl) RemoveMember(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(ParseContext(c), "handlers.RemoveMember")
	defer span.End()

	currentUserId, err := GetCurrentUserId(ctx)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewForbiddenError(err, "forbidden"))
		return
	}

	var input dto.RemoveMemberOrganizaionInput
	orgId, err := uuid.Parse(c.Param("org_id"))
	if err != nil {
		span.SetStatus(codes.Error, "failed to parse organization id")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	memberId, err := uuid.Parse(c.Param("member_id"))
	if err != nil {
		span.SetStatus(codes.Error, "failed to parse member id")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	input.OrgId = orgId
	input.MemberId = memberId
	if input.MemberId == currentUserId {
		span.SetStatus(codes.Error, "cannot remove yourself")
		span.RecordError(errors.New("cannot remove yourself"))
		dto.ErrorResponse(c, dto.NewForbiddenError(errors.New("cannot remove yourself"), "cannot remove yourself"))
		return
	}

	input.CurrentUserID = currentUserId
	err = o.usecase.RemoveMember(ctx, input)
	if err != nil {
		span.SetStatus(codes.Error, "failed to remove member")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewInternalError(err))
		return
	}

	span.AddEvent("successfully removed member")
	span.SetStatus(codes.Ok, "successfully removed member")
	c.JSON(http.StatusOK, gin.H{
		"message": "Remove member successfully",
	})
}

// UpdateMember godoc
//
//	@Summary	Update Member in Organizanion
//	@Tags		Organization
//	@Accept		json
//	@Produce	json
//	@Param		model	body		dto.UpdateMemberOrganizaionInput	true	"Invite User to Organization information"
//	@Failure	403		{object}	dto.HTTPError						"Forbidden - invalid token"
//	@Failure	500		{object}	dto.HTTPError						"Internal Host Error - Encountered an unexpected condition"
//	@Router		/organizations/{org_id}/members/{member_id} [put]
//	@Security	Bearer
func (o *organizationHandlerImpl) UpdateMember(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(ParseContext(c), "handlers.UpdateMember")
	defer span.End()

	currentUserId, err := GetCurrentUserId(ctx)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewForbiddenError(err, "forbidden"))
		return
	}

	var input dto.UpdateMemberOrganizaionInput
	if err := c.ShouldBindJSON(&input); err != nil {
		span.SetStatus(codes.Error, "failed to bind input")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	orgId, err := uuid.Parse(c.Param("org_id"))
	if err != nil {
		span.SetStatus(codes.Error, "failed to parse organization id")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	memberId, err := uuid.Parse(c.Param("member_id"))
	if err != nil {
		span.SetStatus(codes.Error, "failed to parse member id")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	input.OrgId = orgId
	input.MemberId = memberId
	if input.MemberId == currentUserId {
		span.SetStatus(codes.Error, "cannot update yourself")
		span.RecordError(errors.New("cannot update yourself"))
		dto.ErrorResponse(c, dto.NewForbiddenError(errors.New("cannot update yourself"), "cannot update yourself"))
		return
	}

	input.CurrentUserID = currentUserId
	err = o.usecase.UpdateMember(ctx, input)
	if err != nil {
		span.SetStatus(codes.Error, "failed to update member")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewInternalError(err))
		return
	}

	span.AddEvent("successfully updated member")
	span.SetStatus(codes.Ok, "successfully updated member")
	c.JSON(http.StatusOK, gin.H{
		"message": "Update member successfully",
	})
}

// UploadOrganizationAvatar godoc
//
//	@Summary	Upload organization avatar
//	@Tags		Organization
//	@Accept		multipart/form-data
//	@Produce	json
//	@Param		org_id	path		string			true	"Organization ID"
//	@Param		file	formData	file			true	"Avatar file"
//	@Failure	400		{object}	dto.HTTPError	"Bad Request - invalid request"
//	@Failure	404		{object}	dto.HTTPError	"Not Found"
//	@Failure	500		{object}	dto.HTTPError	"Internal Server Error - Encountered an unexpected condition"
//	@Router		/organizations/{org_id}/avatar [post]
//
//	@Security	Bearer
func (o *organizationHandlerImpl) UploadOrganizationAvatar(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(ParseContext(c), "handlers.UploadOrganizationAvatar")
	defer span.End()

	currentUserId, err := GetCurrentUserId(ctx)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewForbiddenError(err))
		return
	}

	orgId, err := uuid.Parse(c.Param("org_id"))
	if err != nil {
		span.SetStatus(codes.Error, "failed to parse organization id")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	file, err := c.FormFile("file")
	if err != nil {
		span.SetStatus(codes.Error, "failed to get file from form")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err, "failed to get file from form"))
		return
	}

	err = utils.ValidateImage(o.config.FileConfig.ImageMaxSize, file)
	if err != nil {
		span.SetStatus(codes.Error, "invalid image file")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err, "invalid content file"))
		return
	}

	input := dto.UploadOrganizationAvatarInput{
		OrgID:         orgId,
		CurrentUserId: currentUserId,
		File:          file,
	}

	avatarUrl, err := o.usecase.UploadAvatarOrganization(ctx, input)
	if err != nil {
		span.SetStatus(codes.Error, "failed to upload organization avatar")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewErrorFromUsecase(err))
		return
	}

	span.AddEvent("successfully uploaded organization avatar")
	span.SetStatus(codes.Ok, "successfully uploaded organization avatar")
	c.JSON(http.StatusOK, gin.H{
		"avatar_url": avatarUrl,
	})
}

// DeleteOrganizationAvatar godoc
//
//	@Summary	Delete organization avatar
//	@Tags		Organization
//	@Produce	json
//	@Param		org_id	path		string			true	"Organization ID"
//	@Failure	400		{object}	dto.HTTPError	"Bad Request - invalid request"
//	@Failure	404		{object}	dto.HTTPError	"Not Found"
//	@Failure	500		{object}	dto.HTTPError	"Internal Server Error - Encountered an unexpected condition"
//	@Router		/organizations/{org_id}/avatar [delete]
//
//	@Security	Bearer
func (o *organizationHandlerImpl) DeleteOrganizationAvatar(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(ParseContext(c), "handlers.DeleteOrganizationAvatar")
	defer span.End()

	currentUserId, err := GetCurrentUserId(ctx)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewForbiddenError(err))
		return
	}

	orgId, err := uuid.Parse(c.Param("org_id"))
	if err != nil {
		span.SetStatus(codes.Error, "failed to parse organization id")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	input := dto.DeleteOrganizationAvatarInput{
		OrgID:         orgId,
		CurrentUserId: currentUserId,
	}

	if err := o.usecase.DeleteAvatarOrganization(ctx, input); err != nil {
		span.SetStatus(codes.Error, "failed to delete organization avatar")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewErrorFromUsecase(err))
		return
	}

	span.AddEvent("successfully deleted organization avatar")
	span.SetStatus(codes.Ok, "successfully deleted organization avatar")
	c.JSON(http.StatusOK, gin.H{
		"message": "Remove Avatar successfully",
	})
}
