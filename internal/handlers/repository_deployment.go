package handlers

import (
	"encoding/json"
	"io"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"go.opentelemetry.io/otel/codes"

	"api-server/internal/dto"
	"api-server/internal/enums"
	"api-server/internal/gateways/gitlab"
	"api-server/internal/types"
	"api-server/pkg/oteltrace"
	"api-server/pkg/validator"
)

// GetDeployment godoc
//
//	@Summary	Get deployment by repository ID
//	@Tags		Repository Deployment
//	@Accept		json
//	@Produce	json
//	@Param		repo_id	path		int				true	"Repository ID"
//	@Success	200		{object}	dto.Deployment	"Return deployment"
//	@Failure	400		{object}	dto.HTTPError	"Bad Request - invalid request"
//	@Failure	404		{object}	dto.HTTPError	"Not Found"
//	@Failure	500		{object}	dto.HTTPError	"Internal Server Error - Encountered an unexpected condition"
//	@Router		/repositories/{repo_type}/{namespace}/{repo_name}/deployments [get]
//
//	@Security	Bearer
// func (h *Handler) GetDeployment(ctx *gin.Context) {
//
// }

// GetDeploymentBuildLogs godoc
//
//	@Summary	Retrieve deployment build logs by repository ID
//	@Tags		Repository Deployment
//	@Accept		json
//	@Produce	json
//	@Param		repo_type	path		string			true	"Repository type"	Enums(spaces, datasets, models)
//	@Param		namespace	path		string			true	"Repository namespace"
//	@Param		repo_name	path		string			true	"Repository name"
//	@Success	200			{array}		string			"Return deployment build logs"
//	@Failure	400			{object}	dto.HTTPError	"Bad Request - invalid request"
//	@Failure	404			{object}	dto.HTTPError	"Not Found"
//	@Failure	500			{object}	dto.HTTPError	"Internal Server Error - Encountered an unexpected condition"
//	@Router		/repositories/{repo_type}/{namespace}/{repo_name}/deployments/logs [get]
//
//	@Security	Bearer
func (h *repositoryHandlerImpl) GetDeploymentBuildLogs(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(c.Request.Context(), "handlers.repository_deployment.GetDeploymentBuildLogs")
	defer span.End()

	repoID, err := types.RepoID{}.FromGinContext(c)
	if err != nil {
		span.SetStatus(codes.Error, "invalid repository ID")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err, "invalid repository ID"))
		return
	}

	req := dto.GetDeploymentLogsRequest{
		RepoID: repoID,
	}
	if err := validator.Validate(req); err != nil {
		span.SetStatus(codes.Error, "failed to validate input")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	logChan, err := h.repoUseCase.GetDeploymentBuildLogs(ctx, req)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get deployment build logs")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewErrorFromUsecase(err))
		return
	}

	c.Writer.Header().Set("X-Accel-Buffering", "no")

	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()
	c.Stream(func(w io.Writer) bool {
		select {
		case <-ticker.C:
			c.SSEvent("heartbeat", "heartbeat")
		case msg := <-logChan:
			jsonBytes, err := json.Marshal(msg)
			if err != nil {
				span.SetStatus(codes.Error, "failed to encode log message")
				span.RecordError(err)
				c.SSEvent("terminate", "error encoding JSON")
				return false
			}
			c.SSEvent("message", string(jsonBytes))
		case <-ctx.Done():
			if ctx.Err() != nil {
				span.SetStatus(codes.Error, "context cancelled")
				span.RecordError(ctx.Err())
			}
			c.SSEvent("terminate", ctx.Err().Error())
			return false
		}
		return true
	})
}

// GetDeploymentPodLogs godoc
//
//	@Summary	Retrieve deployment pod logs by repository ID
//	@Tags		Repository Deployment
//	@Accept		json
//	@Produce	json
//	@Param		repo_type	path		string			true	"Repository type"	Enums(spaces, datasets, models)
//	@Param		namespace	path		string			true	"Repository namespace"
//	@Param		repo_name	path		string			true	"Repository name"
//	@Success	200			{array}		string			"Return deployment pod logs"
//	@Failure	400			{object}	dto.HTTPError	"Bad Request - invalid request"
//	@Failure	404			{object}	dto.HTTPError	"Not Found"
//	@Failure	500			{object}	dto.HTTPError	"Internal Server Error - Encountered an unexpected condition"
//	@Router		/repositories/{repo_type}/{namespace}/{repo_name}/deployments/pods/logs [get]
//
//	@Security	Bearer
func (h *repositoryHandlerImpl) GetDeploymentPodLogs(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(c.Request.Context(), "handlers.repository_deployment.GetDeploymentPodLogs")
	defer span.End()

	repoID, err := types.RepoID{}.FromGinContext(c)
	if err != nil {
		span.SetStatus(codes.Error, "invalid repository ID")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err, "invalid repository ID"))
		return
	}

	req := dto.GetDeploymentLogsRequest{
		RepoID: repoID,
	}
	if err := validator.Validate(req); err != nil {
		span.SetStatus(codes.Error, "failed to validate input")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	logChan, err := h.repoUseCase.GetPodLogs(ctx, req)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get pod logs")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewErrorFromUsecase(err))
		return
	}

	c.Writer.Header().Set("X-Accel-Buffering", "no")

	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()
	c.Stream(func(w io.Writer) bool {
		select {
		case <-ticker.C:
			c.SSEvent("heartbeat", "heartbeat")
		case msg := <-logChan:
			jsonBytes, err := json.Marshal(msg)
			if err != nil {
				span.SetStatus(codes.Error, "failed to encode log message")
				span.RecordError(err)
				c.SSEvent("terminate", "error encoding JSON")
				return false
			}
			c.SSEvent("message", string(jsonBytes))
		case <-ctx.Done():
			if ctx.Err() != nil {
				span.SetStatus(codes.Error, "context cancelled")
				span.RecordError(ctx.Err())
			}
			c.SSEvent("terminate", ctx.Err().Error())
			return false
		}
		return true
	})
}

// AddNewDeployment godoc
//
//	@Summary		Create a new deployment
//	@Description	Each space repository should only have one deployment
//	@Tags			Repository Deployment
//	@Accept			json
//	@Produce		json
//	@Param			repo_id	path		int							true	"Repository ID"
//	@Param			model	body		dto.DeploymentCreateRequest	true	"Add deployment"
//	@Success		200		{object}	dto.Deployment				"Return created deployment"
//	@Failure		400		{object}	dto.HTTPError				"Bad Request - invalid request"
//	@Failure		404		{object}	dto.HTTPError				"Not Found"
//	@Failure		500		{object}	dto.HTTPError				"Internal Server Error - Encountered an unexpected condition"
//	@Router			/repositories/{repo_type}/{namespace}/{repo_name}/deployments [post]
//
//	@Security		Bearer
// func (h *Handler) AddNewDeployment(ctx *gin.Context) {
//
// }

// UpdateDeploymentConfig godoc
//
//	@Summary		Update deployment configurations
//	@Description	Configurations of the deployment such as hardware specification and timeout settings
//	@Tags			Repository Deployment
//	@Accept			json
//	@Produce		json
//	@Param			repo_id	path	int									true	"Repository ID"
//	@Param			model	body	dto.DeploymentUpdateConfigRequest	true	"Updated deployment configurations"
//	@Success		204		"No Content - Request has been successfully completed, but no response payload body"
//	@Failure		400		{object}	dto.HTTPError	"Bad Request - invalid request"
//	@Failure		404		{object}	dto.HTTPError	"Not Found"
//	@Failure		500		{object}	dto.HTTPError	"Internal Server Error - Encountered an unexpected condition"
//	@Router			/repositories/{repo_type}/{namespace}/{repo_name}/deployments/config [patch]
//
//	@Security		Bearer
// func (h *Handler) UpdateDeploymentConfig(ctx *gin.Context) {
//
// }

// StartDeployment godoc
//
//	@Summary	Start deployment
//	@Tags		Repository Deployment
//	@Accept		json
//	@Produce	json
//	@Param		repo_type	path		string						true	"Repository type"	Enums(spaces, datasets, models)
//	@Param		namespace	path		string						true	"Repository namespace"
//	@Param		repo_name	path		string						true	"Repository name"
//	@Param		model		body		dto.StartDeploymentRequest	true	"Start deployment request"
//	@Success	200			{object}	dto.StartDeploymentResponse	"Start deployment successfully"
//	@Failure	400			{object}	dto.HTTPError				"Bad Request - invalid request"
//	@Failure	404			{object}	dto.HTTPError				"Not Found"
//	@Failure	500			{object}	dto.HTTPError				"Internal Server Error - Encountered an unexpected condition"
//	@Router		/repositories/{repo_type}/{namespace}/{repo_name}/deployments/start [post]
//
//	@Security	Bearer
func (h *repositoryHandlerImpl) StartDeployment(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(c.Request.Context(), "handlers.repository_deployment.StartDeployment")
	defer span.End()

	// userID, _ := uuid.Parse("5c8746c8-ac8d-4610-833b-db30af89ee3c")
	// c.Set(enums.USER_ID, userID)

	currentUserId, err := GetCurrentUserId(ParseContext(c))
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user id")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewForbiddenError(err))
		return
	}

	repoID, err := types.RepoID{}.FromGinContext(c)
	if err != nil {
		span.SetStatus(codes.Error, "invalid repository ID")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err, err.Error()))
		return
	}

	var req dto.StartDeploymentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		span.SetStatus(codes.Error, "failed to bind JSON request")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	var resp *dto.StartDeploymentResponse
	if *repoID.RepoType() == enums.RepoType_Composes {
		data, err := h.composeUseCase.CreateComposeDeployment(ctx, currentUserId, repoID, req)
		if err != nil {
			span.SetStatus(codes.Error, "failed to start Compose deployment")
			span.RecordError(err)

			if gitlabErr, ok := err.(*gitlab.GitlabError); ok {
				c.AbortWithStatusJSON(gitlabErr.StatusCode, dto.NewHTTPError(gitlabErr.StatusCode, gitlabErr))
				return
			}

			dto.ErrorResponse(c, dto.NewErrorFromUsecase(err))
			return
		}
		resp = data
	} else {
		data, err := h.repoUseCase.StartDeployment(ctx, currentUserId, repoID, req)
		if err != nil {
			span.SetStatus(codes.Error, "failed to start deployment")
			span.RecordError(err)

			if gitlabErr, ok := err.(*gitlab.GitlabError); ok {
				c.AbortWithStatusJSON(gitlabErr.StatusCode, dto.NewHTTPError(gitlabErr.StatusCode, gitlabErr))
				return
			}

			dto.ErrorResponse(c, dto.NewErrorFromUsecase(err))
			return
		}
		resp = data
	}

	span.AddEvent("deployment started successfully")
	span.SetStatus(codes.Ok, "deployment started successfully")
	c.JSON(http.StatusOK, resp)
}

// RestartDeployment godoc
//
//	@Summary	Restart space's Kubernetes deployment
//	@Tags		Repository Deployment
//	@Accept		json
//	@Produce	json
//	@Param		repo_type	path		string			true	"Repository type"	Enums(spaces, datasets, models)
//	@Param		namespace	path		string			true	"Repository namespace"
//	@Param		repo_name	path		string			true	"Repository name"
//	@Success	200			{object}	dto.HTTPResp	"Restart space successfully"
//	@Failure	400			{object}	dto.HTTPError	"Bad Request - invalid request"
//	@Failure	404			{object}	dto.HTTPError	"Not Found"
//	@Failure	500			{object}	dto.HTTPError	"Internal Server Error - Encountered an unexpected condition"
//	@Router		/repositories/{repo_type}/{namespace}/{repo_name}/deployments/restart [get]
//
//	@Security	Bearer
func (h *repositoryHandlerImpl) RestartDeployment(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(c.Request.Context(), "handlers.repository_deployment.RestartDeployment")
	defer span.End()

	repoID, err := types.RepoID{}.FromGinContext(c)
	if err != nil {
		span.SetStatus(codes.Error, "invalid repository ID")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err, "invalid repository ID"))
		return
	}

	err = h.repoUseCase.RestartDeployment(ctx, dto.RestartDeploymentRequest{
		RepoID: repoID,
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to restart deployment")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewErrorFromUsecase(err))
		return
	}

	span.AddEvent("deployment restarted successfully")
	span.SetStatus(codes.Ok, "deployment restarted successfully")
	c.JSON(http.StatusOK, dto.NewHTTPResponse[string]().WithMessage("Restart space successfully"))
}

// StopDeployment godoc
//
//	@Summary	Stop deployment
//	@Tags		Repository Deployment
//	@Accept		json
//	@Produce	json
//	@Param		repo_type	path	string						true	"Repository type"	Enums(spaces, datasets, models)
//	@Param		namespace	path	string						true	"Repository namespace"
//	@Param		repo_name	path	string						true	"Repository name"
//	@Param		model		body	dto.StopDeploymentRequest	true	"Stop deployment request"
//	@Success	204			"No Content - Request has been successfully completed, but no response payload body"
//	@Failure	400			{object}	dto.HTTPError	"Bad Request - invalid request"
//	@Failure	404			{object}	dto.HTTPError	"Not Found"
//	@Failure	500			{object}	dto.HTTPError	"Internal Server Error - Encountered an unexpected condition"
//	@Router		/repositories/{repo_type}/{namespace}/{repo_name}/deployments/stop [post]
//
//	@Security	Bearer
func (h *repositoryHandlerImpl) StopDeployment(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(ParseContext(c), "handlers.repository_deployment.StopDeployment")
	defer span.End()

	// userID, _ := uuid.Parse("5c8746c8-ac8d-4610-833b-db30af89ee3c")
	// c.Set(enums.USER_ID, userID)

	repoID, err := types.RepoID{}.FromGinContext(c)
	if err != nil {
		span.SetStatus(codes.Error, "invalid repository ID")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err, "invalid repository ID"))
		return
	}

	req := dto.StopDeploymentRequest{
		RepoID: &repoID,
	}
	if err := validator.Validate(req); err != nil {
		span.SetStatus(codes.Error, "failed to validate input")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	if *repoID.RepoType() == enums.RepoType_Composes {
		err = h.composeUseCase.StopComposeDeployment(ctx, repoID)
		if err != nil {
			span.SetStatus(codes.Error, "failed to stop deployment")
			span.RecordError(err)
			dto.ErrorResponse(c, dto.NewErrorFromUsecase(err))
			return
		}
	} else {
		err = h.repoUseCase.StopDeployment(ctx, req)
		if err != nil {
			span.SetStatus(codes.Error, "failed to stop deployment")
			span.RecordError(err)
			dto.ErrorResponse(c, dto.NewErrorFromUsecase(err))
			return
		}
	}

	span.AddEvent("deployment stopped successfully")
	span.SetStatus(codes.Ok, "deployment stopped successfully")
	c.JSON(http.StatusNoContent, nil)
}

// UpdateDeploymentStatus godoc
//
//	@Summary	Update deployment status
//	@Tags		Repository Deployment
//	@Accept		json
//	@Produce	json
//	@Param		model	body		dto.UpdateDeploymentStatusRequest	true	"Deployment Data"
//	@Success	204		{object}	string								"no content"
//	@Failure	400		{object}	dto.HTTPError						"Bad Request - invalid request"
//	@Failure	404		{object}	dto.HTTPError						"Not Found"
//	@Failure	500		{object}	dto.HTTPError						"Internal Server Error - Encountered an unexpected condition"
//	@Router		/repositories/deployments/status [post]
func (u *repositoryHandlerImpl) UpdateDeploymentStatus(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(ParseContext(c), "handlers.repository_deployment.UpdateWorkflowStatus")
	defer span.End()

	var req dto.UpdateDeploymentStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		span.SetStatus(codes.Error, "failed to bind JSON request")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err, "invalid JSON"))
		return
	}

	err := u.repoUseCase.UpdateDeploymentStatus(ctx, req)
	if err != nil {
		span.SetStatus(codes.Error, "failed to update deployment status")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewErrorFromUsecase(err))
		return
	}

	span.AddEvent("deployment status updated successfully")
	span.SetStatus(codes.Ok, "deployment status updated successfully")
	c.JSON(http.StatusNoContent, nil)
}

// ListDeployments godoc
//
//	@Summary	List all deployments
//	@Tags		Repository Deployment
//	@Accept		json
//	@Produce	json
//	@Param		repo_type	path	string						true	"Repository type"	Enums(spaces, datasets, models)
//	@Param		namespace	path	string						true	"Repository namespace"
//	@Param		repo_name	path	string						true	"Repository name"
//	@Param		model		body	dto.StartDeploymentRequest	true	"Start deployment request"
//	@Success	204			"No Content - Request has been successfully completed, but no response payload body"
//	@Failure	400			{object}	dto.HTTPError	"Bad Request - invalid request"
//	@Failure	404			{object}	dto.HTTPError	"Not Found"
//	@Failure	500			{object}	dto.HTTPError	"Internal Server Error - Encountered an unexpected condition"
//	@Router		/repositories/deployments [get]
//
//	@Security	Bearer
func (h *repositoryHandlerImpl) ListDeployments(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(ParseContext(c), "handlers.repository_deployment.ListDeployments")
	defer span.End()

	var req dto.ListDeploymentRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		span.SetStatus(codes.Error, "failed to bind query params")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	resp, err := h.repoUseCase.ListDeployment(ctx, req)
	if err != nil {
		span.SetStatus(codes.Error, "failed to list deployments")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewErrorFromUsecase(err))
		return
	}

	span.AddEvent("succesfully listed deployments")
	span.SetStatus(codes.Ok, "succesfully listed deployments")
	c.JSON(http.StatusOK, resp)
}

// TerminateDeployment godoc
//
//	@Summary	Terminate deployment
//	@Tags		Repository Deployment
//	@Accept		json
//	@Produce	json
//	@Param		repo_type	path		string							true	"Repository type"	Enums(spaces, datasets, models)
//	@Param		namespace	path		string							true	"Repository namespace"
//	@Param		repo_name	path		string							true	"Repository name"
//	@Param		model		body		dto.TerminateDeploymentRequest	true	"Terminate deployment request"
//	@Success	200			{object}	string							"Terminate deployment successfully"
//	@Failure	400			{object}	dto.HTTPError					"Bad Request - invalid request"
//	@Failure	404			{object}	dto.HTTPError					"Not Found"
//	@Failure	500			{object}	dto.HTTPError					"Internal Server Error - Encountered an unexpected condition"
//	@Router		/repositories/{repo_type}/{namespace}/{repo_name}/deployments/terminate [post]
//
//	@Security	Bearer
// func (h *RepositoryHandler) TerminateDeployment(c *gin.Context) {
// 	ctx, span := oteltrace.Tracer.Start(c.Request.Context(), "handlers.repository_deployment.TerminateDeployment")
// 	defer span.End()
//
// 	currentUserId, err := GetCurrentUserId(ParseContext(c))
// 	if err != nil {
// 		span.SetStatus(codes.Error, "failed to get current user id")
// 		span.RecordError(err)
// 		dto.ErrorResponse(c, dto.NewBadRequestError(err))
// 		return
// 	}
//
// 	var req dto.TerminateDeploymentRequest
// 	if err := c.ShouldBind(&req); err != nil {
// 		span.SetStatus(codes.Error, "failed to bind request")
// 		span.RecordError(err)
// 		dto.ErrorResponse(c, dto.NewBadRequestError(err))
// 		return
// 	}
//
// 	repoID, err := types.RepoID{}.FromGinContext(c)
// 	if err != nil {
// 		span.SetStatus(codes.Error, "invalid repository ID")
// 		span.RecordError(err)
// 		dto.ErrorResponse(c, dto.NewBadRequestError(err, err.Error()))
// 		return
// 	}
//
// 	err = h.repoUseCase.TerminateDeployment(ctx, currentUserId, repoID)
// 	if err != nil {
// 		span.SetStatus(codes.Error, "failed to terminate deployment")
// 		span.RecordError(err)
// 		dto.ErrorResponse(c, dto.NewErrorFromUsecase(err))
// 		return
// 	}
//
// 	span.AddEvent("deployment terminated successfully")
// 	span.SetStatus(codes.Ok, "deployment terminated successfully")
// 	c.JSON(http.StatusOK, gin.H{"message": "Terminate successfully"})
// }

// GetDeploymentStatus godoc
//
//	@Summary	Get deployment status
//	@Tags		Repository Deployment
//	@Accept		json
//	@Produce	json
//	@Param		repo_type	path		string						true	"Repository type"	Enums(spaces, datasets, models)
//	@Param		namespace	path		string						true	"Repository namespace"
//	@Param		repo_name	path		string						true	"Repository name"
//	@Param		model		body		dto.StartDeploymentRequest	true	"Start deployment request"
//	@Success	200			{object}	dto.StartDeploymentResponse	"Start deployment successfully"
//	@Failure	400			{object}	dto.HTTPError				"Bad Request - invalid request"
//	@Failure	404			{object}	dto.HTTPError				"Not Found"
//	@Failure	500			{object}	dto.HTTPError				"Internal Server Error - Encountered an unexpected condition"
//	@Router		/repositories/{repo_type}/{namespace}/{repo_name}/deployments/status [get]
//
//	@Security	Bearer
func (h *repositoryHandlerImpl) GetDeploymentStatus(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(c.Request.Context(), "handlers.repository_deployment.GetDeploymentStatus")
	defer span.End()

	repoID, err := types.RepoID{}.FromGinContext(c)
	if err != nil {
		span.SetStatus(codes.Error, "invalid repository ID")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err, "invalid repository ID"))
		return
	}

	req := dto.GetDeploymentStatusRequest{
		RepoID: repoID,
	}
	// if err := validator.Validate(req); err != nil {
	// 	span.SetStatus(codes.Error, "failed to validate input")
	// 	span.RecordError(err)
	// 	dto.ErrorResponse(c, dto.NewBadRequestError(err))
	// 	return
	// }

	var resp *dto.GetDeploymentStatusResponse
	if *repoID.RepoType() == enums.RepoType_Composes {
		// get deployment for Compose repository
		data, err := h.repoUseCase.GetComposeRepoDeploymentStatus(ctx, req)
		if err != nil {
			span.SetStatus(codes.Error, "failed to get deployment status")
			span.RecordError(err)
			dto.ErrorResponse(c, dto.NewErrorFromUsecase(err))
			return
		}
		resp = data
	} else {
		data, err := h.repoUseCase.GetDeploymentStatus(ctx, req)
		if err != nil {
			span.SetStatus(codes.Error, "failed to get deployment status")
			span.RecordError(err)
			dto.ErrorResponse(c, dto.NewErrorFromUsecase(err))
			return
		}
		resp = data
	}

	span.AddEvent("successfully fetched deployment status")
	span.SetStatus(codes.Ok, "successfully fetched deployment status")
	c.JSON(http.StatusOK, resp)
}
