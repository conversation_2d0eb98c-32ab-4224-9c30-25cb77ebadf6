package handlers_test

//
// import (
// 	"bytes"
// 	"encoding/json"
// 	"errors"
// 	"net/http"
// 	"net/http/httptest"
// 	"testing"
//
// 	"github.com/gin-gonic/gin"
// 	"github.com/google/uuid"
// 	"github.com/stretchr/testify/assert"
// 	"github.com/stretchr/testify/mock"
//
// 	"api-server/internal/dto"
// 	"api-server/internal/enums"
// 	"api-server/internal/middlewares"
// 	workflow_usecase_mocks "api-server/internal/usecase/workflow/mocks"
// 	"api-server/pkg/oteltrace"
// )
//
// func TestUpdateWorkflowStatus(t *testing.T) {
// 	oteltrace.New()
// 	gin.SetMode(gin.TestMode)
//
// 	mockUserID, _ := uuid.Parse("d2de43e6-fde6-4deb-a1d0-2d5c2c2e1038")
//
// 	testcases := []struct {
// 		name             string
// 		mockFn           func(d *workflow_usecase_mocks.MockWorkflowUsecase)
// 		requestBody      dto.UpdateWorkflowRequest
// 		setUserIDContext bool
// 		expectBody       string
// 		expectHTTPCode   int
// 	}{
// 		// {
// 		// 	name:             "should return Unauthorized error if missing userID from context",
// 		// 	mockFn:           func(u *workflow_usecase_mocks.MockWorkflowUsecase) {},
// 		// 	requestBody:      dto.UpdateWorkflowRequest{},
// 		// 	setUserIDContext: false,
// 		// 	expectBody:       "{\"code\":401,\"message\":\"Unauthorized\"}",
// 		// 	expectHTTPCode:   http.StatusUnauthorized,
// 		// },
// 		{
// 			name: "should return Internal error if UpdateWorkflow return error",
// 			mockFn: func(u *workflow_usecase_mocks.MockWorkflowUsecase) {
// 				u.On("UpdateWorkflow", mock.Anything, mock.Anything).Return(errors.New("error"))
// 			},
// 			requestBody: dto.UpdateWorkflowRequest{
// 				Name:     "name",
// 				Status:   enums.ArgoWorkflowStatus_Running,
// 				Duration: 0,
// 			},
// 			setUserIDContext: true,
// 			expectBody:       "{\"code\":500,\"message\":\"error\"}",
// 			expectHTTPCode:   http.StatusInternalServerError,
// 		},
// 		{
// 			name:   "should return BadRequest error if request with invalid data",
// 			mockFn: func(u *workflow_usecase_mocks.MockWorkflowUsecase) {},
// 			requestBody: dto.UpdateWorkflowRequest{
// 				Name:     "name",
// 				Status:   "invalid",
// 				Duration: 0,
// 			},
// 			setUserIDContext: true,
// 			expectBody:       "{\"code\":400,\"message\":\"Key: 'UpdateWorkflowRequest.Status' Error:Field validation for 'Status' failed on the 'oneof' tag\"}",
// 			expectHTTPCode:   http.StatusBadRequest,
// 		},
// 		{
// 			name: "should pass",
// 			mockFn: func(u *workflow_usecase_mocks.MockWorkflowUsecase) {
// 				u.On("UpdateWorkflow", mock.Anything, mock.Anything).Return(nil)
// 			},
// 			requestBody: dto.UpdateWorkflowRequest{
// 				Name:     "name",
// 				Status:   enums.ArgoWorkflowStatus_Running,
// 				Duration: 0,
// 			},
// 			setUserIDContext: true,
// 			expectBody:       "",
// 			expectHTTPCode:   http.StatusNoContent,
// 		},
// 	}
//
// 	for _, testcase := range testcases {
// 		t.Run(testcase.name, func(t *testing.T) {
// 			usecase := &workflow_usecase_mocks.MockWorkflowUsecase{}
// 			testcase.mockFn(usecase)
// 			handler := NewWorkflowHandler(usecase)
//
// 			rr := httptest.NewRecorder()
// 			c, _ := gin.CreateTestContext(rr)
// 			if testcase.setUserIDContext {
// 				c.Set(middlewares.USER_ID, mockUserID)
// 			}
// 			body, _ := json.Marshal(testcase.requestBody)
// 			c.Request = httptest.NewRequest(http.MethodPost, "/", bytes.NewBuffer(body))
// 			handler.UpdateWorkflowStatus(c)
//
// 			assert.Equal(t, testcase.expectHTTPCode, rr.Code)
// 			assert.Equal(t, testcase.expectBody, rr.Body.String())
// 		})
// 	}
// }
//
// func TestCreateWorkflow(t *testing.T) {
// 	oteltrace.New()
// 	gin.SetMode(gin.TestMode)
//
// 	mockUserID, _ := uuid.Parse("d2de43e6-fde6-4deb-a1d0-2d5c2c2e1038")
// 	mockRepoID, _ := uuid.Parse("4838b018-933f-4812-a03e-d1af54565822")
//
// 	testcases := []struct {
// 		name             string
// 		mockFn           func(d *workflow_usecase_mocks.MockWorkflowUsecase)
// 		requestBody      dto.CreateWorkflowRequest
// 		setUserIDContext bool
// 		expectBody       string
// 		expectHTTPCode   int
// 	}{
// 		{
// 			name: "should return Internal error if CreateWorkflow return error",
// 			mockFn: func(u *workflow_usecase_mocks.MockWorkflowUsecase) {
// 				u.On("CreateWorkflow", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
// 			},
// 			requestBody: dto.CreateWorkflowRequest{
// 				RepoID:   mockRepoID,
// 				Revision: "main",
// 			},
// 			setUserIDContext: true,
// 			expectBody:       "{\"code\":500,\"message\":\"error\"}",
// 			expectHTTPCode:   http.StatusInternalServerError,
// 		},
// 		{
// 			name: "should pass",
// 			mockFn: func(u *workflow_usecase_mocks.MockWorkflowUsecase) {
// 				u.On("CreateWorkflow", mock.Anything, mock.Anything).Return(&dto.CreateWorkflowResponse{
// 					Message: "created",
// 					Data: &dto.Workflow{
// 						ID:       mockRepoID,
// 						Name:     "name",
// 						Status:   enums.ArgoWorkflowStatus_Pending,
// 						RepoID:   mockRepoID,
// 						Revision: "main",
// 					},
// 				}, nil)
// 			},
// 			requestBody: dto.CreateWorkflowRequest{
// 				RepoID:   mockRepoID,
// 				Revision: "main",
// 			},
// 			setUserIDContext: true,
// 			expectBody:       "{\"message\":\"created\",\"data\":{\"id\":\"4838b018-933f-4812-a03e-d1af54565822\",\"name\":\"name\",\"status\":\"Pending\",\"repo_id\":\"4838b018-933f-4812-a03e-d1af54565822\",\"revision\":\"main\",\"created_at\":\"0001-01-01T00:00:00Z\",\"updated_at\":\"0001-01-01T00:00:00Z\"}}",
// 			expectHTTPCode:   http.StatusCreated,
// 		},
// 	}
//
// 	for _, testcase := range testcases {
// 		t.Run(testcase.name, func(t *testing.T) {
// 			usecase := &workflow_usecase_mocks.MockWorkflowUsecase{}
// 			testcase.mockFn(usecase)
// 			handler := NewWorkflowHandler(usecase)
//
// 			rr := httptest.NewRecorder()
// 			c, _ := gin.CreateTestContext(rr)
// 			if testcase.setUserIDContext {
// 				c.Set(middlewares.USER_ID, mockUserID)
// 			}
// 			body, _ := json.Marshal(testcase.requestBody)
// 			c.Request = httptest.NewRequest(http.MethodPost, "/", bytes.NewBuffer(body))
// 			handler.CreateWorkflow(c)
//
// 			assert.Equal(t, testcase.expectHTTPCode, rr.Code)
// 			assert.Equal(t, testcase.expectBody, rr.Body.String())
// 		})
// 	}
// }
//
// func TestListWorkflow(t *testing.T) {
// 	oteltrace.New()
// 	gin.SetMode(gin.TestMode)
//
// 	mockUserID, _ := uuid.Parse("d2de43e6-fde6-4deb-a1d0-2d5c2c2e1038")
// 	mockRepoID, _ := uuid.Parse("4838b018-933f-4812-a03e-d1af54565822")
//
// 	testcases := []struct {
// 		name             string
// 		mockFn           func(d *workflow_usecase_mocks.MockWorkflowUsecase)
// 		requestBody      dto.ListWorkflowRequest
// 		setUserIDContext bool
// 		expectBody       string
// 		expectHTTPCode   int
// 	}{
// 		{
// 			name: "should return Internal error if ListWorkflow return error",
// 			mockFn: func(u *workflow_usecase_mocks.MockWorkflowUsecase) {
// 				u.On("ListWorkflow", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
// 			},
// 			requestBody: dto.ListWorkflowRequest{
// 				RepoID:   mockRepoID,
// 				Paginate: dto.PaginateRequest{},
// 			},
// 			setUserIDContext: true,
// 			expectBody:       "{\"code\":500,\"message\":\"error\"}",
// 			expectHTTPCode:   http.StatusInternalServerError,
// 		},
// 		{
// 			name: "should pass",
// 			mockFn: func(u *workflow_usecase_mocks.MockWorkflowUsecase) {
// 				u.On("ListWorkflow", mock.Anything, mock.Anything).Return(&dto.ListWorkflowResponse{
// 					Message: "success",
// 					Data: &[]dto.Workflow{{
// 						ID:       mockRepoID,
// 						Name:     "name",
// 						Status:   enums.ArgoWorkflowStatus_Pending,
// 						RepoID:   mockRepoID,
// 						Revision: "main",
// 					}},
// 				}, nil)
// 			},
// 			requestBody: dto.ListWorkflowRequest{
// 				RepoID:   mockRepoID,
// 				Paginate: dto.PaginateRequest{},
// 			},
// 			setUserIDContext: true,
// 			expectBody:       "{\"message\":\"success\",\"data\":[{\"id\":\"4838b018-933f-4812-a03e-d1af54565822\",\"name\":\"name\",\"status\":\"Pending\",\"repo_id\":\"4838b018-933f-4812-a03e-d1af54565822\",\"revision\":\"main\",\"created_at\":\"0001-01-01T00:00:00Z\",\"updated_at\":\"0001-01-01T00:00:00Z\"}]}",
// 			expectHTTPCode:   http.StatusOK,
// 		},
// 	}
//
// 	for _, testcase := range testcases {
// 		t.Run(testcase.name, func(t *testing.T) {
// 			usecase := &workflow_usecase_mocks.MockWorkflowUsecase{}
// 			testcase.mockFn(usecase)
// 			handler := NewWorkflowHandler(usecase)
//
// 			rr := httptest.NewRecorder()
// 			c, _ := gin.CreateTestContext(rr)
// 			if testcase.setUserIDContext {
// 				c.Set(middlewares.USER_ID, mockUserID)
// 			}
// 			body, _ := json.Marshal(testcase.requestBody)
// 			c.Request = httptest.NewRequest(http.MethodPost, "/", bytes.NewBuffer(body))
// 			handler.ListWorkflow(c)
//
// 			assert.Equal(t, testcase.expectHTTPCode, rr.Code)
// 			assert.Equal(t, testcase.expectBody, rr.Body.String())
// 		})
// 	}
// }

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"api-server/internal/handlers"
	"api-server/internal/usecase/workflow/mocks"
)

func TestNewWorkflowHandler(t *testing.T) {
	// Create a mock usecase
	mockUsecase := &mocks.MockWorkflowUsecase{}

	// Create a new handler with the mock usecase
	handler := handlers.NewWorkflowHandler(mockUsecase)

	// Assert that the handler is not nil
	assert.NotNil(t, handler, "NewWorkflowHandler should return a non-nil handler")

	// Assert that the handler implements the WorkflowHandler interface
	_, ok := handler.(handlers.WorkflowHandler)
	assert.True(t, ok, "Handler should implement WorkflowHandler interface")
}
