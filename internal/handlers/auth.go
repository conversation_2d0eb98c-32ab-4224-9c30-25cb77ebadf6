package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.uber.org/zap"

	"api-server/internal/dto"
	"api-server/pkg/oteltrace"
	"api-server/pkg/otelzap"
)

type AuthHandler struct {
}

func NewAuthHandler() *AuthHandler {
	return &AuthHandler{}
}

// Supabase Auth Hook Custom Claims
//
//	@Summary		Generate custom claims for Auth hook
//	@Description	Generate custom claims for Auth hook
//	@Accept			json
//	@Produce		json
//	@Param			model	body		dto.CustomAccessTokenInput	true	"Custom access token input"
//	@Success		201		{object}	dto.CustomAccessTokenOutput	"Custom access token output"
//	@Failure		400		{object}	dto.HTTPError				"Bad Request - invalid request"
//	@Failure		404		{object}	dto.HTTPError				"Not Found"
//	@Failure		500		{object}	dto.HTTPError				"Internal Server Error - Encountered an unexpected condition"
//	@Router			/custom_claims [post]
func (h *AuthHandler) HandleSupabaseAuthHook(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(c.Request.Context(), "handlers.HandleAuthHook")
	defer span.End()

	otelzap.DebugWithContext(ctx, "handling supabase auth hook")

	var body dto.CustomAccessTokenInput
	if err := c.ShouldBindJSON(&body); err != nil {
		span.SetStatus(codes.Error, err.Error())
		span.RecordError(err)
		otelzap.ErrorWithContext(ctx, "failed to bind json", err)
		c.JSON(http.StatusBadRequest, dto.CustomAccessTokenOutput{
			HookError: dto.AuthHookError{
				HTTPCode: 0,
				Message:  "",
			},
		})
		return
	}

	otelzap.InfoWithContext(ctx, "successfully generated custom claims", zap.String("requested_by", body.UserID.String()))
	span.SetAttributes(attribute.KeyValue{Key: "user_id", Value: attribute.StringValue(body.UserID.String())})
	c.JSON(http.StatusOK, dto.CustomAccessTokenOutput{
		Claims: map[string]interface{}{
			// these claims are required
			"aud":          body.Claims.Audience,
			"iat":          body.Claims.IssuedAt,
			"exp":          body.Claims.ExpiresAt,
			"sub":          body.Claims.Subject,
			"email":        body.Claims.Email,
			"phone":        body.Claims.Phone,
			"role":         body.Claims.Role,
			"aal":          body.Claims.AuthenticatorAssuranceLevel,
			"session_id":   body.Claims.SessionId,
			"is_anonymous": body.Claims.IsAnonymous,
			// custom claims
			"user_id": body.UserID,
		},
	})
}
