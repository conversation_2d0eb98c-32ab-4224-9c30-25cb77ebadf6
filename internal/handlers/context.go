package handlers

import (
	"context"
	"errors"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"

	"api-server/internal/dto"
	"api-server/internal/enums"
	"api-server/internal/utils"
)

// GetCurrentUserId retrieves the current user's ID from the context.
// It returns the user ID if found, otherwise an error indicating the user ID was not found or is invalid.
//
// Parameters:
//   - ctx: The context from which to retrieve the user ID.
//
// Returns:
//   - uuid.UUID: The user ID.
//   - error: An error if the user ID is not found or is invalid.
func GetCurrentUserId(ctx context.Context) (uuid.UUID, error) {
	val, ok := ctx.Value(utils.ContextKeyUserId).(uuid.UUID)
	if !ok || val == uuid.Nil {
		err := errors.New("current user id not found")
		return uuid.UUID{}, dto.NewForbiddenError(err, http.StatusText(http.StatusForbidden))
	}
	return val, nil
}

// ParseContext extracts the user ID from the Gin context and returns a new context.Context with the user ID.
// If the user ID is not found in the Gin context, a zero UUID is used.
//
// Parameters:
//   - ctx: The Gin context from which to extract the user ID.
//
// Returns:
//   - context.Context: A new context.Context containing the user ID.
func ParseContext(ctx *gin.Context) context.Context {
	val, ok := ctx.Get("user_id")
	if !ok {
		val = uuid.UUID{}
	}
	userId := val.(uuid.UUID)
	return context.WithValue(ctx.Request.Context(), utils.ContextKeyUserId, userId)
}

// GetOrgId retrieves the current organization ID from the context.
// It returns the organization ID if found, otherwise an error indicating the organization ID was not found or is invalid.
//
// Parameters:
//   - ctx: The context from which to retrieve the organization ID.
//
// Returns:
//   - uuid.UUID: The organization ID.
//   - error: An error if the organization ID is not found or is invalid.
func GetOrgId(ctx context.Context) (uuid.UUID, error) {
	val, ok := ctx.Value(enums.ORG_ID).(uuid.UUID)
	if !ok || val == uuid.Nil {
		err := errors.New("current org id not found")
		return uuid.UUID{}, dto.NewForbiddenError(err, http.StatusText(http.StatusForbidden))
	}
	return val, nil
}
