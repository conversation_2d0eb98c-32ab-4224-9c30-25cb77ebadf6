package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"go.opentelemetry.io/otel/codes"

	"api-server/internal/dto"
	"api-server/internal/types"
	"api-server/internal/utils"
	"api-server/pkg/oteltrace"
	"api-server/pkg/validator"
)

// UploadRepoAvatar godoc
//
//	@Summary	Upload repository avatar
//	@Tags		Repository
//	@Accept		multipart/form-data
//	@Produce	json
//	@Param		repo_type	path		string			true	"Repository type"	Enums(spaces, datasets, models)
//	@Param		namespace	path		string			true	"Repository namespace"
//	@Param		repo_name	path		string			true	"Repository name"
//	@Param		file		formData	file			true	"Avatar file"
//	@Failure	400			{object}	dto.HTTPError	"Bad Request - invalid request"
//	@Failure	404			{object}	dto.HTTPError	"Not Found"
//	@Failure	500			{object}	dto.HTTPError	"Internal Server Error - Encountered an unexpected condition"
//	@Router		/repositories/{repo_type}/{namespace}/{repo_name}/avatar [post]
//
//	@Security	Bearer
func (h *repositoryHandlerImpl) UploadRepoAvatar(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(c.Request.Context(), "handlers.repository_avatar.UploadRepoAvatar")
	defer span.End()

	currentUserId, err := GetCurrentUserId(ParseContext(c))
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user id")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewForbiddenError(err, "failed to get current user id"))
		return
	}

	repoID, err := types.RepoID{}.FromGinContext(c)
	if err != nil {
		span.SetStatus(codes.Error, "invalid repository ID")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err, err.Error()))
		return
	}

	file, err := c.FormFile("file")
	if err != nil {
		span.SetStatus(codes.Error, "failed to get avatar form file")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	err = utils.ValidateImage(h.config.FileConfig.ImageMaxSize, file)
	if err != nil {
		span.SetStatus(codes.Error, "failed to validate image file")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err, "invalid content file"))
		return
	}

	input := dto.UploadRepositoryAvatarInput{
		RepoID:        repoID,
		CurrentUserId: currentUserId,
		File:          file,
	}
	if err := validator.Validate(input); err != nil {
		span.SetStatus(codes.Error, "failed to validate input")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	avatarUrl, err := h.repoUseCase.UploadAvatarRepo(ctx, input)
	if err != nil {
		span.SetStatus(codes.Error, "failed to upload avatar for repository")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewErrorFromUsecase(err))
		return
	}

	span.AddEvent("repository avatar uploaded successfully")
	span.SetStatus(codes.Ok, "repository avatar uploaded successfully")
	c.JSON(http.StatusOK, gin.H{
		"avatar_url": avatarUrl,
	})
}

// DeleteRepoAvatar godoc
//
//	@Summary	Delete repository avatar
//	@Tags		Repository
//	@Produce	json
//	@Param		repo_type	path		string			true	"Repository type"	Enums(spaces, datasets, models)
//	@Param		namespace	path		string			true	"Repository namespace"
//	@Param		repo_name	path		string			true	"Repository name"
//	@Failure	400			{object}	dto.HTTPError	"Bad Request - invalid request"
//	@Failure	404			{object}	dto.HTTPError	"Not Found"
//	@Failure	500			{object}	dto.HTTPError	"Internal Server Error - Encountered an unexpected condition"
//	@Router		/repositories/{repo_type}/{namespace}/{repo_name}/avatar [delete]
//
//	@Security	Bearer
func (h *repositoryHandlerImpl) DeleteRepoAvatar(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(c.Request.Context(), "handlers.repository_avatar.DeleteRepoAvatar")
	defer span.End()

	currentUserId, err := GetCurrentUserId(ParseContext(c))
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user id")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewForbiddenError(err, "failed to get current user id"))
		return
	}

	repoID, err := types.RepoID{}.FromGinContext(c)
	if err != nil {
		span.SetStatus(codes.Error, "invalid repository ID")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err, err.Error()))
		return
	}

	input := dto.DeleteRepositoryAvatarInput{
		RepoID:        repoID,
		CurrentUserId: currentUserId,
	}
	if err := validator.Validate(input); err != nil {
		span.SetStatus(codes.Error, "failed to validate input")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	if err := h.repoUseCase.DeleteAvatarRepo(ctx, input); err != nil {
		span.SetStatus(codes.Error, "failed to delete avatar")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewErrorFromUsecase(err))
		return
	}

	span.AddEvent("repository avatar removed successfully")
	span.SetStatus(codes.Ok, "repository avatar removed successfully")
	c.JSON(http.StatusOK, gin.H{
		"message": "Remove Avatar successfully",
	})
}
