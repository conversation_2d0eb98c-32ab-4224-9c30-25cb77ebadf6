package handlers_test

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"image"
	"image/color"
	"image/png"
	"mime/multipart"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"api-server/configs"
	"api-server/internal/dto"
	"api-server/internal/enums"
	"api-server/internal/handlers"
	"api-server/internal/usecase"
	"api-server/internal/usecase/user/mocks"
	"api-server/internal/utils"
)

func TestListUsers(t *testing.T) {
	gin.SetMode(gin.TestMode)

	type dependencies struct {
		usecase *mocks.MockUserUsecase
	}

	tests := []struct {
		name          string
		mockFn        func(d *dependencies)
		expectErr     error
		ctxValue      uuid.UUID
		expStatusCode int
	}{
		{
			name:          "should return error if get users fail",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusInternalServerError,
			mockFn: func(d *dependencies) {
				d.usecase.On("ListUsers", mock.Anything, mock.Anything).Return(dto.ListUsersOutput{}, errors.New("Error"))
			},
			expectErr: errors.New("Error"),
		},
		{
			name:          "should return error when GetCurrentUserId returns error",
			expStatusCode: http.StatusForbidden,
			mockFn:        func(d *dependencies) {},
			expectErr:     errors.New("Forbidden"),
		},
		{
			name:          "should get users successfully",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusOK,
			mockFn: func(d *dependencies) {
				d.usecase.On("ListUsers", mock.Anything, mock.Anything).Return(dto.ListUsersOutput{}, nil)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				usecase: &mocks.MockUserUsecase{},
			}
			tt.mockFn(d)
			h := handlers.NewUserUserHandler(nil, d.usecase)
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Set("user_id", tt.ctxValue)
			c.Request, _ = http.NewRequest(http.MethodGet, "/users", nil)
			h.ListUsers(c)
			if tt.expectErr != nil {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expStatusCode, w.Code)
				assert.Equal(t, tt.expectErr.Error(), response.Message)
			} else {
				assert.Equal(t, tt.expStatusCode, w.Code)
			}

		})
	}
}

func TestUpdateUserRole(t *testing.T) {
	gin.SetMode(gin.TestMode)

	userId := uuid.New()

	type dependencies struct {
		usecase *mocks.MockUserUsecase
	}

	tests := []struct {
		name          string
		mockFn        func(d *dependencies)
		expectErr     error
		ctxValue      uuid.UUID
		expStatusCode int
		input         dto.UpdateRoleInput
	}{
		{
			name:          "should return error if GetCurrentUserId fail",
			expStatusCode: http.StatusForbidden,
			mockFn:        func(d *dependencies) {},
			expectErr:     errors.New("Forbidden"),
		},
		{
			name:          "should return error if userID same value with currentUserId ",
			mockFn:        func(d *dependencies) {},
			ctxValue:      userId,
			expStatusCode: http.StatusForbidden,
			expectErr:     errors.New("Forbidden"),
			input: dto.UpdateRoleInput{
				Role:   enums.UserRole_User,
				UserID: userId,
			},
		},
		{
			name:          "should return error if Validate Input fail",
			mockFn:        func(d *dependencies) {},
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusBadRequest,
			expectErr:     errors.New("Key: 'UpdateRoleInput.Role' Error:Field validation for 'Role' failed on the 'required' tag"),
			input: dto.UpdateRoleInput{
				UserID: uuid.New(),
			},
		},
		{
			name: "should return error if UpdateRoleUser fail with ErrUserNotFound",
			mockFn: func(d *dependencies) {
				d.usecase.On("UpdateRoleUser", mock.Anything, mock.Anything).Return(usecase.ErrUserNotFound)
			},
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusNotFound,
			expectErr:     usecase.ErrUserNotFound,
			input: dto.UpdateRoleInput{
				Role:   enums.UserRole_User,
				UserID: uuid.New(),
			},
		},
		{
			name: "should return error if UpdateRoleUser fail with error",
			mockFn: func(d *dependencies) {
				d.usecase.On("UpdateRoleUser", mock.Anything, mock.Anything).Return(errors.New("Error"))
			},
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusInternalServerError,
			expectErr:     errors.New("Error"),
			input: dto.UpdateRoleInput{
				Role:   enums.UserRole_User,
				UserID: uuid.New(),
			},
		},
		{
			name: "should update role user successfully",
			mockFn: func(d *dependencies) {
				d.usecase.On("UpdateRoleUser", mock.Anything, mock.Anything).Return(nil)
			},
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusNoContent,
			input: dto.UpdateRoleInput{
				Role:   enums.UserRole_User,
				UserID: uuid.New(),
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				usecase: &mocks.MockUserUsecase{},
			}
			tt.mockFn(d)
			h := handlers.NewUserUserHandler(nil, d.usecase)
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Set("user_id", tt.ctxValue)
			inputJSON, _ := json.Marshal(tt.input)

			c.Request = httptest.NewRequest(http.MethodPatch, "/users/role", strings.NewReader(string(inputJSON)))
			c.Params = gin.Params{
				{Key: "user_id", Value: tt.input.UserID.String()},
			}
			h.UpdateUserRole(c)
			if tt.expectErr != nil {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expStatusCode, w.Code)
				assert.Equal(t, tt.expectErr.Error(), response.Message)
			} else {
				assert.Equal(t, tt.expStatusCode, http.StatusNoContent)
			}
		})
	}
}

func TestDeleteUser(t *testing.T) {
	gin.SetMode(gin.TestMode)

	userId := uuid.New()

	type dependencies struct {
		usecase *mocks.MockUserUsecase
	}

	tests := []struct {
		name          string
		mockFn        func(d *dependencies)
		expectErr     error
		ctxValue      uuid.UUID
		expStatusCode int
	}{
		{
			name:          "should return error if GetCurrentUserId fails",
			expStatusCode: http.StatusForbidden,
			mockFn:        func(d *dependencies) {},
			expectErr:     errors.New("Forbidden"),
		},
		{
			name:          "should return forbidden if trying to delete self",
			mockFn:        func(d *dependencies) {},
			ctxValue:      userId,
			expStatusCode: http.StatusForbidden,
			expectErr:     errors.New("Cannot delete yourself"),
		},
		{
			name: "should delete user successfully",
			mockFn: func(d *dependencies) {
				d.usecase.On("DeleteUser", mock.Anything, mock.Anything, userId).Return(nil)
			},
			ctxValue:      uuid.New(), // Simulate a valid current user ID that is different from userId
			expStatusCode: http.StatusOK,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				usecase: &mocks.MockUserUsecase{},
			}
			tt.mockFn(d)
			h := handlers.NewUserUserHandler(nil, d.usecase)
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Set("user_id", tt.ctxValue)

			c.Params = gin.Params{
				{Key: "user_id", Value: userId.String()},
			}

			c.Request = httptest.NewRequest(http.MethodDelete, "/users/"+userId.String(), nil)

			h.DeleteUser(c)

			if tt.expectErr != nil {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expStatusCode, w.Code)
				assert.Equal(t, tt.expectErr.Error(), response.Message)
			} else {
				assert.Equal(t, tt.expStatusCode, w.Code)
			}
		})
	}
}

func TestGetCurrentUser(t *testing.T) {
	gin.SetMode(gin.TestMode)

	type dependencies struct {
		usecase *mocks.MockUserUsecase
	}

	tests := []struct {
		name          string
		mockFn        func(d *dependencies)
		expectErr     error
		ctxValue      uuid.UUID
		expStatusCode int
	}{
		{
			name:          "should return error when GetCurrentUserId returns error",
			expStatusCode: http.StatusForbidden,
			mockFn:        func(d *dependencies) {},
			expectErr:     errors.New("Forbidden"),
		},
		{
			name:     "should get current user successfully",
			ctxValue: uuid.New(),
			mockFn: func(d *dependencies) {
				d.usecase.On("GetCurrentUser", mock.Anything, mock.Anything).Return(dto.User{
					ID:                uuid.New().String(),
					Role:              enums.UserRole_User,
					Username:          "testuser",
					UserStatus:        enums.UserStatus_Assigned,
					Avatar:            utils.Ptr("https://example.com/avatar.jpg"),
					GitlabAccessToken: utils.Ptr("gl-access-token-123"),
				}, nil)
			},
			expStatusCode: http.StatusOK,
		},
		{
			name:     "should get current user without gitlab token successfully",
			ctxValue: uuid.New(),
			mockFn: func(d *dependencies) {
				d.usecase.On("GetCurrentUser", mock.Anything, mock.Anything).Return(dto.User{
					ID:                uuid.New().String(),
					Role:              enums.UserRole_User,
					Username:          "testuser2",
					UserStatus:        enums.UserStatus_Assigned,
					Avatar:            nil,
					GitlabAccessToken: nil,
				}, nil)
			},
			expStatusCode: http.StatusOK,
		},
		{
			name:     "should return error if get current user fails",
			ctxValue: uuid.New(),
			mockFn: func(d *dependencies) {
				d.usecase.On("GetCurrentUser", mock.Anything, mock.Anything).Return(dto.User{}, errors.New("Error"))
			},
			expStatusCode: http.StatusInternalServerError,
			expectErr:     errors.New("Error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				usecase: &mocks.MockUserUsecase{},
			}
			tt.mockFn(d)
			h := handlers.NewUserUserHandler(nil, d.usecase)
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Set("user_id", tt.ctxValue)
			c.Request, _ = http.NewRequest(http.MethodGet, "/users/me", nil)

			h.GetCurrentUser(c)

			if tt.expectErr != nil {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expStatusCode, w.Code)
				assert.Equal(t, tt.expectErr.Error(), response.Message)
			} else {
				assert.Equal(t, tt.expStatusCode, w.Code)
				if tt.expStatusCode == http.StatusOK {
					var response dto.User
					err := json.Unmarshal(w.Body.Bytes(), &response)
					assert.NoError(t, err)
					// You might want to add more specific assertions here
					assert.NotEmpty(t, response.ID)
					assert.NotEmpty(t, response.Username)
				}
			}
		})
	}
}

func TestUploadUserAvatar(t *testing.T) {
	gin.SetMode(gin.TestMode)

	userId := uuid.New()

	type dependencies struct {
		usecase *mocks.MockUserUsecase
	}

	createMockContentFile := func() []byte {
		img := image.NewRGBA(image.Rect(0, 0, 100, 100))
		col := color.RGBA{R: 255, G: 0, B: 0, A: 255}
		for y := 0; y < 100; y++ {
			for x := 0; x < 100; x++ {
				img.Set(x, y, col)
			}
		}

		var buf bytes.Buffer
		png.Encode(&buf, img)

		return buf.Bytes()
	}

	tests := []struct {
		name          string
		inp           dto.UploadUserAvatarInput
		mockFn        func(d *dependencies)
		expectErr     error
		ctxValue      uuid.UUID
		expStatusCode int
	}{
		{
			name:          "should return error if upload user avatar fail",
			ctxValue:      userId,
			expStatusCode: http.StatusInternalServerError,
			inp: dto.UploadUserAvatarInput{
				UserId: userId,
			},
			mockFn: func(d *dependencies) {
				d.usecase.On("UploadAvatarUser", mock.Anything, mock.Anything).Return("", errors.New("Internal server error"))
			},
			expectErr: errors.New("Internal server error"),
		},
		{
			name:          "should return error when GetCurrentUserId returns error",
			expStatusCode: http.StatusForbidden,
			mockFn: func(d *dependencies) {
				d.usecase.On("GetCurrentUserId", mock.Anything, mock.Anything).Return(nil, errors.New("Forbidden"))
			},
			expectErr: errors.New("Forbidden - invalid user"),
		},
		{
			name:          "should upload user avatar successfully",
			ctxValue:      userId,
			expStatusCode: http.StatusOK,
			inp: dto.UploadUserAvatarInput{
				UserId: userId,
			},
			mockFn: func(d *dependencies) {
				d.usecase.On("UploadAvatarUser", mock.Anything, mock.Anything).Return("http://localhost/sample.svg", nil)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup Gin context
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)

			// Set user_id in context
			c.Set("user_id", tt.ctxValue)

			c.Params = gin.Params{
				{Key: "user_id", Value: userId.String()},
			}

			body := &bytes.Buffer{}
			writer := multipart.NewWriter(body)
			writer.WriteField("current_user_id", userId.String())
			part, _ := writer.CreateFormFile("file", "mock-avatar.png")
			part.Write(createMockContentFile())

			writer.Close()

			// Set the request body
			// Create an HTTP POST request with the multipart body
			c.Request = httptest.NewRequest(http.MethodPost, fmt.Sprintf("/users/%s/avatar", userId.String()), body)
			c.Request.Header.Set("Content-Type", writer.FormDataContentType())

			d := &dependencies{
				usecase: &mocks.MockUserUsecase{},
			}
			tt.mockFn(d)
			h := handlers.NewUserUserHandler(&configs.GlobalConfig{
				FileConfig: &configs.FileConfig{
					ImageMaxSize: 5 * 1024 * 1024,
				},
			}, d.usecase)

			// Call the handler
			h.UploadUserAvatar(c)

			// Assert the status code
			assert.Equal(t, tt.expStatusCode, w.Code)

			if tt.expectErr != nil {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expectErr.Error(), response.Message)
				assert.Equal(t, tt.expStatusCode, w.Code)
			} else {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
			}
		})
	}
}

func TestDeleteUserAvatar(t *testing.T) {
	gin.SetMode(gin.TestMode)

	userId := uuid.New()

	type dependencies struct {
		usecase *mocks.MockUserUsecase
	}

	tests := []struct {
		name          string
		inp           uuid.UUID
		mockFn        func(d *dependencies)
		expectErr     error
		ctxValue      uuid.UUID
		expStatusCode int
	}{
		{
			name:          "should return error if upload user avatar fail",
			ctxValue:      userId,
			expStatusCode: http.StatusInternalServerError,
			inp:           userId,
			mockFn: func(d *dependencies) {
				d.usecase.On("DeleteAvatarUser", mock.Anything, mock.Anything).Return(errors.New("Error"))
			},
			expectErr: errors.New("Error"),
		},
		{
			name:          "should return error when GetCurrentUserId returns error",
			expStatusCode: http.StatusForbidden,
			mockFn:        func(d *dependencies) {},
			expectErr:     errors.New("Forbidden"),
		},
		{
			name:          "should upload user avatar successfully",
			ctxValue:      userId,
			expStatusCode: http.StatusOK,
			inp:           userId,
			mockFn: func(d *dependencies) {
				d.usecase.On("DeleteAvatarUser", mock.Anything, mock.Anything).Return(nil)
			},
		},
		{
			name:          "should return error if DeleteAvatarUser return ErrUserNotFound",
			ctxValue:      userId,
			expStatusCode: http.StatusNotFound,
			inp:           userId,
			mockFn: func(d *dependencies) {
				d.usecase.On("DeleteAvatarUser", mock.Anything, mock.Anything).Return(usecase.ErrUserNotFound)
			},
			expectErr: errors.New("User not found"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup Gin context
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)

			// Set user_id in context
			c.Set("user_id", tt.ctxValue)
			c.Request = httptest.NewRequest(http.MethodDelete, fmt.Sprintf("/users/%s/avatar", tt.inp.String()), nil)
			c.Params = gin.Params{
				{Key: "user_id", Value: tt.inp.String()},
			}
			d := &dependencies{
				usecase: &mocks.MockUserUsecase{},
			}
			tt.mockFn(d)
			h := handlers.NewUserUserHandler(nil, d.usecase)

			// Call the handler
			h.DeleteUserAvatar(c)

			// Assert the status code
			assert.Equal(t, tt.expStatusCode, w.Code)

			if tt.expectErr != nil {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expectErr.Error(), response.Message)
				assert.Equal(t, tt.expStatusCode, w.Code)
			} else {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
			}
		})
	}
}

func TestChangePassword(t *testing.T) {
	gin.SetMode(gin.TestMode)

	type dependencies struct {
		usecase *mocks.MockUserUsecase
	}

	currentUserId := uuid.New()
	tests := []struct {
		name          string
		input         dto.ChangePasswordInput
		mockFn        func(d *dependencies)
		expectErr     error
		ctxValue      uuid.UUID
		expStatusCode int
		authToken     string
	}{
		{
			name:          "should return error when GetCurrentUserId returns error",
			expStatusCode: http.StatusForbidden,
			mockFn:        func(d *dependencies) {},
			expectErr:     errors.New("Failed to get current user id"),
		},
		{
			name:          "should return error when input validation fails",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusBadRequest,
			mockFn:        func(d *dependencies) {},
			expectErr:     errors.New("Key: 'ChangePasswordInput.CurrentPassword' Error:Field validation for 'CurrentPassword' failed on the 'min' tag"),
			input: dto.ChangePasswordInput{
				CurrentPassword: "oldpass",
				NewPassword:     "newpass",
			},
		},
		{
			name:          "should return error when ChangePassword fails",
			ctxValue:      currentUserId,
			expStatusCode: http.StatusInternalServerError,
			mockFn: func(d *dependencies) {
				d.usecase.On("ChangePassword", mock.Anything, currentUserId, mock.Anything, mock.Anything).Return(errors.New("Internal server error"))
			},
			expectErr: errors.New("Internal server error"),
			authToken: "test-token",
			input: dto.ChangePasswordInput{
				CurrentPassword: "oldpass123",
				NewPassword:     "newpass123",
			},
		},
		{
			name:          "should change password successfully",
			ctxValue:      currentUserId,
			expStatusCode: http.StatusNoContent,
			mockFn: func(d *dependencies) {
				d.usecase.On("ChangePassword", mock.Anything, currentUserId, mock.Anything, mock.Anything).Return(nil)
			},
			authToken: "test-token",
			input: dto.ChangePasswordInput{
				CurrentPassword: "oldpass123",
				NewPassword:     "newpass123",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				usecase: &mocks.MockUserUsecase{},
			}
			tt.mockFn(d)
			h := handlers.NewUserUserHandler(nil, d.usecase)
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Set("user_id", tt.ctxValue)
			inputJSON, _ := json.Marshal(tt.input)
			c.Request = httptest.NewRequest(http.MethodPut, "/users/me/password", strings.NewReader(string(inputJSON)))
			if tt.authToken != "" {
				c.Request.Header.Set("Authorization", "Bearer "+tt.authToken)
			}

			h.ChangePassword(c)

			if tt.expectErr != nil {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expStatusCode, w.Code)
				assert.Contains(t, response.Message, tt.expectErr.Error())
			} else {
				assert.Equal(t, tt.expStatusCode, w.Code)
			}
		})
	}
}
