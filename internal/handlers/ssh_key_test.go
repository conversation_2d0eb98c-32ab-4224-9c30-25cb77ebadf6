package handlers_test

import (
	"bytes"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"api-server/internal/dto"
	"api-server/internal/enums"
	"api-server/internal/handlers"
	"api-server/internal/usecase/ssh_key/mocks"
)

func TestListSshKeys(t *testing.T) {
	gin.SetMode(gin.TestMode)

	type dependencies struct {
		usecase *mocks.MockSshKeyUsecase
	}

	tests := []struct {
		name          string
		mockFn        func(d *dependencies)
		expectErr     error
		ctxValue      uuid.UUID
		expStatusCode int
		setupRequest  func(*http.Request)
	}{
		{
			name:          "should return error if get current user id fails",
			expStatusCode: http.StatusForbidden,
			mockFn:        func(d *dependencies) {},
			expectErr:     errors.New("Forbidden"),
			setupRequest:  func(r *http.Request) {},
		},
		{
			name:          "should return error if binding query fails",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusBadRequest,
			mockFn: func(d *dependencies) {
				// No expectation needed as the error happens before the usecase is called
			},
			expectErr: errors.New("Strconv.ParseInt: parsing \"invalid\": invalid syntax"),
			setupRequest: func(r *http.Request) {
				// Set a malformed query parameter to cause binding error
				q := r.URL.Query()
				q.Add("per_page", "invalid") // This should cause a binding error as per_page expects an integer
				r.URL.RawQuery = q.Encode()
			},
		},
		{
			name:          "should return error if usecase.GetAllSSHKeys fails",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusInternalServerError,
			mockFn: func(d *dependencies) {
				d.usecase.On("GetAllSSHKeys", mock.Anything, mock.Anything).Return(nil, errors.New("database error"))
			},
			expectErr:    errors.New("Database error"), // Match the actual error from handler
			setupRequest: func(r *http.Request) {},
		},
		{
			name:          "should successfully return ssh keys",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusOK,
			mockFn: func(d *dependencies) {
				// Create a successful response
				sshKeys := []dto.SSHKey{
					{
						Id:    uuid.New(),
						Title: "Test Key",
						Key:   "ssh-rsa AAAAB3NzaC1yc2E...",
					},
				}
				mockOutput := &dto.GetSSHKeyOutput{}
				// Need to use a pointer to the slice for the Data field
				data := sshKeys
				mockOutput.Data = &data
				d.usecase.On("GetAllSSHKeys", mock.Anything, mock.Anything).Return(mockOutput, nil)
			},
			setupRequest: func(r *http.Request) {},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup dependencies
			d := &dependencies{
				usecase: mocks.NewMockSshKeyUsecase(t),
			}
			tt.mockFn(d)

			// Create handler with mocked usecase
			h := handlers.NewSshKeyHandler(d.usecase)

			// Setup test HTTP server
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)

			// Set user context if needed
			if tt.ctxValue != uuid.Nil {
				c.Set("user_id", tt.ctxValue)
			}

			// Create test request
			c.Request, _ = http.NewRequest(http.MethodGet, "/users/keys", nil)

			// Set up the request for specific test cases
			tt.setupRequest(c.Request)

			// Call the handler function
			h.ListSshKeys(c)

			// Check the result
			if tt.expectErr != nil {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expStatusCode, w.Code)
				assert.Contains(t, response.Message, tt.expectErr.Error())
			} else {
				assert.Equal(t, tt.expStatusCode, w.Code)
				// For successful responses, we could also test the response body structure
				if w.Code == http.StatusOK {
					var response dto.GetSSHKeyOutput
					err := json.Unmarshal(w.Body.Bytes(), &response)
					assert.NoError(t, err)
					assert.NotNil(t, response.Data)
				}
			}
		})
	}
}

func TestAddNewSshKey(t *testing.T) {
	gin.SetMode(gin.TestMode)

	type dependencies struct {
		usecase *mocks.MockSshKeyUsecase
	}

	// Generate a fixed UUID for testing
	currentUserID := uuid.New()

	// Test input
	validSSHKey := dto.CreateSSHKeyInput{
		Title:     "Test SSH Key",
		Key:       "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQC6Qp1qQRMvvhxb...",
		UsageType: enums.UsageType_Auth,
	}

	tests := []struct {
		name          string
		mockFn        func(d *dependencies)
		setupRequest  func(*gin.Context)
		expectErr     error
		ctxValue      uuid.UUID
		expStatusCode int
	}{
		{
			name:          "should return error if get current user id fails",
			expStatusCode: http.StatusForbidden,
			mockFn:        func(d *dependencies) {},
			setupRequest: func(c *gin.Context) {
				jsonBody, _ := json.Marshal(validSSHKey)
				c.Request, _ = http.NewRequest(http.MethodPost, "/users/keys", bytes.NewBuffer(jsonBody))
				c.Request.Header.Set("Content-Type", "application/json")
			},
			expectErr: errors.New("Forbidden"),
		},
		{
			name:          "should return error if binding JSON fails",
			ctxValue:      currentUserID,
			expStatusCode: http.StatusBadRequest,
			mockFn:        func(d *dependencies) {},
			setupRequest: func(c *gin.Context) {
				// Create malformed JSON to trigger a binding error
				malformedJSON := "{\"title\": \"Test Key\", \"key\" 123}"
				c.Request, _ = http.NewRequest(http.MethodPost, "/users/keys", strings.NewReader(malformedJSON))
				c.Request.Header.Set("Content-Type", "application/json")
			},
			expectErr: errors.New("Invalid character '1' after object key"),
		},
		{
			name:          "should return error if validation fails",
			ctxValue:      currentUserID,
			expStatusCode: http.StatusBadRequest,
			mockFn:        func(d *dependencies) {},
			setupRequest: func(c *gin.Context) {
				// Missing required fields for validation error
				invalidInput := dto.CreateSSHKeyInput{
					Title: "Test Key",
					// Missing Key and UsageType
				}
				jsonBody, _ := json.Marshal(invalidInput)
				c.Request, _ = http.NewRequest(http.MethodPost, "/users/keys", bytes.NewBuffer(jsonBody))
				c.Request.Header.Set("Content-Type", "application/json")
			},
			expectErr: errors.New("Key: 'CreateSSHKeyInput.Key' Error:Field validation for 'Key' failed on the 'required' tag"),
		},
		{
			name:          "should return error if usecase.CreateSSHKey fails",
			ctxValue:      currentUserID,
			expStatusCode: http.StatusInternalServerError,
			mockFn: func(d *dependencies) {
				d.usecase.On("CreateSSHKey", mock.Anything, mock.Anything).Return(errors.New("duplicate key"))
			},
			setupRequest: func(c *gin.Context) {
				jsonBody, _ := json.Marshal(validSSHKey)
				c.Request, _ = http.NewRequest(http.MethodPost, "/users/keys", bytes.NewBuffer(jsonBody))
				c.Request.Header.Set("Content-Type", "application/json")
			},
			expectErr: errors.New("Duplicate key"),
		},
		{
			name:          "should successfully create ssh key",
			ctxValue:      currentUserID,
			expStatusCode: http.StatusOK,
			mockFn: func(d *dependencies) {
				d.usecase.On("CreateSSHKey", mock.Anything, mock.Anything).Return(nil)
			},
			setupRequest: func(c *gin.Context) {
				jsonBody, _ := json.Marshal(validSSHKey)
				c.Request, _ = http.NewRequest(http.MethodPost, "/users/keys", bytes.NewBuffer(jsonBody))
				c.Request.Header.Set("Content-Type", "application/json")
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup dependencies
			d := &dependencies{
				usecase: mocks.NewMockSshKeyUsecase(t),
			}
			tt.mockFn(d)

			// Create handler with mocked usecase
			h := handlers.NewSshKeyHandler(d.usecase)

			// Setup test HTTP server
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)

			// Set user context if needed
			if tt.ctxValue != uuid.Nil {
				c.Set("user_id", tt.ctxValue)
			}

			// Set up the request with the appropriate content
			tt.setupRequest(c)

			// Call the handler function
			h.AddNewSshKey(c)

			// Check the result
			if tt.expectErr != nil {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expStatusCode, w.Code)
				assert.Contains(t, response.Message, tt.expectErr.Error())
			} else {
				assert.Equal(t, tt.expStatusCode, w.Code)
				var response dto.HTTPResponse[dto.SSHKey]
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Contains(t, response.Message, "SSH Key created successfully")
			}
		})
	}
}

func TestDeleteSshKey(t *testing.T) {
	gin.SetMode(gin.TestMode)

	type dependencies struct {
		usecase *mocks.MockSshKeyUsecase
	}

	// Generate fixed UUIDs for testing
	currentUserID := uuid.New()
	keyID := uuid.New()
	invalidUUID := "not-a-uuid"

	tests := []struct {
		name          string
		mockFn        func(d *dependencies)
		expectErr     error
		ctxValue      uuid.UUID
		keyIDParam    string
		expStatusCode int
	}{
		{
			name:          "should return error if get current user id fails",
			expStatusCode: http.StatusForbidden,
			mockFn:        func(d *dependencies) {},
			keyIDParam:    keyID.String(),
			expectErr:     errors.New("Forbidden"),
		},
		{
			name:          "should return error if parsing key_id fails",
			ctxValue:      currentUserID,
			expStatusCode: http.StatusBadRequest,
			mockFn:        func(d *dependencies) {},
			keyIDParam:    invalidUUID,
			expectErr:     errors.New("Invalid UUID"),
		},
		{
			name:          "should return error if usecase.DeleteSSHKey fails",
			ctxValue:      currentUserID,
			expStatusCode: http.StatusInternalServerError,
			mockFn: func(d *dependencies) {
				d.usecase.On("DeleteSSHKey", mock.Anything, mock.Anything).Return(errors.New("key not found"))
			},
			keyIDParam: keyID.String(),
			expectErr:  errors.New("Key not found"),
		},
		{
			name:          "should successfully delete ssh key",
			ctxValue:      currentUserID,
			expStatusCode: http.StatusOK,
			mockFn: func(d *dependencies) {
				d.usecase.On("DeleteSSHKey", mock.Anything, mock.Anything).Return(nil)
			},
			keyIDParam: keyID.String(),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup dependencies
			d := &dependencies{
				usecase: mocks.NewMockSshKeyUsecase(t),
			}
			tt.mockFn(d)

			// Create handler with mocked usecase
			h := handlers.NewSshKeyHandler(d.usecase)

			// Setup test HTTP server
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)

			// Set user context if needed
			if tt.ctxValue != uuid.Nil {
				c.Set("user_id", tt.ctxValue)
			}

			// Create test request
			c.Request, _ = http.NewRequest(http.MethodDelete, "/users/keys/"+tt.keyIDParam, nil)

			// Set the key_id path parameter
			c.Params = gin.Params{
				{Key: "key_id", Value: tt.keyIDParam},
			}

			// Call the handler function
			h.DeleteSshKey(c)

			// Check the result
			if tt.expectErr != nil {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expStatusCode, w.Code)
				assert.Contains(t, response.Message, tt.expectErr.Error())
			} else {
				assert.Equal(t, tt.expStatusCode, w.Code)
				var response dto.HTTPResponse[dto.SSHKey]
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Contains(t, response.Message, "SSH Key deleted successfully")
			}
		})
	}
}

func TestGetSshKey(t *testing.T) {
	gin.SetMode(gin.TestMode)

	type dependencies struct {
		usecase *mocks.MockSshKeyUsecase
	}

	// Generate fixed UUIDs for testing
	currentUserID := uuid.New()
	keyID := uuid.New()
	invalidUUID := "not-a-uuid"

	// Create a mock SSH key for the success response
	now := time.Now()
	mockSSHKeyOutput := &dto.GetSingleSSHKeyOutput{
		Id:        keyID,
		UserId:    currentUserID,
		Title:     "Test Key",
		PublicKey: "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQC6Qp1qQRMvvhxb...",
		UsageType: enums.UsageType_Auth,
		CreatedAt: now,
		UpdatedAt: now,
	}

	tests := []struct {
		name          string
		mockFn        func(d *dependencies)
		expectErr     error
		ctxValue      uuid.UUID
		keyIDParam    string
		expStatusCode int
	}{
		{
			name:          "should return error if get current user id fails",
			expStatusCode: http.StatusForbidden,
			mockFn:        func(d *dependencies) {},
			keyIDParam:    keyID.String(),
			expectErr:     errors.New("Forbidden"),
		},
		{
			name:          "should return error if parsing key_id fails",
			ctxValue:      currentUserID,
			expStatusCode: http.StatusBadRequest,
			mockFn:        func(d *dependencies) {},
			keyIDParam:    invalidUUID,
			expectErr:     errors.New("Invalid UUID"),
		},
		{
			name:          "should return error if usecase.GetSSHKey fails",
			ctxValue:      currentUserID,
			expStatusCode: http.StatusInternalServerError,
			mockFn: func(d *dependencies) {
				d.usecase.On("GetSSHKey", mock.Anything, mock.Anything).Return(nil, errors.New("key not found"))
			},
			keyIDParam: keyID.String(),
			expectErr:  errors.New("Key not found"),
		},
		{
			name:          "should successfully get ssh key",
			ctxValue:      currentUserID,
			expStatusCode: http.StatusOK,
			mockFn: func(d *dependencies) {
				d.usecase.On("GetSSHKey", mock.Anything, mock.Anything).Return(mockSSHKeyOutput, nil)
			},
			keyIDParam: keyID.String(),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup dependencies
			d := &dependencies{
				usecase: mocks.NewMockSshKeyUsecase(t),
			}
			tt.mockFn(d)

			// Create handler with mocked usecase
			h := handlers.NewSshKeyHandler(d.usecase)

			// Setup test HTTP server
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)

			// Set user context if needed
			if tt.ctxValue != uuid.Nil {
				c.Set("user_id", tt.ctxValue)
			}

			// Create test request
			c.Request, _ = http.NewRequest(http.MethodGet, "/users/keys/"+tt.keyIDParam, nil)

			// Set the key_id path parameter
			c.Params = gin.Params{
				{Key: "key_id", Value: tt.keyIDParam},
			}

			// Call the handler function
			h.GetSshKey(c)

			// Check the result
			if tt.expectErr != nil {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expStatusCode, w.Code)
				assert.Contains(t, response.Message, tt.expectErr.Error())
			} else {
				assert.Equal(t, tt.expStatusCode, w.Code)
				var response dto.GetSingleSSHKeyOutput
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, keyID, response.Id)
				assert.Equal(t, "Test Key", response.Title)
			}
		})
	}
}
