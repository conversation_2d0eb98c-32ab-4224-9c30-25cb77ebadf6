package handlers

import (
	workflow_usecase "api-server/internal/usecase/workflow"
)

type WorkflowHandler interface {
	// CreateWorkflow(c *gin.Context)
	// ListWorkflow(c *gin.Context)
}

type workflowHandlerImpl struct {
	usecase workflow_usecase.WorkflowUsecase
}

func NewWorkflowHandler(usecase workflow_usecase.WorkflowUsecase) WorkflowHandler {
	return &workflowHandlerImpl{
		usecase: usecase,
	}
}

// // CreateWorkflow godoc
// //
// //	@Summary	Create workflow
// //	@Tags		Workflow
// //	@Accept		json
// //	@Produce	json
// //	@Param		model	body		dto.CreateWorkflowRequest	true	"Workflow Data"
// //	@Success	201		{object}	dto.CreateWorkflowResponse	"Workflow"
// //	@Failure	400		{object}	dto.HTTPError				"Bad Request - invalid request"
// //	@Failure	404		{object}	dto.HTTPError				"Not Found"
// //	@Failure	500		{object}	dto.HTTPError				"Internal Server Error - Encountered an unexpected condition"
// //	@Router		/repositories/workflows [post]
// func (u *WorkflowHandler) CreateWorkflow(c *gin.Context) {
// 	ctx, span := oteltrace.Tracer.Start(ParseContext(c), "handlers.workflow.CreateWorkflow")
// 	defer span.End()
//
// 	var req dto.CreateWorkflowRequest
// 	if err := c.ShouldBindJSON(&req); err != nil {
// 		dto.ErrorResponse(c, dto.NewBadRequestError(err))
// 		return
// 	}
//
// 	resp, err := u.usecase.CreateWorkflow(ctx, req)
// 	if err != nil {
// 		dto.ErrorResponse(c, dto.NewInternalError(err))
// 		return
// 	}
//
// 	c.JSON(http.StatusCreated, resp)
// }
//
// // CreateWorkflow godoc
// //
// //	@Summary	Create workflow
// //	@Tags		Workflow
// //	@Accept		json
// //	@Produce	json
// //	@Param		model	query		dto.ListWorkflowRequest		true	"Workflow Data"
// //	@Success	200		{object}	dto.ListWorkflowResponse	"Workflow"
// //	@Failure	400		{object}	dto.HTTPError				"Bad Request - invalid request"
// //	@Failure	404		{object}	dto.HTTPError				"Not Found"
// //	@Failure	500		{object}	dto.HTTPError				"Internal Server Error - Encountered an unexpected condition"
// //	@Router		/repositories/workflows [get]
// func (u *WorkflowHandler) ListWorkflow(c *gin.Context) {
// 	ctx, span := oteltrace.Tracer.Start(ParseContext(c), "handlers.workflow.ListWorkflow")
// 	defer span.End()
//
// 	var req dto.ListWorkflowRequest
// 	if err := c.ShouldBindQuery(&req); err != nil {
// 		dto.ErrorResponse(c, dto.NewBadRequestError(err))
// 		return
// 	}
//
// 	resp, err := u.usecase.ListWorkflow(ctx, req)
// 	if err != nil {
// 		dto.ErrorResponse(c, dto.NewInternalError(err))
// 		return
// 	}
//
// 	c.JSON(http.StatusOK, resp)
// }
