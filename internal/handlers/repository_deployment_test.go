package handlers_test

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"api-server/internal/dto"
	"api-server/internal/enums"
	"api-server/internal/gateways/gitlab"
	"api-server/internal/handlers"
	"api-server/internal/types"
	"api-server/internal/usecase"
	"api-server/internal/usecase/repository/mocks"
)

func TestStartDeploymentHandler(t *testing.T) {
	gin.SetMode(gin.TestMode)

	mockUserID, _ := uuid.Parse("d2de43e6-fde6-4deb-a1d0-2d5c2c2e1038")

	repoType := enums.RepoType_Spaces
	namespace := "namespace"
	repoName := "repo-name"
	repoID := *types.NewRepoID(repoType, namespace, repoName)
	_ = repoID

	testcases := []struct {
		name             string
		mockFn           func(u *mocks.MockRepoUsecase)
		requestBody      dto.StartDeploymentRequest
		setUserIDContext bool
		expectBody       string
		expectHTTPCode   int
	}{
		{
			name: "should return Internal error if StartDeployment return error",
			mockFn: func(u *mocks.MockRepoUsecase) {
				u.On("StartDeployment", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			requestBody:      dto.StartDeploymentRequest{},
			setUserIDContext: true,
			expectBody:       "{\"code\":500,\"message\":\"Error\"}",
			expectHTTPCode:   http.StatusInternalServerError,
		},
		{
			name: "should return BadRequest error if StartDeployment return gitlab error",
			mockFn: func(u *mocks.MockRepoUsecase) {
				u.On("StartDeployment", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil, &gitlab.GitlabError{
					StatusCode: http.StatusBadRequest,
					Message:    "bad request",
					Data:       map[string]any{},
				})
			},
			requestBody:      dto.StartDeploymentRequest{},
			setUserIDContext: true,
			expectBody:       "{\"code\":400,\"message\":\"Bad request\"}",
			expectHTTPCode:   http.StatusBadRequest,
		},
		{
			name: "should pass",
			mockFn: func(u *mocks.MockRepoUsecase) {
				u.On("StartDeployment", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(&dto.StartDeploymentResponse{
					Message: "success",
					Data: &dto.Deployment{
						ID:   mockUserID,
						Name: namespace,
						URL:  "url",
						Status: dto.DeploymentStatus{
							Status: "Running",
						},
						RepoID:       repoID,
						WorkflowName: "workflow_name",
						Duration:     0,
						Hardware:     dto.Hardware{},
						User:         &dto.User{},
					},
					Pagination: &dto.Pagination{},
				}, nil)
			},
			requestBody:      dto.StartDeploymentRequest{},
			setUserIDContext: true,
			expectBody:       "{\"message\":\"success\",\"data\":{\"id\":\"d2de43e6-fde6-4deb-a1d0-2d5c2c2e1038\",\"name\":\"namespace\",\"url\":\"url\",\"status\":{\"status\":\"Running\"},\"repo_id\":\"spaces/namespace/repo-name\",\"revision\":\"\",\"commit\":\"\",\"created_at\":\"0001-01-01T00:00:00Z\",\"updated_at\":\"0001-01-01T00:00:00Z\",\"workflow_name\":\"workflow_name\",\"duration\":0,\"hardware\":{\"id\":\"00000000-0000-0000-0000-000000000000\",\"name\":\"\",\"cpu\":0,\"mem\":{\"amount\":0,\"unit\":\"\"},\"gpu_mem\":null,\"created_at\":\"0001-01-01T00:00:00Z\",\"updated_at\":\"0001-01-01T00:00:00Z\"},\"user\":{\"id\":\"\",\"role\":\"\",\"email\":\"\",\"name\":\"\",\"username\":\"\",\"user_status\":\"\",\"avatar\":null}},\"total\":0,\"page_no\":0,\"page_size\":0}",
			expectHTTPCode:   http.StatusOK,
		},
	}

	for _, testcase := range testcases {
		t.Run(testcase.name, func(t *testing.T) {
			usecase := &mocks.MockRepoUsecase{}
			testcase.mockFn(usecase)
			handler := handlers.NewRepositoryHandler(nil, usecase)

			rr := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(rr)
			c.Set("user_id", mockUserID)
			c.Params = gin.Params{
				{Key: enums.REPO_TYPE, Value: repoType.String()},
				{Key: enums.NAMESPACE, Value: namespace},
				{Key: enums.REPO_NAME, Value: repoName},
			}
			if testcase.setUserIDContext {
				c.Set(enums.USER_ID, mockUserID)
			}
			body, _ := json.Marshal(testcase.requestBody)
			c.Request = httptest.NewRequest(http.MethodPost, "/repositories/:repo_type/:namespace/:repo_name/deployments/start", bytes.NewBuffer(body))
			handler.StartDeployment(c)

			assert.Equal(t, testcase.expectHTTPCode, rr.Code)
			assert.Equal(t, testcase.expectBody, rr.Body.String())
		})
	}
}

func TestListDeployments(t *testing.T) {
	gin.SetMode(gin.TestMode)

	repoType := enums.RepoType_Models
	namespace := "namespace"
	repoName := "repo-name"

	type dependencies struct {
		usecase *mocks.MockRepoUsecase
	}

	tests := []struct {
		name          string
		mockFn        func(d *dependencies)
		expectErr     error
		ctxValue      uuid.UUID
		expStatusCode int
	}{
		{
			name:          "should return error if list deployments fail",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusInternalServerError,
			mockFn: func(d *dependencies) {
				d.usecase.On("ListDeployment", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectErr: errors.New("Error"),
		},
		// {
		// 	name:          "should return error when GetCurrentUserId returns error",
		// 	expStatusCode: http.StatusForbidden,
		// 	mockFn:        func(d *dependencies) {},
		// 	expectErr:     errors.New("Forbidden"),
		// },
		{
			name:          "should list deployments successfully",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusOK,
			mockFn: func(d *dependencies) {
				d.usecase.On("ListDeployment", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(&dto.ListDeploymentResponse{}, nil)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				usecase: &mocks.MockRepoUsecase{},
			}
			tt.mockFn(d)
			h := handlers.NewRepositoryHandler(nil, d.usecase)

			// Setup Gin context
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Set("user_id", tt.ctxValue)
			c.Params = gin.Params{
				{Key: enums.REPO_TYPE, Value: repoType.String()},
				{Key: enums.NAMESPACE, Value: namespace},
				{Key: enums.REPO_NAME, Value: repoName},
			}
			c.Request = httptest.NewRequest("GET", "/repositories/:repo_type/:namespace/:repo_name/deployments", nil)
			h.ListDeployments(c)
			assert.Equal(t, tt.expStatusCode, w.Code)
			if tt.expectErr != nil {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expectErr.Error(), response.Message)
				assert.Equal(t, tt.expStatusCode, w.Code)
			} else {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expStatusCode, w.Code)
			}
		})
	}
}

// func TestTerminateDeploymentHandler(t *testing.T) {
// 	gin.SetMode(gin.TestMode)
//
// 	mockUserID, _ := uuid.Parse("d2de43e6-fde6-4deb-a1d0-2d5c2c2e1038")
//
// 	testcases := []struct {
// 		name             string
// 		mockFn           func(u *mocks.MockRepoUsecase)
// 		repoID           *types.RepoID
// 		requestBody      dto.TerminateDeploymentRequest
// 		setUserIDContext bool
// 		expectBody       string
// 		expectHTTPCode   int
// 	}{
// 		{
// 			name:             "should return error if cannot get user id in context",
// 			mockFn:           func(u *mocks.MockRepoUsecase) {},
// 			repoID:           types.NewRepoID(enums.RepoType_Spaces, "namespace", "repo-name"),
// 			requestBody:      dto.TerminateDeploymentRequest{},
// 			setUserIDContext: false,
// 			expectBody:       "{\"code\":500,\"message\":\"error\"}",
// 			expectHTTPCode:   http.StatusInternalServerError,
// 		},
// 		{
// 			name: "should return Internal error if TerminateDeployment return error",
// 			mockFn: func(u *mocks.MockRepoUsecase) {
// 				u.On("TerminateDeployment", mock.Anything, mock.Anything, mock.Anything).Return(errors.New("error"))
// 			},
// 			repoID:           types.NewRepoID(enums.RepoType_Spaces, "namespace", "repo-name"),
// 			requestBody:      dto.TerminateDeploymentRequest{},
// 			setUserIDContext: true,
// 			expectBody:       "{\"code\":500,\"message\":\"error\"}",
// 			expectHTTPCode:   http.StatusInternalServerError,
// 		},
// 		{
// 			name: "should pass",
// 			mockFn: func(u *mocks.MockRepoUsecase) {
// 				u.On("TerminateDeployment", mock.Anything, mock.Anything, mock.Anything).Return(nil)
// 			},
// 			repoID:           types.NewRepoID(enums.RepoType_Spaces, "namespace", "repo-name"),
// 			requestBody:      dto.TerminateDeploymentRequest{},
// 			setUserIDContext: true,
// 			expectBody:       "{\"message\":\"Terminate successfully\"}",
// 			expectHTTPCode:   http.StatusOK,
// 		},
// 	}
//
// 	for _, testcase := range testcases {
// 		t.Run(testcase.name, func(t *testing.T) {
// 			usecase := &mocks.MockRepoUsecase{}
// 			testcase.mockFn(usecase)
// 			handler := handlers.NewRepositoryHandler(nil, usecase)
//
// 			rr := httptest.NewRecorder()
// 			c, _ := gin.CreateTestContext(rr)
// 			c.Set("user_id", mockUserID)
// 			c.Params = gin.Params{
// 				{Key: enums.REPO_TYPE, Value: testcase.repoID.RepoType().String()},
// 				{Key: enums.NAMESPACE, Value: *testcase.repoID.Namespace()},
// 				{Key: enums.REPO_NAME, Value: *testcase.repoID.RepoName()},
// 			}
// 			if testcase.setUserIDContext {
// 				c.Set(enums.USER_ID, mockUserID)
// 			}
// 			body, _ := json.Marshal(testcase.requestBody)
// 			c.Request = httptest.NewRequest(http.MethodPost, "/repositories/:repo_type/:namespace/:repo_name/deployments/terminate", bytes.NewBuffer(body))
// 			handler.TerminateDeployment(c)
//
// 			assert.Equal(t, testcase.expectHTTPCode, rr.Code)
// 			assert.Equal(t, testcase.expectBody, rr.Body.String())
// 		})
// 	}
// }

func TestGetDeploymentStatus(t *testing.T) {
	gin.SetMode(gin.TestMode)

	type dependencies struct {
		usecase *mocks.MockRepoUsecase
	}

	tests := []struct {
		name          string
		mockFn        func(d *dependencies)
		repoID        *types.RepoID
		expectErr     error
		expStatusCode int
		expResponse   dto.GetDeploymentStatusResponse
	}{
		{
			name:          "should return error if get deployment status fails",
			expStatusCode: http.StatusInternalServerError,
			repoID:        types.NewRepoID(enums.RepoType_Models, "namespace", "repo-name"),
			mockFn: func(d *dependencies) {
				d.usecase.On("GetDeploymentStatus", mock.Anything, mock.Anything).Return(nil, errors.New("internal server error"))
			},
			expectErr: errors.New("Internal server error"),
		},
		{
			name:          "should return error when repository ID is invalid",
			expStatusCode: http.StatusBadRequest,
			repoID:        types.NewRepoID(enums.RepoType("invalid"), "namespace", "repo-name"),
			mockFn:        func(d *dependencies) {},
			expectErr:     errors.New("Invalid repository ID"),
		},
		{
			name:          "should get deployment status successfully",
			expStatusCode: http.StatusBadRequest,
			repoID:        types.NewRepoID(enums.RepoType_Models, "namespace", "repo-name"),
			mockFn: func(d *dependencies) {
				d.usecase.On("GetDeploymentStatus", mock.Anything, mock.Anything).Return(nil, usecase.ErrNotSpaceRepo)
			},
			expectErr: usecase.ErrNotSpaceRepo,
		},
		{
			name:          "should get deployment status successfully",
			expStatusCode: http.StatusOK,
			repoID:        types.NewRepoID(enums.RepoType_Spaces, "namespace", "repo-name"),
			mockFn: func(d *dependencies) {
				status := dto.DeploymentStatus{
					Status: "Running",
				}
				d.usecase.On("GetDeploymentStatus", mock.Anything, mock.Anything).Return(&dto.GetDeploymentStatusResponse{
					Data: &status,
				}, nil)
			},
			expResponse: dto.GetDeploymentStatusResponse{
				Data: &dto.DeploymentStatus{
					Status: "Running",
				},
			},
		},
		{
			name:          "should return error if deployment not found",
			expStatusCode: http.StatusNotFound,
			repoID:        types.NewRepoID(enums.RepoType_Spaces, "namespace", "repo-name"),
			mockFn: func(d *dependencies) {
				d.usecase.On("GetDeploymentStatus", mock.Anything, mock.Anything).Return(nil, usecase.ErrRepositoryNotFound)
			},
			expectErr: usecase.ErrRepositoryNotFound,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				usecase: &mocks.MockRepoUsecase{},
			}
			tt.mockFn(d)
			h := handlers.NewRepositoryHandler(nil, d.usecase)

			// Setup Gin context
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)

			// Setting repository ID in parameters
			c.Params = gin.Params{
				{Key: enums.REPO_TYPE, Value: tt.repoID.RepoType().String()},
				{Key: enums.NAMESPACE, Value: *tt.repoID.Namespace()},
				{Key: enums.REPO_NAME, Value: *tt.repoID.RepoName()},
			}

			c.Request = httptest.NewRequest(http.MethodGet, "/repositories/:repo_type/:namespace/:repo_name/deployments/status", nil)

			// Call the handler
			h.GetDeploymentStatus(c)

			// Assert the status code
			assert.Equal(t, tt.expStatusCode, w.Code)

			if tt.expectErr != nil {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expectErr.Error(), response.Message)
			} else {
				var response dto.GetDeploymentStatusResponse
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, "Running", response.Data.Status)
			}
		})
	}
}

func TestUpdateDeploymentStatus(t *testing.T) {
	gin.SetMode(gin.TestMode)

	type dependencies struct {
		usecase *mocks.MockRepoUsecase
	}

	tests := []struct {
		name          string
		requestBody   dto.UpdateDeploymentStatusRequest
		invalidJSON   bool
		mockFn        func(d *dependencies)
		expectErr     error
		expStatusCode int
	}{
		{
			name: "should return error if JSON binding fails",
			requestBody: dto.UpdateDeploymentStatusRequest{
				Name:   "test-deployment",
				Status: "invalid-status",
			},
			invalidJSON:   true,
			expStatusCode: http.StatusBadRequest,
			mockFn:        func(d *dependencies) {},
			expectErr:     errors.New("Invalid JSON"),
		},
		{
			name: "should return error if update deployment status fails",
			requestBody: dto.UpdateDeploymentStatusRequest{
				Name:   "test-deployment",
				Status: enums.ArgoWorkflowStatus_Running,
			},
			mockFn: func(d *dependencies) {
				d.usecase.On("UpdateDeploymentStatus", mock.Anything, mock.Anything).Return(errors.New("internal server error"))
			},
			expStatusCode: http.StatusInternalServerError,
			expectErr:     errors.New("Internal server error"),
		},
		{
			name: "should update deployment status successfully",
			requestBody: dto.UpdateDeploymentStatusRequest{
				Name:     "test-deployment",
				Status:   enums.ArgoWorkflowStatus_Succeeded,
				Duration: 120.5,
			},
			mockFn: func(d *dependencies) {
				d.usecase.On("UpdateDeploymentStatus", mock.Anything, mock.Anything).Return(nil)
			},
			expStatusCode: http.StatusNoContent,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				usecase: &mocks.MockRepoUsecase{},
			}
			tt.mockFn(d)
			h := handlers.NewRepositoryHandler(nil, d.usecase)

			// Setup Gin context
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)

			var body []byte
			var err error

			if tt.invalidJSON {
				body = []byte(`{invalid json`)
			} else {
				body, err = json.Marshal(tt.requestBody)
				assert.NoError(t, err)
			}

			c.Request = httptest.NewRequest(http.MethodPost, "/repositories/deployments/status", bytes.NewBuffer(body))
			c.Request.Header.Set("Content-Type", "application/json")

			// Call the handler
			h.UpdateDeploymentStatus(c)

			// Assert the status code
			assert.Equal(t, tt.expStatusCode, w.Code)

			if tt.expectErr != nil {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				if err == nil { // Only check if we successfully unmarshaled
					assert.Contains(t, response.Message, tt.expectErr.Error())
				}
			} else if tt.expStatusCode == http.StatusNoContent {
				assert.Empty(t, w.Body.String())
			}
		})
	}
}

func TestStopDeployment(t *testing.T) {
	gin.SetMode(gin.TestMode)

	type dependencies struct {
		usecase *mocks.MockRepoUsecase
	}

	tests := []struct {
		name          string
		repoID        *types.RepoID
		mockFn        func(d *dependencies)
		expectErr     error
		expStatusCode int
	}{
		{
			name:          "should return error if repository ID is invalid",
			repoID:        types.NewRepoID(enums.RepoType("invalid"), "namespace", "repo-name"),
			expStatusCode: http.StatusBadRequest,
			mockFn:        func(d *dependencies) {},
			expectErr:     errors.New("Invalid repository ID"),
		},
		{
			name:          "should return error if StopDeployment fails",
			repoID:        types.NewRepoID(enums.RepoType_Spaces, "namespace", "repo-name"),
			expStatusCode: http.StatusInternalServerError,
			mockFn: func(d *dependencies) {
				d.usecase.On("StopDeployment", mock.Anything, mock.Anything).Return(errors.New("failed to stop deployment"))
			},
			expectErr: errors.New("Failed to stop deployment"),
		},
		{
			name:          "should return error if StopDeployment return usecase.ErrNotSpaceRepo",
			repoID:        types.NewRepoID(enums.RepoType_Models, "namespace", "repo-name"),
			expStatusCode: http.StatusBadRequest,
			mockFn: func(d *dependencies) {
				d.usecase.On("StopDeployment", mock.Anything, mock.Anything).Return(usecase.ErrNotSpaceRepo)
			},
			expectErr: usecase.ErrNotSpaceRepo,
		},
		{
			name:          "should stop deployment successfully",
			repoID:        types.NewRepoID(enums.RepoType_Spaces, "namespace", "repo-name"),
			expStatusCode: http.StatusNoContent,
			mockFn: func(d *dependencies) {
				d.usecase.On("StopDeployment", mock.Anything, mock.Anything).Return(nil)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				usecase: &mocks.MockRepoUsecase{},
			}
			tt.mockFn(d)
			h := handlers.NewRepositoryHandler(nil, d.usecase)

			// Setup Gin context
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)

			c.Params = gin.Params{
				{Key: enums.REPO_TYPE, Value: tt.repoID.RepoType().String()},
				{Key: enums.NAMESPACE, Value: *tt.repoID.Namespace()},
				{Key: enums.REPO_NAME, Value: *tt.repoID.RepoName()},
			}

			c.Request = httptest.NewRequest(http.MethodPost, "/repositories/:repo_type/:namespace/:repo_name/deployments/stop", nil)
			c.Request.Header.Set("Content-Type", "application/json")

			// Call the handler
			h.StopDeployment(c)

			// Assert the status code
			assert.Equal(t, tt.expStatusCode, w.Code)

			if tt.expectErr != nil {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Contains(t, response.Message, tt.expectErr.Error())
			} else if tt.expStatusCode == http.StatusNoContent {
				assert.Empty(t, w.Body.String())
			}
		})
	}
}

func TestRestartDeployment(t *testing.T) {
	gin.SetMode(gin.TestMode)

	type dependencies struct {
		usecase *mocks.MockRepoUsecase
	}

	tests := []struct {
		name          string
		repoID        *types.RepoID
		mockFn        func(d *dependencies)
		expectErr     error
		expStatusCode int
		expResponse   string
	}{
		{
			name:          "should return error if repository ID is invalid",
			repoID:        types.NewRepoID(enums.RepoType("invalid"), "namespace", "repo-name"),
			expStatusCode: http.StatusBadRequest,
			mockFn:        func(d *dependencies) {},
			expectErr:     errors.New("Invalid repository ID"),
		},
		{
			name:          "should return error if RestartDeployment fails",
			repoID:        types.NewRepoID(enums.RepoType_Spaces, "namespace", "repo-name"),
			expStatusCode: http.StatusInternalServerError,
			mockFn: func(d *dependencies) {
				d.usecase.On("RestartDeployment", mock.Anything, mock.Anything).Return(errors.New("Failed to restart deployment"))
			},
			expectErr: errors.New("Failed to restart deployment"),
		},
		{
			name:          "should return error if deployment not found",
			repoID:        types.NewRepoID(enums.RepoType_Spaces, "namespace", "repo-name"),
			expStatusCode: http.StatusNotFound,
			mockFn: func(d *dependencies) {
				d.usecase.On("RestartDeployment", mock.Anything, mock.Anything).Return(usecase.ErrRepositoryNotFound)
			},
			expectErr: usecase.ErrRepositoryNotFound,
		},
		{
			name:          "should return error if not space repo",
			repoID:        types.NewRepoID(enums.RepoType_Spaces, "namespace", "repo-name"),
			expStatusCode: http.StatusBadRequest,
			mockFn: func(d *dependencies) {
				d.usecase.On("RestartDeployment", mock.Anything, mock.Anything).Return(usecase.ErrNotSpaceRepo)
			},
			expectErr: usecase.ErrNotSpaceRepo,
		},
		{
			name:          "should restart deployment successfully",
			repoID:        types.NewRepoID(enums.RepoType_Spaces, "namespace", "repo-name"),
			expStatusCode: http.StatusOK,
			mockFn: func(d *dependencies) {
				d.usecase.On("RestartDeployment", mock.Anything, mock.Anything).Return(nil)
			},
			expResponse: `{"message":"Restart space successfully"}`,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				usecase: &mocks.MockRepoUsecase{},
			}
			tt.mockFn(d)
			h := handlers.NewRepositoryHandler(nil, d.usecase)

			// Setup Gin context
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)

			c.Params = gin.Params{
				{Key: enums.REPO_TYPE, Value: tt.repoID.RepoType().String()},
				{Key: enums.NAMESPACE, Value: *tt.repoID.Namespace()},
				{Key: enums.REPO_NAME, Value: *tt.repoID.RepoName()},
			}

			c.Request = httptest.NewRequest(http.MethodGet, "/repositories/:repo_type/:namespace/:repo_name/deployments/restart", nil)

			// Call the handler
			h.RestartDeployment(c)

			// Assert the status code
			assert.Equal(t, tt.expStatusCode, w.Code)

			if tt.expectErr != nil {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Contains(t, response.Message, tt.expectErr.Error())
			} else if tt.expResponse != "" {
				assert.JSONEq(t, tt.expResponse, w.Body.String())
			}
		})
	}
}

func TestGetDeploymentBuildLogs(t *testing.T) {
	gin.SetMode(gin.TestMode)

	type dependencies struct {
		usecase *mocks.MockRepoUsecase
	}

	tests := []struct {
		name            string
		repoID          *types.RepoID
		mockFn          func(d *dependencies)
		expectErr       error
		expStatusCode   int
		checkEventType  bool
		expectedLogData string
		ctxTimeout      time.Duration
	}{
		{
			name:          "should return error if repository ID is invalid",
			repoID:        types.NewRepoID(enums.RepoType("invalid"), "namespace", "repo-name"),
			expStatusCode: http.StatusBadRequest,
			mockFn:        func(d *dependencies) {},
			expectErr:     errors.New("Invalid repository ID"),
		},
		{
			name:          "should return error if GetDeploymentBuildLogs fails",
			repoID:        types.NewRepoID(enums.RepoType_Spaces, "namespace", "repo-name"),
			expStatusCode: http.StatusInternalServerError,
			mockFn: func(d *dependencies) {
				d.usecase.On("GetDeploymentBuildLogs", mock.Anything, mock.Anything).Return(nil, errors.New("failed to get build logs"))
			},
			expectErr: errors.New("Failed to get build logs"),
		},
		{
			name:          "should return error if repository not found",
			repoID:        types.NewRepoID(enums.RepoType_Spaces, "namespace", "repo-name"),
			expStatusCode: http.StatusNotFound,
			mockFn: func(d *dependencies) {
				d.usecase.On("GetDeploymentBuildLogs", mock.Anything, mock.Anything).Return(nil, usecase.ErrRepositoryNotFound)
			},
			expectErr: usecase.ErrRepositoryNotFound,
		},
		{
			name:          "should return error if repo has no deployment",
			repoID:        types.NewRepoID(enums.RepoType_Spaces, "namespace", "repo-name"),
			expStatusCode: http.StatusInternalServerError,
			mockFn: func(d *dependencies) {
				d.usecase.On("GetDeploymentBuildLogs", mock.Anything, mock.Anything).Return(nil, usecase.ErrNoDeployment)
			},
			expectErr: usecase.ErrNoDeployment,
		},
		{
			name:            "should stream logs successfully",
			repoID:          types.NewRepoID(enums.RepoType_Spaces, "namespace", "repo-name"),
			expStatusCode:   http.StatusOK,
			checkEventType:  true,
			expectedLogData: `{"pod_name":"test-pod","content":"log line 1"}`,
			ctxTimeout:      100 * time.Millisecond,
			mockFn: func(d *dependencies) {
				logChan := make(chan dto.GetDeploymentLogsResponse, 1)
				go func() {
					logChan <- dto.GetDeploymentLogsResponse{
						PodName: "test-pod",
						Content: "log line 1",
					}
					time.Sleep(50 * time.Millisecond)
					close(logChan)
				}()
				d.usecase.On("GetDeploymentBuildLogs", mock.Anything, mock.Anything).Return(logChan, nil)
			},
		},
		{
			name:            "should handle context cancellation",
			repoID:          types.NewRepoID(enums.RepoType_Spaces, "namespace", "repo-name"),
			expStatusCode:   http.StatusOK,
			checkEventType:  true,
			expectedLogData: "context deadline exceeded",
			ctxTimeout:      10 * time.Millisecond,
			mockFn: func(d *dependencies) {
				logChan := make(chan dto.GetDeploymentLogsResponse)
				d.usecase.On("GetDeploymentBuildLogs", mock.Anything, mock.Anything).Return(logChan, nil)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				usecase: &mocks.MockRepoUsecase{},
			}
			tt.mockFn(d)
			h := handlers.NewRepositoryHandler(nil, d.usecase)

			// Setup Gin context with recorder that captures streaming responses
			w := NewCloseNotifyRecorder()
			c, _ := gin.CreateTestContext(w)

			// Create a context with timeout
			ctx, cancel := context.WithTimeout(context.Background(), tt.ctxTimeout)
			defer cancel()

			// Create request with context
			req := httptest.NewRequest(http.MethodGet, "/repositories/:repo_type/:namespace/:repo_name/deployments/logs", nil)
			req = req.WithContext(ctx)
			c.Request = req

			c.Params = gin.Params{
				{Key: enums.REPO_TYPE, Value: tt.repoID.RepoType().String()},
				{Key: enums.NAMESPACE, Value: *tt.repoID.Namespace()},
				{Key: enums.REPO_NAME, Value: *tt.repoID.RepoName()},
			}

			// Call the handler
			h.GetDeploymentBuildLogs(c)

			// For error cases
			if tt.expectErr != nil {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Contains(t, response.Message, tt.expectErr.Error())
				assert.Equal(t, tt.expStatusCode, w.Code)
			} else if tt.checkEventType {
				responseBody := w.Body.String()
				if tt.expectedLogData == "context deadline exceeded" {
					assert.Contains(t, responseBody, "event:terminate")
					assert.Contains(t, responseBody, "data:"+tt.expectedLogData)
				} else if tt.expectedLogData == "error encoding JSON" {
					// Check for terminate event
					assert.Contains(t, responseBody, "event:terminate")
					// Check for error message
					assert.Contains(t, responseBody, "data:error encoding JSON")
					// Verify the response ends with a newline
					assert.True(t, strings.HasSuffix(responseBody, "\n\n"))
				} else {
					assert.Contains(t, responseBody, "event:message")
					assert.Contains(t, responseBody, "data:"+tt.expectedLogData)
				}
			}
		})
	}
}

func TestGetDeploymentPodLogs(t *testing.T) {
	gin.SetMode(gin.TestMode)

	type dependencies struct {
		usecase *mocks.MockRepoUsecase
	}

	tests := []struct {
		name            string
		repoID          *types.RepoID
		mockFn          func(d *dependencies)
		expectErr       error
		expStatusCode   int
		checkEventType  bool
		expectedLogData string
		ctxTimeout      time.Duration
	}{
		{
			name:          "should return error if repository ID is invalid",
			repoID:        types.NewRepoID(enums.RepoType("invalid"), "namespace", "repo-name"),
			expStatusCode: http.StatusBadRequest,
			mockFn:        func(d *dependencies) {},
			expectErr:     errors.New("Invalid repository ID"),
		},
		{
			name:          "should return error if GetPodLogs fails",
			repoID:        types.NewRepoID(enums.RepoType_Spaces, "namespace", "repo-name"),
			expStatusCode: http.StatusInternalServerError,
			mockFn: func(d *dependencies) {
				d.usecase.On("GetPodLogs", mock.Anything, mock.Anything).Return(nil, errors.New("Failed to get pod logs"))
			},
			expectErr: errors.New("Failed to get pod logs"),
		},
		{
			name:          "should return error if repository not found",
			repoID:        types.NewRepoID(enums.RepoType_Spaces, "namespace", "repo-name"),
			expStatusCode: http.StatusNotFound,
			mockFn: func(d *dependencies) {
				d.usecase.On("GetPodLogs", mock.Anything, mock.Anything).Return(nil, usecase.ErrRepositoryNotFound)
			},
			expectErr: usecase.ErrRepositoryNotFound,
		},
		{
			name:          "should return error if repo has no deployment",
			repoID:        types.NewRepoID(enums.RepoType_Spaces, "namespace", "repo-name"),
			expStatusCode: http.StatusInternalServerError,
			mockFn: func(d *dependencies) {
				d.usecase.On("GetPodLogs", mock.Anything, mock.Anything).Return(nil, usecase.ErrNoDeployment)
			},
			expectErr: usecase.ErrNoDeployment,
		},
		{
			name:            "should stream logs successfully",
			repoID:          types.NewRepoID(enums.RepoType_Spaces, "namespace", "repo-name"),
			expStatusCode:   http.StatusOK,
			checkEventType:  true,
			expectedLogData: `{"pod_name":"test-pod","content":"log line 1"}`,
			ctxTimeout:      100 * time.Millisecond,
			mockFn: func(d *dependencies) {
				logChan := make(chan dto.GetDeploymentLogsResponse, 1)
				go func() {
					logChan <- dto.GetDeploymentLogsResponse{
						PodName: "test-pod",
						Content: "log line 1",
					}
					time.Sleep(50 * time.Millisecond)
					close(logChan)
				}()
				d.usecase.On("GetPodLogs", mock.Anything, mock.Anything).Return(logChan, nil)
			},
		},
		{
			name:            "should handle context cancellation",
			repoID:          types.NewRepoID(enums.RepoType_Spaces, "namespace", "repo-name"),
			expStatusCode:   http.StatusOK,
			checkEventType:  true,
			expectedLogData: "context deadline exceeded",
			ctxTimeout:      10 * time.Millisecond,
			mockFn: func(d *dependencies) {
				logChan := make(chan dto.GetDeploymentLogsResponse)
				d.usecase.On("GetPodLogs", mock.Anything, mock.Anything).Return(logChan, nil)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				usecase: &mocks.MockRepoUsecase{},
			}
			tt.mockFn(d)
			h := handlers.NewRepositoryHandler(nil, d.usecase)

			// Setup Gin context with recorder that captures streaming responses
			w := NewCloseNotifyRecorder()
			c, _ := gin.CreateTestContext(w)

			// Create a context with timeout
			ctx, cancel := context.WithTimeout(context.Background(), tt.ctxTimeout)
			defer cancel()

			// Create request with context
			req := httptest.NewRequest(http.MethodGet, "/repositories/:repo_type/:namespace/:repo_name/deployments/pods/logs", nil)
			req = req.WithContext(ctx)
			c.Request = req

			c.Params = gin.Params{
				{Key: enums.REPO_TYPE, Value: tt.repoID.RepoType().String()},
				{Key: enums.NAMESPACE, Value: *tt.repoID.Namespace()},
				{Key: enums.REPO_NAME, Value: *tt.repoID.RepoName()},
			}

			// Call the handler
			h.GetDeploymentPodLogs(c)

			// For error cases
			if tt.expectErr != nil {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Contains(t, response.Message, tt.expectErr.Error())
				assert.Equal(t, tt.expStatusCode, w.Code)
			} else if tt.checkEventType {
				responseBody := w.Body.String()
				if tt.expectedLogData == "context deadline exceeded" {
					assert.Contains(t, responseBody, "event:terminate")
					assert.Contains(t, responseBody, "data:"+tt.expectedLogData)
				} else if tt.expectedLogData == "error encoding JSON" {
					// Check for terminate event
					assert.Contains(t, responseBody, "event:terminate")
					// Check for error message
					assert.Contains(t, responseBody, "data:error encoding JSON")
					// Verify the response ends with a newline
					assert.True(t, strings.HasSuffix(responseBody, "\n\n"))
				} else {
					assert.Contains(t, responseBody, "event:message")
					assert.Contains(t, responseBody, "data:"+tt.expectedLogData)
				}
			}
		})
	}
}

// CloseNotifyRecorder wraps httptest.ResponseRecorder and implements http.CloseNotifier.
type CloseNotifyRecorder struct {
	*httptest.ResponseRecorder
	closeChan chan bool
}

// CloseNotify implements the http.CloseNotifier interface.
func (r *CloseNotifyRecorder) CloseNotify() <-chan bool {
	return r.closeChan
}

// NewCloseNotifyRecorder creates a new CloseNotifyRecorder.
func NewCloseNotifyRecorder() *CloseNotifyRecorder {
	return &CloseNotifyRecorder{
		ResponseRecorder: httptest.NewRecorder(),
		closeChan:        make(chan bool, 1), // Buffered channel to simulate close notifications.
	}
}
