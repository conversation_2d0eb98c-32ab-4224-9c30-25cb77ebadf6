package handlers_test

import (
	"bytes"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"api-server/internal/dto"
	"api-server/internal/entities"
	"api-server/internal/handlers"
	"api-server/internal/usecase/repository/mocks"
)

func TestCreateRepoTag(t *testing.T) {
	gin.SetMode(gin.TestMode)

	type dependencies struct {
		usecase *mocks.MockRepoUsecase
	}

	validRepoTypes := []string{"spaces", "models", "datasets"}

	tests := []struct {
		name          string
		mockFn        func(d *dependencies)
		expectErr     string
		ctxValue      uuid.UUID
		input         dto.CreateRepoTagInput
		expStatusCode int
	}{
		{
			name: "should return error when validate request",
			input: dto.CreateRepoTagInput{
				Type:      "category",
				SubType:   "language",
				Name:      strings.Repeat("Python", 20),
				RepoTypes: validRepoTypes,
			},
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusBadRequest,
			mockFn:        func(d *dependencies) {},
			expectErr:     "Name must start and end with a letter or number, may contain letters, numbers, underscores, spaces, and must be between 2 and 50 characters long",
		},
		{
			name: "should return error when GetCurrentUserId returns error",
			input: dto.CreateRepoTagInput{
				Type:      "category",
				SubType:   "language",
				Name:      "Python",
				RepoTypes: validRepoTypes,
			},
			expStatusCode: http.StatusForbidden,
			mockFn:        func(d *dependencies) {},
			expectErr:     "Failed to get current user id",
		},
		{
			name: "should return error if validation fails for Name",
			input: dto.CreateRepoTagInput{
				Type:      "category",
				SubType:   "language",
				Name:      "", // Name is required
				RepoTypes: validRepoTypes,
			},
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusBadRequest,
			mockFn:        func(d *dependencies) {},
			expectErr:     "Key: 'CreateRepoTagInput.Name' Error:Field validation for 'Name' failed on the 'required' tag",
		},
		{
			name: "should return error if RepoTypes validation fails",
			input: dto.CreateRepoTagInput{
				Type:      "category",
				SubType:   "language",
				Name:      "Python",
				RepoTypes: []string{"Invalid"}, // Invalid repo type
			},
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusBadRequest,
			mockFn:        func(d *dependencies) {},
			expectErr:     "Key: 'CreateRepoTagInput.RepoTypes[0]' Error:Field validation for 'RepoTypes[0]' failed on the 'oneof' tag",
		},
		{
			name: "should return error if repo usecase returns error",
			input: dto.CreateRepoTagInput{
				Type:      "category",
				SubType:   "language",
				Name:      "Python",
				RepoTypes: validRepoTypes,
			},
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusInternalServerError,
			mockFn: func(d *dependencies) {
				d.usecase.On("CreateRepoTag", mock.Anything, mock.Anything).Return(errors.New("database error"))
			},
			expectErr: "Database error",
		},
		{
			name: "should create tag successfully",
			input: dto.CreateRepoTagInput{
				Type:      "category",
				SubType:   "language",
				Name:      "Python",
				RepoTypes: validRepoTypes,
			},
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusCreated,
			mockFn: func(d *dependencies) {
				d.usecase.On("CreateRepoTag", mock.Anything, mock.Anything).Return(nil)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				usecase: &mocks.MockRepoUsecase{},
			}
			tt.mockFn(d)
			h := handlers.NewRepositoryHandler(nil, d.usecase)

			// Setup Gin context
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Set("user_id", tt.ctxValue)
			jsonInput, _ := json.Marshal(tt.input)
			c.Request = httptest.NewRequest("POST", "/repositories/tags", bytes.NewReader(jsonInput))
			c.Request.Header.Set("Content-Type", "application/json")

			h.CreateRepoTag(c)

			assert.Equal(t, tt.expStatusCode, w.Code)
			if tt.expectErr != "" {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Contains(t, response.Message, tt.expectErr)
			} else {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, "Tag created successfully", response["message"])
			}
		})
	}
}

func TestUpdateRepoTag(t *testing.T) {
	gin.SetMode(gin.TestMode)

	type dependencies struct {
		usecase *mocks.MockRepoUsecase
	}

	validRepoTypes := []string{"spaces", "models", "datasets"}
	validTagId := uuid.New()

	tests := []struct {
		name          string
		mockFn        func(d *dependencies)
		expectErr     string
		ctxValue      uuid.UUID
		input         dto.UpdateRepoTagInput
		tagIdParam    string
		expStatusCode int
	}{
		{
			name: "should return error when validate request",
			input: dto.UpdateRepoTagInput{
				Type:      "category",
				SubType:   "language",
				Name:      strings.Repeat("Python", 20),
				RepoTypes: validRepoTypes,
			},
			tagIdParam:    validTagId.String(),
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusBadRequest,
			mockFn:        func(d *dependencies) {},
			expectErr:     "Name must start and end with a letter or number, may contain letters, numbers, underscores, spaces, and must be between 2 and 50 characters long",
		},
		{
			name: "should return error when GetCurrentUserId returns error",
			input: dto.UpdateRepoTagInput{
				Type:      "category",
				SubType:   "language",
				Name:      "Python",
				RepoTypes: validRepoTypes,
			},
			tagIdParam:    validTagId.String(),
			expStatusCode: http.StatusForbidden,
			mockFn:        func(d *dependencies) {},
			expectErr:     "Failed to get current user id",
		},
		{
			name: "should return error if binding JSON fails",
			input: dto.UpdateRepoTagInput{
				Type:      "category",
				SubType:   "language",
				Name:      "Python",
				RepoTypes: validRepoTypes,
			},
			tagIdParam:    validTagId.String(),
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusBadRequest,
			mockFn:        func(d *dependencies) {},
			expectErr:     "EOF",
		},
		{
			name: "should return error if tag_id parameter is invalid",
			input: dto.UpdateRepoTagInput{
				Type:      "category",
				SubType:   "language",
				Name:      "Python",
				RepoTypes: validRepoTypes,
			},
			tagIdParam:    "invalid-uuid",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusBadRequest,
			mockFn:        func(d *dependencies) {},
			expectErr:     "Invalid UUID",
		},
		{
			name: "should return error if validation fails for Name",
			input: dto.UpdateRepoTagInput{
				Type:      "category",
				SubType:   "language",
				Name:      "", // Name is required
				RepoTypes: validRepoTypes,
			},
			tagIdParam:    validTagId.String(),
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusBadRequest,
			mockFn:        func(d *dependencies) {},
			expectErr:     "Key: 'UpdateRepoTagInput.Name' Error:Field validation for 'Name' failed on the 'required' tag",
		},
		{
			name: "should return error if RepoTypes validation fails",
			input: dto.UpdateRepoTagInput{
				Type:      "category",
				SubType:   "language",
				Name:      "Python",
				RepoTypes: []string{"Invalid"}, // Invalid repo type
			},
			tagIdParam:    validTagId.String(),
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusBadRequest,
			mockFn:        func(d *dependencies) {},
			expectErr:     "Key: 'UpdateRepoTagInput.RepoTypes[0]' Error:Field validation for 'RepoTypes[0]' failed on the 'oneof' tag",
		},
		{
			name: "should return error if repo usecase returns error",
			input: dto.UpdateRepoTagInput{
				Type:      "category",
				SubType:   "language",
				Name:      "Python",
				RepoTypes: validRepoTypes,
			},
			tagIdParam:    validTagId.String(),
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusInternalServerError,
			mockFn: func(d *dependencies) {
				d.usecase.On("UpdateRepoTag", mock.Anything, mock.Anything).Return(errors.New("database error"))
			},
			expectErr: "Database error",
		},
		{
			name: "should update tag successfully",
			input: dto.UpdateRepoTagInput{
				Type:      "category",
				SubType:   "language",
				Name:      "Python",
				RepoTypes: validRepoTypes,
			},
			tagIdParam:    validTagId.String(),
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusOK,
			mockFn: func(d *dependencies) {
				d.usecase.On("UpdateRepoTag", mock.Anything, mock.Anything).Return(nil)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				usecase: &mocks.MockRepoUsecase{},
			}
			tt.mockFn(d)
			h := handlers.NewRepositoryHandler(nil, d.usecase)

			// Setup Gin context
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Set("user_id", tt.ctxValue)

			// Set path parameter
			c.AddParam("tag_id", tt.tagIdParam)

			// For the binding JSON error test case, don't set the request body
			if tt.name != "should return error if binding JSON fails" {
				jsonInput, _ := json.Marshal(tt.input)
				c.Request = httptest.NewRequest("PATCH", "/repositories/tags/"+tt.tagIdParam, bytes.NewReader(jsonInput))
				c.Request.Header.Set("Content-Type", "application/json")
			} else {
				// Empty request body will cause binding error
				c.Request = httptest.NewRequest("PATCH", "/repositories/tags/"+tt.tagIdParam, nil)
				c.Request.Header.Set("Content-Type", "application/json")
			}

			h.UpdateRepoTag(c)

			assert.Equal(t, tt.expStatusCode, w.Code)
			if tt.expectErr != "" {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Contains(t, response.Message, tt.expectErr)
			} else {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, "Tag updated successfully", response["message"])
			}
		})
	}
}

func TestDeleteRepoTag(t *testing.T) {
	gin.SetMode(gin.TestMode)

	type dependencies struct {
		usecase *mocks.MockRepoUsecase
	}

	validTagId := uuid.New()

	tests := []struct {
		name          string
		mockFn        func(d *dependencies)
		expectErr     string
		ctxValue      uuid.UUID
		tagIdParam    string
		expStatusCode int
	}{
		{
			name:          "should return error when GetCurrentUserId returns error",
			tagIdParam:    validTagId.String(),
			expStatusCode: http.StatusForbidden,
			mockFn:        func(d *dependencies) {},
			expectErr:     "Failed to get current user id",
		},
		{
			name:          "should return error if tag_id parameter is invalid",
			tagIdParam:    "invalid-uuid",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusBadRequest,
			mockFn:        func(d *dependencies) {},
			expectErr:     "Invalid UUID",
		},
		{
			name:          "should return error if repo usecase returns error",
			tagIdParam:    validTagId.String(),
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusInternalServerError,
			mockFn: func(d *dependencies) {
				d.usecase.On("DeleteRepoTag", mock.Anything, mock.Anything).Return(errors.New("database error"))
			},
			expectErr: "Database error",
		},
		{
			name:          "should delete tag successfully",
			tagIdParam:    validTagId.String(),
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusOK,
			mockFn: func(d *dependencies) {
				d.usecase.On("DeleteRepoTag", mock.Anything, mock.Anything).Return(nil)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				usecase: &mocks.MockRepoUsecase{},
			}
			tt.mockFn(d)
			h := handlers.NewRepositoryHandler(nil, d.usecase)

			// Setup Gin context
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Set("user_id", tt.ctxValue)

			// Set path parameter
			c.AddParam("tag_id", tt.tagIdParam)
			c.Request = httptest.NewRequest("DELETE", "/repositories/tags/"+tt.tagIdParam, nil)

			h.DeleteRepoTag(c)

			assert.Equal(t, tt.expStatusCode, w.Code)
			if tt.expectErr != "" {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Contains(t, response.Message, tt.expectErr)
			} else {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, "Tag deleted successfully", response["message"])
			}
		})
	}
}

func TestGetRepoTag(t *testing.T) {
	gin.SetMode(gin.TestMode)

	type dependencies struct {
		usecase *mocks.MockRepoUsecase
	}

	validTagId := uuid.New()
	currentUserID := uuid.New()

	tests := []struct {
		name          string
		mockFn        func(d *dependencies)
		expectErr     string
		ctxValue      uuid.UUID
		tagIdParam    string
		expStatusCode int
		expOutput     *dto.Tag
	}{
		{
			name:          "should return error when GetCurrentUserId returns error",
			tagIdParam:    validTagId.String(),
			expStatusCode: http.StatusForbidden,
			mockFn:        func(d *dependencies) {},
			expectErr:     "Failed to get current user id",
		},
		{
			name:          "should return error if tag_id parameter is invalid",
			tagIdParam:    "invalid-uuid",
			ctxValue:      currentUserID,
			expStatusCode: http.StatusBadRequest,
			mockFn:        func(d *dependencies) {},
			expectErr:     "Invalid UUID",
		},
		{
			name:       "should return error if repo usecase returns error",
			tagIdParam: validTagId.String(),
			ctxValue:   currentUserID,
			mockFn: func(d *dependencies) {
				d.usecase.On("GetRepoTag", mock.Anything, mock.Anything).Return(nil, errors.New("database error"))
			},
			expStatusCode: http.StatusInternalServerError,
			expectErr:     "Database error",
		},
		{
			name:       "should get tag successfully",
			tagIdParam: validTagId.String(),
			ctxValue:   currentUserID,
			mockFn: func(d *dependencies) {
				tag := &entities.Tag{
					BaseModel: entities.BaseModel{
						ID: validTagId,
					},
					Type:      "category",
					SubType:   "language",
					Name:      "Python",
					RepoTypes: []string{"spaces", "models", "datasets"},
				}
				d.usecase.On("GetRepoTag", mock.Anything, mock.Anything).Return(tag, nil)
			},
			expStatusCode: http.StatusOK,
			expOutput: &dto.Tag{
				Id:        validTagId,
				Type:      "category",
				SubType:   "language",
				Name:      "Python",
				RepoTypes: []string{"spaces", "models", "datasets"},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				usecase: &mocks.MockRepoUsecase{},
			}
			tt.mockFn(d)
			h := handlers.NewRepositoryHandler(nil, d.usecase)

			// Setup Gin context
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Set("user_id", tt.ctxValue)

			// Set path parameter
			c.AddParam("tag_id", tt.tagIdParam)
			c.Request = httptest.NewRequest("GET", "/repositories/tags/"+tt.tagIdParam, nil)

			h.GetRepoTag(c)

			assert.Equal(t, tt.expStatusCode, w.Code)
			if tt.expectErr != "" {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Contains(t, response.Message, tt.expectErr)
			} else {
				var response dto.Tag
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expOutput.Id, response.Id)
				assert.Equal(t, tt.expOutput.Type, response.Type)
				assert.Equal(t, tt.expOutput.SubType, response.SubType)
				assert.Equal(t, tt.expOutput.Name, response.Name)
				assert.Equal(t, tt.expOutput.RepoTypes, response.RepoTypes)
			}
		})
	}
}
