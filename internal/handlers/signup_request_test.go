package handlers_test

import (
	"api-server/internal/enums"
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"api-server/internal/dto"
	"api-server/internal/handlers"
	"api-server/internal/usecase/signup_request/mocks"
)

func TestSignup(t *testing.T) {
	gin.SetMode(gin.TestMode)

	type dependencies struct {
		usecase *mocks.MockSignupRequestUsecase
	}

	tests := []struct {
		name          string
		input         dto.SignupInput
		ctxValue      uuid.UUID
		mockFn        func(d *dependencies)
		expectErr     error
		expStatusCode int
	}{
		{
			name: "should return error when Signup returns error",
			input: dto.SignupInput{
				Email:    "<EMAIL>",
				Password: "112391824",
				Name:     "huongbuibuidz",
				Username: "username",
			},
			mockFn: func(d *dependencies) {
				d.usecase.On("Signup", mock.Anything, mock.Anything).Return(errors.New("Error"))
			},
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusInternalServerError,
			expectErr:     errors.New("Error"),
		},
		{
			name: "should return error when email invalid",
			input: dto.SignupInput{
				Email:    "huongbuidzgmail.com",
				Password: "112391824",
				Name:     "name",
				Username: "username",
			},
			mockFn: func(d *dependencies) {
				d.usecase.On("Signup", mock.Anything, mock.Anything).Return(errors.New("Key: 'SignupInput.Email' Error:Field validation for 'Email' failed on the 'email' tag"))
			},
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusBadRequest,
			expectErr:     errors.New("Key: 'SignupInput.Email' Error:Field validation for 'Email' failed on the 'email' tag"),
		},
		{
			name: "should return error when name invalid",
			input: dto.SignupInput{
				Email:    "<EMAIL>",
				Password: "112391824",
				Name:     "hu?>+()",
				Username: "username",
			},
			mockFn: func(d *dependencies) {
				d.usecase.On("Signup", mock.Anything, mock.Anything).Return(errors.New("Error"))
			},
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusBadRequest,
			expectErr:     errors.New("Name must start and end with a letter or number, may contain letters, numbers, underscores, spaces, and must be between 2 and 50 characters long"),
		},
		{
			name: "should return error when username invalid",
			input: dto.SignupInput{
				Email:    "<EMAIL>",
				Password: "112391824",
				Name:     "name",
				Username: "user+m@",
			},
			mockFn:        func(d *dependencies) {},
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusBadRequest,
			expectErr:     errors.New("Username must start with a letter or number, may contain underscores or hyphens, and must be between 2 and 50 characters long."),
		},
		{
			name: "should return error if error",
			input: dto.SignupInput{
				Email:    "<EMAIL>",
				Password: "112391824",
				Name:     "huongbuibuidz",
				Username: "username",
			},
			mockFn: func(d *dependencies) {
				d.usecase.On("Signup", mock.Anything, mock.Anything).Return(errors.New("Error"))
			},
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusInternalServerError,
			expectErr:     errors.New("Error"),
		},
		{
			name: "should signup successfully",
			input: dto.SignupInput{
				Email:    "<EMAIL>",
				Password: "112391824",
				Name:     "huongbuibuidz",
				Username: "username",
			},
			mockFn: func(d *dependencies) {
				d.usecase.On("Signup", mock.Anything, mock.Anything).Return(nil)
			},
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusCreated,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				usecase: &mocks.MockSignupRequestUsecase{},
			}
			tt.mockFn(d)

			handler := handlers.NewSignUpRequestHandler(d.usecase)
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Set("user_id", tt.ctxValue)
			inputJson, _ := json.Marshal(tt.input)
			c.Request, _ = http.NewRequest(http.MethodPost, "/signup", bytes.NewBuffer(inputJson))

			handler.Signup(c)

			assert.Equal(t, tt.expStatusCode, w.Code)
			if tt.expectErr != nil {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expectErr.Error(), response.Message)
			}
		})
	}
}

func TestListApprovalRequest(t *testing.T) {
	gin.SetMode(gin.TestMode)

	type dependencies struct {
		usecase *mocks.MockSignupRequestUsecase
	}

	tests := []struct {
		name          string
		ctxValue      uuid.UUID
		mockFn        func(d *dependencies)
		expectErr     error
		expStatusCode int
	}{
		{
			name: "should return error when GetAllSignUpRequests returns error",
			mockFn: func(d *dependencies) {
				d.usecase.On("GetAllSignUpRequests", mock.Anything).Return(dto.GetAllSignUpRequestsOutput{}, errors.New("internal server error"))
			},
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusInternalServerError,
			expectErr:     errors.New("Internal server error"),
		},
		{
			name: "should list all sign up requests successfully",
			mockFn: func(d *dependencies) {
				d.usecase.On("GetAllSignUpRequests", mock.Anything).Return(dto.GetAllSignUpRequestsOutput{}, nil)
			},
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusOK,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Arrange
			dep := dependencies{
				usecase: &mocks.MockSignupRequestUsecase{},
			}
			tt.mockFn(&dep)
			h := handlers.NewSignUpRequestHandler(dep.usecase)
			w := httptest.NewRecorder()
			ctx, _ := gin.CreateTestContext(w)
			ctx.Set("user_id", tt.ctxValue)
			ctx.Request = httptest.NewRequest(http.MethodGet, "/signups", nil)

			// Act
			h.ListApprovalRequest(ctx)
			assert.Equal(t, tt.expStatusCode, w.Code)
			if tt.expectErr != nil {
				var resp dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &resp)
				assert.NoError(t, err)
				assert.Equal(t, tt.expectErr.Error(), resp.Message)
				assert.Equal(t, tt.expStatusCode, w.Code)
			} else {
				var resp map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &resp)
				assert.NoError(t, err)
				assert.Equal(t, tt.expStatusCode, w.Code)
			}
		})
	}
}

func TestCreateApprovalRequest(t *testing.T) {
	gin.SetMode(gin.TestMode)

	type dependencies struct {
		usecase *mocks.MockSignupRequestUsecase
	}

	isAccepted := true
	tests := []struct {
		name          string
		input         dto.ApprovalRequest
		ctxValue      uuid.UUID
		mockFn        func(d *dependencies)
		expectErr     error
		expStatusCode int
	}{
		{
			name: "should return error when ProcessSignUpRequest returns error",
			input: dto.ApprovalRequest{
				ID:         uuid.New(),
				IsAccepted: &isAccepted,
			},
			mockFn: func(d *dependencies) {
				d.usecase.On("ProcessSignUpRequest", mock.Anything, mock.Anything).Return(errors.New("internal server error"))
			},
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusInternalServerError,
			expectErr:     errors.New("Internal server error"),
		},
		{
			name: "should approve approval request successfully",
			input: dto.ApprovalRequest{
				ID:         uuid.New(),
				IsAccepted: &isAccepted,
			},
			mockFn: func(d *dependencies) {
				d.usecase.On("ProcessSignUpRequest", mock.Anything, mock.Anything).Return(nil)
			},
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusNoContent,
		},
		{
			name: "should return error when GetCurrentUserId returns error",
			input: dto.ApprovalRequest{
				ID:         uuid.New(),
				IsAccepted: &isAccepted,
			},
			mockFn:        func(d *dependencies) {},
			expStatusCode: http.StatusForbidden,
			expectErr:     errors.New("Forbidden"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Arrange
			dep := dependencies{
				usecase: &mocks.MockSignupRequestUsecase{},
			}
			tt.mockFn(&dep)
			h := handlers.NewSignUpRequestHandler(dep.usecase)
			w := httptest.NewRecorder()
			ctx, _ := gin.CreateTestContext(w)
			ctx.Set("user_id", tt.ctxValue)
			inputJSON, _ := json.Marshal(tt.input)
			ctx.Request = httptest.NewRequest(http.MethodPatch, "/signup_requests/"+tt.input.ID.String()+"/approval", strings.NewReader(string(inputJSON)))
			ctx.Params = gin.Params{
				{Key: "id", Value: tt.input.ID.String()},
			}
			// Act
			h.ProcessSignUpRequest(ctx)
			assert.Equal(t, tt.expStatusCode, w.Code)
			if tt.expectErr != nil {
				var resp dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &resp)
				assert.NoError(t, err)
				assert.Equal(t, tt.expectErr.Error(), resp.Message)
				assert.Equal(t, tt.expStatusCode, w.Code)
			} else {
				assert.Equal(t, http.StatusNoContent, w.Code)
			}
		})
	}
}

func TestInviteUser(t *testing.T) {
	gin.SetMode(gin.TestMode)

	type dependencies struct {
		usecase *mocks.MockSignupRequestUsecase
	}

	tests := []struct {
		name          string
		input         dto.InviteUserInput
		ctxValue      interface{}
		mockFn        func(d *dependencies)
		expectErr     error
		expStatusCode int
		expMessage    string
	}{
		{
			name: "should return error when InviteUser returns error",
			input: dto.InviteUserInput{
				Email: "<EMAIL>",
				Name:  "huongbui",
				Role:  enums.UserRole_User,
			},
			mockFn: func(d *dependencies) {
				d.usecase.On("InviteUser", mock.Anything, mock.Anything).Return(errors.New("internal server error"))
			},
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusInternalServerError,
			expectErr:     errors.New("Internal server error"),
		},
		{
			name: "should validate fail when email is empty",
			input: dto.InviteUserInput{
				Email: "",
				Name:  "huongbui",
				Role:  enums.UserRole_User,
			},
			mockFn:        func(d *dependencies) {},
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusBadRequest,
			expectErr:     fmt.Errorf("Key: 'InviteUserInput.Email' Error:Field validation for 'Email' failed on the 'required' tag"),
		},
		{
			name: "should validate fail when name is empty",
			input: dto.InviteUserInput{
				Email: "<EMAIL>",
				Name:  "",
				Role:  enums.UserRole_User,
			},
			mockFn:        func(d *dependencies) {},
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusBadRequest,
			expectErr:     fmt.Errorf("Key: 'InviteUserInput.Name' Error:Field validation for 'Name' failed on the 'required' tag"),
		},
		{
			name: "should return forbidden when user_id is missing from context",
			input: dto.InviteUserInput{
				Email: "<EMAIL>",
				Name:  "huongbui",
				Role:  enums.UserRole_User,
			},
			mockFn:        func(d *dependencies) {},
			ctxValue:      nil,
			expStatusCode: http.StatusForbidden,
			expectErr:     errors.New("Forbidden"),
		},
		{
			name: "should invite user successfully",
			input: dto.InviteUserInput{
				Email: "<EMAIL>",
				Name:  "huongbui",
				Role:  enums.UserRole_User,
			},
			mockFn: func(d *dependencies) {
				d.usecase.On("InviteUser", mock.Anything, mock.Anything).Return(nil)
			},
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusOK,
			expMessage:    "User has been invited",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Arrange
			dep := dependencies{
				usecase: &mocks.MockSignupRequestUsecase{},
			}
			tt.mockFn(&dep)
			h := handlers.NewSignUpRequestHandler(dep.usecase)
			w := httptest.NewRecorder()
			ctx, _ := gin.CreateTestContext(w)
			if tt.ctxValue != nil {
				ctx.Set("user_id", tt.ctxValue)
			}
			inputJSON, _ := json.Marshal(tt.input)
			ctx.Request = httptest.NewRequest(http.MethodPost, "/admin/invite", strings.NewReader(string(inputJSON)))
			ctx.Request.Header.Set("Content-Type", "application/json")

			// Act
			h.InviteUser(ctx)

			// Assert
			assert.Equal(t, tt.expStatusCode, w.Code)

			if tt.expectErr != nil {
				var resp dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &resp)
				assert.NoError(t, err)
				assert.Contains(t, resp.Message, tt.expectErr.Error())
			} else if tt.expMessage != "" {
				var resp map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &resp)
				assert.NoError(t, err)
				assert.Equal(t, tt.expMessage, resp["message"])
			}
		})
	}
}

func TestSendConfirmEmail(t *testing.T) {
	gin.SetMode(gin.TestMode)

	type dependencies struct {
		usecase *mocks.MockSignupRequestUsecase
	}

	tests := []struct {
		name          string
		input         dto.SendConfirmEmailInput
		mockFn        func(d *dependencies)
		expectErr     error
		expStatusCode int
		expMessage    string
	}{
		{
			name: "should return error when SendConfirmEmail returns error",
			input: dto.SendConfirmEmailInput{
				Email: "<EMAIL>",
			},
			mockFn: func(d *dependencies) {
				d.usecase.On("SendConfirmEmail", mock.Anything, mock.Anything).Return(errors.New("failed to send email"))
			},
			expStatusCode: http.StatusInternalServerError,
			expectErr:     errors.New("Failed to send email"),
		},
		{
			name: "should validate fail when email is empty",
			input: dto.SendConfirmEmailInput{
				Email: "",
			},
			mockFn:        func(d *dependencies) {},
			expStatusCode: http.StatusBadRequest,
			expectErr:     fmt.Errorf("Key: 'SendConfirmEmailInput.Email' Error:Field validation for 'Email' failed on the 'required' tag"),
		},
		{
			name: "should validate fail when email is invalid",
			input: dto.SendConfirmEmailInput{
				Email: "invalid-email",
			},
			mockFn:        func(d *dependencies) {},
			expStatusCode: http.StatusBadRequest,
			expectErr:     fmt.Errorf("Key: 'SendConfirmEmailInput.Email' Error:Field validation for 'Email' failed on the 'email' tag"),
		},
		{
			name: "should send confirmation email successfully",
			input: dto.SendConfirmEmailInput{
				Email: "<EMAIL>",
			},
			mockFn: func(d *dependencies) {
				d.usecase.On("SendConfirmEmail", mock.Anything, mock.Anything).Return(nil)
			},
			expStatusCode: http.StatusOK,
			expMessage:    "Email has been sent",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Arrange
			dep := dependencies{
				usecase: &mocks.MockSignupRequestUsecase{},
			}
			tt.mockFn(&dep)
			h := handlers.NewSignUpRequestHandler(dep.usecase)
			w := httptest.NewRecorder()
			ctx, _ := gin.CreateTestContext(w)
			inputJSON, _ := json.Marshal(tt.input)
			ctx.Request = httptest.NewRequest(http.MethodPost, "/send-confirm-email", strings.NewReader(string(inputJSON)))
			ctx.Request.Header.Set("Content-Type", "application/json")

			// Act
			h.SendConfirmEmail(ctx)

			// Assert
			assert.Equal(t, tt.expStatusCode, w.Code)

			if tt.expectErr != nil {
				var resp dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &resp)
				assert.NoError(t, err)
				assert.Contains(t, resp.Message, tt.expectErr.Error())
			} else if tt.expMessage != "" {
				var resp map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &resp)
				assert.NoError(t, err)
				assert.Equal(t, tt.expMessage, resp["message"])
			}
		})
	}
}
