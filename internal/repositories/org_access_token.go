package repository

import (
	"context"
	"strings"
	"time"

	"github.com/google/uuid"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"

	"api-server/internal/entities"
	"api-server/internal/enums"
	"api-server/internal/types"
	"api-server/pkg/oteltrace"
	"api-server/pkg/otelzap"
)

// OrgAccessTokenRepository defines the interface for organization access token operations.
// It provides methods for managing organization access tokens, including creating,
// finding, listing, and removing tokens.
type OrgAccessTokenRepository interface {
	// CreateOrgAccessToken creates a new access token for an organization.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - in: Input data for creating the access token
	//
	// Returns:
	//   - *entities.OrgToken: The created access token if successful
	//   - error: nil if successful, otherwise the error that occurred
	CreateOrgAccessToken(ctx context.Context, in CreateOrgAccessTokenInput) (*entities.OrgToken, error)

	// FindOrgAccessToken retrieves a specific organization access token.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - in: Query parameters for finding the access token
	//
	// Returns:
	//   - *entities.OrgToken: The found access token if successful
	//   - error: nil if successful, otherwise the error that occurred
	FindOrgAccessToken(ctx context.Context, in FindOrgAccessTokenQuery) (*entities.OrgToken, error)

	// ListOrgAccessToken retrieves a paginated list of organization access tokens.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - pagination: Pagination parameters
	//   - order: Ordering parameters
	//   - in: Query parameters for filtering tokens
	//
	// Returns:
	//   - []entities.OrgToken: List of access tokens
	//   - error: nil if successful, otherwise the error that occurred
	ListOrgAccessToken(ctx context.Context, pagination types.Pagination, order types.OrderBy, in ListOrgAccessTokenQuery) ([]entities.OrgToken, error)

	// CountOrgAccessToken counts the total number of organization access tokens.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - in: Query parameters for filtering tokens
	//
	// Returns:
	//   - int64: Total number of tokens
	//   - error: nil if successful, otherwise the error that occurred
	CountOrgAccessToken(ctx context.Context, in ListOrgAccessTokenQuery) (int64, error)

	// DeleteOrgAccessToken removes a specific organization access token.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - in: Input data for deleting the access token
	//
	// Returns:
	//   - error: nil if successful, otherwise the error that occurred
	DeleteOrgAccessToken(ctx context.Context, in DeleteOrgAccessTokenInput) error

	// DeleteAllUserOrgAccessToken removes all access tokens for an organization.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - orgId: ID of the organization
	//
	// Returns:
	//   - error: nil if successful, otherwise the error that occurred
	DeleteAllUserOrgAccessToken(ctx context.Context, orgId uuid.UUID) error
}

var _ OrgAccessTokenRepository = (*repository)(nil)

// CreateOrgAccessTokenInput contains the data needed to create a new organization access token.
type CreateOrgAccessTokenInput struct {
	Name           string
	OrgAccessToken string
	Scopes         []enums.RepoAccessTokenScope
	OrgID          uuid.UUID
	ExpiresAt      *time.Time
}

// CreateOrgAccessToken creates a new access token for an organization.
func (r *repository) CreateOrgAccessToken(ctx context.Context, in CreateOrgAccessTokenInput) (*entities.OrgToken, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.access_token.CreateOrgAccessToken",
		trace.WithAttributes(
			attribute.String("action", "CREATE_ACCESS_TOKEN"),
			attribute.String("org_id", in.OrgID.String()),
		))
	defer span.End()

	otelzap.InfoWithContext(ctx, "creating access token",
		zap.String("action", "CREATE_ACCESS_TOKEN"),
		zap.String("org_id", in.OrgID.String()))

	stringScopes := make([]string, len(in.Scopes))
	for i, scope := range in.Scopes {
		stringScopes[i] = string(scope)
	}

	data := entities.OrgToken{
		Name:        in.Name,
		AccessToken: in.OrgAccessToken,
		Scopes:      strings.Join(stringScopes, ","),
		OrgID:       in.OrgID,
		ExpiresAt:   in.ExpiresAt,
	}

	err := r.GetDB(ctx).Create(&data).Error
	if err != nil {
		span.SetStatus(codes.Error, "failed to create access token")
		span.RecordError(err)
		otelzap.ErrorWithContext(ctx, "failed to create access token", err,
			zap.String("action", "CREATE_ACCESS_TOKEN"),
			zap.String("org_id", in.OrgID.String()),
			zap.String("status", "failed"))
		return nil, err
	}

	span.AddEvent("access token created successfully")
	span.SetStatus(codes.Ok, "access token created successfully")
	span.SetAttributes(attribute.String("status", "success"))
	otelzap.InfoWithContext(ctx, "access token created successfully",
		zap.String("action", "CREATE_ACCESS_TOKEN"),
		zap.String("org_id", in.OrgID.String()),
		zap.String("status", "success"))
	return &data, nil
}

// FindOrgAccessTokenQuery contains the criteria for finding an organization access token.
type FindOrgAccessTokenQuery struct {
	OrgAccessToken string
	Revoked        bool
}

// FindOrgAccessToken retrieves a specific organization access token.
func (r *repository) FindOrgAccessToken(ctx context.Context, in FindOrgAccessTokenQuery) (*entities.OrgToken, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.access_token.FindOrgAccessToken",
		trace.WithAttributes(
			attribute.String("action", "FIND_ACCESS_TOKEN"),
			attribute.String("access_token", in.OrgAccessToken),
		))
	defer span.End()

	otelzap.InfoWithContext(ctx, "finding access token",
		zap.String("action", "FIND_ACCESS_TOKEN"),
		zap.String("access_token", in.OrgAccessToken))

	var result entities.OrgToken
	predicate := entities.OrgToken{
		AccessToken: in.OrgAccessToken,
		Revoked:     in.Revoked,
	}
	err := r.GetDB(ctx).Where(&predicate).First(&result).Error
	if err != nil {
		span.SetStatus(codes.Error, "failed to find access token")
		span.RecordError(err)
		otelzap.ErrorWithContext(ctx, "failed to find access token", err,
			zap.String("action", "FIND_ACCESS_TOKEN"),
			zap.String("access_token", in.OrgAccessToken),
			zap.String("status", "failed"))
		return nil, err
	}

	span.AddEvent("access token found successfully")
	span.SetStatus(codes.Ok, "access token found successfully")
	span.SetAttributes(attribute.String("status", "success"))
	otelzap.InfoWithContext(ctx, "access token found successfully",
		zap.String("action", "FIND_ACCESS_TOKEN"),
		zap.String("access_token", in.OrgAccessToken),
		zap.String("status", "success"))
	return &result, nil
}

// ListOrgAccessTokenQuery contains the criteria for listing organization access tokens.
type ListOrgAccessTokenQuery struct {
	OrgID uuid.UUID
}

// ListOrgAccessToken retrieves a paginated list of organization access tokens.
func (r *repository) ListOrgAccessToken(ctx context.Context, pagination types.Pagination, order types.OrderBy, in ListOrgAccessTokenQuery) ([]entities.OrgToken, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.access_token.ListOrgAccessToken",
		trace.WithAttributes(
			attribute.String("action", "LIST_ACCESS_TOKENS"),
			attribute.String("org_id", in.OrgID.String()),
		))
	defer span.End()

	otelzap.InfoWithContext(ctx, "listing access tokens",
		zap.String("action", "LIST_ACCESS_TOKENS"),
		zap.String("org_id", in.OrgID.String()))

	var result []entities.OrgToken
	predicate := entities.OrgToken{
		OrgID: in.OrgID,
	}
	err := r.
		GetDB(ctx).
		Scopes(paginate(pagination.PageNo, pagination.PageSize), orderBy(order)).
		Where(&predicate).
		Find(&result).Error
	if err != nil {
		span.SetStatus(codes.Error, "failed to list access tokens")
		span.RecordError(err)
		otelzap.ErrorWithContext(ctx, "failed to list access tokens", err,
			zap.String("action", "LIST_ACCESS_TOKENS"),
			zap.String("org_id", in.OrgID.String()),
			zap.String("status", "failed"))
		return nil, err
	}

	span.AddEvent("access tokens listed successfully")
	span.SetStatus(codes.Ok, "access tokens listed successfully")
	span.SetAttributes(attribute.String("status", "success"))
	otelzap.InfoWithContext(ctx, "access tokens listed successfully",
		zap.String("action", "LIST_ACCESS_TOKENS"),
		zap.String("org_id", in.OrgID.String()),
		zap.String("status", "success"))
	return result, nil
}

// CountOrgAccessToken counts the total number of organization access tokens.
func (r *repository) CountOrgAccessToken(ctx context.Context, in ListOrgAccessTokenQuery) (int64, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.access_token.CountOrgAccessToken",
		trace.WithAttributes(
			attribute.String("action", "COUNT_ACCESS_TOKENS"),
			attribute.String("org_id", in.OrgID.String()),
		))
	defer span.End()

	otelzap.InfoWithContext(ctx, "counting access tokens",
		zap.String("action", "COUNT_ACCESS_TOKENS"),
		zap.String("org_id", in.OrgID.String()))

	var result int64
	predicate := entities.OrgToken{
		OrgID: in.OrgID,
	}
	err := r.GetDB(ctx).Model(&entities.OrgToken{}).Where(&predicate).Count(&result).Error
	if err != nil {
		span.SetStatus(codes.Error, "failed to count access tokens")
		span.RecordError(err)
		otelzap.ErrorWithContext(ctx, "failed to count access tokens", err,
			zap.String("action", "COUNT_ACCESS_TOKENS"),
			zap.String("org_id", in.OrgID.String()),
			zap.String("status", "failed"))
		return 0, err
	}

	span.AddEvent("access tokens counted successfully")
	span.SetStatus(codes.Ok, "access tokens counted successfully")
	span.SetAttributes(attribute.String("status", "success"))
	otelzap.InfoWithContext(ctx, "access tokens counted successfully",
		zap.String("action", "COUNT_ACCESS_TOKENS"),
		zap.String("org_id", in.OrgID.String()),
		zap.String("status", "success"))
	return result, nil
}

// DeleteOrgAccessTokenInput contains the data needed to delete an organization access token.
type DeleteOrgAccessTokenInput struct {
	OrgAccessTokenID uuid.UUID
	OrgID            uuid.UUID
}

// DeleteOrgAccessToken removes a specific organization access token.
func (r *repository) DeleteOrgAccessToken(ctx context.Context, in DeleteOrgAccessTokenInput) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.access_token.DeleteOrgAccessToken",
		trace.WithAttributes(
			attribute.String("action", "DELETE_ACCESS_TOKEN"),
			attribute.String("access_token_id", in.OrgAccessTokenID.String()),
			attribute.String("org_id", in.OrgID.String()),
		))
	defer span.End()

	otelzap.InfoWithContext(ctx, "deleting access token",
		zap.String("action", "DELETE_ACCESS_TOKEN"),
		zap.String("access_token_id", in.OrgAccessTokenID.String()),
		zap.String("org_id", in.OrgID.String()))

	token := entities.OrgToken{
		BaseModel: entities.BaseModel{
			ID: in.OrgAccessTokenID,
		},
		OrgID: in.OrgID,
	}
	err := r.GetDB(ctx).Delete(&token).Error
	if err != nil {
		span.SetStatus(codes.Error, "failed to delete access token")
		span.RecordError(err)
		otelzap.ErrorWithContext(ctx, "failed to delete access token", err,
			zap.String("action", "DELETE_ACCESS_TOKEN"),
			zap.String("access_token_id", in.OrgAccessTokenID.String()),
			zap.String("org_id", in.OrgID.String()),
			zap.String("status", "failed"))
		return err
	}

	span.AddEvent("access token deleted successfully")
	span.SetStatus(codes.Ok, "access token deleted successfully")
	span.SetAttributes(attribute.String("status", "success"))
	otelzap.InfoWithContext(ctx, "access token deleted successfully",
		zap.String("action", "DELETE_ACCESS_TOKEN"),
		zap.String("access_token_id", in.OrgAccessTokenID.String()),
		zap.String("org_id", in.OrgID.String()),
		zap.String("status", "success"))
	return nil
}

// DeleteAllUserOrgAccessToken removes all access tokens for an organization.
func (r *repository) DeleteAllUserOrgAccessToken(ctx context.Context, orgId uuid.UUID) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.access_token.DeleteAllUserOrgAccessToken",
		trace.WithAttributes(
			attribute.String("action", "DELETE_ALL_USER_ACCESS_TOKENS"),
			attribute.String("org_id", orgId.String()),
		))
	defer span.End()

	otelzap.InfoWithContext(ctx, "deleting all org access tokens",
		zap.String("action", "DELETE_ALL_USER_ACCESS_TOKENS"),
		zap.String("org_id", orgId.String()))

	err := r.GetDB(ctx).Delete(&entities.OrgToken{}, "org_id = ?", orgId).Error
	if err != nil {
		span.SetStatus(codes.Error, "failed to delete org all access tokens")
		span.RecordError(err)
		otelzap.ErrorWithContext(ctx, "failed to delete org all access tokens", err,
			zap.String("action", "DELETE_ALL_USER_ACCESS_TOKENS"),
			zap.String("org_id", orgId.String()),
			zap.String("status", "failed"))
		return err
	}

	span.AddEvent("all access tokens deleted successfully for org")
	span.SetStatus(codes.Ok, "all access tokens deleted successfully for org")
	span.SetAttributes(attribute.String("status", "success"))
	otelzap.InfoWithContext(ctx, "all access tokens deleted successfully for org",
		zap.String("action", "DELETE_ALL_USER_ACCESS_TOKENS"),
		zap.String("org_id", orgId.String()),
		zap.String("status", "success"))
	return nil
}
