package repository

import (
	"api-server/internal/entities"
	"api-server/pkg/oteltrace"
	"context"

	"github.com/google/uuid"
	"go.opentelemetry.io/otel/codes"
)

// UserGitGroupRepository defines the interface for user Gitlab group operations.
// Using Gitlab group to represent user namespace
// It provides methods for managing user git groups, including creating,
// finding, and removing git groups.
type UserGitGroupRepository interface {
	// CreateUserGitGroup creates a new git group for a user.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - in: Input data containing git group details
	//
	// Returns:
	//   - *entities.UserGitGroup: The created git group if successful
	//   - error: nil if successful, otherwise the error that occurred
	CreateUserGitGroup(ctx context.Context, in CreateUserGitGroupInput) (*entities.UserGitGroup, error)

	// UpdateUserGitGroup updates an existing user's git group.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - in: Input data containing git group details
	//
	// Returns:
	//   - *entities.UserGitGroup: The updated git group if successful
	//   - error: nil if successful, otherwise the error that occurred
	UpdateUserGitGroup(ctx context.Context, in UpdateUserGitGroupInput) (*entities.UserGitGroup, error)

	// FindUserGitGroup retrieves a specific user's git group.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - userID: UUID of the user
	//
	// Returns:
	//   - *entities.DefaultGitGroup: The found git group if successful
	//   - error: nil if successful, otherwise the error that occurred
	FindUserGitGroup(ctx context.Context, userID uuid.UUID) (*entities.DefaultGitGroup, error)

	// DeleteUserGitGroup removes a user's git group.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - userID: UUID of the user
	//
	// Returns:
	//   - error: nil if successful, otherwise the error that occurred
	DeleteUserGitGroup(ctx context.Context, userID uuid.UUID) error
}

var _ UserGitGroupRepository = (*repository)(nil)

// CreateUserGitGroupInput contains the data needed to create a new user git group.
type CreateUserGitGroupInput struct {
	UserID           uuid.UUID
	RefGitModelsID   int64
	RefGitSpacesID   int64
	RefGitDatasetsID int64
	RefGitComposesID *int64
}

func (r *repository) CreateUserGitGroup(ctx context.Context, in CreateUserGitGroupInput) (*entities.UserGitGroup, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.user_git_groups.CreateUserGitGroup")
	defer span.End()

	data := entities.UserGitGroup{
		UserID: in.UserID,
		DefaultGitGroup: entities.DefaultGitGroup{
			RefGitModelsID:   in.RefGitModelsID,
			RefGitSpacesID:   in.RefGitSpacesID,
			RefGitDatasetsID: in.RefGitDatasetsID,
			RefGitComposesID: in.RefGitComposesID,
		},
	}

	err := r.GetDB(ctx).Create(&data).Error
	if err != nil {
		span.SetStatus(codes.Error, "failed to create user git group")
		span.RecordError(err)
		return nil, err
	}

	span.AddEvent("user git group created successfully")
	span.SetStatus(codes.Ok, "user git group created successfully")
	return &data, nil
}

type UpdateUserGitGroupInput struct {
	UserID           uuid.UUID
	RefGitModelsID   *int64
	RefGitSpacesID   *int64
	RefGitDatasetsID *int64
	RefGitComposesID *int64
}

func (r *repository) UpdateUserGitGroup(ctx context.Context, in UpdateUserGitGroupInput) (*entities.UserGitGroup, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.user_git_groups.UpdateUserGitGroup")
	defer span.End()

	data := entities.UserGitGroup{
		UserID: in.UserID,
		// DefaultGitGroup: entities.DefaultGitGroup{},
	}

	if in.RefGitModelsID != nil {
		data.RefGitModelsID = *in.RefGitModelsID
	}
	if in.RefGitSpacesID != nil {
		data.RefGitSpacesID = *in.RefGitSpacesID
	}
	if in.RefGitDatasetsID != nil {
		data.RefGitDatasetsID = *in.RefGitDatasetsID
	}
	if in.RefGitComposesID != nil {
		data.RefGitComposesID = in.RefGitComposesID
	}

	err := r.GetDB(ctx).Where("user_id = ?", in.UserID).Updates(&data).Error
	if err != nil {
		span.SetStatus(codes.Error, "failed to update user git group")
		span.RecordError(err)
		return nil, err
	}

	span.AddEvent("user git group updated successfully")
	span.SetStatus(codes.Ok, "user git group updated successfully")
	return &data, nil
}

func (r *repository) FindUserGitGroup(ctx context.Context, userID uuid.UUID) (*entities.DefaultGitGroup, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.user_git_groups.FindUserGitGroup")
	defer span.End()

	var result entities.DefaultGitGroup
	predicate := entities.UserGitGroup{
		UserID: userID,
	}
	err := r.GetDB(ctx).Table("user_git_groups").Where(&predicate).First(&result).Error
	if err != nil {
		span.SetStatus(codes.Error, "failed to find user git group")
		span.RecordError(err)
		return nil, err
	}

	span.AddEvent("user git group retrieved successfully")
	span.SetStatus(codes.Ok, "user git group retrieved successfully")
	return &result, nil
}

func (r *repository) DeleteUserGitGroup(ctx context.Context, userID uuid.UUID) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.user_git_groups.DeleteUserGitGroup")
	defer span.End()

	err := r.GetDB(ctx).Where("user_id = ?", userID).Delete(&entities.UserGitGroup{}).Error
	if err != nil {
		span.SetStatus(codes.Error, "failed to delete user git group")
		span.RecordError(err)
		return err
	}

	span.AddEvent("user git group deleted successfully")
	span.SetStatus(codes.Ok, "user git group deleted successfully")
	return nil
}
