package repository

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"

	"api-server/internal/dto"
	"api-server/internal/entities"
	"api-server/internal/enums"
	"api-server/internal/types"
	"api-server/pkg/oteltrace"

	"gorm.io/gorm"
)

// UserFilters contains filtering criteria for user operations.
type UserFilters struct{}

// UserRepository defines the interface for user operations.
// It provides methods for managing users, including retrieving permissions,
// listing users, finding users, and managing user data.
type UserRepository interface {
	// GetAppPermission retrieves application permissions for a user.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - userID: UUID of the user
	//
	// Returns:
	//   - []entities.AppPermission: List of application permissions
	//   - error: nil if successful, otherwise the error that occurred
	GetAppPermission(ctx context.Context, userID uuid.UUID) ([]entities.AppPermission, error)

	// GetOrgPermission retrieves organization permissions for a user.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - userID: UUID of the user
	//   - orgID: UUID of the organization
	//
	// Returns:
	//   - []entities.RepoPermission: List of repository permissions
	//   - error: nil if successful, otherwise the error that occurred
	GetOrgPermission(ctx context.Context, userID uuid.UUID, orgID uuid.UUID) ([]entities.RepoPermission, error)

	// GetRepoPermission retrieves repository permissions for a user.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - userID: UUID of the user
	//   - repoID: UUID of the repository
	//
	// Returns:
	//   - []entities.RepoPermission: List of repository permissions
	//   - error: nil if successful, otherwise the error that occurred
	GetRepoPermission(ctx context.Context, userID uuid.UUID, repoID uuid.UUID) ([]entities.RepoPermission, error)

	// CountUsers counts the total number of users based on filter criteria.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - input: Filter criteria for counting users
	//
	// Returns:
	//   - int64: Total number of users
	//   - error: nil if successful, otherwise the error that occurred
	CountUsers(ctx context.Context, input ListUsersInput) (int64, error)

	// ListUsers retrieves a list of users based on filter criteria.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - input: Filter criteria for listing users
	//
	// Returns:
	//   - []entities.User: List of users
	//   - error: nil if successful, otherwise the error that occurred
	ListUsers(ctx context.Context, input ListUsersInput) ([]entities.User, error)

	// FindUserByID retrieves a specific user by their ID.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - id: UUID of the user to retrieve
	//
	// Returns:
	//   - *entities.User: The found user if successful
	//   - error: nil if successful, otherwise the error that occurred
	FindUserByID(ctx context.Context, id uuid.UUID) (*entities.User, error)

	// FindAuthUser retrieves an authenticated user based on filter criteria.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - filters: Filter criteria for finding the user
	//
	// Returns:
	//   - *entities.AuthUser: The found authenticated user if successful
	//   - error: nil if successful, otherwise the error that occurred
	FindAuthUser(ctx context.Context, filters dto.AuthUserFilter) (*entities.AuthUser, error)

	// FindUser retrieves a user based on filter criteria.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - filters: Filter criteria for finding the user
	//
	// Returns:
	//   - *entities.User: The found user if successful
	//   - error: nil if successful, otherwise the error that occurred
	FindUser(ctx context.Context, filters FindUserFilter) (*entities.User, error)

	// DeleteUser removes a user from the system.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - userID: UUID of the user to delete
	//
	// Returns:
	//   - error: nil if successful, otherwise the error that occurred
	DeleteUser(ctx context.Context, userID uuid.UUID) error

	// CountPrefixUsername counts the number of usernames with a given prefix.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - prefixUsername: The username prefix to count
	//
	// Returns:
	//   - int: Number of matching usernames
	//   - error: nil if successful, otherwise the error that occurred
	CountPrefixUsername(ctx context.Context, prefixUsername string) (int, error)

	// CheckCurrentPassword verifies a user's current password.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - userID: UUID of the user
	//   - currentPassword: The password to verify
	//
	// Returns:
	//   - error: nil if successful, otherwise the error that occurred
	CheckCurrentPassword(ctx context.Context, userID uuid.UUID, currentPassword string) error
}

var _ UserRepository = (*repository)(nil)

func (r repository) FindUserByID(ctx context.Context, id uuid.UUID) (*entities.User, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.user.FindUserByID")
	defer span.End()

	userID := &id
	user, err := r.FindUser(ctx, FindUserFilter{Id: userID})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find user")
		span.RecordError(err)
		return nil, err
	}
	span.AddEvent("user retrieved successfully")
	span.SetStatus(codes.Ok, "user retrieved successfully")
	return user, nil
}

func (r *repository) GetAppPermission(ctx context.Context, userID uuid.UUID) ([]entities.AppPermission, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.user.GetAppPermission")
	defer span.End()

	var userAppRole entities.User
	var result []entities.AppPermission

	err := r.
		GetDB(ctx).
		Where(&entities.User{
			BaseModel: entities.BaseModel{
				ID: userID,
			},
		}).
		First(&userAppRole).Error
	if err != nil {
		span.SetStatus(codes.Error, "failed to get user app role")
		span.RecordError(err)
		return nil, err
	}

	err = r.
		GetDB(ctx).
		Where(&entities.AppPermission{
			Role: userAppRole.Role,
		}).
		Find(&result).Error
	if err != nil {
		span.SetStatus(codes.Error, "failed to get app permission")
		span.RecordError(err)
		return nil, err
	}

	span.AddEvent("app permissions retrieved successfully")
	span.SetStatus(codes.Ok, "app permissions retrieved successfully")
	return result, nil
}

func (r *repository) GetOrgPermission(ctx context.Context, userID uuid.UUID, orgID uuid.UUID) ([]entities.RepoPermission, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.user.GetOrgPermission")
	defer span.End()

	var orgMember entities.OrgMember
	var result []entities.RepoPermission

	err := r.
		GetDB(ctx).
		Where(&entities.OrgMember{
			UserID: userID,
			OrgID:  orgID,
		}).
		First(&orgMember).Error
	if err != nil {
		span.SetStatus(codes.Error, "failed to get organization member")
		span.RecordError(err)
		return nil, err
	}

	var repoRole enums.RepoRole
	switch orgMember.Role {
	case enums.OrgRole_Owner:
		repoRole = enums.RepoRole_Owner
	case enums.OrgRole_Developer:
		repoRole = enums.RepoRole_Developer
	}

	err = r.
		GetDB(ctx).
		Where(&entities.RepoPermission{
			Role: repoRole,
		}).
		Find(&result).Error
	if err != nil {
		span.SetStatus(codes.Error, "failed to get repo permission")
		span.RecordError(err)
		return nil, err
	}

	span.AddEvent("organization permissions retrieved successfully")
	span.SetStatus(codes.Ok, "organization permissions retrieved successfully")
	return result, nil
}

func (r *repository) GetRepoPermission(ctx context.Context, userID uuid.UUID, repoID uuid.UUID) ([]entities.RepoPermission, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.user.GetRepoPermission")
	defer span.End()

	var repoMember entities.RepoMember
	var result []entities.RepoPermission

	err := r.
		GetDB(ctx).
		Where(&entities.RepoMember{
			UserID: userID,
			RepoID: repoID,
		}).
		First(&repoMember).Error
	if err != nil {
		span.SetStatus(codes.Error, "failed to get repository member")
		span.RecordError(err)
		return nil, err
	}

	err = r.
		GetDB(ctx).
		Where(&entities.RepoPermission{
			Role: repoMember.Role,
		}).
		Find(&result).Error
	if err != nil {
		span.SetStatus(codes.Error, "failed to get repo permission")
		span.RecordError(err)
		return nil, err
	}

	span.AddEvent("repository permissions retrieved successfully")
	span.SetStatus(codes.Ok, "repository permissions retrieved successfully")
	return result, nil
}

// buildUserQuery builds the base query for user operations
func (r repository) buildUserQuery(ctx context.Context, input ListUsersInput) *gorm.DB {
	query := r.GetDB(ctx).Table("users").Select("users.*, au.email AS email, au.email_confirmed_at AS email_confirmed_at").
		Joins("LEFT JOIN auth.users au ON users.id = au.id").Where("users.ref_git_userid != ?", enums.AdminRefGitID)

	if input.Keyword != "" {
		keyword := fmt.Sprintf("%%%s%%", input.Keyword)
		query = query.Where("users.name ILIKE ? OR users.username ILIKE ? OR au.email ILIKE ?", keyword, keyword, keyword)
	}

	if input.Except.NotInRepId != nil {
		query = query.Joins("LEFT JOIN repo_members rm ON users.id = rm.user_id AND rm.repo_id = ?", *input.Except.NotInRepId).
			Where("rm.id IS NULL")
	}

	if input.Except.NotInOrgId != nil {
		query = query.Joins("LEFT JOIN org_members om ON users.id = om.user_id AND om.org_id = ?", *input.Except.NotInOrgId).
			Where("om.id IS NULL")
	}

	return query
}

// applyPagination applies pagination and sorting to the query
func applyPagination(query *gorm.DB, input ListUsersInput) *gorm.DB {
	if input.Paginate.Page != 0 && input.Paginate.PerPage != 0 {
		query = query.Scopes(paginate(input.Paginate.Page, input.Paginate.PerPage))
	}

	if input.Paginate.OrderBy != "" && input.Paginate.Sort != "" {
		query = query.Scopes(orderBy(types.OrderBy{input.Paginate.OrderBy: input.Paginate.Sort}))
	}

	return query
}

// ListUsersInput contains the criteria for listing users.
type ListUsersInput struct {
	Paginate PaginateRequest
	Keyword  string
	Except   ExceptFilter
}

// ExceptFilter contains exclusion criteria for user listing.
type ExceptFilter struct {
	NotInRepId *uuid.UUID
	NotInOrgId *uuid.UUID
}

func (r repository) CountUsers(ctx context.Context, input ListUsersInput) (int64, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.user.CountUsers")
	defer span.End()

	var count int64
	query := r.buildUserQuery(ctx, input)

	err := query.Count(&count).Error
	if err != nil {
		span.SetStatus(codes.Error, "failed to count users")
		span.RecordError(err)
		return 0, err
	}

	span.AddEvent("users counted successfully")
	span.SetStatus(codes.Ok, "users counted successfully")
	return count, nil
}

func (r repository) ListUsers(ctx context.Context, input ListUsersInput) ([]entities.User, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.user.ListUsers")
	defer span.End()

	query := r.buildUserQuery(ctx, input)
	query = applyPagination(query, input)

	var users []entities.UserInfo
	err := query.Find(&users).Error
	if err != nil {
		span.SetStatus(codes.Error, "failed to list users")
		span.RecordError(err)
		return nil, err
	}

	var us []entities.User
	for _, u := range users {
		us = append(us, u.ToEntity())
	}

	span.AddEvent("users listed successfully")
	span.SetStatus(codes.Ok, "users listed successfully")
	return us, nil
}

func (r repository) FindAuthUser(ctx context.Context, filters dto.AuthUserFilter) (*entities.AuthUser, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.user.FindAuthUser")
	defer span.End()

	var user entities.AuthUser
	query := r.GetDB(ctx).Table("auth.users")

	if filters.Id != nil {
		query = query.Where("id = ?", *filters.Id)
	}

	if filters.Email != nil {
		query = query.Where("email = ?", *filters.Email)
	}

	err := query.First(&user).Error
	if err != nil {
		span.SetStatus(codes.Error, "failed to find auth user")
		span.RecordError(err)
		return nil, err
	}

	span.AddEvent("auth user retrieved successfully")
	span.SetStatus(codes.Ok, "auth user retrieved successfully")
	return &user, nil
}

// FindUserFilter contains the criteria for finding a user.
type FindUserFilter struct {
	Id           *uuid.UUID `json:"id"`
	RefGitUserID *int64     `json:"ref_git_userid"`
	Email        *string    `json:"email"`
}

func (r repository) FindUser(ctx context.Context, filters FindUserFilter) (*entities.User, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.user.FindUser")
	defer span.End()

	var user entities.UserInfo
	query := r.GetDB(ctx).Select("users.*, au.email AS email, au.email_confirmed_at AS email_confirmed_at").Joins("LEFT JOIN auth.users au ON users.id = au.id")
	if filters.Id != nil {
		query = query.Where("users.id = ?", *filters.Id)
	}

	if filters.Email != nil {
		query = query.Where("au.email = ?", *filters.Email)
	}

	if filters.RefGitUserID != nil {
		query = query.Where("users.ref_git_userid = ?", *filters.RefGitUserID)
	}

	err := query.First(&user).Error
	if err != nil {
		span.SetStatus(codes.Error, "failed to find user")
		span.RecordError(err)
		return nil, err
	}

	u := user.ToEntity()
	span.AddEvent("user retrieved successfully")
	span.SetStatus(codes.Ok, "user retrieved successfully")
	return &u, nil
}

func (r repository) DeleteUser(ctx context.Context, userID uuid.UUID) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.user.DeleteUser")
	defer span.End()

	if err := r.GetDB(ctx).Where("id = ?", userID).Delete(&entities.User{}).Error; err != nil {
		span.SetStatus(codes.Error, "failed to delete user")
		span.RecordError(err)
		return err
	}

	span.AddEvent("user deleted successfully")
	span.SetStatus(codes.Ok, "user deleted successfully")
	return nil
}

func (r repository) CountPrefixUsername(ctx context.Context, username string) (int, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.user.CountPrefixUsername")
	defer span.End()

	var count int64
	query := r.GetDB(ctx).Table("users").Where("users.username = ?", username)

	err := query.Count(&count).Error
	if err != nil {
		span.SetStatus(codes.Error, "failed to count prefix username")
		span.RecordError(err)
		return 0, err
	}

	span.AddEvent("prefix username counted successfully")
	span.SetStatus(codes.Ok, "prefix username counted successfully")
	return int(count), nil
}

func (r repository) CheckCurrentPassword(ctx context.Context, userID uuid.UUID, currentPassword string) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.user.CheckCurrentPassword")
	defer span.End()

	var result string
	err := r.GetDB(ctx).Raw("SELECT check_current_password(?, ?)", userID, currentPassword).Scan(&result).Error
	if err != nil {
		span.SetStatus(codes.Error, "failed to check current user password")
		span.RecordError(err)
		return err
	}

	span.AddEvent("check current user password successfully", trace.WithAttributes(attribute.String("result", result)))
	span.SetStatus(codes.Ok, "check current user password successfully")
	return nil
}
