package repository

import (
	"context"
	"testing"
	"time"

	sqlmock "github.com/DATA-DOG/go-sqlmock"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"

	"api-server/internal/entities"
	"api-server/internal/types"
)

func TestListHardware(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		hardware1 := entities.Hardware{
			BaseModel: entities.BaseModel{
				ID:        uuid.New(),
				CreatedAt: time.Now(),
				UpdatedAt: time.Now(),
			},
			Name:   "Test Hardware 1",
			CPU:    4,
			Mem:    8192,
			GPUMem: nil,
		}
		hardware2 := entities.Hardware{
			BaseModel: entities.BaseModel{
				ID:        uuid.New(),
				CreatedAt: time.Now(),
				UpdatedAt: time.Now(),
			},
			Name:   "Test Hardware 2",
			CPU:    8,
			Mem:    16384,
			GPUMem: nil,
		}

		// Setup mock expectations
		rows := sqlmock.NewRows([]string{"id", "created_at", "updated_at", "name", "num_cpu", "mem", "gpu_mem"}).
			AddRow(hardware1.ID, hardware1.CreatedAt, hardware1.UpdatedAt, hardware1.Name, hardware1.CPU, hardware1.Mem, hardware1.GPUMem).
			AddRow(hardware2.ID, hardware2.CreatedAt, hardware2.UpdatedAt, hardware2.Name, hardware2.CPU, hardware2.Mem, hardware2.GPUMem)

		mock.ExpectQuery(`SELECT \* FROM "hardwares" ORDER BY id asc LIMIT \$1`).
			WithArgs(10).
			WillReturnRows(rows)

		// Call the method
		pagination := types.Pagination{
			PageNo:   1,
			PageSize: 10,
		}
		order := types.OrderBy{
			"id": "asc",
		}
		result, err := repo.ListHardware(context.Background(), pagination, order)

		// Assertions
		assert.NoError(t, err)
		assert.Len(t, result, 2)
		assert.Equal(t, hardware1.ID, result[0].ID)
		assert.Equal(t, hardware1.Name, result[0].Name)
		assert.Equal(t, hardware1.CPU, result[0].CPU)
		assert.Equal(t, hardware1.Mem, result[0].Mem)
		assert.Equal(t, hardware2.ID, result[1].ID)
		assert.Equal(t, hardware2.Name, result[1].Name)
		assert.Equal(t, hardware2.CPU, result[1].CPU)
		assert.Equal(t, hardware2.Mem, result[1].Mem)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup mock expectations
		mock.ExpectQuery(`SELECT \* FROM "hardwares" ORDER BY id asc LIMIT \$1`).
			WithArgs(10).
			WillReturnError(gorm.ErrInvalidDB)

		// Call the method
		pagination := types.Pagination{
			PageNo:   1,
			PageSize: 10,
		}
		order := types.OrderBy{
			"id": "asc",
		}
		result, err := repo.ListHardware(context.Background(), pagination, order)

		// Assertions
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Equal(t, gorm.ErrInvalidDB, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestCountHardware(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup mock expectations
		rows := sqlmock.NewRows([]string{"count"}).
			AddRow(5)

		mock.ExpectQuery(`SELECT count\(\*\) FROM "hardwares"`).
			WillReturnRows(rows)

		// Call the method
		count, err := repo.CountHardware(context.Background())

		// Assertions
		assert.NoError(t, err)
		assert.Equal(t, int64(5), count)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup mock expectations
		mock.ExpectQuery(`SELECT count\(\*\) FROM "hardwares"`).
			WillReturnError(gorm.ErrInvalidDB)

		// Call the method
		count, err := repo.CountHardware(context.Background())

		// Assertions
		assert.Error(t, err)
		assert.Equal(t, int64(0), count)
		assert.Equal(t, gorm.ErrInvalidDB, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestFindHardwareByID(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		hardware := entities.Hardware{
			BaseModel: entities.BaseModel{
				ID:        uuid.New(),
				CreatedAt: time.Now(),
				UpdatedAt: time.Now(),
			},
			Name:   "Test Hardware",
			CPU:    4,
			Mem:    8192,
			GPUMem: nil,
		}

		// Setup mock expectations
		rows := sqlmock.NewRows([]string{"id", "created_at", "updated_at", "name", "num_cpu", "mem", "gpu_mem"}).
			AddRow(hardware.ID, hardware.CreatedAt, hardware.UpdatedAt, hardware.Name, hardware.CPU, hardware.Mem, hardware.GPUMem)

		mock.ExpectQuery(`SELECT \* FROM "hardwares" WHERE "hardwares"."id" = \$1 ORDER BY "hardwares"."id" LIMIT \$2`).
			WithArgs(hardware.ID, 1).
			WillReturnRows(rows)

		// Call the method
		result, err := repo.FindHardwareByID(context.Background(), hardware.ID)

		// Assertions
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, hardware.ID, result.ID)
		assert.Equal(t, hardware.Name, result.Name)
		assert.Equal(t, hardware.CPU, result.CPU)
		assert.Equal(t, hardware.Mem, result.Mem)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("not found", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		id := uuid.New()

		// Setup mock expectations
		mock.ExpectQuery(`SELECT \* FROM "hardwares" WHERE "hardwares"."id" = \$1 ORDER BY "hardwares"."id" LIMIT \$2`).
			WithArgs(id, 1).
			WillReturnError(gorm.ErrRecordNotFound)

		// Call the method
		result, err := repo.FindHardwareByID(context.Background(), id)

		// Assertions
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Equal(t, gorm.ErrRecordNotFound, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		id := uuid.New()

		// Setup mock expectations
		mock.ExpectQuery(`SELECT \* FROM "hardwares" WHERE "hardwares"."id" = \$1 ORDER BY "hardwares"."id" LIMIT \$2`).
			WithArgs(id, 1).
			WillReturnError(gorm.ErrInvalidDB)

		// Call the method
		result, err := repo.FindHardwareByID(context.Background(), id)

		// Assertions
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Equal(t, gorm.ErrInvalidDB, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}
