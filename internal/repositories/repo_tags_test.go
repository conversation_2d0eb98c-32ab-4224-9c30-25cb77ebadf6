package repository

import (
	"context"
	"testing"
	"time"

	sqlmock "github.com/DATA-DOG/go-sqlmock"
	"github.com/google/uuid"
	"github.com/lib/pq"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"

	"api-server/internal/dto"
)

func TestListRepoTags(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		tagID := uuid.New()
		createdAt := time.Now()
		updatedAt := time.Now()

		// Setup mock expectations
		rows := sqlmock.NewRows([]string{"id", "type", "sub_type", "value", "name", "icon_url", "query", "repo_types", "created_at", "updated_at"}).
			AddRow(tagID, "test-type", "test-subtype", "test-value", "test-name", "test-icon-url", "test-query", pq.StringArray{"spaces", "models"}, createdAt, updatedAt)

		// Main query - updated to match actual query
		mock.ExpectQuery(`SELECT \* FROM "tags" WHERE \(tags.name ILIKE \$1 OR tags.value ILIKE \$2\) AND tags.type = \$3 AND tags.sub_type = \$4 AND tags.repo_types @> \$5`).
			WithArgs("%test%", "%test%", "test-type", "test-subtype", `{"spaces"}`).
			WillReturnRows(rows)

		// Call the method
		input := dto.ListRepoTagsInput{
			Keyword:  "test",
			Type:     "test-type",
			SubType:  "test-subtype",
			RepoType: "spaces",
		}
		result, err := repo.ListRepoTags(context.Background(), input)

		// Assertions
		assert.NoError(t, err)
		assert.Len(t, result, 1)
		assert.Equal(t, tagID, result[0].ID)
		assert.Equal(t, "test-type", result[0].Type)
		assert.Equal(t, "test-subtype", result[0].SubType)
		assert.Equal(t, "test-value", result[0].Value)
		assert.Equal(t, "test-name", result[0].Name)
		assert.Equal(t, "test-icon-url", result[0].IconUrl)
		assert.Equal(t, "test-query", result[0].Query)
		assert.Equal(t, pq.StringArray{"spaces", "models"}, result[0].RepoTypes)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup mock expectations
		mock.ExpectQuery(`SELECT \* FROM "tags"`).
			WillReturnError(gorm.ErrInvalidDB)

		// Call the method
		input := dto.ListRepoTagsInput{}
		result, err := repo.ListRepoTags(context.Background(), input)

		// Assertions
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Equal(t, gorm.ErrInvalidDB, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestListRepoTagsByQueryModel(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		tagID := uuid.New()
		createdAt := time.Now()
		updatedAt := time.Now()

		// Setup mock expectations
		rows := sqlmock.NewRows([]string{"id", "type", "sub_type", "value", "name", "icon_url", "query", "repo_types", "created_at", "updated_at"}).
			AddRow(tagID, "test-type", "test-subtype", "test:value", "test-name", "test-icon-url", "test-query", pq.StringArray{"spaces", "models"}, createdAt, updatedAt)

		// Main query - updated to include ORDER BY and LIMIT
		mock.ExpectQuery(`SELECT \* FROM "tags" WHERE tags.value = \$1 ORDER BY "tags"."id" LIMIT \$2`).
			WithArgs("test:value", 1).
			WillReturnRows(rows)

		// Call the method
		input := map[string]any{
			"test": "value",
		}
		result, err := repo.ListRepoTagsByQueryModel(context.Background(), input)

		// Assertions
		assert.NoError(t, err)
		assert.Len(t, result, 1)
		assert.Equal(t, tagID, result[0].ID)
		assert.Equal(t, "test-type", result[0].Type)
		assert.Equal(t, "test-subtype", result[0].SubType)
		assert.Equal(t, "test:value", result[0].Value)
		assert.Equal(t, "test-name", result[0].Name)
		assert.Equal(t, "test-icon-url", result[0].IconUrl)
		assert.Equal(t, "test-query", result[0].Query)
		assert.Equal(t, pq.StringArray{"spaces", "models"}, result[0].RepoTypes)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup mock expectations
		mock.ExpectQuery(`SELECT \* FROM "tags" WHERE tags.value = \$1 ORDER BY "tags"."id" LIMIT \$2`).
			WithArgs("test:value", 1).
			WillReturnError(gorm.ErrInvalidDB)

		// Call the method
		input := map[string]any{
			"test": "value",
		}
		result, err := repo.ListRepoTagsByQueryModel(context.Background(), input)

		// Assertions
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Equal(t, gorm.ErrInvalidDB, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestFindRepoTag(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		tagID := uuid.New()
		createdAt := time.Now()
		updatedAt := time.Now()

		// Setup mock expectations
		rows := sqlmock.NewRows([]string{"id", "type", "sub_type", "value", "name", "icon_url", "query", "repo_types", "created_at", "updated_at"}).
			AddRow(tagID, "test-type", "test-subtype", "test-value", "test-name", "test-icon-url", "test-query", pq.StringArray{"spaces", "models"}, createdAt, updatedAt)

		// Main query - updated to include ORDER BY and LIMIT
		mock.ExpectQuery(`SELECT \* FROM "tags" WHERE id = \$1 ORDER BY "tags"."id" LIMIT \$2`).
			WithArgs(tagID, 1).
			WillReturnRows(rows)

		// Call the method
		filter := FilterTagInput{
			Id: tagID,
		}
		result, err := repo.FindRepoTag(context.Background(), filter)

		// Assertions
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, tagID, result.ID)
		assert.Equal(t, "test-type", result.Type)
		assert.Equal(t, "test-subtype", result.SubType)
		assert.Equal(t, "test-value", result.Value)
		assert.Equal(t, "test-name", result.Name)
		assert.Equal(t, "test-icon-url", result.IconUrl)
		assert.Equal(t, "test-query", result.Query)
		assert.Equal(t, pq.StringArray{"spaces", "models"}, result.RepoTypes)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("not found", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Generate a single UUID for the test case
		tagID := uuid.New()

		// Setup mock expectations
		mock.ExpectQuery(`SELECT \* FROM "tags" WHERE id = \$1 ORDER BY "tags"."id" LIMIT \$2`).
			WithArgs(tagID, 1).
			WillReturnError(gorm.ErrRecordNotFound)

		// Call the method
		filter := FilterTagInput{
			Id: tagID,
		}
		result, err := repo.FindRepoTag(context.Background(), filter)

		// Assertions
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Equal(t, gorm.ErrRecordNotFound, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Generate a single UUID for the test case
		tagID := uuid.New()

		// Setup mock expectations
		mock.ExpectQuery(`SELECT \* FROM "tags" WHERE id = \$1 ORDER BY "tags"."id" LIMIT \$2`).
			WithArgs(tagID, 1).
			WillReturnError(gorm.ErrInvalidDB)

		// Call the method
		filter := FilterTagInput{
			Id: tagID,
		}
		result, err := repo.FindRepoTag(context.Background(), filter)

		// Assertions
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Equal(t, gorm.ErrInvalidDB, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}
