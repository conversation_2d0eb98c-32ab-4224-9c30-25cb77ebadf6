package repository

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/google/uuid"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
	"gorm.io/datatypes"

	"api-server/internal/entities"
	"api-server/internal/types"
	"api-server/pkg/oteltrace"
	"api-server/pkg/otelzap"
)

// CustomImageDeploymentRepository defines the interface for custom image deployment operations
type CustomImageDeploymentRepository interface {
	// CreateDeployment creates a new custom image deployment
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - deployment: The deployment to create
	//
	// Returns:
	//   - *entities.CustomImageDeployment: The created deployment
	//   - error: nil if successful, otherwise the error that occurred
	CreateECRDeployment(ctx context.Context, input CreateCustomImageDeploymentInput) (*entities.CustomImageDeployment, error)

	// UpdateDeployment updates an existing custom image deployment
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - deployment: The deployment to update
	//
	// Returns:
	//   - *entities.CustomImageDeployment: The updated deployment
	//   - error: nil if successful, otherwise the error that occurred
	UpdateECRDeployment(ctx context.Context, input UpdateECRDeploymentInput) (*entities.CustomImageDeployment, error)

	// FindDeploymentByID retrieves a deployment by its ID
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - id: UUID of the deployment
	//
	// Returns:
	//   - *entities.CustomImageDeployment: The found deployment
	//   - error: nil if successful, otherwise the error that occurred
	FindECRDeployment(ctx context.Context, input FindECRDeploymentInput) (*entities.CustomImageDeployment, error)

	// FindUserDeployments retrieves all deployments for a user
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - userID: UUID of the user
	//
	// Returns:
	//   - []entities.CustomImageDeployment: List of user's deployments
	//   - error: nil if successful, otherwise the error that occurred
	FindUserDeployments(ctx context.Context, userID uuid.UUID) ([]entities.CustomImageDeployment, error)

	// FindOrgDeployments retrieves all deployments for an organization
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - orgID: UUID of the organization
	//
	// Returns:
	//   - []entities.CustomImageDeployment: List of organization's deployments
	//   - error: nil if successful, otherwise the error that occurred
	FindOrgDeployments(ctx context.Context, orgID uuid.UUID) ([]entities.CustomImageDeployment, error)

	// DeleteDeployment removes a deployment
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - id: UUID of the deployment to delete
	//
	// Returns:
	//   - error: nil if successful, otherwise the error that occurred
	DeleteECRDeployment(ctx context.Context, id uuid.UUID) error

	// DeleteECRDeploymentsByRepoID removes all deployments for a repository with optional deployment type filter
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - repoID: UUID of the repository
	//   - deploymentType: Optional deployment type filter (nil for all types)
	//
	// Returns:
	//   - error: nil if successful, otherwise the error that occurred
	DeleteECRDeploymentsByRepoID(ctx context.Context, repoID uuid.UUID, deploymentType *types.CustomImageDeploymentType) error

	// UpdateDeploymentStatus updates the status of a deployment
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - id: UUID of the deployment
	//   - status: New status to set
	//
	// Returns:
	//   - error: nil if successful, otherwise the error that occurred
	// UpdateDeploymentStatus(ctx context.Context, id uuid.UUID, status string) error

	CreateECRDeploymentEnv(ctx context.Context, in CreateECRDeploymentEnvInput) error

	UpdateECRDeploymentEnv(ctx context.Context, in UpdateECRDeploymentEnvInput) error

	DeleteECRDeploymentEnv(ctx context.Context, in DeleteECRDeploymentEnv) error

	// ListECRDeployment retrieves all ECR deployments with pagination
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - input: Pagination parameters
	//
	// Returns:
	//   - []entities.CustomImageDeployment: List of deployments
	//   - int64: Total count of deployments
	//   - error: nil if successful, otherwise the error that occurred
	ListECRDeployment(ctx context.Context, pagination types.Pagination, order types.OrderBy, in ListECRDeploymentInput) ([]entities.CustomImageDeployment, int64, error)
}

var _ CustomImageDeploymentRepository = (*repository)(nil)

type CreateCustomImageDeploymentInput struct {
	DeploymentName string
	ImageURI       string
	NodeName       string
	Env            map[string]string
	Port           int32
	UserID         *uuid.UUID
	OrgID          *uuid.UUID
	CPU            int
	Mem            int
	GPUMem         *int
	GPUModel       *string
	ProxyBodySize  int // in megabytes

	// for compose deployments
	Volumes        []string
	Author         *string
	ComposePorts   []string
	RestartPolicy  *string
	DeploymentType *types.CustomImageDeploymentType
	RepoID         *uuid.UUID
	Namespace      string
}

// CreateDeployment creates a new custom image deployment
func (r *repository) CreateECRDeployment(ctx context.Context, input CreateCustomImageDeploymentInput) (*entities.CustomImageDeployment, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repository.CreateDeployment")
	defer span.End()

	deployment := &entities.CustomImageDeployment{
		DeploymentName: input.DeploymentName,
		ImageURI:       input.ImageURI,
		NodeName:       input.NodeName,
		Port:           input.Port,
		UserID:         input.UserID,
		OrgID:          input.OrgID,
		ProxyBodySize:  input.ProxyBodySize,
		CPU:            input.CPU,
		Mem:            input.Mem,
		GPUMem:         input.GPUMem,
		GPUModel:       input.GPUModel,
		Author:         input.Author,
		RestartPolicy:  input.RestartPolicy,
		DeploymentType: input.DeploymentType,
		Volumes:        input.Volumes,
		ComposePorts:   input.ComposePorts,
		RepoID:         input.RepoID,
		Namespace:      &input.Namespace,
	}

	// create environment variables JSON
	inputEnv, err := json.Marshal(input.Env)
	if err != nil {
		span.SetStatus(codes.Error, "failed to marshal ecr env")
		span.RecordError(err)
		return nil, err
	}
	deployment.Env = inputEnv

	if err := r.GetDB(ctx).Table("custom_image_deployments").Model(&entities.CustomImageDeployment{}).Create(deployment).Error; err != nil {
		span.SetStatus(codes.Error, "failed to create deployment")
		span.RecordError(err)
		return nil, fmt.Errorf("failed to create deployment: %w", err)
	}

	return deployment, nil
}

type UpdateECRDeploymentInput struct {
	ID            uuid.UUID
	NodeName      *string
	Port          *int32
	GPUModel      *string
	Mem           *uint
	NumCpu        *uint
	ProxyBodySize *int // in megabytes
}

// UpdateDeployment updates an existing custom image deployment
func (r *repository) UpdateECRDeployment(ctx context.Context, input UpdateECRDeploymentInput) (*entities.CustomImageDeployment, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repository.UpdateDeployment")
	defer span.End()

	db := r.GetDB(ctx)

	var deployment entities.CustomImageDeployment

	err := db.WithContext(ctx).
		Where("id = ?", input.ID).
		First(&deployment).Error
	if err != nil {
		return nil, err
	}

	if input.NodeName != nil {
		deployment.NodeName = *input.NodeName
	}
	if input.Port != nil {
		deployment.Port = *input.Port
	}
	if input.GPUModel != nil {
		deployment.GPUModel = input.GPUModel
	}
	if input.Mem != nil {
		deployment.Mem = int(*input.Mem)
	}
	if input.NumCpu != nil {
		deployment.CPU = int(*input.NumCpu)
	}
	if input.ProxyBodySize != nil {
		deployment.ProxyBodySize = *input.ProxyBodySize
	}

	if err := db.Save(deployment).Error; err != nil {
		span.SetStatus(codes.Error, "failed to update deployment")
		span.RecordError(err)
		return nil, fmt.Errorf("failed to update deployment: %w", err)
	}

	return &deployment, nil
}

type FindECRDeploymentInput struct {
	ID             *uuid.UUID
	DeploymentName *string
}

// FindDeploymentByID retrieves a deployment
func (r *repository) FindECRDeployment(ctx context.Context, input FindECRDeploymentInput) (*entities.CustomImageDeployment, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repository.FindDeploymentByID")
	defer span.End()

	db := r.GetDB(ctx).Preload("User").Preload("Repo")

	if input.ID != nil {
		db = db.Where("id = ?", input.ID)
	}
	if input.DeploymentName != nil {
		db = db.Where("deployment_name = ?", input.DeploymentName)
	}

	deployment := &entities.CustomImageDeployment{}
	if err := db.First(deployment).Error; err != nil {
		span.SetStatus(codes.Error, "failed to find deployment")
		span.RecordError(err)
		return nil, err
	}

	return deployment, nil
}

// FindUserDeployments retrieves all deployments for a user
func (r *repository) FindUserDeployments(ctx context.Context, userID uuid.UUID) ([]entities.CustomImageDeployment, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repository.FindUserDeployments")
	defer span.End()

	db := r.GetDB(ctx)
	var deployments []entities.CustomImageDeployment

	if err := db.Where("user_id = ?", userID).Find(&deployments).Error; err != nil {
		span.SetStatus(codes.Error, "failed to find user deployments")
		span.RecordError(err)
		return nil, fmt.Errorf("failed to find user deployments: %w", err)
	}

	return deployments, nil
}

// FindOrgDeployments retrieves all deployments for an organization
func (r *repository) FindOrgDeployments(ctx context.Context, orgID uuid.UUID) ([]entities.CustomImageDeployment, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repository.FindOrgDeployments")
	defer span.End()

	db := r.GetDB(ctx)
	var deployments []entities.CustomImageDeployment

	if err := db.Where("org_id = ?", orgID).Find(&deployments).Error; err != nil {
		span.SetStatus(codes.Error, "failed to find organization deployments")
		span.RecordError(err)
		return nil, fmt.Errorf("failed to find organization deployments: %w", err)
	}

	return deployments, nil
}

// DeleteDeployment removes a deployment
func (r *repository) DeleteECRDeployment(ctx context.Context, id uuid.UUID) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "repository.DeleteECRDeployment")
	defer span.End()

	db := r.GetDB(ctx)

	if err := db.Delete(&entities.CustomImageDeployment{}, id).Error; err != nil {
		span.SetStatus(codes.Error, "failed to delete deployment")
		span.RecordError(err)
		return fmt.Errorf("failed to delete deployment: %w", err)
	}

	return nil
}

// DeleteECRDeploymentsByRepoID removes all deployments for a repository with optional deployment type filter
func (r *repository) DeleteECRDeploymentsByRepoID(ctx context.Context, repoID uuid.UUID, deploymentType *types.CustomImageDeploymentType) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "repository.DeleteECRDeploymentsByRepoID")
	defer span.End()

	db := r.GetDB(ctx)
	query := db.Where("repo_id = ?", repoID)

	// Add deployment type filter if provided
	if deploymentType != nil {
		query = query.Where("deployment_type = ?", string(*deploymentType))
		span.SetAttributes(attribute.String("deployment_type", string(*deploymentType)))
	}

	span.SetAttributes(attribute.String("repo_id", repoID.String()))

	result := query.Delete(&entities.CustomImageDeployment{})
	if result.Error != nil {
		span.SetStatus(codes.Error, "failed to delete deployments by repo ID")
		span.RecordError(result.Error)
		return fmt.Errorf("failed to delete deployments by repo ID: %w", result.Error)
	}

	span.SetAttributes(attribute.Int64("deleted_count", result.RowsAffected))
	span.AddEvent("Deleted deployments by repo ID", trace.WithAttributes(
		attribute.String("repo_id", repoID.String()),
		attribute.Int64("count", result.RowsAffected),
	))

	return nil
}

// CreateEnvInput contains the data needed to create environment variables.
type CreateECRDeploymentEnvInput struct {
	ID    uuid.UUID
	Key   string
	Value string
}

// CreateEnv creates environment variables for a repository.
func (r repository) CreateECRDeploymentEnv(ctx context.Context, in CreateECRDeploymentEnvInput) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.repository.CreateECRDeploymentEnv")
	defer span.End()

	db := r.GetDB(ctx)

	if err := db.Model(&entities.CustomImageDeployment{}).
		Where("id = ?", in.ID).
		UpdateColumn("env", datatypes.JSONSet("env").Set(fmt.Sprintf("{%s}", in.Key), in.Value)).
		Error; err != nil {
		span.SetStatus(codes.Error, err.Error())
		span.RecordError(err)
		otelzap.ErrorWithContext(ctx, "failed to update ECR deployment environment", err)
		return err
	}

	otelzap.InfoWithContext(ctx, "successfully updated ECR deployment environment")
	return nil
}

type DeleteECRDeploymentEnv struct {
	ID  uuid.UUID
	Key string
}

func (r repository) DeleteECRDeploymentEnv(ctx context.Context, in DeleteECRDeploymentEnv) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.repository.DeleteECRDeploymentEnv")
	defer span.End()

	db := r.GetDB(ctx)

	var deployment entities.CustomImageDeployment
	err := db.WithContext(ctx).
		Where("id = ?", in.ID).
		First(&deployment).Error
	if err != nil {
		span.SetStatus(codes.Error, err.Error())
		span.RecordError(err)
		otelzap.ErrorWithContext(ctx, "failed to delete repository environment", err)
		return err
	}

	var envMap map[string]string
	if err := json.Unmarshal(deployment.Env, &envMap); err != nil {
		return err
	}
	delete(envMap, in.Key)

	updatedEnv, err := json.Marshal(envMap)
	if err != nil {
		return err
	}
	deployment.Env = updatedEnv

	if err := db.Save(&deployment).Error; err != nil {
		return err
	}

	span.AddEvent("successfully deleted repository environment variable")
	span.SetStatus(codes.Ok, "successfully deleted repository environment variable")
	return nil
}

type UpdateECRDeploymentEnvInput struct {
	ID     uuid.UUID
	OldKey string
	Key    string
	Value  string
}

// UpdateEnv updates the environment variables for a repository.
func (r repository) UpdateECRDeploymentEnv(ctx context.Context, in UpdateECRDeploymentEnvInput) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.repository.UpdateECRDeploymentEnv")
	defer span.End()

	db := r.GetDB(ctx)

	var deployment entities.CustomImageDeployment
	err := db.WithContext(ctx).
		Where("id = ?", in.ID).
		First(&deployment).Error
	if err != nil {
		return err
	}

	var envMap map[string]string
	if err := json.Unmarshal(deployment.Env, &envMap); err != nil {
		return err
	}
	delete(envMap, in.OldKey)
	envMap[in.Key] = in.Value

	updatedEnv, err := json.Marshal(envMap)
	if err != nil {
		return err
	}
	deployment.Env = updatedEnv

	if err := db.Save(&deployment).Error; err != nil {
		return err
	}

	return nil
}

type ListECRDeploymentInput struct {
	UserID         *uuid.UUID
	OrgID          *uuid.UUID
	RepoID         *uuid.UUID
	Search         *string
	DeploymentType *types.CustomImageDeploymentType
}

// ListECRDeployment retrieves all ECR deployments with pagination
func (r *repository) ListECRDeployment(ctx context.Context, pagination types.Pagination, order types.OrderBy, in ListECRDeploymentInput) ([]entities.CustomImageDeployment, int64, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repository.ListECRDeployment")
	defer span.End()

	var deployments []entities.CustomImageDeployment
	var total int64

	db := r.GetDB(ctx).Preload("User")
	query := db.Model(&entities.CustomImageDeployment{})

	if in.UserID != nil {
		query = query.Where("user_id = ?", in.UserID)
	}
	if in.OrgID != nil {
		query = query.Where("org_id = ?", in.OrgID)
	}
	if in.RepoID != nil {
		query = query.Where("repo_id = ?", in.RepoID)
	}
	if in.Search != nil {
		keyword := fmt.Sprintf("%%%s%%", *in.Search)
		query = query.Where(
			"deployment_name ILIKE ?",
			keyword,
		)
	}
	if in.DeploymentType != nil {
		query = query.Where("deployment_type = ?", string(*in.DeploymentType))
	}

	// Count total records
	if err := query.Count(&total).Error; err != nil {
		span.SetStatus(codes.Error, "failed to count deployments")
		span.RecordError(err)
		return nil, 0, fmt.Errorf("failed to count deployments: %w", err)
	}

	// Get paginated records
	if err := query.Scopes(paginate(pagination.PageNo, pagination.PageSize), orderBy(order)).
		Find(&deployments).Error; err != nil {
		span.SetStatus(codes.Error, "failed to list deployments")
		span.RecordError(err)
		return nil, 0, fmt.Errorf("failed to list deployments: %w", err)
	}

	return deployments, total, nil
}
