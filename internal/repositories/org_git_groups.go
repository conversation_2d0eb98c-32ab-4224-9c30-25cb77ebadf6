package repository

import (
	"api-server/internal/entities"
	"api-server/pkg/oteltrace"
	"context"

	"github.com/google/uuid"
	"go.opentelemetry.io/otel/codes"
)

// OrgGitGroupRepository defines the interface for organization Gitlab group (git group for short) operations.
// Using Gitlab group to represent organization
// It provides methods for managing organization git groups, including creating,
// finding, and removing git groups.
type OrgGitGroupRepository interface {
	// CreateOrgGitGroup creates a new git group for an organization.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - in: Input data for creating the git group
	//
	// Returns:
	//   - *entities.OrgGitGroup: The created git group if successful
	//   - error: nil if successful, otherwise the error that occurred
	CreateOrgGitGroup(ctx context.Context, in CreateOrgGitGroupInput) (*entities.OrgGitGroup, error)

	// FindOrgGitGroup retrieves a specific organization's git group.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - orgID: ID of the organization
	//
	// Returns:
	//   - *entities.DefaultGitGroup: The found git group if successful
	//   - error: nil if successful, otherwise the error that occurred
	FindOrgGitGroup(ctx context.Context, orgID uuid.UUID) (*entities.DefaultGitGroup, error)

	// DeleteOrgGitGroup removes a git group from an organization.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - orgID: ID of the organization
	//
	// Returns:
	//   - error: nil if successful, otherwise the error that occurred
	DeleteOrgGitGroup(ctx context.Context, orgID uuid.UUID) error
}

var _ OrgGitGroupRepository = (*repository)(nil)

// CreateOrgGitGroupInput contains the data needed to create a new organization git group.
type CreateOrgGitGroupInput struct {
	OrgID            uuid.UUID
	RefGitModelsID   int64
	RefGitSpacesID   int64
	RefGitDatasetsID int64
	RefGitComposesID *int64
}

// CreateOrgGitGroup creates a new git group for an organization.
func (r *repository) CreateOrgGitGroup(ctx context.Context, in CreateOrgGitGroupInput) (*entities.OrgGitGroup, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.org_git_groups.CreateOrgGitGroup")
	defer span.End()

	data := entities.OrgGitGroup{
		OrgID: in.OrgID,
		DefaultGitGroup: entities.DefaultGitGroup{
			RefGitModelsID:   in.RefGitModelsID,
			RefGitSpacesID:   in.RefGitSpacesID,
			RefGitDatasetsID: in.RefGitDatasetsID,
			RefGitComposesID: in.RefGitComposesID,
		},
	}

	err := r.GetDB(ctx).Create(&data).Error
	if err != nil {
		span.SetStatus(codes.Error, "failed to create organization git group")
		span.RecordError(err)
		return nil, err
	}

	span.AddEvent("organization git group created")
	span.SetStatus(codes.Ok, "organization git group created")
	return &data, nil
}

// FindOrgGitGroup retrieves a specific organization's git group.
func (r *repository) FindOrgGitGroup(ctx context.Context, orgID uuid.UUID) (*entities.DefaultGitGroup, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.org_git_groups.FindOrgGitGroup")
	defer span.End()

	var result entities.DefaultGitGroup
	predicate := entities.OrgGitGroup{
		OrgID: orgID,
	}
	err := r.GetDB(ctx).Table("org_git_groups").Where(&predicate).First(&result).Error
	if err != nil {
		span.SetStatus(codes.Error, "failed to find organization git group")
		span.RecordError(err)
		return nil, err
	}

	span.AddEvent("organization git group found")
	span.SetStatus(codes.Ok, "organization git group found")
	return &result, nil
}

// DeleteOrgGitGroup removes a git group from an organization.
func (r *repository) DeleteOrgGitGroup(ctx context.Context, orgID uuid.UUID) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.org_git_groups.DeleteOrgGitGroup")
	defer span.End()

	err := r.GetDB(ctx).Where("org_id = ?", orgID).Delete(&entities.OrgGitGroup{}).Error
	if err != nil {
		span.SetStatus(codes.Error, "failed to delete organization git group")
		span.RecordError(err)
		return err
	}

	span.AddEvent("organization git group deleted")
	span.SetStatus(codes.Ok, "organization git group deleted")
	return nil
}
