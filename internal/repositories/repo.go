package repository

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"

	"github.com/google/uuid"
	"go.opentelemetry.io/otel/codes"
	"gorm.io/datatypes"
	"gorm.io/gorm"

	"api-server/internal/dto"
	"api-server/internal/entities"
	"api-server/internal/enums"
	"api-server/internal/types"
	"api-server/pkg/oteltrace"
	"api-server/pkg/otelzap"
)

// RepoRepository defines the interface for repository operations.
// It provides methods for managing repositories, including creating, updating,
// finding, and listing repositories with various filtering options.
type RepoRepository interface {
	// DeleteProjectsByOrganizationID removes all projects from an organization.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - organizationID: UUID of the organization
	//
	// Returns:
	//   - error: nil if successful, otherwise the error that occurred
	DeleteProjectsByOrganizationID(ctx context.Context, organizationID uuid.UUID) error

	// DeleteProjectsByUserID removes all projects from a user.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - userID: UUID of the user
	//
	// Returns:
	//   - error: nil if successful, otherwise the error that occurred
	DeleteProjectsByUserID(ctx context.Context, userID uuid.UUID) error
	// FindRepositoryByID(ctx context.Context, id uuid.UUID) (*entities.Repository, error)

	// ListRepositories retrieves a paginated list of repositories based on input criteria.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - input: Criteria for listing repositories including pagination and filtering
	//
	// Returns:
	//   - []entities.Repository: List of repositories
	//   - error: nil if successful, otherwise the error that occurred
	ListRepositories(
		ctx context.Context,
		input dto.GetRepositoriesInput,
	) ([]entities.Repository, error)

	// CountRepositories counts the total number of repositories based on input criteria.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - input: Criteria for counting repositories
	//
	// Returns:
	//   - int64: Total number of repositories
	//   - error: nil if successful, otherwise the error that occurred
	CountRepositories(ctx context.Context, input dto.GetRepositoriesInput) (int64, error)

	// FindRepository retrieves a specific repository based on filter criteria.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - filter: Filter criteria for finding the repository
	//
	// Returns:
	//   - *entities.Repository: The found repository if successful
	//   - error: nil if successful, otherwise the error that occurred
	FindRepository(ctx context.Context, filter RepositoryFilter) (*entities.Repository, error)

	// UpdateRepository updates an existing repository.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - input: Repository data to update
	//
	// Returns:
	//   - error: nil if successful, otherwise the error that occurred
	UpdateRepository(ctx context.Context, input entities.Repository) error

	// ListRepoMembers retrieves a paginated list of repository members.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - pagination: Pagination parameters
	//   - order: Ordering parameters
	//   - query: Query parameters for filtering members
	//
	// Returns:
	//   - []entities.RepoMemberInfo: List of repository members
	//   - error: nil if successful, otherwise the error that occurred
	ListRepoMembers(
		ctx context.Context,
		pagination types.Pagination,
		order types.OrderBy,
		query ListRepositoryMembersQuery,
	) ([]entities.RepoMemberInfo, error)

	// CountRepoMembers counts the total number of repository members.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - query: Query parameters for filtering members
	//
	// Returns:
	//   - int: Total number of members
	//   - error: nil if successful, otherwise the error that occurred
	CountRepoMembers(ctx context.Context, query ListRepositoryMembersQuery) (int, error)

	// RemoveUserInRepository removes a user from a repository.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - in: Input data for removing the user
	//
	// Returns:
	//   - error: nil if successful, otherwise the error that occurred
	RemoveUserInRepository(ctx context.Context, in RemoveUserInRepositoryInput) error

	// GetEnv retrieves the environment variables for a repository.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - repoID: UUID of the repository
	//
	// Returns:
	//   - *entities.RepoEnv: The repository environment variables
	//   - error: nil if successful, otherwise the error that occurred
	GetEnv(ctx context.Context, repoID uuid.UUID) (*entities.RepoEnv, error)

	// UpdateEnv updates the environment variables for a repository.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - in: Input data for updating environment variables
	//
	// Returns:
	//   - error: nil if successful, otherwise the error that occurred
	UpdateEnv(ctx context.Context, in UpdateEnvInput) error

	// CreateEnv creates environment variables for a repository.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - in: Input data for creating environment variables
	//
	// Returns:
	//   - error: nil if successful, otherwise the error that occurred
	CreateEnv(ctx context.Context, in CreateEnvInput) error

	// BulkCreateEnv creates multiple environment variables for a repository in a single operation.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - in: Input data for creating multiple environment variables
	//
	// Returns:
	//   - error: nil if successful, otherwise the error that occurred
	BulkCreateEnv(ctx context.Context, in BulkCreateEnvInput) error

	// DeleteEnv deletes environment variables from a repository.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - in: Input data for deleting environment variables
	//
	// Returns:
	//   - error: nil if successful, otherwise the error that occurred
	DeleteEnv(ctx context.Context, in CreateEnvInput) error
}

var _ RepoRepository = (*repository)(nil)

// DeleteProjectsByOrganizationID removes all projects from an organization.
func (r repository) DeleteProjectsByOrganizationID(ctx context.Context, organizationID uuid.UUID) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.repo.DeleteProjectsByOrganizationID")
	defer span.End()

	if err := r.GetDB(ctx).Where("org_id = ?", organizationID).Delete(&entities.Repository{}).Error; err != nil {
		span.SetStatus(codes.Error, "Failed to delete projects by organization ID")
		span.RecordError(err)
		return err
	}

	span.AddEvent("Successfully deleted projects by organization ID")
	span.SetStatus(codes.Ok, "Successfully deleted projects by organization ID")
	return nil
}

// DeleteProjectsByUserID removes all projects from a user.
func (r repository) DeleteProjectsByUserID(ctx context.Context, userID uuid.UUID) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.repo.DeleteProjectsByUserID")
	defer span.End()

	if err := r.GetDB(ctx).Where("user_id = ?", userID).Delete(&entities.Repository{}).Error; err != nil {
		span.SetStatus(codes.Error, "Failed to delete projects by user ID")
		span.RecordError(err)
		return err
	}

	span.AddEvent("Successfully deleted projects by user ID")
	span.SetStatus(codes.Ok, "Successfully deleted projects by user ID")
	return nil
}

// func (r repository) FindRepositoryByID(
// 	ctx context.Context,
// 	id uuid.UUID,
// ) (*entities.Repository, error) {
// 	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.repo.FindRepositoryByID")
// 	defer span.End()
//
// 	repo, err := r.FindRepository(ctx, RepositoryFilter{Id: &id})
// 	if err != nil {
// 		return nil, err
// 	}
// 	return repo, nil
// }

// ListRepositories retrieves a paginated list of repositories based on input criteria.
func (r repository) ListRepositories(
	ctx context.Context,
	input dto.GetRepositoriesInput,
) ([]entities.Repository, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.repo.ListRepositories")
	defer span.End()

	query := r.GetDB(ctx).
		Preload("Deployment").
		Preload("User").
		Preload("Org").
		Preload("Hardware").
		Select("repositories.*, au.email AS user_email").
		Joins("LEFT JOIN users ON repositories.user_id = users.id").
		Joins("LEFT JOIN organizations ON repositories.org_id = organizations.id").
		Joins("LEFT JOIN auth.users au ON repositories.user_id = au.id")

	query = listRepositoriesWithPredicate(&input, query)

	if input.Paginate.Page != 0 && input.Paginate.PerPage != 0 {
		query = query.Scopes(paginate(input.Paginate.Page, input.Paginate.PerPage))
	}

	if input.Paginate.OrderBy != "" && input.Paginate.Sort != "" {
		query = query.Scopes(orderBy(types.OrderBy{input.Paginate.OrderBy: input.Paginate.Sort}))
	}

	var repos []entities.RepositoryInfo
	if err := query.Find(&repos).Error; err != nil {
		span.SetStatus(codes.Error, "failed to list repositories")
		span.RecordError(err)
		return nil, err
	}

	var rs []entities.Repository
	for _, u := range repos {
		rs = append(rs, u.ToEntity())
	}

	otelzap.InfoWithContext(ctx, "successfully listed repositories")
	return rs, nil
}

// TagsQueryModel contains the query model for repository tags.
type TagsQueryModel struct {
	Tags       []string `json:"tags,omitempty"`
	Tasks      []string `json:"task_categories,omitempty"`
	Libraries  []string `json:"libraries,omitempty"`
	Languages  []string `json:"language,omitempty"`
	Licenses   string   `json:"license,omitempty"`
	Other      []string `json:"other,omitempty"`
	Sizes      []string `json:"size_categories,omitempty"`
	Datasets   []string `json:"datasets,omitempty"`
	PrettyName string   `json:"pretty_name,omitempty"`
}

func listRepositoriesWithPredicate(input *dto.GetRepositoriesInput, query *gorm.DB) *gorm.DB {
	if input.RepositoryType != "" {
		query = query.Where("repositories.type = ?", input.RepositoryType)
	}

	if input.UserID != "" {
		query = query.Where("repositories.user_id = ?", input.UserID)
	}

	if input.OrgID != "" {
		query = query.Where("repositories.org_id = ?", input.OrgID)
	}

	if input.Keyword != "" {
		keyword := fmt.Sprintf("%%%s%%", input.Keyword)
		query = query.Where(
			"repositories.name ILIKE ? OR users.username ILIKE ? OR organizations.name ILIKE ?",
			keyword,
			keyword,
			keyword,
		)
	}

	out, err := removeEmptyArrays(parseTagsQueryModel(input.TagsQuery))
	if err != nil {
		return nil
	}

	rawJson, err := json.Marshal(out)
	if err != nil {
		return nil
	}

	if rawJson != nil && string(rawJson) != "{}" {
		query = query.Where("metadata @> ?", string(rawJson))
	}

	return query
}

func removeEmptyArrays(input any) (map[string]any, error) {
	jsonBytes, err := json.Marshal(input)
	if err != nil {
		return nil, err
	}

	var result map[string]any
	err = json.Unmarshal(jsonBytes, &result)
	if err != nil {
		return nil, err
	}

	for key, value := range result {
		if arr, ok := value.([]any); ok && len(arr) == 1 && arr[0] == "" {
			delete(result, key)
		}
	}

	return result, nil
}

// parseTagsQueryModel converts a dto.TagsQuery to a TagsQueryModel.
// It processes and splits comma-separated string values from the input DTO into string slices.
func parseTagsQueryModel(input dto.TagsQuery) TagsQueryModel {
	return TagsQueryModel{
		Tags: input.GetValues(append(
			strings.Split(input.Other, ","),
			strings.Split(input.Tags, ",")...,
		)),
		Tasks:      input.GetValues(strings.Split(input.Tasks, ",")),
		Licenses:   input.GetValue(input.License),
		Libraries:  input.GetValues(strings.Split(input.Libraries, ",")),
		Languages:  input.GetValues(strings.Split(input.Languages, ",")),
		Sizes:      input.GetValues(strings.Split(input.Sizes, ",")),
		Datasets:   input.GetValues(strings.Split(input.Datasets, ",")),
		PrettyName: input.GetValue(input.PrettyName),
	}
}

// CountRepositories counts the total number of repositories based on input criteria.
func (r repository) CountRepositories(
	ctx context.Context,
	input dto.GetRepositoriesInput,
) (int64, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.repo.CountRepositories")
	defer span.End()

	query := r.GetDB(ctx).Table("repositories").
		Select("*").
		Joins("LEFT JOIN users ON repositories.user_id = users.id").
		Joins("LEFT JOIN organizations ON repositories.org_id = organizations.id")

	query = listRepositoriesWithPredicate(&input, query)

	var count int64
	if err := query.Count(&count).Error; err != nil {
		span.SetStatus(codes.Error, err.Error())
		span.RecordError(err)
		otelzap.ErrorWithContext(ctx, "failed to count repositories", err)
		return 0, err
	}

	otelzap.InfoWithContext(ctx, "successfully counted repositories")
	return count, nil
}

// RepositoryFilter contains the criteria for finding a repository.
type RepositoryFilter struct {
	Id        *uuid.UUID      `json:"id"`
	Name      *string         `json:"name"`
	Type      *enums.RepoType `json:"type"`
	Namespace *string         `json:"namespace"`
	UserId    *uuid.UUID      `json:"user_id"`
	OrgId     *uuid.UUID      `json:"org_id"`
}

// FindRepository retrieves a specific repository based on filter criteria.
func (r repository) FindRepository(
	ctx context.Context,
	filter RepositoryFilter,
) (*entities.Repository, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.repo.FindRepositoryWithFilter")
	defer span.End()

	var repo entities.RepositoryInfo
	db := r.GetDB(ctx)
	query := db.Table("repositories").
		Preload("User").
		Preload("Org").
		Preload("Deployment").
		Preload("Hardware").
		Select("repositories.*, au.email AS user_email").
		Joins("LEFT JOIN auth.users au ON repositories.user_id = au.id")

	if filter.Id != nil {
		query = query.Where("repositories.id = ?", filter.Id)
	}

	if filter.Name != nil {
		query = query.Where("repositories.name = ?", filter.Name)
	}

	if filter.Type != nil {
		query = query.Where("repositories.type = ?", filter.Type)
	}

	if filter.UserId != nil {
		query = query.Where("repositories.user_id = ?", filter.UserId)
	}

	if filter.Namespace != nil {
		query = query.
			Joins("LEFT JOIN public.users users ON repositories.user_id = users.id").
			Joins("LEFT JOIN public.organizations orgs ON repositories.org_id = orgs.id").
			Where("orgs.path_name = ? OR (users.username = ? AND orgs.id IS NULL)", filter.Namespace, filter.Namespace)
	}

	if filter.OrgId != nil {
		query = query.Where("repositories.org_id = ?", filter.OrgId)
	}

	if err := query.First(&repo).Error; err != nil {
		span.SetStatus(codes.Error, err.Error())
		span.RecordError(err)
		otelzap.ErrorWithContext(ctx, "failed to find repository", err)
		return nil, err
	}

	rp := repo.ToEntity()
	otelzap.InfoWithContext(ctx, "successfully found repository")
	return &rp, nil
}

// UpdateRepository updates an existing repository.
func (r repository) UpdateRepository(ctx context.Context, input entities.Repository) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.repository.UpdateRepository")
	defer span.End()

	predicate := &entities.Repository{
		BaseModel: entities.BaseModel{
			ID: input.ID,
		},
	}

	query := r.GetDB(ctx).Where(predicate)
	if err := query.Model(&entities.Repository{}).Updates(input).Error; err != nil {
		span.SetStatus(codes.Error, err.Error())
		span.RecordError(err)
		otelzap.ErrorWithContext(ctx, "failed to update repository", err)
		return err
	}

	otelzap.InfoWithContext(ctx, "successfully updated repository")
	return nil
}

// ListRepositoryMembersQuery contains the criteria for listing repository members.
type ListRepositoryMembersQuery struct {
	ID            uuid.UUID
	Keyword       string
	CurrentUserId uuid.UUID
}

// ListRepoMembers retrieves a paginated list of repository members.
func (r repository) ListRepoMembers(
	ctx context.Context,
	pagination types.Pagination,
	order types.OrderBy,
	input ListRepositoryMembersQuery,
) ([]entities.RepoMemberInfo, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.repo.ListRepoMembers")
	defer span.End()

	var members []entities.RepoMemberInfo
	query := r.GetDB(ctx).Table("repo_members").
		Scopes(paginate(pagination.PageNo, pagination.PageSize), orderBy(order)).
		Select("repo_members.*, users.name AS name, au.email AS email").
		Joins("LEFT JOIN users ON repo_members.user_id = users.id").
		Joins("LEFT JOIN auth.users au ON users.id = au.id").
		Where("repo_members.repo_id = ?", input.ID)

	if input.Keyword != "" {
		keyword := fmt.Sprintf("%%%s%%", input.Keyword)
		query = query.Where("users.name ILIKE ? OR au.email ILIKE ?", keyword, keyword)
	}

	err := query.Find(&members).Error
	if err != nil {
		span.SetStatus(codes.Error, err.Error())
		span.RecordError(err)
		otelzap.ErrorWithContext(ctx, "failed to list repository members", err)
		return nil, err
	}

	otelzap.InfoWithContext(ctx, "successfully listed repository members")
	return members, nil
}

// CountRepoMembers counts the total number of repository members.
func (r repository) CountRepoMembers(
	ctx context.Context,
	input ListRepositoryMembersQuery,
) (int, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.repo.CountRepoMembers")
	defer span.End()

	var memberNum int64
	query := r.GetDB(ctx).Table("repo_members").
		Select("repo_members.*, users.name AS name, au.email AS email").
		Joins("LEFT JOIN users ON repo_members.user_id = users.id").
		Joins("LEFT JOIN auth.users au ON users.id = au.id").
		Where("repo_members.repo_id = ?", input.ID)

	if input.Keyword != "" {
		keyword := fmt.Sprintf("%%%s%%", input.Keyword)
		query = query.Where("users.name ILIKE ? OR au.email ILIKE ?", keyword, keyword)
	}

	err := query.Count(&memberNum).Error
	if err != nil {
		span.SetStatus(codes.Error, err.Error())
		span.RecordError(err)
		otelzap.ErrorWithContext(ctx, "failed to count repository members", err)
		return 0, err
	}

	otelzap.InfoWithContext(ctx, "successfully counted repository members")
	return int(memberNum), nil
}

// RemoveUserInRepositoryInput contains the data needed to remove a user from a repository.
type RemoveUserInRepositoryInput struct {
	IDs []uuid.UUID
}

// RemoveUserInRepository removes a user from a repository.
func (r repository) RemoveUserInRepository(ctx context.Context, in RemoveUserInRepositoryInput) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.repository.RemoveUserInRepository")
	defer span.End()

	query := r.GetDB(ctx)

	if len(in.IDs) > 0 {
		query = query.Where("id IN ?", in.IDs)
	}

	if err := query.Model(&entities.Repository{}).Update("user_id", nil).Error; err != nil {
		span.SetStatus(codes.Error, err.Error())
		span.RecordError(err)
		otelzap.ErrorWithContext(ctx, "failed to remove user from repository", err)
		return err
	}

	otelzap.InfoWithContext(ctx, "successfully removed user from repository")
	return nil
}

// GetEnv retrieves the environment variables for a repository.
func (r repository) GetEnv(ctx context.Context, repoID uuid.UUID) (*entities.RepoEnv, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.repository.GetEnv")
	defer span.End()

	var env entities.RepoEnv
	err := r.GetDB(ctx).WithContext(ctx).
		Where("repo_id = ?", repoID).
		First(&env).Error
	if err != nil {
		span.SetStatus(codes.Error, err.Error())
		span.RecordError(err)
		otelzap.ErrorWithContext(ctx, "failed to get repository environment", err)
		return nil, err
	}

	otelzap.InfoWithContext(ctx, "successfully retrieved repository environment")
	return &env, nil
}

// CreateEnvInput contains the data needed to create environment variables.
type CreateEnvInput struct {
	RepoID uuid.UUID
	Key    string
	Value  string
}

// BulkCreateEnvInput contains the data needed to create multiple environment variables.
type BulkCreateEnvInput struct {
	RepoID uuid.UUID
	Envs   map[string]string // Key-value pairs of environment variables
}

// CreateEnv creates environment variables for a repository.
func (r repository) CreateEnv(ctx context.Context, in CreateEnvInput) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.repository.UpdateEnv")
	defer span.End()

	db := r.GetDB(ctx)

	var env entities.RepoEnv
	err := db.WithContext(ctx).
		Where("repo_id = ?", in.RepoID).
		First(&env).Error
	if err != nil {
		// create new record if not exists
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = db.Create(&entities.RepoEnv{
				RepoID: in.RepoID,
				Env:    datatypes.JSON([]byte(fmt.Sprintf(`{"%s":"%s"}`, in.Key, in.Value))),
			}).Error
			if err != nil {
				span.SetStatus(codes.Error, err.Error())
				span.RecordError(err)
				otelzap.ErrorWithContext(ctx, "failed to create repository environment", err)
				return err
			}
			otelzap.InfoWithContext(ctx, "successfully created repository environment")
			return nil
		}

		span.SetStatus(codes.Error, err.Error())
		span.RecordError(err)
		otelzap.ErrorWithContext(ctx, "failed to fetch repository environment", err)
		return err
	}

	if err := db.Model(&entities.RepoEnv{}).
		Where("repo_id = ?", in.RepoID).
		UpdateColumn("env", datatypes.JSONSet("env").Set(fmt.Sprintf("{%s}", in.Key), in.Value)).
		Error; err != nil {
		span.SetStatus(codes.Error, err.Error())
		span.RecordError(err)
		otelzap.ErrorWithContext(ctx, "failed to update repository environment", err)
		return err
	}

	otelzap.InfoWithContext(ctx, "successfully updated repository environment")
	return nil
}

// BulkCreateEnv creates multiple environment variables for a repository in a single operation.
func (r repository) BulkCreateEnv(ctx context.Context, in BulkCreateEnvInput) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.repository.BulkCreateEnv")
	defer span.End()

	db := r.GetDB(ctx)

	var env entities.RepoEnv
	err := db.WithContext(ctx).
		Where("repo_id = ?", in.RepoID).
		First(&env).Error
	if err != nil {
		// create new record if not exists
		if errors.Is(err, gorm.ErrRecordNotFound) {
			envJSON, err := json.Marshal(in.Envs)
			if err != nil {
				span.SetStatus(codes.Error, err.Error())
				span.RecordError(err)
				otelzap.ErrorWithContext(ctx, "failed to marshal environment variables", err)
				return err
			}

			err = db.Create(&entities.RepoEnv{
				RepoID: in.RepoID,
				Env:    datatypes.JSON(envJSON),
			}).Error
			if err != nil {
				span.SetStatus(codes.Error, err.Error())
				span.RecordError(err)
				otelzap.ErrorWithContext(ctx, "failed to create repository environment", err)
				return err
			}
			otelzap.InfoWithContext(ctx, "successfully created repository environment with bulk envs")
			return nil
		}

		span.SetStatus(codes.Error, err.Error())
		span.RecordError(err)
		otelzap.ErrorWithContext(ctx, "failed to fetch repository environment", err)
		return err
	}

	// Update existing environment variables by merging with new ones
	var existingEnvMap map[string]string
	if err := json.Unmarshal(env.Env, &existingEnvMap); err != nil {
		span.SetStatus(codes.Error, err.Error())
		span.RecordError(err)
		otelzap.ErrorWithContext(ctx, "failed to unmarshal existing environment variables", err)
		return err
	}

	// Merge new environment variables with existing ones
	for key, value := range in.Envs {
		if value == "" {
			// Delete environment variable if value is empty (similar to single CreateEnv logic)
			delete(existingEnvMap, key)
		} else {
			existingEnvMap[key] = value
		}
	}

	updatedEnv, err := json.Marshal(existingEnvMap)
	if err != nil {
		span.SetStatus(codes.Error, err.Error())
		span.RecordError(err)
		otelzap.ErrorWithContext(ctx, "failed to marshal updated environment variables", err)
		return err
	}

	env.Env = updatedEnv
	if err := db.Save(&env).Error; err != nil {
		span.SetStatus(codes.Error, err.Error())
		span.RecordError(err)
		otelzap.ErrorWithContext(ctx, "failed to save updated repository environment", err)
		return err
	}

	otelzap.InfoWithContext(ctx, "successfully bulk updated repository environment")
	return nil
}

// DeleteEnv deletes environment variables from a repository.
func (r repository) DeleteEnv(ctx context.Context, in CreateEnvInput) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.repository.DeleteEnv")
	defer span.End()

	db := r.GetDB(ctx)

	var env entities.RepoEnv
	err := db.WithContext(ctx).
		Where("repo_id = ?", in.RepoID).
		First(&env).Error
	if err != nil {
		span.SetStatus(codes.Error, err.Error())
		span.RecordError(err)
		otelzap.ErrorWithContext(ctx, "failed to delete repository environment", err)
		return err
	}

	var envMap map[string]string
	if err := json.Unmarshal(env.Env, &envMap); err != nil {
		return err
	}
	delete(envMap, in.Key)

	updatedEnv, err := json.Marshal(envMap)
	if err != nil {
		return err
	}
	env.Env = updatedEnv

	if err := db.Save(&env).Error; err != nil {
		return err
	}

	span.AddEvent("successfully deleted repository environment variable")
	span.SetStatus(codes.Ok, "successfully deleted repository environment variable")
	return nil
}

// UpdateEnvInput contains the data needed to update environment variables.
type UpdateEnvInput struct {
	RepoID uuid.UUID
	OldKey string
	Key    string
	Value  string
}

// UpdateEnv updates the environment variables for a repository.
func (r repository) UpdateEnv(ctx context.Context, in UpdateEnvInput) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.repository.UpdateEnv")
	defer span.End()

	db := r.GetDB(ctx)

	var env entities.RepoEnv
	err := db.WithContext(ctx).
		Where("repo_id = ?", in.RepoID).
		First(&env).Error
	if err != nil {
		return err
	}

	var envMap map[string]string
	if err := json.Unmarshal(env.Env, &envMap); err != nil {
		return err
	}
	delete(envMap, in.OldKey)
	envMap[in.Key] = in.Value

	updatedEnv, err := json.Marshal(envMap)
	if err != nil {
		return err
	}
	env.Env = updatedEnv

	if err := db.Save(&env).Error; err != nil {
		return err
	}

	return nil
}
