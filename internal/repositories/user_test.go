package repository

import (
	"context"
	"testing"

	sqlmock "github.com/DATA-DOG/go-sqlmock"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"

	"api-server/internal/dto"
	"api-server/internal/enums"
)

func TestFindUserByID(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		userID := uuid.New()

		// Setup mock expectations
		mock.ExpectQuery(`SELECT users.\*, au.email AS email, au.email_confirmed_at AS email_confirmed_at FROM "public"."users" LEFT JOIN auth.users au ON users.id = au.id WHERE users.id = \$1 ORDER BY "users"."id" LIMIT \$2`).
			WithArgs(userID, 1).
			WillReturnRows(sqlmock.NewRows([]string{"id", "name", "username", "email", "email_confirmed_at"}).
				AddRow(userID, "Test User", "testuser", "<EMAIL>", nil))

		// Execute
		user, err := repo.FindUserByID(context.Background(), userID)

		// Assert
		assert.NoError(t, err)
		assert.NotNil(t, user)
		assert.Equal(t, userID, user.ID)
		assert.Equal(t, "Test User", user.Name)
		assert.Equal(t, "testuser", user.Username)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("not found", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		userID := uuid.New()

		// Setup mock expectations
		mock.ExpectQuery(`SELECT users.\*, au.email AS email, au.email_confirmed_at AS email_confirmed_at FROM "public"."users" LEFT JOIN auth.users au ON users.id = au.id WHERE users.id = \$1 ORDER BY "users"."id" LIMIT \$2`).
			WithArgs(userID, 1).
			WillReturnError(gorm.ErrRecordNotFound)

		// Execute
		user, err := repo.FindUserByID(context.Background(), userID)

		// Assert
		assert.Error(t, err)
		assert.Nil(t, user)
		assert.Equal(t, gorm.ErrRecordNotFound, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestFindAuthUser(t *testing.T) {
	t.Run("success by id", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		userID := uuid.New()
		filters := dto.AuthUserFilter{
			Id: &userID,
		}

		// Setup mock expectations
		mock.ExpectQuery(`SELECT \* FROM "auth"."users" WHERE id = \$1 ORDER BY "users"."id" LIMIT \$2`).
			WithArgs(userID, 1).
			WillReturnRows(sqlmock.NewRows([]string{"id", "email", "email_confirmed_at"}).
				AddRow(userID, "<EMAIL>", nil))

		// Execute
		user, err := repo.FindAuthUser(context.Background(), filters)

		// Assert
		assert.NoError(t, err)
		assert.NotNil(t, user)
		assert.Equal(t, userID, user.ID)
		assert.Equal(t, "<EMAIL>", user.Email)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("success by email", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		email := "<EMAIL>"
		filters := dto.AuthUserFilter{
			Email: &email,
		}

		// Setup mock expectations
		mock.ExpectQuery(`SELECT \* FROM "auth"."users" WHERE email = \$1 ORDER BY "users"."id" LIMIT \$2`).
			WithArgs(email, 1).
			WillReturnRows(sqlmock.NewRows([]string{"id", "email", "email_confirmed_at"}).
				AddRow(uuid.New(), email, nil))

		// Execute
		user, err := repo.FindAuthUser(context.Background(), filters)

		// Assert
		assert.NoError(t, err)
		assert.NotNil(t, user)
		assert.Equal(t, email, user.Email)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("not found", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		userID := uuid.New()
		filters := dto.AuthUserFilter{
			Id: &userID,
		}

		// Setup mock expectations
		mock.ExpectQuery(`SELECT \* FROM "auth"."users" WHERE id = \$1 ORDER BY "users"."id" LIMIT \$2`).
			WithArgs(userID, 1).
			WillReturnError(gorm.ErrRecordNotFound)

		// Execute
		user, err := repo.FindAuthUser(context.Background(), filters)

		// Assert
		assert.Error(t, err)
		assert.Nil(t, user)
		assert.Equal(t, gorm.ErrRecordNotFound, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestDeleteUser(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		userID := uuid.New()

		// Setup mock expectations
		mock.ExpectBegin()
		mock.ExpectExec(`DELETE FROM "public"."users" WHERE id = \$1`).
			WithArgs(userID).
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		// Execute
		err := repo.DeleteUser(context.Background(), userID)

		// Assert
		assert.NoError(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		userID := uuid.New()

		// Setup mock expectations
		mock.ExpectBegin()
		mock.ExpectExec(`DELETE FROM "public"."users" WHERE id = \$1`).
			WithArgs(userID).
			WillReturnError(gorm.ErrInvalidDB)
		mock.ExpectRollback()

		// Execute
		err := repo.DeleteUser(context.Background(), userID)

		// Assert
		assert.Error(t, err)
		assert.Equal(t, gorm.ErrInvalidDB, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestCountPrefixUsername(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		username := "testuser"

		// Setup mock expectations
		mock.ExpectQuery(`SELECT count\(\*\) FROM "users" WHERE users.username = \$1`).
			WithArgs(username).
			WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))

		// Execute
		count, err := repo.CountPrefixUsername(context.Background(), username)

		// Assert
		assert.NoError(t, err)
		assert.Equal(t, 1, count)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		username := "testuser"

		// Setup mock expectations
		mock.ExpectQuery(`SELECT count\(\*\) FROM "users" WHERE users.username = \$1`).
			WithArgs(username).
			WillReturnError(gorm.ErrInvalidDB)

		// Execute
		count, err := repo.CountPrefixUsername(context.Background(), username)

		// Assert
		assert.Error(t, err)
		assert.Equal(t, 0, count)
		assert.Equal(t, gorm.ErrInvalidDB, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestCheckCurrentPassword(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		userID := uuid.New()
		password := "current_password"

		// Setup mock expectations
		mock.ExpectQuery(`SELECT check_current_password\(\$1, \$2\)`).
			WithArgs(userID, password).
			WillReturnRows(sqlmock.NewRows([]string{"check_current_password"}).AddRow("true"))

		// Execute
		err := repo.CheckCurrentPassword(context.Background(), userID, password)

		// Assert
		assert.NoError(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		userID := uuid.New()
		password := "current_password"

		// Setup mock expectations
		mock.ExpectQuery(`SELECT check_current_password\(\$1, \$2\)`).
			WithArgs(userID, password).
			WillReturnError(gorm.ErrInvalidDB)

		// Execute
		err := repo.CheckCurrentPassword(context.Background(), userID, password)

		// Assert
		assert.Error(t, err)
		assert.Equal(t, gorm.ErrInvalidDB, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestGetAppPermission(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		userID := uuid.New()
		role := enums.UserRole_Admin

		// Setup mock expectations for user query
		mock.ExpectQuery(`SELECT \* FROM "public"."users" WHERE "users"."id" = \$1 ORDER BY "users"."id" LIMIT \$2`).
			WithArgs(userID, 1).
			WillReturnRows(sqlmock.NewRows([]string{"id", "role"}).
				AddRow(userID, role))

		// Setup mock expectations for permissions query
		mock.ExpectQuery(`SELECT \* FROM "app_permissions" WHERE "app_permissions"."role" = \$1`).
			WithArgs(role).
			WillReturnRows(sqlmock.NewRows([]string{"id", "role", "permission"}).
				AddRow(uuid.New(), role, "read").
				AddRow(uuid.New(), role, "write"))

		// Execute
		permissions, err := repo.GetAppPermission(context.Background(), userID)

		// Assert
		assert.NoError(t, err)
		assert.NotNil(t, permissions)
		assert.Len(t, permissions, 2)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("user not found", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		userID := uuid.New()

		// Setup mock expectations
		mock.ExpectQuery(`SELECT \* FROM "public"."users" WHERE "users"."id" = \$1 ORDER BY "users"."id" LIMIT \$2`).
			WithArgs(userID, 1).
			WillReturnError(gorm.ErrRecordNotFound)

		// Execute
		permissions, err := repo.GetAppPermission(context.Background(), userID)

		// Assert
		assert.Error(t, err)
		assert.Nil(t, permissions)
		assert.Equal(t, gorm.ErrRecordNotFound, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestGetOrgPermission(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		userID := uuid.New()
		orgID := uuid.New()
		role := enums.OrgRole_Owner

		// Setup mock expectations for org member query
		mock.ExpectQuery(`SELECT \* FROM "org_members" WHERE "org_members"."user_id" = \$1 AND "org_members"."org_id" = \$2 ORDER BY "org_members"."id" LIMIT \$3`).
			WithArgs(userID, orgID, 1).
			WillReturnRows(sqlmock.NewRows([]string{"id", "user_id", "org_id", "role"}).
				AddRow(uuid.New(), userID, orgID, role))

		// Setup mock expectations for permissions query
		mock.ExpectQuery(`SELECT \* FROM "repo_permissions" WHERE "repo_permissions"."role" = \$1`).
			WithArgs(enums.RepoRole_Owner).
			WillReturnRows(sqlmock.NewRows([]string{"id", "role", "permission"}).
				AddRow(uuid.New(), enums.RepoRole_Owner, "read").
				AddRow(uuid.New(), enums.RepoRole_Owner, "write"))

		// Execute
		permissions, err := repo.GetOrgPermission(context.Background(), userID, orgID)

		// Assert
		assert.NoError(t, err)
		assert.NotNil(t, permissions)
		assert.Len(t, permissions, 2)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("org member not found", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		userID := uuid.New()
		orgID := uuid.New()

		// Setup mock expectations
		mock.ExpectQuery(`SELECT \* FROM "org_members" WHERE "org_members"."user_id" = \$1 AND "org_members"."org_id" = \$2 ORDER BY "org_members"."id" LIMIT \$3`).
			WithArgs(userID, orgID, 1).
			WillReturnError(gorm.ErrRecordNotFound)

		// Execute
		permissions, err := repo.GetOrgPermission(context.Background(), userID, orgID)

		// Assert
		assert.Error(t, err)
		assert.Nil(t, permissions)
		assert.Equal(t, gorm.ErrRecordNotFound, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestGetRepoPermission(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		userID := uuid.New()
		repoID := uuid.New()
		role := enums.RepoRole_Developer

		// Setup mock expectations for repo member query
		mock.ExpectQuery(`SELECT \* FROM "repo_members" WHERE "repo_members"."user_id" = \$1 AND "repo_members"."repo_id" = \$2 ORDER BY "repo_members"."id" LIMIT \$3`).
			WithArgs(userID, repoID, 1).
			WillReturnRows(sqlmock.NewRows([]string{"id", "user_id", "repo_id", "role"}).
				AddRow(uuid.New(), userID, repoID, role))

		// Setup mock expectations for permissions query
		mock.ExpectQuery(`SELECT \* FROM "repo_permissions" WHERE "repo_permissions"."role" = \$1`).
			WithArgs(role).
			WillReturnRows(sqlmock.NewRows([]string{"id", "role", "permission"}).
				AddRow(uuid.New(), role, "read").
				AddRow(uuid.New(), role, "write"))

		// Execute
		permissions, err := repo.GetRepoPermission(context.Background(), userID, repoID)

		// Assert
		assert.NoError(t, err)
		assert.NotNil(t, permissions)
		assert.Len(t, permissions, 2)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("repo member not found", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		userID := uuid.New()
		repoID := uuid.New()

		// Setup mock expectations
		mock.ExpectQuery(`SELECT \* FROM "repo_members" WHERE "repo_members"."user_id" = \$1 AND "repo_members"."repo_id" = \$2 ORDER BY "repo_members"."id" LIMIT \$3`).
			WithArgs(userID, repoID, 1).
			WillReturnError(gorm.ErrRecordNotFound)

		// Execute
		permissions, err := repo.GetRepoPermission(context.Background(), userID, repoID)

		// Assert
		assert.Error(t, err)
		assert.Nil(t, permissions)
		assert.Equal(t, gorm.ErrRecordNotFound, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestCountUsers(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		input := ListUsersInput{
			Keyword: "test",
			Except: ExceptFilter{
				NotInRepId: nil,
				NotInOrgId: nil,
			},
		}

		// Setup mock expectations
		mock.ExpectQuery(`SELECT count\(\*\) FROM "users" LEFT JOIN auth.users au ON users.id = au.id WHERE users.ref_git_userid != \$1 AND \(users.name ILIKE \$2 OR users.username ILIKE \$3 OR au.email ILIKE \$4\)`).
			WithArgs(enums.AdminRefGitID, "%test%", "%test%", "%test%").
			WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(5))

		// Execute
		count, err := repo.CountUsers(context.Background(), input)

		// Assert
		assert.NoError(t, err)
		assert.Equal(t, int64(5), count)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		input := ListUsersInput{}

		// Setup mock expectations
		mock.ExpectQuery(`SELECT count\(\*\) FROM "users" LEFT JOIN auth.users au ON users.id = au.id WHERE users.ref_git_userid != \$1`).
			WithArgs(enums.AdminRefGitID).
			WillReturnError(gorm.ErrInvalidDB)

		// Execute
		count, err := repo.CountUsers(context.Background(), input)

		// Assert
		assert.Error(t, err)
		assert.Equal(t, int64(0), count)
		assert.Equal(t, gorm.ErrInvalidDB, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestListUsers(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		input := ListUsersInput{
			Paginate: PaginateRequest{
				Page:    1,
				PerPage: 10,
				OrderBy: "name",
				Sort:    "asc",
			},
			Keyword: "test",
		}

		// Setup mock expectations
		mock.ExpectQuery(`SELECT users.\*, au.email AS email, au.email_confirmed_at AS email_confirmed_at FROM "users" LEFT JOIN auth.users au ON users.id = au.id WHERE users.ref_git_userid != \$1 AND \(users.name ILIKE \$2 OR users.username ILIKE \$3 OR au.email ILIKE \$4\) ORDER BY name asc LIMIT \$5`).
			WithArgs(enums.AdminRefGitID, "%test%", "%test%", "%test%", 10).
			WillReturnRows(sqlmock.NewRows([]string{"id", "name", "username", "email", "email_confirmed_at"}).
				AddRow(uuid.New(), "Test User 1", "test1", "<EMAIL>", nil).
				AddRow(uuid.New(), "Test User 2", "test2", "<EMAIL>", nil))

		// Execute
		users, err := repo.ListUsers(context.Background(), input)

		// Assert
		assert.NoError(t, err)
		assert.NotNil(t, users)
		assert.Len(t, users, 2)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		input := ListUsersInput{}

		// Setup mock expectations
		mock.ExpectQuery(`SELECT users.\*, au.email AS email, au.email_confirmed_at AS email_confirmed_at FROM "users" LEFT JOIN auth.users au ON users.id = au.id WHERE users.ref_git_userid != \$1`).
			WithArgs(enums.AdminRefGitID).
			WillReturnError(gorm.ErrInvalidDB)

		// Execute
		users, err := repo.ListUsers(context.Background(), input)

		// Assert
		assert.Error(t, err)
		assert.Nil(t, users)
		assert.Equal(t, gorm.ErrInvalidDB, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}
