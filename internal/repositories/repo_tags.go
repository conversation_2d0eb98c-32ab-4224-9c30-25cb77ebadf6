package repository

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"api-server/internal/dto"
	"api-server/internal/entities"
	"api-server/pkg/oteltrace"

	"github.com/google/uuid"
	"github.com/lib/pq"
	"go.opentelemetry.io/otel/codes"
	"gorm.io/gorm"
)

// RepoTagsRepository defines the interface for repository tag operations.
// It provides methods for managing repository tags, including listing and finding
// tags with various filtering options.
type RepoTagsRepository interface {
	// ListRepoTags retrieves a list of repository tags based on filtering criteria.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - input: Criteria for listing tags including keyword, type, and subtype filters
	//
	// Returns:
	//   - []entities.Tag: List of tags matching the criteria
	//   - error: nil if successful, otherwise the error that occurred
	ListRepoTags(ctx context.Context, input dto.ListRepoTagsInput) ([]entities.Tag, error)

	// ListRepoTagsByQueryModel retrieves a list of repository tags based on a query model.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - input: Query model containing tag criteria
	//
	// Returns:
	//   - []entities.Tag: List of tags matching the query model
	//   - error: nil if successful, otherwise the error that occurred
	ListRepoTagsByQueryModel(ctx context.Context, input any) ([]entities.Tag, error)

	// FindRepoTag retrieves a specific repository tag based on filter criteria.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - filter: Filter criteria for finding the tag
	//
	// Returns:
	//   - *entities.Tag: The found tag if successful
	//   - error: nil if successful, otherwise the error that occurred
	FindRepoTag(ctx context.Context, filter FilterTagInput) (*entities.Tag, error)
}

var _ RepoTagsRepository = (*repository)(nil)

// ListRepoTags retrieves a list of repository tags based on filtering criteria.
func (r repository) ListRepoTags(ctx context.Context, input dto.ListRepoTagsInput) ([]entities.Tag, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.repo_tags.ListRepoTags")
	defer span.End()

	var result []entities.Tag

	query := r.GetDB(ctx).Table("tags")

	if input.Keyword != "" {
		keyword := fmt.Sprintf("%%%s%%", input.Keyword)
		query = query.Where("tags.name ILIKE ? OR tags.value ILIKE ?", keyword, keyword)
	}

	if input.Type != "" {
		query = query.Where("tags.type = ?", strings.TrimSpace(input.Type))
	}

	if input.SubType != "" {
		query = query.Where("tags.sub_type = ?", strings.TrimSpace(input.SubType))
	}

	if input.RepoType != "" {
		query = query.Where("tags.repo_types @> ?", pq.StringArray{strings.TrimSpace(input.RepoType)})
	}

	err := query.Find(&result).Error
	if err != nil {
		span.SetStatus(codes.Error, "failed to list repo tags")
		span.RecordError(err)
		return nil, err
	}

	span.AddEvent("repo tags listed successfully")
	span.SetStatus(codes.Ok, "repo tags listed successfully")
	return result, nil
}

// ListRepoTagsByQueryModel retrieves a list of repository tags based on a query model.
func (r repository) ListRepoTagsByQueryModel(ctx context.Context, input any) ([]entities.Tag, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.repo_tags.ListRepoTagsByQueryModel")
	defer span.End()

	model, _ := removeEmptyArrays(input)
	var result []entities.Tag

	queryTag := func(ctx context.Context, prefix, value string) (*entities.Tag, error) {
		query := r.GetDB(ctx).Table("tags").Where("tags.value = ?", fmt.Sprintf("%s:%s", prefix, value))
		var tag entities.Tag
		if err := query.First(&tag).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, nil
			}
			return nil, err
		}
		return &tag, nil
	}

	for key, value := range model {
		prefix := key
		switch v := value.(type) {
		case []any:
			for _, item := range v {
				if str, ok := item.(string); ok {
					tag, err := queryTag(ctx, prefix, str)
					if err != nil {
						span.SetStatus(codes.Error, "failed to query repo tags by query model when value type is []any")
						span.RecordError(err)
						return nil, err
					}
					if tag != nil {
						result = append(result, *tag)
					}
				}
			}
		case string:
			tag, err := queryTag(ctx, prefix, v)
			if err != nil {
				span.SetStatus(codes.Error, "failed to query repo tags by query model when value type is string")
				span.RecordError(err)
				return nil, err
			}
			if tag != nil {
				result = append(result, *tag)
			}
		}
	}

	span.AddEvent("repo tags queried by model successfully")
	span.SetStatus(codes.Ok, "repo tags queried by model successfully")
	return result, nil
}

// FilterTagInput contains the criteria for finding a repository tag.
type FilterTagInput struct {
	Id uuid.UUID
}

// FindRepoTag retrieves a specific repository tag based on filter criteria.
func (r repository) FindRepoTag(ctx context.Context, filter FilterTagInput) (*entities.Tag, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.repo_tags.FindRepoTag")
	defer span.End()

	var result entities.Tag

	query := r.GetDB(ctx).Table("tags")
	if filter.Id != uuid.Nil {
		query = query.Where("id = ?", filter.Id)
	}

	if err := query.First(&result).Error; err != nil {
		return nil, err
	}

	return &result, nil
}
