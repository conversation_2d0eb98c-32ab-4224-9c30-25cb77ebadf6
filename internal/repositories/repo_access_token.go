package repository

import (
	"context"
	"strings"
	"time"

	"github.com/google/uuid"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"

	"api-server/internal/entities"
	"api-server/internal/enums"
	"api-server/internal/types"
	"api-server/pkg/oteltrace"
	"api-server/pkg/otelzap"
)

// RepoAccessTokenRepository defines the interface for repository access token operations.
// It provides methods for managing repository access tokens, including creating,
// finding, listing, and removing tokens.
type RepoAccessTokenRepository interface {
	// CreateRepoAccessToken creates a new access token for a repository.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - in: Input data for creating the access token
	//
	// Returns:
	//   - *entities.RepoAccessToken: The created access token if successful
	//   - error: nil if successful, otherwise the error that occurred
	CreateRepoAccessToken(ctx context.Context, in CreateRepoAccessTokenInput) (*entities.RepoAccessToken, error)

	// ListRepoAccessToken retrieves a paginated list of repository access tokens.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - pagination: Pagination parameters
	//   - order: Ordering parameters
	//   - in: Query parameters for filtering tokens
	//
	// Returns:
	//   - []entities.RepoAccessToken: List of access tokens
	//   - error: nil if successful, otherwise the error that occurred
	ListRepoAccessToken(ctx context.Context, pagination types.Pagination, order types.OrderBy, in ListRepoAccessTokenQuery) ([]entities.RepoAccessToken, error)

	// DeleteRepoAccessToken removes a specific repository access token.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - in: Input data for deleting the access token
	//
	// Returns:
	//   - error: nil if successful, otherwise the error that occurred
	DeleteRepoAccessToken(ctx context.Context, in DeleteRepoAccessTokenInput) error

	// CountRepoAccessToken counts the total number of repository access tokens.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - in: Query parameters for filtering tokens
	//
	// Returns:
	//   - int64: Total number of tokens
	//   - error: nil if successful, otherwise the error that occurred
	CountRepoAccessToken(ctx context.Context, in ListRepoAccessTokenQuery) (int64, error)

	// FindRepoAccessToken retrieves a specific repository access token by ID.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - in: Query parameters for finding the token
	//
	// Returns:
	//   - *entities.RepoAccessToken: The found access token if successful
	//   - error: nil if successful, otherwise the error that occurred
	FindRepoAccessToken(ctx context.Context, in FindRepoAccessTokenQuery) (*entities.RepoAccessToken, error)
}

var _ RepoAccessTokenRepository = (*repository)(nil)

// CreateRepoAccessTokenInput contains the data needed to create a new repository access token.
type CreateRepoAccessTokenInput struct {
	Name        string
	AccessToken string
	Scopes      []enums.RepoAccessTokenScope
	RepoID      uuid.UUID
	ExpiresAt   time.Time
	RefGitID    int64
}

// CreateRepoAccessToken creates a new access token for a repository.
func (r *repository) CreateRepoAccessToken(ctx context.Context, in CreateRepoAccessTokenInput) (*entities.RepoAccessToken, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.repo_access_token.CreateRepoAccessToken",
		trace.WithAttributes(
			attribute.String("action", "CREATE_REPO_ACCESS_TOKEN"),
			attribute.String("repo_id", in.RepoID.String()),
			attribute.String("token_name", in.Name),
			// attribute.StringSlice("scopes", stringSlice), // stringSlice is already captured in stringScopes
			// attribute.String("expires_at", in.ExpiresAt.String()), // ExpiresAt is already captured in in.ExpiresAt
		))
	defer span.End()

	stringScopes := make([]string, len(in.Scopes))
	for i, scope := range in.Scopes {
		stringScopes[i] = string(scope)
	}
	stringSlice := stringScopes

	otelzap.InfoWithContext(ctx, "creating repository access token",
		zap.String("action", "CREATE_REPO_ACCESS_TOKEN"),
		zap.String("repo_id", in.RepoID.String()),
		zap.String("token_name", in.Name),
		zap.Strings("scopes", stringSlice),
		zap.Time("expires_at", in.ExpiresAt))

	data := entities.RepoAccessToken{
		Name:        in.Name,
		RepoID:      in.RepoID,
		AccessToken: in.AccessToken,
		ExpiresAt:   in.ExpiresAt,
		Revoked:     false,
		Scopes:      strings.Join(stringScopes, ","),
		RefGitID:    in.RefGitID,
	}

	err := r.GetDB(ctx).Create(&data).Error
	if err != nil {
		span.SetStatus(codes.Error, "failed to create repo access token")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "failed to create repo access token", err,
			zap.String("action", "CREATE_REPO_ACCESS_TOKEN"),
			zap.String("repo_id", in.RepoID.String()),
			zap.String("token_name", in.Name),
			zap.Strings("scopes", stringSlice),
			zap.Time("expires_at", in.ExpiresAt),
			zap.String("status", "failed"))
		return nil, err
	}

	span.AddEvent("repo access token created successfully")
	span.SetStatus(codes.Ok, "repo access token created successfully")
	span.SetAttributes(attribute.String("status", "success"))
	otelzap.InfoWithContext(ctx, "repo access token created successfully",
		zap.String("action", "CREATE_REPO_ACCESS_TOKEN"),
		zap.String("repo_id", in.RepoID.String()),
		zap.String("token_name", in.Name),
		zap.Strings("scopes", stringSlice),
		zap.Time("expires_at", in.ExpiresAt),
		zap.String("status", "success"))
	return &data, nil
}

// ListRepoAccessTokenQuery contains the criteria for listing repository access tokens.
type ListRepoAccessTokenQuery struct {
	RepoID uuid.UUID
}

// ListRepoAccessToken retrieves a paginated list of repository access tokens.
func (r *repository) ListRepoAccessToken(ctx context.Context, pagination types.Pagination, order types.OrderBy, in ListRepoAccessTokenQuery) ([]entities.RepoAccessToken, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.repo_access_token.ListRepoAccessToken",
		trace.WithAttributes(
			attribute.String("action", "LIST_REPO_ACCESS_TOKENS"),
			attribute.String("repo_id", in.RepoID.String()),
		))
	defer span.End()

	otelzap.InfoWithContext(ctx, "listing repository access tokens",
		zap.String("action", "LIST_REPO_ACCESS_TOKENS"),
		zap.String("repo_id", in.RepoID.String()))

	var result []entities.RepoAccessToken
	predicate := entities.RepoAccessToken{
		RepoID: in.RepoID,
	}

	query := r.GetDB(ctx)
	if pagination.PageNo != 0 && pagination.PageSize != 0 {
		query = query.Scopes(paginate(pagination.PageNo, pagination.PageSize))
	}

	if order != nil {
		query = query.Scopes(orderBy(order))
	}

	err := query.Where(&predicate).
		Find(&result).Error
	if err != nil {
		span.SetStatus(codes.Error, "failed to list repo access tokens")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "failed to list repo access tokens", err,
			zap.String("action", "LIST_REPO_ACCESS_TOKENS"),
			zap.String("repo_id", in.RepoID.String()),
			zap.String("status", "failed"))
		return nil, err
	}

	span.AddEvent("repo access tokens listed successfully")
	span.SetStatus(codes.Ok, "repo access tokens listed successfully")
	span.SetAttributes(attribute.String("status", "success"))
	otelzap.InfoWithContext(ctx, "repo access tokens listed successfully",
		zap.String("action", "LIST_REPO_ACCESS_TOKENS"),
		zap.String("repo_id", in.RepoID.String()),
		zap.String("status", "success"))
	return result, nil
}

// DeleteRepoAccessTokenInput contains the data needed to delete a repository access token.
type DeleteRepoAccessTokenInput struct {
	AccessTokenID uuid.UUID
	RepoID        uuid.UUID
}

// DeleteRepoAccessToken removes a specific repository access token.
func (r *repository) DeleteRepoAccessToken(ctx context.Context, in DeleteRepoAccessTokenInput) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.repo_access_token.DeleteAccessToken",
		trace.WithAttributes(
			attribute.String("action", "DELETE_REPO_ACCESS_TOKEN"),
			attribute.String("repo_id", in.RepoID.String()),
			attribute.String("access_token_id", in.AccessTokenID.String()),
		))
	defer span.End()

	otelzap.InfoWithContext(ctx, "deleting repository access token",
		zap.String("action", "DELETE_REPO_ACCESS_TOKEN"),
		zap.String("repo_id", in.RepoID.String()),
		zap.String("access_token_id", in.AccessTokenID.String()))

	token := entities.RepoAccessToken{
		BaseModel: entities.BaseModel{
			ID: in.AccessTokenID,
		},
		RepoID: in.RepoID,
	}
	err := r.GetDB(ctx).Delete(&token).Error
	if err != nil {
		span.SetStatus(codes.Error, "failed to delete repo access token")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "failed to delete repo access token", err,
			zap.String("action", "DELETE_REPO_ACCESS_TOKEN"),
			zap.String("repo_id", in.RepoID.String()),
			zap.String("access_token_id", in.AccessTokenID.String()),
			zap.String("status", "failed"))
		return err
	}

	span.AddEvent("repo access token deleted successfully")
	span.SetStatus(codes.Ok, "repo access token deleted successfully")
	span.SetAttributes(attribute.String("status", "success"))
	otelzap.InfoWithContext(ctx, "repo access token deleted successfully",
		zap.String("action", "DELETE_REPO_ACCESS_TOKEN"),
		zap.String("repo_id", in.RepoID.String()),
		zap.String("access_token_id", in.AccessTokenID.String()),
		zap.String("status", "success"))
	return nil
}

// CountRepoAccessToken counts the total number of repository access tokens.
func (r *repository) CountRepoAccessToken(ctx context.Context, in ListRepoAccessTokenQuery) (int64, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.repo_access_token.CountRepoAccessToken",
		trace.WithAttributes(
			attribute.String("action", "COUNT_REPO_ACCESS_TOKENS"),
			attribute.String("repo_id", in.RepoID.String()),
		))
	defer span.End()

	otelzap.InfoWithContext(ctx, "counting repository access tokens",
		zap.String("action", "COUNT_REPO_ACCESS_TOKENS"),
		zap.String("repo_id", in.RepoID.String()))

	var result int64
	predicate := entities.RepoAccessToken{
		RepoID: in.RepoID,
	}
	err := r.GetDB(ctx).Model(&entities.RepoAccessToken{}).Where(&predicate).Count(&result).Error
	if err != nil {
		span.SetStatus(codes.Error, "failed to count repo access tokens")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "failed to count repo access tokens", err,
			zap.String("action", "COUNT_REPO_ACCESS_TOKENS"),
			zap.String("repo_id", in.RepoID.String()),
			zap.String("status", "failed"))
		return 0, err
	}

	span.AddEvent("repo access tokens counted successfully")
	span.SetStatus(codes.Ok, "repo access tokens counted successfully")
	span.SetAttributes(attribute.String("status", "success"))
	otelzap.InfoWithContext(ctx, "repo access tokens counted successfully",
		zap.String("action", "COUNT_REPO_ACCESS_TOKENS"),
		zap.String("repo_id", in.RepoID.String()),
		zap.String("status", "success"))
	return result, nil
}

// FindRepoAccessTokenQuery contains the criteria for finding a repository access token.
type FindRepoAccessTokenQuery struct {
	ID uuid.UUID
}

// FindRepoAccessToken retrieves a specific repository access token by ID.
func (i *repository) FindRepoAccessToken(ctx context.Context, in FindRepoAccessTokenQuery) (*entities.RepoAccessToken, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.repo_access_token.FindRepoAccessToken",
		trace.WithAttributes(
			attribute.String("action", "FIND_REPO_ACCESS_TOKEN"),
			attribute.String("access_token_id", in.ID.String()),
		))
	defer span.End()

	otelzap.InfoWithContext(ctx, "finding repository access token",
		zap.String("action", "FIND_REPO_ACCESS_TOKEN"),
		zap.String("access_token_id", in.ID.String()))

	var result entities.RepoAccessToken
	predicate := entities.RepoAccessToken{
		BaseModel: entities.BaseModel{
			ID: in.ID,
		},
	}
	err := i.GetDB(ctx).Where(&predicate).First(&result).Error
	if err != nil {
		span.SetStatus(codes.Error, "failed to find repo access token")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "failed to find repo access token", err,
			zap.String("action", "FIND_REPO_ACCESS_TOKEN"),
			zap.String("access_token_id", in.ID.String()),
			zap.String("status", "failed"))
		return nil, err
	}

	span.AddEvent("repo access token found successfully")
	span.SetStatus(codes.Ok, "repo access token found successfully")
	span.SetAttributes(attribute.String("status", "success"))
	otelzap.InfoWithContext(ctx, "repo access token found successfully",
		zap.String("action", "FIND_REPO_ACCESS_TOKEN"),
		zap.String("access_token_id", in.ID.String()),
		zap.String("status", "success"))
	return &result, nil
}
