package repository

import (
	"api-server/internal/dto"
	"api-server/internal/types"
	"context"
	"fmt"

	"github.com/google/uuid"
	"go.opentelemetry.io/otel/codes"

	"api-server/internal/entities"
	"api-server/pkg/oteltrace"
)

// OrganizationRepository defines the interface for organization operations.
// It provides methods for managing organizations, including finding and listing
// organizations with various filtering options.
type OrganizationRepository interface {
	// FindOrganizationByID retrieves a specific organization by its ID.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - id: UUID of the organization to find
	//
	// Returns:
	//   - *entities.Organization: The found organization if successful
	//   - error: nil if successful, otherwise the error that occurred
	FindOrganizationByID(ctx context.Context, id uuid.UUID) (*entities.Organization, error)

	// FindOrganization retrieves a specific organization based on filter criteria.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - input: Filter criteria for finding the organization
	//
	// Returns:
	//   - *entities.Organization: The found organization if successful
	//   - error: nil if successful, otherwise the error that occurred
	FindOrganization(ctx context.Context, input FilterOrganization) (*entities.Organization, error)

	// ListOrganizations retrieves a paginated list of organizations.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - input: Criteria for listing organizations including pagination and filtering
	//
	// Returns:
	//   - int: Total number of organizations
	//   - []entities.Organization: List of organizations
	//   - error: nil if successful, otherwise the error that occurred
	ListOrganizations(ctx context.Context, input dto.ListOrganizationsInput) (int, []entities.Organization, error)
}

var _ OrganizationRepository = (*repository)(nil)

// FindOrganizationByID retrieves a specific organization by its ID.
func (r repository) FindOrganizationByID(ctx context.Context, id uuid.UUID) (*entities.Organization, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.organization.FindOrganizationByID")
	defer span.End()

	db := r.GetDB(ctx)
	var organization entities.Organization
	err := db.Where("id = ?", id).First(&organization).Error
	if err != nil {
		span.SetStatus(codes.Error, "failed to find organization by ID")
		span.RecordError(err)
		return nil, err
	}

	span.AddEvent("organization found by ID")
	span.SetStatus(codes.Ok, "organization found by ID")
	return &organization, nil
}

// ListOrganizations retrieves a paginated list of organizations.
func (r repository) ListOrganizations(ctx context.Context, input dto.ListOrganizationsInput) (int, []entities.Organization, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.organization.ListOrganizations")
	defer span.End()

	var orgIDs []uuid.UUID
	orgUserQuery := r.GetDB(ctx).WithContext(ctx).Table("organizations").Select("organizations.id").
		Joins("LEFT JOIN org_members ON org_members.org_id = organizations.id").
		Distinct()
	if input.UserId != uuid.Nil {
		orgUserQuery = orgUserQuery.Where("user_id = ?", input.UserId)
	}

	if input.Role != "" {
		orgUserQuery = orgUserQuery.Where("role = ?", input.Role)
	}

	if input.Keyword != "" {
		keyword := fmt.Sprintf("%%%s%%", input.Keyword)
		orgUserQuery = orgUserQuery.Where("organizations.name ILIKE ?", keyword)
	}

	if err := orgUserQuery.Find(&orgIDs).Error; err != nil {
		span.SetStatus(codes.Error, "failed to find organization IDs")
		span.RecordError(err)
		return 0, nil, err
	}

	orgQuery := r.GetDB(ctx).WithContext(ctx).Table("organizations").Where("id IN ?", orgIDs)
	if input.Paginate.Page != 0 && input.Paginate.PerPage != 0 {
		orgQuery = orgQuery.Scopes(paginate(input.Paginate.Page, input.Paginate.PerPage))
	}

	if input.Paginate.OrderBy != "" && input.Paginate.Sort != "" {
		orgQuery = orgQuery.Scopes(orderBy(types.OrderBy{input.Paginate.OrderBy: input.Paginate.Sort}))
	}

	var orgs []entities.Organization
	if err := orgQuery.Find(&orgs).Error; err != nil {
		span.SetStatus(codes.Error, "failed to list organizations")
		span.RecordError(err)
		return 0, nil, err
	}

	span.AddEvent("organizations listed")
	span.SetStatus(codes.Ok, "organizations listed")
	return len(orgIDs), orgs, nil
}

// FilterOrganization contains the criteria for finding an organization.
type FilterOrganization struct {
	OrgId    *uuid.UUID `json:"org_id" form:"org_id"`
	Name     *string    `json:"name"`
	PathName *string    `json:"path_name"`
}

// FindOrganization retrieves a specific organization based on filter criteria.
func (r repository) FindOrganization(ctx context.Context, input FilterOrganization) (*entities.Organization, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.organization.FindOrganization")
	defer span.End()

	db := r.GetDB(ctx)
	var org entities.Organization
	if input.OrgId != nil {
		db = db.Where("id = ?", input.OrgId)
	}

	if input.Name != nil && input.PathName != nil {
		db = db.Where("name = ?", *input.Name).Or("path_name = ?", *input.PathName)
	}

	err := db.First(&org).Error
	if err != nil {
		span.SetStatus(codes.Error, "failed to find organization")
		span.RecordError(err)
		return nil, err
	}

	span.AddEvent("organization found")
	span.SetStatus(codes.Ok, "organization found")
	return &org, nil
}
