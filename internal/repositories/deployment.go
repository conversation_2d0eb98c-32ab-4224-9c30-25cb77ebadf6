package repository

import (
	"context"

	"github.com/google/uuid"
	"go.opentelemetry.io/otel/codes"
	"gorm.io/gorm/clause"

	"api-server/internal/entities"
	"api-server/internal/enums"
	"api-server/internal/types"
	"api-server/pkg/oteltrace"
)

// DeploymentRepository defines the interface for deployment operations.
// It provides methods for managing deployments, including creating, updating,
// finding, listing, and removing deployments.
type DeploymentRepository interface {
	// CreateDeployment creates a new deployment.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - in: Input data for creating the deployment
	//
	// Returns:
	//   - *entities.Deployment: The created deployment if successful
	//   - error: nil if successful, otherwise the error that occurred
	CreateDeployment(ctx context.Context, in CreateDeploymentInput) (*entities.Deployment, error)

	// UpsertDeployment creates or updates a deployment.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - in: Input data for upserting the deployment
	//
	// Returns:
	//   - *entities.Deployment: The upserted deployment if successful
	//   - error: nil if successful, otherwise the error that occurred
	UpsertDeployment(ctx context.Context, in CreateDeploymentInput) (*entities.Deployment, error)

	// UpdateDeployment updates an existing deployment.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - in: Input data for updating the deployment
	//
	// Returns:
	//   - *entities.Deployment: The updated deployment if successful
	//   - error: nil if successful, otherwise the error that occurred
	UpdateDeployment(ctx context.Context, in UpdateDeploymentInput) (*entities.Deployment, error)

	// FindDeployment retrieves a specific deployment based on repository ID and optional clauses.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - repoID: ID of the repository
	//   - clauses: Optional GORM clauses for filtering
	//
	// Returns:
	//   - *entities.Deployment: The found deployment if successful
	//   - error: nil if successful, otherwise the error that occurred
	FindDeployment(ctx context.Context, repoID uuid.UUID, clauses ...clause.Expression) (*entities.Deployment, error)

	// ListDeployment retrieves a paginated list of deployments.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - pagination: Pagination parameters
	//   - order: Ordering parameters
	//   - in: Input data for filtering deployments
	//
	// Returns:
	//   - []entities.Deployment: List of deployments
	//   - error: nil if successful, otherwise the error that occurred
	ListDeployment(ctx context.Context, pagination types.Pagination, order types.OrderBy, in ListDeploymentInput) ([]entities.Deployment, error)

	// CountDeployment counts the total number of deployments.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - in: Input data for filtering deployments
	//
	// Returns:
	//   - int64: Total number of deployments
	//   - error: nil if successful, otherwise the error that occurred
	CountDeployment(ctx context.Context, in ListDeploymentInput) (int64, error)

	// DeleteDeploymentsByRepoID removes all deployments for a repository.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - repoID: ID of the repository
	//
	// Returns:
	//   - error: nil if successful, otherwise the error that occurred
	DeleteDeploymentsByRepoID(ctx context.Context, repoID uuid.UUID) error

	// RemoveUserInDeployments removes a user from specified deployments.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - in: Input data for removing the user from deployments
	//
	// Returns:
	//   - error: nil if successful, otherwise the error that occurred
	RemoveUserInDeployments(ctx context.Context, in RemoveUserInDeploymentsInput) error
}

var _ DeploymentRepository = (*repository)(nil)

// CreateDeploymentInput contains the data needed to create a new deployment.
type CreateDeploymentInput struct {
	Name         string
	UserID       uuid.UUID
	WorkflowName string
	RepoID       uuid.UUID
	Revision     string
	Status       enums.ArgoWorkflowStatus
	Commit       string
	ResourceName string
	// HardwareID   uuid.UUID
}

func (r *repository) CreateDeployment(ctx context.Context, in CreateDeploymentInput) (*entities.Deployment, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.deployment.CreateDeployment")
	defer span.End()

	data := entities.Deployment{
		Name:         in.Name,
		UserID:       &in.UserID,
		Status:       in.Status,
		WorkflowName: in.WorkflowName,
		RepoID:       in.RepoID,
		Revision:     in.Revision,
		Commit:       in.Commit,
		// HardwareID:   in.HardwareID,
	}

	err := r.GetDB(ctx).Create(&data).Error
	if err != nil {
		span.SetStatus(codes.Error, err.Error())
		span.RecordError(err)
		return nil, err
	}

	return &data, nil
}

func (r *repository) UpsertDeployment(ctx context.Context, in CreateDeploymentInput) (*entities.Deployment, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.deployment.UpsertDeployment")
	defer span.End()

	data := entities.Deployment{
		Name:         in.Name,
		UserID:       &in.UserID,
		Status:       in.Status,
		WorkflowName: in.WorkflowName,
		RepoID:       in.RepoID,
		Revision:     in.Revision,
		Commit:       in.Commit,
		// HardwareID:   in.HardwareID,
	}

	err := r.GetDB(ctx).
		Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "repo_id"}},
			UpdateAll: true,
		}).
		Create(&data).Error
	if err != nil {
		span.SetStatus(codes.Error, err.Error())
		span.RecordError(err)
		return nil, err
	}

	return &data, nil
}

// UpdateDeploymentInput contains the data needed to update a deployment.
type UpdateDeploymentInput struct {
	WorkflowName string
	Status       enums.ArgoWorkflowStatus
	Duration     float32
}

func (r *repository) UpdateDeployment(ctx context.Context, in UpdateDeploymentInput) (*entities.Deployment, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.deployment.UpdateDeployment")
	defer span.End()

	data := entities.Deployment{
		Status:   in.Status,
		Duration: in.Duration,
	}

	err := r.GetDB(ctx).
		Model(&data).
		Where("workflow_name = ?", in.WorkflowName).
		Updates(data).
		Error
	if err != nil {
		span.SetStatus(codes.Error, err.Error())
		span.RecordError(err)
		return nil, err
	}

	return &data, nil
}

// FindDeployment retrieves a specific deployment based on repository ID and optional clauses.
func (r *repository) FindDeployment(ctx context.Context, repoID uuid.UUID, clauses ...clause.Expression) (*entities.Deployment, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.deployment.FindDeployment")
	defer span.End()

	var result entities.Deployment
	predicate := entities.Deployment{
		RepoID: repoID,
	}

	err := r.
		GetDB(ctx).
		Clauses(clauses...).
		Where(&predicate).
		First(&result).Error
	if err != nil {
		span.SetStatus(codes.Error, err.Error())
		span.RecordError(err)
		return nil, err
	}

	return &result, nil
}

// ListDeploymentInput contains the criteria for listing deployments.
type ListDeploymentInput struct {
	Keyword string
	RepoID  uuid.UUID
}

// ListDeployment retrieves a paginated list of deployments.
func (r *repository) ListDeployment(ctx context.Context, pagination types.Pagination, order types.OrderBy, in ListDeploymentInput) ([]entities.Deployment, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.deployment.ListDeployment")
	defer span.End()

	var result []entities.Deployment
	predicate := entities.Deployment{
		// RepoID: in.RepoID,
	}
	err := r.
		GetDB(ctx).
		Preload("User").
		Preload("Repo").
		Preload("Repo.User").
		Preload("Repo.Org").
		Preload("Repo.Hardware").
		// Joins("LEFT JOIN public.users users ON deployments.user_id = users.id").
		Scopes(paginate(pagination.PageNo, pagination.PageSize), orderBy(order)).
		Where(&predicate).
		Find(&result).Error
	if err != nil {
		span.SetStatus(codes.Error, err.Error())
		span.RecordError(err)
		return nil, err
	}

	return result, nil
}

// CountDeployment counts the total number of deployments.
func (r *repository) CountDeployment(ctx context.Context, in ListDeploymentInput) (int64, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.deployment.CountDeployment")
	defer span.End()

	var result int64
	predicate := entities.Deployment{
		// RepoID: in.RepoID,
	}
	err := r.GetDB(ctx).Model(&entities.Deployment{}).Where(&predicate).Count(&result).Error
	if err != nil {
		span.SetStatus(codes.Error, err.Error())
		span.RecordError(err)
		return 0, err
	}

	return result, nil
}

// DeleteDeploymentsByRepoID removes all deployments for a repository.
func (r *repository) DeleteDeploymentsByRepoID(ctx context.Context, repoID uuid.UUID) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.deployment.DeleteDeploymentsRepoID")
	defer span.End()

	if err := r.GetDB(ctx).Where("repo_id = ?", repoID).Delete(&entities.Deployment{}).Error; err != nil {
		span.SetStatus(codes.Error, err.Error())
		span.RecordError(err)
		return err
	}

	return nil
}

// RemoveUserInDeploymentsInput contains the data needed to remove a user from deployments.
type RemoveUserInDeploymentsInput struct {
	RepoIDs []uuid.UUID
}

// RemoveUserInDeployments removes a user from specified deployments.
func (r *repository) RemoveUserInDeployments(ctx context.Context, in RemoveUserInDeploymentsInput) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.deployment.RemoveUserInDeployments")
	defer span.End()

	query := r.GetDB(ctx)

	if len(in.RepoIDs) > 0 {
		query = query.Where("repo_id IN ?", in.RepoIDs)
	}

	if err := query.Model(&entities.Deployment{}).Update("user_id", nil).Error; err != nil {
		span.SetStatus(codes.Error, err.Error())
		span.RecordError(err)
		return err
	}

	return nil
}
