package repository

import (
	"context"
	"database/sql"
	"testing"
	"time"

	sqlmock "github.com/DATA-DOG/go-sqlmock"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
)

func TestListTemplates(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		// Setup
		db, mock := setupTestDB(t)
		repo := &repository{db: db}
		now := time.Now()

		// Mock the expected SQL query
		rows := sqlmock.NewRows([]string{"id", "type", "name", "icon", "git_ref_id", "created_at", "updated_at"}).
			AddRow(uuid.New(), "model", "template1", "icon1", 1, now, now).
			AddRow(uuid.New(), "dataset", "template2", "icon2", 2, now, now)
		mock.ExpectQuery(`SELECT \* FROM "templates"`).
			WillReturnRows(rows)

		// Execute
		result, err := repo.ListTemplates(context.Background())

		// Assert
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Len(t, result, 2)
		assert.Equal(t, "model", result[0].Type)
		assert.Equal(t, "template1", result[0].Name)
		assert.Equal(t, "icon1", result[0].Icon)
		assert.Equal(t, int64(1), result[0].GitRefId)
		assert.Equal(t, "dataset", result[1].Type)
		assert.Equal(t, "template2", result[1].Name)
		assert.Equal(t, "icon2", result[1].Icon)
		assert.Equal(t, int64(2), result[1].GitRefId)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		// Setup
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Mock the expected SQL query with error
		mock.ExpectQuery(`SELECT \* FROM "templates"`).
			WillReturnError(sql.ErrConnDone)

		// Execute
		result, err := repo.ListTemplates(context.Background())

		// Assert
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestFindTemplate(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		// Setup
		db, mock := setupTestDB(t)
		repo := &repository{db: db}
		templateID := uuid.New()
		now := time.Now()

		// Mock the expected SQL query
		rows := sqlmock.NewRows([]string{"id", "type", "name", "icon", "git_ref_id", "created_at", "updated_at"}).
			AddRow(templateID, "model", "template1", "icon1", 1, now, now)
		mock.ExpectQuery(`SELECT \* FROM "templates" WHERE "templates"."id" = \$1`).
			WithArgs(templateID).
			WillReturnRows(rows)

		// Execute
		result, err := repo.FindTemplate(context.Background(), templateID)

		// Assert
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, templateID, result.ID)
		assert.Equal(t, "model", result.Type)
		assert.Equal(t, "template1", result.Name)
		assert.Equal(t, "icon1", result.Icon)
		assert.Equal(t, int64(1), result.GitRefId)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("not found", func(t *testing.T) {
		// Setup
		db, mock := setupTestDB(t)
		repo := &repository{db: db}
		templateID := uuid.New()

		// Mock the expected SQL query with no rows
		mock.ExpectQuery(`SELECT \* FROM "templates" WHERE "templates"."id" = \$1`).
			WithArgs(templateID).
			WillReturnError(gorm.ErrRecordNotFound)

		// Execute
		result, err := repo.FindTemplate(context.Background(), templateID)

		// Assert
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Equal(t, gorm.ErrRecordNotFound, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		// Setup
		db, mock := setupTestDB(t)
		repo := &repository{db: db}
		templateID := uuid.New()

		// Mock the expected SQL query with error
		mock.ExpectQuery(`SELECT \* FROM "templates" WHERE "templates"."id" = \$1`).
			WithArgs(templateID).
			WillReturnError(sql.ErrConnDone)

		// Execute
		result, err := repo.FindTemplate(context.Background(), templateID)

		// Assert
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}
