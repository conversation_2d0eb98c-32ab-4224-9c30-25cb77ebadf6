package repository

import (
	"api-server/internal/utils"
	"context"
	"database/sql"
	"testing"
	"time"

	sqlmock "github.com/DATA-DOG/go-sqlmock"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
)

func TestCreateUserGitGroup(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		// Setup
		db, mock := setupTestDB(t)
		repo := &repository{db: db}
		userID := uuid.New()
		input := CreateUserGitGroupInput{
			UserID:           userID,
			RefGitModelsID:   1,
			RefGitSpacesID:   2,
			RefGitDatasetsID: 3,
			RefGitComposesID: utils.Ptr(int64(4)),
		}

		// Mock the expected SQL query
		mock.ExpectBegin()
		mock.ExpectQuery(`INSERT INTO "user_git_groups" \("created_at","updated_at","ref_git_models_id","ref_git_spaces_id","ref_git_datasets_id","ref_git_composes_id","user_id","id"\) VALUES \(\$1,\$2,\$3,\$4,\$5,\$6,\$7,\$8\) RETURNING "id"`).
			WithArgs(
				sqlmock.AnyArg(), // created_at
				sqlmock.AnyArg(), // updated_at
				1,                // ref_git_models_id
				2,                // ref_git_spaces_id
				3,                // ref_git_datasets_id
				4,                // ref_git_composes_id
				userID,           // user_id
				sqlmock.AnyArg(), // id (let GORM generate this)
			).
			WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(uuid.New()))
		mock.ExpectCommit()

		// Execute
		result, err := repo.CreateUserGitGroup(context.Background(), input)

		// Assert
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, int64(1), result.RefGitModelsID)
		assert.Equal(t, int64(2), result.RefGitSpacesID)
		assert.Equal(t, int64(3), result.RefGitDatasetsID)
		assert.Equal(t, int64(4), result.RefGitComposesID)
		assert.NotEmpty(t, result.ID)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		// Setup
		db, mock := setupTestDB(t)
		repo := &repository{db: db}
		userID := uuid.New()
		input := CreateUserGitGroupInput{
			UserID:           userID,
			RefGitModelsID:   1,
			RefGitSpacesID:   2,
			RefGitDatasetsID: 3,
			RefGitComposesID: utils.Ptr(int64(4)),
		}

		// Mock the expected SQL query with error
		mock.ExpectBegin()
		mock.ExpectQuery(`INSERT INTO "user_git_groups" \("created_at","updated_at","ref_git_models_id","ref_git_spaces_id","ref_git_datasets_id","ref_git_composes_id","user_id","id"\) VALUES \(\$1,\$2,\$3,\$4,\$5,\$6,\$7,\$8\) RETURNING "id"`).
			WithArgs(
				sqlmock.AnyArg(), // created_at
				sqlmock.AnyArg(), // updated_at
				1,                // ref_git_models_id
				2,                // ref_git_spaces_id
				3,                // ref_git_datasets_id
				4,                // ref_git_composes_id
				userID,           // user_id
				sqlmock.AnyArg(), // id (let GORM generate this)
			).
			WillReturnError(sql.ErrConnDone)
		mock.ExpectRollback()

		// Execute
		result, err := repo.CreateUserGitGroup(context.Background(), input)

		// Assert
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestFindUserGitGroup(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		// Setup
		db, mock := setupTestDB(t)
		repo := &repository{db: db}
		userID := uuid.New()

		// Mock the expected SQL query
		now := time.Now()
		rows := sqlmock.NewRows([]string{"id", "user_id", "ref_git_models_id", "ref_git_spaces_id", "ref_git_datasets_id", "ref_git_composes_id", "created_at", "updated_at"}).
			AddRow(uuid.New(), userID, 1, 2, 3, 4, now, now)
		mock.ExpectQuery(`SELECT \* FROM "user_git_groups" WHERE "user_git_groups"."user_id" = \$1 ORDER BY "user_git_groups"."ref_git_models_id" LIMIT \$2`).
			WithArgs(userID, 1).
			WillReturnRows(rows)

		// Execute
		result, err := repo.FindUserGitGroup(context.Background(), userID)

		// Assert
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, int64(1), result.RefGitModelsID)
		assert.Equal(t, int64(2), result.RefGitSpacesID)
		assert.Equal(t, int64(3), result.RefGitDatasetsID)
		assert.Equal(t, int64(4), result.RefGitComposesID)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("not found", func(t *testing.T) {
		// Setup
		db, mock := setupTestDB(t)
		repo := &repository{db: db}
		userID := uuid.New()

		// Mock the expected SQL query with no rows
		mock.ExpectQuery(`SELECT \* FROM "user_git_groups" WHERE "user_git_groups"."user_id" = \$1 ORDER BY "user_git_groups"."ref_git_models_id" LIMIT \$2`).
			WithArgs(userID, 1).
			WillReturnRows(sqlmock.NewRows([]string{"id", "user_id", "ref_git_models_id", "ref_git_spaces_id", "ref_git_datasets_id", "ref_git_composes_id", "created_at", "updated_at"}))

		// Execute
		result, err := repo.FindUserGitGroup(context.Background(), userID)

		// Assert
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Equal(t, gorm.ErrRecordNotFound, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestDeleteUserGitGroup(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		// Setup
		db, mock := setupTestDB(t)
		repo := &repository{db: db}
		userID := uuid.New()

		// Mock the expected SQL query
		mock.ExpectBegin()
		mock.ExpectExec(`DELETE FROM "user_git_groups" WHERE user_id = \$1`).
			WithArgs(userID).
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		// Execute
		err := repo.DeleteUserGitGroup(context.Background(), userID)

		// Assert
		assert.NoError(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		// Setup
		db, mock := setupTestDB(t)
		repo := &repository{db: db}
		userID := uuid.New()

		// Mock the expected SQL query with error
		mock.ExpectBegin()
		mock.ExpectExec(`DELETE FROM "user_git_groups" WHERE user_id = \$1`).
			WithArgs(userID).
			WillReturnError(sql.ErrConnDone)
		mock.ExpectRollback()

		// Execute
		err := repo.DeleteUserGitGroup(context.Background(), userID)

		// Assert
		assert.Error(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}
