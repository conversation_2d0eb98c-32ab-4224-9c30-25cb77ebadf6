package repository

import (
	"context"
	"database/sql"
	"testing"
	"time"

	sqlmock "github.com/DATA-DOG/go-sqlmock"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"

	"api-server/internal/entities"
)

func TestUpsertSignupRequest(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		// Setup
		db, mock := setupTestDB(t)
		repo := &repository{db: db}
		now := time.Now()
		input := &entities.SignupRequest{
			BaseModel: entities.BaseModel{
				ID:        uuid.New(),
				CreatedAt: now,
				UpdatedAt: now,
			},
			Email:    "<EMAIL>",
			Password: "hashed_password",
			Name:     "Test User",
			Username: "testuser",
		}

		// Mock the expected SQL queries
		// First query checks for existing email/username
		mock.ExpectQuery(`SELECT \* FROM "signup_requests" WHERE email = \$1 OR username = \$2 ORDER BY "signup_requests"."id" LIMIT \$3`).
			WithArgs(input.Email, input.Username, 1).
			WillReturnError(gorm.ErrRecordNotFound)

		// Second query creates the new record
		mock.ExpectBegin()
		mock.ExpectQuery(`INSERT INTO "signup_requests" \("created_at","updated_at","email","password","name","username","id"\) VALUES \(\$1,\$2,\$3,\$4,\$5,\$6,\$7\) RETURNING "id"`).
			WithArgs(now, now, input.Email, input.Password, input.Name, input.Username, input.ID).
			WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(input.ID))
		mock.ExpectCommit()

		// Execute
		err := repo.UpsertSignupRequest(context.Background(), input)

		// Assert
		assert.NoError(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("email already exists", func(t *testing.T) {
		// Setup
		db, mock := setupTestDB(t)
		repo := &repository{db: db}
		now := time.Now()
		input := &entities.SignupRequest{
			BaseModel: entities.BaseModel{
				ID:        uuid.New(),
				CreatedAt: now,
				UpdatedAt: now,
			},
			Email:    "<EMAIL>",
			Password: "hashed_password",
			Name:     "Test User",
			Username: "testuser",
		}

		// Mock the expected SQL query
		rows := sqlmock.NewRows([]string{"id", "email", "username"}).
			AddRow(uuid.New(), input.Email, "otheruser")
		mock.ExpectQuery(`SELECT \* FROM "signup_requests" WHERE email = \$1 OR username = \$2 ORDER BY "signup_requests"."id" LIMIT \$3`).
			WithArgs(input.Email, input.Username, 1).
			WillReturnRows(rows)

		// Execute
		err := repo.UpsertSignupRequest(context.Background(), input)

		// Assert
		assert.Error(t, err)
		assert.Equal(t, "email already exists", err.Error())
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("username already exists", func(t *testing.T) {
		// Setup
		db, mock := setupTestDB(t)
		repo := &repository{db: db}
		now := time.Now()
		input := &entities.SignupRequest{
			BaseModel: entities.BaseModel{
				ID:        uuid.New(),
				CreatedAt: now,
				UpdatedAt: now,
			},
			Email:    "<EMAIL>",
			Password: "hashed_password",
			Name:     "Test User",
			Username: "testuser",
		}

		// Mock the expected SQL query
		rows := sqlmock.NewRows([]string{"id", "email", "username"}).
			AddRow(uuid.New(), "<EMAIL>", input.Username)
		mock.ExpectQuery(`SELECT \* FROM "signup_requests" WHERE email = \$1 OR username = \$2 ORDER BY "signup_requests"."id" LIMIT \$3`).
			WithArgs(input.Email, input.Username, 1).
			WillReturnRows(rows)

		// Execute
		err := repo.UpsertSignupRequest(context.Background(), input)

		// Assert
		assert.Error(t, err)
		assert.Equal(t, "username already exists", err.Error())
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		// Setup
		db, mock := setupTestDB(t)
		repo := &repository{db: db}
		now := time.Now()
		input := &entities.SignupRequest{
			BaseModel: entities.BaseModel{
				ID:        uuid.New(),
				CreatedAt: now,
				UpdatedAt: now,
			},
			Email:    "<EMAIL>",
			Password: "hashed_password",
			Name:     "Test User",
			Username: "testuser",
		}

		// Mock the expected SQL query with error
		mock.ExpectQuery(`SELECT \* FROM "signup_requests" WHERE email = \$1 OR username = \$2 ORDER BY "signup_requests"."id" LIMIT \$3`).
			WithArgs(input.Email, input.Username, 1).
			WillReturnError(sql.ErrConnDone)

		// Execute
		err := repo.UpsertSignupRequest(context.Background(), input)

		// Assert
		assert.Error(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestListSignUpRequests(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		// Setup
		db, mock := setupTestDB(t)
		repo := &repository{db: db}
		now := time.Now()

		// Mock the expected SQL query
		rows := sqlmock.NewRows([]string{"id", "email", "password", "name", "username", "created_at", "updated_at"}).
			AddRow(uuid.New(), "<EMAIL>", "pass1", "User 1", "user1", now, now).
			AddRow(uuid.New(), "<EMAIL>", "pass2", "User 2", "user2", now, now)
		mock.ExpectQuery(`SELECT \* FROM "signup_requests"`).
			WillReturnRows(rows)

		// Execute
		result, err := repo.ListSignUpRequests(context.Background())

		// Assert
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Len(t, result, 2)
		assert.Equal(t, "<EMAIL>", result[0].Email)
		assert.Equal(t, "<EMAIL>", result[1].Email)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		// Setup
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Mock the expected SQL query with error
		mock.ExpectQuery(`SELECT \* FROM "signup_requests"`).
			WillReturnError(sql.ErrConnDone)

		// Execute
		result, err := repo.ListSignUpRequests(context.Background())

		// Assert
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestGetSignUpRequestByID(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		// Setup
		db, mock := setupTestDB(t)
		repo := &repository{db: db}
		requestID := uuid.New()
		now := time.Now()

		// Mock the expected SQL query
		rows := sqlmock.NewRows([]string{"id", "email", "password", "name", "username", "created_at", "updated_at"}).
			AddRow(requestID, "<EMAIL>", "hashed_password", "Test User", "testuser", now, now)
		mock.ExpectQuery(`SELECT \* FROM "signup_requests" WHERE id = \$1 ORDER BY "signup_requests"."id" LIMIT \$2`).
			WithArgs(requestID, 1).
			WillReturnRows(rows)

		// Execute
		result, err := repo.GetSignUpRequestByID(context.Background(), requestID)

		// Assert
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, requestID, result.ID)
		assert.Equal(t, "<EMAIL>", result.Email)
		assert.Equal(t, "Test User", result.Name)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("not found", func(t *testing.T) {
		// Setup
		db, mock := setupTestDB(t)
		repo := &repository{db: db}
		requestID := uuid.New()

		// Mock the expected SQL query with no rows
		mock.ExpectQuery(`SELECT \* FROM "signup_requests" WHERE id = \$1 ORDER BY "signup_requests"."id" LIMIT \$2`).
			WithArgs(requestID, 1).
			WillReturnError(gorm.ErrRecordNotFound)

		// Execute
		result, err := repo.GetSignUpRequestByID(context.Background(), requestID)

		// Assert
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Equal(t, gorm.ErrRecordNotFound, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		// Setup
		db, mock := setupTestDB(t)
		repo := &repository{db: db}
		requestID := uuid.New()

		// Mock the expected SQL query with error
		mock.ExpectQuery(`SELECT \* FROM "signup_requests" WHERE id = \$1 ORDER BY "signup_requests"."id" LIMIT \$2`).
			WithArgs(requestID, 1).
			WillReturnError(sql.ErrConnDone)

		// Execute
		result, err := repo.GetSignUpRequestByID(context.Background(), requestID)

		// Assert
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}
