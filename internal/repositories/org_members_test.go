package repository

import (
	"context"
	"testing"
	"time"

	sqlmock "github.com/DATA-DOG/go-sqlmock"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"

	"api-server/internal/dto"
	"api-server/internal/enums"
)

func TestDeleteMembersByOrganizationID(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		orgID := uuid.New()

		// Setup mock expectations
		mock.ExpectBegin()
		mock.ExpectExec(`DELETE FROM "org_members" WHERE org_id = \$1`).
			WithArgs(orgID).
			WillReturnResult(sqlmock.NewResult(0, 1))
		mock.ExpectCommit()

		// Call the method
		err := repo.DeleteMembersByOrganizationID(context.Background(), orgID)

		// Assertions
		assert.NoError(t, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		orgID := uuid.New()

		// Setup mock expectations
		mock.ExpectBegin()
		mock.ExpectExec(`DELETE FROM "org_members" WHERE org_id = \$1`).
			WithArgs(orgID).
			WillReturnError(gorm.ErrInvalidDB)
		mock.ExpectRollback()

		// Call the method
		err := repo.DeleteMembersByOrganizationID(context.Background(), orgID)

		// Assertions
		assert.Error(t, err)
		assert.Equal(t, gorm.ErrInvalidDB, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestFindOrgMember(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		orgID := uuid.New()
		userID := uuid.New()
		createdAt := time.Now()
		updatedAt := time.Now()

		// Setup mock expectations
		rows := sqlmock.NewRows([]string{"id", "user_id", "org_id", "role", "created_at", "updated_at"}).
			AddRow(uuid.New(), userID, orgID, enums.OrgRole_Owner, createdAt, updatedAt)

		mock.ExpectQuery(`SELECT \* FROM "org_members" WHERE org_id = \$1 AND user_id = \$2 AND role = \$3 ORDER BY "org_members"."id" LIMIT \$4`).
			WithArgs(orgID, userID, enums.OrgRole_Owner, 1).
			WillReturnRows(rows)

		// Call the method
		filter := FilterOrgMember{
			OrgID:  &orgID,
			UserID: &userID,
			Role:   enums.RepoRole_Owner,
		}
		result, err := repo.FindOrgMember(context.Background(), filter)

		// Assertions
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, userID, result.UserID)
		assert.Equal(t, orgID, result.OrgID)
		assert.Equal(t, enums.OrgRole_Owner, result.Role)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("not found", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		orgID := uuid.New()
		userID := uuid.New()

		// Setup mock expectations
		mock.ExpectQuery(`SELECT \* FROM "org_members" WHERE org_id = \$1 AND user_id = \$2 AND role = \$3 ORDER BY "org_members"."id" LIMIT \$4`).
			WithArgs(orgID, userID, enums.OrgRole_Owner, 1).
			WillReturnError(gorm.ErrRecordNotFound)

		// Call the method
		filter := FilterOrgMember{
			OrgID:  &orgID,
			UserID: &userID,
			Role:   enums.RepoRole_Owner,
		}
		result, err := repo.FindOrgMember(context.Background(), filter)

		// Assertions
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Equal(t, gorm.ErrRecordNotFound, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		orgID := uuid.New()
		userID := uuid.New()

		// Setup mock expectations
		mock.ExpectQuery(`SELECT \* FROM "org_members" WHERE org_id = \$1 AND user_id = \$2 AND role = \$3 ORDER BY "org_members"."id" LIMIT \$4`).
			WithArgs(orgID, userID, enums.OrgRole_Owner, 1).
			WillReturnError(gorm.ErrInvalidDB)

		// Call the method
		filter := FilterOrgMember{
			OrgID:  &orgID,
			UserID: &userID,
			Role:   enums.RepoRole_Owner,
		}
		result, err := repo.FindOrgMember(context.Background(), filter)

		// Assertions
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Equal(t, gorm.ErrInvalidDB, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestListMembers(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		orgID := uuid.New()
		userID1 := uuid.New()
		userID2 := uuid.New()
		createdAt := time.Now()
		updatedAt := time.Now()

		// Setup mock expectations for list members query (this comes first)
		memberRows := sqlmock.NewRows([]string{"id", "user_id", "org_id", "role", "name", "email", "avatar", "created_at", "updated_at"}).
			AddRow(uuid.New(), userID1, orgID, enums.OrgRole_Owner, "user1", "<EMAIL>", "avatar1", createdAt, updatedAt).
			AddRow(uuid.New(), userID2, orgID, enums.OrgRole_Developer, "user2", "<EMAIL>", "avatar2", createdAt, updatedAt)

		mock.ExpectQuery(`SELECT org_members\.\*, users\.name AS name, au\.email AS email, users\.avatar AS avatar FROM "org_members" LEFT JOIN users ON org_members\.user_id = users\.id LEFT JOIN auth\.users au ON users\.id = au\.id WHERE org_members\.org_id = \$1 ORDER BY id asc LIMIT \$2`).
			WithArgs(orgID, 10).
			WillReturnRows(memberRows)

		// Setup mock expectations for organization check (this comes after)
		orgRows := sqlmock.NewRows([]string{"id", "name", "created_at", "updated_at"}).
			AddRow(orgID, "Test Org", createdAt, updatedAt)
		mock.ExpectQuery(`SELECT \* FROM "organizations" WHERE "organizations"."id" = \$1`).
			WithArgs(orgID).
			WillReturnRows(orgRows)

		// Setup mock expectations for batch User preload using IN clause
		userRows := sqlmock.NewRows([]string{"id", "name", "created_at", "updated_at"}).
			AddRow(userID1, "user1", createdAt, updatedAt).
			AddRow(userID2, "user2", createdAt, updatedAt)
		mock.ExpectQuery(`SELECT \* FROM "public"."users" WHERE "users"."id" IN \(\$1,\$2\)`).
			WithArgs(userID1, userID2).
			WillReturnRows(userRows)

		// Call the method
		input := dto.ListOrgMembersInput{
			OrgId: orgID,
			Paginate: dto.PaginateRequest{
				Page:    1,
				PerPage: 10,
				OrderBy: "id",
				Sort:    "asc",
			},
		}
		result, err := repo.ListMembers(context.Background(), input)

		// Assertions
		assert.NoError(t, err)
		assert.Len(t, result, 2)
		assert.Equal(t, userID1, result[0].UserID)
		assert.Equal(t, "user1", result[0].Name)
		assert.Equal(t, "<EMAIL>", result[0].Email)
		assert.Equal(t, "avatar1", *result[0].Avatar)
		assert.Equal(t, userID2, result[1].UserID)
		assert.Equal(t, "user2", result[1].Name)
		assert.Equal(t, "<EMAIL>", result[1].Email)
		assert.Equal(t, "avatar2", *result[1].Avatar)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		orgID := uuid.New()

		// Setup mock expectations for list members query (error case)
		mock.ExpectQuery(`SELECT org_members\.\*, users\.name AS name, au\.email AS email, users\.avatar AS avatar FROM "org_members" LEFT JOIN users ON org_members\.user_id = users\.id LEFT JOIN auth\.users au ON users\.id = au\.id WHERE org_members\.org_id = \$1 ORDER BY id asc LIMIT \$2`).
			WithArgs(orgID, 10).
			WillReturnError(gorm.ErrInvalidDB)

		// Call the method
		input := dto.ListOrgMembersInput{
			OrgId: orgID,
			Paginate: dto.PaginateRequest{
				Page:    1,
				PerPage: 10,
				OrderBy: "id",
				Sort:    "asc",
			},
		}
		result, err := repo.ListMembers(context.Background(), input)

		// Assertions
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Equal(t, gorm.ErrInvalidDB, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestCountMembers(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		orgID := uuid.New()

		// Setup mock expectations
		rows := sqlmock.NewRows([]string{"count"}).
			AddRow(2)

		mock.ExpectQuery(`SELECT count\(\*\) FROM "org_members" LEFT JOIN users ON org_members\.user_id = users\.id LEFT JOIN auth\.users au ON users\.id = au\.id WHERE org_members\.org_id = \$1`).
			WithArgs(orgID).
			WillReturnRows(rows)

		// Call the method
		input := dto.ListOrgMembersInput{
			OrgId: orgID,
		}
		count, err := repo.CountMembers(context.Background(), input)

		// Assertions
		assert.NoError(t, err)
		assert.Equal(t, 2, count)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		orgID := uuid.New()

		// Setup mock expectations
		mock.ExpectQuery(`SELECT count\(\*\) FROM "org_members" LEFT JOIN users ON org_members\.user_id = users\.id LEFT JOIN auth\.users au ON users\.id = au\.id WHERE org_members\.org_id = \$1`).
			WithArgs(orgID).
			WillReturnError(gorm.ErrInvalidDB)

		// Call the method
		input := dto.ListOrgMembersInput{
			OrgId: orgID,
		}
		count, err := repo.CountMembers(context.Background(), input)

		// Assertions
		assert.Error(t, err)
		assert.Equal(t, 0, count)
		assert.Equal(t, gorm.ErrInvalidDB, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestDeleteOrgMembersByUserID(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		userID := uuid.New()

		// Setup mock expectations for transaction
		mock.ExpectBegin()
		mock.ExpectExec(`DELETE FROM "org_members" WHERE user_id = \$1`).
			WithArgs(userID).
			WillReturnResult(sqlmock.NewResult(0, 1))
		mock.ExpectCommit()

		// Call the method
		err := repo.DeleteOrgMembersByUserID(context.Background(), userID)

		// Assertions
		assert.NoError(t, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		userID := uuid.New()

		// Setup mock expectations for transaction with error
		mock.ExpectBegin()
		mock.ExpectExec(`DELETE FROM "org_members" WHERE user_id = \$1`).
			WithArgs(userID).
			WillReturnError(gorm.ErrInvalidDB)
		mock.ExpectRollback()

		// Call the method
		err := repo.DeleteOrgMembersByUserID(context.Background(), userID)

		// Assertions
		assert.Error(t, err)
		assert.Equal(t, gorm.ErrInvalidDB, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestDeleteOrgMemberByID(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		id := uuid.New()

		// Setup mock expectations for transaction
		mock.ExpectBegin()
		mock.ExpectExec(`DELETE FROM "org_members" WHERE id = \$1`).
			WithArgs(id).
			WillReturnResult(sqlmock.NewResult(0, 1))
		mock.ExpectCommit()

		// Call the method
		err := repo.DeleteOrgMemberByID(context.Background(), id)

		// Assertions
		assert.NoError(t, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		id := uuid.New()

		// Setup mock expectations for transaction with error
		mock.ExpectBegin()
		mock.ExpectExec(`DELETE FROM "org_members" WHERE id = \$1`).
			WithArgs(id).
			WillReturnError(gorm.ErrInvalidDB)
		mock.ExpectRollback()

		// Call the method
		err := repo.DeleteOrgMemberByID(context.Background(), id)

		// Assertions
		assert.Error(t, err)
		assert.Equal(t, gorm.ErrInvalidDB, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}
