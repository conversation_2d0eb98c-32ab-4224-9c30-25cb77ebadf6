// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	entities "api-server/internal/entities"
	context "context"

	mock "github.com/stretchr/testify/mock"

	repository "api-server/internal/repositories"

	types "api-server/internal/types"

	uuid "github.com/google/uuid"
)

// MockSshKeyRepository is an autogenerated mock type for the SshKeyRepository type
type MockSshKeyRepository struct {
	mock.Mock
}

type MockSshKeyRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockSshKeyRepository) EXPECT() *MockSshKeyRepository_Expecter {
	return &MockSshKeyRepository_Expecter{mock: &_m.Mock}
}

// CountAllSSHKeys provides a mock function with given fields: ctx, query
func (_m *MockSshKeyRepository) CountAllSSHKeys(ctx context.Context, query repository.GetSSHKeyQuery) (int64, error) {
	ret := _m.Called(ctx, query)

	if len(ret) == 0 {
		panic("no return value specified for CountAllSSHKeys")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.GetSSHKeyQuery) (int64, error)); ok {
		return rf(ctx, query)
	}
	if rf, ok := ret.Get(0).(func(context.Context, repository.GetSSHKeyQuery) int64); ok {
		r0 = rf(ctx, query)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, repository.GetSSHKeyQuery) error); ok {
		r1 = rf(ctx, query)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockSshKeyRepository_CountAllSSHKeys_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CountAllSSHKeys'
type MockSshKeyRepository_CountAllSSHKeys_Call struct {
	*mock.Call
}

// CountAllSSHKeys is a helper method to define mock.On call
//   - ctx context.Context
//   - query repository.GetSSHKeyQuery
func (_e *MockSshKeyRepository_Expecter) CountAllSSHKeys(ctx interface{}, query interface{}) *MockSshKeyRepository_CountAllSSHKeys_Call {
	return &MockSshKeyRepository_CountAllSSHKeys_Call{Call: _e.mock.On("CountAllSSHKeys", ctx, query)}
}

func (_c *MockSshKeyRepository_CountAllSSHKeys_Call) Run(run func(ctx context.Context, query repository.GetSSHKeyQuery)) *MockSshKeyRepository_CountAllSSHKeys_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.GetSSHKeyQuery))
	})
	return _c
}

func (_c *MockSshKeyRepository_CountAllSSHKeys_Call) Return(_a0 int64, _a1 error) *MockSshKeyRepository_CountAllSSHKeys_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockSshKeyRepository_CountAllSSHKeys_Call) RunAndReturn(run func(context.Context, repository.GetSSHKeyQuery) (int64, error)) *MockSshKeyRepository_CountAllSSHKeys_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteSSHKeysByUserID provides a mock function with given fields: ctx, userID
func (_m *MockSshKeyRepository) DeleteSSHKeysByUserID(ctx context.Context, userID uuid.UUID) error {
	ret := _m.Called(ctx, userID)

	if len(ret) == 0 {
		panic("no return value specified for DeleteSSHKeysByUserID")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) error); ok {
		r0 = rf(ctx, userID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockSshKeyRepository_DeleteSSHKeysByUserID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteSSHKeysByUserID'
type MockSshKeyRepository_DeleteSSHKeysByUserID_Call struct {
	*mock.Call
}

// DeleteSSHKeysByUserID is a helper method to define mock.On call
//   - ctx context.Context
//   - userID uuid.UUID
func (_e *MockSshKeyRepository_Expecter) DeleteSSHKeysByUserID(ctx interface{}, userID interface{}) *MockSshKeyRepository_DeleteSSHKeysByUserID_Call {
	return &MockSshKeyRepository_DeleteSSHKeysByUserID_Call{Call: _e.mock.On("DeleteSSHKeysByUserID", ctx, userID)}
}

func (_c *MockSshKeyRepository_DeleteSSHKeysByUserID_Call) Run(run func(ctx context.Context, userID uuid.UUID)) *MockSshKeyRepository_DeleteSSHKeysByUserID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *MockSshKeyRepository_DeleteSSHKeysByUserID_Call) Return(_a0 error) *MockSshKeyRepository_DeleteSSHKeysByUserID_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockSshKeyRepository_DeleteSSHKeysByUserID_Call) RunAndReturn(run func(context.Context, uuid.UUID) error) *MockSshKeyRepository_DeleteSSHKeysByUserID_Call {
	_c.Call.Return(run)
	return _c
}

// GetSSHKeyByID provides a mock function with given fields: ctx, id
func (_m *MockSshKeyRepository) GetSSHKeyByID(ctx context.Context, id uuid.UUID) (*entities.SSHKey, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetSSHKeyByID")
	}

	var r0 *entities.SSHKey
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) (*entities.SSHKey, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) *entities.SSHKey); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.SSHKey)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uuid.UUID) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockSshKeyRepository_GetSSHKeyByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetSSHKeyByID'
type MockSshKeyRepository_GetSSHKeyByID_Call struct {
	*mock.Call
}

// GetSSHKeyByID is a helper method to define mock.On call
//   - ctx context.Context
//   - id uuid.UUID
func (_e *MockSshKeyRepository_Expecter) GetSSHKeyByID(ctx interface{}, id interface{}) *MockSshKeyRepository_GetSSHKeyByID_Call {
	return &MockSshKeyRepository_GetSSHKeyByID_Call{Call: _e.mock.On("GetSSHKeyByID", ctx, id)}
}

func (_c *MockSshKeyRepository_GetSSHKeyByID_Call) Run(run func(ctx context.Context, id uuid.UUID)) *MockSshKeyRepository_GetSSHKeyByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *MockSshKeyRepository_GetSSHKeyByID_Call) Return(_a0 *entities.SSHKey, _a1 error) *MockSshKeyRepository_GetSSHKeyByID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockSshKeyRepository_GetSSHKeyByID_Call) RunAndReturn(run func(context.Context, uuid.UUID) (*entities.SSHKey, error)) *MockSshKeyRepository_GetSSHKeyByID_Call {
	_c.Call.Return(run)
	return _c
}

// IsDuplicateSSHKey provides a mock function with given fields: ctx, key
func (_m *MockSshKeyRepository) IsDuplicateSSHKey(ctx context.Context, key string) (bool, error) {
	ret := _m.Called(ctx, key)

	if len(ret) == 0 {
		panic("no return value specified for IsDuplicateSSHKey")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (bool, error)); ok {
		return rf(ctx, key)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) bool); ok {
		r0 = rf(ctx, key)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, key)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockSshKeyRepository_IsDuplicateSSHKey_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'IsDuplicateSSHKey'
type MockSshKeyRepository_IsDuplicateSSHKey_Call struct {
	*mock.Call
}

// IsDuplicateSSHKey is a helper method to define mock.On call
//   - ctx context.Context
//   - key string
func (_e *MockSshKeyRepository_Expecter) IsDuplicateSSHKey(ctx interface{}, key interface{}) *MockSshKeyRepository_IsDuplicateSSHKey_Call {
	return &MockSshKeyRepository_IsDuplicateSSHKey_Call{Call: _e.mock.On("IsDuplicateSSHKey", ctx, key)}
}

func (_c *MockSshKeyRepository_IsDuplicateSSHKey_Call) Run(run func(ctx context.Context, key string)) *MockSshKeyRepository_IsDuplicateSSHKey_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockSshKeyRepository_IsDuplicateSSHKey_Call) Return(_a0 bool, _a1 error) *MockSshKeyRepository_IsDuplicateSSHKey_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockSshKeyRepository_IsDuplicateSSHKey_Call) RunAndReturn(run func(context.Context, string) (bool, error)) *MockSshKeyRepository_IsDuplicateSSHKey_Call {
	_c.Call.Return(run)
	return _c
}

// ListSSHKeys provides a mock function with given fields: ctx, pageNo, pageSize, order, query
func (_m *MockSshKeyRepository) ListSSHKeys(ctx context.Context, pageNo int, pageSize int, order types.OrderBy, query repository.GetSSHKeyQuery) ([]entities.SSHKey, error) {
	ret := _m.Called(ctx, pageNo, pageSize, order, query)

	if len(ret) == 0 {
		panic("no return value specified for ListSSHKeys")
	}

	var r0 []entities.SSHKey
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int, int, types.OrderBy, repository.GetSSHKeyQuery) ([]entities.SSHKey, error)); ok {
		return rf(ctx, pageNo, pageSize, order, query)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int, int, types.OrderBy, repository.GetSSHKeyQuery) []entities.SSHKey); ok {
		r0 = rf(ctx, pageNo, pageSize, order, query)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]entities.SSHKey)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int, int, types.OrderBy, repository.GetSSHKeyQuery) error); ok {
		r1 = rf(ctx, pageNo, pageSize, order, query)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockSshKeyRepository_ListSSHKeys_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListSSHKeys'
type MockSshKeyRepository_ListSSHKeys_Call struct {
	*mock.Call
}

// ListSSHKeys is a helper method to define mock.On call
//   - ctx context.Context
//   - pageNo int
//   - pageSize int
//   - order types.OrderBy
//   - query repository.GetSSHKeyQuery
func (_e *MockSshKeyRepository_Expecter) ListSSHKeys(ctx interface{}, pageNo interface{}, pageSize interface{}, order interface{}, query interface{}) *MockSshKeyRepository_ListSSHKeys_Call {
	return &MockSshKeyRepository_ListSSHKeys_Call{Call: _e.mock.On("ListSSHKeys", ctx, pageNo, pageSize, order, query)}
}

func (_c *MockSshKeyRepository_ListSSHKeys_Call) Run(run func(ctx context.Context, pageNo int, pageSize int, order types.OrderBy, query repository.GetSSHKeyQuery)) *MockSshKeyRepository_ListSSHKeys_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int), args[2].(int), args[3].(types.OrderBy), args[4].(repository.GetSSHKeyQuery))
	})
	return _c
}

func (_c *MockSshKeyRepository_ListSSHKeys_Call) Return(_a0 []entities.SSHKey, _a1 error) *MockSshKeyRepository_ListSSHKeys_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockSshKeyRepository_ListSSHKeys_Call) RunAndReturn(run func(context.Context, int, int, types.OrderBy, repository.GetSSHKeyQuery) ([]entities.SSHKey, error)) *MockSshKeyRepository_ListSSHKeys_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockSshKeyRepository creates a new instance of MockSshKeyRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockSshKeyRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockSshKeyRepository {
	mock := &MockSshKeyRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
