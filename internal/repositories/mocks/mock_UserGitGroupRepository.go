// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	entities "api-server/internal/entities"
	context "context"

	mock "github.com/stretchr/testify/mock"

	repository "api-server/internal/repositories"

	uuid "github.com/google/uuid"
)

// MockUserGitGroupRepository is an autogenerated mock type for the UserGitGroupRepository type
type MockUserGitGroupRepository struct {
	mock.Mock
}

type MockUserGitGroupRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockUserGitGroupRepository) EXPECT() *MockUserGitGroupRepository_Expecter {
	return &MockUserGitGroupRepository_Expecter{mock: &_m.Mock}
}

// CreateUserGitGroup provides a mock function with given fields: ctx, in
func (_m *MockUserGitGroupRepository) CreateUserGitGroup(ctx context.Context, in repository.CreateUserGitGroupInput) (*entities.UserGitGroup, error) {
	ret := _m.Called(ctx, in)

	if len(ret) == 0 {
		panic("no return value specified for CreateUserGitGroup")
	}

	var r0 *entities.UserGitGroup
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.CreateUserGitGroupInput) (*entities.UserGitGroup, error)); ok {
		return rf(ctx, in)
	}
	if rf, ok := ret.Get(0).(func(context.Context, repository.CreateUserGitGroupInput) *entities.UserGitGroup); ok {
		r0 = rf(ctx, in)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.UserGitGroup)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, repository.CreateUserGitGroupInput) error); ok {
		r1 = rf(ctx, in)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockUserGitGroupRepository_CreateUserGitGroup_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateUserGitGroup'
type MockUserGitGroupRepository_CreateUserGitGroup_Call struct {
	*mock.Call
}

// CreateUserGitGroup is a helper method to define mock.On call
//   - ctx context.Context
//   - in repository.CreateUserGitGroupInput
func (_e *MockUserGitGroupRepository_Expecter) CreateUserGitGroup(ctx interface{}, in interface{}) *MockUserGitGroupRepository_CreateUserGitGroup_Call {
	return &MockUserGitGroupRepository_CreateUserGitGroup_Call{Call: _e.mock.On("CreateUserGitGroup", ctx, in)}
}

func (_c *MockUserGitGroupRepository_CreateUserGitGroup_Call) Run(run func(ctx context.Context, in repository.CreateUserGitGroupInput)) *MockUserGitGroupRepository_CreateUserGitGroup_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.CreateUserGitGroupInput))
	})
	return _c
}

func (_c *MockUserGitGroupRepository_CreateUserGitGroup_Call) Return(_a0 *entities.UserGitGroup, _a1 error) *MockUserGitGroupRepository_CreateUserGitGroup_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockUserGitGroupRepository_CreateUserGitGroup_Call) RunAndReturn(run func(context.Context, repository.CreateUserGitGroupInput) (*entities.UserGitGroup, error)) *MockUserGitGroupRepository_CreateUserGitGroup_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteUserGitGroup provides a mock function with given fields: ctx, userID
func (_m *MockUserGitGroupRepository) DeleteUserGitGroup(ctx context.Context, userID uuid.UUID) error {
	ret := _m.Called(ctx, userID)

	if len(ret) == 0 {
		panic("no return value specified for DeleteUserGitGroup")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) error); ok {
		r0 = rf(ctx, userID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockUserGitGroupRepository_DeleteUserGitGroup_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteUserGitGroup'
type MockUserGitGroupRepository_DeleteUserGitGroup_Call struct {
	*mock.Call
}

// DeleteUserGitGroup is a helper method to define mock.On call
//   - ctx context.Context
//   - userID uuid.UUID
func (_e *MockUserGitGroupRepository_Expecter) DeleteUserGitGroup(ctx interface{}, userID interface{}) *MockUserGitGroupRepository_DeleteUserGitGroup_Call {
	return &MockUserGitGroupRepository_DeleteUserGitGroup_Call{Call: _e.mock.On("DeleteUserGitGroup", ctx, userID)}
}

func (_c *MockUserGitGroupRepository_DeleteUserGitGroup_Call) Run(run func(ctx context.Context, userID uuid.UUID)) *MockUserGitGroupRepository_DeleteUserGitGroup_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *MockUserGitGroupRepository_DeleteUserGitGroup_Call) Return(_a0 error) *MockUserGitGroupRepository_DeleteUserGitGroup_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockUserGitGroupRepository_DeleteUserGitGroup_Call) RunAndReturn(run func(context.Context, uuid.UUID) error) *MockUserGitGroupRepository_DeleteUserGitGroup_Call {
	_c.Call.Return(run)
	return _c
}

// FindUserGitGroup provides a mock function with given fields: ctx, userID
func (_m *MockUserGitGroupRepository) FindUserGitGroup(ctx context.Context, userID uuid.UUID) (*entities.DefaultGitGroup, error) {
	ret := _m.Called(ctx, userID)

	if len(ret) == 0 {
		panic("no return value specified for FindUserGitGroup")
	}

	var r0 *entities.DefaultGitGroup
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) (*entities.DefaultGitGroup, error)); ok {
		return rf(ctx, userID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) *entities.DefaultGitGroup); ok {
		r0 = rf(ctx, userID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.DefaultGitGroup)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uuid.UUID) error); ok {
		r1 = rf(ctx, userID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockUserGitGroupRepository_FindUserGitGroup_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindUserGitGroup'
type MockUserGitGroupRepository_FindUserGitGroup_Call struct {
	*mock.Call
}

// FindUserGitGroup is a helper method to define mock.On call
//   - ctx context.Context
//   - userID uuid.UUID
func (_e *MockUserGitGroupRepository_Expecter) FindUserGitGroup(ctx interface{}, userID interface{}) *MockUserGitGroupRepository_FindUserGitGroup_Call {
	return &MockUserGitGroupRepository_FindUserGitGroup_Call{Call: _e.mock.On("FindUserGitGroup", ctx, userID)}
}

func (_c *MockUserGitGroupRepository_FindUserGitGroup_Call) Run(run func(ctx context.Context, userID uuid.UUID)) *MockUserGitGroupRepository_FindUserGitGroup_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *MockUserGitGroupRepository_FindUserGitGroup_Call) Return(_a0 *entities.DefaultGitGroup, _a1 error) *MockUserGitGroupRepository_FindUserGitGroup_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockUserGitGroupRepository_FindUserGitGroup_Call) RunAndReturn(run func(context.Context, uuid.UUID) (*entities.DefaultGitGroup, error)) *MockUserGitGroupRepository_FindUserGitGroup_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateUserGitGroup provides a mock function with given fields: ctx, in
func (_m *MockUserGitGroupRepository) UpdateUserGitGroup(ctx context.Context, in repository.UpdateUserGitGroupInput) (*entities.UserGitGroup, error) {
	ret := _m.Called(ctx, in)

	if len(ret) == 0 {
		panic("no return value specified for UpdateUserGitGroup")
	}

	var r0 *entities.UserGitGroup
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.UpdateUserGitGroupInput) (*entities.UserGitGroup, error)); ok {
		return rf(ctx, in)
	}
	if rf, ok := ret.Get(0).(func(context.Context, repository.UpdateUserGitGroupInput) *entities.UserGitGroup); ok {
		r0 = rf(ctx, in)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.UserGitGroup)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, repository.UpdateUserGitGroupInput) error); ok {
		r1 = rf(ctx, in)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockUserGitGroupRepository_UpdateUserGitGroup_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateUserGitGroup'
type MockUserGitGroupRepository_UpdateUserGitGroup_Call struct {
	*mock.Call
}

// UpdateUserGitGroup is a helper method to define mock.On call
//   - ctx context.Context
//   - in repository.UpdateUserGitGroupInput
func (_e *MockUserGitGroupRepository_Expecter) UpdateUserGitGroup(ctx interface{}, in interface{}) *MockUserGitGroupRepository_UpdateUserGitGroup_Call {
	return &MockUserGitGroupRepository_UpdateUserGitGroup_Call{Call: _e.mock.On("UpdateUserGitGroup", ctx, in)}
}

func (_c *MockUserGitGroupRepository_UpdateUserGitGroup_Call) Run(run func(ctx context.Context, in repository.UpdateUserGitGroupInput)) *MockUserGitGroupRepository_UpdateUserGitGroup_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.UpdateUserGitGroupInput))
	})
	return _c
}

func (_c *MockUserGitGroupRepository_UpdateUserGitGroup_Call) Return(_a0 *entities.UserGitGroup, _a1 error) *MockUserGitGroupRepository_UpdateUserGitGroup_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockUserGitGroupRepository_UpdateUserGitGroup_Call) RunAndReturn(run func(context.Context, repository.UpdateUserGitGroupInput) (*entities.UserGitGroup, error)) *MockUserGitGroupRepository_UpdateUserGitGroup_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockUserGitGroupRepository creates a new instance of MockUserGitGroupRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockUserGitGroupRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockUserGitGroupRepository {
	mock := &MockUserGitGroupRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
