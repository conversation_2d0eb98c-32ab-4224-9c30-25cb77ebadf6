// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	entities "api-server/internal/entities"
	context "context"

	mock "github.com/stretchr/testify/mock"

	repository "api-server/internal/repositories"

	types "api-server/internal/types"

	uuid "github.com/google/uuid"
)

// MockAccessTokenRepository is an autogenerated mock type for the AccessTokenRepository type
type MockAccessTokenRepository struct {
	mock.Mock
}

type MockAccessTokenRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockAccessTokenRepository) EXPECT() *MockAccessTokenRepository_Expecter {
	return &MockAccessTokenRepository_Expecter{mock: &_m.Mock}
}

// CountAccessToken provides a mock function with given fields: ctx, in
func (_m *MockAccessTokenRepository) CountAccessToken(ctx context.Context, in repository.ListAccessTokenQuery) (int64, error) {
	ret := _m.Called(ctx, in)

	if len(ret) == 0 {
		panic("no return value specified for CountAccessToken")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.ListAccessTokenQuery) (int64, error)); ok {
		return rf(ctx, in)
	}
	if rf, ok := ret.Get(0).(func(context.Context, repository.ListAccessTokenQuery) int64); ok {
		r0 = rf(ctx, in)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, repository.ListAccessTokenQuery) error); ok {
		r1 = rf(ctx, in)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockAccessTokenRepository_CountAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CountAccessToken'
type MockAccessTokenRepository_CountAccessToken_Call struct {
	*mock.Call
}

// CountAccessToken is a helper method to define mock.On call
//   - ctx context.Context
//   - in repository.ListAccessTokenQuery
func (_e *MockAccessTokenRepository_Expecter) CountAccessToken(ctx interface{}, in interface{}) *MockAccessTokenRepository_CountAccessToken_Call {
	return &MockAccessTokenRepository_CountAccessToken_Call{Call: _e.mock.On("CountAccessToken", ctx, in)}
}

func (_c *MockAccessTokenRepository_CountAccessToken_Call) Run(run func(ctx context.Context, in repository.ListAccessTokenQuery)) *MockAccessTokenRepository_CountAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.ListAccessTokenQuery))
	})
	return _c
}

func (_c *MockAccessTokenRepository_CountAccessToken_Call) Return(_a0 int64, _a1 error) *MockAccessTokenRepository_CountAccessToken_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockAccessTokenRepository_CountAccessToken_Call) RunAndReturn(run func(context.Context, repository.ListAccessTokenQuery) (int64, error)) *MockAccessTokenRepository_CountAccessToken_Call {
	_c.Call.Return(run)
	return _c
}

// CreateAccessToken provides a mock function with given fields: ctx, in
func (_m *MockAccessTokenRepository) CreateAccessToken(ctx context.Context, in repository.CreateAccessTokenInput) (*entities.UserToken, error) {
	ret := _m.Called(ctx, in)

	if len(ret) == 0 {
		panic("no return value specified for CreateAccessToken")
	}

	var r0 *entities.UserToken
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.CreateAccessTokenInput) (*entities.UserToken, error)); ok {
		return rf(ctx, in)
	}
	if rf, ok := ret.Get(0).(func(context.Context, repository.CreateAccessTokenInput) *entities.UserToken); ok {
		r0 = rf(ctx, in)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.UserToken)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, repository.CreateAccessTokenInput) error); ok {
		r1 = rf(ctx, in)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockAccessTokenRepository_CreateAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateAccessToken'
type MockAccessTokenRepository_CreateAccessToken_Call struct {
	*mock.Call
}

// CreateAccessToken is a helper method to define mock.On call
//   - ctx context.Context
//   - in repository.CreateAccessTokenInput
func (_e *MockAccessTokenRepository_Expecter) CreateAccessToken(ctx interface{}, in interface{}) *MockAccessTokenRepository_CreateAccessToken_Call {
	return &MockAccessTokenRepository_CreateAccessToken_Call{Call: _e.mock.On("CreateAccessToken", ctx, in)}
}

func (_c *MockAccessTokenRepository_CreateAccessToken_Call) Run(run func(ctx context.Context, in repository.CreateAccessTokenInput)) *MockAccessTokenRepository_CreateAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.CreateAccessTokenInput))
	})
	return _c
}

func (_c *MockAccessTokenRepository_CreateAccessToken_Call) Return(_a0 *entities.UserToken, _a1 error) *MockAccessTokenRepository_CreateAccessToken_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockAccessTokenRepository_CreateAccessToken_Call) RunAndReturn(run func(context.Context, repository.CreateAccessTokenInput) (*entities.UserToken, error)) *MockAccessTokenRepository_CreateAccessToken_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteAccessToken provides a mock function with given fields: ctx, in
func (_m *MockAccessTokenRepository) DeleteAccessToken(ctx context.Context, in repository.DeleteAccessTokenInput) error {
	ret := _m.Called(ctx, in)

	if len(ret) == 0 {
		panic("no return value specified for DeleteAccessToken")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.DeleteAccessTokenInput) error); ok {
		r0 = rf(ctx, in)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockAccessTokenRepository_DeleteAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteAccessToken'
type MockAccessTokenRepository_DeleteAccessToken_Call struct {
	*mock.Call
}

// DeleteAccessToken is a helper method to define mock.On call
//   - ctx context.Context
//   - in repository.DeleteAccessTokenInput
func (_e *MockAccessTokenRepository_Expecter) DeleteAccessToken(ctx interface{}, in interface{}) *MockAccessTokenRepository_DeleteAccessToken_Call {
	return &MockAccessTokenRepository_DeleteAccessToken_Call{Call: _e.mock.On("DeleteAccessToken", ctx, in)}
}

func (_c *MockAccessTokenRepository_DeleteAccessToken_Call) Run(run func(ctx context.Context, in repository.DeleteAccessTokenInput)) *MockAccessTokenRepository_DeleteAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.DeleteAccessTokenInput))
	})
	return _c
}

func (_c *MockAccessTokenRepository_DeleteAccessToken_Call) Return(_a0 error) *MockAccessTokenRepository_DeleteAccessToken_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockAccessTokenRepository_DeleteAccessToken_Call) RunAndReturn(run func(context.Context, repository.DeleteAccessTokenInput) error) *MockAccessTokenRepository_DeleteAccessToken_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteAllUserAccessToken provides a mock function with given fields: ctx, userId
func (_m *MockAccessTokenRepository) DeleteAllUserAccessToken(ctx context.Context, userId uuid.UUID) error {
	ret := _m.Called(ctx, userId)

	if len(ret) == 0 {
		panic("no return value specified for DeleteAllUserAccessToken")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) error); ok {
		r0 = rf(ctx, userId)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockAccessTokenRepository_DeleteAllUserAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteAllUserAccessToken'
type MockAccessTokenRepository_DeleteAllUserAccessToken_Call struct {
	*mock.Call
}

// DeleteAllUserAccessToken is a helper method to define mock.On call
//   - ctx context.Context
//   - userId uuid.UUID
func (_e *MockAccessTokenRepository_Expecter) DeleteAllUserAccessToken(ctx interface{}, userId interface{}) *MockAccessTokenRepository_DeleteAllUserAccessToken_Call {
	return &MockAccessTokenRepository_DeleteAllUserAccessToken_Call{Call: _e.mock.On("DeleteAllUserAccessToken", ctx, userId)}
}

func (_c *MockAccessTokenRepository_DeleteAllUserAccessToken_Call) Run(run func(ctx context.Context, userId uuid.UUID)) *MockAccessTokenRepository_DeleteAllUserAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *MockAccessTokenRepository_DeleteAllUserAccessToken_Call) Return(_a0 error) *MockAccessTokenRepository_DeleteAllUserAccessToken_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockAccessTokenRepository_DeleteAllUserAccessToken_Call) RunAndReturn(run func(context.Context, uuid.UUID) error) *MockAccessTokenRepository_DeleteAllUserAccessToken_Call {
	_c.Call.Return(run)
	return _c
}

// FindAccessToken provides a mock function with given fields: ctx, in
func (_m *MockAccessTokenRepository) FindAccessToken(ctx context.Context, in repository.FindAccessTokenQuery) (*entities.UserToken, error) {
	ret := _m.Called(ctx, in)

	if len(ret) == 0 {
		panic("no return value specified for FindAccessToken")
	}

	var r0 *entities.UserToken
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.FindAccessTokenQuery) (*entities.UserToken, error)); ok {
		return rf(ctx, in)
	}
	if rf, ok := ret.Get(0).(func(context.Context, repository.FindAccessTokenQuery) *entities.UserToken); ok {
		r0 = rf(ctx, in)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.UserToken)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, repository.FindAccessTokenQuery) error); ok {
		r1 = rf(ctx, in)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockAccessTokenRepository_FindAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindAccessToken'
type MockAccessTokenRepository_FindAccessToken_Call struct {
	*mock.Call
}

// FindAccessToken is a helper method to define mock.On call
//   - ctx context.Context
//   - in repository.FindAccessTokenQuery
func (_e *MockAccessTokenRepository_Expecter) FindAccessToken(ctx interface{}, in interface{}) *MockAccessTokenRepository_FindAccessToken_Call {
	return &MockAccessTokenRepository_FindAccessToken_Call{Call: _e.mock.On("FindAccessToken", ctx, in)}
}

func (_c *MockAccessTokenRepository_FindAccessToken_Call) Run(run func(ctx context.Context, in repository.FindAccessTokenQuery)) *MockAccessTokenRepository_FindAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.FindAccessTokenQuery))
	})
	return _c
}

func (_c *MockAccessTokenRepository_FindAccessToken_Call) Return(_a0 *entities.UserToken, _a1 error) *MockAccessTokenRepository_FindAccessToken_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockAccessTokenRepository_FindAccessToken_Call) RunAndReturn(run func(context.Context, repository.FindAccessTokenQuery) (*entities.UserToken, error)) *MockAccessTokenRepository_FindAccessToken_Call {
	_c.Call.Return(run)
	return _c
}

// ListAccessToken provides a mock function with given fields: ctx, pagination, order, in
func (_m *MockAccessTokenRepository) ListAccessToken(ctx context.Context, pagination types.Pagination, order types.OrderBy, in repository.ListAccessTokenQuery) ([]entities.UserToken, error) {
	ret := _m.Called(ctx, pagination, order, in)

	if len(ret) == 0 {
		panic("no return value specified for ListAccessToken")
	}

	var r0 []entities.UserToken
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, types.Pagination, types.OrderBy, repository.ListAccessTokenQuery) ([]entities.UserToken, error)); ok {
		return rf(ctx, pagination, order, in)
	}
	if rf, ok := ret.Get(0).(func(context.Context, types.Pagination, types.OrderBy, repository.ListAccessTokenQuery) []entities.UserToken); ok {
		r0 = rf(ctx, pagination, order, in)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]entities.UserToken)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, types.Pagination, types.OrderBy, repository.ListAccessTokenQuery) error); ok {
		r1 = rf(ctx, pagination, order, in)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockAccessTokenRepository_ListAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListAccessToken'
type MockAccessTokenRepository_ListAccessToken_Call struct {
	*mock.Call
}

// ListAccessToken is a helper method to define mock.On call
//   - ctx context.Context
//   - pagination types.Pagination
//   - order types.OrderBy
//   - in repository.ListAccessTokenQuery
func (_e *MockAccessTokenRepository_Expecter) ListAccessToken(ctx interface{}, pagination interface{}, order interface{}, in interface{}) *MockAccessTokenRepository_ListAccessToken_Call {
	return &MockAccessTokenRepository_ListAccessToken_Call{Call: _e.mock.On("ListAccessToken", ctx, pagination, order, in)}
}

func (_c *MockAccessTokenRepository_ListAccessToken_Call) Run(run func(ctx context.Context, pagination types.Pagination, order types.OrderBy, in repository.ListAccessTokenQuery)) *MockAccessTokenRepository_ListAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(types.Pagination), args[2].(types.OrderBy), args[3].(repository.ListAccessTokenQuery))
	})
	return _c
}

func (_c *MockAccessTokenRepository_ListAccessToken_Call) Return(_a0 []entities.UserToken, _a1 error) *MockAccessTokenRepository_ListAccessToken_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockAccessTokenRepository_ListAccessToken_Call) RunAndReturn(run func(context.Context, types.Pagination, types.OrderBy, repository.ListAccessTokenQuery) ([]entities.UserToken, error)) *MockAccessTokenRepository_ListAccessToken_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockAccessTokenRepository creates a new instance of MockAccessTokenRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockAccessTokenRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockAccessTokenRepository {
	mock := &MockAccessTokenRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
