// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	entities "api-server/internal/entities"
	context "context"

	mock "github.com/stretchr/testify/mock"

	repository "api-server/internal/repositories"

	uuid "github.com/google/uuid"
)

// MockRepoMemberRepository is an autogenerated mock type for the RepoMemberRepository type
type MockRepoMemberRepository struct {
	mock.Mock
}

type MockRepoMemberRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockRepoMemberRepository) EXPECT() *MockRepoMemberRepository_Expecter {
	return &MockRepoMemberRepository_Expecter{mock: &_m.Mock}
}

// CreateRepositoryMember provides a mock function with given fields: ctx, input
func (_m *MockRepoMemberRepository) CreateRepositoryMember(ctx context.Context, input *entities.RepoMember) error {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for CreateRepositoryMember")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *entities.RepoMember) error); ok {
		r0 = rf(ctx, input)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepoMemberRepository_CreateRepositoryMember_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateRepositoryMember'
type MockRepoMemberRepository_CreateRepositoryMember_Call struct {
	*mock.Call
}

// CreateRepositoryMember is a helper method to define mock.On call
//   - ctx context.Context
//   - input *entities.RepoMember
func (_e *MockRepoMemberRepository_Expecter) CreateRepositoryMember(ctx interface{}, input interface{}) *MockRepoMemberRepository_CreateRepositoryMember_Call {
	return &MockRepoMemberRepository_CreateRepositoryMember_Call{Call: _e.mock.On("CreateRepositoryMember", ctx, input)}
}

func (_c *MockRepoMemberRepository_CreateRepositoryMember_Call) Run(run func(ctx context.Context, input *entities.RepoMember)) *MockRepoMemberRepository_CreateRepositoryMember_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*entities.RepoMember))
	})
	return _c
}

func (_c *MockRepoMemberRepository_CreateRepositoryMember_Call) Return(_a0 error) *MockRepoMemberRepository_CreateRepositoryMember_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepoMemberRepository_CreateRepositoryMember_Call) RunAndReturn(run func(context.Context, *entities.RepoMember) error) *MockRepoMemberRepository_CreateRepositoryMember_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteRepositoryMemberByRepositoryID provides a mock function with given fields: ctx, repositoryID
func (_m *MockRepoMemberRepository) DeleteRepositoryMemberByRepositoryID(ctx context.Context, repositoryID uuid.UUID) error {
	ret := _m.Called(ctx, repositoryID)

	if len(ret) == 0 {
		panic("no return value specified for DeleteRepositoryMemberByRepositoryID")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) error); ok {
		r0 = rf(ctx, repositoryID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepoMemberRepository_DeleteRepositoryMemberByRepositoryID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteRepositoryMemberByRepositoryID'
type MockRepoMemberRepository_DeleteRepositoryMemberByRepositoryID_Call struct {
	*mock.Call
}

// DeleteRepositoryMemberByRepositoryID is a helper method to define mock.On call
//   - ctx context.Context
//   - repositoryID uuid.UUID
func (_e *MockRepoMemberRepository_Expecter) DeleteRepositoryMemberByRepositoryID(ctx interface{}, repositoryID interface{}) *MockRepoMemberRepository_DeleteRepositoryMemberByRepositoryID_Call {
	return &MockRepoMemberRepository_DeleteRepositoryMemberByRepositoryID_Call{Call: _e.mock.On("DeleteRepositoryMemberByRepositoryID", ctx, repositoryID)}
}

func (_c *MockRepoMemberRepository_DeleteRepositoryMemberByRepositoryID_Call) Run(run func(ctx context.Context, repositoryID uuid.UUID)) *MockRepoMemberRepository_DeleteRepositoryMemberByRepositoryID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *MockRepoMemberRepository_DeleteRepositoryMemberByRepositoryID_Call) Return(_a0 error) *MockRepoMemberRepository_DeleteRepositoryMemberByRepositoryID_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepoMemberRepository_DeleteRepositoryMemberByRepositoryID_Call) RunAndReturn(run func(context.Context, uuid.UUID) error) *MockRepoMemberRepository_DeleteRepositoryMemberByRepositoryID_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteRepositoryMemberByUserID provides a mock function with given fields: ctx, userID
func (_m *MockRepoMemberRepository) DeleteRepositoryMemberByUserID(ctx context.Context, userID uuid.UUID) error {
	ret := _m.Called(ctx, userID)

	if len(ret) == 0 {
		panic("no return value specified for DeleteRepositoryMemberByUserID")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) error); ok {
		r0 = rf(ctx, userID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepoMemberRepository_DeleteRepositoryMemberByUserID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteRepositoryMemberByUserID'
type MockRepoMemberRepository_DeleteRepositoryMemberByUserID_Call struct {
	*mock.Call
}

// DeleteRepositoryMemberByUserID is a helper method to define mock.On call
//   - ctx context.Context
//   - userID uuid.UUID
func (_e *MockRepoMemberRepository_Expecter) DeleteRepositoryMemberByUserID(ctx interface{}, userID interface{}) *MockRepoMemberRepository_DeleteRepositoryMemberByUserID_Call {
	return &MockRepoMemberRepository_DeleteRepositoryMemberByUserID_Call{Call: _e.mock.On("DeleteRepositoryMemberByUserID", ctx, userID)}
}

func (_c *MockRepoMemberRepository_DeleteRepositoryMemberByUserID_Call) Run(run func(ctx context.Context, userID uuid.UUID)) *MockRepoMemberRepository_DeleteRepositoryMemberByUserID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *MockRepoMemberRepository_DeleteRepositoryMemberByUserID_Call) Return(_a0 error) *MockRepoMemberRepository_DeleteRepositoryMemberByUserID_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepoMemberRepository_DeleteRepositoryMemberByUserID_Call) RunAndReturn(run func(context.Context, uuid.UUID) error) *MockRepoMemberRepository_DeleteRepositoryMemberByUserID_Call {
	_c.Call.Return(run)
	return _c
}

// FindRepositoryMember provides a mock function with given fields: ctx, filter
func (_m *MockRepoMemberRepository) FindRepositoryMember(ctx context.Context, filter repository.FilterRepoMember) (*entities.RepoMember, error) {
	ret := _m.Called(ctx, filter)

	if len(ret) == 0 {
		panic("no return value specified for FindRepositoryMember")
	}

	var r0 *entities.RepoMember
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.FilterRepoMember) (*entities.RepoMember, error)); ok {
		return rf(ctx, filter)
	}
	if rf, ok := ret.Get(0).(func(context.Context, repository.FilterRepoMember) *entities.RepoMember); ok {
		r0 = rf(ctx, filter)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.RepoMember)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, repository.FilterRepoMember) error); ok {
		r1 = rf(ctx, filter)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepoMemberRepository_FindRepositoryMember_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindRepositoryMember'
type MockRepoMemberRepository_FindRepositoryMember_Call struct {
	*mock.Call
}

// FindRepositoryMember is a helper method to define mock.On call
//   - ctx context.Context
//   - filter repository.FilterRepoMember
func (_e *MockRepoMemberRepository_Expecter) FindRepositoryMember(ctx interface{}, filter interface{}) *MockRepoMemberRepository_FindRepositoryMember_Call {
	return &MockRepoMemberRepository_FindRepositoryMember_Call{Call: _e.mock.On("FindRepositoryMember", ctx, filter)}
}

func (_c *MockRepoMemberRepository_FindRepositoryMember_Call) Run(run func(ctx context.Context, filter repository.FilterRepoMember)) *MockRepoMemberRepository_FindRepositoryMember_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.FilterRepoMember))
	})
	return _c
}

func (_c *MockRepoMemberRepository_FindRepositoryMember_Call) Return(_a0 *entities.RepoMember, _a1 error) *MockRepoMemberRepository_FindRepositoryMember_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepoMemberRepository_FindRepositoryMember_Call) RunAndReturn(run func(context.Context, repository.FilterRepoMember) (*entities.RepoMember, error)) *MockRepoMemberRepository_FindRepositoryMember_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockRepoMemberRepository creates a new instance of MockRepoMemberRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockRepoMemberRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockRepoMemberRepository {
	mock := &MockRepoMemberRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
