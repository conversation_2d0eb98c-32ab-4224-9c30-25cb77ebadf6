// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	dto "api-server/internal/dto"
	entities "api-server/internal/entities"
	context "context"

	mock "github.com/stretchr/testify/mock"

	repository "api-server/internal/repositories"

	uuid "github.com/google/uuid"
)

// MockUserRepository is an autogenerated mock type for the UserRepository type
type MockUserRepository struct {
	mock.Mock
}

type MockUserRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockUserRepository) EXPECT() *MockUserRepository_Expecter {
	return &MockUserRepository_Expecter{mock: &_m.Mock}
}

// CheckCurrentPassword provides a mock function with given fields: ctx, userID, currentPassword
func (_m *MockUserRepository) CheckCurrentPassword(ctx context.Context, userID uuid.UUID, currentPassword string) error {
	ret := _m.Called(ctx, userID, currentPassword)

	if len(ret) == 0 {
		panic("no return value specified for CheckCurrentPassword")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID, string) error); ok {
		r0 = rf(ctx, userID, currentPassword)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockUserRepository_CheckCurrentPassword_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CheckCurrentPassword'
type MockUserRepository_CheckCurrentPassword_Call struct {
	*mock.Call
}

// CheckCurrentPassword is a helper method to define mock.On call
//   - ctx context.Context
//   - userID uuid.UUID
//   - currentPassword string
func (_e *MockUserRepository_Expecter) CheckCurrentPassword(ctx interface{}, userID interface{}, currentPassword interface{}) *MockUserRepository_CheckCurrentPassword_Call {
	return &MockUserRepository_CheckCurrentPassword_Call{Call: _e.mock.On("CheckCurrentPassword", ctx, userID, currentPassword)}
}

func (_c *MockUserRepository_CheckCurrentPassword_Call) Run(run func(ctx context.Context, userID uuid.UUID, currentPassword string)) *MockUserRepository_CheckCurrentPassword_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID), args[2].(string))
	})
	return _c
}

func (_c *MockUserRepository_CheckCurrentPassword_Call) Return(_a0 error) *MockUserRepository_CheckCurrentPassword_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockUserRepository_CheckCurrentPassword_Call) RunAndReturn(run func(context.Context, uuid.UUID, string) error) *MockUserRepository_CheckCurrentPassword_Call {
	_c.Call.Return(run)
	return _c
}

// CountPrefixUsername provides a mock function with given fields: ctx, prefixUsername
func (_m *MockUserRepository) CountPrefixUsername(ctx context.Context, prefixUsername string) (int, error) {
	ret := _m.Called(ctx, prefixUsername)

	if len(ret) == 0 {
		panic("no return value specified for CountPrefixUsername")
	}

	var r0 int
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (int, error)); ok {
		return rf(ctx, prefixUsername)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) int); ok {
		r0 = rf(ctx, prefixUsername)
	} else {
		r0 = ret.Get(0).(int)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, prefixUsername)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockUserRepository_CountPrefixUsername_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CountPrefixUsername'
type MockUserRepository_CountPrefixUsername_Call struct {
	*mock.Call
}

// CountPrefixUsername is a helper method to define mock.On call
//   - ctx context.Context
//   - prefixUsername string
func (_e *MockUserRepository_Expecter) CountPrefixUsername(ctx interface{}, prefixUsername interface{}) *MockUserRepository_CountPrefixUsername_Call {
	return &MockUserRepository_CountPrefixUsername_Call{Call: _e.mock.On("CountPrefixUsername", ctx, prefixUsername)}
}

func (_c *MockUserRepository_CountPrefixUsername_Call) Run(run func(ctx context.Context, prefixUsername string)) *MockUserRepository_CountPrefixUsername_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockUserRepository_CountPrefixUsername_Call) Return(_a0 int, _a1 error) *MockUserRepository_CountPrefixUsername_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockUserRepository_CountPrefixUsername_Call) RunAndReturn(run func(context.Context, string) (int, error)) *MockUserRepository_CountPrefixUsername_Call {
	_c.Call.Return(run)
	return _c
}

// CountUsers provides a mock function with given fields: ctx, input
func (_m *MockUserRepository) CountUsers(ctx context.Context, input repository.ListUsersInput) (int64, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for CountUsers")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.ListUsersInput) (int64, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, repository.ListUsersInput) int64); ok {
		r0 = rf(ctx, input)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, repository.ListUsersInput) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockUserRepository_CountUsers_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CountUsers'
type MockUserRepository_CountUsers_Call struct {
	*mock.Call
}

// CountUsers is a helper method to define mock.On call
//   - ctx context.Context
//   - input repository.ListUsersInput
func (_e *MockUserRepository_Expecter) CountUsers(ctx interface{}, input interface{}) *MockUserRepository_CountUsers_Call {
	return &MockUserRepository_CountUsers_Call{Call: _e.mock.On("CountUsers", ctx, input)}
}

func (_c *MockUserRepository_CountUsers_Call) Run(run func(ctx context.Context, input repository.ListUsersInput)) *MockUserRepository_CountUsers_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.ListUsersInput))
	})
	return _c
}

func (_c *MockUserRepository_CountUsers_Call) Return(_a0 int64, _a1 error) *MockUserRepository_CountUsers_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockUserRepository_CountUsers_Call) RunAndReturn(run func(context.Context, repository.ListUsersInput) (int64, error)) *MockUserRepository_CountUsers_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteUser provides a mock function with given fields: ctx, userID
func (_m *MockUserRepository) DeleteUser(ctx context.Context, userID uuid.UUID) error {
	ret := _m.Called(ctx, userID)

	if len(ret) == 0 {
		panic("no return value specified for DeleteUser")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) error); ok {
		r0 = rf(ctx, userID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockUserRepository_DeleteUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteUser'
type MockUserRepository_DeleteUser_Call struct {
	*mock.Call
}

// DeleteUser is a helper method to define mock.On call
//   - ctx context.Context
//   - userID uuid.UUID
func (_e *MockUserRepository_Expecter) DeleteUser(ctx interface{}, userID interface{}) *MockUserRepository_DeleteUser_Call {
	return &MockUserRepository_DeleteUser_Call{Call: _e.mock.On("DeleteUser", ctx, userID)}
}

func (_c *MockUserRepository_DeleteUser_Call) Run(run func(ctx context.Context, userID uuid.UUID)) *MockUserRepository_DeleteUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *MockUserRepository_DeleteUser_Call) Return(_a0 error) *MockUserRepository_DeleteUser_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockUserRepository_DeleteUser_Call) RunAndReturn(run func(context.Context, uuid.UUID) error) *MockUserRepository_DeleteUser_Call {
	_c.Call.Return(run)
	return _c
}

// FindAuthUser provides a mock function with given fields: ctx, filters
func (_m *MockUserRepository) FindAuthUser(ctx context.Context, filters dto.AuthUserFilter) (*entities.AuthUser, error) {
	ret := _m.Called(ctx, filters)

	if len(ret) == 0 {
		panic("no return value specified for FindAuthUser")
	}

	var r0 *entities.AuthUser
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.AuthUserFilter) (*entities.AuthUser, error)); ok {
		return rf(ctx, filters)
	}
	if rf, ok := ret.Get(0).(func(context.Context, dto.AuthUserFilter) *entities.AuthUser); ok {
		r0 = rf(ctx, filters)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.AuthUser)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, dto.AuthUserFilter) error); ok {
		r1 = rf(ctx, filters)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockUserRepository_FindAuthUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindAuthUser'
type MockUserRepository_FindAuthUser_Call struct {
	*mock.Call
}

// FindAuthUser is a helper method to define mock.On call
//   - ctx context.Context
//   - filters dto.AuthUserFilter
func (_e *MockUserRepository_Expecter) FindAuthUser(ctx interface{}, filters interface{}) *MockUserRepository_FindAuthUser_Call {
	return &MockUserRepository_FindAuthUser_Call{Call: _e.mock.On("FindAuthUser", ctx, filters)}
}

func (_c *MockUserRepository_FindAuthUser_Call) Run(run func(ctx context.Context, filters dto.AuthUserFilter)) *MockUserRepository_FindAuthUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.AuthUserFilter))
	})
	return _c
}

func (_c *MockUserRepository_FindAuthUser_Call) Return(_a0 *entities.AuthUser, _a1 error) *MockUserRepository_FindAuthUser_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockUserRepository_FindAuthUser_Call) RunAndReturn(run func(context.Context, dto.AuthUserFilter) (*entities.AuthUser, error)) *MockUserRepository_FindAuthUser_Call {
	_c.Call.Return(run)
	return _c
}

// FindUser provides a mock function with given fields: ctx, filters
func (_m *MockUserRepository) FindUser(ctx context.Context, filters repository.FindUserFilter) (*entities.User, error) {
	ret := _m.Called(ctx, filters)

	if len(ret) == 0 {
		panic("no return value specified for FindUser")
	}

	var r0 *entities.User
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.FindUserFilter) (*entities.User, error)); ok {
		return rf(ctx, filters)
	}
	if rf, ok := ret.Get(0).(func(context.Context, repository.FindUserFilter) *entities.User); ok {
		r0 = rf(ctx, filters)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.User)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, repository.FindUserFilter) error); ok {
		r1 = rf(ctx, filters)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockUserRepository_FindUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindUser'
type MockUserRepository_FindUser_Call struct {
	*mock.Call
}

// FindUser is a helper method to define mock.On call
//   - ctx context.Context
//   - filters repository.FindUserFilter
func (_e *MockUserRepository_Expecter) FindUser(ctx interface{}, filters interface{}) *MockUserRepository_FindUser_Call {
	return &MockUserRepository_FindUser_Call{Call: _e.mock.On("FindUser", ctx, filters)}
}

func (_c *MockUserRepository_FindUser_Call) Run(run func(ctx context.Context, filters repository.FindUserFilter)) *MockUserRepository_FindUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.FindUserFilter))
	})
	return _c
}

func (_c *MockUserRepository_FindUser_Call) Return(_a0 *entities.User, _a1 error) *MockUserRepository_FindUser_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockUserRepository_FindUser_Call) RunAndReturn(run func(context.Context, repository.FindUserFilter) (*entities.User, error)) *MockUserRepository_FindUser_Call {
	_c.Call.Return(run)
	return _c
}

// FindUserByID provides a mock function with given fields: ctx, id
func (_m *MockUserRepository) FindUserByID(ctx context.Context, id uuid.UUID) (*entities.User, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for FindUserByID")
	}

	var r0 *entities.User
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) (*entities.User, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) *entities.User); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.User)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uuid.UUID) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockUserRepository_FindUserByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindUserByID'
type MockUserRepository_FindUserByID_Call struct {
	*mock.Call
}

// FindUserByID is a helper method to define mock.On call
//   - ctx context.Context
//   - id uuid.UUID
func (_e *MockUserRepository_Expecter) FindUserByID(ctx interface{}, id interface{}) *MockUserRepository_FindUserByID_Call {
	return &MockUserRepository_FindUserByID_Call{Call: _e.mock.On("FindUserByID", ctx, id)}
}

func (_c *MockUserRepository_FindUserByID_Call) Run(run func(ctx context.Context, id uuid.UUID)) *MockUserRepository_FindUserByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *MockUserRepository_FindUserByID_Call) Return(_a0 *entities.User, _a1 error) *MockUserRepository_FindUserByID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockUserRepository_FindUserByID_Call) RunAndReturn(run func(context.Context, uuid.UUID) (*entities.User, error)) *MockUserRepository_FindUserByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetAppPermission provides a mock function with given fields: ctx, userID
func (_m *MockUserRepository) GetAppPermission(ctx context.Context, userID uuid.UUID) ([]entities.AppPermission, error) {
	ret := _m.Called(ctx, userID)

	if len(ret) == 0 {
		panic("no return value specified for GetAppPermission")
	}

	var r0 []entities.AppPermission
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) ([]entities.AppPermission, error)); ok {
		return rf(ctx, userID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) []entities.AppPermission); ok {
		r0 = rf(ctx, userID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]entities.AppPermission)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uuid.UUID) error); ok {
		r1 = rf(ctx, userID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockUserRepository_GetAppPermission_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAppPermission'
type MockUserRepository_GetAppPermission_Call struct {
	*mock.Call
}

// GetAppPermission is a helper method to define mock.On call
//   - ctx context.Context
//   - userID uuid.UUID
func (_e *MockUserRepository_Expecter) GetAppPermission(ctx interface{}, userID interface{}) *MockUserRepository_GetAppPermission_Call {
	return &MockUserRepository_GetAppPermission_Call{Call: _e.mock.On("GetAppPermission", ctx, userID)}
}

func (_c *MockUserRepository_GetAppPermission_Call) Run(run func(ctx context.Context, userID uuid.UUID)) *MockUserRepository_GetAppPermission_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *MockUserRepository_GetAppPermission_Call) Return(_a0 []entities.AppPermission, _a1 error) *MockUserRepository_GetAppPermission_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockUserRepository_GetAppPermission_Call) RunAndReturn(run func(context.Context, uuid.UUID) ([]entities.AppPermission, error)) *MockUserRepository_GetAppPermission_Call {
	_c.Call.Return(run)
	return _c
}

// GetOrgPermission provides a mock function with given fields: ctx, userID, orgID
func (_m *MockUserRepository) GetOrgPermission(ctx context.Context, userID uuid.UUID, orgID uuid.UUID) ([]entities.RepoPermission, error) {
	ret := _m.Called(ctx, userID, orgID)

	if len(ret) == 0 {
		panic("no return value specified for GetOrgPermission")
	}

	var r0 []entities.RepoPermission
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID, uuid.UUID) ([]entities.RepoPermission, error)); ok {
		return rf(ctx, userID, orgID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID, uuid.UUID) []entities.RepoPermission); ok {
		r0 = rf(ctx, userID, orgID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]entities.RepoPermission)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uuid.UUID, uuid.UUID) error); ok {
		r1 = rf(ctx, userID, orgID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockUserRepository_GetOrgPermission_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetOrgPermission'
type MockUserRepository_GetOrgPermission_Call struct {
	*mock.Call
}

// GetOrgPermission is a helper method to define mock.On call
//   - ctx context.Context
//   - userID uuid.UUID
//   - orgID uuid.UUID
func (_e *MockUserRepository_Expecter) GetOrgPermission(ctx interface{}, userID interface{}, orgID interface{}) *MockUserRepository_GetOrgPermission_Call {
	return &MockUserRepository_GetOrgPermission_Call{Call: _e.mock.On("GetOrgPermission", ctx, userID, orgID)}
}

func (_c *MockUserRepository_GetOrgPermission_Call) Run(run func(ctx context.Context, userID uuid.UUID, orgID uuid.UUID)) *MockUserRepository_GetOrgPermission_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID), args[2].(uuid.UUID))
	})
	return _c
}

func (_c *MockUserRepository_GetOrgPermission_Call) Return(_a0 []entities.RepoPermission, _a1 error) *MockUserRepository_GetOrgPermission_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockUserRepository_GetOrgPermission_Call) RunAndReturn(run func(context.Context, uuid.UUID, uuid.UUID) ([]entities.RepoPermission, error)) *MockUserRepository_GetOrgPermission_Call {
	_c.Call.Return(run)
	return _c
}

// GetRepoPermission provides a mock function with given fields: ctx, userID, repoID
func (_m *MockUserRepository) GetRepoPermission(ctx context.Context, userID uuid.UUID, repoID uuid.UUID) ([]entities.RepoPermission, error) {
	ret := _m.Called(ctx, userID, repoID)

	if len(ret) == 0 {
		panic("no return value specified for GetRepoPermission")
	}

	var r0 []entities.RepoPermission
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID, uuid.UUID) ([]entities.RepoPermission, error)); ok {
		return rf(ctx, userID, repoID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID, uuid.UUID) []entities.RepoPermission); ok {
		r0 = rf(ctx, userID, repoID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]entities.RepoPermission)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uuid.UUID, uuid.UUID) error); ok {
		r1 = rf(ctx, userID, repoID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockUserRepository_GetRepoPermission_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetRepoPermission'
type MockUserRepository_GetRepoPermission_Call struct {
	*mock.Call
}

// GetRepoPermission is a helper method to define mock.On call
//   - ctx context.Context
//   - userID uuid.UUID
//   - repoID uuid.UUID
func (_e *MockUserRepository_Expecter) GetRepoPermission(ctx interface{}, userID interface{}, repoID interface{}) *MockUserRepository_GetRepoPermission_Call {
	return &MockUserRepository_GetRepoPermission_Call{Call: _e.mock.On("GetRepoPermission", ctx, userID, repoID)}
}

func (_c *MockUserRepository_GetRepoPermission_Call) Run(run func(ctx context.Context, userID uuid.UUID, repoID uuid.UUID)) *MockUserRepository_GetRepoPermission_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID), args[2].(uuid.UUID))
	})
	return _c
}

func (_c *MockUserRepository_GetRepoPermission_Call) Return(_a0 []entities.RepoPermission, _a1 error) *MockUserRepository_GetRepoPermission_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockUserRepository_GetRepoPermission_Call) RunAndReturn(run func(context.Context, uuid.UUID, uuid.UUID) ([]entities.RepoPermission, error)) *MockUserRepository_GetRepoPermission_Call {
	_c.Call.Return(run)
	return _c
}

// ListUsers provides a mock function with given fields: ctx, input
func (_m *MockUserRepository) ListUsers(ctx context.Context, input repository.ListUsersInput) ([]entities.User, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for ListUsers")
	}

	var r0 []entities.User
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.ListUsersInput) ([]entities.User, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, repository.ListUsersInput) []entities.User); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]entities.User)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, repository.ListUsersInput) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockUserRepository_ListUsers_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListUsers'
type MockUserRepository_ListUsers_Call struct {
	*mock.Call
}

// ListUsers is a helper method to define mock.On call
//   - ctx context.Context
//   - input repository.ListUsersInput
func (_e *MockUserRepository_Expecter) ListUsers(ctx interface{}, input interface{}) *MockUserRepository_ListUsers_Call {
	return &MockUserRepository_ListUsers_Call{Call: _e.mock.On("ListUsers", ctx, input)}
}

func (_c *MockUserRepository_ListUsers_Call) Run(run func(ctx context.Context, input repository.ListUsersInput)) *MockUserRepository_ListUsers_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.ListUsersInput))
	})
	return _c
}

func (_c *MockUserRepository_ListUsers_Call) Return(_a0 []entities.User, _a1 error) *MockUserRepository_ListUsers_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockUserRepository_ListUsers_Call) RunAndReturn(run func(context.Context, repository.ListUsersInput) ([]entities.User, error)) *MockUserRepository_ListUsers_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockUserRepository creates a new instance of MockUserRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockUserRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockUserRepository {
	mock := &MockUserRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
