// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	dto "api-server/internal/dto"
	entities "api-server/internal/entities"
	context "context"

	mock "github.com/stretchr/testify/mock"

	repository "api-server/internal/repositories"

	uuid "github.com/google/uuid"
)

// MockOrgMemberRepository is an autogenerated mock type for the OrgMemberRepository type
type MockOrgMemberRepository struct {
	mock.Mock
}

type MockOrgMemberRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockOrgMemberRepository) EXPECT() *MockOrgMemberRepository_Expecter {
	return &MockOrgMemberRepository_Expecter{mock: &_m.Mock}
}

// CountMembers provides a mock function with given fields: ctx, input
func (_m *MockOrgMemberRepository) CountMembers(ctx context.Context, input dto.ListOrgMembersInput) (int, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for CountMembers")
	}

	var r0 int
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.ListOrgMembersInput) (int, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, dto.ListOrgMembersInput) int); ok {
		r0 = rf(ctx, input)
	} else {
		r0 = ret.Get(0).(int)
	}

	if rf, ok := ret.Get(1).(func(context.Context, dto.ListOrgMembersInput) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockOrgMemberRepository_CountMembers_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CountMembers'
type MockOrgMemberRepository_CountMembers_Call struct {
	*mock.Call
}

// CountMembers is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.ListOrgMembersInput
func (_e *MockOrgMemberRepository_Expecter) CountMembers(ctx interface{}, input interface{}) *MockOrgMemberRepository_CountMembers_Call {
	return &MockOrgMemberRepository_CountMembers_Call{Call: _e.mock.On("CountMembers", ctx, input)}
}

func (_c *MockOrgMemberRepository_CountMembers_Call) Run(run func(ctx context.Context, input dto.ListOrgMembersInput)) *MockOrgMemberRepository_CountMembers_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.ListOrgMembersInput))
	})
	return _c
}

func (_c *MockOrgMemberRepository_CountMembers_Call) Return(_a0 int, _a1 error) *MockOrgMemberRepository_CountMembers_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockOrgMemberRepository_CountMembers_Call) RunAndReturn(run func(context.Context, dto.ListOrgMembersInput) (int, error)) *MockOrgMemberRepository_CountMembers_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteMembersByOrganizationID provides a mock function with given fields: ctx, organizationID
func (_m *MockOrgMemberRepository) DeleteMembersByOrganizationID(ctx context.Context, organizationID uuid.UUID) error {
	ret := _m.Called(ctx, organizationID)

	if len(ret) == 0 {
		panic("no return value specified for DeleteMembersByOrganizationID")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) error); ok {
		r0 = rf(ctx, organizationID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockOrgMemberRepository_DeleteMembersByOrganizationID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteMembersByOrganizationID'
type MockOrgMemberRepository_DeleteMembersByOrganizationID_Call struct {
	*mock.Call
}

// DeleteMembersByOrganizationID is a helper method to define mock.On call
//   - ctx context.Context
//   - organizationID uuid.UUID
func (_e *MockOrgMemberRepository_Expecter) DeleteMembersByOrganizationID(ctx interface{}, organizationID interface{}) *MockOrgMemberRepository_DeleteMembersByOrganizationID_Call {
	return &MockOrgMemberRepository_DeleteMembersByOrganizationID_Call{Call: _e.mock.On("DeleteMembersByOrganizationID", ctx, organizationID)}
}

func (_c *MockOrgMemberRepository_DeleteMembersByOrganizationID_Call) Run(run func(ctx context.Context, organizationID uuid.UUID)) *MockOrgMemberRepository_DeleteMembersByOrganizationID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *MockOrgMemberRepository_DeleteMembersByOrganizationID_Call) Return(_a0 error) *MockOrgMemberRepository_DeleteMembersByOrganizationID_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockOrgMemberRepository_DeleteMembersByOrganizationID_Call) RunAndReturn(run func(context.Context, uuid.UUID) error) *MockOrgMemberRepository_DeleteMembersByOrganizationID_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteOrgMemberByID provides a mock function with given fields: ctx, id
func (_m *MockOrgMemberRepository) DeleteOrgMemberByID(ctx context.Context, id uuid.UUID) error {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for DeleteOrgMemberByID")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) error); ok {
		r0 = rf(ctx, id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockOrgMemberRepository_DeleteOrgMemberByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteOrgMemberByID'
type MockOrgMemberRepository_DeleteOrgMemberByID_Call struct {
	*mock.Call
}

// DeleteOrgMemberByID is a helper method to define mock.On call
//   - ctx context.Context
//   - id uuid.UUID
func (_e *MockOrgMemberRepository_Expecter) DeleteOrgMemberByID(ctx interface{}, id interface{}) *MockOrgMemberRepository_DeleteOrgMemberByID_Call {
	return &MockOrgMemberRepository_DeleteOrgMemberByID_Call{Call: _e.mock.On("DeleteOrgMemberByID", ctx, id)}
}

func (_c *MockOrgMemberRepository_DeleteOrgMemberByID_Call) Run(run func(ctx context.Context, id uuid.UUID)) *MockOrgMemberRepository_DeleteOrgMemberByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *MockOrgMemberRepository_DeleteOrgMemberByID_Call) Return(_a0 error) *MockOrgMemberRepository_DeleteOrgMemberByID_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockOrgMemberRepository_DeleteOrgMemberByID_Call) RunAndReturn(run func(context.Context, uuid.UUID) error) *MockOrgMemberRepository_DeleteOrgMemberByID_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteOrgMembersByUserID provides a mock function with given fields: ctx, userID
func (_m *MockOrgMemberRepository) DeleteOrgMembersByUserID(ctx context.Context, userID uuid.UUID) error {
	ret := _m.Called(ctx, userID)

	if len(ret) == 0 {
		panic("no return value specified for DeleteOrgMembersByUserID")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) error); ok {
		r0 = rf(ctx, userID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockOrgMemberRepository_DeleteOrgMembersByUserID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteOrgMembersByUserID'
type MockOrgMemberRepository_DeleteOrgMembersByUserID_Call struct {
	*mock.Call
}

// DeleteOrgMembersByUserID is a helper method to define mock.On call
//   - ctx context.Context
//   - userID uuid.UUID
func (_e *MockOrgMemberRepository_Expecter) DeleteOrgMembersByUserID(ctx interface{}, userID interface{}) *MockOrgMemberRepository_DeleteOrgMembersByUserID_Call {
	return &MockOrgMemberRepository_DeleteOrgMembersByUserID_Call{Call: _e.mock.On("DeleteOrgMembersByUserID", ctx, userID)}
}

func (_c *MockOrgMemberRepository_DeleteOrgMembersByUserID_Call) Run(run func(ctx context.Context, userID uuid.UUID)) *MockOrgMemberRepository_DeleteOrgMembersByUserID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *MockOrgMemberRepository_DeleteOrgMembersByUserID_Call) Return(_a0 error) *MockOrgMemberRepository_DeleteOrgMembersByUserID_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockOrgMemberRepository_DeleteOrgMembersByUserID_Call) RunAndReturn(run func(context.Context, uuid.UUID) error) *MockOrgMemberRepository_DeleteOrgMembersByUserID_Call {
	_c.Call.Return(run)
	return _c
}

// FindOrgMember provides a mock function with given fields: ctx, filter
func (_m *MockOrgMemberRepository) FindOrgMember(ctx context.Context, filter repository.FilterOrgMember) (*entities.OrgMember, error) {
	ret := _m.Called(ctx, filter)

	if len(ret) == 0 {
		panic("no return value specified for FindOrgMember")
	}

	var r0 *entities.OrgMember
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.FilterOrgMember) (*entities.OrgMember, error)); ok {
		return rf(ctx, filter)
	}
	if rf, ok := ret.Get(0).(func(context.Context, repository.FilterOrgMember) *entities.OrgMember); ok {
		r0 = rf(ctx, filter)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.OrgMember)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, repository.FilterOrgMember) error); ok {
		r1 = rf(ctx, filter)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockOrgMemberRepository_FindOrgMember_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindOrgMember'
type MockOrgMemberRepository_FindOrgMember_Call struct {
	*mock.Call
}

// FindOrgMember is a helper method to define mock.On call
//   - ctx context.Context
//   - filter repository.FilterOrgMember
func (_e *MockOrgMemberRepository_Expecter) FindOrgMember(ctx interface{}, filter interface{}) *MockOrgMemberRepository_FindOrgMember_Call {
	return &MockOrgMemberRepository_FindOrgMember_Call{Call: _e.mock.On("FindOrgMember", ctx, filter)}
}

func (_c *MockOrgMemberRepository_FindOrgMember_Call) Run(run func(ctx context.Context, filter repository.FilterOrgMember)) *MockOrgMemberRepository_FindOrgMember_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.FilterOrgMember))
	})
	return _c
}

func (_c *MockOrgMemberRepository_FindOrgMember_Call) Return(_a0 *entities.OrgMember, _a1 error) *MockOrgMemberRepository_FindOrgMember_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockOrgMemberRepository_FindOrgMember_Call) RunAndReturn(run func(context.Context, repository.FilterOrgMember) (*entities.OrgMember, error)) *MockOrgMemberRepository_FindOrgMember_Call {
	_c.Call.Return(run)
	return _c
}

// ListMembers provides a mock function with given fields: ctx, input
func (_m *MockOrgMemberRepository) ListMembers(ctx context.Context, input dto.ListOrgMembersInput) ([]entities.OrgMemberInfo, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for ListMembers")
	}

	var r0 []entities.OrgMemberInfo
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.ListOrgMembersInput) ([]entities.OrgMemberInfo, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, dto.ListOrgMembersInput) []entities.OrgMemberInfo); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]entities.OrgMemberInfo)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, dto.ListOrgMembersInput) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockOrgMemberRepository_ListMembers_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListMembers'
type MockOrgMemberRepository_ListMembers_Call struct {
	*mock.Call
}

// ListMembers is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.ListOrgMembersInput
func (_e *MockOrgMemberRepository_Expecter) ListMembers(ctx interface{}, input interface{}) *MockOrgMemberRepository_ListMembers_Call {
	return &MockOrgMemberRepository_ListMembers_Call{Call: _e.mock.On("ListMembers", ctx, input)}
}

func (_c *MockOrgMemberRepository_ListMembers_Call) Run(run func(ctx context.Context, input dto.ListOrgMembersInput)) *MockOrgMemberRepository_ListMembers_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.ListOrgMembersInput))
	})
	return _c
}

func (_c *MockOrgMemberRepository_ListMembers_Call) Return(_a0 []entities.OrgMemberInfo, _a1 error) *MockOrgMemberRepository_ListMembers_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockOrgMemberRepository_ListMembers_Call) RunAndReturn(run func(context.Context, dto.ListOrgMembersInput) ([]entities.OrgMemberInfo, error)) *MockOrgMemberRepository_ListMembers_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockOrgMemberRepository creates a new instance of MockOrgMemberRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockOrgMemberRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockOrgMemberRepository {
	mock := &MockOrgMemberRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
