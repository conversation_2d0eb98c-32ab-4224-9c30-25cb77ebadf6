// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	context "context"

	clause "gorm.io/gorm/clause"

	dto "api-server/internal/dto"

	entities "api-server/internal/entities"

	gorm "gorm.io/gorm"

	mock "github.com/stretchr/testify/mock"

	repository "api-server/internal/repositories"

	sql "database/sql"

	types "api-server/internal/types"

	uuid "github.com/google/uuid"
)

// MockRepository is an autogenerated mock type for the Repository type
type MockRepository struct {
	mock.Mock
}

type MockRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockRepository) EXPECT() *MockRepository_Expecter {
	return &MockRepository_Expecter{mock: &_m.Mock}
}

// BeginTransaction provides a mock function with given fields: ctx, opts
func (_m *MockRepository) BeginTransaction(ctx context.Context, opts ...*sql.TxOptions) (context.Context, error) {
	_va := make([]interface{}, len(opts))
	for _i := range opts {
		_va[_i] = opts[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for BeginTransaction")
	}

	var r0 context.Context
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, ...*sql.TxOptions) (context.Context, error)); ok {
		return rf(ctx, opts...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, ...*sql.TxOptions) context.Context); ok {
		r0 = rf(ctx, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(context.Context)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, ...*sql.TxOptions) error); ok {
		r1 = rf(ctx, opts...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_BeginTransaction_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'BeginTransaction'
type MockRepository_BeginTransaction_Call struct {
	*mock.Call
}

// BeginTransaction is a helper method to define mock.On call
//   - ctx context.Context
//   - opts ...*sql.TxOptions
func (_e *MockRepository_Expecter) BeginTransaction(ctx interface{}, opts ...interface{}) *MockRepository_BeginTransaction_Call {
	return &MockRepository_BeginTransaction_Call{Call: _e.mock.On("BeginTransaction",
		append([]interface{}{ctx}, opts...)...)}
}

func (_c *MockRepository_BeginTransaction_Call) Run(run func(ctx context.Context, opts ...*sql.TxOptions)) *MockRepository_BeginTransaction_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]*sql.TxOptions, len(args)-1)
		for i, a := range args[1:] {
			if a != nil {
				variadicArgs[i] = a.(*sql.TxOptions)
			}
		}
		run(args[0].(context.Context), variadicArgs...)
	})
	return _c
}

func (_c *MockRepository_BeginTransaction_Call) Return(_a0 context.Context, _a1 error) *MockRepository_BeginTransaction_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_BeginTransaction_Call) RunAndReturn(run func(context.Context, ...*sql.TxOptions) (context.Context, error)) *MockRepository_BeginTransaction_Call {
	_c.Call.Return(run)
	return _c
}

// BulkCreateEnv provides a mock function with given fields: ctx, in
func (_m *MockRepository) BulkCreateEnv(ctx context.Context, in repository.BulkCreateEnvInput) error {
	ret := _m.Called(ctx, in)

	if len(ret) == 0 {
		panic("no return value specified for BulkCreateEnv")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.BulkCreateEnvInput) error); ok {
		r0 = rf(ctx, in)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepository_BulkCreateEnv_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'BulkCreateEnv'
type MockRepository_BulkCreateEnv_Call struct {
	*mock.Call
}

// BulkCreateEnv is a helper method to define mock.On call
//   - ctx context.Context
//   - in repository.BulkCreateEnvInput
func (_e *MockRepository_Expecter) BulkCreateEnv(ctx interface{}, in interface{}) *MockRepository_BulkCreateEnv_Call {
	return &MockRepository_BulkCreateEnv_Call{Call: _e.mock.On("BulkCreateEnv", ctx, in)}
}

func (_c *MockRepository_BulkCreateEnv_Call) Run(run func(ctx context.Context, in repository.BulkCreateEnvInput)) *MockRepository_BulkCreateEnv_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.BulkCreateEnvInput))
	})
	return _c
}

func (_c *MockRepository_BulkCreateEnv_Call) Return(_a0 error) *MockRepository_BulkCreateEnv_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepository_BulkCreateEnv_Call) RunAndReturn(run func(context.Context, repository.BulkCreateEnvInput) error) *MockRepository_BulkCreateEnv_Call {
	_c.Call.Return(run)
	return _c
}

// CheckCurrentPassword provides a mock function with given fields: ctx, userID, currentPassword
func (_m *MockRepository) CheckCurrentPassword(ctx context.Context, userID uuid.UUID, currentPassword string) error {
	ret := _m.Called(ctx, userID, currentPassword)

	if len(ret) == 0 {
		panic("no return value specified for CheckCurrentPassword")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID, string) error); ok {
		r0 = rf(ctx, userID, currentPassword)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepository_CheckCurrentPassword_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CheckCurrentPassword'
type MockRepository_CheckCurrentPassword_Call struct {
	*mock.Call
}

// CheckCurrentPassword is a helper method to define mock.On call
//   - ctx context.Context
//   - userID uuid.UUID
//   - currentPassword string
func (_e *MockRepository_Expecter) CheckCurrentPassword(ctx interface{}, userID interface{}, currentPassword interface{}) *MockRepository_CheckCurrentPassword_Call {
	return &MockRepository_CheckCurrentPassword_Call{Call: _e.mock.On("CheckCurrentPassword", ctx, userID, currentPassword)}
}

func (_c *MockRepository_CheckCurrentPassword_Call) Run(run func(ctx context.Context, userID uuid.UUID, currentPassword string)) *MockRepository_CheckCurrentPassword_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID), args[2].(string))
	})
	return _c
}

func (_c *MockRepository_CheckCurrentPassword_Call) Return(_a0 error) *MockRepository_CheckCurrentPassword_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepository_CheckCurrentPassword_Call) RunAndReturn(run func(context.Context, uuid.UUID, string) error) *MockRepository_CheckCurrentPassword_Call {
	_c.Call.Return(run)
	return _c
}

// CommitTransaction provides a mock function with given fields: ctx
func (_m *MockRepository) CommitTransaction(ctx context.Context) *gorm.DB {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for CommitTransaction")
	}

	var r0 *gorm.DB
	if rf, ok := ret.Get(0).(func(context.Context) *gorm.DB); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*gorm.DB)
		}
	}

	return r0
}

// MockRepository_CommitTransaction_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CommitTransaction'
type MockRepository_CommitTransaction_Call struct {
	*mock.Call
}

// CommitTransaction is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockRepository_Expecter) CommitTransaction(ctx interface{}) *MockRepository_CommitTransaction_Call {
	return &MockRepository_CommitTransaction_Call{Call: _e.mock.On("CommitTransaction", ctx)}
}

func (_c *MockRepository_CommitTransaction_Call) Run(run func(ctx context.Context)) *MockRepository_CommitTransaction_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockRepository_CommitTransaction_Call) Return(_a0 *gorm.DB) *MockRepository_CommitTransaction_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepository_CommitTransaction_Call) RunAndReturn(run func(context.Context) *gorm.DB) *MockRepository_CommitTransaction_Call {
	_c.Call.Return(run)
	return _c
}

// CountAccessToken provides a mock function with given fields: ctx, in
func (_m *MockRepository) CountAccessToken(ctx context.Context, in repository.ListAccessTokenQuery) (int64, error) {
	ret := _m.Called(ctx, in)

	if len(ret) == 0 {
		panic("no return value specified for CountAccessToken")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.ListAccessTokenQuery) (int64, error)); ok {
		return rf(ctx, in)
	}
	if rf, ok := ret.Get(0).(func(context.Context, repository.ListAccessTokenQuery) int64); ok {
		r0 = rf(ctx, in)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, repository.ListAccessTokenQuery) error); ok {
		r1 = rf(ctx, in)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_CountAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CountAccessToken'
type MockRepository_CountAccessToken_Call struct {
	*mock.Call
}

// CountAccessToken is a helper method to define mock.On call
//   - ctx context.Context
//   - in repository.ListAccessTokenQuery
func (_e *MockRepository_Expecter) CountAccessToken(ctx interface{}, in interface{}) *MockRepository_CountAccessToken_Call {
	return &MockRepository_CountAccessToken_Call{Call: _e.mock.On("CountAccessToken", ctx, in)}
}

func (_c *MockRepository_CountAccessToken_Call) Run(run func(ctx context.Context, in repository.ListAccessTokenQuery)) *MockRepository_CountAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.ListAccessTokenQuery))
	})
	return _c
}

func (_c *MockRepository_CountAccessToken_Call) Return(_a0 int64, _a1 error) *MockRepository_CountAccessToken_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_CountAccessToken_Call) RunAndReturn(run func(context.Context, repository.ListAccessTokenQuery) (int64, error)) *MockRepository_CountAccessToken_Call {
	_c.Call.Return(run)
	return _c
}

// CountAllSSHKeys provides a mock function with given fields: ctx, query
func (_m *MockRepository) CountAllSSHKeys(ctx context.Context, query repository.GetSSHKeyQuery) (int64, error) {
	ret := _m.Called(ctx, query)

	if len(ret) == 0 {
		panic("no return value specified for CountAllSSHKeys")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.GetSSHKeyQuery) (int64, error)); ok {
		return rf(ctx, query)
	}
	if rf, ok := ret.Get(0).(func(context.Context, repository.GetSSHKeyQuery) int64); ok {
		r0 = rf(ctx, query)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, repository.GetSSHKeyQuery) error); ok {
		r1 = rf(ctx, query)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_CountAllSSHKeys_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CountAllSSHKeys'
type MockRepository_CountAllSSHKeys_Call struct {
	*mock.Call
}

// CountAllSSHKeys is a helper method to define mock.On call
//   - ctx context.Context
//   - query repository.GetSSHKeyQuery
func (_e *MockRepository_Expecter) CountAllSSHKeys(ctx interface{}, query interface{}) *MockRepository_CountAllSSHKeys_Call {
	return &MockRepository_CountAllSSHKeys_Call{Call: _e.mock.On("CountAllSSHKeys", ctx, query)}
}

func (_c *MockRepository_CountAllSSHKeys_Call) Run(run func(ctx context.Context, query repository.GetSSHKeyQuery)) *MockRepository_CountAllSSHKeys_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.GetSSHKeyQuery))
	})
	return _c
}

func (_c *MockRepository_CountAllSSHKeys_Call) Return(_a0 int64, _a1 error) *MockRepository_CountAllSSHKeys_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_CountAllSSHKeys_Call) RunAndReturn(run func(context.Context, repository.GetSSHKeyQuery) (int64, error)) *MockRepository_CountAllSSHKeys_Call {
	_c.Call.Return(run)
	return _c
}

// CountDeployment provides a mock function with given fields: ctx, in
func (_m *MockRepository) CountDeployment(ctx context.Context, in repository.ListDeploymentInput) (int64, error) {
	ret := _m.Called(ctx, in)

	if len(ret) == 0 {
		panic("no return value specified for CountDeployment")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.ListDeploymentInput) (int64, error)); ok {
		return rf(ctx, in)
	}
	if rf, ok := ret.Get(0).(func(context.Context, repository.ListDeploymentInput) int64); ok {
		r0 = rf(ctx, in)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, repository.ListDeploymentInput) error); ok {
		r1 = rf(ctx, in)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_CountDeployment_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CountDeployment'
type MockRepository_CountDeployment_Call struct {
	*mock.Call
}

// CountDeployment is a helper method to define mock.On call
//   - ctx context.Context
//   - in repository.ListDeploymentInput
func (_e *MockRepository_Expecter) CountDeployment(ctx interface{}, in interface{}) *MockRepository_CountDeployment_Call {
	return &MockRepository_CountDeployment_Call{Call: _e.mock.On("CountDeployment", ctx, in)}
}

func (_c *MockRepository_CountDeployment_Call) Run(run func(ctx context.Context, in repository.ListDeploymentInput)) *MockRepository_CountDeployment_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.ListDeploymentInput))
	})
	return _c
}

func (_c *MockRepository_CountDeployment_Call) Return(_a0 int64, _a1 error) *MockRepository_CountDeployment_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_CountDeployment_Call) RunAndReturn(run func(context.Context, repository.ListDeploymentInput) (int64, error)) *MockRepository_CountDeployment_Call {
	_c.Call.Return(run)
	return _c
}

// CountHardware provides a mock function with given fields: ctx
func (_m *MockRepository) CountHardware(ctx context.Context) (int64, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for CountHardware")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) (int64, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) int64); ok {
		r0 = rf(ctx)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_CountHardware_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CountHardware'
type MockRepository_CountHardware_Call struct {
	*mock.Call
}

// CountHardware is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockRepository_Expecter) CountHardware(ctx interface{}) *MockRepository_CountHardware_Call {
	return &MockRepository_CountHardware_Call{Call: _e.mock.On("CountHardware", ctx)}
}

func (_c *MockRepository_CountHardware_Call) Run(run func(ctx context.Context)) *MockRepository_CountHardware_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockRepository_CountHardware_Call) Return(_a0 int64, _a1 error) *MockRepository_CountHardware_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_CountHardware_Call) RunAndReturn(run func(context.Context) (int64, error)) *MockRepository_CountHardware_Call {
	_c.Call.Return(run)
	return _c
}

// CountMembers provides a mock function with given fields: ctx, input
func (_m *MockRepository) CountMembers(ctx context.Context, input dto.ListOrgMembersInput) (int, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for CountMembers")
	}

	var r0 int
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.ListOrgMembersInput) (int, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, dto.ListOrgMembersInput) int); ok {
		r0 = rf(ctx, input)
	} else {
		r0 = ret.Get(0).(int)
	}

	if rf, ok := ret.Get(1).(func(context.Context, dto.ListOrgMembersInput) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_CountMembers_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CountMembers'
type MockRepository_CountMembers_Call struct {
	*mock.Call
}

// CountMembers is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.ListOrgMembersInput
func (_e *MockRepository_Expecter) CountMembers(ctx interface{}, input interface{}) *MockRepository_CountMembers_Call {
	return &MockRepository_CountMembers_Call{Call: _e.mock.On("CountMembers", ctx, input)}
}

func (_c *MockRepository_CountMembers_Call) Run(run func(ctx context.Context, input dto.ListOrgMembersInput)) *MockRepository_CountMembers_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.ListOrgMembersInput))
	})
	return _c
}

func (_c *MockRepository_CountMembers_Call) Return(_a0 int, _a1 error) *MockRepository_CountMembers_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_CountMembers_Call) RunAndReturn(run func(context.Context, dto.ListOrgMembersInput) (int, error)) *MockRepository_CountMembers_Call {
	_c.Call.Return(run)
	return _c
}

// CountOrgAccessToken provides a mock function with given fields: ctx, in
func (_m *MockRepository) CountOrgAccessToken(ctx context.Context, in repository.ListOrgAccessTokenQuery) (int64, error) {
	ret := _m.Called(ctx, in)

	if len(ret) == 0 {
		panic("no return value specified for CountOrgAccessToken")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.ListOrgAccessTokenQuery) (int64, error)); ok {
		return rf(ctx, in)
	}
	if rf, ok := ret.Get(0).(func(context.Context, repository.ListOrgAccessTokenQuery) int64); ok {
		r0 = rf(ctx, in)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, repository.ListOrgAccessTokenQuery) error); ok {
		r1 = rf(ctx, in)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_CountOrgAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CountOrgAccessToken'
type MockRepository_CountOrgAccessToken_Call struct {
	*mock.Call
}

// CountOrgAccessToken is a helper method to define mock.On call
//   - ctx context.Context
//   - in repository.ListOrgAccessTokenQuery
func (_e *MockRepository_Expecter) CountOrgAccessToken(ctx interface{}, in interface{}) *MockRepository_CountOrgAccessToken_Call {
	return &MockRepository_CountOrgAccessToken_Call{Call: _e.mock.On("CountOrgAccessToken", ctx, in)}
}

func (_c *MockRepository_CountOrgAccessToken_Call) Run(run func(ctx context.Context, in repository.ListOrgAccessTokenQuery)) *MockRepository_CountOrgAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.ListOrgAccessTokenQuery))
	})
	return _c
}

func (_c *MockRepository_CountOrgAccessToken_Call) Return(_a0 int64, _a1 error) *MockRepository_CountOrgAccessToken_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_CountOrgAccessToken_Call) RunAndReturn(run func(context.Context, repository.ListOrgAccessTokenQuery) (int64, error)) *MockRepository_CountOrgAccessToken_Call {
	_c.Call.Return(run)
	return _c
}

// CountPrefixUsername provides a mock function with given fields: ctx, prefixUsername
func (_m *MockRepository) CountPrefixUsername(ctx context.Context, prefixUsername string) (int, error) {
	ret := _m.Called(ctx, prefixUsername)

	if len(ret) == 0 {
		panic("no return value specified for CountPrefixUsername")
	}

	var r0 int
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (int, error)); ok {
		return rf(ctx, prefixUsername)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) int); ok {
		r0 = rf(ctx, prefixUsername)
	} else {
		r0 = ret.Get(0).(int)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, prefixUsername)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_CountPrefixUsername_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CountPrefixUsername'
type MockRepository_CountPrefixUsername_Call struct {
	*mock.Call
}

// CountPrefixUsername is a helper method to define mock.On call
//   - ctx context.Context
//   - prefixUsername string
func (_e *MockRepository_Expecter) CountPrefixUsername(ctx interface{}, prefixUsername interface{}) *MockRepository_CountPrefixUsername_Call {
	return &MockRepository_CountPrefixUsername_Call{Call: _e.mock.On("CountPrefixUsername", ctx, prefixUsername)}
}

func (_c *MockRepository_CountPrefixUsername_Call) Run(run func(ctx context.Context, prefixUsername string)) *MockRepository_CountPrefixUsername_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockRepository_CountPrefixUsername_Call) Return(_a0 int, _a1 error) *MockRepository_CountPrefixUsername_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_CountPrefixUsername_Call) RunAndReturn(run func(context.Context, string) (int, error)) *MockRepository_CountPrefixUsername_Call {
	_c.Call.Return(run)
	return _c
}

// CountRepoAccessToken provides a mock function with given fields: ctx, in
func (_m *MockRepository) CountRepoAccessToken(ctx context.Context, in repository.ListRepoAccessTokenQuery) (int64, error) {
	ret := _m.Called(ctx, in)

	if len(ret) == 0 {
		panic("no return value specified for CountRepoAccessToken")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.ListRepoAccessTokenQuery) (int64, error)); ok {
		return rf(ctx, in)
	}
	if rf, ok := ret.Get(0).(func(context.Context, repository.ListRepoAccessTokenQuery) int64); ok {
		r0 = rf(ctx, in)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, repository.ListRepoAccessTokenQuery) error); ok {
		r1 = rf(ctx, in)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_CountRepoAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CountRepoAccessToken'
type MockRepository_CountRepoAccessToken_Call struct {
	*mock.Call
}

// CountRepoAccessToken is a helper method to define mock.On call
//   - ctx context.Context
//   - in repository.ListRepoAccessTokenQuery
func (_e *MockRepository_Expecter) CountRepoAccessToken(ctx interface{}, in interface{}) *MockRepository_CountRepoAccessToken_Call {
	return &MockRepository_CountRepoAccessToken_Call{Call: _e.mock.On("CountRepoAccessToken", ctx, in)}
}

func (_c *MockRepository_CountRepoAccessToken_Call) Run(run func(ctx context.Context, in repository.ListRepoAccessTokenQuery)) *MockRepository_CountRepoAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.ListRepoAccessTokenQuery))
	})
	return _c
}

func (_c *MockRepository_CountRepoAccessToken_Call) Return(_a0 int64, _a1 error) *MockRepository_CountRepoAccessToken_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_CountRepoAccessToken_Call) RunAndReturn(run func(context.Context, repository.ListRepoAccessTokenQuery) (int64, error)) *MockRepository_CountRepoAccessToken_Call {
	_c.Call.Return(run)
	return _c
}

// CountRepoMembers provides a mock function with given fields: ctx, query
func (_m *MockRepository) CountRepoMembers(ctx context.Context, query repository.ListRepositoryMembersQuery) (int, error) {
	ret := _m.Called(ctx, query)

	if len(ret) == 0 {
		panic("no return value specified for CountRepoMembers")
	}

	var r0 int
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.ListRepositoryMembersQuery) (int, error)); ok {
		return rf(ctx, query)
	}
	if rf, ok := ret.Get(0).(func(context.Context, repository.ListRepositoryMembersQuery) int); ok {
		r0 = rf(ctx, query)
	} else {
		r0 = ret.Get(0).(int)
	}

	if rf, ok := ret.Get(1).(func(context.Context, repository.ListRepositoryMembersQuery) error); ok {
		r1 = rf(ctx, query)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_CountRepoMembers_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CountRepoMembers'
type MockRepository_CountRepoMembers_Call struct {
	*mock.Call
}

// CountRepoMembers is a helper method to define mock.On call
//   - ctx context.Context
//   - query repository.ListRepositoryMembersQuery
func (_e *MockRepository_Expecter) CountRepoMembers(ctx interface{}, query interface{}) *MockRepository_CountRepoMembers_Call {
	return &MockRepository_CountRepoMembers_Call{Call: _e.mock.On("CountRepoMembers", ctx, query)}
}

func (_c *MockRepository_CountRepoMembers_Call) Run(run func(ctx context.Context, query repository.ListRepositoryMembersQuery)) *MockRepository_CountRepoMembers_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.ListRepositoryMembersQuery))
	})
	return _c
}

func (_c *MockRepository_CountRepoMembers_Call) Return(_a0 int, _a1 error) *MockRepository_CountRepoMembers_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_CountRepoMembers_Call) RunAndReturn(run func(context.Context, repository.ListRepositoryMembersQuery) (int, error)) *MockRepository_CountRepoMembers_Call {
	_c.Call.Return(run)
	return _c
}

// CountRepositories provides a mock function with given fields: ctx, input
func (_m *MockRepository) CountRepositories(ctx context.Context, input dto.GetRepositoriesInput) (int64, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for CountRepositories")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.GetRepositoriesInput) (int64, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, dto.GetRepositoriesInput) int64); ok {
		r0 = rf(ctx, input)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, dto.GetRepositoriesInput) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_CountRepositories_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CountRepositories'
type MockRepository_CountRepositories_Call struct {
	*mock.Call
}

// CountRepositories is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.GetRepositoriesInput
func (_e *MockRepository_Expecter) CountRepositories(ctx interface{}, input interface{}) *MockRepository_CountRepositories_Call {
	return &MockRepository_CountRepositories_Call{Call: _e.mock.On("CountRepositories", ctx, input)}
}

func (_c *MockRepository_CountRepositories_Call) Run(run func(ctx context.Context, input dto.GetRepositoriesInput)) *MockRepository_CountRepositories_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.GetRepositoriesInput))
	})
	return _c
}

func (_c *MockRepository_CountRepositories_Call) Return(_a0 int64, _a1 error) *MockRepository_CountRepositories_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_CountRepositories_Call) RunAndReturn(run func(context.Context, dto.GetRepositoriesInput) (int64, error)) *MockRepository_CountRepositories_Call {
	_c.Call.Return(run)
	return _c
}

// CountUsers provides a mock function with given fields: ctx, input
func (_m *MockRepository) CountUsers(ctx context.Context, input repository.ListUsersInput) (int64, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for CountUsers")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.ListUsersInput) (int64, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, repository.ListUsersInput) int64); ok {
		r0 = rf(ctx, input)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, repository.ListUsersInput) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_CountUsers_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CountUsers'
type MockRepository_CountUsers_Call struct {
	*mock.Call
}

// CountUsers is a helper method to define mock.On call
//   - ctx context.Context
//   - input repository.ListUsersInput
func (_e *MockRepository_Expecter) CountUsers(ctx interface{}, input interface{}) *MockRepository_CountUsers_Call {
	return &MockRepository_CountUsers_Call{Call: _e.mock.On("CountUsers", ctx, input)}
}

func (_c *MockRepository_CountUsers_Call) Run(run func(ctx context.Context, input repository.ListUsersInput)) *MockRepository_CountUsers_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.ListUsersInput))
	})
	return _c
}

func (_c *MockRepository_CountUsers_Call) Return(_a0 int64, _a1 error) *MockRepository_CountUsers_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_CountUsers_Call) RunAndReturn(run func(context.Context, repository.ListUsersInput) (int64, error)) *MockRepository_CountUsers_Call {
	_c.Call.Return(run)
	return _c
}

// Create provides a mock function with given fields: ctx, _a1
func (_m *MockRepository) Create(ctx context.Context, _a1 interface{}) error {
	ret := _m.Called(ctx, _a1)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, interface{}) error); ok {
		r0 = rf(ctx, _a1)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepository_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type MockRepository_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - ctx context.Context
//   - _a1 interface{}
func (_e *MockRepository_Expecter) Create(ctx interface{}, _a1 interface{}) *MockRepository_Create_Call {
	return &MockRepository_Create_Call{Call: _e.mock.On("Create", ctx, _a1)}
}

func (_c *MockRepository_Create_Call) Run(run func(ctx context.Context, _a1 interface{})) *MockRepository_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(interface{}))
	})
	return _c
}

func (_c *MockRepository_Create_Call) Return(_a0 error) *MockRepository_Create_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepository_Create_Call) RunAndReturn(run func(context.Context, interface{}) error) *MockRepository_Create_Call {
	_c.Call.Return(run)
	return _c
}

// CreateAccessToken provides a mock function with given fields: ctx, in
func (_m *MockRepository) CreateAccessToken(ctx context.Context, in repository.CreateAccessTokenInput) (*entities.UserToken, error) {
	ret := _m.Called(ctx, in)

	if len(ret) == 0 {
		panic("no return value specified for CreateAccessToken")
	}

	var r0 *entities.UserToken
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.CreateAccessTokenInput) (*entities.UserToken, error)); ok {
		return rf(ctx, in)
	}
	if rf, ok := ret.Get(0).(func(context.Context, repository.CreateAccessTokenInput) *entities.UserToken); ok {
		r0 = rf(ctx, in)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.UserToken)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, repository.CreateAccessTokenInput) error); ok {
		r1 = rf(ctx, in)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_CreateAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateAccessToken'
type MockRepository_CreateAccessToken_Call struct {
	*mock.Call
}

// CreateAccessToken is a helper method to define mock.On call
//   - ctx context.Context
//   - in repository.CreateAccessTokenInput
func (_e *MockRepository_Expecter) CreateAccessToken(ctx interface{}, in interface{}) *MockRepository_CreateAccessToken_Call {
	return &MockRepository_CreateAccessToken_Call{Call: _e.mock.On("CreateAccessToken", ctx, in)}
}

func (_c *MockRepository_CreateAccessToken_Call) Run(run func(ctx context.Context, in repository.CreateAccessTokenInput)) *MockRepository_CreateAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.CreateAccessTokenInput))
	})
	return _c
}

func (_c *MockRepository_CreateAccessToken_Call) Return(_a0 *entities.UserToken, _a1 error) *MockRepository_CreateAccessToken_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_CreateAccessToken_Call) RunAndReturn(run func(context.Context, repository.CreateAccessTokenInput) (*entities.UserToken, error)) *MockRepository_CreateAccessToken_Call {
	_c.Call.Return(run)
	return _c
}

// CreateDeployment provides a mock function with given fields: ctx, in
func (_m *MockRepository) CreateDeployment(ctx context.Context, in repository.CreateDeploymentInput) (*entities.Deployment, error) {
	ret := _m.Called(ctx, in)

	if len(ret) == 0 {
		panic("no return value specified for CreateDeployment")
	}

	var r0 *entities.Deployment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.CreateDeploymentInput) (*entities.Deployment, error)); ok {
		return rf(ctx, in)
	}
	if rf, ok := ret.Get(0).(func(context.Context, repository.CreateDeploymentInput) *entities.Deployment); ok {
		r0 = rf(ctx, in)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.Deployment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, repository.CreateDeploymentInput) error); ok {
		r1 = rf(ctx, in)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_CreateDeployment_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateDeployment'
type MockRepository_CreateDeployment_Call struct {
	*mock.Call
}

// CreateDeployment is a helper method to define mock.On call
//   - ctx context.Context
//   - in repository.CreateDeploymentInput
func (_e *MockRepository_Expecter) CreateDeployment(ctx interface{}, in interface{}) *MockRepository_CreateDeployment_Call {
	return &MockRepository_CreateDeployment_Call{Call: _e.mock.On("CreateDeployment", ctx, in)}
}

func (_c *MockRepository_CreateDeployment_Call) Run(run func(ctx context.Context, in repository.CreateDeploymentInput)) *MockRepository_CreateDeployment_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.CreateDeploymentInput))
	})
	return _c
}

func (_c *MockRepository_CreateDeployment_Call) Return(_a0 *entities.Deployment, _a1 error) *MockRepository_CreateDeployment_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_CreateDeployment_Call) RunAndReturn(run func(context.Context, repository.CreateDeploymentInput) (*entities.Deployment, error)) *MockRepository_CreateDeployment_Call {
	_c.Call.Return(run)
	return _c
}

// CreateECRDeployment provides a mock function with given fields: ctx, input
func (_m *MockRepository) CreateECRDeployment(ctx context.Context, input repository.CreateCustomImageDeploymentInput) (*entities.CustomImageDeployment, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for CreateECRDeployment")
	}

	var r0 *entities.CustomImageDeployment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.CreateCustomImageDeploymentInput) (*entities.CustomImageDeployment, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, repository.CreateCustomImageDeploymentInput) *entities.CustomImageDeployment); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.CustomImageDeployment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, repository.CreateCustomImageDeploymentInput) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_CreateECRDeployment_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateECRDeployment'
type MockRepository_CreateECRDeployment_Call struct {
	*mock.Call
}

// CreateECRDeployment is a helper method to define mock.On call
//   - ctx context.Context
//   - input repository.CreateCustomImageDeploymentInput
func (_e *MockRepository_Expecter) CreateECRDeployment(ctx interface{}, input interface{}) *MockRepository_CreateECRDeployment_Call {
	return &MockRepository_CreateECRDeployment_Call{Call: _e.mock.On("CreateECRDeployment", ctx, input)}
}

func (_c *MockRepository_CreateECRDeployment_Call) Run(run func(ctx context.Context, input repository.CreateCustomImageDeploymentInput)) *MockRepository_CreateECRDeployment_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.CreateCustomImageDeploymentInput))
	})
	return _c
}

func (_c *MockRepository_CreateECRDeployment_Call) Return(_a0 *entities.CustomImageDeployment, _a1 error) *MockRepository_CreateECRDeployment_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_CreateECRDeployment_Call) RunAndReturn(run func(context.Context, repository.CreateCustomImageDeploymentInput) (*entities.CustomImageDeployment, error)) *MockRepository_CreateECRDeployment_Call {
	_c.Call.Return(run)
	return _c
}

// CreateECRDeploymentEnv provides a mock function with given fields: ctx, in
func (_m *MockRepository) CreateECRDeploymentEnv(ctx context.Context, in repository.CreateECRDeploymentEnvInput) error {
	ret := _m.Called(ctx, in)

	if len(ret) == 0 {
		panic("no return value specified for CreateECRDeploymentEnv")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.CreateECRDeploymentEnvInput) error); ok {
		r0 = rf(ctx, in)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepository_CreateECRDeploymentEnv_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateECRDeploymentEnv'
type MockRepository_CreateECRDeploymentEnv_Call struct {
	*mock.Call
}

// CreateECRDeploymentEnv is a helper method to define mock.On call
//   - ctx context.Context
//   - in repository.CreateECRDeploymentEnvInput
func (_e *MockRepository_Expecter) CreateECRDeploymentEnv(ctx interface{}, in interface{}) *MockRepository_CreateECRDeploymentEnv_Call {
	return &MockRepository_CreateECRDeploymentEnv_Call{Call: _e.mock.On("CreateECRDeploymentEnv", ctx, in)}
}

func (_c *MockRepository_CreateECRDeploymentEnv_Call) Run(run func(ctx context.Context, in repository.CreateECRDeploymentEnvInput)) *MockRepository_CreateECRDeploymentEnv_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.CreateECRDeploymentEnvInput))
	})
	return _c
}

func (_c *MockRepository_CreateECRDeploymentEnv_Call) Return(_a0 error) *MockRepository_CreateECRDeploymentEnv_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepository_CreateECRDeploymentEnv_Call) RunAndReturn(run func(context.Context, repository.CreateECRDeploymentEnvInput) error) *MockRepository_CreateECRDeploymentEnv_Call {
	_c.Call.Return(run)
	return _c
}

// CreateEnv provides a mock function with given fields: ctx, in
func (_m *MockRepository) CreateEnv(ctx context.Context, in repository.CreateEnvInput) error {
	ret := _m.Called(ctx, in)

	if len(ret) == 0 {
		panic("no return value specified for CreateEnv")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.CreateEnvInput) error); ok {
		r0 = rf(ctx, in)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepository_CreateEnv_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateEnv'
type MockRepository_CreateEnv_Call struct {
	*mock.Call
}

// CreateEnv is a helper method to define mock.On call
//   - ctx context.Context
//   - in repository.CreateEnvInput
func (_e *MockRepository_Expecter) CreateEnv(ctx interface{}, in interface{}) *MockRepository_CreateEnv_Call {
	return &MockRepository_CreateEnv_Call{Call: _e.mock.On("CreateEnv", ctx, in)}
}

func (_c *MockRepository_CreateEnv_Call) Run(run func(ctx context.Context, in repository.CreateEnvInput)) *MockRepository_CreateEnv_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.CreateEnvInput))
	})
	return _c
}

func (_c *MockRepository_CreateEnv_Call) Return(_a0 error) *MockRepository_CreateEnv_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepository_CreateEnv_Call) RunAndReturn(run func(context.Context, repository.CreateEnvInput) error) *MockRepository_CreateEnv_Call {
	_c.Call.Return(run)
	return _c
}

// CreateHardware provides a mock function with given fields: ctx, hardware
func (_m *MockRepository) CreateHardware(ctx context.Context, hardware *entities.Hardware) (*entities.Hardware, error) {
	ret := _m.Called(ctx, hardware)

	if len(ret) == 0 {
		panic("no return value specified for CreateHardware")
	}

	var r0 *entities.Hardware
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *entities.Hardware) (*entities.Hardware, error)); ok {
		return rf(ctx, hardware)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *entities.Hardware) *entities.Hardware); ok {
		r0 = rf(ctx, hardware)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.Hardware)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *entities.Hardware) error); ok {
		r1 = rf(ctx, hardware)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_CreateHardware_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateHardware'
type MockRepository_CreateHardware_Call struct {
	*mock.Call
}

// CreateHardware is a helper method to define mock.On call
//   - ctx context.Context
//   - hardware *entities.Hardware
func (_e *MockRepository_Expecter) CreateHardware(ctx interface{}, hardware interface{}) *MockRepository_CreateHardware_Call {
	return &MockRepository_CreateHardware_Call{Call: _e.mock.On("CreateHardware", ctx, hardware)}
}

func (_c *MockRepository_CreateHardware_Call) Run(run func(ctx context.Context, hardware *entities.Hardware)) *MockRepository_CreateHardware_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*entities.Hardware))
	})
	return _c
}

func (_c *MockRepository_CreateHardware_Call) Return(_a0 *entities.Hardware, _a1 error) *MockRepository_CreateHardware_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_CreateHardware_Call) RunAndReturn(run func(context.Context, *entities.Hardware) (*entities.Hardware, error)) *MockRepository_CreateHardware_Call {
	_c.Call.Return(run)
	return _c
}

// CreateOrgAccessToken provides a mock function with given fields: ctx, in
func (_m *MockRepository) CreateOrgAccessToken(ctx context.Context, in repository.CreateOrgAccessTokenInput) (*entities.OrgToken, error) {
	ret := _m.Called(ctx, in)

	if len(ret) == 0 {
		panic("no return value specified for CreateOrgAccessToken")
	}

	var r0 *entities.OrgToken
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.CreateOrgAccessTokenInput) (*entities.OrgToken, error)); ok {
		return rf(ctx, in)
	}
	if rf, ok := ret.Get(0).(func(context.Context, repository.CreateOrgAccessTokenInput) *entities.OrgToken); ok {
		r0 = rf(ctx, in)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.OrgToken)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, repository.CreateOrgAccessTokenInput) error); ok {
		r1 = rf(ctx, in)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_CreateOrgAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateOrgAccessToken'
type MockRepository_CreateOrgAccessToken_Call struct {
	*mock.Call
}

// CreateOrgAccessToken is a helper method to define mock.On call
//   - ctx context.Context
//   - in repository.CreateOrgAccessTokenInput
func (_e *MockRepository_Expecter) CreateOrgAccessToken(ctx interface{}, in interface{}) *MockRepository_CreateOrgAccessToken_Call {
	return &MockRepository_CreateOrgAccessToken_Call{Call: _e.mock.On("CreateOrgAccessToken", ctx, in)}
}

func (_c *MockRepository_CreateOrgAccessToken_Call) Run(run func(ctx context.Context, in repository.CreateOrgAccessTokenInput)) *MockRepository_CreateOrgAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.CreateOrgAccessTokenInput))
	})
	return _c
}

func (_c *MockRepository_CreateOrgAccessToken_Call) Return(_a0 *entities.OrgToken, _a1 error) *MockRepository_CreateOrgAccessToken_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_CreateOrgAccessToken_Call) RunAndReturn(run func(context.Context, repository.CreateOrgAccessTokenInput) (*entities.OrgToken, error)) *MockRepository_CreateOrgAccessToken_Call {
	_c.Call.Return(run)
	return _c
}

// CreateOrgGitGroup provides a mock function with given fields: ctx, in
func (_m *MockRepository) CreateOrgGitGroup(ctx context.Context, in repository.CreateOrgGitGroupInput) (*entities.OrgGitGroup, error) {
	ret := _m.Called(ctx, in)

	if len(ret) == 0 {
		panic("no return value specified for CreateOrgGitGroup")
	}

	var r0 *entities.OrgGitGroup
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.CreateOrgGitGroupInput) (*entities.OrgGitGroup, error)); ok {
		return rf(ctx, in)
	}
	if rf, ok := ret.Get(0).(func(context.Context, repository.CreateOrgGitGroupInput) *entities.OrgGitGroup); ok {
		r0 = rf(ctx, in)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.OrgGitGroup)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, repository.CreateOrgGitGroupInput) error); ok {
		r1 = rf(ctx, in)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_CreateOrgGitGroup_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateOrgGitGroup'
type MockRepository_CreateOrgGitGroup_Call struct {
	*mock.Call
}

// CreateOrgGitGroup is a helper method to define mock.On call
//   - ctx context.Context
//   - in repository.CreateOrgGitGroupInput
func (_e *MockRepository_Expecter) CreateOrgGitGroup(ctx interface{}, in interface{}) *MockRepository_CreateOrgGitGroup_Call {
	return &MockRepository_CreateOrgGitGroup_Call{Call: _e.mock.On("CreateOrgGitGroup", ctx, in)}
}

func (_c *MockRepository_CreateOrgGitGroup_Call) Run(run func(ctx context.Context, in repository.CreateOrgGitGroupInput)) *MockRepository_CreateOrgGitGroup_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.CreateOrgGitGroupInput))
	})
	return _c
}

func (_c *MockRepository_CreateOrgGitGroup_Call) Return(_a0 *entities.OrgGitGroup, _a1 error) *MockRepository_CreateOrgGitGroup_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_CreateOrgGitGroup_Call) RunAndReturn(run func(context.Context, repository.CreateOrgGitGroupInput) (*entities.OrgGitGroup, error)) *MockRepository_CreateOrgGitGroup_Call {
	_c.Call.Return(run)
	return _c
}

// CreateRepoAccessToken provides a mock function with given fields: ctx, in
func (_m *MockRepository) CreateRepoAccessToken(ctx context.Context, in repository.CreateRepoAccessTokenInput) (*entities.RepoAccessToken, error) {
	ret := _m.Called(ctx, in)

	if len(ret) == 0 {
		panic("no return value specified for CreateRepoAccessToken")
	}

	var r0 *entities.RepoAccessToken
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.CreateRepoAccessTokenInput) (*entities.RepoAccessToken, error)); ok {
		return rf(ctx, in)
	}
	if rf, ok := ret.Get(0).(func(context.Context, repository.CreateRepoAccessTokenInput) *entities.RepoAccessToken); ok {
		r0 = rf(ctx, in)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.RepoAccessToken)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, repository.CreateRepoAccessTokenInput) error); ok {
		r1 = rf(ctx, in)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_CreateRepoAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateRepoAccessToken'
type MockRepository_CreateRepoAccessToken_Call struct {
	*mock.Call
}

// CreateRepoAccessToken is a helper method to define mock.On call
//   - ctx context.Context
//   - in repository.CreateRepoAccessTokenInput
func (_e *MockRepository_Expecter) CreateRepoAccessToken(ctx interface{}, in interface{}) *MockRepository_CreateRepoAccessToken_Call {
	return &MockRepository_CreateRepoAccessToken_Call{Call: _e.mock.On("CreateRepoAccessToken", ctx, in)}
}

func (_c *MockRepository_CreateRepoAccessToken_Call) Run(run func(ctx context.Context, in repository.CreateRepoAccessTokenInput)) *MockRepository_CreateRepoAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.CreateRepoAccessTokenInput))
	})
	return _c
}

func (_c *MockRepository_CreateRepoAccessToken_Call) Return(_a0 *entities.RepoAccessToken, _a1 error) *MockRepository_CreateRepoAccessToken_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_CreateRepoAccessToken_Call) RunAndReturn(run func(context.Context, repository.CreateRepoAccessTokenInput) (*entities.RepoAccessToken, error)) *MockRepository_CreateRepoAccessToken_Call {
	_c.Call.Return(run)
	return _c
}

// CreateRepositoryMember provides a mock function with given fields: ctx, input
func (_m *MockRepository) CreateRepositoryMember(ctx context.Context, input *entities.RepoMember) error {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for CreateRepositoryMember")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *entities.RepoMember) error); ok {
		r0 = rf(ctx, input)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepository_CreateRepositoryMember_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateRepositoryMember'
type MockRepository_CreateRepositoryMember_Call struct {
	*mock.Call
}

// CreateRepositoryMember is a helper method to define mock.On call
//   - ctx context.Context
//   - input *entities.RepoMember
func (_e *MockRepository_Expecter) CreateRepositoryMember(ctx interface{}, input interface{}) *MockRepository_CreateRepositoryMember_Call {
	return &MockRepository_CreateRepositoryMember_Call{Call: _e.mock.On("CreateRepositoryMember", ctx, input)}
}

func (_c *MockRepository_CreateRepositoryMember_Call) Run(run func(ctx context.Context, input *entities.RepoMember)) *MockRepository_CreateRepositoryMember_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*entities.RepoMember))
	})
	return _c
}

func (_c *MockRepository_CreateRepositoryMember_Call) Return(_a0 error) *MockRepository_CreateRepositoryMember_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepository_CreateRepositoryMember_Call) RunAndReturn(run func(context.Context, *entities.RepoMember) error) *MockRepository_CreateRepositoryMember_Call {
	_c.Call.Return(run)
	return _c
}

// CreateUserGitGroup provides a mock function with given fields: ctx, in
func (_m *MockRepository) CreateUserGitGroup(ctx context.Context, in repository.CreateUserGitGroupInput) (*entities.UserGitGroup, error) {
	ret := _m.Called(ctx, in)

	if len(ret) == 0 {
		panic("no return value specified for CreateUserGitGroup")
	}

	var r0 *entities.UserGitGroup
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.CreateUserGitGroupInput) (*entities.UserGitGroup, error)); ok {
		return rf(ctx, in)
	}
	if rf, ok := ret.Get(0).(func(context.Context, repository.CreateUserGitGroupInput) *entities.UserGitGroup); ok {
		r0 = rf(ctx, in)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.UserGitGroup)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, repository.CreateUserGitGroupInput) error); ok {
		r1 = rf(ctx, in)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_CreateUserGitGroup_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateUserGitGroup'
type MockRepository_CreateUserGitGroup_Call struct {
	*mock.Call
}

// CreateUserGitGroup is a helper method to define mock.On call
//   - ctx context.Context
//   - in repository.CreateUserGitGroupInput
func (_e *MockRepository_Expecter) CreateUserGitGroup(ctx interface{}, in interface{}) *MockRepository_CreateUserGitGroup_Call {
	return &MockRepository_CreateUserGitGroup_Call{Call: _e.mock.On("CreateUserGitGroup", ctx, in)}
}

func (_c *MockRepository_CreateUserGitGroup_Call) Run(run func(ctx context.Context, in repository.CreateUserGitGroupInput)) *MockRepository_CreateUserGitGroup_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.CreateUserGitGroupInput))
	})
	return _c
}

func (_c *MockRepository_CreateUserGitGroup_Call) Return(_a0 *entities.UserGitGroup, _a1 error) *MockRepository_CreateUserGitGroup_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_CreateUserGitGroup_Call) RunAndReturn(run func(context.Context, repository.CreateUserGitGroupInput) (*entities.UserGitGroup, error)) *MockRepository_CreateUserGitGroup_Call {
	_c.Call.Return(run)
	return _c
}

// Delete provides a mock function with given fields: ctx, _a1
func (_m *MockRepository) Delete(ctx context.Context, _a1 interface{}) error {
	ret := _m.Called(ctx, _a1)

	if len(ret) == 0 {
		panic("no return value specified for Delete")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, interface{}) error); ok {
		r0 = rf(ctx, _a1)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepository_Delete_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Delete'
type MockRepository_Delete_Call struct {
	*mock.Call
}

// Delete is a helper method to define mock.On call
//   - ctx context.Context
//   - _a1 interface{}
func (_e *MockRepository_Expecter) Delete(ctx interface{}, _a1 interface{}) *MockRepository_Delete_Call {
	return &MockRepository_Delete_Call{Call: _e.mock.On("Delete", ctx, _a1)}
}

func (_c *MockRepository_Delete_Call) Run(run func(ctx context.Context, _a1 interface{})) *MockRepository_Delete_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(interface{}))
	})
	return _c
}

func (_c *MockRepository_Delete_Call) Return(_a0 error) *MockRepository_Delete_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepository_Delete_Call) RunAndReturn(run func(context.Context, interface{}) error) *MockRepository_Delete_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteAccessToken provides a mock function with given fields: ctx, in
func (_m *MockRepository) DeleteAccessToken(ctx context.Context, in repository.DeleteAccessTokenInput) error {
	ret := _m.Called(ctx, in)

	if len(ret) == 0 {
		panic("no return value specified for DeleteAccessToken")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.DeleteAccessTokenInput) error); ok {
		r0 = rf(ctx, in)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepository_DeleteAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteAccessToken'
type MockRepository_DeleteAccessToken_Call struct {
	*mock.Call
}

// DeleteAccessToken is a helper method to define mock.On call
//   - ctx context.Context
//   - in repository.DeleteAccessTokenInput
func (_e *MockRepository_Expecter) DeleteAccessToken(ctx interface{}, in interface{}) *MockRepository_DeleteAccessToken_Call {
	return &MockRepository_DeleteAccessToken_Call{Call: _e.mock.On("DeleteAccessToken", ctx, in)}
}

func (_c *MockRepository_DeleteAccessToken_Call) Run(run func(ctx context.Context, in repository.DeleteAccessTokenInput)) *MockRepository_DeleteAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.DeleteAccessTokenInput))
	})
	return _c
}

func (_c *MockRepository_DeleteAccessToken_Call) Return(_a0 error) *MockRepository_DeleteAccessToken_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepository_DeleteAccessToken_Call) RunAndReturn(run func(context.Context, repository.DeleteAccessTokenInput) error) *MockRepository_DeleteAccessToken_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteAllUserAccessToken provides a mock function with given fields: ctx, userId
func (_m *MockRepository) DeleteAllUserAccessToken(ctx context.Context, userId uuid.UUID) error {
	ret := _m.Called(ctx, userId)

	if len(ret) == 0 {
		panic("no return value specified for DeleteAllUserAccessToken")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) error); ok {
		r0 = rf(ctx, userId)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepository_DeleteAllUserAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteAllUserAccessToken'
type MockRepository_DeleteAllUserAccessToken_Call struct {
	*mock.Call
}

// DeleteAllUserAccessToken is a helper method to define mock.On call
//   - ctx context.Context
//   - userId uuid.UUID
func (_e *MockRepository_Expecter) DeleteAllUserAccessToken(ctx interface{}, userId interface{}) *MockRepository_DeleteAllUserAccessToken_Call {
	return &MockRepository_DeleteAllUserAccessToken_Call{Call: _e.mock.On("DeleteAllUserAccessToken", ctx, userId)}
}

func (_c *MockRepository_DeleteAllUserAccessToken_Call) Run(run func(ctx context.Context, userId uuid.UUID)) *MockRepository_DeleteAllUserAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *MockRepository_DeleteAllUserAccessToken_Call) Return(_a0 error) *MockRepository_DeleteAllUserAccessToken_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepository_DeleteAllUserAccessToken_Call) RunAndReturn(run func(context.Context, uuid.UUID) error) *MockRepository_DeleteAllUserAccessToken_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteAllUserOrgAccessToken provides a mock function with given fields: ctx, orgId
func (_m *MockRepository) DeleteAllUserOrgAccessToken(ctx context.Context, orgId uuid.UUID) error {
	ret := _m.Called(ctx, orgId)

	if len(ret) == 0 {
		panic("no return value specified for DeleteAllUserOrgAccessToken")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) error); ok {
		r0 = rf(ctx, orgId)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepository_DeleteAllUserOrgAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteAllUserOrgAccessToken'
type MockRepository_DeleteAllUserOrgAccessToken_Call struct {
	*mock.Call
}

// DeleteAllUserOrgAccessToken is a helper method to define mock.On call
//   - ctx context.Context
//   - orgId uuid.UUID
func (_e *MockRepository_Expecter) DeleteAllUserOrgAccessToken(ctx interface{}, orgId interface{}) *MockRepository_DeleteAllUserOrgAccessToken_Call {
	return &MockRepository_DeleteAllUserOrgAccessToken_Call{Call: _e.mock.On("DeleteAllUserOrgAccessToken", ctx, orgId)}
}

func (_c *MockRepository_DeleteAllUserOrgAccessToken_Call) Run(run func(ctx context.Context, orgId uuid.UUID)) *MockRepository_DeleteAllUserOrgAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *MockRepository_DeleteAllUserOrgAccessToken_Call) Return(_a0 error) *MockRepository_DeleteAllUserOrgAccessToken_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepository_DeleteAllUserOrgAccessToken_Call) RunAndReturn(run func(context.Context, uuid.UUID) error) *MockRepository_DeleteAllUserOrgAccessToken_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteById provides a mock function with given fields: ctx, model, id
func (_m *MockRepository) DeleteById(ctx context.Context, model interface{}, id uuid.UUID) error {
	ret := _m.Called(ctx, model, id)

	if len(ret) == 0 {
		panic("no return value specified for DeleteById")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, interface{}, uuid.UUID) error); ok {
		r0 = rf(ctx, model, id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepository_DeleteById_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteById'
type MockRepository_DeleteById_Call struct {
	*mock.Call
}

// DeleteById is a helper method to define mock.On call
//   - ctx context.Context
//   - model interface{}
//   - id uuid.UUID
func (_e *MockRepository_Expecter) DeleteById(ctx interface{}, model interface{}, id interface{}) *MockRepository_DeleteById_Call {
	return &MockRepository_DeleteById_Call{Call: _e.mock.On("DeleteById", ctx, model, id)}
}

func (_c *MockRepository_DeleteById_Call) Run(run func(ctx context.Context, model interface{}, id uuid.UUID)) *MockRepository_DeleteById_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(interface{}), args[2].(uuid.UUID))
	})
	return _c
}

func (_c *MockRepository_DeleteById_Call) Return(_a0 error) *MockRepository_DeleteById_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepository_DeleteById_Call) RunAndReturn(run func(context.Context, interface{}, uuid.UUID) error) *MockRepository_DeleteById_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteDeploymentsByRepoID provides a mock function with given fields: ctx, repoID
func (_m *MockRepository) DeleteDeploymentsByRepoID(ctx context.Context, repoID uuid.UUID) error {
	ret := _m.Called(ctx, repoID)

	if len(ret) == 0 {
		panic("no return value specified for DeleteDeploymentsByRepoID")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) error); ok {
		r0 = rf(ctx, repoID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepository_DeleteDeploymentsByRepoID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteDeploymentsByRepoID'
type MockRepository_DeleteDeploymentsByRepoID_Call struct {
	*mock.Call
}

// DeleteDeploymentsByRepoID is a helper method to define mock.On call
//   - ctx context.Context
//   - repoID uuid.UUID
func (_e *MockRepository_Expecter) DeleteDeploymentsByRepoID(ctx interface{}, repoID interface{}) *MockRepository_DeleteDeploymentsByRepoID_Call {
	return &MockRepository_DeleteDeploymentsByRepoID_Call{Call: _e.mock.On("DeleteDeploymentsByRepoID", ctx, repoID)}
}

func (_c *MockRepository_DeleteDeploymentsByRepoID_Call) Run(run func(ctx context.Context, repoID uuid.UUID)) *MockRepository_DeleteDeploymentsByRepoID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *MockRepository_DeleteDeploymentsByRepoID_Call) Return(_a0 error) *MockRepository_DeleteDeploymentsByRepoID_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepository_DeleteDeploymentsByRepoID_Call) RunAndReturn(run func(context.Context, uuid.UUID) error) *MockRepository_DeleteDeploymentsByRepoID_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteECRDeployment provides a mock function with given fields: ctx, id
func (_m *MockRepository) DeleteECRDeployment(ctx context.Context, id uuid.UUID) error {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for DeleteECRDeployment")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) error); ok {
		r0 = rf(ctx, id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepository_DeleteECRDeployment_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteECRDeployment'
type MockRepository_DeleteECRDeployment_Call struct {
	*mock.Call
}

// DeleteECRDeployment is a helper method to define mock.On call
//   - ctx context.Context
//   - id uuid.UUID
func (_e *MockRepository_Expecter) DeleteECRDeployment(ctx interface{}, id interface{}) *MockRepository_DeleteECRDeployment_Call {
	return &MockRepository_DeleteECRDeployment_Call{Call: _e.mock.On("DeleteECRDeployment", ctx, id)}
}

func (_c *MockRepository_DeleteECRDeployment_Call) Run(run func(ctx context.Context, id uuid.UUID)) *MockRepository_DeleteECRDeployment_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *MockRepository_DeleteECRDeployment_Call) Return(_a0 error) *MockRepository_DeleteECRDeployment_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepository_DeleteECRDeployment_Call) RunAndReturn(run func(context.Context, uuid.UUID) error) *MockRepository_DeleteECRDeployment_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteECRDeploymentEnv provides a mock function with given fields: ctx, in
func (_m *MockRepository) DeleteECRDeploymentEnv(ctx context.Context, in repository.DeleteECRDeploymentEnv) error {
	ret := _m.Called(ctx, in)

	if len(ret) == 0 {
		panic("no return value specified for DeleteECRDeploymentEnv")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.DeleteECRDeploymentEnv) error); ok {
		r0 = rf(ctx, in)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepository_DeleteECRDeploymentEnv_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteECRDeploymentEnv'
type MockRepository_DeleteECRDeploymentEnv_Call struct {
	*mock.Call
}

// DeleteECRDeploymentEnv is a helper method to define mock.On call
//   - ctx context.Context
//   - in repository.DeleteECRDeploymentEnv
func (_e *MockRepository_Expecter) DeleteECRDeploymentEnv(ctx interface{}, in interface{}) *MockRepository_DeleteECRDeploymentEnv_Call {
	return &MockRepository_DeleteECRDeploymentEnv_Call{Call: _e.mock.On("DeleteECRDeploymentEnv", ctx, in)}
}

func (_c *MockRepository_DeleteECRDeploymentEnv_Call) Run(run func(ctx context.Context, in repository.DeleteECRDeploymentEnv)) *MockRepository_DeleteECRDeploymentEnv_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.DeleteECRDeploymentEnv))
	})
	return _c
}

func (_c *MockRepository_DeleteECRDeploymentEnv_Call) Return(_a0 error) *MockRepository_DeleteECRDeploymentEnv_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepository_DeleteECRDeploymentEnv_Call) RunAndReturn(run func(context.Context, repository.DeleteECRDeploymentEnv) error) *MockRepository_DeleteECRDeploymentEnv_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteEnv provides a mock function with given fields: ctx, in
func (_m *MockRepository) DeleteEnv(ctx context.Context, in repository.CreateEnvInput) error {
	ret := _m.Called(ctx, in)

	if len(ret) == 0 {
		panic("no return value specified for DeleteEnv")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.CreateEnvInput) error); ok {
		r0 = rf(ctx, in)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepository_DeleteEnv_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteEnv'
type MockRepository_DeleteEnv_Call struct {
	*mock.Call
}

// DeleteEnv is a helper method to define mock.On call
//   - ctx context.Context
//   - in repository.CreateEnvInput
func (_e *MockRepository_Expecter) DeleteEnv(ctx interface{}, in interface{}) *MockRepository_DeleteEnv_Call {
	return &MockRepository_DeleteEnv_Call{Call: _e.mock.On("DeleteEnv", ctx, in)}
}

func (_c *MockRepository_DeleteEnv_Call) Run(run func(ctx context.Context, in repository.CreateEnvInput)) *MockRepository_DeleteEnv_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.CreateEnvInput))
	})
	return _c
}

func (_c *MockRepository_DeleteEnv_Call) Return(_a0 error) *MockRepository_DeleteEnv_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepository_DeleteEnv_Call) RunAndReturn(run func(context.Context, repository.CreateEnvInput) error) *MockRepository_DeleteEnv_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteMembersByOrganizationID provides a mock function with given fields: ctx, organizationID
func (_m *MockRepository) DeleteMembersByOrganizationID(ctx context.Context, organizationID uuid.UUID) error {
	ret := _m.Called(ctx, organizationID)

	if len(ret) == 0 {
		panic("no return value specified for DeleteMembersByOrganizationID")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) error); ok {
		r0 = rf(ctx, organizationID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepository_DeleteMembersByOrganizationID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteMembersByOrganizationID'
type MockRepository_DeleteMembersByOrganizationID_Call struct {
	*mock.Call
}

// DeleteMembersByOrganizationID is a helper method to define mock.On call
//   - ctx context.Context
//   - organizationID uuid.UUID
func (_e *MockRepository_Expecter) DeleteMembersByOrganizationID(ctx interface{}, organizationID interface{}) *MockRepository_DeleteMembersByOrganizationID_Call {
	return &MockRepository_DeleteMembersByOrganizationID_Call{Call: _e.mock.On("DeleteMembersByOrganizationID", ctx, organizationID)}
}

func (_c *MockRepository_DeleteMembersByOrganizationID_Call) Run(run func(ctx context.Context, organizationID uuid.UUID)) *MockRepository_DeleteMembersByOrganizationID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *MockRepository_DeleteMembersByOrganizationID_Call) Return(_a0 error) *MockRepository_DeleteMembersByOrganizationID_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepository_DeleteMembersByOrganizationID_Call) RunAndReturn(run func(context.Context, uuid.UUID) error) *MockRepository_DeleteMembersByOrganizationID_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteOrgAccessToken provides a mock function with given fields: ctx, in
func (_m *MockRepository) DeleteOrgAccessToken(ctx context.Context, in repository.DeleteOrgAccessTokenInput) error {
	ret := _m.Called(ctx, in)

	if len(ret) == 0 {
		panic("no return value specified for DeleteOrgAccessToken")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.DeleteOrgAccessTokenInput) error); ok {
		r0 = rf(ctx, in)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepository_DeleteOrgAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteOrgAccessToken'
type MockRepository_DeleteOrgAccessToken_Call struct {
	*mock.Call
}

// DeleteOrgAccessToken is a helper method to define mock.On call
//   - ctx context.Context
//   - in repository.DeleteOrgAccessTokenInput
func (_e *MockRepository_Expecter) DeleteOrgAccessToken(ctx interface{}, in interface{}) *MockRepository_DeleteOrgAccessToken_Call {
	return &MockRepository_DeleteOrgAccessToken_Call{Call: _e.mock.On("DeleteOrgAccessToken", ctx, in)}
}

func (_c *MockRepository_DeleteOrgAccessToken_Call) Run(run func(ctx context.Context, in repository.DeleteOrgAccessTokenInput)) *MockRepository_DeleteOrgAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.DeleteOrgAccessTokenInput))
	})
	return _c
}

func (_c *MockRepository_DeleteOrgAccessToken_Call) Return(_a0 error) *MockRepository_DeleteOrgAccessToken_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepository_DeleteOrgAccessToken_Call) RunAndReturn(run func(context.Context, repository.DeleteOrgAccessTokenInput) error) *MockRepository_DeleteOrgAccessToken_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteOrgGitGroup provides a mock function with given fields: ctx, orgID
func (_m *MockRepository) DeleteOrgGitGroup(ctx context.Context, orgID uuid.UUID) error {
	ret := _m.Called(ctx, orgID)

	if len(ret) == 0 {
		panic("no return value specified for DeleteOrgGitGroup")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) error); ok {
		r0 = rf(ctx, orgID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepository_DeleteOrgGitGroup_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteOrgGitGroup'
type MockRepository_DeleteOrgGitGroup_Call struct {
	*mock.Call
}

// DeleteOrgGitGroup is a helper method to define mock.On call
//   - ctx context.Context
//   - orgID uuid.UUID
func (_e *MockRepository_Expecter) DeleteOrgGitGroup(ctx interface{}, orgID interface{}) *MockRepository_DeleteOrgGitGroup_Call {
	return &MockRepository_DeleteOrgGitGroup_Call{Call: _e.mock.On("DeleteOrgGitGroup", ctx, orgID)}
}

func (_c *MockRepository_DeleteOrgGitGroup_Call) Run(run func(ctx context.Context, orgID uuid.UUID)) *MockRepository_DeleteOrgGitGroup_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *MockRepository_DeleteOrgGitGroup_Call) Return(_a0 error) *MockRepository_DeleteOrgGitGroup_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepository_DeleteOrgGitGroup_Call) RunAndReturn(run func(context.Context, uuid.UUID) error) *MockRepository_DeleteOrgGitGroup_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteOrgMemberByID provides a mock function with given fields: ctx, id
func (_m *MockRepository) DeleteOrgMemberByID(ctx context.Context, id uuid.UUID) error {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for DeleteOrgMemberByID")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) error); ok {
		r0 = rf(ctx, id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepository_DeleteOrgMemberByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteOrgMemberByID'
type MockRepository_DeleteOrgMemberByID_Call struct {
	*mock.Call
}

// DeleteOrgMemberByID is a helper method to define mock.On call
//   - ctx context.Context
//   - id uuid.UUID
func (_e *MockRepository_Expecter) DeleteOrgMemberByID(ctx interface{}, id interface{}) *MockRepository_DeleteOrgMemberByID_Call {
	return &MockRepository_DeleteOrgMemberByID_Call{Call: _e.mock.On("DeleteOrgMemberByID", ctx, id)}
}

func (_c *MockRepository_DeleteOrgMemberByID_Call) Run(run func(ctx context.Context, id uuid.UUID)) *MockRepository_DeleteOrgMemberByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *MockRepository_DeleteOrgMemberByID_Call) Return(_a0 error) *MockRepository_DeleteOrgMemberByID_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepository_DeleteOrgMemberByID_Call) RunAndReturn(run func(context.Context, uuid.UUID) error) *MockRepository_DeleteOrgMemberByID_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteOrgMembersByUserID provides a mock function with given fields: ctx, userID
func (_m *MockRepository) DeleteOrgMembersByUserID(ctx context.Context, userID uuid.UUID) error {
	ret := _m.Called(ctx, userID)

	if len(ret) == 0 {
		panic("no return value specified for DeleteOrgMembersByUserID")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) error); ok {
		r0 = rf(ctx, userID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepository_DeleteOrgMembersByUserID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteOrgMembersByUserID'
type MockRepository_DeleteOrgMembersByUserID_Call struct {
	*mock.Call
}

// DeleteOrgMembersByUserID is a helper method to define mock.On call
//   - ctx context.Context
//   - userID uuid.UUID
func (_e *MockRepository_Expecter) DeleteOrgMembersByUserID(ctx interface{}, userID interface{}) *MockRepository_DeleteOrgMembersByUserID_Call {
	return &MockRepository_DeleteOrgMembersByUserID_Call{Call: _e.mock.On("DeleteOrgMembersByUserID", ctx, userID)}
}

func (_c *MockRepository_DeleteOrgMembersByUserID_Call) Run(run func(ctx context.Context, userID uuid.UUID)) *MockRepository_DeleteOrgMembersByUserID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *MockRepository_DeleteOrgMembersByUserID_Call) Return(_a0 error) *MockRepository_DeleteOrgMembersByUserID_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepository_DeleteOrgMembersByUserID_Call) RunAndReturn(run func(context.Context, uuid.UUID) error) *MockRepository_DeleteOrgMembersByUserID_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteProjectsByOrganizationID provides a mock function with given fields: ctx, organizationID
func (_m *MockRepository) DeleteProjectsByOrganizationID(ctx context.Context, organizationID uuid.UUID) error {
	ret := _m.Called(ctx, organizationID)

	if len(ret) == 0 {
		panic("no return value specified for DeleteProjectsByOrganizationID")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) error); ok {
		r0 = rf(ctx, organizationID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepository_DeleteProjectsByOrganizationID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteProjectsByOrganizationID'
type MockRepository_DeleteProjectsByOrganizationID_Call struct {
	*mock.Call
}

// DeleteProjectsByOrganizationID is a helper method to define mock.On call
//   - ctx context.Context
//   - organizationID uuid.UUID
func (_e *MockRepository_Expecter) DeleteProjectsByOrganizationID(ctx interface{}, organizationID interface{}) *MockRepository_DeleteProjectsByOrganizationID_Call {
	return &MockRepository_DeleteProjectsByOrganizationID_Call{Call: _e.mock.On("DeleteProjectsByOrganizationID", ctx, organizationID)}
}

func (_c *MockRepository_DeleteProjectsByOrganizationID_Call) Run(run func(ctx context.Context, organizationID uuid.UUID)) *MockRepository_DeleteProjectsByOrganizationID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *MockRepository_DeleteProjectsByOrganizationID_Call) Return(_a0 error) *MockRepository_DeleteProjectsByOrganizationID_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepository_DeleteProjectsByOrganizationID_Call) RunAndReturn(run func(context.Context, uuid.UUID) error) *MockRepository_DeleteProjectsByOrganizationID_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteProjectsByUserID provides a mock function with given fields: ctx, userID
func (_m *MockRepository) DeleteProjectsByUserID(ctx context.Context, userID uuid.UUID) error {
	ret := _m.Called(ctx, userID)

	if len(ret) == 0 {
		panic("no return value specified for DeleteProjectsByUserID")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) error); ok {
		r0 = rf(ctx, userID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepository_DeleteProjectsByUserID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteProjectsByUserID'
type MockRepository_DeleteProjectsByUserID_Call struct {
	*mock.Call
}

// DeleteProjectsByUserID is a helper method to define mock.On call
//   - ctx context.Context
//   - userID uuid.UUID
func (_e *MockRepository_Expecter) DeleteProjectsByUserID(ctx interface{}, userID interface{}) *MockRepository_DeleteProjectsByUserID_Call {
	return &MockRepository_DeleteProjectsByUserID_Call{Call: _e.mock.On("DeleteProjectsByUserID", ctx, userID)}
}

func (_c *MockRepository_DeleteProjectsByUserID_Call) Run(run func(ctx context.Context, userID uuid.UUID)) *MockRepository_DeleteProjectsByUserID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *MockRepository_DeleteProjectsByUserID_Call) Return(_a0 error) *MockRepository_DeleteProjectsByUserID_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepository_DeleteProjectsByUserID_Call) RunAndReturn(run func(context.Context, uuid.UUID) error) *MockRepository_DeleteProjectsByUserID_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteRepoAccessToken provides a mock function with given fields: ctx, in
func (_m *MockRepository) DeleteRepoAccessToken(ctx context.Context, in repository.DeleteRepoAccessTokenInput) error {
	ret := _m.Called(ctx, in)

	if len(ret) == 0 {
		panic("no return value specified for DeleteRepoAccessToken")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.DeleteRepoAccessTokenInput) error); ok {
		r0 = rf(ctx, in)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepository_DeleteRepoAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteRepoAccessToken'
type MockRepository_DeleteRepoAccessToken_Call struct {
	*mock.Call
}

// DeleteRepoAccessToken is a helper method to define mock.On call
//   - ctx context.Context
//   - in repository.DeleteRepoAccessTokenInput
func (_e *MockRepository_Expecter) DeleteRepoAccessToken(ctx interface{}, in interface{}) *MockRepository_DeleteRepoAccessToken_Call {
	return &MockRepository_DeleteRepoAccessToken_Call{Call: _e.mock.On("DeleteRepoAccessToken", ctx, in)}
}

func (_c *MockRepository_DeleteRepoAccessToken_Call) Run(run func(ctx context.Context, in repository.DeleteRepoAccessTokenInput)) *MockRepository_DeleteRepoAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.DeleteRepoAccessTokenInput))
	})
	return _c
}

func (_c *MockRepository_DeleteRepoAccessToken_Call) Return(_a0 error) *MockRepository_DeleteRepoAccessToken_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepository_DeleteRepoAccessToken_Call) RunAndReturn(run func(context.Context, repository.DeleteRepoAccessTokenInput) error) *MockRepository_DeleteRepoAccessToken_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteRepositoryMemberByRepositoryID provides a mock function with given fields: ctx, repositoryID
func (_m *MockRepository) DeleteRepositoryMemberByRepositoryID(ctx context.Context, repositoryID uuid.UUID) error {
	ret := _m.Called(ctx, repositoryID)

	if len(ret) == 0 {
		panic("no return value specified for DeleteRepositoryMemberByRepositoryID")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) error); ok {
		r0 = rf(ctx, repositoryID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepository_DeleteRepositoryMemberByRepositoryID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteRepositoryMemberByRepositoryID'
type MockRepository_DeleteRepositoryMemberByRepositoryID_Call struct {
	*mock.Call
}

// DeleteRepositoryMemberByRepositoryID is a helper method to define mock.On call
//   - ctx context.Context
//   - repositoryID uuid.UUID
func (_e *MockRepository_Expecter) DeleteRepositoryMemberByRepositoryID(ctx interface{}, repositoryID interface{}) *MockRepository_DeleteRepositoryMemberByRepositoryID_Call {
	return &MockRepository_DeleteRepositoryMemberByRepositoryID_Call{Call: _e.mock.On("DeleteRepositoryMemberByRepositoryID", ctx, repositoryID)}
}

func (_c *MockRepository_DeleteRepositoryMemberByRepositoryID_Call) Run(run func(ctx context.Context, repositoryID uuid.UUID)) *MockRepository_DeleteRepositoryMemberByRepositoryID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *MockRepository_DeleteRepositoryMemberByRepositoryID_Call) Return(_a0 error) *MockRepository_DeleteRepositoryMemberByRepositoryID_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepository_DeleteRepositoryMemberByRepositoryID_Call) RunAndReturn(run func(context.Context, uuid.UUID) error) *MockRepository_DeleteRepositoryMemberByRepositoryID_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteRepositoryMemberByUserID provides a mock function with given fields: ctx, userID
func (_m *MockRepository) DeleteRepositoryMemberByUserID(ctx context.Context, userID uuid.UUID) error {
	ret := _m.Called(ctx, userID)

	if len(ret) == 0 {
		panic("no return value specified for DeleteRepositoryMemberByUserID")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) error); ok {
		r0 = rf(ctx, userID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepository_DeleteRepositoryMemberByUserID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteRepositoryMemberByUserID'
type MockRepository_DeleteRepositoryMemberByUserID_Call struct {
	*mock.Call
}

// DeleteRepositoryMemberByUserID is a helper method to define mock.On call
//   - ctx context.Context
//   - userID uuid.UUID
func (_e *MockRepository_Expecter) DeleteRepositoryMemberByUserID(ctx interface{}, userID interface{}) *MockRepository_DeleteRepositoryMemberByUserID_Call {
	return &MockRepository_DeleteRepositoryMemberByUserID_Call{Call: _e.mock.On("DeleteRepositoryMemberByUserID", ctx, userID)}
}

func (_c *MockRepository_DeleteRepositoryMemberByUserID_Call) Run(run func(ctx context.Context, userID uuid.UUID)) *MockRepository_DeleteRepositoryMemberByUserID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *MockRepository_DeleteRepositoryMemberByUserID_Call) Return(_a0 error) *MockRepository_DeleteRepositoryMemberByUserID_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepository_DeleteRepositoryMemberByUserID_Call) RunAndReturn(run func(context.Context, uuid.UUID) error) *MockRepository_DeleteRepositoryMemberByUserID_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteSSHKeysByUserID provides a mock function with given fields: ctx, userID
func (_m *MockRepository) DeleteSSHKeysByUserID(ctx context.Context, userID uuid.UUID) error {
	ret := _m.Called(ctx, userID)

	if len(ret) == 0 {
		panic("no return value specified for DeleteSSHKeysByUserID")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) error); ok {
		r0 = rf(ctx, userID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepository_DeleteSSHKeysByUserID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteSSHKeysByUserID'
type MockRepository_DeleteSSHKeysByUserID_Call struct {
	*mock.Call
}

// DeleteSSHKeysByUserID is a helper method to define mock.On call
//   - ctx context.Context
//   - userID uuid.UUID
func (_e *MockRepository_Expecter) DeleteSSHKeysByUserID(ctx interface{}, userID interface{}) *MockRepository_DeleteSSHKeysByUserID_Call {
	return &MockRepository_DeleteSSHKeysByUserID_Call{Call: _e.mock.On("DeleteSSHKeysByUserID", ctx, userID)}
}

func (_c *MockRepository_DeleteSSHKeysByUserID_Call) Run(run func(ctx context.Context, userID uuid.UUID)) *MockRepository_DeleteSSHKeysByUserID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *MockRepository_DeleteSSHKeysByUserID_Call) Return(_a0 error) *MockRepository_DeleteSSHKeysByUserID_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepository_DeleteSSHKeysByUserID_Call) RunAndReturn(run func(context.Context, uuid.UUID) error) *MockRepository_DeleteSSHKeysByUserID_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteUser provides a mock function with given fields: ctx, userID
func (_m *MockRepository) DeleteUser(ctx context.Context, userID uuid.UUID) error {
	ret := _m.Called(ctx, userID)

	if len(ret) == 0 {
		panic("no return value specified for DeleteUser")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) error); ok {
		r0 = rf(ctx, userID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepository_DeleteUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteUser'
type MockRepository_DeleteUser_Call struct {
	*mock.Call
}

// DeleteUser is a helper method to define mock.On call
//   - ctx context.Context
//   - userID uuid.UUID
func (_e *MockRepository_Expecter) DeleteUser(ctx interface{}, userID interface{}) *MockRepository_DeleteUser_Call {
	return &MockRepository_DeleteUser_Call{Call: _e.mock.On("DeleteUser", ctx, userID)}
}

func (_c *MockRepository_DeleteUser_Call) Run(run func(ctx context.Context, userID uuid.UUID)) *MockRepository_DeleteUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *MockRepository_DeleteUser_Call) Return(_a0 error) *MockRepository_DeleteUser_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepository_DeleteUser_Call) RunAndReturn(run func(context.Context, uuid.UUID) error) *MockRepository_DeleteUser_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteUserGitGroup provides a mock function with given fields: ctx, userID
func (_m *MockRepository) DeleteUserGitGroup(ctx context.Context, userID uuid.UUID) error {
	ret := _m.Called(ctx, userID)

	if len(ret) == 0 {
		panic("no return value specified for DeleteUserGitGroup")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) error); ok {
		r0 = rf(ctx, userID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepository_DeleteUserGitGroup_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteUserGitGroup'
type MockRepository_DeleteUserGitGroup_Call struct {
	*mock.Call
}

// DeleteUserGitGroup is a helper method to define mock.On call
//   - ctx context.Context
//   - userID uuid.UUID
func (_e *MockRepository_Expecter) DeleteUserGitGroup(ctx interface{}, userID interface{}) *MockRepository_DeleteUserGitGroup_Call {
	return &MockRepository_DeleteUserGitGroup_Call{Call: _e.mock.On("DeleteUserGitGroup", ctx, userID)}
}

func (_c *MockRepository_DeleteUserGitGroup_Call) Run(run func(ctx context.Context, userID uuid.UUID)) *MockRepository_DeleteUserGitGroup_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *MockRepository_DeleteUserGitGroup_Call) Return(_a0 error) *MockRepository_DeleteUserGitGroup_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepository_DeleteUserGitGroup_Call) RunAndReturn(run func(context.Context, uuid.UUID) error) *MockRepository_DeleteUserGitGroup_Call {
	_c.Call.Return(run)
	return _c
}

// FindAccessToken provides a mock function with given fields: ctx, in
func (_m *MockRepository) FindAccessToken(ctx context.Context, in repository.FindAccessTokenQuery) (*entities.UserToken, error) {
	ret := _m.Called(ctx, in)

	if len(ret) == 0 {
		panic("no return value specified for FindAccessToken")
	}

	var r0 *entities.UserToken
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.FindAccessTokenQuery) (*entities.UserToken, error)); ok {
		return rf(ctx, in)
	}
	if rf, ok := ret.Get(0).(func(context.Context, repository.FindAccessTokenQuery) *entities.UserToken); ok {
		r0 = rf(ctx, in)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.UserToken)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, repository.FindAccessTokenQuery) error); ok {
		r1 = rf(ctx, in)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_FindAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindAccessToken'
type MockRepository_FindAccessToken_Call struct {
	*mock.Call
}

// FindAccessToken is a helper method to define mock.On call
//   - ctx context.Context
//   - in repository.FindAccessTokenQuery
func (_e *MockRepository_Expecter) FindAccessToken(ctx interface{}, in interface{}) *MockRepository_FindAccessToken_Call {
	return &MockRepository_FindAccessToken_Call{Call: _e.mock.On("FindAccessToken", ctx, in)}
}

func (_c *MockRepository_FindAccessToken_Call) Run(run func(ctx context.Context, in repository.FindAccessTokenQuery)) *MockRepository_FindAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.FindAccessTokenQuery))
	})
	return _c
}

func (_c *MockRepository_FindAccessToken_Call) Return(_a0 *entities.UserToken, _a1 error) *MockRepository_FindAccessToken_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_FindAccessToken_Call) RunAndReturn(run func(context.Context, repository.FindAccessTokenQuery) (*entities.UserToken, error)) *MockRepository_FindAccessToken_Call {
	_c.Call.Return(run)
	return _c
}

// FindAuthUser provides a mock function with given fields: ctx, filters
func (_m *MockRepository) FindAuthUser(ctx context.Context, filters dto.AuthUserFilter) (*entities.AuthUser, error) {
	ret := _m.Called(ctx, filters)

	if len(ret) == 0 {
		panic("no return value specified for FindAuthUser")
	}

	var r0 *entities.AuthUser
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.AuthUserFilter) (*entities.AuthUser, error)); ok {
		return rf(ctx, filters)
	}
	if rf, ok := ret.Get(0).(func(context.Context, dto.AuthUserFilter) *entities.AuthUser); ok {
		r0 = rf(ctx, filters)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.AuthUser)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, dto.AuthUserFilter) error); ok {
		r1 = rf(ctx, filters)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_FindAuthUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindAuthUser'
type MockRepository_FindAuthUser_Call struct {
	*mock.Call
}

// FindAuthUser is a helper method to define mock.On call
//   - ctx context.Context
//   - filters dto.AuthUserFilter
func (_e *MockRepository_Expecter) FindAuthUser(ctx interface{}, filters interface{}) *MockRepository_FindAuthUser_Call {
	return &MockRepository_FindAuthUser_Call{Call: _e.mock.On("FindAuthUser", ctx, filters)}
}

func (_c *MockRepository_FindAuthUser_Call) Run(run func(ctx context.Context, filters dto.AuthUserFilter)) *MockRepository_FindAuthUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.AuthUserFilter))
	})
	return _c
}

func (_c *MockRepository_FindAuthUser_Call) Return(_a0 *entities.AuthUser, _a1 error) *MockRepository_FindAuthUser_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_FindAuthUser_Call) RunAndReturn(run func(context.Context, dto.AuthUserFilter) (*entities.AuthUser, error)) *MockRepository_FindAuthUser_Call {
	_c.Call.Return(run)
	return _c
}

// FindDeployment provides a mock function with given fields: ctx, repoID, clauses
func (_m *MockRepository) FindDeployment(ctx context.Context, repoID uuid.UUID, clauses ...clause.Expression) (*entities.Deployment, error) {
	_va := make([]interface{}, len(clauses))
	for _i := range clauses {
		_va[_i] = clauses[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, repoID)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for FindDeployment")
	}

	var r0 *entities.Deployment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID, ...clause.Expression) (*entities.Deployment, error)); ok {
		return rf(ctx, repoID, clauses...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID, ...clause.Expression) *entities.Deployment); ok {
		r0 = rf(ctx, repoID, clauses...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.Deployment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uuid.UUID, ...clause.Expression) error); ok {
		r1 = rf(ctx, repoID, clauses...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_FindDeployment_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindDeployment'
type MockRepository_FindDeployment_Call struct {
	*mock.Call
}

// FindDeployment is a helper method to define mock.On call
//   - ctx context.Context
//   - repoID uuid.UUID
//   - clauses ...clause.Expression
func (_e *MockRepository_Expecter) FindDeployment(ctx interface{}, repoID interface{}, clauses ...interface{}) *MockRepository_FindDeployment_Call {
	return &MockRepository_FindDeployment_Call{Call: _e.mock.On("FindDeployment",
		append([]interface{}{ctx, repoID}, clauses...)...)}
}

func (_c *MockRepository_FindDeployment_Call) Run(run func(ctx context.Context, repoID uuid.UUID, clauses ...clause.Expression)) *MockRepository_FindDeployment_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]clause.Expression, len(args)-2)
		for i, a := range args[2:] {
			if a != nil {
				variadicArgs[i] = a.(clause.Expression)
			}
		}
		run(args[0].(context.Context), args[1].(uuid.UUID), variadicArgs...)
	})
	return _c
}

func (_c *MockRepository_FindDeployment_Call) Return(_a0 *entities.Deployment, _a1 error) *MockRepository_FindDeployment_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_FindDeployment_Call) RunAndReturn(run func(context.Context, uuid.UUID, ...clause.Expression) (*entities.Deployment, error)) *MockRepository_FindDeployment_Call {
	_c.Call.Return(run)
	return _c
}

// FindECRDeployment provides a mock function with given fields: ctx, input
func (_m *MockRepository) FindECRDeployment(ctx context.Context, input repository.FindECRDeploymentInput) (*entities.CustomImageDeployment, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for FindECRDeployment")
	}

	var r0 *entities.CustomImageDeployment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.FindECRDeploymentInput) (*entities.CustomImageDeployment, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, repository.FindECRDeploymentInput) *entities.CustomImageDeployment); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.CustomImageDeployment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, repository.FindECRDeploymentInput) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_FindECRDeployment_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindECRDeployment'
type MockRepository_FindECRDeployment_Call struct {
	*mock.Call
}

// FindECRDeployment is a helper method to define mock.On call
//   - ctx context.Context
//   - input repository.FindECRDeploymentInput
func (_e *MockRepository_Expecter) FindECRDeployment(ctx interface{}, input interface{}) *MockRepository_FindECRDeployment_Call {
	return &MockRepository_FindECRDeployment_Call{Call: _e.mock.On("FindECRDeployment", ctx, input)}
}

func (_c *MockRepository_FindECRDeployment_Call) Run(run func(ctx context.Context, input repository.FindECRDeploymentInput)) *MockRepository_FindECRDeployment_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.FindECRDeploymentInput))
	})
	return _c
}

func (_c *MockRepository_FindECRDeployment_Call) Return(_a0 *entities.CustomImageDeployment, _a1 error) *MockRepository_FindECRDeployment_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_FindECRDeployment_Call) RunAndReturn(run func(context.Context, repository.FindECRDeploymentInput) (*entities.CustomImageDeployment, error)) *MockRepository_FindECRDeployment_Call {
	_c.Call.Return(run)
	return _c
}

// FindHardwareByID provides a mock function with given fields: ctx, id
func (_m *MockRepository) FindHardwareByID(ctx context.Context, id uuid.UUID) (*entities.Hardware, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for FindHardwareByID")
	}

	var r0 *entities.Hardware
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) (*entities.Hardware, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) *entities.Hardware); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.Hardware)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uuid.UUID) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_FindHardwareByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindHardwareByID'
type MockRepository_FindHardwareByID_Call struct {
	*mock.Call
}

// FindHardwareByID is a helper method to define mock.On call
//   - ctx context.Context
//   - id uuid.UUID
func (_e *MockRepository_Expecter) FindHardwareByID(ctx interface{}, id interface{}) *MockRepository_FindHardwareByID_Call {
	return &MockRepository_FindHardwareByID_Call{Call: _e.mock.On("FindHardwareByID", ctx, id)}
}

func (_c *MockRepository_FindHardwareByID_Call) Run(run func(ctx context.Context, id uuid.UUID)) *MockRepository_FindHardwareByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *MockRepository_FindHardwareByID_Call) Return(_a0 *entities.Hardware, _a1 error) *MockRepository_FindHardwareByID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_FindHardwareByID_Call) RunAndReturn(run func(context.Context, uuid.UUID) (*entities.Hardware, error)) *MockRepository_FindHardwareByID_Call {
	_c.Call.Return(run)
	return _c
}

// FindOrgAccessToken provides a mock function with given fields: ctx, in
func (_m *MockRepository) FindOrgAccessToken(ctx context.Context, in repository.FindOrgAccessTokenQuery) (*entities.OrgToken, error) {
	ret := _m.Called(ctx, in)

	if len(ret) == 0 {
		panic("no return value specified for FindOrgAccessToken")
	}

	var r0 *entities.OrgToken
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.FindOrgAccessTokenQuery) (*entities.OrgToken, error)); ok {
		return rf(ctx, in)
	}
	if rf, ok := ret.Get(0).(func(context.Context, repository.FindOrgAccessTokenQuery) *entities.OrgToken); ok {
		r0 = rf(ctx, in)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.OrgToken)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, repository.FindOrgAccessTokenQuery) error); ok {
		r1 = rf(ctx, in)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_FindOrgAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindOrgAccessToken'
type MockRepository_FindOrgAccessToken_Call struct {
	*mock.Call
}

// FindOrgAccessToken is a helper method to define mock.On call
//   - ctx context.Context
//   - in repository.FindOrgAccessTokenQuery
func (_e *MockRepository_Expecter) FindOrgAccessToken(ctx interface{}, in interface{}) *MockRepository_FindOrgAccessToken_Call {
	return &MockRepository_FindOrgAccessToken_Call{Call: _e.mock.On("FindOrgAccessToken", ctx, in)}
}

func (_c *MockRepository_FindOrgAccessToken_Call) Run(run func(ctx context.Context, in repository.FindOrgAccessTokenQuery)) *MockRepository_FindOrgAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.FindOrgAccessTokenQuery))
	})
	return _c
}

func (_c *MockRepository_FindOrgAccessToken_Call) Return(_a0 *entities.OrgToken, _a1 error) *MockRepository_FindOrgAccessToken_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_FindOrgAccessToken_Call) RunAndReturn(run func(context.Context, repository.FindOrgAccessTokenQuery) (*entities.OrgToken, error)) *MockRepository_FindOrgAccessToken_Call {
	_c.Call.Return(run)
	return _c
}

// FindOrgDeployments provides a mock function with given fields: ctx, orgID
func (_m *MockRepository) FindOrgDeployments(ctx context.Context, orgID uuid.UUID) ([]entities.CustomImageDeployment, error) {
	ret := _m.Called(ctx, orgID)

	if len(ret) == 0 {
		panic("no return value specified for FindOrgDeployments")
	}

	var r0 []entities.CustomImageDeployment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) ([]entities.CustomImageDeployment, error)); ok {
		return rf(ctx, orgID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) []entities.CustomImageDeployment); ok {
		r0 = rf(ctx, orgID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]entities.CustomImageDeployment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uuid.UUID) error); ok {
		r1 = rf(ctx, orgID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_FindOrgDeployments_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindOrgDeployments'
type MockRepository_FindOrgDeployments_Call struct {
	*mock.Call
}

// FindOrgDeployments is a helper method to define mock.On call
//   - ctx context.Context
//   - orgID uuid.UUID
func (_e *MockRepository_Expecter) FindOrgDeployments(ctx interface{}, orgID interface{}) *MockRepository_FindOrgDeployments_Call {
	return &MockRepository_FindOrgDeployments_Call{Call: _e.mock.On("FindOrgDeployments", ctx, orgID)}
}

func (_c *MockRepository_FindOrgDeployments_Call) Run(run func(ctx context.Context, orgID uuid.UUID)) *MockRepository_FindOrgDeployments_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *MockRepository_FindOrgDeployments_Call) Return(_a0 []entities.CustomImageDeployment, _a1 error) *MockRepository_FindOrgDeployments_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_FindOrgDeployments_Call) RunAndReturn(run func(context.Context, uuid.UUID) ([]entities.CustomImageDeployment, error)) *MockRepository_FindOrgDeployments_Call {
	_c.Call.Return(run)
	return _c
}

// FindOrgGitGroup provides a mock function with given fields: ctx, orgID
func (_m *MockRepository) FindOrgGitGroup(ctx context.Context, orgID uuid.UUID) (*entities.DefaultGitGroup, error) {
	ret := _m.Called(ctx, orgID)

	if len(ret) == 0 {
		panic("no return value specified for FindOrgGitGroup")
	}

	var r0 *entities.DefaultGitGroup
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) (*entities.DefaultGitGroup, error)); ok {
		return rf(ctx, orgID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) *entities.DefaultGitGroup); ok {
		r0 = rf(ctx, orgID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.DefaultGitGroup)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uuid.UUID) error); ok {
		r1 = rf(ctx, orgID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_FindOrgGitGroup_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindOrgGitGroup'
type MockRepository_FindOrgGitGroup_Call struct {
	*mock.Call
}

// FindOrgGitGroup is a helper method to define mock.On call
//   - ctx context.Context
//   - orgID uuid.UUID
func (_e *MockRepository_Expecter) FindOrgGitGroup(ctx interface{}, orgID interface{}) *MockRepository_FindOrgGitGroup_Call {
	return &MockRepository_FindOrgGitGroup_Call{Call: _e.mock.On("FindOrgGitGroup", ctx, orgID)}
}

func (_c *MockRepository_FindOrgGitGroup_Call) Run(run func(ctx context.Context, orgID uuid.UUID)) *MockRepository_FindOrgGitGroup_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *MockRepository_FindOrgGitGroup_Call) Return(_a0 *entities.DefaultGitGroup, _a1 error) *MockRepository_FindOrgGitGroup_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_FindOrgGitGroup_Call) RunAndReturn(run func(context.Context, uuid.UUID) (*entities.DefaultGitGroup, error)) *MockRepository_FindOrgGitGroup_Call {
	_c.Call.Return(run)
	return _c
}

// FindOrgMember provides a mock function with given fields: ctx, filter
func (_m *MockRepository) FindOrgMember(ctx context.Context, filter repository.FilterOrgMember) (*entities.OrgMember, error) {
	ret := _m.Called(ctx, filter)

	if len(ret) == 0 {
		panic("no return value specified for FindOrgMember")
	}

	var r0 *entities.OrgMember
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.FilterOrgMember) (*entities.OrgMember, error)); ok {
		return rf(ctx, filter)
	}
	if rf, ok := ret.Get(0).(func(context.Context, repository.FilterOrgMember) *entities.OrgMember); ok {
		r0 = rf(ctx, filter)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.OrgMember)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, repository.FilterOrgMember) error); ok {
		r1 = rf(ctx, filter)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_FindOrgMember_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindOrgMember'
type MockRepository_FindOrgMember_Call struct {
	*mock.Call
}

// FindOrgMember is a helper method to define mock.On call
//   - ctx context.Context
//   - filter repository.FilterOrgMember
func (_e *MockRepository_Expecter) FindOrgMember(ctx interface{}, filter interface{}) *MockRepository_FindOrgMember_Call {
	return &MockRepository_FindOrgMember_Call{Call: _e.mock.On("FindOrgMember", ctx, filter)}
}

func (_c *MockRepository_FindOrgMember_Call) Run(run func(ctx context.Context, filter repository.FilterOrgMember)) *MockRepository_FindOrgMember_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.FilterOrgMember))
	})
	return _c
}

func (_c *MockRepository_FindOrgMember_Call) Return(_a0 *entities.OrgMember, _a1 error) *MockRepository_FindOrgMember_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_FindOrgMember_Call) RunAndReturn(run func(context.Context, repository.FilterOrgMember) (*entities.OrgMember, error)) *MockRepository_FindOrgMember_Call {
	_c.Call.Return(run)
	return _c
}

// FindOrganization provides a mock function with given fields: ctx, input
func (_m *MockRepository) FindOrganization(ctx context.Context, input repository.FilterOrganization) (*entities.Organization, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for FindOrganization")
	}

	var r0 *entities.Organization
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.FilterOrganization) (*entities.Organization, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, repository.FilterOrganization) *entities.Organization); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.Organization)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, repository.FilterOrganization) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_FindOrganization_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindOrganization'
type MockRepository_FindOrganization_Call struct {
	*mock.Call
}

// FindOrganization is a helper method to define mock.On call
//   - ctx context.Context
//   - input repository.FilterOrganization
func (_e *MockRepository_Expecter) FindOrganization(ctx interface{}, input interface{}) *MockRepository_FindOrganization_Call {
	return &MockRepository_FindOrganization_Call{Call: _e.mock.On("FindOrganization", ctx, input)}
}

func (_c *MockRepository_FindOrganization_Call) Run(run func(ctx context.Context, input repository.FilterOrganization)) *MockRepository_FindOrganization_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.FilterOrganization))
	})
	return _c
}

func (_c *MockRepository_FindOrganization_Call) Return(_a0 *entities.Organization, _a1 error) *MockRepository_FindOrganization_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_FindOrganization_Call) RunAndReturn(run func(context.Context, repository.FilterOrganization) (*entities.Organization, error)) *MockRepository_FindOrganization_Call {
	_c.Call.Return(run)
	return _c
}

// FindOrganizationByID provides a mock function with given fields: ctx, id
func (_m *MockRepository) FindOrganizationByID(ctx context.Context, id uuid.UUID) (*entities.Organization, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for FindOrganizationByID")
	}

	var r0 *entities.Organization
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) (*entities.Organization, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) *entities.Organization); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.Organization)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uuid.UUID) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_FindOrganizationByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindOrganizationByID'
type MockRepository_FindOrganizationByID_Call struct {
	*mock.Call
}

// FindOrganizationByID is a helper method to define mock.On call
//   - ctx context.Context
//   - id uuid.UUID
func (_e *MockRepository_Expecter) FindOrganizationByID(ctx interface{}, id interface{}) *MockRepository_FindOrganizationByID_Call {
	return &MockRepository_FindOrganizationByID_Call{Call: _e.mock.On("FindOrganizationByID", ctx, id)}
}

func (_c *MockRepository_FindOrganizationByID_Call) Run(run func(ctx context.Context, id uuid.UUID)) *MockRepository_FindOrganizationByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *MockRepository_FindOrganizationByID_Call) Return(_a0 *entities.Organization, _a1 error) *MockRepository_FindOrganizationByID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_FindOrganizationByID_Call) RunAndReturn(run func(context.Context, uuid.UUID) (*entities.Organization, error)) *MockRepository_FindOrganizationByID_Call {
	_c.Call.Return(run)
	return _c
}

// FindRepoAccessToken provides a mock function with given fields: ctx, in
func (_m *MockRepository) FindRepoAccessToken(ctx context.Context, in repository.FindRepoAccessTokenQuery) (*entities.RepoAccessToken, error) {
	ret := _m.Called(ctx, in)

	if len(ret) == 0 {
		panic("no return value specified for FindRepoAccessToken")
	}

	var r0 *entities.RepoAccessToken
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.FindRepoAccessTokenQuery) (*entities.RepoAccessToken, error)); ok {
		return rf(ctx, in)
	}
	if rf, ok := ret.Get(0).(func(context.Context, repository.FindRepoAccessTokenQuery) *entities.RepoAccessToken); ok {
		r0 = rf(ctx, in)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.RepoAccessToken)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, repository.FindRepoAccessTokenQuery) error); ok {
		r1 = rf(ctx, in)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_FindRepoAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindRepoAccessToken'
type MockRepository_FindRepoAccessToken_Call struct {
	*mock.Call
}

// FindRepoAccessToken is a helper method to define mock.On call
//   - ctx context.Context
//   - in repository.FindRepoAccessTokenQuery
func (_e *MockRepository_Expecter) FindRepoAccessToken(ctx interface{}, in interface{}) *MockRepository_FindRepoAccessToken_Call {
	return &MockRepository_FindRepoAccessToken_Call{Call: _e.mock.On("FindRepoAccessToken", ctx, in)}
}

func (_c *MockRepository_FindRepoAccessToken_Call) Run(run func(ctx context.Context, in repository.FindRepoAccessTokenQuery)) *MockRepository_FindRepoAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.FindRepoAccessTokenQuery))
	})
	return _c
}

func (_c *MockRepository_FindRepoAccessToken_Call) Return(_a0 *entities.RepoAccessToken, _a1 error) *MockRepository_FindRepoAccessToken_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_FindRepoAccessToken_Call) RunAndReturn(run func(context.Context, repository.FindRepoAccessTokenQuery) (*entities.RepoAccessToken, error)) *MockRepository_FindRepoAccessToken_Call {
	_c.Call.Return(run)
	return _c
}

// FindRepoTag provides a mock function with given fields: ctx, filter
func (_m *MockRepository) FindRepoTag(ctx context.Context, filter repository.FilterTagInput) (*entities.Tag, error) {
	ret := _m.Called(ctx, filter)

	if len(ret) == 0 {
		panic("no return value specified for FindRepoTag")
	}

	var r0 *entities.Tag
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.FilterTagInput) (*entities.Tag, error)); ok {
		return rf(ctx, filter)
	}
	if rf, ok := ret.Get(0).(func(context.Context, repository.FilterTagInput) *entities.Tag); ok {
		r0 = rf(ctx, filter)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.Tag)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, repository.FilterTagInput) error); ok {
		r1 = rf(ctx, filter)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_FindRepoTag_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindRepoTag'
type MockRepository_FindRepoTag_Call struct {
	*mock.Call
}

// FindRepoTag is a helper method to define mock.On call
//   - ctx context.Context
//   - filter repository.FilterTagInput
func (_e *MockRepository_Expecter) FindRepoTag(ctx interface{}, filter interface{}) *MockRepository_FindRepoTag_Call {
	return &MockRepository_FindRepoTag_Call{Call: _e.mock.On("FindRepoTag", ctx, filter)}
}

func (_c *MockRepository_FindRepoTag_Call) Run(run func(ctx context.Context, filter repository.FilterTagInput)) *MockRepository_FindRepoTag_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.FilterTagInput))
	})
	return _c
}

func (_c *MockRepository_FindRepoTag_Call) Return(_a0 *entities.Tag, _a1 error) *MockRepository_FindRepoTag_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_FindRepoTag_Call) RunAndReturn(run func(context.Context, repository.FilterTagInput) (*entities.Tag, error)) *MockRepository_FindRepoTag_Call {
	_c.Call.Return(run)
	return _c
}

// FindRepository provides a mock function with given fields: ctx, filter
func (_m *MockRepository) FindRepository(ctx context.Context, filter repository.RepositoryFilter) (*entities.Repository, error) {
	ret := _m.Called(ctx, filter)

	if len(ret) == 0 {
		panic("no return value specified for FindRepository")
	}

	var r0 *entities.Repository
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.RepositoryFilter) (*entities.Repository, error)); ok {
		return rf(ctx, filter)
	}
	if rf, ok := ret.Get(0).(func(context.Context, repository.RepositoryFilter) *entities.Repository); ok {
		r0 = rf(ctx, filter)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.Repository)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, repository.RepositoryFilter) error); ok {
		r1 = rf(ctx, filter)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_FindRepository_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindRepository'
type MockRepository_FindRepository_Call struct {
	*mock.Call
}

// FindRepository is a helper method to define mock.On call
//   - ctx context.Context
//   - filter repository.RepositoryFilter
func (_e *MockRepository_Expecter) FindRepository(ctx interface{}, filter interface{}) *MockRepository_FindRepository_Call {
	return &MockRepository_FindRepository_Call{Call: _e.mock.On("FindRepository", ctx, filter)}
}

func (_c *MockRepository_FindRepository_Call) Run(run func(ctx context.Context, filter repository.RepositoryFilter)) *MockRepository_FindRepository_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.RepositoryFilter))
	})
	return _c
}

func (_c *MockRepository_FindRepository_Call) Return(_a0 *entities.Repository, _a1 error) *MockRepository_FindRepository_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_FindRepository_Call) RunAndReturn(run func(context.Context, repository.RepositoryFilter) (*entities.Repository, error)) *MockRepository_FindRepository_Call {
	_c.Call.Return(run)
	return _c
}

// FindRepositoryMember provides a mock function with given fields: ctx, filter
func (_m *MockRepository) FindRepositoryMember(ctx context.Context, filter repository.FilterRepoMember) (*entities.RepoMember, error) {
	ret := _m.Called(ctx, filter)

	if len(ret) == 0 {
		panic("no return value specified for FindRepositoryMember")
	}

	var r0 *entities.RepoMember
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.FilterRepoMember) (*entities.RepoMember, error)); ok {
		return rf(ctx, filter)
	}
	if rf, ok := ret.Get(0).(func(context.Context, repository.FilterRepoMember) *entities.RepoMember); ok {
		r0 = rf(ctx, filter)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.RepoMember)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, repository.FilterRepoMember) error); ok {
		r1 = rf(ctx, filter)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_FindRepositoryMember_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindRepositoryMember'
type MockRepository_FindRepositoryMember_Call struct {
	*mock.Call
}

// FindRepositoryMember is a helper method to define mock.On call
//   - ctx context.Context
//   - filter repository.FilterRepoMember
func (_e *MockRepository_Expecter) FindRepositoryMember(ctx interface{}, filter interface{}) *MockRepository_FindRepositoryMember_Call {
	return &MockRepository_FindRepositoryMember_Call{Call: _e.mock.On("FindRepositoryMember", ctx, filter)}
}

func (_c *MockRepository_FindRepositoryMember_Call) Run(run func(ctx context.Context, filter repository.FilterRepoMember)) *MockRepository_FindRepositoryMember_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.FilterRepoMember))
	})
	return _c
}

func (_c *MockRepository_FindRepositoryMember_Call) Return(_a0 *entities.RepoMember, _a1 error) *MockRepository_FindRepositoryMember_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_FindRepositoryMember_Call) RunAndReturn(run func(context.Context, repository.FilterRepoMember) (*entities.RepoMember, error)) *MockRepository_FindRepositoryMember_Call {
	_c.Call.Return(run)
	return _c
}

// FindTemplate provides a mock function with given fields: ctx, id
func (_m *MockRepository) FindTemplate(ctx context.Context, id uuid.UUID) (*entities.RepoTemplate, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for FindTemplate")
	}

	var r0 *entities.RepoTemplate
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) (*entities.RepoTemplate, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) *entities.RepoTemplate); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.RepoTemplate)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uuid.UUID) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_FindTemplate_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindTemplate'
type MockRepository_FindTemplate_Call struct {
	*mock.Call
}

// FindTemplate is a helper method to define mock.On call
//   - ctx context.Context
//   - id uuid.UUID
func (_e *MockRepository_Expecter) FindTemplate(ctx interface{}, id interface{}) *MockRepository_FindTemplate_Call {
	return &MockRepository_FindTemplate_Call{Call: _e.mock.On("FindTemplate", ctx, id)}
}

func (_c *MockRepository_FindTemplate_Call) Run(run func(ctx context.Context, id uuid.UUID)) *MockRepository_FindTemplate_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *MockRepository_FindTemplate_Call) Return(_a0 *entities.RepoTemplate, _a1 error) *MockRepository_FindTemplate_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_FindTemplate_Call) RunAndReturn(run func(context.Context, uuid.UUID) (*entities.RepoTemplate, error)) *MockRepository_FindTemplate_Call {
	_c.Call.Return(run)
	return _c
}

// FindUser provides a mock function with given fields: ctx, filters
func (_m *MockRepository) FindUser(ctx context.Context, filters repository.FindUserFilter) (*entities.User, error) {
	ret := _m.Called(ctx, filters)

	if len(ret) == 0 {
		panic("no return value specified for FindUser")
	}

	var r0 *entities.User
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.FindUserFilter) (*entities.User, error)); ok {
		return rf(ctx, filters)
	}
	if rf, ok := ret.Get(0).(func(context.Context, repository.FindUserFilter) *entities.User); ok {
		r0 = rf(ctx, filters)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.User)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, repository.FindUserFilter) error); ok {
		r1 = rf(ctx, filters)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_FindUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindUser'
type MockRepository_FindUser_Call struct {
	*mock.Call
}

// FindUser is a helper method to define mock.On call
//   - ctx context.Context
//   - filters repository.FindUserFilter
func (_e *MockRepository_Expecter) FindUser(ctx interface{}, filters interface{}) *MockRepository_FindUser_Call {
	return &MockRepository_FindUser_Call{Call: _e.mock.On("FindUser", ctx, filters)}
}

func (_c *MockRepository_FindUser_Call) Run(run func(ctx context.Context, filters repository.FindUserFilter)) *MockRepository_FindUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.FindUserFilter))
	})
	return _c
}

func (_c *MockRepository_FindUser_Call) Return(_a0 *entities.User, _a1 error) *MockRepository_FindUser_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_FindUser_Call) RunAndReturn(run func(context.Context, repository.FindUserFilter) (*entities.User, error)) *MockRepository_FindUser_Call {
	_c.Call.Return(run)
	return _c
}

// FindUserByID provides a mock function with given fields: ctx, id
func (_m *MockRepository) FindUserByID(ctx context.Context, id uuid.UUID) (*entities.User, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for FindUserByID")
	}

	var r0 *entities.User
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) (*entities.User, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) *entities.User); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.User)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uuid.UUID) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_FindUserByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindUserByID'
type MockRepository_FindUserByID_Call struct {
	*mock.Call
}

// FindUserByID is a helper method to define mock.On call
//   - ctx context.Context
//   - id uuid.UUID
func (_e *MockRepository_Expecter) FindUserByID(ctx interface{}, id interface{}) *MockRepository_FindUserByID_Call {
	return &MockRepository_FindUserByID_Call{Call: _e.mock.On("FindUserByID", ctx, id)}
}

func (_c *MockRepository_FindUserByID_Call) Run(run func(ctx context.Context, id uuid.UUID)) *MockRepository_FindUserByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *MockRepository_FindUserByID_Call) Return(_a0 *entities.User, _a1 error) *MockRepository_FindUserByID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_FindUserByID_Call) RunAndReturn(run func(context.Context, uuid.UUID) (*entities.User, error)) *MockRepository_FindUserByID_Call {
	_c.Call.Return(run)
	return _c
}

// FindUserDeployments provides a mock function with given fields: ctx, userID
func (_m *MockRepository) FindUserDeployments(ctx context.Context, userID uuid.UUID) ([]entities.CustomImageDeployment, error) {
	ret := _m.Called(ctx, userID)

	if len(ret) == 0 {
		panic("no return value specified for FindUserDeployments")
	}

	var r0 []entities.CustomImageDeployment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) ([]entities.CustomImageDeployment, error)); ok {
		return rf(ctx, userID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) []entities.CustomImageDeployment); ok {
		r0 = rf(ctx, userID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]entities.CustomImageDeployment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uuid.UUID) error); ok {
		r1 = rf(ctx, userID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_FindUserDeployments_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindUserDeployments'
type MockRepository_FindUserDeployments_Call struct {
	*mock.Call
}

// FindUserDeployments is a helper method to define mock.On call
//   - ctx context.Context
//   - userID uuid.UUID
func (_e *MockRepository_Expecter) FindUserDeployments(ctx interface{}, userID interface{}) *MockRepository_FindUserDeployments_Call {
	return &MockRepository_FindUserDeployments_Call{Call: _e.mock.On("FindUserDeployments", ctx, userID)}
}

func (_c *MockRepository_FindUserDeployments_Call) Run(run func(ctx context.Context, userID uuid.UUID)) *MockRepository_FindUserDeployments_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *MockRepository_FindUserDeployments_Call) Return(_a0 []entities.CustomImageDeployment, _a1 error) *MockRepository_FindUserDeployments_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_FindUserDeployments_Call) RunAndReturn(run func(context.Context, uuid.UUID) ([]entities.CustomImageDeployment, error)) *MockRepository_FindUserDeployments_Call {
	_c.Call.Return(run)
	return _c
}

// FindUserGitGroup provides a mock function with given fields: ctx, userID
func (_m *MockRepository) FindUserGitGroup(ctx context.Context, userID uuid.UUID) (*entities.DefaultGitGroup, error) {
	ret := _m.Called(ctx, userID)

	if len(ret) == 0 {
		panic("no return value specified for FindUserGitGroup")
	}

	var r0 *entities.DefaultGitGroup
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) (*entities.DefaultGitGroup, error)); ok {
		return rf(ctx, userID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) *entities.DefaultGitGroup); ok {
		r0 = rf(ctx, userID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.DefaultGitGroup)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uuid.UUID) error); ok {
		r1 = rf(ctx, userID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_FindUserGitGroup_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindUserGitGroup'
type MockRepository_FindUserGitGroup_Call struct {
	*mock.Call
}

// FindUserGitGroup is a helper method to define mock.On call
//   - ctx context.Context
//   - userID uuid.UUID
func (_e *MockRepository_Expecter) FindUserGitGroup(ctx interface{}, userID interface{}) *MockRepository_FindUserGitGroup_Call {
	return &MockRepository_FindUserGitGroup_Call{Call: _e.mock.On("FindUserGitGroup", ctx, userID)}
}

func (_c *MockRepository_FindUserGitGroup_Call) Run(run func(ctx context.Context, userID uuid.UUID)) *MockRepository_FindUserGitGroup_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *MockRepository_FindUserGitGroup_Call) Return(_a0 *entities.DefaultGitGroup, _a1 error) *MockRepository_FindUserGitGroup_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_FindUserGitGroup_Call) RunAndReturn(run func(context.Context, uuid.UUID) (*entities.DefaultGitGroup, error)) *MockRepository_FindUserGitGroup_Call {
	_c.Call.Return(run)
	return _c
}

// GetAppPermission provides a mock function with given fields: ctx, userID
func (_m *MockRepository) GetAppPermission(ctx context.Context, userID uuid.UUID) ([]entities.AppPermission, error) {
	ret := _m.Called(ctx, userID)

	if len(ret) == 0 {
		panic("no return value specified for GetAppPermission")
	}

	var r0 []entities.AppPermission
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) ([]entities.AppPermission, error)); ok {
		return rf(ctx, userID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) []entities.AppPermission); ok {
		r0 = rf(ctx, userID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]entities.AppPermission)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uuid.UUID) error); ok {
		r1 = rf(ctx, userID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_GetAppPermission_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAppPermission'
type MockRepository_GetAppPermission_Call struct {
	*mock.Call
}

// GetAppPermission is a helper method to define mock.On call
//   - ctx context.Context
//   - userID uuid.UUID
func (_e *MockRepository_Expecter) GetAppPermission(ctx interface{}, userID interface{}) *MockRepository_GetAppPermission_Call {
	return &MockRepository_GetAppPermission_Call{Call: _e.mock.On("GetAppPermission", ctx, userID)}
}

func (_c *MockRepository_GetAppPermission_Call) Run(run func(ctx context.Context, userID uuid.UUID)) *MockRepository_GetAppPermission_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *MockRepository_GetAppPermission_Call) Return(_a0 []entities.AppPermission, _a1 error) *MockRepository_GetAppPermission_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_GetAppPermission_Call) RunAndReturn(run func(context.Context, uuid.UUID) ([]entities.AppPermission, error)) *MockRepository_GetAppPermission_Call {
	_c.Call.Return(run)
	return _c
}

// GetDB provides a mock function with given fields: ctx
func (_m *MockRepository) GetDB(ctx context.Context) *gorm.DB {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetDB")
	}

	var r0 *gorm.DB
	if rf, ok := ret.Get(0).(func(context.Context) *gorm.DB); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*gorm.DB)
		}
	}

	return r0
}

// MockRepository_GetDB_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDB'
type MockRepository_GetDB_Call struct {
	*mock.Call
}

// GetDB is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockRepository_Expecter) GetDB(ctx interface{}) *MockRepository_GetDB_Call {
	return &MockRepository_GetDB_Call{Call: _e.mock.On("GetDB", ctx)}
}

func (_c *MockRepository_GetDB_Call) Run(run func(ctx context.Context)) *MockRepository_GetDB_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockRepository_GetDB_Call) Return(_a0 *gorm.DB) *MockRepository_GetDB_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepository_GetDB_Call) RunAndReturn(run func(context.Context) *gorm.DB) *MockRepository_GetDB_Call {
	_c.Call.Return(run)
	return _c
}

// GetEnv provides a mock function with given fields: ctx, repoID
func (_m *MockRepository) GetEnv(ctx context.Context, repoID uuid.UUID) (*entities.RepoEnv, error) {
	ret := _m.Called(ctx, repoID)

	if len(ret) == 0 {
		panic("no return value specified for GetEnv")
	}

	var r0 *entities.RepoEnv
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) (*entities.RepoEnv, error)); ok {
		return rf(ctx, repoID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) *entities.RepoEnv); ok {
		r0 = rf(ctx, repoID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.RepoEnv)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uuid.UUID) error); ok {
		r1 = rf(ctx, repoID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_GetEnv_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetEnv'
type MockRepository_GetEnv_Call struct {
	*mock.Call
}

// GetEnv is a helper method to define mock.On call
//   - ctx context.Context
//   - repoID uuid.UUID
func (_e *MockRepository_Expecter) GetEnv(ctx interface{}, repoID interface{}) *MockRepository_GetEnv_Call {
	return &MockRepository_GetEnv_Call{Call: _e.mock.On("GetEnv", ctx, repoID)}
}

func (_c *MockRepository_GetEnv_Call) Run(run func(ctx context.Context, repoID uuid.UUID)) *MockRepository_GetEnv_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *MockRepository_GetEnv_Call) Return(_a0 *entities.RepoEnv, _a1 error) *MockRepository_GetEnv_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_GetEnv_Call) RunAndReturn(run func(context.Context, uuid.UUID) (*entities.RepoEnv, error)) *MockRepository_GetEnv_Call {
	_c.Call.Return(run)
	return _c
}

// GetOrgPermission provides a mock function with given fields: ctx, userID, orgID
func (_m *MockRepository) GetOrgPermission(ctx context.Context, userID uuid.UUID, orgID uuid.UUID) ([]entities.RepoPermission, error) {
	ret := _m.Called(ctx, userID, orgID)

	if len(ret) == 0 {
		panic("no return value specified for GetOrgPermission")
	}

	var r0 []entities.RepoPermission
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID, uuid.UUID) ([]entities.RepoPermission, error)); ok {
		return rf(ctx, userID, orgID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID, uuid.UUID) []entities.RepoPermission); ok {
		r0 = rf(ctx, userID, orgID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]entities.RepoPermission)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uuid.UUID, uuid.UUID) error); ok {
		r1 = rf(ctx, userID, orgID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_GetOrgPermission_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetOrgPermission'
type MockRepository_GetOrgPermission_Call struct {
	*mock.Call
}

// GetOrgPermission is a helper method to define mock.On call
//   - ctx context.Context
//   - userID uuid.UUID
//   - orgID uuid.UUID
func (_e *MockRepository_Expecter) GetOrgPermission(ctx interface{}, userID interface{}, orgID interface{}) *MockRepository_GetOrgPermission_Call {
	return &MockRepository_GetOrgPermission_Call{Call: _e.mock.On("GetOrgPermission", ctx, userID, orgID)}
}

func (_c *MockRepository_GetOrgPermission_Call) Run(run func(ctx context.Context, userID uuid.UUID, orgID uuid.UUID)) *MockRepository_GetOrgPermission_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID), args[2].(uuid.UUID))
	})
	return _c
}

func (_c *MockRepository_GetOrgPermission_Call) Return(_a0 []entities.RepoPermission, _a1 error) *MockRepository_GetOrgPermission_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_GetOrgPermission_Call) RunAndReturn(run func(context.Context, uuid.UUID, uuid.UUID) ([]entities.RepoPermission, error)) *MockRepository_GetOrgPermission_Call {
	_c.Call.Return(run)
	return _c
}

// GetRepoPermission provides a mock function with given fields: ctx, userID, repoID
func (_m *MockRepository) GetRepoPermission(ctx context.Context, userID uuid.UUID, repoID uuid.UUID) ([]entities.RepoPermission, error) {
	ret := _m.Called(ctx, userID, repoID)

	if len(ret) == 0 {
		panic("no return value specified for GetRepoPermission")
	}

	var r0 []entities.RepoPermission
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID, uuid.UUID) ([]entities.RepoPermission, error)); ok {
		return rf(ctx, userID, repoID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID, uuid.UUID) []entities.RepoPermission); ok {
		r0 = rf(ctx, userID, repoID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]entities.RepoPermission)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uuid.UUID, uuid.UUID) error); ok {
		r1 = rf(ctx, userID, repoID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_GetRepoPermission_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetRepoPermission'
type MockRepository_GetRepoPermission_Call struct {
	*mock.Call
}

// GetRepoPermission is a helper method to define mock.On call
//   - ctx context.Context
//   - userID uuid.UUID
//   - repoID uuid.UUID
func (_e *MockRepository_Expecter) GetRepoPermission(ctx interface{}, userID interface{}, repoID interface{}) *MockRepository_GetRepoPermission_Call {
	return &MockRepository_GetRepoPermission_Call{Call: _e.mock.On("GetRepoPermission", ctx, userID, repoID)}
}

func (_c *MockRepository_GetRepoPermission_Call) Run(run func(ctx context.Context, userID uuid.UUID, repoID uuid.UUID)) *MockRepository_GetRepoPermission_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID), args[2].(uuid.UUID))
	})
	return _c
}

func (_c *MockRepository_GetRepoPermission_Call) Return(_a0 []entities.RepoPermission, _a1 error) *MockRepository_GetRepoPermission_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_GetRepoPermission_Call) RunAndReturn(run func(context.Context, uuid.UUID, uuid.UUID) ([]entities.RepoPermission, error)) *MockRepository_GetRepoPermission_Call {
	_c.Call.Return(run)
	return _c
}

// GetSSHKeyByID provides a mock function with given fields: ctx, id
func (_m *MockRepository) GetSSHKeyByID(ctx context.Context, id uuid.UUID) (*entities.SSHKey, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetSSHKeyByID")
	}

	var r0 *entities.SSHKey
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) (*entities.SSHKey, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) *entities.SSHKey); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.SSHKey)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uuid.UUID) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_GetSSHKeyByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetSSHKeyByID'
type MockRepository_GetSSHKeyByID_Call struct {
	*mock.Call
}

// GetSSHKeyByID is a helper method to define mock.On call
//   - ctx context.Context
//   - id uuid.UUID
func (_e *MockRepository_Expecter) GetSSHKeyByID(ctx interface{}, id interface{}) *MockRepository_GetSSHKeyByID_Call {
	return &MockRepository_GetSSHKeyByID_Call{Call: _e.mock.On("GetSSHKeyByID", ctx, id)}
}

func (_c *MockRepository_GetSSHKeyByID_Call) Run(run func(ctx context.Context, id uuid.UUID)) *MockRepository_GetSSHKeyByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *MockRepository_GetSSHKeyByID_Call) Return(_a0 *entities.SSHKey, _a1 error) *MockRepository_GetSSHKeyByID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_GetSSHKeyByID_Call) RunAndReturn(run func(context.Context, uuid.UUID) (*entities.SSHKey, error)) *MockRepository_GetSSHKeyByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetSignUpRequestByID provides a mock function with given fields: ctx, id
func (_m *MockRepository) GetSignUpRequestByID(ctx context.Context, id uuid.UUID) (*entities.SignupRequest, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetSignUpRequestByID")
	}

	var r0 *entities.SignupRequest
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) (*entities.SignupRequest, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) *entities.SignupRequest); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.SignupRequest)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uuid.UUID) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_GetSignUpRequestByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetSignUpRequestByID'
type MockRepository_GetSignUpRequestByID_Call struct {
	*mock.Call
}

// GetSignUpRequestByID is a helper method to define mock.On call
//   - ctx context.Context
//   - id uuid.UUID
func (_e *MockRepository_Expecter) GetSignUpRequestByID(ctx interface{}, id interface{}) *MockRepository_GetSignUpRequestByID_Call {
	return &MockRepository_GetSignUpRequestByID_Call{Call: _e.mock.On("GetSignUpRequestByID", ctx, id)}
}

func (_c *MockRepository_GetSignUpRequestByID_Call) Run(run func(ctx context.Context, id uuid.UUID)) *MockRepository_GetSignUpRequestByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *MockRepository_GetSignUpRequestByID_Call) Return(_a0 *entities.SignupRequest, _a1 error) *MockRepository_GetSignUpRequestByID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_GetSignUpRequestByID_Call) RunAndReturn(run func(context.Context, uuid.UUID) (*entities.SignupRequest, error)) *MockRepository_GetSignUpRequestByID_Call {
	_c.Call.Return(run)
	return _c
}

// IsDuplicateSSHKey provides a mock function with given fields: ctx, key
func (_m *MockRepository) IsDuplicateSSHKey(ctx context.Context, key string) (bool, error) {
	ret := _m.Called(ctx, key)

	if len(ret) == 0 {
		panic("no return value specified for IsDuplicateSSHKey")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (bool, error)); ok {
		return rf(ctx, key)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) bool); ok {
		r0 = rf(ctx, key)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, key)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_IsDuplicateSSHKey_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'IsDuplicateSSHKey'
type MockRepository_IsDuplicateSSHKey_Call struct {
	*mock.Call
}

// IsDuplicateSSHKey is a helper method to define mock.On call
//   - ctx context.Context
//   - key string
func (_e *MockRepository_Expecter) IsDuplicateSSHKey(ctx interface{}, key interface{}) *MockRepository_IsDuplicateSSHKey_Call {
	return &MockRepository_IsDuplicateSSHKey_Call{Call: _e.mock.On("IsDuplicateSSHKey", ctx, key)}
}

func (_c *MockRepository_IsDuplicateSSHKey_Call) Run(run func(ctx context.Context, key string)) *MockRepository_IsDuplicateSSHKey_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockRepository_IsDuplicateSSHKey_Call) Return(_a0 bool, _a1 error) *MockRepository_IsDuplicateSSHKey_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_IsDuplicateSSHKey_Call) RunAndReturn(run func(context.Context, string) (bool, error)) *MockRepository_IsDuplicateSSHKey_Call {
	_c.Call.Return(run)
	return _c
}

// ListAccessToken provides a mock function with given fields: ctx, pagination, order, in
func (_m *MockRepository) ListAccessToken(ctx context.Context, pagination types.Pagination, order types.OrderBy, in repository.ListAccessTokenQuery) ([]entities.UserToken, error) {
	ret := _m.Called(ctx, pagination, order, in)

	if len(ret) == 0 {
		panic("no return value specified for ListAccessToken")
	}

	var r0 []entities.UserToken
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, types.Pagination, types.OrderBy, repository.ListAccessTokenQuery) ([]entities.UserToken, error)); ok {
		return rf(ctx, pagination, order, in)
	}
	if rf, ok := ret.Get(0).(func(context.Context, types.Pagination, types.OrderBy, repository.ListAccessTokenQuery) []entities.UserToken); ok {
		r0 = rf(ctx, pagination, order, in)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]entities.UserToken)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, types.Pagination, types.OrderBy, repository.ListAccessTokenQuery) error); ok {
		r1 = rf(ctx, pagination, order, in)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_ListAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListAccessToken'
type MockRepository_ListAccessToken_Call struct {
	*mock.Call
}

// ListAccessToken is a helper method to define mock.On call
//   - ctx context.Context
//   - pagination types.Pagination
//   - order types.OrderBy
//   - in repository.ListAccessTokenQuery
func (_e *MockRepository_Expecter) ListAccessToken(ctx interface{}, pagination interface{}, order interface{}, in interface{}) *MockRepository_ListAccessToken_Call {
	return &MockRepository_ListAccessToken_Call{Call: _e.mock.On("ListAccessToken", ctx, pagination, order, in)}
}

func (_c *MockRepository_ListAccessToken_Call) Run(run func(ctx context.Context, pagination types.Pagination, order types.OrderBy, in repository.ListAccessTokenQuery)) *MockRepository_ListAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(types.Pagination), args[2].(types.OrderBy), args[3].(repository.ListAccessTokenQuery))
	})
	return _c
}

func (_c *MockRepository_ListAccessToken_Call) Return(_a0 []entities.UserToken, _a1 error) *MockRepository_ListAccessToken_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_ListAccessToken_Call) RunAndReturn(run func(context.Context, types.Pagination, types.OrderBy, repository.ListAccessTokenQuery) ([]entities.UserToken, error)) *MockRepository_ListAccessToken_Call {
	_c.Call.Return(run)
	return _c
}

// ListDeployment provides a mock function with given fields: ctx, pagination, order, in
func (_m *MockRepository) ListDeployment(ctx context.Context, pagination types.Pagination, order types.OrderBy, in repository.ListDeploymentInput) ([]entities.Deployment, error) {
	ret := _m.Called(ctx, pagination, order, in)

	if len(ret) == 0 {
		panic("no return value specified for ListDeployment")
	}

	var r0 []entities.Deployment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, types.Pagination, types.OrderBy, repository.ListDeploymentInput) ([]entities.Deployment, error)); ok {
		return rf(ctx, pagination, order, in)
	}
	if rf, ok := ret.Get(0).(func(context.Context, types.Pagination, types.OrderBy, repository.ListDeploymentInput) []entities.Deployment); ok {
		r0 = rf(ctx, pagination, order, in)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]entities.Deployment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, types.Pagination, types.OrderBy, repository.ListDeploymentInput) error); ok {
		r1 = rf(ctx, pagination, order, in)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_ListDeployment_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListDeployment'
type MockRepository_ListDeployment_Call struct {
	*mock.Call
}

// ListDeployment is a helper method to define mock.On call
//   - ctx context.Context
//   - pagination types.Pagination
//   - order types.OrderBy
//   - in repository.ListDeploymentInput
func (_e *MockRepository_Expecter) ListDeployment(ctx interface{}, pagination interface{}, order interface{}, in interface{}) *MockRepository_ListDeployment_Call {
	return &MockRepository_ListDeployment_Call{Call: _e.mock.On("ListDeployment", ctx, pagination, order, in)}
}

func (_c *MockRepository_ListDeployment_Call) Run(run func(ctx context.Context, pagination types.Pagination, order types.OrderBy, in repository.ListDeploymentInput)) *MockRepository_ListDeployment_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(types.Pagination), args[2].(types.OrderBy), args[3].(repository.ListDeploymentInput))
	})
	return _c
}

func (_c *MockRepository_ListDeployment_Call) Return(_a0 []entities.Deployment, _a1 error) *MockRepository_ListDeployment_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_ListDeployment_Call) RunAndReturn(run func(context.Context, types.Pagination, types.OrderBy, repository.ListDeploymentInput) ([]entities.Deployment, error)) *MockRepository_ListDeployment_Call {
	_c.Call.Return(run)
	return _c
}

// ListECRDeployment provides a mock function with given fields: ctx, pagination, order, in
func (_m *MockRepository) ListECRDeployment(ctx context.Context, pagination types.Pagination, order types.OrderBy, in repository.ListECRDeploymentInput) ([]entities.CustomImageDeployment, int64, error) {
	ret := _m.Called(ctx, pagination, order, in)

	if len(ret) == 0 {
		panic("no return value specified for ListECRDeployment")
	}

	var r0 []entities.CustomImageDeployment
	var r1 int64
	var r2 error
	if rf, ok := ret.Get(0).(func(context.Context, types.Pagination, types.OrderBy, repository.ListECRDeploymentInput) ([]entities.CustomImageDeployment, int64, error)); ok {
		return rf(ctx, pagination, order, in)
	}
	if rf, ok := ret.Get(0).(func(context.Context, types.Pagination, types.OrderBy, repository.ListECRDeploymentInput) []entities.CustomImageDeployment); ok {
		r0 = rf(ctx, pagination, order, in)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]entities.CustomImageDeployment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, types.Pagination, types.OrderBy, repository.ListECRDeploymentInput) int64); ok {
		r1 = rf(ctx, pagination, order, in)
	} else {
		r1 = ret.Get(1).(int64)
	}

	if rf, ok := ret.Get(2).(func(context.Context, types.Pagination, types.OrderBy, repository.ListECRDeploymentInput) error); ok {
		r2 = rf(ctx, pagination, order, in)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// MockRepository_ListECRDeployment_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListECRDeployment'
type MockRepository_ListECRDeployment_Call struct {
	*mock.Call
}

// ListECRDeployment is a helper method to define mock.On call
//   - ctx context.Context
//   - pagination types.Pagination
//   - order types.OrderBy
//   - in repository.ListECRDeploymentInput
func (_e *MockRepository_Expecter) ListECRDeployment(ctx interface{}, pagination interface{}, order interface{}, in interface{}) *MockRepository_ListECRDeployment_Call {
	return &MockRepository_ListECRDeployment_Call{Call: _e.mock.On("ListECRDeployment", ctx, pagination, order, in)}
}

func (_c *MockRepository_ListECRDeployment_Call) Run(run func(ctx context.Context, pagination types.Pagination, order types.OrderBy, in repository.ListECRDeploymentInput)) *MockRepository_ListECRDeployment_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(types.Pagination), args[2].(types.OrderBy), args[3].(repository.ListECRDeploymentInput))
	})
	return _c
}

func (_c *MockRepository_ListECRDeployment_Call) Return(_a0 []entities.CustomImageDeployment, _a1 int64, _a2 error) *MockRepository_ListECRDeployment_Call {
	_c.Call.Return(_a0, _a1, _a2)
	return _c
}

func (_c *MockRepository_ListECRDeployment_Call) RunAndReturn(run func(context.Context, types.Pagination, types.OrderBy, repository.ListECRDeploymentInput) ([]entities.CustomImageDeployment, int64, error)) *MockRepository_ListECRDeployment_Call {
	_c.Call.Return(run)
	return _c
}

// ListHardware provides a mock function with given fields: ctx, pagination, order
func (_m *MockRepository) ListHardware(ctx context.Context, pagination types.Pagination, order types.OrderBy) ([]entities.Hardware, error) {
	ret := _m.Called(ctx, pagination, order)

	if len(ret) == 0 {
		panic("no return value specified for ListHardware")
	}

	var r0 []entities.Hardware
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, types.Pagination, types.OrderBy) ([]entities.Hardware, error)); ok {
		return rf(ctx, pagination, order)
	}
	if rf, ok := ret.Get(0).(func(context.Context, types.Pagination, types.OrderBy) []entities.Hardware); ok {
		r0 = rf(ctx, pagination, order)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]entities.Hardware)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, types.Pagination, types.OrderBy) error); ok {
		r1 = rf(ctx, pagination, order)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_ListHardware_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListHardware'
type MockRepository_ListHardware_Call struct {
	*mock.Call
}

// ListHardware is a helper method to define mock.On call
//   - ctx context.Context
//   - pagination types.Pagination
//   - order types.OrderBy
func (_e *MockRepository_Expecter) ListHardware(ctx interface{}, pagination interface{}, order interface{}) *MockRepository_ListHardware_Call {
	return &MockRepository_ListHardware_Call{Call: _e.mock.On("ListHardware", ctx, pagination, order)}
}

func (_c *MockRepository_ListHardware_Call) Run(run func(ctx context.Context, pagination types.Pagination, order types.OrderBy)) *MockRepository_ListHardware_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(types.Pagination), args[2].(types.OrderBy))
	})
	return _c
}

func (_c *MockRepository_ListHardware_Call) Return(_a0 []entities.Hardware, _a1 error) *MockRepository_ListHardware_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_ListHardware_Call) RunAndReturn(run func(context.Context, types.Pagination, types.OrderBy) ([]entities.Hardware, error)) *MockRepository_ListHardware_Call {
	_c.Call.Return(run)
	return _c
}

// ListMembers provides a mock function with given fields: ctx, input
func (_m *MockRepository) ListMembers(ctx context.Context, input dto.ListOrgMembersInput) ([]entities.OrgMemberInfo, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for ListMembers")
	}

	var r0 []entities.OrgMemberInfo
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.ListOrgMembersInput) ([]entities.OrgMemberInfo, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, dto.ListOrgMembersInput) []entities.OrgMemberInfo); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]entities.OrgMemberInfo)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, dto.ListOrgMembersInput) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_ListMembers_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListMembers'
type MockRepository_ListMembers_Call struct {
	*mock.Call
}

// ListMembers is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.ListOrgMembersInput
func (_e *MockRepository_Expecter) ListMembers(ctx interface{}, input interface{}) *MockRepository_ListMembers_Call {
	return &MockRepository_ListMembers_Call{Call: _e.mock.On("ListMembers", ctx, input)}
}

func (_c *MockRepository_ListMembers_Call) Run(run func(ctx context.Context, input dto.ListOrgMembersInput)) *MockRepository_ListMembers_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.ListOrgMembersInput))
	})
	return _c
}

func (_c *MockRepository_ListMembers_Call) Return(_a0 []entities.OrgMemberInfo, _a1 error) *MockRepository_ListMembers_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_ListMembers_Call) RunAndReturn(run func(context.Context, dto.ListOrgMembersInput) ([]entities.OrgMemberInfo, error)) *MockRepository_ListMembers_Call {
	_c.Call.Return(run)
	return _c
}

// ListOrgAccessToken provides a mock function with given fields: ctx, pagination, order, in
func (_m *MockRepository) ListOrgAccessToken(ctx context.Context, pagination types.Pagination, order types.OrderBy, in repository.ListOrgAccessTokenQuery) ([]entities.OrgToken, error) {
	ret := _m.Called(ctx, pagination, order, in)

	if len(ret) == 0 {
		panic("no return value specified for ListOrgAccessToken")
	}

	var r0 []entities.OrgToken
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, types.Pagination, types.OrderBy, repository.ListOrgAccessTokenQuery) ([]entities.OrgToken, error)); ok {
		return rf(ctx, pagination, order, in)
	}
	if rf, ok := ret.Get(0).(func(context.Context, types.Pagination, types.OrderBy, repository.ListOrgAccessTokenQuery) []entities.OrgToken); ok {
		r0 = rf(ctx, pagination, order, in)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]entities.OrgToken)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, types.Pagination, types.OrderBy, repository.ListOrgAccessTokenQuery) error); ok {
		r1 = rf(ctx, pagination, order, in)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_ListOrgAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListOrgAccessToken'
type MockRepository_ListOrgAccessToken_Call struct {
	*mock.Call
}

// ListOrgAccessToken is a helper method to define mock.On call
//   - ctx context.Context
//   - pagination types.Pagination
//   - order types.OrderBy
//   - in repository.ListOrgAccessTokenQuery
func (_e *MockRepository_Expecter) ListOrgAccessToken(ctx interface{}, pagination interface{}, order interface{}, in interface{}) *MockRepository_ListOrgAccessToken_Call {
	return &MockRepository_ListOrgAccessToken_Call{Call: _e.mock.On("ListOrgAccessToken", ctx, pagination, order, in)}
}

func (_c *MockRepository_ListOrgAccessToken_Call) Run(run func(ctx context.Context, pagination types.Pagination, order types.OrderBy, in repository.ListOrgAccessTokenQuery)) *MockRepository_ListOrgAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(types.Pagination), args[2].(types.OrderBy), args[3].(repository.ListOrgAccessTokenQuery))
	})
	return _c
}

func (_c *MockRepository_ListOrgAccessToken_Call) Return(_a0 []entities.OrgToken, _a1 error) *MockRepository_ListOrgAccessToken_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_ListOrgAccessToken_Call) RunAndReturn(run func(context.Context, types.Pagination, types.OrderBy, repository.ListOrgAccessTokenQuery) ([]entities.OrgToken, error)) *MockRepository_ListOrgAccessToken_Call {
	_c.Call.Return(run)
	return _c
}

// ListOrganizations provides a mock function with given fields: ctx, input
func (_m *MockRepository) ListOrganizations(ctx context.Context, input dto.ListOrganizationsInput) (int, []entities.Organization, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for ListOrganizations")
	}

	var r0 int
	var r1 []entities.Organization
	var r2 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.ListOrganizationsInput) (int, []entities.Organization, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, dto.ListOrganizationsInput) int); ok {
		r0 = rf(ctx, input)
	} else {
		r0 = ret.Get(0).(int)
	}

	if rf, ok := ret.Get(1).(func(context.Context, dto.ListOrganizationsInput) []entities.Organization); ok {
		r1 = rf(ctx, input)
	} else {
		if ret.Get(1) != nil {
			r1 = ret.Get(1).([]entities.Organization)
		}
	}

	if rf, ok := ret.Get(2).(func(context.Context, dto.ListOrganizationsInput) error); ok {
		r2 = rf(ctx, input)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// MockRepository_ListOrganizations_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListOrganizations'
type MockRepository_ListOrganizations_Call struct {
	*mock.Call
}

// ListOrganizations is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.ListOrganizationsInput
func (_e *MockRepository_Expecter) ListOrganizations(ctx interface{}, input interface{}) *MockRepository_ListOrganizations_Call {
	return &MockRepository_ListOrganizations_Call{Call: _e.mock.On("ListOrganizations", ctx, input)}
}

func (_c *MockRepository_ListOrganizations_Call) Run(run func(ctx context.Context, input dto.ListOrganizationsInput)) *MockRepository_ListOrganizations_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.ListOrganizationsInput))
	})
	return _c
}

func (_c *MockRepository_ListOrganizations_Call) Return(_a0 int, _a1 []entities.Organization, _a2 error) *MockRepository_ListOrganizations_Call {
	_c.Call.Return(_a0, _a1, _a2)
	return _c
}

func (_c *MockRepository_ListOrganizations_Call) RunAndReturn(run func(context.Context, dto.ListOrganizationsInput) (int, []entities.Organization, error)) *MockRepository_ListOrganizations_Call {
	_c.Call.Return(run)
	return _c
}

// ListRepoAccessToken provides a mock function with given fields: ctx, pagination, order, in
func (_m *MockRepository) ListRepoAccessToken(ctx context.Context, pagination types.Pagination, order types.OrderBy, in repository.ListRepoAccessTokenQuery) ([]entities.RepoAccessToken, error) {
	ret := _m.Called(ctx, pagination, order, in)

	if len(ret) == 0 {
		panic("no return value specified for ListRepoAccessToken")
	}

	var r0 []entities.RepoAccessToken
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, types.Pagination, types.OrderBy, repository.ListRepoAccessTokenQuery) ([]entities.RepoAccessToken, error)); ok {
		return rf(ctx, pagination, order, in)
	}
	if rf, ok := ret.Get(0).(func(context.Context, types.Pagination, types.OrderBy, repository.ListRepoAccessTokenQuery) []entities.RepoAccessToken); ok {
		r0 = rf(ctx, pagination, order, in)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]entities.RepoAccessToken)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, types.Pagination, types.OrderBy, repository.ListRepoAccessTokenQuery) error); ok {
		r1 = rf(ctx, pagination, order, in)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_ListRepoAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListRepoAccessToken'
type MockRepository_ListRepoAccessToken_Call struct {
	*mock.Call
}

// ListRepoAccessToken is a helper method to define mock.On call
//   - ctx context.Context
//   - pagination types.Pagination
//   - order types.OrderBy
//   - in repository.ListRepoAccessTokenQuery
func (_e *MockRepository_Expecter) ListRepoAccessToken(ctx interface{}, pagination interface{}, order interface{}, in interface{}) *MockRepository_ListRepoAccessToken_Call {
	return &MockRepository_ListRepoAccessToken_Call{Call: _e.mock.On("ListRepoAccessToken", ctx, pagination, order, in)}
}

func (_c *MockRepository_ListRepoAccessToken_Call) Run(run func(ctx context.Context, pagination types.Pagination, order types.OrderBy, in repository.ListRepoAccessTokenQuery)) *MockRepository_ListRepoAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(types.Pagination), args[2].(types.OrderBy), args[3].(repository.ListRepoAccessTokenQuery))
	})
	return _c
}

func (_c *MockRepository_ListRepoAccessToken_Call) Return(_a0 []entities.RepoAccessToken, _a1 error) *MockRepository_ListRepoAccessToken_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_ListRepoAccessToken_Call) RunAndReturn(run func(context.Context, types.Pagination, types.OrderBy, repository.ListRepoAccessTokenQuery) ([]entities.RepoAccessToken, error)) *MockRepository_ListRepoAccessToken_Call {
	_c.Call.Return(run)
	return _c
}

// ListRepoMembers provides a mock function with given fields: ctx, pagination, order, query
func (_m *MockRepository) ListRepoMembers(ctx context.Context, pagination types.Pagination, order types.OrderBy, query repository.ListRepositoryMembersQuery) ([]entities.RepoMemberInfo, error) {
	ret := _m.Called(ctx, pagination, order, query)

	if len(ret) == 0 {
		panic("no return value specified for ListRepoMembers")
	}

	var r0 []entities.RepoMemberInfo
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, types.Pagination, types.OrderBy, repository.ListRepositoryMembersQuery) ([]entities.RepoMemberInfo, error)); ok {
		return rf(ctx, pagination, order, query)
	}
	if rf, ok := ret.Get(0).(func(context.Context, types.Pagination, types.OrderBy, repository.ListRepositoryMembersQuery) []entities.RepoMemberInfo); ok {
		r0 = rf(ctx, pagination, order, query)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]entities.RepoMemberInfo)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, types.Pagination, types.OrderBy, repository.ListRepositoryMembersQuery) error); ok {
		r1 = rf(ctx, pagination, order, query)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_ListRepoMembers_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListRepoMembers'
type MockRepository_ListRepoMembers_Call struct {
	*mock.Call
}

// ListRepoMembers is a helper method to define mock.On call
//   - ctx context.Context
//   - pagination types.Pagination
//   - order types.OrderBy
//   - query repository.ListRepositoryMembersQuery
func (_e *MockRepository_Expecter) ListRepoMembers(ctx interface{}, pagination interface{}, order interface{}, query interface{}) *MockRepository_ListRepoMembers_Call {
	return &MockRepository_ListRepoMembers_Call{Call: _e.mock.On("ListRepoMembers", ctx, pagination, order, query)}
}

func (_c *MockRepository_ListRepoMembers_Call) Run(run func(ctx context.Context, pagination types.Pagination, order types.OrderBy, query repository.ListRepositoryMembersQuery)) *MockRepository_ListRepoMembers_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(types.Pagination), args[2].(types.OrderBy), args[3].(repository.ListRepositoryMembersQuery))
	})
	return _c
}

func (_c *MockRepository_ListRepoMembers_Call) Return(_a0 []entities.RepoMemberInfo, _a1 error) *MockRepository_ListRepoMembers_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_ListRepoMembers_Call) RunAndReturn(run func(context.Context, types.Pagination, types.OrderBy, repository.ListRepositoryMembersQuery) ([]entities.RepoMemberInfo, error)) *MockRepository_ListRepoMembers_Call {
	_c.Call.Return(run)
	return _c
}

// ListRepoTags provides a mock function with given fields: ctx, input
func (_m *MockRepository) ListRepoTags(ctx context.Context, input dto.ListRepoTagsInput) ([]entities.Tag, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for ListRepoTags")
	}

	var r0 []entities.Tag
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.ListRepoTagsInput) ([]entities.Tag, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, dto.ListRepoTagsInput) []entities.Tag); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]entities.Tag)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, dto.ListRepoTagsInput) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_ListRepoTags_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListRepoTags'
type MockRepository_ListRepoTags_Call struct {
	*mock.Call
}

// ListRepoTags is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.ListRepoTagsInput
func (_e *MockRepository_Expecter) ListRepoTags(ctx interface{}, input interface{}) *MockRepository_ListRepoTags_Call {
	return &MockRepository_ListRepoTags_Call{Call: _e.mock.On("ListRepoTags", ctx, input)}
}

func (_c *MockRepository_ListRepoTags_Call) Run(run func(ctx context.Context, input dto.ListRepoTagsInput)) *MockRepository_ListRepoTags_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.ListRepoTagsInput))
	})
	return _c
}

func (_c *MockRepository_ListRepoTags_Call) Return(_a0 []entities.Tag, _a1 error) *MockRepository_ListRepoTags_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_ListRepoTags_Call) RunAndReturn(run func(context.Context, dto.ListRepoTagsInput) ([]entities.Tag, error)) *MockRepository_ListRepoTags_Call {
	_c.Call.Return(run)
	return _c
}

// ListRepoTagsByQueryModel provides a mock function with given fields: ctx, input
func (_m *MockRepository) ListRepoTagsByQueryModel(ctx context.Context, input interface{}) ([]entities.Tag, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for ListRepoTagsByQueryModel")
	}

	var r0 []entities.Tag
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, interface{}) ([]entities.Tag, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, interface{}) []entities.Tag); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]entities.Tag)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, interface{}) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_ListRepoTagsByQueryModel_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListRepoTagsByQueryModel'
type MockRepository_ListRepoTagsByQueryModel_Call struct {
	*mock.Call
}

// ListRepoTagsByQueryModel is a helper method to define mock.On call
//   - ctx context.Context
//   - input interface{}
func (_e *MockRepository_Expecter) ListRepoTagsByQueryModel(ctx interface{}, input interface{}) *MockRepository_ListRepoTagsByQueryModel_Call {
	return &MockRepository_ListRepoTagsByQueryModel_Call{Call: _e.mock.On("ListRepoTagsByQueryModel", ctx, input)}
}

func (_c *MockRepository_ListRepoTagsByQueryModel_Call) Run(run func(ctx context.Context, input interface{})) *MockRepository_ListRepoTagsByQueryModel_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(interface{}))
	})
	return _c
}

func (_c *MockRepository_ListRepoTagsByQueryModel_Call) Return(_a0 []entities.Tag, _a1 error) *MockRepository_ListRepoTagsByQueryModel_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_ListRepoTagsByQueryModel_Call) RunAndReturn(run func(context.Context, interface{}) ([]entities.Tag, error)) *MockRepository_ListRepoTagsByQueryModel_Call {
	_c.Call.Return(run)
	return _c
}

// ListRepositories provides a mock function with given fields: ctx, input
func (_m *MockRepository) ListRepositories(ctx context.Context, input dto.GetRepositoriesInput) ([]entities.Repository, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for ListRepositories")
	}

	var r0 []entities.Repository
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.GetRepositoriesInput) ([]entities.Repository, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, dto.GetRepositoriesInput) []entities.Repository); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]entities.Repository)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, dto.GetRepositoriesInput) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_ListRepositories_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListRepositories'
type MockRepository_ListRepositories_Call struct {
	*mock.Call
}

// ListRepositories is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.GetRepositoriesInput
func (_e *MockRepository_Expecter) ListRepositories(ctx interface{}, input interface{}) *MockRepository_ListRepositories_Call {
	return &MockRepository_ListRepositories_Call{Call: _e.mock.On("ListRepositories", ctx, input)}
}

func (_c *MockRepository_ListRepositories_Call) Run(run func(ctx context.Context, input dto.GetRepositoriesInput)) *MockRepository_ListRepositories_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.GetRepositoriesInput))
	})
	return _c
}

func (_c *MockRepository_ListRepositories_Call) Return(_a0 []entities.Repository, _a1 error) *MockRepository_ListRepositories_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_ListRepositories_Call) RunAndReturn(run func(context.Context, dto.GetRepositoriesInput) ([]entities.Repository, error)) *MockRepository_ListRepositories_Call {
	_c.Call.Return(run)
	return _c
}

// ListSSHKeys provides a mock function with given fields: ctx, pageNo, pageSize, order, query
func (_m *MockRepository) ListSSHKeys(ctx context.Context, pageNo int, pageSize int, order types.OrderBy, query repository.GetSSHKeyQuery) ([]entities.SSHKey, error) {
	ret := _m.Called(ctx, pageNo, pageSize, order, query)

	if len(ret) == 0 {
		panic("no return value specified for ListSSHKeys")
	}

	var r0 []entities.SSHKey
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int, int, types.OrderBy, repository.GetSSHKeyQuery) ([]entities.SSHKey, error)); ok {
		return rf(ctx, pageNo, pageSize, order, query)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int, int, types.OrderBy, repository.GetSSHKeyQuery) []entities.SSHKey); ok {
		r0 = rf(ctx, pageNo, pageSize, order, query)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]entities.SSHKey)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int, int, types.OrderBy, repository.GetSSHKeyQuery) error); ok {
		r1 = rf(ctx, pageNo, pageSize, order, query)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_ListSSHKeys_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListSSHKeys'
type MockRepository_ListSSHKeys_Call struct {
	*mock.Call
}

// ListSSHKeys is a helper method to define mock.On call
//   - ctx context.Context
//   - pageNo int
//   - pageSize int
//   - order types.OrderBy
//   - query repository.GetSSHKeyQuery
func (_e *MockRepository_Expecter) ListSSHKeys(ctx interface{}, pageNo interface{}, pageSize interface{}, order interface{}, query interface{}) *MockRepository_ListSSHKeys_Call {
	return &MockRepository_ListSSHKeys_Call{Call: _e.mock.On("ListSSHKeys", ctx, pageNo, pageSize, order, query)}
}

func (_c *MockRepository_ListSSHKeys_Call) Run(run func(ctx context.Context, pageNo int, pageSize int, order types.OrderBy, query repository.GetSSHKeyQuery)) *MockRepository_ListSSHKeys_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int), args[2].(int), args[3].(types.OrderBy), args[4].(repository.GetSSHKeyQuery))
	})
	return _c
}

func (_c *MockRepository_ListSSHKeys_Call) Return(_a0 []entities.SSHKey, _a1 error) *MockRepository_ListSSHKeys_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_ListSSHKeys_Call) RunAndReturn(run func(context.Context, int, int, types.OrderBy, repository.GetSSHKeyQuery) ([]entities.SSHKey, error)) *MockRepository_ListSSHKeys_Call {
	_c.Call.Return(run)
	return _c
}

// ListSignUpRequests provides a mock function with given fields: ctx
func (_m *MockRepository) ListSignUpRequests(ctx context.Context) ([]entities.SignupRequest, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for ListSignUpRequests")
	}

	var r0 []entities.SignupRequest
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) ([]entities.SignupRequest, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) []entities.SignupRequest); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]entities.SignupRequest)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_ListSignUpRequests_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListSignUpRequests'
type MockRepository_ListSignUpRequests_Call struct {
	*mock.Call
}

// ListSignUpRequests is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockRepository_Expecter) ListSignUpRequests(ctx interface{}) *MockRepository_ListSignUpRequests_Call {
	return &MockRepository_ListSignUpRequests_Call{Call: _e.mock.On("ListSignUpRequests", ctx)}
}

func (_c *MockRepository_ListSignUpRequests_Call) Run(run func(ctx context.Context)) *MockRepository_ListSignUpRequests_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockRepository_ListSignUpRequests_Call) Return(_a0 []entities.SignupRequest, _a1 error) *MockRepository_ListSignUpRequests_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_ListSignUpRequests_Call) RunAndReturn(run func(context.Context) ([]entities.SignupRequest, error)) *MockRepository_ListSignUpRequests_Call {
	_c.Call.Return(run)
	return _c
}

// ListTemplates provides a mock function with given fields: ctx
func (_m *MockRepository) ListTemplates(ctx context.Context) ([]entities.RepoTemplate, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for ListTemplates")
	}

	var r0 []entities.RepoTemplate
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) ([]entities.RepoTemplate, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) []entities.RepoTemplate); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]entities.RepoTemplate)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_ListTemplates_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListTemplates'
type MockRepository_ListTemplates_Call struct {
	*mock.Call
}

// ListTemplates is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockRepository_Expecter) ListTemplates(ctx interface{}) *MockRepository_ListTemplates_Call {
	return &MockRepository_ListTemplates_Call{Call: _e.mock.On("ListTemplates", ctx)}
}

func (_c *MockRepository_ListTemplates_Call) Run(run func(ctx context.Context)) *MockRepository_ListTemplates_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockRepository_ListTemplates_Call) Return(_a0 []entities.RepoTemplate, _a1 error) *MockRepository_ListTemplates_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_ListTemplates_Call) RunAndReturn(run func(context.Context) ([]entities.RepoTemplate, error)) *MockRepository_ListTemplates_Call {
	_c.Call.Return(run)
	return _c
}

// ListUsers provides a mock function with given fields: ctx, input
func (_m *MockRepository) ListUsers(ctx context.Context, input repository.ListUsersInput) ([]entities.User, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for ListUsers")
	}

	var r0 []entities.User
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.ListUsersInput) ([]entities.User, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, repository.ListUsersInput) []entities.User); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]entities.User)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, repository.ListUsersInput) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_ListUsers_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListUsers'
type MockRepository_ListUsers_Call struct {
	*mock.Call
}

// ListUsers is a helper method to define mock.On call
//   - ctx context.Context
//   - input repository.ListUsersInput
func (_e *MockRepository_Expecter) ListUsers(ctx interface{}, input interface{}) *MockRepository_ListUsers_Call {
	return &MockRepository_ListUsers_Call{Call: _e.mock.On("ListUsers", ctx, input)}
}

func (_c *MockRepository_ListUsers_Call) Run(run func(ctx context.Context, input repository.ListUsersInput)) *MockRepository_ListUsers_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.ListUsersInput))
	})
	return _c
}

func (_c *MockRepository_ListUsers_Call) Return(_a0 []entities.User, _a1 error) *MockRepository_ListUsers_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_ListUsers_Call) RunAndReturn(run func(context.Context, repository.ListUsersInput) ([]entities.User, error)) *MockRepository_ListUsers_Call {
	_c.Call.Return(run)
	return _c
}

// RemoveUserInDeployments provides a mock function with given fields: ctx, in
func (_m *MockRepository) RemoveUserInDeployments(ctx context.Context, in repository.RemoveUserInDeploymentsInput) error {
	ret := _m.Called(ctx, in)

	if len(ret) == 0 {
		panic("no return value specified for RemoveUserInDeployments")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.RemoveUserInDeploymentsInput) error); ok {
		r0 = rf(ctx, in)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepository_RemoveUserInDeployments_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RemoveUserInDeployments'
type MockRepository_RemoveUserInDeployments_Call struct {
	*mock.Call
}

// RemoveUserInDeployments is a helper method to define mock.On call
//   - ctx context.Context
//   - in repository.RemoveUserInDeploymentsInput
func (_e *MockRepository_Expecter) RemoveUserInDeployments(ctx interface{}, in interface{}) *MockRepository_RemoveUserInDeployments_Call {
	return &MockRepository_RemoveUserInDeployments_Call{Call: _e.mock.On("RemoveUserInDeployments", ctx, in)}
}

func (_c *MockRepository_RemoveUserInDeployments_Call) Run(run func(ctx context.Context, in repository.RemoveUserInDeploymentsInput)) *MockRepository_RemoveUserInDeployments_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.RemoveUserInDeploymentsInput))
	})
	return _c
}

func (_c *MockRepository_RemoveUserInDeployments_Call) Return(_a0 error) *MockRepository_RemoveUserInDeployments_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepository_RemoveUserInDeployments_Call) RunAndReturn(run func(context.Context, repository.RemoveUserInDeploymentsInput) error) *MockRepository_RemoveUserInDeployments_Call {
	_c.Call.Return(run)
	return _c
}

// RemoveUserInRepository provides a mock function with given fields: ctx, in
func (_m *MockRepository) RemoveUserInRepository(ctx context.Context, in repository.RemoveUserInRepositoryInput) error {
	ret := _m.Called(ctx, in)

	if len(ret) == 0 {
		panic("no return value specified for RemoveUserInRepository")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.RemoveUserInRepositoryInput) error); ok {
		r0 = rf(ctx, in)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepository_RemoveUserInRepository_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RemoveUserInRepository'
type MockRepository_RemoveUserInRepository_Call struct {
	*mock.Call
}

// RemoveUserInRepository is a helper method to define mock.On call
//   - ctx context.Context
//   - in repository.RemoveUserInRepositoryInput
func (_e *MockRepository_Expecter) RemoveUserInRepository(ctx interface{}, in interface{}) *MockRepository_RemoveUserInRepository_Call {
	return &MockRepository_RemoveUserInRepository_Call{Call: _e.mock.On("RemoveUserInRepository", ctx, in)}
}

func (_c *MockRepository_RemoveUserInRepository_Call) Run(run func(ctx context.Context, in repository.RemoveUserInRepositoryInput)) *MockRepository_RemoveUserInRepository_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.RemoveUserInRepositoryInput))
	})
	return _c
}

func (_c *MockRepository_RemoveUserInRepository_Call) Return(_a0 error) *MockRepository_RemoveUserInRepository_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepository_RemoveUserInRepository_Call) RunAndReturn(run func(context.Context, repository.RemoveUserInRepositoryInput) error) *MockRepository_RemoveUserInRepository_Call {
	_c.Call.Return(run)
	return _c
}

// RollbackTransaction provides a mock function with given fields: ctx
func (_m *MockRepository) RollbackTransaction(ctx context.Context) *gorm.DB {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for RollbackTransaction")
	}

	var r0 *gorm.DB
	if rf, ok := ret.Get(0).(func(context.Context) *gorm.DB); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*gorm.DB)
		}
	}

	return r0
}

// MockRepository_RollbackTransaction_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RollbackTransaction'
type MockRepository_RollbackTransaction_Call struct {
	*mock.Call
}

// RollbackTransaction is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockRepository_Expecter) RollbackTransaction(ctx interface{}) *MockRepository_RollbackTransaction_Call {
	return &MockRepository_RollbackTransaction_Call{Call: _e.mock.On("RollbackTransaction", ctx)}
}

func (_c *MockRepository_RollbackTransaction_Call) Run(run func(ctx context.Context)) *MockRepository_RollbackTransaction_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockRepository_RollbackTransaction_Call) Return(_a0 *gorm.DB) *MockRepository_RollbackTransaction_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepository_RollbackTransaction_Call) RunAndReturn(run func(context.Context) *gorm.DB) *MockRepository_RollbackTransaction_Call {
	_c.Call.Return(run)
	return _c
}

// Save provides a mock function with given fields: ctx, _a1
func (_m *MockRepository) Save(ctx context.Context, _a1 interface{}) error {
	ret := _m.Called(ctx, _a1)

	if len(ret) == 0 {
		panic("no return value specified for Save")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, interface{}) error); ok {
		r0 = rf(ctx, _a1)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepository_Save_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Save'
type MockRepository_Save_Call struct {
	*mock.Call
}

// Save is a helper method to define mock.On call
//   - ctx context.Context
//   - _a1 interface{}
func (_e *MockRepository_Expecter) Save(ctx interface{}, _a1 interface{}) *MockRepository_Save_Call {
	return &MockRepository_Save_Call{Call: _e.mock.On("Save", ctx, _a1)}
}

func (_c *MockRepository_Save_Call) Run(run func(ctx context.Context, _a1 interface{})) *MockRepository_Save_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(interface{}))
	})
	return _c
}

func (_c *MockRepository_Save_Call) Return(_a0 error) *MockRepository_Save_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepository_Save_Call) RunAndReturn(run func(context.Context, interface{}) error) *MockRepository_Save_Call {
	_c.Call.Return(run)
	return _c
}

// Transaction provides a mock function with given fields: ctx, callback, opts
func (_m *MockRepository) Transaction(ctx context.Context, callback func(context.Context) error, opts ...*sql.TxOptions) error {
	_va := make([]interface{}, len(opts))
	for _i := range opts {
		_va[_i] = opts[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, callback)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for Transaction")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, func(context.Context) error, ...*sql.TxOptions) error); ok {
		r0 = rf(ctx, callback, opts...)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepository_Transaction_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Transaction'
type MockRepository_Transaction_Call struct {
	*mock.Call
}

// Transaction is a helper method to define mock.On call
//   - ctx context.Context
//   - callback func(context.Context) error
//   - opts ...*sql.TxOptions
func (_e *MockRepository_Expecter) Transaction(ctx interface{}, callback interface{}, opts ...interface{}) *MockRepository_Transaction_Call {
	return &MockRepository_Transaction_Call{Call: _e.mock.On("Transaction",
		append([]interface{}{ctx, callback}, opts...)...)}
}

func (_c *MockRepository_Transaction_Call) Run(run func(ctx context.Context, callback func(context.Context) error, opts ...*sql.TxOptions)) *MockRepository_Transaction_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]*sql.TxOptions, len(args)-2)
		for i, a := range args[2:] {
			if a != nil {
				variadicArgs[i] = a.(*sql.TxOptions)
			}
		}
		run(args[0].(context.Context), args[1].(func(context.Context) error), variadicArgs...)
	})
	return _c
}

func (_c *MockRepository_Transaction_Call) Return(_a0 error) *MockRepository_Transaction_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepository_Transaction_Call) RunAndReturn(run func(context.Context, func(context.Context) error, ...*sql.TxOptions) error) *MockRepository_Transaction_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateDeployment provides a mock function with given fields: ctx, in
func (_m *MockRepository) UpdateDeployment(ctx context.Context, in repository.UpdateDeploymentInput) (*entities.Deployment, error) {
	ret := _m.Called(ctx, in)

	if len(ret) == 0 {
		panic("no return value specified for UpdateDeployment")
	}

	var r0 *entities.Deployment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.UpdateDeploymentInput) (*entities.Deployment, error)); ok {
		return rf(ctx, in)
	}
	if rf, ok := ret.Get(0).(func(context.Context, repository.UpdateDeploymentInput) *entities.Deployment); ok {
		r0 = rf(ctx, in)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.Deployment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, repository.UpdateDeploymentInput) error); ok {
		r1 = rf(ctx, in)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_UpdateDeployment_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateDeployment'
type MockRepository_UpdateDeployment_Call struct {
	*mock.Call
}

// UpdateDeployment is a helper method to define mock.On call
//   - ctx context.Context
//   - in repository.UpdateDeploymentInput
func (_e *MockRepository_Expecter) UpdateDeployment(ctx interface{}, in interface{}) *MockRepository_UpdateDeployment_Call {
	return &MockRepository_UpdateDeployment_Call{Call: _e.mock.On("UpdateDeployment", ctx, in)}
}

func (_c *MockRepository_UpdateDeployment_Call) Run(run func(ctx context.Context, in repository.UpdateDeploymentInput)) *MockRepository_UpdateDeployment_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.UpdateDeploymentInput))
	})
	return _c
}

func (_c *MockRepository_UpdateDeployment_Call) Return(_a0 *entities.Deployment, _a1 error) *MockRepository_UpdateDeployment_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_UpdateDeployment_Call) RunAndReturn(run func(context.Context, repository.UpdateDeploymentInput) (*entities.Deployment, error)) *MockRepository_UpdateDeployment_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateECRDeployment provides a mock function with given fields: ctx, input
func (_m *MockRepository) UpdateECRDeployment(ctx context.Context, input repository.UpdateECRDeploymentInput) (*entities.CustomImageDeployment, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for UpdateECRDeployment")
	}

	var r0 *entities.CustomImageDeployment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.UpdateECRDeploymentInput) (*entities.CustomImageDeployment, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, repository.UpdateECRDeploymentInput) *entities.CustomImageDeployment); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.CustomImageDeployment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, repository.UpdateECRDeploymentInput) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_UpdateECRDeployment_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateECRDeployment'
type MockRepository_UpdateECRDeployment_Call struct {
	*mock.Call
}

// UpdateECRDeployment is a helper method to define mock.On call
//   - ctx context.Context
//   - input repository.UpdateECRDeploymentInput
func (_e *MockRepository_Expecter) UpdateECRDeployment(ctx interface{}, input interface{}) *MockRepository_UpdateECRDeployment_Call {
	return &MockRepository_UpdateECRDeployment_Call{Call: _e.mock.On("UpdateECRDeployment", ctx, input)}
}

func (_c *MockRepository_UpdateECRDeployment_Call) Run(run func(ctx context.Context, input repository.UpdateECRDeploymentInput)) *MockRepository_UpdateECRDeployment_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.UpdateECRDeploymentInput))
	})
	return _c
}

func (_c *MockRepository_UpdateECRDeployment_Call) Return(_a0 *entities.CustomImageDeployment, _a1 error) *MockRepository_UpdateECRDeployment_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_UpdateECRDeployment_Call) RunAndReturn(run func(context.Context, repository.UpdateECRDeploymentInput) (*entities.CustomImageDeployment, error)) *MockRepository_UpdateECRDeployment_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateECRDeploymentEnv provides a mock function with given fields: ctx, in
func (_m *MockRepository) UpdateECRDeploymentEnv(ctx context.Context, in repository.UpdateECRDeploymentEnvInput) error {
	ret := _m.Called(ctx, in)

	if len(ret) == 0 {
		panic("no return value specified for UpdateECRDeploymentEnv")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.UpdateECRDeploymentEnvInput) error); ok {
		r0 = rf(ctx, in)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepository_UpdateECRDeploymentEnv_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateECRDeploymentEnv'
type MockRepository_UpdateECRDeploymentEnv_Call struct {
	*mock.Call
}

// UpdateECRDeploymentEnv is a helper method to define mock.On call
//   - ctx context.Context
//   - in repository.UpdateECRDeploymentEnvInput
func (_e *MockRepository_Expecter) UpdateECRDeploymentEnv(ctx interface{}, in interface{}) *MockRepository_UpdateECRDeploymentEnv_Call {
	return &MockRepository_UpdateECRDeploymentEnv_Call{Call: _e.mock.On("UpdateECRDeploymentEnv", ctx, in)}
}

func (_c *MockRepository_UpdateECRDeploymentEnv_Call) Run(run func(ctx context.Context, in repository.UpdateECRDeploymentEnvInput)) *MockRepository_UpdateECRDeploymentEnv_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.UpdateECRDeploymentEnvInput))
	})
	return _c
}

func (_c *MockRepository_UpdateECRDeploymentEnv_Call) Return(_a0 error) *MockRepository_UpdateECRDeploymentEnv_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepository_UpdateECRDeploymentEnv_Call) RunAndReturn(run func(context.Context, repository.UpdateECRDeploymentEnvInput) error) *MockRepository_UpdateECRDeploymentEnv_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateEnv provides a mock function with given fields: ctx, in
func (_m *MockRepository) UpdateEnv(ctx context.Context, in repository.UpdateEnvInput) error {
	ret := _m.Called(ctx, in)

	if len(ret) == 0 {
		panic("no return value specified for UpdateEnv")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.UpdateEnvInput) error); ok {
		r0 = rf(ctx, in)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepository_UpdateEnv_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateEnv'
type MockRepository_UpdateEnv_Call struct {
	*mock.Call
}

// UpdateEnv is a helper method to define mock.On call
//   - ctx context.Context
//   - in repository.UpdateEnvInput
func (_e *MockRepository_Expecter) UpdateEnv(ctx interface{}, in interface{}) *MockRepository_UpdateEnv_Call {
	return &MockRepository_UpdateEnv_Call{Call: _e.mock.On("UpdateEnv", ctx, in)}
}

func (_c *MockRepository_UpdateEnv_Call) Run(run func(ctx context.Context, in repository.UpdateEnvInput)) *MockRepository_UpdateEnv_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.UpdateEnvInput))
	})
	return _c
}

func (_c *MockRepository_UpdateEnv_Call) Return(_a0 error) *MockRepository_UpdateEnv_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepository_UpdateEnv_Call) RunAndReturn(run func(context.Context, repository.UpdateEnvInput) error) *MockRepository_UpdateEnv_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateHardware provides a mock function with given fields: ctx, hardware
func (_m *MockRepository) UpdateHardware(ctx context.Context, hardware *entities.Hardware) error {
	ret := _m.Called(ctx, hardware)

	if len(ret) == 0 {
		panic("no return value specified for UpdateHardware")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *entities.Hardware) error); ok {
		r0 = rf(ctx, hardware)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepository_UpdateHardware_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateHardware'
type MockRepository_UpdateHardware_Call struct {
	*mock.Call
}

// UpdateHardware is a helper method to define mock.On call
//   - ctx context.Context
//   - hardware *entities.Hardware
func (_e *MockRepository_Expecter) UpdateHardware(ctx interface{}, hardware interface{}) *MockRepository_UpdateHardware_Call {
	return &MockRepository_UpdateHardware_Call{Call: _e.mock.On("UpdateHardware", ctx, hardware)}
}

func (_c *MockRepository_UpdateHardware_Call) Run(run func(ctx context.Context, hardware *entities.Hardware)) *MockRepository_UpdateHardware_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*entities.Hardware))
	})
	return _c
}

func (_c *MockRepository_UpdateHardware_Call) Return(_a0 error) *MockRepository_UpdateHardware_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepository_UpdateHardware_Call) RunAndReturn(run func(context.Context, *entities.Hardware) error) *MockRepository_UpdateHardware_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateRepository provides a mock function with given fields: ctx, input
func (_m *MockRepository) UpdateRepository(ctx context.Context, input entities.Repository) error {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for UpdateRepository")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, entities.Repository) error); ok {
		r0 = rf(ctx, input)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepository_UpdateRepository_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateRepository'
type MockRepository_UpdateRepository_Call struct {
	*mock.Call
}

// UpdateRepository is a helper method to define mock.On call
//   - ctx context.Context
//   - input entities.Repository
func (_e *MockRepository_Expecter) UpdateRepository(ctx interface{}, input interface{}) *MockRepository_UpdateRepository_Call {
	return &MockRepository_UpdateRepository_Call{Call: _e.mock.On("UpdateRepository", ctx, input)}
}

func (_c *MockRepository_UpdateRepository_Call) Run(run func(ctx context.Context, input entities.Repository)) *MockRepository_UpdateRepository_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(entities.Repository))
	})
	return _c
}

func (_c *MockRepository_UpdateRepository_Call) Return(_a0 error) *MockRepository_UpdateRepository_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepository_UpdateRepository_Call) RunAndReturn(run func(context.Context, entities.Repository) error) *MockRepository_UpdateRepository_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateUserGitGroup provides a mock function with given fields: ctx, in
func (_m *MockRepository) UpdateUserGitGroup(ctx context.Context, in repository.UpdateUserGitGroupInput) (*entities.UserGitGroup, error) {
	ret := _m.Called(ctx, in)

	if len(ret) == 0 {
		panic("no return value specified for UpdateUserGitGroup")
	}

	var r0 *entities.UserGitGroup
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.UpdateUserGitGroupInput) (*entities.UserGitGroup, error)); ok {
		return rf(ctx, in)
	}
	if rf, ok := ret.Get(0).(func(context.Context, repository.UpdateUserGitGroupInput) *entities.UserGitGroup); ok {
		r0 = rf(ctx, in)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.UserGitGroup)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, repository.UpdateUserGitGroupInput) error); ok {
		r1 = rf(ctx, in)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_UpdateUserGitGroup_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateUserGitGroup'
type MockRepository_UpdateUserGitGroup_Call struct {
	*mock.Call
}

// UpdateUserGitGroup is a helper method to define mock.On call
//   - ctx context.Context
//   - in repository.UpdateUserGitGroupInput
func (_e *MockRepository_Expecter) UpdateUserGitGroup(ctx interface{}, in interface{}) *MockRepository_UpdateUserGitGroup_Call {
	return &MockRepository_UpdateUserGitGroup_Call{Call: _e.mock.On("UpdateUserGitGroup", ctx, in)}
}

func (_c *MockRepository_UpdateUserGitGroup_Call) Run(run func(ctx context.Context, in repository.UpdateUserGitGroupInput)) *MockRepository_UpdateUserGitGroup_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.UpdateUserGitGroupInput))
	})
	return _c
}

func (_c *MockRepository_UpdateUserGitGroup_Call) Return(_a0 *entities.UserGitGroup, _a1 error) *MockRepository_UpdateUserGitGroup_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_UpdateUserGitGroup_Call) RunAndReturn(run func(context.Context, repository.UpdateUserGitGroupInput) (*entities.UserGitGroup, error)) *MockRepository_UpdateUserGitGroup_Call {
	_c.Call.Return(run)
	return _c
}

// UpsertDeployment provides a mock function with given fields: ctx, in
func (_m *MockRepository) UpsertDeployment(ctx context.Context, in repository.CreateDeploymentInput) (*entities.Deployment, error) {
	ret := _m.Called(ctx, in)

	if len(ret) == 0 {
		panic("no return value specified for UpsertDeployment")
	}

	var r0 *entities.Deployment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.CreateDeploymentInput) (*entities.Deployment, error)); ok {
		return rf(ctx, in)
	}
	if rf, ok := ret.Get(0).(func(context.Context, repository.CreateDeploymentInput) *entities.Deployment); ok {
		r0 = rf(ctx, in)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.Deployment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, repository.CreateDeploymentInput) error); ok {
		r1 = rf(ctx, in)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_UpsertDeployment_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpsertDeployment'
type MockRepository_UpsertDeployment_Call struct {
	*mock.Call
}

// UpsertDeployment is a helper method to define mock.On call
//   - ctx context.Context
//   - in repository.CreateDeploymentInput
func (_e *MockRepository_Expecter) UpsertDeployment(ctx interface{}, in interface{}) *MockRepository_UpsertDeployment_Call {
	return &MockRepository_UpsertDeployment_Call{Call: _e.mock.On("UpsertDeployment", ctx, in)}
}

func (_c *MockRepository_UpsertDeployment_Call) Run(run func(ctx context.Context, in repository.CreateDeploymentInput)) *MockRepository_UpsertDeployment_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.CreateDeploymentInput))
	})
	return _c
}

func (_c *MockRepository_UpsertDeployment_Call) Return(_a0 *entities.Deployment, _a1 error) *MockRepository_UpsertDeployment_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_UpsertDeployment_Call) RunAndReturn(run func(context.Context, repository.CreateDeploymentInput) (*entities.Deployment, error)) *MockRepository_UpsertDeployment_Call {
	_c.Call.Return(run)
	return _c
}

// UpsertSignupRequest provides a mock function with given fields: ctx, input
func (_m *MockRepository) UpsertSignupRequest(ctx context.Context, input *entities.SignupRequest) error {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for UpsertSignupRequest")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *entities.SignupRequest) error); ok {
		r0 = rf(ctx, input)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepository_UpsertSignupRequest_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpsertSignupRequest'
type MockRepository_UpsertSignupRequest_Call struct {
	*mock.Call
}

// UpsertSignupRequest is a helper method to define mock.On call
//   - ctx context.Context
//   - input *entities.SignupRequest
func (_e *MockRepository_Expecter) UpsertSignupRequest(ctx interface{}, input interface{}) *MockRepository_UpsertSignupRequest_Call {
	return &MockRepository_UpsertSignupRequest_Call{Call: _e.mock.On("UpsertSignupRequest", ctx, input)}
}

func (_c *MockRepository_UpsertSignupRequest_Call) Run(run func(ctx context.Context, input *entities.SignupRequest)) *MockRepository_UpsertSignupRequest_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*entities.SignupRequest))
	})
	return _c
}

func (_c *MockRepository_UpsertSignupRequest_Call) Return(_a0 error) *MockRepository_UpsertSignupRequest_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepository_UpsertSignupRequest_Call) RunAndReturn(run func(context.Context, *entities.SignupRequest) error) *MockRepository_UpsertSignupRequest_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockRepository creates a new instance of MockRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockRepository {
	mock := &MockRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
