// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	dto "api-server/internal/dto"
	entities "api-server/internal/entities"
	context "context"

	mock "github.com/stretchr/testify/mock"

	repository "api-server/internal/repositories"

	types "api-server/internal/types"

	uuid "github.com/google/uuid"
)

// MockRepoRepository is an autogenerated mock type for the RepoRepository type
type MockRepoRepository struct {
	mock.Mock
}

type MockRepoRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockRepoRepository) EXPECT() *MockRepoRepository_Expecter {
	return &MockRepoRepository_Expecter{mock: &_m.Mock}
}

// BulkCreateEnv provides a mock function with given fields: ctx, in
func (_m *MockRepoRepository) BulkCreateEnv(ctx context.Context, in repository.BulkCreateEnvInput) error {
	ret := _m.Called(ctx, in)

	if len(ret) == 0 {
		panic("no return value specified for BulkCreateEnv")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.BulkCreateEnvInput) error); ok {
		r0 = rf(ctx, in)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepoRepository_BulkCreateEnv_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'BulkCreateEnv'
type MockRepoRepository_BulkCreateEnv_Call struct {
	*mock.Call
}

// BulkCreateEnv is a helper method to define mock.On call
//   - ctx context.Context
//   - in repository.BulkCreateEnvInput
func (_e *MockRepoRepository_Expecter) BulkCreateEnv(ctx interface{}, in interface{}) *MockRepoRepository_BulkCreateEnv_Call {
	return &MockRepoRepository_BulkCreateEnv_Call{Call: _e.mock.On("BulkCreateEnv", ctx, in)}
}

func (_c *MockRepoRepository_BulkCreateEnv_Call) Run(run func(ctx context.Context, in repository.BulkCreateEnvInput)) *MockRepoRepository_BulkCreateEnv_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.BulkCreateEnvInput))
	})
	return _c
}

func (_c *MockRepoRepository_BulkCreateEnv_Call) Return(_a0 error) *MockRepoRepository_BulkCreateEnv_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepoRepository_BulkCreateEnv_Call) RunAndReturn(run func(context.Context, repository.BulkCreateEnvInput) error) *MockRepoRepository_BulkCreateEnv_Call {
	_c.Call.Return(run)
	return _c
}

// CountRepoMembers provides a mock function with given fields: ctx, query
func (_m *MockRepoRepository) CountRepoMembers(ctx context.Context, query repository.ListRepositoryMembersQuery) (int, error) {
	ret := _m.Called(ctx, query)

	if len(ret) == 0 {
		panic("no return value specified for CountRepoMembers")
	}

	var r0 int
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.ListRepositoryMembersQuery) (int, error)); ok {
		return rf(ctx, query)
	}
	if rf, ok := ret.Get(0).(func(context.Context, repository.ListRepositoryMembersQuery) int); ok {
		r0 = rf(ctx, query)
	} else {
		r0 = ret.Get(0).(int)
	}

	if rf, ok := ret.Get(1).(func(context.Context, repository.ListRepositoryMembersQuery) error); ok {
		r1 = rf(ctx, query)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepoRepository_CountRepoMembers_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CountRepoMembers'
type MockRepoRepository_CountRepoMembers_Call struct {
	*mock.Call
}

// CountRepoMembers is a helper method to define mock.On call
//   - ctx context.Context
//   - query repository.ListRepositoryMembersQuery
func (_e *MockRepoRepository_Expecter) CountRepoMembers(ctx interface{}, query interface{}) *MockRepoRepository_CountRepoMembers_Call {
	return &MockRepoRepository_CountRepoMembers_Call{Call: _e.mock.On("CountRepoMembers", ctx, query)}
}

func (_c *MockRepoRepository_CountRepoMembers_Call) Run(run func(ctx context.Context, query repository.ListRepositoryMembersQuery)) *MockRepoRepository_CountRepoMembers_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.ListRepositoryMembersQuery))
	})
	return _c
}

func (_c *MockRepoRepository_CountRepoMembers_Call) Return(_a0 int, _a1 error) *MockRepoRepository_CountRepoMembers_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepoRepository_CountRepoMembers_Call) RunAndReturn(run func(context.Context, repository.ListRepositoryMembersQuery) (int, error)) *MockRepoRepository_CountRepoMembers_Call {
	_c.Call.Return(run)
	return _c
}

// CountRepositories provides a mock function with given fields: ctx, input
func (_m *MockRepoRepository) CountRepositories(ctx context.Context, input dto.GetRepositoriesInput) (int64, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for CountRepositories")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.GetRepositoriesInput) (int64, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, dto.GetRepositoriesInput) int64); ok {
		r0 = rf(ctx, input)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, dto.GetRepositoriesInput) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepoRepository_CountRepositories_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CountRepositories'
type MockRepoRepository_CountRepositories_Call struct {
	*mock.Call
}

// CountRepositories is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.GetRepositoriesInput
func (_e *MockRepoRepository_Expecter) CountRepositories(ctx interface{}, input interface{}) *MockRepoRepository_CountRepositories_Call {
	return &MockRepoRepository_CountRepositories_Call{Call: _e.mock.On("CountRepositories", ctx, input)}
}

func (_c *MockRepoRepository_CountRepositories_Call) Run(run func(ctx context.Context, input dto.GetRepositoriesInput)) *MockRepoRepository_CountRepositories_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.GetRepositoriesInput))
	})
	return _c
}

func (_c *MockRepoRepository_CountRepositories_Call) Return(_a0 int64, _a1 error) *MockRepoRepository_CountRepositories_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepoRepository_CountRepositories_Call) RunAndReturn(run func(context.Context, dto.GetRepositoriesInput) (int64, error)) *MockRepoRepository_CountRepositories_Call {
	_c.Call.Return(run)
	return _c
}

// CreateEnv provides a mock function with given fields: ctx, in
func (_m *MockRepoRepository) CreateEnv(ctx context.Context, in repository.CreateEnvInput) error {
	ret := _m.Called(ctx, in)

	if len(ret) == 0 {
		panic("no return value specified for CreateEnv")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.CreateEnvInput) error); ok {
		r0 = rf(ctx, in)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepoRepository_CreateEnv_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateEnv'
type MockRepoRepository_CreateEnv_Call struct {
	*mock.Call
}

// CreateEnv is a helper method to define mock.On call
//   - ctx context.Context
//   - in repository.CreateEnvInput
func (_e *MockRepoRepository_Expecter) CreateEnv(ctx interface{}, in interface{}) *MockRepoRepository_CreateEnv_Call {
	return &MockRepoRepository_CreateEnv_Call{Call: _e.mock.On("CreateEnv", ctx, in)}
}

func (_c *MockRepoRepository_CreateEnv_Call) Run(run func(ctx context.Context, in repository.CreateEnvInput)) *MockRepoRepository_CreateEnv_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.CreateEnvInput))
	})
	return _c
}

func (_c *MockRepoRepository_CreateEnv_Call) Return(_a0 error) *MockRepoRepository_CreateEnv_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepoRepository_CreateEnv_Call) RunAndReturn(run func(context.Context, repository.CreateEnvInput) error) *MockRepoRepository_CreateEnv_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteEnv provides a mock function with given fields: ctx, in
func (_m *MockRepoRepository) DeleteEnv(ctx context.Context, in repository.CreateEnvInput) error {
	ret := _m.Called(ctx, in)

	if len(ret) == 0 {
		panic("no return value specified for DeleteEnv")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.CreateEnvInput) error); ok {
		r0 = rf(ctx, in)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepoRepository_DeleteEnv_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteEnv'
type MockRepoRepository_DeleteEnv_Call struct {
	*mock.Call
}

// DeleteEnv is a helper method to define mock.On call
//   - ctx context.Context
//   - in repository.CreateEnvInput
func (_e *MockRepoRepository_Expecter) DeleteEnv(ctx interface{}, in interface{}) *MockRepoRepository_DeleteEnv_Call {
	return &MockRepoRepository_DeleteEnv_Call{Call: _e.mock.On("DeleteEnv", ctx, in)}
}

func (_c *MockRepoRepository_DeleteEnv_Call) Run(run func(ctx context.Context, in repository.CreateEnvInput)) *MockRepoRepository_DeleteEnv_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.CreateEnvInput))
	})
	return _c
}

func (_c *MockRepoRepository_DeleteEnv_Call) Return(_a0 error) *MockRepoRepository_DeleteEnv_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepoRepository_DeleteEnv_Call) RunAndReturn(run func(context.Context, repository.CreateEnvInput) error) *MockRepoRepository_DeleteEnv_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteProjectsByOrganizationID provides a mock function with given fields: ctx, organizationID
func (_m *MockRepoRepository) DeleteProjectsByOrganizationID(ctx context.Context, organizationID uuid.UUID) error {
	ret := _m.Called(ctx, organizationID)

	if len(ret) == 0 {
		panic("no return value specified for DeleteProjectsByOrganizationID")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) error); ok {
		r0 = rf(ctx, organizationID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepoRepository_DeleteProjectsByOrganizationID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteProjectsByOrganizationID'
type MockRepoRepository_DeleteProjectsByOrganizationID_Call struct {
	*mock.Call
}

// DeleteProjectsByOrganizationID is a helper method to define mock.On call
//   - ctx context.Context
//   - organizationID uuid.UUID
func (_e *MockRepoRepository_Expecter) DeleteProjectsByOrganizationID(ctx interface{}, organizationID interface{}) *MockRepoRepository_DeleteProjectsByOrganizationID_Call {
	return &MockRepoRepository_DeleteProjectsByOrganizationID_Call{Call: _e.mock.On("DeleteProjectsByOrganizationID", ctx, organizationID)}
}

func (_c *MockRepoRepository_DeleteProjectsByOrganizationID_Call) Run(run func(ctx context.Context, organizationID uuid.UUID)) *MockRepoRepository_DeleteProjectsByOrganizationID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *MockRepoRepository_DeleteProjectsByOrganizationID_Call) Return(_a0 error) *MockRepoRepository_DeleteProjectsByOrganizationID_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepoRepository_DeleteProjectsByOrganizationID_Call) RunAndReturn(run func(context.Context, uuid.UUID) error) *MockRepoRepository_DeleteProjectsByOrganizationID_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteProjectsByUserID provides a mock function with given fields: ctx, userID
func (_m *MockRepoRepository) DeleteProjectsByUserID(ctx context.Context, userID uuid.UUID) error {
	ret := _m.Called(ctx, userID)

	if len(ret) == 0 {
		panic("no return value specified for DeleteProjectsByUserID")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) error); ok {
		r0 = rf(ctx, userID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepoRepository_DeleteProjectsByUserID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteProjectsByUserID'
type MockRepoRepository_DeleteProjectsByUserID_Call struct {
	*mock.Call
}

// DeleteProjectsByUserID is a helper method to define mock.On call
//   - ctx context.Context
//   - userID uuid.UUID
func (_e *MockRepoRepository_Expecter) DeleteProjectsByUserID(ctx interface{}, userID interface{}) *MockRepoRepository_DeleteProjectsByUserID_Call {
	return &MockRepoRepository_DeleteProjectsByUserID_Call{Call: _e.mock.On("DeleteProjectsByUserID", ctx, userID)}
}

func (_c *MockRepoRepository_DeleteProjectsByUserID_Call) Run(run func(ctx context.Context, userID uuid.UUID)) *MockRepoRepository_DeleteProjectsByUserID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *MockRepoRepository_DeleteProjectsByUserID_Call) Return(_a0 error) *MockRepoRepository_DeleteProjectsByUserID_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepoRepository_DeleteProjectsByUserID_Call) RunAndReturn(run func(context.Context, uuid.UUID) error) *MockRepoRepository_DeleteProjectsByUserID_Call {
	_c.Call.Return(run)
	return _c
}

// FindRepository provides a mock function with given fields: ctx, filter
func (_m *MockRepoRepository) FindRepository(ctx context.Context, filter repository.RepositoryFilter) (*entities.Repository, error) {
	ret := _m.Called(ctx, filter)

	if len(ret) == 0 {
		panic("no return value specified for FindRepository")
	}

	var r0 *entities.Repository
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.RepositoryFilter) (*entities.Repository, error)); ok {
		return rf(ctx, filter)
	}
	if rf, ok := ret.Get(0).(func(context.Context, repository.RepositoryFilter) *entities.Repository); ok {
		r0 = rf(ctx, filter)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.Repository)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, repository.RepositoryFilter) error); ok {
		r1 = rf(ctx, filter)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepoRepository_FindRepository_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindRepository'
type MockRepoRepository_FindRepository_Call struct {
	*mock.Call
}

// FindRepository is a helper method to define mock.On call
//   - ctx context.Context
//   - filter repository.RepositoryFilter
func (_e *MockRepoRepository_Expecter) FindRepository(ctx interface{}, filter interface{}) *MockRepoRepository_FindRepository_Call {
	return &MockRepoRepository_FindRepository_Call{Call: _e.mock.On("FindRepository", ctx, filter)}
}

func (_c *MockRepoRepository_FindRepository_Call) Run(run func(ctx context.Context, filter repository.RepositoryFilter)) *MockRepoRepository_FindRepository_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.RepositoryFilter))
	})
	return _c
}

func (_c *MockRepoRepository_FindRepository_Call) Return(_a0 *entities.Repository, _a1 error) *MockRepoRepository_FindRepository_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepoRepository_FindRepository_Call) RunAndReturn(run func(context.Context, repository.RepositoryFilter) (*entities.Repository, error)) *MockRepoRepository_FindRepository_Call {
	_c.Call.Return(run)
	return _c
}

// GetEnv provides a mock function with given fields: ctx, repoID
func (_m *MockRepoRepository) GetEnv(ctx context.Context, repoID uuid.UUID) (*entities.RepoEnv, error) {
	ret := _m.Called(ctx, repoID)

	if len(ret) == 0 {
		panic("no return value specified for GetEnv")
	}

	var r0 *entities.RepoEnv
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) (*entities.RepoEnv, error)); ok {
		return rf(ctx, repoID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) *entities.RepoEnv); ok {
		r0 = rf(ctx, repoID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.RepoEnv)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uuid.UUID) error); ok {
		r1 = rf(ctx, repoID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepoRepository_GetEnv_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetEnv'
type MockRepoRepository_GetEnv_Call struct {
	*mock.Call
}

// GetEnv is a helper method to define mock.On call
//   - ctx context.Context
//   - repoID uuid.UUID
func (_e *MockRepoRepository_Expecter) GetEnv(ctx interface{}, repoID interface{}) *MockRepoRepository_GetEnv_Call {
	return &MockRepoRepository_GetEnv_Call{Call: _e.mock.On("GetEnv", ctx, repoID)}
}

func (_c *MockRepoRepository_GetEnv_Call) Run(run func(ctx context.Context, repoID uuid.UUID)) *MockRepoRepository_GetEnv_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *MockRepoRepository_GetEnv_Call) Return(_a0 *entities.RepoEnv, _a1 error) *MockRepoRepository_GetEnv_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepoRepository_GetEnv_Call) RunAndReturn(run func(context.Context, uuid.UUID) (*entities.RepoEnv, error)) *MockRepoRepository_GetEnv_Call {
	_c.Call.Return(run)
	return _c
}

// ListRepoMembers provides a mock function with given fields: ctx, pagination, order, query
func (_m *MockRepoRepository) ListRepoMembers(ctx context.Context, pagination types.Pagination, order types.OrderBy, query repository.ListRepositoryMembersQuery) ([]entities.RepoMemberInfo, error) {
	ret := _m.Called(ctx, pagination, order, query)

	if len(ret) == 0 {
		panic("no return value specified for ListRepoMembers")
	}

	var r0 []entities.RepoMemberInfo
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, types.Pagination, types.OrderBy, repository.ListRepositoryMembersQuery) ([]entities.RepoMemberInfo, error)); ok {
		return rf(ctx, pagination, order, query)
	}
	if rf, ok := ret.Get(0).(func(context.Context, types.Pagination, types.OrderBy, repository.ListRepositoryMembersQuery) []entities.RepoMemberInfo); ok {
		r0 = rf(ctx, pagination, order, query)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]entities.RepoMemberInfo)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, types.Pagination, types.OrderBy, repository.ListRepositoryMembersQuery) error); ok {
		r1 = rf(ctx, pagination, order, query)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepoRepository_ListRepoMembers_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListRepoMembers'
type MockRepoRepository_ListRepoMembers_Call struct {
	*mock.Call
}

// ListRepoMembers is a helper method to define mock.On call
//   - ctx context.Context
//   - pagination types.Pagination
//   - order types.OrderBy
//   - query repository.ListRepositoryMembersQuery
func (_e *MockRepoRepository_Expecter) ListRepoMembers(ctx interface{}, pagination interface{}, order interface{}, query interface{}) *MockRepoRepository_ListRepoMembers_Call {
	return &MockRepoRepository_ListRepoMembers_Call{Call: _e.mock.On("ListRepoMembers", ctx, pagination, order, query)}
}

func (_c *MockRepoRepository_ListRepoMembers_Call) Run(run func(ctx context.Context, pagination types.Pagination, order types.OrderBy, query repository.ListRepositoryMembersQuery)) *MockRepoRepository_ListRepoMembers_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(types.Pagination), args[2].(types.OrderBy), args[3].(repository.ListRepositoryMembersQuery))
	})
	return _c
}

func (_c *MockRepoRepository_ListRepoMembers_Call) Return(_a0 []entities.RepoMemberInfo, _a1 error) *MockRepoRepository_ListRepoMembers_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepoRepository_ListRepoMembers_Call) RunAndReturn(run func(context.Context, types.Pagination, types.OrderBy, repository.ListRepositoryMembersQuery) ([]entities.RepoMemberInfo, error)) *MockRepoRepository_ListRepoMembers_Call {
	_c.Call.Return(run)
	return _c
}

// ListRepositories provides a mock function with given fields: ctx, input
func (_m *MockRepoRepository) ListRepositories(ctx context.Context, input dto.GetRepositoriesInput) ([]entities.Repository, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for ListRepositories")
	}

	var r0 []entities.Repository
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.GetRepositoriesInput) ([]entities.Repository, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, dto.GetRepositoriesInput) []entities.Repository); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]entities.Repository)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, dto.GetRepositoriesInput) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepoRepository_ListRepositories_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListRepositories'
type MockRepoRepository_ListRepositories_Call struct {
	*mock.Call
}

// ListRepositories is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.GetRepositoriesInput
func (_e *MockRepoRepository_Expecter) ListRepositories(ctx interface{}, input interface{}) *MockRepoRepository_ListRepositories_Call {
	return &MockRepoRepository_ListRepositories_Call{Call: _e.mock.On("ListRepositories", ctx, input)}
}

func (_c *MockRepoRepository_ListRepositories_Call) Run(run func(ctx context.Context, input dto.GetRepositoriesInput)) *MockRepoRepository_ListRepositories_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.GetRepositoriesInput))
	})
	return _c
}

func (_c *MockRepoRepository_ListRepositories_Call) Return(_a0 []entities.Repository, _a1 error) *MockRepoRepository_ListRepositories_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepoRepository_ListRepositories_Call) RunAndReturn(run func(context.Context, dto.GetRepositoriesInput) ([]entities.Repository, error)) *MockRepoRepository_ListRepositories_Call {
	_c.Call.Return(run)
	return _c
}

// RemoveUserInRepository provides a mock function with given fields: ctx, in
func (_m *MockRepoRepository) RemoveUserInRepository(ctx context.Context, in repository.RemoveUserInRepositoryInput) error {
	ret := _m.Called(ctx, in)

	if len(ret) == 0 {
		panic("no return value specified for RemoveUserInRepository")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.RemoveUserInRepositoryInput) error); ok {
		r0 = rf(ctx, in)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepoRepository_RemoveUserInRepository_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RemoveUserInRepository'
type MockRepoRepository_RemoveUserInRepository_Call struct {
	*mock.Call
}

// RemoveUserInRepository is a helper method to define mock.On call
//   - ctx context.Context
//   - in repository.RemoveUserInRepositoryInput
func (_e *MockRepoRepository_Expecter) RemoveUserInRepository(ctx interface{}, in interface{}) *MockRepoRepository_RemoveUserInRepository_Call {
	return &MockRepoRepository_RemoveUserInRepository_Call{Call: _e.mock.On("RemoveUserInRepository", ctx, in)}
}

func (_c *MockRepoRepository_RemoveUserInRepository_Call) Run(run func(ctx context.Context, in repository.RemoveUserInRepositoryInput)) *MockRepoRepository_RemoveUserInRepository_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.RemoveUserInRepositoryInput))
	})
	return _c
}

func (_c *MockRepoRepository_RemoveUserInRepository_Call) Return(_a0 error) *MockRepoRepository_RemoveUserInRepository_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepoRepository_RemoveUserInRepository_Call) RunAndReturn(run func(context.Context, repository.RemoveUserInRepositoryInput) error) *MockRepoRepository_RemoveUserInRepository_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateEnv provides a mock function with given fields: ctx, in
func (_m *MockRepoRepository) UpdateEnv(ctx context.Context, in repository.UpdateEnvInput) error {
	ret := _m.Called(ctx, in)

	if len(ret) == 0 {
		panic("no return value specified for UpdateEnv")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.UpdateEnvInput) error); ok {
		r0 = rf(ctx, in)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepoRepository_UpdateEnv_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateEnv'
type MockRepoRepository_UpdateEnv_Call struct {
	*mock.Call
}

// UpdateEnv is a helper method to define mock.On call
//   - ctx context.Context
//   - in repository.UpdateEnvInput
func (_e *MockRepoRepository_Expecter) UpdateEnv(ctx interface{}, in interface{}) *MockRepoRepository_UpdateEnv_Call {
	return &MockRepoRepository_UpdateEnv_Call{Call: _e.mock.On("UpdateEnv", ctx, in)}
}

func (_c *MockRepoRepository_UpdateEnv_Call) Run(run func(ctx context.Context, in repository.UpdateEnvInput)) *MockRepoRepository_UpdateEnv_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.UpdateEnvInput))
	})
	return _c
}

func (_c *MockRepoRepository_UpdateEnv_Call) Return(_a0 error) *MockRepoRepository_UpdateEnv_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepoRepository_UpdateEnv_Call) RunAndReturn(run func(context.Context, repository.UpdateEnvInput) error) *MockRepoRepository_UpdateEnv_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateRepository provides a mock function with given fields: ctx, input
func (_m *MockRepoRepository) UpdateRepository(ctx context.Context, input entities.Repository) error {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for UpdateRepository")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, entities.Repository) error); ok {
		r0 = rf(ctx, input)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepoRepository_UpdateRepository_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateRepository'
type MockRepoRepository_UpdateRepository_Call struct {
	*mock.Call
}

// UpdateRepository is a helper method to define mock.On call
//   - ctx context.Context
//   - input entities.Repository
func (_e *MockRepoRepository_Expecter) UpdateRepository(ctx interface{}, input interface{}) *MockRepoRepository_UpdateRepository_Call {
	return &MockRepoRepository_UpdateRepository_Call{Call: _e.mock.On("UpdateRepository", ctx, input)}
}

func (_c *MockRepoRepository_UpdateRepository_Call) Run(run func(ctx context.Context, input entities.Repository)) *MockRepoRepository_UpdateRepository_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(entities.Repository))
	})
	return _c
}

func (_c *MockRepoRepository_UpdateRepository_Call) Return(_a0 error) *MockRepoRepository_UpdateRepository_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepoRepository_UpdateRepository_Call) RunAndReturn(run func(context.Context, entities.Repository) error) *MockRepoRepository_UpdateRepository_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockRepoRepository creates a new instance of MockRepoRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockRepoRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockRepoRepository {
	mock := &MockRepoRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
