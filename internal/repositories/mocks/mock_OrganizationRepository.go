// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	dto "api-server/internal/dto"
	entities "api-server/internal/entities"
	context "context"

	mock "github.com/stretchr/testify/mock"

	repository "api-server/internal/repositories"

	uuid "github.com/google/uuid"
)

// MockOrganizationRepository is an autogenerated mock type for the OrganizationRepository type
type MockOrganizationRepository struct {
	mock.Mock
}

type MockOrganizationRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockOrganizationRepository) EXPECT() *MockOrganizationRepository_Expecter {
	return &MockOrganizationRepository_Expecter{mock: &_m.Mock}
}

// FindOrganization provides a mock function with given fields: ctx, input
func (_m *MockOrganizationRepository) FindOrganization(ctx context.Context, input repository.FilterOrganization) (*entities.Organization, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for FindOrganization")
	}

	var r0 *entities.Organization
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.FilterOrganization) (*entities.Organization, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, repository.FilterOrganization) *entities.Organization); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.Organization)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, repository.FilterOrganization) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockOrganizationRepository_FindOrganization_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindOrganization'
type MockOrganizationRepository_FindOrganization_Call struct {
	*mock.Call
}

// FindOrganization is a helper method to define mock.On call
//   - ctx context.Context
//   - input repository.FilterOrganization
func (_e *MockOrganizationRepository_Expecter) FindOrganization(ctx interface{}, input interface{}) *MockOrganizationRepository_FindOrganization_Call {
	return &MockOrganizationRepository_FindOrganization_Call{Call: _e.mock.On("FindOrganization", ctx, input)}
}

func (_c *MockOrganizationRepository_FindOrganization_Call) Run(run func(ctx context.Context, input repository.FilterOrganization)) *MockOrganizationRepository_FindOrganization_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.FilterOrganization))
	})
	return _c
}

func (_c *MockOrganizationRepository_FindOrganization_Call) Return(_a0 *entities.Organization, _a1 error) *MockOrganizationRepository_FindOrganization_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockOrganizationRepository_FindOrganization_Call) RunAndReturn(run func(context.Context, repository.FilterOrganization) (*entities.Organization, error)) *MockOrganizationRepository_FindOrganization_Call {
	_c.Call.Return(run)
	return _c
}

// FindOrganizationByID provides a mock function with given fields: ctx, id
func (_m *MockOrganizationRepository) FindOrganizationByID(ctx context.Context, id uuid.UUID) (*entities.Organization, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for FindOrganizationByID")
	}

	var r0 *entities.Organization
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) (*entities.Organization, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) *entities.Organization); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.Organization)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uuid.UUID) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockOrganizationRepository_FindOrganizationByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindOrganizationByID'
type MockOrganizationRepository_FindOrganizationByID_Call struct {
	*mock.Call
}

// FindOrganizationByID is a helper method to define mock.On call
//   - ctx context.Context
//   - id uuid.UUID
func (_e *MockOrganizationRepository_Expecter) FindOrganizationByID(ctx interface{}, id interface{}) *MockOrganizationRepository_FindOrganizationByID_Call {
	return &MockOrganizationRepository_FindOrganizationByID_Call{Call: _e.mock.On("FindOrganizationByID", ctx, id)}
}

func (_c *MockOrganizationRepository_FindOrganizationByID_Call) Run(run func(ctx context.Context, id uuid.UUID)) *MockOrganizationRepository_FindOrganizationByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *MockOrganizationRepository_FindOrganizationByID_Call) Return(_a0 *entities.Organization, _a1 error) *MockOrganizationRepository_FindOrganizationByID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockOrganizationRepository_FindOrganizationByID_Call) RunAndReturn(run func(context.Context, uuid.UUID) (*entities.Organization, error)) *MockOrganizationRepository_FindOrganizationByID_Call {
	_c.Call.Return(run)
	return _c
}

// ListOrganizations provides a mock function with given fields: ctx, input
func (_m *MockOrganizationRepository) ListOrganizations(ctx context.Context, input dto.ListOrganizationsInput) (int, []entities.Organization, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for ListOrganizations")
	}

	var r0 int
	var r1 []entities.Organization
	var r2 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.ListOrganizationsInput) (int, []entities.Organization, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, dto.ListOrganizationsInput) int); ok {
		r0 = rf(ctx, input)
	} else {
		r0 = ret.Get(0).(int)
	}

	if rf, ok := ret.Get(1).(func(context.Context, dto.ListOrganizationsInput) []entities.Organization); ok {
		r1 = rf(ctx, input)
	} else {
		if ret.Get(1) != nil {
			r1 = ret.Get(1).([]entities.Organization)
		}
	}

	if rf, ok := ret.Get(2).(func(context.Context, dto.ListOrganizationsInput) error); ok {
		r2 = rf(ctx, input)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// MockOrganizationRepository_ListOrganizations_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListOrganizations'
type MockOrganizationRepository_ListOrganizations_Call struct {
	*mock.Call
}

// ListOrganizations is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.ListOrganizationsInput
func (_e *MockOrganizationRepository_Expecter) ListOrganizations(ctx interface{}, input interface{}) *MockOrganizationRepository_ListOrganizations_Call {
	return &MockOrganizationRepository_ListOrganizations_Call{Call: _e.mock.On("ListOrganizations", ctx, input)}
}

func (_c *MockOrganizationRepository_ListOrganizations_Call) Run(run func(ctx context.Context, input dto.ListOrganizationsInput)) *MockOrganizationRepository_ListOrganizations_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.ListOrganizationsInput))
	})
	return _c
}

func (_c *MockOrganizationRepository_ListOrganizations_Call) Return(_a0 int, _a1 []entities.Organization, _a2 error) *MockOrganizationRepository_ListOrganizations_Call {
	_c.Call.Return(_a0, _a1, _a2)
	return _c
}

func (_c *MockOrganizationRepository_ListOrganizations_Call) RunAndReturn(run func(context.Context, dto.ListOrganizationsInput) (int, []entities.Organization, error)) *MockOrganizationRepository_ListOrganizations_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockOrganizationRepository creates a new instance of MockOrganizationRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockOrganizationRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockOrganizationRepository {
	mock := &MockOrganizationRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
