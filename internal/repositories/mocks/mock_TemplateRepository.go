// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	entities "api-server/internal/entities"
	context "context"

	mock "github.com/stretchr/testify/mock"

	uuid "github.com/google/uuid"
)

// MockTemplateRepository is an autogenerated mock type for the TemplateRepository type
type MockTemplateRepository struct {
	mock.Mock
}

type MockTemplateRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockTemplateRepository) EXPECT() *MockTemplateRepository_Expecter {
	return &MockTemplateRepository_Expecter{mock: &_m.Mock}
}

// FindTemplate provides a mock function with given fields: ctx, id
func (_m *MockTemplateRepository) FindTemplate(ctx context.Context, id uuid.UUID) (*entities.RepoTemplate, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for FindTemplate")
	}

	var r0 *entities.RepoTemplate
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) (*entities.RepoTemplate, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) *entities.RepoTemplate); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.RepoTemplate)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uuid.UUID) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockTemplateRepository_FindTemplate_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindTemplate'
type MockTemplateRepository_FindTemplate_Call struct {
	*mock.Call
}

// FindTemplate is a helper method to define mock.On call
//   - ctx context.Context
//   - id uuid.UUID
func (_e *MockTemplateRepository_Expecter) FindTemplate(ctx interface{}, id interface{}) *MockTemplateRepository_FindTemplate_Call {
	return &MockTemplateRepository_FindTemplate_Call{Call: _e.mock.On("FindTemplate", ctx, id)}
}

func (_c *MockTemplateRepository_FindTemplate_Call) Run(run func(ctx context.Context, id uuid.UUID)) *MockTemplateRepository_FindTemplate_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *MockTemplateRepository_FindTemplate_Call) Return(_a0 *entities.RepoTemplate, _a1 error) *MockTemplateRepository_FindTemplate_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockTemplateRepository_FindTemplate_Call) RunAndReturn(run func(context.Context, uuid.UUID) (*entities.RepoTemplate, error)) *MockTemplateRepository_FindTemplate_Call {
	_c.Call.Return(run)
	return _c
}

// ListTemplates provides a mock function with given fields: ctx
func (_m *MockTemplateRepository) ListTemplates(ctx context.Context) ([]entities.RepoTemplate, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for ListTemplates")
	}

	var r0 []entities.RepoTemplate
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) ([]entities.RepoTemplate, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) []entities.RepoTemplate); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]entities.RepoTemplate)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockTemplateRepository_ListTemplates_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListTemplates'
type MockTemplateRepository_ListTemplates_Call struct {
	*mock.Call
}

// ListTemplates is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockTemplateRepository_Expecter) ListTemplates(ctx interface{}) *MockTemplateRepository_ListTemplates_Call {
	return &MockTemplateRepository_ListTemplates_Call{Call: _e.mock.On("ListTemplates", ctx)}
}

func (_c *MockTemplateRepository_ListTemplates_Call) Run(run func(ctx context.Context)) *MockTemplateRepository_ListTemplates_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockTemplateRepository_ListTemplates_Call) Return(_a0 []entities.RepoTemplate, _a1 error) *MockTemplateRepository_ListTemplates_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockTemplateRepository_ListTemplates_Call) RunAndReturn(run func(context.Context) ([]entities.RepoTemplate, error)) *MockTemplateRepository_ListTemplates_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockTemplateRepository creates a new instance of MockTemplateRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockTemplateRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockTemplateRepository {
	mock := &MockTemplateRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
