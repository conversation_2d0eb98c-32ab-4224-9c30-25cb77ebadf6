// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	entities "api-server/internal/entities"
	context "context"

	mock "github.com/stretchr/testify/mock"

	repository "api-server/internal/repositories"

	types "api-server/internal/types"

	uuid "github.com/google/uuid"
)

// MockOrgAccessTokenRepository is an autogenerated mock type for the OrgAccessTokenRepository type
type MockOrgAccessTokenRepository struct {
	mock.Mock
}

type MockOrgAccessTokenRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockOrgAccessTokenRepository) EXPECT() *MockOrgAccessTokenRepository_Expecter {
	return &MockOrgAccessTokenRepository_Expecter{mock: &_m.Mock}
}

// CountOrgAccessToken provides a mock function with given fields: ctx, in
func (_m *MockOrgAccessTokenRepository) CountOrgAccessToken(ctx context.Context, in repository.ListOrgAccessTokenQuery) (int64, error) {
	ret := _m.Called(ctx, in)

	if len(ret) == 0 {
		panic("no return value specified for CountOrgAccessToken")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.ListOrgAccessTokenQuery) (int64, error)); ok {
		return rf(ctx, in)
	}
	if rf, ok := ret.Get(0).(func(context.Context, repository.ListOrgAccessTokenQuery) int64); ok {
		r0 = rf(ctx, in)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, repository.ListOrgAccessTokenQuery) error); ok {
		r1 = rf(ctx, in)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockOrgAccessTokenRepository_CountOrgAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CountOrgAccessToken'
type MockOrgAccessTokenRepository_CountOrgAccessToken_Call struct {
	*mock.Call
}

// CountOrgAccessToken is a helper method to define mock.On call
//   - ctx context.Context
//   - in repository.ListOrgAccessTokenQuery
func (_e *MockOrgAccessTokenRepository_Expecter) CountOrgAccessToken(ctx interface{}, in interface{}) *MockOrgAccessTokenRepository_CountOrgAccessToken_Call {
	return &MockOrgAccessTokenRepository_CountOrgAccessToken_Call{Call: _e.mock.On("CountOrgAccessToken", ctx, in)}
}

func (_c *MockOrgAccessTokenRepository_CountOrgAccessToken_Call) Run(run func(ctx context.Context, in repository.ListOrgAccessTokenQuery)) *MockOrgAccessTokenRepository_CountOrgAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.ListOrgAccessTokenQuery))
	})
	return _c
}

func (_c *MockOrgAccessTokenRepository_CountOrgAccessToken_Call) Return(_a0 int64, _a1 error) *MockOrgAccessTokenRepository_CountOrgAccessToken_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockOrgAccessTokenRepository_CountOrgAccessToken_Call) RunAndReturn(run func(context.Context, repository.ListOrgAccessTokenQuery) (int64, error)) *MockOrgAccessTokenRepository_CountOrgAccessToken_Call {
	_c.Call.Return(run)
	return _c
}

// CreateOrgAccessToken provides a mock function with given fields: ctx, in
func (_m *MockOrgAccessTokenRepository) CreateOrgAccessToken(ctx context.Context, in repository.CreateOrgAccessTokenInput) (*entities.OrgToken, error) {
	ret := _m.Called(ctx, in)

	if len(ret) == 0 {
		panic("no return value specified for CreateOrgAccessToken")
	}

	var r0 *entities.OrgToken
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.CreateOrgAccessTokenInput) (*entities.OrgToken, error)); ok {
		return rf(ctx, in)
	}
	if rf, ok := ret.Get(0).(func(context.Context, repository.CreateOrgAccessTokenInput) *entities.OrgToken); ok {
		r0 = rf(ctx, in)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.OrgToken)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, repository.CreateOrgAccessTokenInput) error); ok {
		r1 = rf(ctx, in)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockOrgAccessTokenRepository_CreateOrgAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateOrgAccessToken'
type MockOrgAccessTokenRepository_CreateOrgAccessToken_Call struct {
	*mock.Call
}

// CreateOrgAccessToken is a helper method to define mock.On call
//   - ctx context.Context
//   - in repository.CreateOrgAccessTokenInput
func (_e *MockOrgAccessTokenRepository_Expecter) CreateOrgAccessToken(ctx interface{}, in interface{}) *MockOrgAccessTokenRepository_CreateOrgAccessToken_Call {
	return &MockOrgAccessTokenRepository_CreateOrgAccessToken_Call{Call: _e.mock.On("CreateOrgAccessToken", ctx, in)}
}

func (_c *MockOrgAccessTokenRepository_CreateOrgAccessToken_Call) Run(run func(ctx context.Context, in repository.CreateOrgAccessTokenInput)) *MockOrgAccessTokenRepository_CreateOrgAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.CreateOrgAccessTokenInput))
	})
	return _c
}

func (_c *MockOrgAccessTokenRepository_CreateOrgAccessToken_Call) Return(_a0 *entities.OrgToken, _a1 error) *MockOrgAccessTokenRepository_CreateOrgAccessToken_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockOrgAccessTokenRepository_CreateOrgAccessToken_Call) RunAndReturn(run func(context.Context, repository.CreateOrgAccessTokenInput) (*entities.OrgToken, error)) *MockOrgAccessTokenRepository_CreateOrgAccessToken_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteAllUserOrgAccessToken provides a mock function with given fields: ctx, orgId
func (_m *MockOrgAccessTokenRepository) DeleteAllUserOrgAccessToken(ctx context.Context, orgId uuid.UUID) error {
	ret := _m.Called(ctx, orgId)

	if len(ret) == 0 {
		panic("no return value specified for DeleteAllUserOrgAccessToken")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) error); ok {
		r0 = rf(ctx, orgId)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockOrgAccessTokenRepository_DeleteAllUserOrgAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteAllUserOrgAccessToken'
type MockOrgAccessTokenRepository_DeleteAllUserOrgAccessToken_Call struct {
	*mock.Call
}

// DeleteAllUserOrgAccessToken is a helper method to define mock.On call
//   - ctx context.Context
//   - orgId uuid.UUID
func (_e *MockOrgAccessTokenRepository_Expecter) DeleteAllUserOrgAccessToken(ctx interface{}, orgId interface{}) *MockOrgAccessTokenRepository_DeleteAllUserOrgAccessToken_Call {
	return &MockOrgAccessTokenRepository_DeleteAllUserOrgAccessToken_Call{Call: _e.mock.On("DeleteAllUserOrgAccessToken", ctx, orgId)}
}

func (_c *MockOrgAccessTokenRepository_DeleteAllUserOrgAccessToken_Call) Run(run func(ctx context.Context, orgId uuid.UUID)) *MockOrgAccessTokenRepository_DeleteAllUserOrgAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *MockOrgAccessTokenRepository_DeleteAllUserOrgAccessToken_Call) Return(_a0 error) *MockOrgAccessTokenRepository_DeleteAllUserOrgAccessToken_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockOrgAccessTokenRepository_DeleteAllUserOrgAccessToken_Call) RunAndReturn(run func(context.Context, uuid.UUID) error) *MockOrgAccessTokenRepository_DeleteAllUserOrgAccessToken_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteOrgAccessToken provides a mock function with given fields: ctx, in
func (_m *MockOrgAccessTokenRepository) DeleteOrgAccessToken(ctx context.Context, in repository.DeleteOrgAccessTokenInput) error {
	ret := _m.Called(ctx, in)

	if len(ret) == 0 {
		panic("no return value specified for DeleteOrgAccessToken")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.DeleteOrgAccessTokenInput) error); ok {
		r0 = rf(ctx, in)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockOrgAccessTokenRepository_DeleteOrgAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteOrgAccessToken'
type MockOrgAccessTokenRepository_DeleteOrgAccessToken_Call struct {
	*mock.Call
}

// DeleteOrgAccessToken is a helper method to define mock.On call
//   - ctx context.Context
//   - in repository.DeleteOrgAccessTokenInput
func (_e *MockOrgAccessTokenRepository_Expecter) DeleteOrgAccessToken(ctx interface{}, in interface{}) *MockOrgAccessTokenRepository_DeleteOrgAccessToken_Call {
	return &MockOrgAccessTokenRepository_DeleteOrgAccessToken_Call{Call: _e.mock.On("DeleteOrgAccessToken", ctx, in)}
}

func (_c *MockOrgAccessTokenRepository_DeleteOrgAccessToken_Call) Run(run func(ctx context.Context, in repository.DeleteOrgAccessTokenInput)) *MockOrgAccessTokenRepository_DeleteOrgAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.DeleteOrgAccessTokenInput))
	})
	return _c
}

func (_c *MockOrgAccessTokenRepository_DeleteOrgAccessToken_Call) Return(_a0 error) *MockOrgAccessTokenRepository_DeleteOrgAccessToken_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockOrgAccessTokenRepository_DeleteOrgAccessToken_Call) RunAndReturn(run func(context.Context, repository.DeleteOrgAccessTokenInput) error) *MockOrgAccessTokenRepository_DeleteOrgAccessToken_Call {
	_c.Call.Return(run)
	return _c
}

// FindOrgAccessToken provides a mock function with given fields: ctx, in
func (_m *MockOrgAccessTokenRepository) FindOrgAccessToken(ctx context.Context, in repository.FindOrgAccessTokenQuery) (*entities.OrgToken, error) {
	ret := _m.Called(ctx, in)

	if len(ret) == 0 {
		panic("no return value specified for FindOrgAccessToken")
	}

	var r0 *entities.OrgToken
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.FindOrgAccessTokenQuery) (*entities.OrgToken, error)); ok {
		return rf(ctx, in)
	}
	if rf, ok := ret.Get(0).(func(context.Context, repository.FindOrgAccessTokenQuery) *entities.OrgToken); ok {
		r0 = rf(ctx, in)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.OrgToken)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, repository.FindOrgAccessTokenQuery) error); ok {
		r1 = rf(ctx, in)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockOrgAccessTokenRepository_FindOrgAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindOrgAccessToken'
type MockOrgAccessTokenRepository_FindOrgAccessToken_Call struct {
	*mock.Call
}

// FindOrgAccessToken is a helper method to define mock.On call
//   - ctx context.Context
//   - in repository.FindOrgAccessTokenQuery
func (_e *MockOrgAccessTokenRepository_Expecter) FindOrgAccessToken(ctx interface{}, in interface{}) *MockOrgAccessTokenRepository_FindOrgAccessToken_Call {
	return &MockOrgAccessTokenRepository_FindOrgAccessToken_Call{Call: _e.mock.On("FindOrgAccessToken", ctx, in)}
}

func (_c *MockOrgAccessTokenRepository_FindOrgAccessToken_Call) Run(run func(ctx context.Context, in repository.FindOrgAccessTokenQuery)) *MockOrgAccessTokenRepository_FindOrgAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.FindOrgAccessTokenQuery))
	})
	return _c
}

func (_c *MockOrgAccessTokenRepository_FindOrgAccessToken_Call) Return(_a0 *entities.OrgToken, _a1 error) *MockOrgAccessTokenRepository_FindOrgAccessToken_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockOrgAccessTokenRepository_FindOrgAccessToken_Call) RunAndReturn(run func(context.Context, repository.FindOrgAccessTokenQuery) (*entities.OrgToken, error)) *MockOrgAccessTokenRepository_FindOrgAccessToken_Call {
	_c.Call.Return(run)
	return _c
}

// ListOrgAccessToken provides a mock function with given fields: ctx, pagination, order, in
func (_m *MockOrgAccessTokenRepository) ListOrgAccessToken(ctx context.Context, pagination types.Pagination, order types.OrderBy, in repository.ListOrgAccessTokenQuery) ([]entities.OrgToken, error) {
	ret := _m.Called(ctx, pagination, order, in)

	if len(ret) == 0 {
		panic("no return value specified for ListOrgAccessToken")
	}

	var r0 []entities.OrgToken
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, types.Pagination, types.OrderBy, repository.ListOrgAccessTokenQuery) ([]entities.OrgToken, error)); ok {
		return rf(ctx, pagination, order, in)
	}
	if rf, ok := ret.Get(0).(func(context.Context, types.Pagination, types.OrderBy, repository.ListOrgAccessTokenQuery) []entities.OrgToken); ok {
		r0 = rf(ctx, pagination, order, in)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]entities.OrgToken)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, types.Pagination, types.OrderBy, repository.ListOrgAccessTokenQuery) error); ok {
		r1 = rf(ctx, pagination, order, in)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockOrgAccessTokenRepository_ListOrgAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListOrgAccessToken'
type MockOrgAccessTokenRepository_ListOrgAccessToken_Call struct {
	*mock.Call
}

// ListOrgAccessToken is a helper method to define mock.On call
//   - ctx context.Context
//   - pagination types.Pagination
//   - order types.OrderBy
//   - in repository.ListOrgAccessTokenQuery
func (_e *MockOrgAccessTokenRepository_Expecter) ListOrgAccessToken(ctx interface{}, pagination interface{}, order interface{}, in interface{}) *MockOrgAccessTokenRepository_ListOrgAccessToken_Call {
	return &MockOrgAccessTokenRepository_ListOrgAccessToken_Call{Call: _e.mock.On("ListOrgAccessToken", ctx, pagination, order, in)}
}

func (_c *MockOrgAccessTokenRepository_ListOrgAccessToken_Call) Run(run func(ctx context.Context, pagination types.Pagination, order types.OrderBy, in repository.ListOrgAccessTokenQuery)) *MockOrgAccessTokenRepository_ListOrgAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(types.Pagination), args[2].(types.OrderBy), args[3].(repository.ListOrgAccessTokenQuery))
	})
	return _c
}

func (_c *MockOrgAccessTokenRepository_ListOrgAccessToken_Call) Return(_a0 []entities.OrgToken, _a1 error) *MockOrgAccessTokenRepository_ListOrgAccessToken_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockOrgAccessTokenRepository_ListOrgAccessToken_Call) RunAndReturn(run func(context.Context, types.Pagination, types.OrderBy, repository.ListOrgAccessTokenQuery) ([]entities.OrgToken, error)) *MockOrgAccessTokenRepository_ListOrgAccessToken_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockOrgAccessTokenRepository creates a new instance of MockOrgAccessTokenRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockOrgAccessTokenRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockOrgAccessTokenRepository {
	mock := &MockOrgAccessTokenRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
