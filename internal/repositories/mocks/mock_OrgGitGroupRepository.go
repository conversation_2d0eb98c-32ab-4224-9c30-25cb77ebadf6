// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	entities "api-server/internal/entities"
	context "context"

	mock "github.com/stretchr/testify/mock"

	repository "api-server/internal/repositories"

	uuid "github.com/google/uuid"
)

// MockOrgGitGroupRepository is an autogenerated mock type for the OrgGitGroupRepository type
type MockOrgGitGroupRepository struct {
	mock.Mock
}

type MockOrgGitGroupRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockOrgGitGroupRepository) EXPECT() *MockOrgGitGroupRepository_Expecter {
	return &MockOrgGitGroupRepository_Expecter{mock: &_m.Mock}
}

// CreateOrgGitGroup provides a mock function with given fields: ctx, in
func (_m *MockOrgGitGroupRepository) CreateOrgGitGroup(ctx context.Context, in repository.CreateOrgGitGroupInput) (*entities.OrgGitGroup, error) {
	ret := _m.Called(ctx, in)

	if len(ret) == 0 {
		panic("no return value specified for CreateOrgGitGroup")
	}

	var r0 *entities.OrgGitGroup
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.CreateOrgGitGroupInput) (*entities.OrgGitGroup, error)); ok {
		return rf(ctx, in)
	}
	if rf, ok := ret.Get(0).(func(context.Context, repository.CreateOrgGitGroupInput) *entities.OrgGitGroup); ok {
		r0 = rf(ctx, in)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.OrgGitGroup)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, repository.CreateOrgGitGroupInput) error); ok {
		r1 = rf(ctx, in)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockOrgGitGroupRepository_CreateOrgGitGroup_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateOrgGitGroup'
type MockOrgGitGroupRepository_CreateOrgGitGroup_Call struct {
	*mock.Call
}

// CreateOrgGitGroup is a helper method to define mock.On call
//   - ctx context.Context
//   - in repository.CreateOrgGitGroupInput
func (_e *MockOrgGitGroupRepository_Expecter) CreateOrgGitGroup(ctx interface{}, in interface{}) *MockOrgGitGroupRepository_CreateOrgGitGroup_Call {
	return &MockOrgGitGroupRepository_CreateOrgGitGroup_Call{Call: _e.mock.On("CreateOrgGitGroup", ctx, in)}
}

func (_c *MockOrgGitGroupRepository_CreateOrgGitGroup_Call) Run(run func(ctx context.Context, in repository.CreateOrgGitGroupInput)) *MockOrgGitGroupRepository_CreateOrgGitGroup_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.CreateOrgGitGroupInput))
	})
	return _c
}

func (_c *MockOrgGitGroupRepository_CreateOrgGitGroup_Call) Return(_a0 *entities.OrgGitGroup, _a1 error) *MockOrgGitGroupRepository_CreateOrgGitGroup_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockOrgGitGroupRepository_CreateOrgGitGroup_Call) RunAndReturn(run func(context.Context, repository.CreateOrgGitGroupInput) (*entities.OrgGitGroup, error)) *MockOrgGitGroupRepository_CreateOrgGitGroup_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteOrgGitGroup provides a mock function with given fields: ctx, orgID
func (_m *MockOrgGitGroupRepository) DeleteOrgGitGroup(ctx context.Context, orgID uuid.UUID) error {
	ret := _m.Called(ctx, orgID)

	if len(ret) == 0 {
		panic("no return value specified for DeleteOrgGitGroup")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) error); ok {
		r0 = rf(ctx, orgID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockOrgGitGroupRepository_DeleteOrgGitGroup_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteOrgGitGroup'
type MockOrgGitGroupRepository_DeleteOrgGitGroup_Call struct {
	*mock.Call
}

// DeleteOrgGitGroup is a helper method to define mock.On call
//   - ctx context.Context
//   - orgID uuid.UUID
func (_e *MockOrgGitGroupRepository_Expecter) DeleteOrgGitGroup(ctx interface{}, orgID interface{}) *MockOrgGitGroupRepository_DeleteOrgGitGroup_Call {
	return &MockOrgGitGroupRepository_DeleteOrgGitGroup_Call{Call: _e.mock.On("DeleteOrgGitGroup", ctx, orgID)}
}

func (_c *MockOrgGitGroupRepository_DeleteOrgGitGroup_Call) Run(run func(ctx context.Context, orgID uuid.UUID)) *MockOrgGitGroupRepository_DeleteOrgGitGroup_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *MockOrgGitGroupRepository_DeleteOrgGitGroup_Call) Return(_a0 error) *MockOrgGitGroupRepository_DeleteOrgGitGroup_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockOrgGitGroupRepository_DeleteOrgGitGroup_Call) RunAndReturn(run func(context.Context, uuid.UUID) error) *MockOrgGitGroupRepository_DeleteOrgGitGroup_Call {
	_c.Call.Return(run)
	return _c
}

// FindOrgGitGroup provides a mock function with given fields: ctx, orgID
func (_m *MockOrgGitGroupRepository) FindOrgGitGroup(ctx context.Context, orgID uuid.UUID) (*entities.DefaultGitGroup, error) {
	ret := _m.Called(ctx, orgID)

	if len(ret) == 0 {
		panic("no return value specified for FindOrgGitGroup")
	}

	var r0 *entities.DefaultGitGroup
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) (*entities.DefaultGitGroup, error)); ok {
		return rf(ctx, orgID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) *entities.DefaultGitGroup); ok {
		r0 = rf(ctx, orgID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.DefaultGitGroup)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uuid.UUID) error); ok {
		r1 = rf(ctx, orgID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockOrgGitGroupRepository_FindOrgGitGroup_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindOrgGitGroup'
type MockOrgGitGroupRepository_FindOrgGitGroup_Call struct {
	*mock.Call
}

// FindOrgGitGroup is a helper method to define mock.On call
//   - ctx context.Context
//   - orgID uuid.UUID
func (_e *MockOrgGitGroupRepository_Expecter) FindOrgGitGroup(ctx interface{}, orgID interface{}) *MockOrgGitGroupRepository_FindOrgGitGroup_Call {
	return &MockOrgGitGroupRepository_FindOrgGitGroup_Call{Call: _e.mock.On("FindOrgGitGroup", ctx, orgID)}
}

func (_c *MockOrgGitGroupRepository_FindOrgGitGroup_Call) Run(run func(ctx context.Context, orgID uuid.UUID)) *MockOrgGitGroupRepository_FindOrgGitGroup_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *MockOrgGitGroupRepository_FindOrgGitGroup_Call) Return(_a0 *entities.DefaultGitGroup, _a1 error) *MockOrgGitGroupRepository_FindOrgGitGroup_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockOrgGitGroupRepository_FindOrgGitGroup_Call) RunAndReturn(run func(context.Context, uuid.UUID) (*entities.DefaultGitGroup, error)) *MockOrgGitGroupRepository_FindOrgGitGroup_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockOrgGitGroupRepository creates a new instance of MockOrgGitGroupRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockOrgGitGroupRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockOrgGitGroupRepository {
	mock := &MockOrgGitGroupRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
