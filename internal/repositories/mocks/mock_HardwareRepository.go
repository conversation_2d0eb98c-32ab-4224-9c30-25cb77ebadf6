// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	entities "api-server/internal/entities"
	context "context"

	mock "github.com/stretchr/testify/mock"

	types "api-server/internal/types"

	uuid "github.com/google/uuid"
)

// MockHardwareRepository is an autogenerated mock type for the HardwareRepository type
type MockHardwareRepository struct {
	mock.Mock
}

type MockHardwareRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockHardwareRepository) EXPECT() *MockHardwareRepository_Expecter {
	return &MockHardwareRepository_Expecter{mock: &_m.Mock}
}

// CountHardware provides a mock function with given fields: ctx
func (_m *MockHardwareRepository) CountHardware(ctx context.Context) (int64, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for CountHardware")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) (int64, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) int64); ok {
		r0 = rf(ctx)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockHardwareRepository_CountHardware_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CountHardware'
type MockHardwareRepository_CountHardware_Call struct {
	*mock.Call
}

// CountHardware is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockHardwareRepository_Expecter) CountHardware(ctx interface{}) *MockHardwareRepository_CountHardware_Call {
	return &MockHardwareRepository_CountHardware_Call{Call: _e.mock.On("CountHardware", ctx)}
}

func (_c *MockHardwareRepository_CountHardware_Call) Run(run func(ctx context.Context)) *MockHardwareRepository_CountHardware_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockHardwareRepository_CountHardware_Call) Return(_a0 int64, _a1 error) *MockHardwareRepository_CountHardware_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockHardwareRepository_CountHardware_Call) RunAndReturn(run func(context.Context) (int64, error)) *MockHardwareRepository_CountHardware_Call {
	_c.Call.Return(run)
	return _c
}

// CreateHardware provides a mock function with given fields: ctx, hardware
func (_m *MockHardwareRepository) CreateHardware(ctx context.Context, hardware *entities.Hardware) (*entities.Hardware, error) {
	ret := _m.Called(ctx, hardware)

	if len(ret) == 0 {
		panic("no return value specified for CreateHardware")
	}

	var r0 *entities.Hardware
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *entities.Hardware) (*entities.Hardware, error)); ok {
		return rf(ctx, hardware)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *entities.Hardware) *entities.Hardware); ok {
		r0 = rf(ctx, hardware)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.Hardware)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *entities.Hardware) error); ok {
		r1 = rf(ctx, hardware)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockHardwareRepository_CreateHardware_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateHardware'
type MockHardwareRepository_CreateHardware_Call struct {
	*mock.Call
}

// CreateHardware is a helper method to define mock.On call
//   - ctx context.Context
//   - hardware *entities.Hardware
func (_e *MockHardwareRepository_Expecter) CreateHardware(ctx interface{}, hardware interface{}) *MockHardwareRepository_CreateHardware_Call {
	return &MockHardwareRepository_CreateHardware_Call{Call: _e.mock.On("CreateHardware", ctx, hardware)}
}

func (_c *MockHardwareRepository_CreateHardware_Call) Run(run func(ctx context.Context, hardware *entities.Hardware)) *MockHardwareRepository_CreateHardware_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*entities.Hardware))
	})
	return _c
}

func (_c *MockHardwareRepository_CreateHardware_Call) Return(_a0 *entities.Hardware, _a1 error) *MockHardwareRepository_CreateHardware_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockHardwareRepository_CreateHardware_Call) RunAndReturn(run func(context.Context, *entities.Hardware) (*entities.Hardware, error)) *MockHardwareRepository_CreateHardware_Call {
	_c.Call.Return(run)
	return _c
}

// FindHardwareByID provides a mock function with given fields: ctx, id
func (_m *MockHardwareRepository) FindHardwareByID(ctx context.Context, id uuid.UUID) (*entities.Hardware, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for FindHardwareByID")
	}

	var r0 *entities.Hardware
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) (*entities.Hardware, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) *entities.Hardware); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.Hardware)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uuid.UUID) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockHardwareRepository_FindHardwareByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindHardwareByID'
type MockHardwareRepository_FindHardwareByID_Call struct {
	*mock.Call
}

// FindHardwareByID is a helper method to define mock.On call
//   - ctx context.Context
//   - id uuid.UUID
func (_e *MockHardwareRepository_Expecter) FindHardwareByID(ctx interface{}, id interface{}) *MockHardwareRepository_FindHardwareByID_Call {
	return &MockHardwareRepository_FindHardwareByID_Call{Call: _e.mock.On("FindHardwareByID", ctx, id)}
}

func (_c *MockHardwareRepository_FindHardwareByID_Call) Run(run func(ctx context.Context, id uuid.UUID)) *MockHardwareRepository_FindHardwareByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *MockHardwareRepository_FindHardwareByID_Call) Return(_a0 *entities.Hardware, _a1 error) *MockHardwareRepository_FindHardwareByID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockHardwareRepository_FindHardwareByID_Call) RunAndReturn(run func(context.Context, uuid.UUID) (*entities.Hardware, error)) *MockHardwareRepository_FindHardwareByID_Call {
	_c.Call.Return(run)
	return _c
}

// ListHardware provides a mock function with given fields: ctx, pagination, order
func (_m *MockHardwareRepository) ListHardware(ctx context.Context, pagination types.Pagination, order types.OrderBy) ([]entities.Hardware, error) {
	ret := _m.Called(ctx, pagination, order)

	if len(ret) == 0 {
		panic("no return value specified for ListHardware")
	}

	var r0 []entities.Hardware
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, types.Pagination, types.OrderBy) ([]entities.Hardware, error)); ok {
		return rf(ctx, pagination, order)
	}
	if rf, ok := ret.Get(0).(func(context.Context, types.Pagination, types.OrderBy) []entities.Hardware); ok {
		r0 = rf(ctx, pagination, order)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]entities.Hardware)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, types.Pagination, types.OrderBy) error); ok {
		r1 = rf(ctx, pagination, order)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockHardwareRepository_ListHardware_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListHardware'
type MockHardwareRepository_ListHardware_Call struct {
	*mock.Call
}

// ListHardware is a helper method to define mock.On call
//   - ctx context.Context
//   - pagination types.Pagination
//   - order types.OrderBy
func (_e *MockHardwareRepository_Expecter) ListHardware(ctx interface{}, pagination interface{}, order interface{}) *MockHardwareRepository_ListHardware_Call {
	return &MockHardwareRepository_ListHardware_Call{Call: _e.mock.On("ListHardware", ctx, pagination, order)}
}

func (_c *MockHardwareRepository_ListHardware_Call) Run(run func(ctx context.Context, pagination types.Pagination, order types.OrderBy)) *MockHardwareRepository_ListHardware_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(types.Pagination), args[2].(types.OrderBy))
	})
	return _c
}

func (_c *MockHardwareRepository_ListHardware_Call) Return(_a0 []entities.Hardware, _a1 error) *MockHardwareRepository_ListHardware_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockHardwareRepository_ListHardware_Call) RunAndReturn(run func(context.Context, types.Pagination, types.OrderBy) ([]entities.Hardware, error)) *MockHardwareRepository_ListHardware_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateHardware provides a mock function with given fields: ctx, hardware
func (_m *MockHardwareRepository) UpdateHardware(ctx context.Context, hardware *entities.Hardware) error {
	ret := _m.Called(ctx, hardware)

	if len(ret) == 0 {
		panic("no return value specified for UpdateHardware")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *entities.Hardware) error); ok {
		r0 = rf(ctx, hardware)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockHardwareRepository_UpdateHardware_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateHardware'
type MockHardwareRepository_UpdateHardware_Call struct {
	*mock.Call
}

// UpdateHardware is a helper method to define mock.On call
//   - ctx context.Context
//   - hardware *entities.Hardware
func (_e *MockHardwareRepository_Expecter) UpdateHardware(ctx interface{}, hardware interface{}) *MockHardwareRepository_UpdateHardware_Call {
	return &MockHardwareRepository_UpdateHardware_Call{Call: _e.mock.On("UpdateHardware", ctx, hardware)}
}

func (_c *MockHardwareRepository_UpdateHardware_Call) Run(run func(ctx context.Context, hardware *entities.Hardware)) *MockHardwareRepository_UpdateHardware_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*entities.Hardware))
	})
	return _c
}

func (_c *MockHardwareRepository_UpdateHardware_Call) Return(_a0 error) *MockHardwareRepository_UpdateHardware_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockHardwareRepository_UpdateHardware_Call) RunAndReturn(run func(context.Context, *entities.Hardware) error) *MockHardwareRepository_UpdateHardware_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockHardwareRepository creates a new instance of MockHardwareRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockHardwareRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockHardwareRepository {
	mock := &MockHardwareRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
