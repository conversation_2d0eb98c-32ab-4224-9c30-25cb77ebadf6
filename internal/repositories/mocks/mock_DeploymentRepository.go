// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	context "context"

	clause "gorm.io/gorm/clause"

	entities "api-server/internal/entities"

	mock "github.com/stretchr/testify/mock"

	repository "api-server/internal/repositories"

	types "api-server/internal/types"

	uuid "github.com/google/uuid"
)

// MockDeploymentRepository is an autogenerated mock type for the DeploymentRepository type
type MockDeploymentRepository struct {
	mock.Mock
}

type MockDeploymentRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockDeploymentRepository) EXPECT() *MockDeploymentRepository_Expecter {
	return &MockDeploymentRepository_Expecter{mock: &_m.Mock}
}

// CountDeployment provides a mock function with given fields: ctx, in
func (_m *MockDeploymentRepository) CountDeployment(ctx context.Context, in repository.ListDeploymentInput) (int64, error) {
	ret := _m.Called(ctx, in)

	if len(ret) == 0 {
		panic("no return value specified for CountDeployment")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.ListDeploymentInput) (int64, error)); ok {
		return rf(ctx, in)
	}
	if rf, ok := ret.Get(0).(func(context.Context, repository.ListDeploymentInput) int64); ok {
		r0 = rf(ctx, in)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, repository.ListDeploymentInput) error); ok {
		r1 = rf(ctx, in)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDeploymentRepository_CountDeployment_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CountDeployment'
type MockDeploymentRepository_CountDeployment_Call struct {
	*mock.Call
}

// CountDeployment is a helper method to define mock.On call
//   - ctx context.Context
//   - in repository.ListDeploymentInput
func (_e *MockDeploymentRepository_Expecter) CountDeployment(ctx interface{}, in interface{}) *MockDeploymentRepository_CountDeployment_Call {
	return &MockDeploymentRepository_CountDeployment_Call{Call: _e.mock.On("CountDeployment", ctx, in)}
}

func (_c *MockDeploymentRepository_CountDeployment_Call) Run(run func(ctx context.Context, in repository.ListDeploymentInput)) *MockDeploymentRepository_CountDeployment_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.ListDeploymentInput))
	})
	return _c
}

func (_c *MockDeploymentRepository_CountDeployment_Call) Return(_a0 int64, _a1 error) *MockDeploymentRepository_CountDeployment_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDeploymentRepository_CountDeployment_Call) RunAndReturn(run func(context.Context, repository.ListDeploymentInput) (int64, error)) *MockDeploymentRepository_CountDeployment_Call {
	_c.Call.Return(run)
	return _c
}

// CreateDeployment provides a mock function with given fields: ctx, in
func (_m *MockDeploymentRepository) CreateDeployment(ctx context.Context, in repository.CreateDeploymentInput) (*entities.Deployment, error) {
	ret := _m.Called(ctx, in)

	if len(ret) == 0 {
		panic("no return value specified for CreateDeployment")
	}

	var r0 *entities.Deployment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.CreateDeploymentInput) (*entities.Deployment, error)); ok {
		return rf(ctx, in)
	}
	if rf, ok := ret.Get(0).(func(context.Context, repository.CreateDeploymentInput) *entities.Deployment); ok {
		r0 = rf(ctx, in)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.Deployment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, repository.CreateDeploymentInput) error); ok {
		r1 = rf(ctx, in)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDeploymentRepository_CreateDeployment_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateDeployment'
type MockDeploymentRepository_CreateDeployment_Call struct {
	*mock.Call
}

// CreateDeployment is a helper method to define mock.On call
//   - ctx context.Context
//   - in repository.CreateDeploymentInput
func (_e *MockDeploymentRepository_Expecter) CreateDeployment(ctx interface{}, in interface{}) *MockDeploymentRepository_CreateDeployment_Call {
	return &MockDeploymentRepository_CreateDeployment_Call{Call: _e.mock.On("CreateDeployment", ctx, in)}
}

func (_c *MockDeploymentRepository_CreateDeployment_Call) Run(run func(ctx context.Context, in repository.CreateDeploymentInput)) *MockDeploymentRepository_CreateDeployment_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.CreateDeploymentInput))
	})
	return _c
}

func (_c *MockDeploymentRepository_CreateDeployment_Call) Return(_a0 *entities.Deployment, _a1 error) *MockDeploymentRepository_CreateDeployment_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDeploymentRepository_CreateDeployment_Call) RunAndReturn(run func(context.Context, repository.CreateDeploymentInput) (*entities.Deployment, error)) *MockDeploymentRepository_CreateDeployment_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteDeploymentsByRepoID provides a mock function with given fields: ctx, repoID
func (_m *MockDeploymentRepository) DeleteDeploymentsByRepoID(ctx context.Context, repoID uuid.UUID) error {
	ret := _m.Called(ctx, repoID)

	if len(ret) == 0 {
		panic("no return value specified for DeleteDeploymentsByRepoID")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) error); ok {
		r0 = rf(ctx, repoID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockDeploymentRepository_DeleteDeploymentsByRepoID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteDeploymentsByRepoID'
type MockDeploymentRepository_DeleteDeploymentsByRepoID_Call struct {
	*mock.Call
}

// DeleteDeploymentsByRepoID is a helper method to define mock.On call
//   - ctx context.Context
//   - repoID uuid.UUID
func (_e *MockDeploymentRepository_Expecter) DeleteDeploymentsByRepoID(ctx interface{}, repoID interface{}) *MockDeploymentRepository_DeleteDeploymentsByRepoID_Call {
	return &MockDeploymentRepository_DeleteDeploymentsByRepoID_Call{Call: _e.mock.On("DeleteDeploymentsByRepoID", ctx, repoID)}
}

func (_c *MockDeploymentRepository_DeleteDeploymentsByRepoID_Call) Run(run func(ctx context.Context, repoID uuid.UUID)) *MockDeploymentRepository_DeleteDeploymentsByRepoID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *MockDeploymentRepository_DeleteDeploymentsByRepoID_Call) Return(_a0 error) *MockDeploymentRepository_DeleteDeploymentsByRepoID_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDeploymentRepository_DeleteDeploymentsByRepoID_Call) RunAndReturn(run func(context.Context, uuid.UUID) error) *MockDeploymentRepository_DeleteDeploymentsByRepoID_Call {
	_c.Call.Return(run)
	return _c
}

// FindDeployment provides a mock function with given fields: ctx, repoID, clauses
func (_m *MockDeploymentRepository) FindDeployment(ctx context.Context, repoID uuid.UUID, clauses ...clause.Expression) (*entities.Deployment, error) {
	_va := make([]interface{}, len(clauses))
	for _i := range clauses {
		_va[_i] = clauses[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, repoID)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for FindDeployment")
	}

	var r0 *entities.Deployment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID, ...clause.Expression) (*entities.Deployment, error)); ok {
		return rf(ctx, repoID, clauses...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID, ...clause.Expression) *entities.Deployment); ok {
		r0 = rf(ctx, repoID, clauses...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.Deployment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uuid.UUID, ...clause.Expression) error); ok {
		r1 = rf(ctx, repoID, clauses...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDeploymentRepository_FindDeployment_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindDeployment'
type MockDeploymentRepository_FindDeployment_Call struct {
	*mock.Call
}

// FindDeployment is a helper method to define mock.On call
//   - ctx context.Context
//   - repoID uuid.UUID
//   - clauses ...clause.Expression
func (_e *MockDeploymentRepository_Expecter) FindDeployment(ctx interface{}, repoID interface{}, clauses ...interface{}) *MockDeploymentRepository_FindDeployment_Call {
	return &MockDeploymentRepository_FindDeployment_Call{Call: _e.mock.On("FindDeployment",
		append([]interface{}{ctx, repoID}, clauses...)...)}
}

func (_c *MockDeploymentRepository_FindDeployment_Call) Run(run func(ctx context.Context, repoID uuid.UUID, clauses ...clause.Expression)) *MockDeploymentRepository_FindDeployment_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]clause.Expression, len(args)-2)
		for i, a := range args[2:] {
			if a != nil {
				variadicArgs[i] = a.(clause.Expression)
			}
		}
		run(args[0].(context.Context), args[1].(uuid.UUID), variadicArgs...)
	})
	return _c
}

func (_c *MockDeploymentRepository_FindDeployment_Call) Return(_a0 *entities.Deployment, _a1 error) *MockDeploymentRepository_FindDeployment_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDeploymentRepository_FindDeployment_Call) RunAndReturn(run func(context.Context, uuid.UUID, ...clause.Expression) (*entities.Deployment, error)) *MockDeploymentRepository_FindDeployment_Call {
	_c.Call.Return(run)
	return _c
}

// ListDeployment provides a mock function with given fields: ctx, pagination, order, in
func (_m *MockDeploymentRepository) ListDeployment(ctx context.Context, pagination types.Pagination, order types.OrderBy, in repository.ListDeploymentInput) ([]entities.Deployment, error) {
	ret := _m.Called(ctx, pagination, order, in)

	if len(ret) == 0 {
		panic("no return value specified for ListDeployment")
	}

	var r0 []entities.Deployment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, types.Pagination, types.OrderBy, repository.ListDeploymentInput) ([]entities.Deployment, error)); ok {
		return rf(ctx, pagination, order, in)
	}
	if rf, ok := ret.Get(0).(func(context.Context, types.Pagination, types.OrderBy, repository.ListDeploymentInput) []entities.Deployment); ok {
		r0 = rf(ctx, pagination, order, in)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]entities.Deployment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, types.Pagination, types.OrderBy, repository.ListDeploymentInput) error); ok {
		r1 = rf(ctx, pagination, order, in)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDeploymentRepository_ListDeployment_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListDeployment'
type MockDeploymentRepository_ListDeployment_Call struct {
	*mock.Call
}

// ListDeployment is a helper method to define mock.On call
//   - ctx context.Context
//   - pagination types.Pagination
//   - order types.OrderBy
//   - in repository.ListDeploymentInput
func (_e *MockDeploymentRepository_Expecter) ListDeployment(ctx interface{}, pagination interface{}, order interface{}, in interface{}) *MockDeploymentRepository_ListDeployment_Call {
	return &MockDeploymentRepository_ListDeployment_Call{Call: _e.mock.On("ListDeployment", ctx, pagination, order, in)}
}

func (_c *MockDeploymentRepository_ListDeployment_Call) Run(run func(ctx context.Context, pagination types.Pagination, order types.OrderBy, in repository.ListDeploymentInput)) *MockDeploymentRepository_ListDeployment_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(types.Pagination), args[2].(types.OrderBy), args[3].(repository.ListDeploymentInput))
	})
	return _c
}

func (_c *MockDeploymentRepository_ListDeployment_Call) Return(_a0 []entities.Deployment, _a1 error) *MockDeploymentRepository_ListDeployment_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDeploymentRepository_ListDeployment_Call) RunAndReturn(run func(context.Context, types.Pagination, types.OrderBy, repository.ListDeploymentInput) ([]entities.Deployment, error)) *MockDeploymentRepository_ListDeployment_Call {
	_c.Call.Return(run)
	return _c
}

// RemoveUserInDeployments provides a mock function with given fields: ctx, in
func (_m *MockDeploymentRepository) RemoveUserInDeployments(ctx context.Context, in repository.RemoveUserInDeploymentsInput) error {
	ret := _m.Called(ctx, in)

	if len(ret) == 0 {
		panic("no return value specified for RemoveUserInDeployments")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.RemoveUserInDeploymentsInput) error); ok {
		r0 = rf(ctx, in)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockDeploymentRepository_RemoveUserInDeployments_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RemoveUserInDeployments'
type MockDeploymentRepository_RemoveUserInDeployments_Call struct {
	*mock.Call
}

// RemoveUserInDeployments is a helper method to define mock.On call
//   - ctx context.Context
//   - in repository.RemoveUserInDeploymentsInput
func (_e *MockDeploymentRepository_Expecter) RemoveUserInDeployments(ctx interface{}, in interface{}) *MockDeploymentRepository_RemoveUserInDeployments_Call {
	return &MockDeploymentRepository_RemoveUserInDeployments_Call{Call: _e.mock.On("RemoveUserInDeployments", ctx, in)}
}

func (_c *MockDeploymentRepository_RemoveUserInDeployments_Call) Run(run func(ctx context.Context, in repository.RemoveUserInDeploymentsInput)) *MockDeploymentRepository_RemoveUserInDeployments_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.RemoveUserInDeploymentsInput))
	})
	return _c
}

func (_c *MockDeploymentRepository_RemoveUserInDeployments_Call) Return(_a0 error) *MockDeploymentRepository_RemoveUserInDeployments_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDeploymentRepository_RemoveUserInDeployments_Call) RunAndReturn(run func(context.Context, repository.RemoveUserInDeploymentsInput) error) *MockDeploymentRepository_RemoveUserInDeployments_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateDeployment provides a mock function with given fields: ctx, in
func (_m *MockDeploymentRepository) UpdateDeployment(ctx context.Context, in repository.UpdateDeploymentInput) (*entities.Deployment, error) {
	ret := _m.Called(ctx, in)

	if len(ret) == 0 {
		panic("no return value specified for UpdateDeployment")
	}

	var r0 *entities.Deployment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.UpdateDeploymentInput) (*entities.Deployment, error)); ok {
		return rf(ctx, in)
	}
	if rf, ok := ret.Get(0).(func(context.Context, repository.UpdateDeploymentInput) *entities.Deployment); ok {
		r0 = rf(ctx, in)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.Deployment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, repository.UpdateDeploymentInput) error); ok {
		r1 = rf(ctx, in)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDeploymentRepository_UpdateDeployment_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateDeployment'
type MockDeploymentRepository_UpdateDeployment_Call struct {
	*mock.Call
}

// UpdateDeployment is a helper method to define mock.On call
//   - ctx context.Context
//   - in repository.UpdateDeploymentInput
func (_e *MockDeploymentRepository_Expecter) UpdateDeployment(ctx interface{}, in interface{}) *MockDeploymentRepository_UpdateDeployment_Call {
	return &MockDeploymentRepository_UpdateDeployment_Call{Call: _e.mock.On("UpdateDeployment", ctx, in)}
}

func (_c *MockDeploymentRepository_UpdateDeployment_Call) Run(run func(ctx context.Context, in repository.UpdateDeploymentInput)) *MockDeploymentRepository_UpdateDeployment_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.UpdateDeploymentInput))
	})
	return _c
}

func (_c *MockDeploymentRepository_UpdateDeployment_Call) Return(_a0 *entities.Deployment, _a1 error) *MockDeploymentRepository_UpdateDeployment_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDeploymentRepository_UpdateDeployment_Call) RunAndReturn(run func(context.Context, repository.UpdateDeploymentInput) (*entities.Deployment, error)) *MockDeploymentRepository_UpdateDeployment_Call {
	_c.Call.Return(run)
	return _c
}

// UpsertDeployment provides a mock function with given fields: ctx, in
func (_m *MockDeploymentRepository) UpsertDeployment(ctx context.Context, in repository.CreateDeploymentInput) (*entities.Deployment, error) {
	ret := _m.Called(ctx, in)

	if len(ret) == 0 {
		panic("no return value specified for UpsertDeployment")
	}

	var r0 *entities.Deployment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.CreateDeploymentInput) (*entities.Deployment, error)); ok {
		return rf(ctx, in)
	}
	if rf, ok := ret.Get(0).(func(context.Context, repository.CreateDeploymentInput) *entities.Deployment); ok {
		r0 = rf(ctx, in)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.Deployment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, repository.CreateDeploymentInput) error); ok {
		r1 = rf(ctx, in)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDeploymentRepository_UpsertDeployment_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpsertDeployment'
type MockDeploymentRepository_UpsertDeployment_Call struct {
	*mock.Call
}

// UpsertDeployment is a helper method to define mock.On call
//   - ctx context.Context
//   - in repository.CreateDeploymentInput
func (_e *MockDeploymentRepository_Expecter) UpsertDeployment(ctx interface{}, in interface{}) *MockDeploymentRepository_UpsertDeployment_Call {
	return &MockDeploymentRepository_UpsertDeployment_Call{Call: _e.mock.On("UpsertDeployment", ctx, in)}
}

func (_c *MockDeploymentRepository_UpsertDeployment_Call) Run(run func(ctx context.Context, in repository.CreateDeploymentInput)) *MockDeploymentRepository_UpsertDeployment_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.CreateDeploymentInput))
	})
	return _c
}

func (_c *MockDeploymentRepository_UpsertDeployment_Call) Return(_a0 *entities.Deployment, _a1 error) *MockDeploymentRepository_UpsertDeployment_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDeploymentRepository_UpsertDeployment_Call) RunAndReturn(run func(context.Context, repository.CreateDeploymentInput) (*entities.Deployment, error)) *MockDeploymentRepository_UpsertDeployment_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockDeploymentRepository creates a new instance of MockDeploymentRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockDeploymentRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockDeploymentRepository {
	mock := &MockDeploymentRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
