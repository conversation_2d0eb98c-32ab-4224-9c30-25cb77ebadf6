// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	entities "api-server/internal/entities"
	context "context"

	mock "github.com/stretchr/testify/mock"

	repository "api-server/internal/repositories"

	types "api-server/internal/types"
)

// MockRepoAccessTokenRepository is an autogenerated mock type for the RepoAccessTokenRepository type
type MockRepoAccessTokenRepository struct {
	mock.Mock
}

type MockRepoAccessTokenRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockRepoAccessTokenRepository) EXPECT() *MockRepoAccessTokenRepository_Expecter {
	return &MockRepoAccessTokenRepository_Expecter{mock: &_m.Mock}
}

// CountRepoAccessToken provides a mock function with given fields: ctx, in
func (_m *MockRepoAccessTokenRepository) CountRepoAccessToken(ctx context.Context, in repository.ListRepoAccessTokenQuery) (int64, error) {
	ret := _m.Called(ctx, in)

	if len(ret) == 0 {
		panic("no return value specified for CountRepoAccessToken")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.ListRepoAccessTokenQuery) (int64, error)); ok {
		return rf(ctx, in)
	}
	if rf, ok := ret.Get(0).(func(context.Context, repository.ListRepoAccessTokenQuery) int64); ok {
		r0 = rf(ctx, in)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, repository.ListRepoAccessTokenQuery) error); ok {
		r1 = rf(ctx, in)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepoAccessTokenRepository_CountRepoAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CountRepoAccessToken'
type MockRepoAccessTokenRepository_CountRepoAccessToken_Call struct {
	*mock.Call
}

// CountRepoAccessToken is a helper method to define mock.On call
//   - ctx context.Context
//   - in repository.ListRepoAccessTokenQuery
func (_e *MockRepoAccessTokenRepository_Expecter) CountRepoAccessToken(ctx interface{}, in interface{}) *MockRepoAccessTokenRepository_CountRepoAccessToken_Call {
	return &MockRepoAccessTokenRepository_CountRepoAccessToken_Call{Call: _e.mock.On("CountRepoAccessToken", ctx, in)}
}

func (_c *MockRepoAccessTokenRepository_CountRepoAccessToken_Call) Run(run func(ctx context.Context, in repository.ListRepoAccessTokenQuery)) *MockRepoAccessTokenRepository_CountRepoAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.ListRepoAccessTokenQuery))
	})
	return _c
}

func (_c *MockRepoAccessTokenRepository_CountRepoAccessToken_Call) Return(_a0 int64, _a1 error) *MockRepoAccessTokenRepository_CountRepoAccessToken_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepoAccessTokenRepository_CountRepoAccessToken_Call) RunAndReturn(run func(context.Context, repository.ListRepoAccessTokenQuery) (int64, error)) *MockRepoAccessTokenRepository_CountRepoAccessToken_Call {
	_c.Call.Return(run)
	return _c
}

// CreateRepoAccessToken provides a mock function with given fields: ctx, in
func (_m *MockRepoAccessTokenRepository) CreateRepoAccessToken(ctx context.Context, in repository.CreateRepoAccessTokenInput) (*entities.RepoAccessToken, error) {
	ret := _m.Called(ctx, in)

	if len(ret) == 0 {
		panic("no return value specified for CreateRepoAccessToken")
	}

	var r0 *entities.RepoAccessToken
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.CreateRepoAccessTokenInput) (*entities.RepoAccessToken, error)); ok {
		return rf(ctx, in)
	}
	if rf, ok := ret.Get(0).(func(context.Context, repository.CreateRepoAccessTokenInput) *entities.RepoAccessToken); ok {
		r0 = rf(ctx, in)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.RepoAccessToken)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, repository.CreateRepoAccessTokenInput) error); ok {
		r1 = rf(ctx, in)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepoAccessTokenRepository_CreateRepoAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateRepoAccessToken'
type MockRepoAccessTokenRepository_CreateRepoAccessToken_Call struct {
	*mock.Call
}

// CreateRepoAccessToken is a helper method to define mock.On call
//   - ctx context.Context
//   - in repository.CreateRepoAccessTokenInput
func (_e *MockRepoAccessTokenRepository_Expecter) CreateRepoAccessToken(ctx interface{}, in interface{}) *MockRepoAccessTokenRepository_CreateRepoAccessToken_Call {
	return &MockRepoAccessTokenRepository_CreateRepoAccessToken_Call{Call: _e.mock.On("CreateRepoAccessToken", ctx, in)}
}

func (_c *MockRepoAccessTokenRepository_CreateRepoAccessToken_Call) Run(run func(ctx context.Context, in repository.CreateRepoAccessTokenInput)) *MockRepoAccessTokenRepository_CreateRepoAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.CreateRepoAccessTokenInput))
	})
	return _c
}

func (_c *MockRepoAccessTokenRepository_CreateRepoAccessToken_Call) Return(_a0 *entities.RepoAccessToken, _a1 error) *MockRepoAccessTokenRepository_CreateRepoAccessToken_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepoAccessTokenRepository_CreateRepoAccessToken_Call) RunAndReturn(run func(context.Context, repository.CreateRepoAccessTokenInput) (*entities.RepoAccessToken, error)) *MockRepoAccessTokenRepository_CreateRepoAccessToken_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteRepoAccessToken provides a mock function with given fields: ctx, in
func (_m *MockRepoAccessTokenRepository) DeleteRepoAccessToken(ctx context.Context, in repository.DeleteRepoAccessTokenInput) error {
	ret := _m.Called(ctx, in)

	if len(ret) == 0 {
		panic("no return value specified for DeleteRepoAccessToken")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.DeleteRepoAccessTokenInput) error); ok {
		r0 = rf(ctx, in)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepoAccessTokenRepository_DeleteRepoAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteRepoAccessToken'
type MockRepoAccessTokenRepository_DeleteRepoAccessToken_Call struct {
	*mock.Call
}

// DeleteRepoAccessToken is a helper method to define mock.On call
//   - ctx context.Context
//   - in repository.DeleteRepoAccessTokenInput
func (_e *MockRepoAccessTokenRepository_Expecter) DeleteRepoAccessToken(ctx interface{}, in interface{}) *MockRepoAccessTokenRepository_DeleteRepoAccessToken_Call {
	return &MockRepoAccessTokenRepository_DeleteRepoAccessToken_Call{Call: _e.mock.On("DeleteRepoAccessToken", ctx, in)}
}

func (_c *MockRepoAccessTokenRepository_DeleteRepoAccessToken_Call) Run(run func(ctx context.Context, in repository.DeleteRepoAccessTokenInput)) *MockRepoAccessTokenRepository_DeleteRepoAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.DeleteRepoAccessTokenInput))
	})
	return _c
}

func (_c *MockRepoAccessTokenRepository_DeleteRepoAccessToken_Call) Return(_a0 error) *MockRepoAccessTokenRepository_DeleteRepoAccessToken_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepoAccessTokenRepository_DeleteRepoAccessToken_Call) RunAndReturn(run func(context.Context, repository.DeleteRepoAccessTokenInput) error) *MockRepoAccessTokenRepository_DeleteRepoAccessToken_Call {
	_c.Call.Return(run)
	return _c
}

// FindRepoAccessToken provides a mock function with given fields: ctx, in
func (_m *MockRepoAccessTokenRepository) FindRepoAccessToken(ctx context.Context, in repository.FindRepoAccessTokenQuery) (*entities.RepoAccessToken, error) {
	ret := _m.Called(ctx, in)

	if len(ret) == 0 {
		panic("no return value specified for FindRepoAccessToken")
	}

	var r0 *entities.RepoAccessToken
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.FindRepoAccessTokenQuery) (*entities.RepoAccessToken, error)); ok {
		return rf(ctx, in)
	}
	if rf, ok := ret.Get(0).(func(context.Context, repository.FindRepoAccessTokenQuery) *entities.RepoAccessToken); ok {
		r0 = rf(ctx, in)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.RepoAccessToken)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, repository.FindRepoAccessTokenQuery) error); ok {
		r1 = rf(ctx, in)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepoAccessTokenRepository_FindRepoAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindRepoAccessToken'
type MockRepoAccessTokenRepository_FindRepoAccessToken_Call struct {
	*mock.Call
}

// FindRepoAccessToken is a helper method to define mock.On call
//   - ctx context.Context
//   - in repository.FindRepoAccessTokenQuery
func (_e *MockRepoAccessTokenRepository_Expecter) FindRepoAccessToken(ctx interface{}, in interface{}) *MockRepoAccessTokenRepository_FindRepoAccessToken_Call {
	return &MockRepoAccessTokenRepository_FindRepoAccessToken_Call{Call: _e.mock.On("FindRepoAccessToken", ctx, in)}
}

func (_c *MockRepoAccessTokenRepository_FindRepoAccessToken_Call) Run(run func(ctx context.Context, in repository.FindRepoAccessTokenQuery)) *MockRepoAccessTokenRepository_FindRepoAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.FindRepoAccessTokenQuery))
	})
	return _c
}

func (_c *MockRepoAccessTokenRepository_FindRepoAccessToken_Call) Return(_a0 *entities.RepoAccessToken, _a1 error) *MockRepoAccessTokenRepository_FindRepoAccessToken_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepoAccessTokenRepository_FindRepoAccessToken_Call) RunAndReturn(run func(context.Context, repository.FindRepoAccessTokenQuery) (*entities.RepoAccessToken, error)) *MockRepoAccessTokenRepository_FindRepoAccessToken_Call {
	_c.Call.Return(run)
	return _c
}

// ListRepoAccessToken provides a mock function with given fields: ctx, pagination, order, in
func (_m *MockRepoAccessTokenRepository) ListRepoAccessToken(ctx context.Context, pagination types.Pagination, order types.OrderBy, in repository.ListRepoAccessTokenQuery) ([]entities.RepoAccessToken, error) {
	ret := _m.Called(ctx, pagination, order, in)

	if len(ret) == 0 {
		panic("no return value specified for ListRepoAccessToken")
	}

	var r0 []entities.RepoAccessToken
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, types.Pagination, types.OrderBy, repository.ListRepoAccessTokenQuery) ([]entities.RepoAccessToken, error)); ok {
		return rf(ctx, pagination, order, in)
	}
	if rf, ok := ret.Get(0).(func(context.Context, types.Pagination, types.OrderBy, repository.ListRepoAccessTokenQuery) []entities.RepoAccessToken); ok {
		r0 = rf(ctx, pagination, order, in)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]entities.RepoAccessToken)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, types.Pagination, types.OrderBy, repository.ListRepoAccessTokenQuery) error); ok {
		r1 = rf(ctx, pagination, order, in)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepoAccessTokenRepository_ListRepoAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListRepoAccessToken'
type MockRepoAccessTokenRepository_ListRepoAccessToken_Call struct {
	*mock.Call
}

// ListRepoAccessToken is a helper method to define mock.On call
//   - ctx context.Context
//   - pagination types.Pagination
//   - order types.OrderBy
//   - in repository.ListRepoAccessTokenQuery
func (_e *MockRepoAccessTokenRepository_Expecter) ListRepoAccessToken(ctx interface{}, pagination interface{}, order interface{}, in interface{}) *MockRepoAccessTokenRepository_ListRepoAccessToken_Call {
	return &MockRepoAccessTokenRepository_ListRepoAccessToken_Call{Call: _e.mock.On("ListRepoAccessToken", ctx, pagination, order, in)}
}

func (_c *MockRepoAccessTokenRepository_ListRepoAccessToken_Call) Run(run func(ctx context.Context, pagination types.Pagination, order types.OrderBy, in repository.ListRepoAccessTokenQuery)) *MockRepoAccessTokenRepository_ListRepoAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(types.Pagination), args[2].(types.OrderBy), args[3].(repository.ListRepoAccessTokenQuery))
	})
	return _c
}

func (_c *MockRepoAccessTokenRepository_ListRepoAccessToken_Call) Return(_a0 []entities.RepoAccessToken, _a1 error) *MockRepoAccessTokenRepository_ListRepoAccessToken_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepoAccessTokenRepository_ListRepoAccessToken_Call) RunAndReturn(run func(context.Context, types.Pagination, types.OrderBy, repository.ListRepoAccessTokenQuery) ([]entities.RepoAccessToken, error)) *MockRepoAccessTokenRepository_ListRepoAccessToken_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockRepoAccessTokenRepository creates a new instance of MockRepoAccessTokenRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockRepoAccessTokenRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockRepoAccessTokenRepository {
	mock := &MockRepoAccessTokenRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
