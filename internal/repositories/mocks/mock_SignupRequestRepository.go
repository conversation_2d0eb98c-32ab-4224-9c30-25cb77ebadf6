// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	entities "api-server/internal/entities"
	context "context"

	mock "github.com/stretchr/testify/mock"

	uuid "github.com/google/uuid"
)

// MockSignupRequestRepository is an autogenerated mock type for the SignupRequestRepository type
type MockSignupRequestRepository struct {
	mock.Mock
}

type MockSignupRequestRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockSignupRequestRepository) EXPECT() *MockSignupRequestRepository_Expecter {
	return &MockSignupRequestRepository_Expecter{mock: &_m.Mock}
}

// GetSignUpRequestByID provides a mock function with given fields: ctx, id
func (_m *MockSignupRequestRepository) GetSignUpRequestByID(ctx context.Context, id uuid.UUID) (*entities.SignupRequest, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetSignUpRequestByID")
	}

	var r0 *entities.SignupRequest
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) (*entities.SignupRequest, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) *entities.SignupRequest); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.SignupRequest)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uuid.UUID) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockSignupRequestRepository_GetSignUpRequestByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetSignUpRequestByID'
type MockSignupRequestRepository_GetSignUpRequestByID_Call struct {
	*mock.Call
}

// GetSignUpRequestByID is a helper method to define mock.On call
//   - ctx context.Context
//   - id uuid.UUID
func (_e *MockSignupRequestRepository_Expecter) GetSignUpRequestByID(ctx interface{}, id interface{}) *MockSignupRequestRepository_GetSignUpRequestByID_Call {
	return &MockSignupRequestRepository_GetSignUpRequestByID_Call{Call: _e.mock.On("GetSignUpRequestByID", ctx, id)}
}

func (_c *MockSignupRequestRepository_GetSignUpRequestByID_Call) Run(run func(ctx context.Context, id uuid.UUID)) *MockSignupRequestRepository_GetSignUpRequestByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *MockSignupRequestRepository_GetSignUpRequestByID_Call) Return(_a0 *entities.SignupRequest, _a1 error) *MockSignupRequestRepository_GetSignUpRequestByID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockSignupRequestRepository_GetSignUpRequestByID_Call) RunAndReturn(run func(context.Context, uuid.UUID) (*entities.SignupRequest, error)) *MockSignupRequestRepository_GetSignUpRequestByID_Call {
	_c.Call.Return(run)
	return _c
}

// ListSignUpRequests provides a mock function with given fields: ctx
func (_m *MockSignupRequestRepository) ListSignUpRequests(ctx context.Context) ([]entities.SignupRequest, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for ListSignUpRequests")
	}

	var r0 []entities.SignupRequest
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) ([]entities.SignupRequest, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) []entities.SignupRequest); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]entities.SignupRequest)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockSignupRequestRepository_ListSignUpRequests_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListSignUpRequests'
type MockSignupRequestRepository_ListSignUpRequests_Call struct {
	*mock.Call
}

// ListSignUpRequests is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockSignupRequestRepository_Expecter) ListSignUpRequests(ctx interface{}) *MockSignupRequestRepository_ListSignUpRequests_Call {
	return &MockSignupRequestRepository_ListSignUpRequests_Call{Call: _e.mock.On("ListSignUpRequests", ctx)}
}

func (_c *MockSignupRequestRepository_ListSignUpRequests_Call) Run(run func(ctx context.Context)) *MockSignupRequestRepository_ListSignUpRequests_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockSignupRequestRepository_ListSignUpRequests_Call) Return(_a0 []entities.SignupRequest, _a1 error) *MockSignupRequestRepository_ListSignUpRequests_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockSignupRequestRepository_ListSignUpRequests_Call) RunAndReturn(run func(context.Context) ([]entities.SignupRequest, error)) *MockSignupRequestRepository_ListSignUpRequests_Call {
	_c.Call.Return(run)
	return _c
}

// UpsertSignupRequest provides a mock function with given fields: ctx, input
func (_m *MockSignupRequestRepository) UpsertSignupRequest(ctx context.Context, input *entities.SignupRequest) error {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for UpsertSignupRequest")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *entities.SignupRequest) error); ok {
		r0 = rf(ctx, input)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockSignupRequestRepository_UpsertSignupRequest_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpsertSignupRequest'
type MockSignupRequestRepository_UpsertSignupRequest_Call struct {
	*mock.Call
}

// UpsertSignupRequest is a helper method to define mock.On call
//   - ctx context.Context
//   - input *entities.SignupRequest
func (_e *MockSignupRequestRepository_Expecter) UpsertSignupRequest(ctx interface{}, input interface{}) *MockSignupRequestRepository_UpsertSignupRequest_Call {
	return &MockSignupRequestRepository_UpsertSignupRequest_Call{Call: _e.mock.On("UpsertSignupRequest", ctx, input)}
}

func (_c *MockSignupRequestRepository_UpsertSignupRequest_Call) Run(run func(ctx context.Context, input *entities.SignupRequest)) *MockSignupRequestRepository_UpsertSignupRequest_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*entities.SignupRequest))
	})
	return _c
}

func (_c *MockSignupRequestRepository_UpsertSignupRequest_Call) Return(_a0 error) *MockSignupRequestRepository_UpsertSignupRequest_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockSignupRequestRepository_UpsertSignupRequest_Call) RunAndReturn(run func(context.Context, *entities.SignupRequest) error) *MockSignupRequestRepository_UpsertSignupRequest_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockSignupRequestRepository creates a new instance of MockSignupRequestRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockSignupRequestRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockSignupRequestRepository {
	mock := &MockSignupRequestRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
