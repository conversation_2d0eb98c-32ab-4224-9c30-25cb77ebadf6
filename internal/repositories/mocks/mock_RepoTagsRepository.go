// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	dto "api-server/internal/dto"
	entities "api-server/internal/entities"
	context "context"

	mock "github.com/stretchr/testify/mock"

	repository "api-server/internal/repositories"
)

// MockRepoTagsRepository is an autogenerated mock type for the RepoTagsRepository type
type MockRepoTagsRepository struct {
	mock.Mock
}

type MockRepoTagsRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockRepoTagsRepository) EXPECT() *MockRepoTagsRepository_Expecter {
	return &MockRepoTagsRepository_Expecter{mock: &_m.Mock}
}

// FindRepoTag provides a mock function with given fields: ctx, filter
func (_m *MockRepoTagsRepository) FindRepoTag(ctx context.Context, filter repository.FilterTagInput) (*entities.Tag, error) {
	ret := _m.Called(ctx, filter)

	if len(ret) == 0 {
		panic("no return value specified for FindRepoTag")
	}

	var r0 *entities.Tag
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.FilterTagInput) (*entities.Tag, error)); ok {
		return rf(ctx, filter)
	}
	if rf, ok := ret.Get(0).(func(context.Context, repository.FilterTagInput) *entities.Tag); ok {
		r0 = rf(ctx, filter)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.Tag)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, repository.FilterTagInput) error); ok {
		r1 = rf(ctx, filter)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepoTagsRepository_FindRepoTag_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindRepoTag'
type MockRepoTagsRepository_FindRepoTag_Call struct {
	*mock.Call
}

// FindRepoTag is a helper method to define mock.On call
//   - ctx context.Context
//   - filter repository.FilterTagInput
func (_e *MockRepoTagsRepository_Expecter) FindRepoTag(ctx interface{}, filter interface{}) *MockRepoTagsRepository_FindRepoTag_Call {
	return &MockRepoTagsRepository_FindRepoTag_Call{Call: _e.mock.On("FindRepoTag", ctx, filter)}
}

func (_c *MockRepoTagsRepository_FindRepoTag_Call) Run(run func(ctx context.Context, filter repository.FilterTagInput)) *MockRepoTagsRepository_FindRepoTag_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.FilterTagInput))
	})
	return _c
}

func (_c *MockRepoTagsRepository_FindRepoTag_Call) Return(_a0 *entities.Tag, _a1 error) *MockRepoTagsRepository_FindRepoTag_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepoTagsRepository_FindRepoTag_Call) RunAndReturn(run func(context.Context, repository.FilterTagInput) (*entities.Tag, error)) *MockRepoTagsRepository_FindRepoTag_Call {
	_c.Call.Return(run)
	return _c
}

// ListRepoTags provides a mock function with given fields: ctx, input
func (_m *MockRepoTagsRepository) ListRepoTags(ctx context.Context, input dto.ListRepoTagsInput) ([]entities.Tag, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for ListRepoTags")
	}

	var r0 []entities.Tag
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.ListRepoTagsInput) ([]entities.Tag, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, dto.ListRepoTagsInput) []entities.Tag); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]entities.Tag)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, dto.ListRepoTagsInput) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepoTagsRepository_ListRepoTags_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListRepoTags'
type MockRepoTagsRepository_ListRepoTags_Call struct {
	*mock.Call
}

// ListRepoTags is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.ListRepoTagsInput
func (_e *MockRepoTagsRepository_Expecter) ListRepoTags(ctx interface{}, input interface{}) *MockRepoTagsRepository_ListRepoTags_Call {
	return &MockRepoTagsRepository_ListRepoTags_Call{Call: _e.mock.On("ListRepoTags", ctx, input)}
}

func (_c *MockRepoTagsRepository_ListRepoTags_Call) Run(run func(ctx context.Context, input dto.ListRepoTagsInput)) *MockRepoTagsRepository_ListRepoTags_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.ListRepoTagsInput))
	})
	return _c
}

func (_c *MockRepoTagsRepository_ListRepoTags_Call) Return(_a0 []entities.Tag, _a1 error) *MockRepoTagsRepository_ListRepoTags_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepoTagsRepository_ListRepoTags_Call) RunAndReturn(run func(context.Context, dto.ListRepoTagsInput) ([]entities.Tag, error)) *MockRepoTagsRepository_ListRepoTags_Call {
	_c.Call.Return(run)
	return _c
}

// ListRepoTagsByQueryModel provides a mock function with given fields: ctx, input
func (_m *MockRepoTagsRepository) ListRepoTagsByQueryModel(ctx context.Context, input interface{}) ([]entities.Tag, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for ListRepoTagsByQueryModel")
	}

	var r0 []entities.Tag
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, interface{}) ([]entities.Tag, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, interface{}) []entities.Tag); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]entities.Tag)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, interface{}) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepoTagsRepository_ListRepoTagsByQueryModel_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListRepoTagsByQueryModel'
type MockRepoTagsRepository_ListRepoTagsByQueryModel_Call struct {
	*mock.Call
}

// ListRepoTagsByQueryModel is a helper method to define mock.On call
//   - ctx context.Context
//   - input interface{}
func (_e *MockRepoTagsRepository_Expecter) ListRepoTagsByQueryModel(ctx interface{}, input interface{}) *MockRepoTagsRepository_ListRepoTagsByQueryModel_Call {
	return &MockRepoTagsRepository_ListRepoTagsByQueryModel_Call{Call: _e.mock.On("ListRepoTagsByQueryModel", ctx, input)}
}

func (_c *MockRepoTagsRepository_ListRepoTagsByQueryModel_Call) Run(run func(ctx context.Context, input interface{})) *MockRepoTagsRepository_ListRepoTagsByQueryModel_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(interface{}))
	})
	return _c
}

func (_c *MockRepoTagsRepository_ListRepoTagsByQueryModel_Call) Return(_a0 []entities.Tag, _a1 error) *MockRepoTagsRepository_ListRepoTagsByQueryModel_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepoTagsRepository_ListRepoTagsByQueryModel_Call) RunAndReturn(run func(context.Context, interface{}) ([]entities.Tag, error)) *MockRepoTagsRepository_ListRepoTagsByQueryModel_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockRepoTagsRepository creates a new instance of MockRepoTagsRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockRepoTagsRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockRepoTagsRepository {
	mock := &MockRepoTagsRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
