package repository

import (
	"context"
	"errors"

	"github.com/google/uuid"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"api-server/internal/entities"
	"api-server/internal/types"
	"api-server/pkg/oteltrace"
	"api-server/pkg/otelzap"
)

// SshKeyRepository defines the interface for SSH key operations.
// It provides methods for managing SSH keys, including creating, listing,
// finding, and removing SSH keys.
type SshKeyRepository interface {
	// GetSSHKeyByID retrieves a specific SSH key by its ID.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - id: UUID of the SSH key to retrieve
	//
	// Returns:
	//   - *entities.SSHKey: The found SSH key if successful
	//   - error: nil if successful, otherwise the error that occurred
	GetSSHKeyByID(ctx context.Context, id uuid.UUID) (*entities.SSHKey, error)

	// ListSSHKeys retrieves a paginated list of SSH keys.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - pageNo: Page number for pagination
	//   - pageSize: Number of items per page
	//   - order: Ordering parameters
	//   - query: Query parameters for filtering keys
	//
	// Returns:
	//   - []entities.SSHKey: List of SSH keys
	//   - error: nil if successful, otherwise the error that occurred
	ListSSHKeys(ctx context.Context, pageNo, pageSize int, order types.OrderBy, query GetSSHKeyQuery) ([]entities.SSHKey, error)

	// CountAllSSHKeys counts the total number of SSH keys.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - query: Query parameters for filtering keys
	//
	// Returns:
	//   - int64: Total number of SSH keys
	//   - error: nil if successful, otherwise the error that occurred
	CountAllSSHKeys(ctx context.Context, query GetSSHKeyQuery) (int64, error)

	// DeleteSSHKeysByUserID removes all SSH keys for a user.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - userID: UUID of the user
	//
	// Returns:
	//   - error: nil if successful, otherwise the error that occurred
	DeleteSSHKeysByUserID(ctx context.Context, userID uuid.UUID) error

	// IsDuplicateSSHKey checks if an SSH key already exists.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - key: The SSH key to check
	//
	// Returns:
	//   - bool: true if the key exists, false otherwise
	//   - error: nil if successful, otherwise the error that occurred
	IsDuplicateSSHKey(ctx context.Context, key string) (bool, error)
}

var _ SshKeyRepository = (*repository)(nil)

// GetSSHKeyQuery contains the criteria for querying SSH keys.
type GetSSHKeyQuery struct {
	UserId uuid.UUID
}

// GetSSHKeyByID retrieves a specific SSH key by its ID.
func (r repository) GetSSHKeyByID(ctx context.Context, id uuid.UUID) (*entities.SSHKey, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.sshkey.GetSSHKeyByID",
		trace.WithAttributes(
			attribute.String("action", "GET_SSH_KEY_BY_ID"),
			attribute.String("key_id", id.String()),
		))
	defer span.End()

	otelzap.InfoWithContext(ctx, "getting ssh key by id",
		zap.String("action", "GET_SSH_KEY_BY_ID"),
		zap.String("key_id", id.String()))

	db := r.GetDB(ctx)
	var sshKey entities.SSHKey
	err := db.WithContext(ctx).Where("id = ?", id).First(&sshKey).Error
	if err != nil {
		span.SetStatus(codes.Error, "DB query error")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "DB query error", err,
			zap.String("action", "GET_SSH_KEY_BY_ID"),
			zap.String("key_id", id.String()),
			zap.String("status", "failed"))
		return nil, err
	}

	span.AddEvent("SSH key retrieved successfully")
	span.SetStatus(codes.Ok, "SSH key retrieved successfully")
	span.SetAttributes(attribute.String("status", "success"))
	otelzap.InfoWithContext(ctx, "SSH key retrieved successfully",
		zap.String("action", "GET_SSH_KEY_BY_ID"),
		zap.String("key_id", id.String()),
		zap.String("status", "success"))
	return &sshKey, nil
}

// IsDuplicateSSHKey checks if an SSH key already exists.
func (r repository) IsDuplicateSSHKey(ctx context.Context, key string) (bool, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.sshkey.IsDuplicateSSHKey")
	defer span.End()

	db := r.GetDB(ctx)
	var sshKey entities.SSHKey
	err := db.WithContext(ctx).Where("public_key = ?", key).First(&sshKey).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return false, nil
		}
		span.SetStatus(codes.Error, "DB query error")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		return false, err
	}

	span.AddEvent("check duplicate SSH key successfully")
	span.SetStatus(codes.Ok, "check duplicate SSH key successfully")
	span.SetAttributes(attribute.String("status", "success"))
	return true, nil
}

// ListSSHKeys retrieves a paginated list of SSH keys.
func (r repository) ListSSHKeys(ctx context.Context, pageNo, pageSize int, order types.OrderBy, query GetSSHKeyQuery) ([]entities.SSHKey, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.sshkey.ListSSHKeys",
		trace.WithAttributes(
			attribute.String("action", "LIST_SSH_KEYS"),
			attribute.String("user_id", query.UserId.String()),
		))
	defer span.End()

	otelzap.InfoWithContext(ctx, "listing ssh keys",
		zap.String("action", "LIST_SSH_KEYS"),
		zap.String("user_id", query.UserId.String()))

	// Get all keys
	span.AddEvent("get all SSH keys")
	var keys []entities.SSHKey
	err := r.GetDB(ctx).
		Scopes(paginate(pageNo, pageSize), orderBy(order)).
		Where("user_id = ?", query.UserId).
		Find(&keys).
		Error
	if err != nil {
		span.SetStatus(codes.Error, "DB query error")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "DB query error", err,
			zap.String("action", "LIST_SSH_KEYS"),
			zap.String("user_id", query.UserId.String()),
			zap.String("status", "failed"))
		return nil, err
	}

	span.AddEvent("SSH keys listed successfully")
	span.SetStatus(codes.Ok, "SSH keys listed successfully")
	span.SetAttributes(attribute.String("status", "success"))
	otelzap.InfoWithContext(ctx, "SSH keys listed successfully",
		zap.String("action", "LIST_SSH_KEYS"),
		zap.String("user_id", query.UserId.String()),
		zap.String("status", "success"))
	return keys, nil
}

// CountAllSSHKeys counts the total number of SSH keys.
func (r repository) CountAllSSHKeys(ctx context.Context, query GetSSHKeyQuery) (int64, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.sshkey.CountAllSSHKeys",
		trace.WithAttributes(
			attribute.String("action", "COUNT_ALL_SSH_KEYS"),
			attribute.String("user_id", query.UserId.String()),
		))
	defer span.End()

	otelzap.InfoWithContext(ctx, "counting all ssh keys",
		zap.String("action", "COUNT_ALL_SSH_KEYS"),
		zap.String("user_id", query.UserId.String()))

	// Count total number of keys
	span.AddEvent("count all SSH keys")
	var total int64
	if err := r.GetDB(ctx).Model(&entities.SSHKey{}).Where("user_id = ?", query.UserId).Count(&total).Error; err != nil {
		span.SetStatus(codes.Error, "DB query error")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "DB query error", err,
			zap.String("action", "COUNT_ALL_SSH_KEYS"),
			zap.String("user_id", query.UserId.String()),
			zap.String("status", "failed"))
		return 0, err
	}

	span.AddEvent("SSH keys counted successfully")
	span.SetStatus(codes.Ok, "SSH keys counted successfully")
	span.SetAttributes(attribute.String("status", "success"))
	otelzap.InfoWithContext(ctx, "SSH keys counted successfully",
		zap.String("action", "COUNT_ALL_SSH_KEYS"),
		zap.String("user_id", query.UserId.String()),
		zap.String("status", "success"))
	return total, nil
}

// DeleteSSHKeysByUserID removes all SSH keys for a user.
func (r repository) DeleteSSHKeysByUserID(ctx context.Context, userID uuid.UUID) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.sshkey.DeleteSSHKeysByUserID",
		trace.WithAttributes(
			attribute.String("action", "DELETE_SSH_KEYS_BY_USER_ID"),
			attribute.String("user_id", userID.String()),
		))
	defer span.End()

	otelzap.InfoWithContext(ctx, "deleting ssh keys by user id",
		zap.String("action", "DELETE_SSH_KEYS_BY_USER_ID"),
		zap.String("user_id", userID.String()))

	err := r.GetDB(ctx).Where("user_id = ?", userID).Delete(&entities.SSHKey{}).Error
	if err != nil {
		span.SetStatus(codes.Error, "DB query error")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "DB query error", err,
			zap.String("action", "DELETE_SSH_KEYS_BY_USER_ID"),
			zap.String("user_id", userID.String()),
			zap.String("status", "failed"))
		return err
	}

	span.AddEvent("SSH keys deleted successfully for user")
	span.SetStatus(codes.Ok, "SSH keys deleted successfully for user")
	span.SetAttributes(attribute.String("status", "success"))
	otelzap.InfoWithContext(ctx, "SSH keys deleted successfully for user",
		zap.String("action", "DELETE_SSH_KEYS_BY_USER_ID"),
		zap.String("user_id", userID.String()),
		zap.String("status", "success"))
	return err
}
