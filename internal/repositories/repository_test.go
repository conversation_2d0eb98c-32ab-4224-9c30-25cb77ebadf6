package repository

import (
	"context"
	"database/sql"
	"testing"
	"time"

	sqlmock "github.com/DATA-DOG/go-sqlmock"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
)

func TestCreate(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		type TestEntity struct {
			ID        uuid.UUID `gorm:"primaryKey"`
			Name      string
			CreatedAt time.Time
			UpdatedAt time.Time
		}
		entity := TestEntity{
			ID:   uuid.New(),
			Name: "test",
		}

		// Setup mock expectations
		mock.ExpectBegin()
		mock.ExpectExec(`INSERT INTO "test_entities" \("id","name","created_at","updated_at"\) VALUES \(\$1,\$2,\$3,\$4\)`).
			WithArgs(entity.ID, entity.Name, sqlmock.AnyArg(), sqlmock.AnyArg()).
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		// Execute
		err := repo.Create(context.Background(), &entity)

		// Assert
		assert.NoError(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		type TestEntity struct {
			ID        uuid.UUID `gorm:"primaryKey"`
			Name      string
			CreatedAt time.Time
			UpdatedAt time.Time
		}
		entity := TestEntity{
			ID:   uuid.New(),
			Name: "test",
		}

		// Setup mock expectations
		mock.ExpectBegin()
		mock.ExpectExec(`INSERT INTO "test_entities" \("id","name","created_at","updated_at"\) VALUES \(\$1,\$2,\$3,\$4\)`).
			WithArgs(entity.ID, entity.Name, sqlmock.AnyArg(), sqlmock.AnyArg()).
			WillReturnError(gorm.ErrInvalidDB)
		mock.ExpectRollback()

		// Execute
		err := repo.Create(context.Background(), &entity)

		// Assert
		assert.Error(t, err)
		assert.Equal(t, gorm.ErrInvalidDB, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestSave(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		type TestEntity struct {
			ID        uuid.UUID `gorm:"primaryKey"`
			Name      string
			CreatedAt time.Time
			UpdatedAt time.Time
		}
		entity := TestEntity{
			ID:   uuid.New(),
			Name: "test",
		}

		// Setup mock expectations
		mock.ExpectBegin()
		mock.ExpectExec(`UPDATE "test_entities" SET "name"=\$1,"created_at"=\$2,"updated_at"=\$3 WHERE "id" = \$4`).
			WithArgs(entity.Name, sqlmock.AnyArg(), sqlmock.AnyArg(), entity.ID).
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		// Execute
		err := repo.Save(context.Background(), &entity)

		// Assert
		assert.NoError(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		type TestEntity struct {
			ID        uuid.UUID `gorm:"primaryKey"`
			Name      string
			CreatedAt time.Time
			UpdatedAt time.Time
		}
		entity := TestEntity{
			ID:   uuid.New(),
			Name: "test",
		}

		// Setup mock expectations
		mock.ExpectBegin()
		mock.ExpectExec(`UPDATE "test_entities" SET "name"=\$1,"created_at"=\$2,"updated_at"=\$3 WHERE "id" = \$4`).
			WithArgs(entity.Name, sqlmock.AnyArg(), sqlmock.AnyArg(), entity.ID).
			WillReturnError(gorm.ErrInvalidDB)
		mock.ExpectRollback()

		// Execute
		err := repo.Save(context.Background(), &entity)

		// Assert
		assert.Error(t, err)
		assert.Equal(t, gorm.ErrInvalidDB, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestDelete(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		type TestEntity struct {
			ID        uuid.UUID `gorm:"primaryKey"`
			Name      string
			CreatedAt time.Time
			UpdatedAt time.Time
		}
		entity := TestEntity{
			ID: uuid.New(),
		}

		// Setup mock expectations
		mock.ExpectBegin()
		mock.ExpectExec(`DELETE FROM "test_entities" WHERE "test_entities"."id" = \$1`).
			WithArgs(entity.ID).
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		// Execute
		err := repo.Delete(context.Background(), &entity)

		// Assert
		assert.NoError(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		type TestEntity struct {
			ID        uuid.UUID `gorm:"primaryKey"`
			Name      string
			CreatedAt time.Time
			UpdatedAt time.Time
		}
		entity := TestEntity{
			ID: uuid.New(),
		}

		// Setup mock expectations
		mock.ExpectBegin()
		mock.ExpectExec(`DELETE FROM "test_entities" WHERE "test_entities"."id" = \$1`).
			WithArgs(entity.ID).
			WillReturnError(gorm.ErrInvalidDB)
		mock.ExpectRollback()

		// Execute
		err := repo.Delete(context.Background(), &entity)

		// Assert
		assert.Error(t, err)
		assert.Equal(t, gorm.ErrInvalidDB, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestDeleteById(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		type TestEntity struct {
			ID        uuid.UUID `gorm:"primaryKey"`
			Name      string
			CreatedAt time.Time
			UpdatedAt time.Time
		}
		id := uuid.New()
		model := TestEntity{}

		// Setup mock expectations
		mock.ExpectBegin()
		mock.ExpectExec(`DELETE FROM "test_entities" WHERE id = \$1`).
			WithArgs(id).
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		// Execute
		err := repo.DeleteById(context.Background(), &model, id)

		// Assert
		assert.NoError(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		type TestEntity struct {
			ID        uuid.UUID `gorm:"primaryKey"`
			Name      string
			CreatedAt time.Time
			UpdatedAt time.Time
		}
		id := uuid.New()
		model := TestEntity{}

		// Setup mock expectations
		mock.ExpectBegin()
		mock.ExpectExec(`DELETE FROM "test_entities" WHERE id = \$1`).
			WithArgs(id).
			WillReturnError(gorm.ErrInvalidDB)
		mock.ExpectRollback()

		// Execute
		err := repo.DeleteById(context.Background(), &model, id)

		// Assert
		assert.Error(t, err)
		assert.Equal(t, gorm.ErrInvalidDB, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestTransaction(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup mock expectations
		mock.ExpectBegin()
		mock.ExpectCommit()

		// Execute
		err := repo.Transaction(context.Background(), func(ctx context.Context) error {
			return nil
		})

		// Assert
		assert.NoError(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup mock expectations
		mock.ExpectBegin()
		mock.ExpectRollback()

		// Execute
		err := repo.Transaction(context.Background(), func(ctx context.Context) error {
			return gorm.ErrInvalidDB
		})

		// Assert
		assert.Error(t, err)
		assert.Equal(t, gorm.ErrInvalidDB, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestBeginTransaction(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup mock expectations
		mock.ExpectBegin()

		// Execute
		ctx, err := repo.BeginTransaction(context.Background())

		// Assert
		assert.NoError(t, err)
		assert.NotNil(t, ctx)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("with options", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup mock expectations
		mock.ExpectBegin()

		// Execute
		ctx, err := repo.BeginTransaction(context.Background(), &sql.TxOptions{
			Isolation: sql.LevelReadCommitted,
			ReadOnly:  false,
		})

		// Assert
		assert.NoError(t, err)
		assert.NotNil(t, ctx)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestCommitTransaction(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup mock expectations for transaction
		mock.ExpectBegin()
		mock.ExpectCommit()

		// Setup transaction context
		ctx := context.Background()
		txCtx, err := repo.BeginTransaction(ctx)
		assert.NoError(t, err)

		// Execute
		result := repo.CommitTransaction(txCtx)

		// Assert
		assert.NotNil(t, result)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup mock expectations for transaction
		mock.ExpectBegin()
		mock.ExpectCommit().WillReturnError(gorm.ErrInvalidDB)

		// Setup transaction context
		ctx := context.Background()
		txCtx, err := repo.BeginTransaction(ctx)
		assert.NoError(t, err)

		// Execute
		result := repo.CommitTransaction(txCtx)

		// Assert
		assert.NotNil(t, result)
		assert.Error(t, result.Error)
		assert.Equal(t, gorm.ErrInvalidDB, result.Error)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}
