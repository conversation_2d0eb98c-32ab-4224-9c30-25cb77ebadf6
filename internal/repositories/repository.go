package repository

import (
	"context"
	"database/sql"
	"time"

	"github.com/google/uuid"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

type dbkey string
type dbPreload string

var valueDbKey = dbkey("db")
var valueDbPreload = dbPreload("db_preload")

// Repository defines the main interface for database operations.
// It provides methods for managing database transactions, CRUD operations,
// and includes all other repository interfaces for specific entities.
type Repository interface {
	// GetDB retrieves the database connection from the context or creates a new one.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//
	// Returns:
	//   - *gorm.DB: The database connection
	GetDB(ctx context.Context) *gorm.DB

	// BeginTransaction starts a new database transaction.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - opts: Optional transaction options
	//
	// Returns:
	//   - context.Context: Context with transaction
	//   - error: nil if successful, otherwise the error that occurred
	BeginTransaction(ctx context.Context, opts ...*sql.TxOptions) (context.Context, error)

	// CommitTransaction commits the current transaction.
	//
	// Parameters:
	//   - ctx: Context containing the transaction
	//
	// Returns:
	//   - *gorm.DB: The database connection after commit
	CommitTransaction(ctx context.Context) *gorm.DB

	// RollbackTransaction rolls back the current transaction.
	//
	// Parameters:
	//   - ctx: Context containing the transaction
	//
	// Returns:
	//   - *gorm.DB: The database connection after rollback
	RollbackTransaction(ctx context.Context) *gorm.DB

	// Transaction executes a function within a transaction.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - callback: Function to execute within the transaction
	//   - opts: Optional transaction options
	//
	// Returns:
	//   - error: nil if successful, otherwise the error that occurred
	Transaction(ctx context.Context, callback func(ctx context.Context) error, opts ...*sql.TxOptions) error

	// Create creates a new record in the database.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - entities: The entity to create
	//
	// Returns:
	//   - error: nil if successful, otherwise the error that occurred
	Create(ctx context.Context, entities interface{}) error

	// Save saves or updates a record in the database.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - entities: The entity to save
	//
	// Returns:
	//   - error: nil if successful, otherwise the error that occurred
	Save(ctx context.Context, entities interface{}) error

	// Delete removes a record from the database.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - entities: The entity to delete
	//
	// Returns:
	//   - error: nil if successful, otherwise the error that occurred
	Delete(ctx context.Context, entities interface{}) error

	// DeleteById removes a record from the database by its ID.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - model: The model type to delete
	//   - id: UUID of the record to delete
	//
	// Returns:
	//   - error: nil if successful, otherwise the error that occurred
	DeleteById(ctx context.Context, model interface{}, id uuid.UUID) error

	HardwareRepository
	OrgMemberRepository
	OrganizationRepository
	RepoRepository
	RepoAccessTokenRepository
	RepoMemberRepository
	SignupRequestRepository
	SshKeyRepository
	UserRepository
	AccessTokenRepository
	UserGitGroupRepository
	OrgGitGroupRepository
	DeploymentRepository
	RepoTagsRepository
	TemplateRepository
	OrgAccessTokenRepository
	CustomImageDeploymentRepository
}

var _ Repository = (*repository)(nil)

type repository struct {
	db *gorm.DB
}

// NewRepository creates a new repository instance with the given database connection.
//
// Parameters:
//   - db: The database connection pool
//
// Returns:
//   - *repository: A new repository instance
func NewRepository(db gorm.ConnPool) *repository {
	gormDb, _ := gorm.Open(postgres.New(postgres.Config{
		Conn: db,
	}))

	return &repository{db: gormDb}
}

// Create creates a new record in the database.
func (r repository) Create(ctx context.Context, entities interface{}) error {
	db := r.GetDB(ctx)
	return db.WithContext(ctx).Create(entities).Error
}

// Save saves or updates a record in the database.
func (r repository) Save(ctx context.Context, entities interface{}) error {
	db := r.GetDB(ctx)
	return db.WithContext(ctx).Save(entities).Error
}

// Delete removes a record from the database.
func (r repository) Delete(ctx context.Context, entities interface{}) error {
	db := r.GetDB(ctx)
	return db.WithContext(ctx).Delete(entities).Error
}

// DeleteById removes a record from the database by its ID.
func (r repository) DeleteById(ctx context.Context, model interface{}, id uuid.UUID) error {
	db := r.GetDB(ctx)
	return db.WithContext(ctx).Where("id = ?", id).Delete(model).Error
}

// GetDB retrieves the database connection from the context or creates a new one.
func (r repository) GetDB(ctx context.Context) *gorm.DB {
	val, ok := ctx.Value(valueDbKey).(*gorm.DB)
	var db *gorm.DB
	if !ok {
		db = r.db.WithContext(ctx)
		preload, ok := ctx.Value(valueDbPreload).([]string)
		if ok {
			for _, p := range preload {
				db = db.Preload(p)
			}
		}
	} else {
		db = val
	}

	return db.WithContext(ctx)
}

// BeginTransaction starts a new database transaction.
func (r repository) BeginTransaction(ctx context.Context, opts ...*sql.TxOptions) (context.Context, error) {
	db := r.GetDB(ctx)
	return context.WithValue(ctx, valueDbKey, db.Begin(opts...)), nil
}

// CommitTransaction commits the current transaction.
func (r repository) CommitTransaction(ctx context.Context) *gorm.DB {
	db := r.GetDB(ctx)
	return db.Commit()
}

// RollbackTransaction rolls back the current transaction.
func (r repository) RollbackTransaction(ctx context.Context) *gorm.DB {
	db := r.GetDB(ctx)
	return db.Rollback()
}

// Transaction executes a function within a transaction.
func (r repository) Transaction(ctx context.Context, callback func(ctx context.Context) error, opts ...*sql.TxOptions) error {
	return r.GetDB(ctx).Transaction(func(tx *gorm.DB) error {
		txCtxValue := context.WithValue(ctx, valueDbKey, tx)
		txCtx, cancel := context.WithTimeout(txCtxValue, 15*time.Second)
		defer cancel()

		err := callback(txCtx)
		if err != nil {
			return err
		}
		return nil
	}, opts...)
}
