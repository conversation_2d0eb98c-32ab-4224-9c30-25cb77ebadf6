package repository

import (
	"context"
	"testing"
	"time"

	sqlmock "github.com/DATA-DOG/go-sqlmock"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"

	"api-server/internal/entities"
	"api-server/internal/enums"
)

func TestDeleteRepositoryMemberByRepositoryID(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		repoID := uuid.New()

		// Setup mock expectations
		mock.ExpectBegin()
		mock.ExpectExec(`DELETE FROM "repo_members" WHERE repo_id = \$1`).
			WithArgs(repoID).
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		// Call the method
		err := repo.DeleteRepositoryMemberByRepositoryID(context.Background(), repoID)

		// Assertions
		assert.NoError(t, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		repoID := uuid.New()

		// Setup mock expectations
		mock.ExpectBegin()
		mock.ExpectExec(`DELETE FROM "repo_members" WHERE repo_id = \$1`).
			WithArgs(repoID).
			WillReturnError(gorm.ErrInvalidDB)
		mock.ExpectRollback()

		// Call the method
		err := repo.DeleteRepositoryMemberByRepositoryID(context.Background(), repoID)

		// Assertions
		assert.Error(t, err)
		assert.Equal(t, gorm.ErrInvalidDB, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestCreateRepositoryMember(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		memberID := uuid.New()
		userID := uuid.New()
		repoID := uuid.New()
		createdAt := time.Now()
		updatedAt := time.Now()
		expiresAt := time.Now().Add(24 * time.Hour)

		member := &entities.RepoMember{
			BaseModel: entities.BaseModel{
				ID:        memberID,
				CreatedAt: createdAt,
				UpdatedAt: updatedAt,
			},
			UserID:    userID,
			RepoID:    repoID,
			Role:      enums.RepoRole_Owner,
			ExpiresAt: &expiresAt,
		}

		// Setup mock expectations
		mock.ExpectBegin()
		rows := sqlmock.NewRows([]string{"id"}).AddRow(memberID)
		mock.ExpectQuery(`INSERT INTO "repo_members" \("created_at","updated_at","user_id","repo_id","role","expires_at","id"\) VALUES \(\$1,\$2,\$3,\$4,\$5,\$6,\$7\) RETURNING "id"`).
			WithArgs(createdAt, updatedAt, userID, repoID, enums.RepoRole_Owner, expiresAt, memberID).
			WillReturnRows(rows)
		mock.ExpectCommit()

		// Call the method
		err := repo.CreateRepositoryMember(context.Background(), member)

		// Assertions
		assert.NoError(t, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		memberID := uuid.New()
		userID := uuid.New()
		repoID := uuid.New()
		createdAt := time.Now()
		updatedAt := time.Now()
		expiresAt := time.Now().Add(24 * time.Hour)

		member := &entities.RepoMember{
			BaseModel: entities.BaseModel{
				ID:        memberID,
				CreatedAt: createdAt,
				UpdatedAt: updatedAt,
			},
			UserID:    userID,
			RepoID:    repoID,
			Role:      enums.RepoRole_Owner,
			ExpiresAt: &expiresAt,
		}

		// Setup mock expectations
		mock.ExpectBegin()
		mock.ExpectQuery(`INSERT INTO "repo_members" \("created_at","updated_at","user_id","repo_id","role","expires_at","id"\) VALUES \(\$1,\$2,\$3,\$4,\$5,\$6,\$7\) RETURNING "id"`).
			WithArgs(createdAt, updatedAt, userID, repoID, enums.RepoRole_Owner, expiresAt, memberID).
			WillReturnError(gorm.ErrInvalidDB)
		mock.ExpectRollback()

		// Call the method
		err := repo.CreateRepositoryMember(context.Background(), member)

		// Assertions
		assert.Error(t, err)
		assert.Equal(t, gorm.ErrInvalidDB, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestDeleteRepositoryMemberByUserID(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		userID := uuid.New()

		// Setup mock expectations
		mock.ExpectBegin()
		mock.ExpectExec(`DELETE FROM "repo_members" WHERE user_id = \$1`).
			WithArgs(userID).
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		// Call the method
		err := repo.DeleteRepositoryMemberByUserID(context.Background(), userID)

		// Assertions
		assert.NoError(t, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		userID := uuid.New()

		// Setup mock expectations
		mock.ExpectBegin()
		mock.ExpectExec(`DELETE FROM "repo_members" WHERE user_id = \$1`).
			WithArgs(userID).
			WillReturnError(gorm.ErrInvalidDB)
		mock.ExpectRollback()

		// Call the method
		err := repo.DeleteRepositoryMemberByUserID(context.Background(), userID)

		// Assertions
		assert.Error(t, err)
		assert.Equal(t, gorm.ErrInvalidDB, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestFindRepositoryMember(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		memberID := uuid.New()
		userID := uuid.New()
		repoID := uuid.New()
		createdAt := time.Now()
		updatedAt := time.Now()
		expiresAt := time.Now().Add(24 * time.Hour)

		// Setup mock expectations
		rows := sqlmock.NewRows([]string{"id", "user_id", "repo_id", "role", "expires_at", "created_at", "updated_at"}).
			AddRow(memberID, userID, repoID, enums.RepoRole_Owner, expiresAt, createdAt, updatedAt)

		mock.ExpectQuery(`SELECT \* FROM "repo_members" WHERE user_id = \$1 AND repo_id = \$2 AND role = \$3 ORDER BY "repo_members"."id" LIMIT \$4`).
			WithArgs(userID, repoID, enums.RepoRole_Owner, 1).
			WillReturnRows(rows)

		// Call the method
		filter := FilterRepoMember{
			UserID: &userID,
			RepoID: &repoID,
			Role:   enums.RepoRole_Owner,
		}
		result, err := repo.FindRepositoryMember(context.Background(), filter)

		// Assertions
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, memberID, result.ID)
		assert.Equal(t, userID, result.UserID)
		assert.Equal(t, repoID, result.RepoID)
		assert.Equal(t, enums.RepoRole_Owner, result.Role)
		assert.Equal(t, expiresAt, *result.ExpiresAt)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("not found", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		userID := uuid.New()
		repoID := uuid.New()

		// Setup mock expectations
		mock.ExpectQuery(`SELECT \* FROM "repo_members" WHERE user_id = \$1 AND repo_id = \$2 ORDER BY "repo_members"."id" LIMIT \$3`).
			WithArgs(userID, repoID, 1).
			WillReturnError(gorm.ErrRecordNotFound)

		// Call the method
		filter := FilterRepoMember{
			UserID: &userID,
			RepoID: &repoID,
		}
		result, err := repo.FindRepositoryMember(context.Background(), filter)

		// Assertions
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Equal(t, gorm.ErrRecordNotFound, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		userID := uuid.New()
		repoID := uuid.New()

		// Setup mock expectations
		mock.ExpectQuery(`SELECT \* FROM "repo_members" WHERE user_id = \$1 AND repo_id = \$2 ORDER BY "repo_members"."id" LIMIT \$3`).
			WithArgs(userID, repoID, 1).
			WillReturnError(gorm.ErrInvalidDB)

		// Call the method
		filter := FilterRepoMember{
			UserID: &userID,
			RepoID: &repoID,
		}
		result, err := repo.FindRepositoryMember(context.Background(), filter)

		// Assertions
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Equal(t, gorm.ErrInvalidDB, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}
