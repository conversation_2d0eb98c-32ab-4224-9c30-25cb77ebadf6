package repository

import (
	"context"

	"api-server/internal/entities"
	"api-server/pkg/oteltrace"

	"github.com/google/uuid"
)

// TemplateRepository defines the interface for repository template operations.
// It provides methods for managing repository templates, including listing
// and finding templates.
type TemplateRepository interface {
	// ListTemplates retrieves all repository templates.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//
	// Returns:
	//   - []entities.RepoTemplate: List of all repository templates
	//   - error: nil if successful, otherwise the error that occurred
	ListTemplates(ctx context.Context) ([]entities.RepoTemplate, error)

	// FindTemplate retrieves a specific repository template by its ID.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - id: UUID of the template to retrieve
	//
	// Returns:
	//   - *entities.RepoTemplate: The found template if successful
	//   - error: nil if successful, otherwise the error that occurred
	FindTemplate(ctx context.Context, id uuid.UUID) (*entities.RepoTemplate, error)
}

var _ TemplateRepository = (*repository)(nil)

func (r repository) ListTemplates(ctx context.Context) ([]entities.RepoTemplate, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.templates.ListTemplates")
	defer span.End()

	var result []entities.RepoTemplate

	query := r.GetDB(ctx)
	if err := query.Find(&result).Error; err != nil {
		return nil, err
	}

	return result, nil
}

func (r repository) FindTemplate(ctx context.Context, id uuid.UUID) (*entities.RepoTemplate, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.templates.FindTemplate")
	defer span.End()

	var result *entities.RepoTemplate
	predicate := entities.RepoTemplate{
		BaseModel: entities.BaseModel{
			ID: id,
		},
	}

	query := r.GetDB(ctx)
	if err := query.Where(&predicate).
		Find(&result).Error; err != nil {
		return nil, err
	}

	return result, nil
}
