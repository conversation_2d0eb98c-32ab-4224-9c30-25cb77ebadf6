package repository

import (
	"context"
	"testing"
	"time"

	sqlmock "github.com/DATA-DOG/go-sqlmock"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"

	"api-server/internal/dto"
)

func TestFindOrganizationByID(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		orgID := uuid.New()
		createdAt := time.Now()
		updatedAt := time.Now()

		// Setup mock expectations
		rows := sqlmock.NewRows([]string{"id", "name", "path_name", "created_at", "updated_at"}).
			AddRow(orgID, "test-org", "test-org", createdAt, updatedAt)

		mock.ExpectQuery(`SELECT \* FROM "organizations" WHERE id = \$1 ORDER BY "organizations"."id" LIMIT \$2`).
			WithArgs(orgID, 1).
			WillReturnRows(rows)

		// Call the method
		result, err := repo.FindOrganizationByID(context.Background(), orgID)

		// Assertions
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, orgID, result.ID)
		assert.Equal(t, "test-org", result.Name)
		assert.Equal(t, "test-org", result.PathName)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("not found", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		orgID := uuid.New()

		// Setup mock expectations
		mock.ExpectQuery(`SELECT \* FROM "organizations" WHERE id = \$1 ORDER BY "organizations"."id" LIMIT \$2`).
			WithArgs(orgID, 1).
			WillReturnError(gorm.ErrRecordNotFound)

		// Call the method
		result, err := repo.FindOrganizationByID(context.Background(), orgID)

		// Assertions
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Equal(t, gorm.ErrRecordNotFound, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		orgID := uuid.New()

		// Setup mock expectations
		mock.ExpectQuery(`SELECT \* FROM "organizations" WHERE id = \$1 ORDER BY "organizations"."id" LIMIT \$2`).
			WithArgs(orgID, 1).
			WillReturnError(gorm.ErrInvalidDB)

		// Call the method
		result, err := repo.FindOrganizationByID(context.Background(), orgID)

		// Assertions
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Equal(t, gorm.ErrInvalidDB, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestFindOrganization(t *testing.T) {
	t.Run("success by ID", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		orgID := uuid.New()
		createdAt := time.Now()
		updatedAt := time.Now()

		// Setup mock expectations
		rows := sqlmock.NewRows([]string{"id", "name", "path_name", "created_at", "updated_at"}).
			AddRow(orgID, "test-org", "test-org", createdAt, updatedAt)

		mock.ExpectQuery(`SELECT \* FROM "organizations" WHERE id = \$1 ORDER BY "organizations"."id" LIMIT \$2`).
			WithArgs(orgID, 1).
			WillReturnRows(rows)

		// Call the method
		input := FilterOrganization{
			OrgId: &orgID,
		}
		result, err := repo.FindOrganization(context.Background(), input)

		// Assertions
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, orgID, result.ID)
		assert.Equal(t, "test-org", result.Name)
		assert.Equal(t, "test-org", result.PathName)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("success by name or path_name", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		orgID := uuid.New()
		createdAt := time.Now()
		updatedAt := time.Now()
		name := "test-org"
		pathName := "test-org"

		// Setup mock expectations
		rows := sqlmock.NewRows([]string{"id", "name", "path_name", "created_at", "updated_at"}).
			AddRow(orgID, name, pathName, createdAt, updatedAt)

		mock.ExpectQuery(`SELECT \* FROM "organizations" WHERE name = \$1 OR path_name = \$2 ORDER BY "organizations"."id" LIMIT \$3`).
			WithArgs(name, pathName, 1).
			WillReturnRows(rows)

		// Call the method
		input := FilterOrganization{
			Name:     &name,
			PathName: &pathName,
		}
		result, err := repo.FindOrganization(context.Background(), input)

		// Assertions
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, orgID, result.ID)
		assert.Equal(t, name, result.Name)
		assert.Equal(t, pathName, result.PathName)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		orgID := uuid.New()

		// Setup mock expectations
		mock.ExpectQuery(`SELECT \* FROM "organizations" WHERE id = \$1 ORDER BY "organizations"."id" LIMIT \$2`).
			WithArgs(orgID, 1).
			WillReturnError(gorm.ErrInvalidDB)

		// Call the method
		input := FilterOrganization{
			OrgId: &orgID,
		}
		result, err := repo.FindOrganization(context.Background(), input)

		// Assertions
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Equal(t, gorm.ErrInvalidDB, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestListOrganizations(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		userID := uuid.New()
		orgID1 := uuid.New()
		orgID2 := uuid.New()
		createdAt := time.Now()
		updatedAt := time.Now()

		// Setup mock expectations for org IDs query
		orgIDRows := sqlmock.NewRows([]string{"id"}).
			AddRow(orgID1).
			AddRow(orgID2)

		mock.ExpectQuery(`SELECT DISTINCT organizations.id FROM "organizations" LEFT JOIN org_members ON org_members.org_id = organizations.id WHERE user_id = \$1`).
			WithArgs(userID).
			WillReturnRows(orgIDRows)

		// Setup mock expectations for organizations query
		orgRows := sqlmock.NewRows([]string{"id", "name", "path_name", "created_at", "updated_at"}).
			AddRow(orgID1, "org1", "org1", createdAt, updatedAt).
			AddRow(orgID2, "org2", "org2", createdAt, updatedAt)

		mock.ExpectQuery(`SELECT \* FROM "organizations" WHERE id IN \(\$1,\$2\) ORDER BY id asc LIMIT \$3`).
			WithArgs(orgID1, orgID2, 10).
			WillReturnRows(orgRows)

		// Call the method
		input := dto.ListOrganizationsInput{
			UserId: userID,
			Paginate: dto.PaginateRequest{
				Page:    1,
				PerPage: 10,
				OrderBy: "id",
				Sort:    "asc",
			},
		}
		total, result, err := repo.ListOrganizations(context.Background(), input)

		// Assertions
		assert.NoError(t, err)
		assert.Equal(t, 2, total)
		assert.Len(t, result, 2)
		assert.Equal(t, orgID1, result[0].ID)
		assert.Equal(t, "org1", result[0].Name)
		assert.Equal(t, "org1", result[0].PathName)
		assert.Equal(t, orgID2, result[1].ID)
		assert.Equal(t, "org2", result[1].Name)
		assert.Equal(t, "org2", result[1].PathName)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error in org IDs query", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		userID := uuid.New()

		// Setup mock expectations
		mock.ExpectQuery(`SELECT DISTINCT organizations.id FROM "organizations" LEFT JOIN org_members ON org_members.org_id = organizations.id WHERE user_id = \$1`).
			WithArgs(userID).
			WillReturnError(gorm.ErrInvalidDB)

		// Call the method
		input := dto.ListOrganizationsInput{
			UserId: userID,
		}
		total, result, err := repo.ListOrganizations(context.Background(), input)

		// Assertions
		assert.Error(t, err)
		assert.Equal(t, 0, total)
		assert.Nil(t, result)
		assert.Equal(t, gorm.ErrInvalidDB, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error in organizations query", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		userID := uuid.New()
		orgID1 := uuid.New()
		orgID2 := uuid.New()

		// Setup mock expectations for org IDs query
		orgIDRows := sqlmock.NewRows([]string{"id"}).
			AddRow(orgID1).
			AddRow(orgID2)

		mock.ExpectQuery(`SELECT DISTINCT organizations.id FROM "organizations" LEFT JOIN org_members ON org_members.org_id = organizations.id WHERE user_id = \$1`).
			WithArgs(userID).
			WillReturnRows(orgIDRows)

		// Setup mock expectations for organizations query
		mock.ExpectQuery(`SELECT \* FROM "organizations" WHERE id IN \(\$1,\$2\) ORDER BY id asc LIMIT \$3`).
			WithArgs(orgID1, orgID2, 10).
			WillReturnError(gorm.ErrInvalidDB)

		// Call the method
		input := dto.ListOrganizationsInput{
			UserId: userID,
			Paginate: dto.PaginateRequest{
				Page:    1,
				PerPage: 10,
				OrderBy: "id",
				Sort:    "asc",
			},
		}
		total, result, err := repo.ListOrganizations(context.Background(), input)

		// Assertions
		assert.Error(t, err)
		assert.Equal(t, 0, total)
		assert.Nil(t, result)
		assert.Equal(t, gorm.ErrInvalidDB, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}
