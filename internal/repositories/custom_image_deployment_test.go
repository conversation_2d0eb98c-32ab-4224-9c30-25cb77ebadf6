package repository

import (
	"context"
	"testing"
	"time"

	sqlmock "github.com/DATA-DOG/go-sqlmock"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"

	"api-server/internal/entities"
	"api-server/internal/types"
)

func TestCreateECRDeployment(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		userID := uuid.New()
		orgID := uuid.New()
		gpuModel := "RTX 4090"
		env := map[string]string{"ENV1": "value1", "ENV2": "value2"}

		input := CreateCustomImageDeploymentInput{
			DeploymentName: "test-deployment",
			ImageURI:       "123456789012.dkr.ecr.us-east-1.amazonaws.com/my-repo:latest",
			NodeName:       "gpu-node",
			Env:            env,
			Port:           8080,
			UserID:         &userID,
			OrgID:          &orgID,
			CPU:            2,
			Mem:            1024,
			GPUMem:         &[]int{8192}[0],
			GPUModel:       &gpuModel,
			ProxyBodySize:  100,
		}

		// Setup mock expectations
		mock.ExpectBegin()
		mock.ExpectQuery(`INSERT INTO "custom_image_deployments"`).
			WithArgs(
				sqlmock.AnyArg(),  // created_at
				sqlmock.AnyArg(),  // updated_at
				"test-deployment", // deployment_name
				"123456789012.dkr.ecr.us-east-1.amazonaws.com/my-repo:latest", // image_uri
				&userID,          // user_id
				&orgID,           // org_id
				"gpu-node",       // node_name
				int32(8080),      // port
				sqlmock.AnyArg(), // env (use AnyArg for JSON field)
				2,                // num_cpu
				1024,             // mem
				&[]int{8192}[0],  // gpu_mem
				&gpuModel,        // gpu_model
				100,              // proxy_body_size
				sqlmock.AnyArg(), // id
			).
			WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(uuid.New()))
		mock.ExpectCommit()

		// Call the method
		result, err := repo.CreateECRDeployment(context.Background(), input)

		// Assertions
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, "test-deployment", result.DeploymentName)
		assert.Equal(t, "123456789012.dkr.ecr.us-east-1.amazonaws.com/my-repo:latest", result.ImageURI)
		assert.Equal(t, "gpu-node", result.NodeName)
		assert.Equal(t, int32(8080), result.Port)
		assert.Equal(t, &userID, result.UserID)
		assert.Equal(t, &orgID, result.OrgID)
		assert.Equal(t, 2, result.CPU)
		assert.Equal(t, 1024, result.Mem)
		assert.Equal(t, &[]int{8192}[0], result.GPUMem)
		assert.Equal(t, &gpuModel, result.GPUModel)
		assert.Equal(t, 100, result.ProxyBodySize)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("database error", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		input := CreateCustomImageDeploymentInput{
			DeploymentName: "test-deployment",
			ImageURI:       "test-image",
			NodeName:       "cpu-node",
			Env:            map[string]string{"key": "value"},
			Port:           8080,
			CPU:            1,
			Mem:            512,
			ProxyBodySize:  100,
		}

		// Setup mock expectations
		mock.ExpectBegin()
		mock.ExpectQuery(`INSERT INTO "custom_image_deployments"`).
			WithArgs(
				sqlmock.AnyArg(), // created_at
				sqlmock.AnyArg(), // updated_at
				sqlmock.AnyArg(), // deployment_name
				sqlmock.AnyArg(), // image_uri
				sqlmock.AnyArg(), // user_id
				sqlmock.AnyArg(), // org_id
				sqlmock.AnyArg(), // node_name
				sqlmock.AnyArg(), // port
				sqlmock.AnyArg(), // env
				sqlmock.AnyArg(), // num_cpu
				sqlmock.AnyArg(), // mem
				sqlmock.AnyArg(), // gpu_mem
				sqlmock.AnyArg(), // gpu_model
				sqlmock.AnyArg(), // proxy_body_size
				sqlmock.AnyArg(), // id
			).
			WillReturnError(gorm.ErrInvalidDB)
		mock.ExpectRollback()

		// Call the method
		result, err := repo.CreateECRDeployment(context.Background(), input)

		// Assertions
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Contains(t, err.Error(), "failed to create deployment")

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestUpdateECRDeploymentEnv_JSONErrors(t *testing.T) {
	t.Run("json unmarshal error", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		deploymentID := uuid.New()
		input := UpdateECRDeploymentEnvInput{
			ID:     deploymentID,
			OldKey: "OLD_KEY",
			Key:    "NEW_KEY",
			Value:  "updated_value",
		}

		// Setup mock expectations for finding the deployment with invalid JSON
		rows := sqlmock.NewRows([]string{"id", "created_at", "updated_at", "deployment_name", "image_uri", "node_name", "port", "env", "num_cpu", "mem", "gpu_mem", "gpu_model", "proxy_body_size", "user_id", "org_id"}).
			AddRow(deploymentID, time.Now(), time.Now(), "test-deployment", "test-image", "cpu-node", int32(8080), []byte(`invalid json`), 1, 512, nil, nil, 100, nil, nil)

		mock.ExpectQuery(`SELECT \* FROM "custom_image_deployments" WHERE id = \$1 ORDER BY "custom_image_deployments"."id" LIMIT \$2`).
			WithArgs(deploymentID, 1).
			WillReturnRows(rows)

		// Call the method
		err := repo.UpdateECRDeploymentEnv(context.Background(), input)

		// Assertions
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "invalid character")

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("json marshal error after update", func(t *testing.T) {
		// This test is difficult to trigger since map[string]string should always marshal successfully
		// We'll skip this test as it's an edge case that's nearly impossible to reproduce
		t.Skip("JSON marshal error is difficult to trigger with map[string]string")
	})
}

func TestDeleteECRDeploymentEnv_JSONErrors(t *testing.T) {
	t.Run("json unmarshal error", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		deploymentID := uuid.New()
		input := DeleteECRDeploymentEnv{
			ID:  deploymentID,
			Key: "DELETE_KEY",
		}

		// Setup mock expectations for finding the deployment with invalid JSON
		rows := sqlmock.NewRows([]string{"id", "created_at", "updated_at", "deployment_name", "image_uri", "node_name", "port", "env", "num_cpu", "mem", "gpu_mem", "gpu_model", "proxy_body_size", "user_id", "org_id"}).
			AddRow(deploymentID, time.Now(), time.Now(), "test-deployment", "test-image", "cpu-node", int32(8080), []byte(`invalid json`), 1, 512, nil, nil, 100, nil, nil)

		mock.ExpectQuery(`SELECT \* FROM "custom_image_deployments" WHERE id = \$1 ORDER BY "custom_image_deployments"."id" LIMIT \$2`).
			WithArgs(deploymentID, 1).
			WillReturnRows(rows)

		// Call the method
		err := repo.DeleteECRDeploymentEnv(context.Background(), input)

		// Assertions
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "invalid character")

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("json marshal error after delete", func(t *testing.T) {
		// This test is difficult to trigger since map[string]string should always marshal successfully
		// We'll skip this test as it's an edge case that's nearly impossible to reproduce
		t.Skip("JSON marshal error is difficult to trigger with map[string]string")
	})

	t.Run("save error after env update", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		deploymentID := uuid.New()
		input := DeleteECRDeploymentEnv{
			ID:  deploymentID,
			Key: "DELETE_KEY",
		}

		// Setup mock expectations for finding the deployment
		rows := sqlmock.NewRows([]string{"id", "created_at", "updated_at", "deployment_name", "image_uri", "node_name", "port", "env", "num_cpu", "mem", "gpu_mem", "gpu_model", "proxy_body_size", "user_id", "org_id"}).
			AddRow(deploymentID, time.Now(), time.Now(), "test-deployment", "test-image", "cpu-node", int32(8080), []byte(`{"DELETE_KEY":"delete_value","KEEP_KEY":"keep_value"}`), 1, 512, nil, nil, 100, nil, nil)

		mock.ExpectQuery(`SELECT \* FROM "custom_image_deployments" WHERE id = \$1 ORDER BY "custom_image_deployments"."id" LIMIT \$2`).
			WithArgs(deploymentID, 1).
			WillReturnRows(rows)

		// Setup mock expectations for updating the deployment with error
		mock.ExpectBegin()
		mock.ExpectExec(`UPDATE "custom_image_deployments"`).
			WithArgs(
				sqlmock.AnyArg(), // created_at
				sqlmock.AnyArg(), // updated_at
				sqlmock.AnyArg(), // deployment_name
				sqlmock.AnyArg(), // image_uri
				sqlmock.AnyArg(), // user_id
				sqlmock.AnyArg(), // org_id
				sqlmock.AnyArg(), // node_name
				sqlmock.AnyArg(), // port
				sqlmock.AnyArg(), // env
				sqlmock.AnyArg(), // num_cpu
				sqlmock.AnyArg(), // mem
				sqlmock.AnyArg(), // gpu_mem
				sqlmock.AnyArg(), // gpu_model
				sqlmock.AnyArg(), // proxy_body_size
				sqlmock.AnyArg(), // WHERE id
			).
			WillReturnError(gorm.ErrInvalidDB)
		mock.ExpectRollback()

		// Call the method
		err := repo.DeleteECRDeploymentEnv(context.Background(), input)

		// Assertions
		assert.Error(t, err)
		assert.Equal(t, gorm.ErrInvalidDB, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestUpdateECRDeployment(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		deploymentID := uuid.New()
		nodeName := "updated-node"
		port := int32(9090)
		gpuModel := "RTX 4090"
		mem := uint(2048)
		numCpu := uint(4)
		proxyBodySize := 200

		input := UpdateECRDeploymentInput{
			ID:            deploymentID,
			NodeName:      &nodeName,
			Port:          &port,
			GPUModel:      &gpuModel,
			Mem:           &mem,
			NumCpu:        &numCpu,
			ProxyBodySize: &proxyBodySize,
		}

		// Setup mock expectations for finding the deployment
		rows := sqlmock.NewRows([]string{"id", "created_at", "updated_at", "deployment_name", "image_uri", "node_name", "port", "env", "num_cpu", "mem", "gpu_mem", "gpu_model", "proxy_body_size", "user_id", "org_id"}).
			AddRow(deploymentID, time.Now(), time.Now(), "test-deployment", "test-image", "old-node", int32(8080), []byte(`{}`), 1, 512, nil, nil, 100, nil, nil)

		mock.ExpectQuery(`SELECT \* FROM "custom_image_deployments" WHERE id = \$1 ORDER BY "custom_image_deployments"."id" LIMIT \$2`).
			WithArgs(deploymentID, 1).
			WillReturnRows(rows)

		// Setup mock expectations for updating the deployment
		mock.ExpectBegin()
		mock.ExpectExec(`UPDATE "custom_image_deployments"`).
			WithArgs(
				sqlmock.AnyArg(), // created_at
				sqlmock.AnyArg(), // updated_at
				sqlmock.AnyArg(), // id
				sqlmock.AnyArg(), // deployment_name
				sqlmock.AnyArg(), // image_uri
				sqlmock.AnyArg(), // user_id
				sqlmock.AnyArg(), // org_id
				sqlmock.AnyArg(), // node_name
				sqlmock.AnyArg(), // port
				sqlmock.AnyArg(), // env
				sqlmock.AnyArg(), // num_cpu
				sqlmock.AnyArg(), // mem
				sqlmock.AnyArg(), // gpu_mem
				sqlmock.AnyArg(), // gpu_model
				sqlmock.AnyArg(), // proxy_body_size
				sqlmock.AnyArg(), // WHERE id
			).
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		// Call the method
		result, err := repo.UpdateECRDeployment(context.Background(), input)

		// Assertions
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, deploymentID, result.ID)
		assert.Equal(t, nodeName, result.NodeName)
		assert.Equal(t, port, result.Port)
		assert.Equal(t, &gpuModel, result.GPUModel)
		assert.Equal(t, int(mem), result.Mem)
		assert.Equal(t, int(numCpu), result.CPU)
		assert.Equal(t, proxyBodySize, result.ProxyBodySize)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("deployment not found", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		deploymentID := uuid.New()
		input := UpdateECRDeploymentInput{
			ID: deploymentID,
		}

		// Setup mock expectations
		mock.ExpectQuery(`SELECT \* FROM "custom_image_deployments" WHERE id = \$1 ORDER BY "custom_image_deployments"."id" LIMIT \$2`).
			WithArgs(deploymentID, 1).
			WillReturnError(gorm.ErrRecordNotFound)

		// Call the method
		result, err := repo.UpdateECRDeployment(context.Background(), input)

		// Assertions
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Equal(t, gorm.ErrRecordNotFound, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("update error", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		deploymentID := uuid.New()
		input := UpdateECRDeploymentInput{
			ID: deploymentID,
		}

		// Setup mock expectations for finding the deployment
		rows := sqlmock.NewRows([]string{"id", "created_at", "updated_at", "deployment_name", "image_uri", "node_name", "port", "env", "num_cpu", "mem", "gpu_mem", "gpu_model", "proxy_body_size", "user_id", "org_id"}).
			AddRow(deploymentID, time.Now(), time.Now(), "test-deployment", "test-image", "old-node", int32(8080), []byte(`{}`), 1, 512, nil, nil, 100, nil, nil)

		mock.ExpectQuery(`SELECT \* FROM "custom_image_deployments" WHERE id = \$1 ORDER BY "custom_image_deployments"."id" LIMIT \$2`).
			WithArgs(deploymentID, 1).
			WillReturnRows(rows)

		// Setup mock expectations for updating the deployment
		mock.ExpectBegin()
		mock.ExpectExec(`UPDATE "custom_image_deployments"`).
			WithArgs(
				sqlmock.AnyArg(), // created_at
				sqlmock.AnyArg(), // updated_at
				sqlmock.AnyArg(), // id
				sqlmock.AnyArg(), // deployment_name
				sqlmock.AnyArg(), // image_uri
				sqlmock.AnyArg(), // user_id
				sqlmock.AnyArg(), // org_id
				sqlmock.AnyArg(), // node_name
				sqlmock.AnyArg(), // port
				sqlmock.AnyArg(), // env
				sqlmock.AnyArg(), // num_cpu
				sqlmock.AnyArg(), // mem
				sqlmock.AnyArg(), // gpu_mem
				sqlmock.AnyArg(), // gpu_model
				sqlmock.AnyArg(), // proxy_body_size
				sqlmock.AnyArg(), // WHERE id
			).
			WillReturnError(gorm.ErrInvalidDB)
		mock.ExpectRollback()

		// Call the method
		result, err := repo.UpdateECRDeployment(context.Background(), input)

		// Assertions
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Contains(t, err.Error(), "failed to update deployment")

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestFindECRDeployment(t *testing.T) {
	t.Run("success by ID", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		deploymentID := uuid.New()
		userID := uuid.New()
		deployment := entities.CustomImageDeployment{
			BaseModel: entities.BaseModel{
				ID:        deploymentID,
				CreatedAt: time.Now(),
				UpdatedAt: time.Now(),
			},
			DeploymentName: "test-deployment",
			ImageURI:       "test-image",
			NodeName:       "cpu-node",
			Port:           8080,
			UserID:         &userID,
		}

		input := FindECRDeploymentInput{
			ID: &deploymentID,
		}

		// Setup mock expectations
		rows := sqlmock.NewRows([]string{"id", "created_at", "updated_at", "deployment_name", "image_uri", "node_name", "port", "env", "num_cpu", "mem", "gpu_mem", "gpu_model", "proxy_body_size", "user_id", "org_id"}).
			AddRow(deployment.ID, deployment.CreatedAt, deployment.UpdatedAt, deployment.DeploymentName, deployment.ImageURI, deployment.NodeName, deployment.Port, []byte(`{}`), 1, 512, nil, nil, 100, deployment.UserID, nil)

		mock.ExpectQuery(`SELECT \* FROM "custom_image_deployments" WHERE id = \$1 ORDER BY "custom_image_deployments"."id" LIMIT \$2`).
			WithArgs(deploymentID, 1).
			WillReturnRows(rows)

		// Setup mock expectations for User preload
		userRows := sqlmock.NewRows([]string{"id", "name", "username", "ref_git_userid", "role"}).
			AddRow(userID, "Test User", "testuser", 123, "user")

		mock.ExpectQuery(`SELECT \* FROM "public"."users" WHERE "users"."id" = \$1`).
			WithArgs(userID).
			WillReturnRows(userRows)

		// Call the method
		result, err := repo.FindECRDeployment(context.Background(), input)

		// Assertions
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, deployment.ID, result.ID)
		assert.Equal(t, deployment.DeploymentName, result.DeploymentName)
		assert.Equal(t, deployment.ImageURI, result.ImageURI)
		assert.Equal(t, deployment.NodeName, result.NodeName)
		assert.Equal(t, deployment.Port, result.Port)
		assert.Equal(t, deployment.UserID, result.UserID)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("success by deployment name", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		deploymentID := uuid.New()
		deploymentName := "test-deployment"

		input := FindECRDeploymentInput{
			DeploymentName: &deploymentName,
		}

		// Setup mock expectations
		rows := sqlmock.NewRows([]string{"id", "created_at", "updated_at", "deployment_name", "image_uri", "node_name", "port", "env", "num_cpu", "mem", "gpu_mem", "gpu_model", "proxy_body_size", "user_id", "org_id"}).
			AddRow(deploymentID, time.Now(), time.Now(), deploymentName, "test-image", "cpu-node", int32(8080), []byte(`{}`), 1, 512, nil, nil, 100, nil, nil)

		mock.ExpectQuery(`SELECT \* FROM "custom_image_deployments" WHERE deployment_name = \$1 ORDER BY "custom_image_deployments"."id" LIMIT \$2`).
			WithArgs(deploymentName, 1).
			WillReturnRows(rows)

		// Call the method
		result, err := repo.FindECRDeployment(context.Background(), input)

		// Assertions
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, deploymentID, result.ID)
		assert.Equal(t, deploymentName, result.DeploymentName)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("not found", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		deploymentID := uuid.New()
		input := FindECRDeploymentInput{
			ID: &deploymentID,
		}

		// Setup mock expectations
		mock.ExpectQuery(`SELECT \* FROM "custom_image_deployments" WHERE id = \$1 ORDER BY "custom_image_deployments"."id" LIMIT \$2`).
			WithArgs(deploymentID, 1).
			WillReturnError(gorm.ErrRecordNotFound)

		// Call the method
		result, err := repo.FindECRDeployment(context.Background(), input)

		// Assertions
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Equal(t, gorm.ErrRecordNotFound, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestFindUserDeployments(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		userID := uuid.New()
		deployment1ID := uuid.New()
		deployment2ID := uuid.New()

		// Setup mock expectations
		rows := sqlmock.NewRows([]string{"id", "created_at", "updated_at", "deployment_name", "image_uri", "node_name", "port", "env", "num_cpu", "mem", "gpu_mem", "gpu_model", "proxy_body_size", "user_id", "org_id"}).
			AddRow(deployment1ID, time.Now(), time.Now(), "deployment-1", "image-1", "node-1", int32(8080), []byte(`{}`), 1, 512, nil, nil, 100, userID, nil).
			AddRow(deployment2ID, time.Now(), time.Now(), "deployment-2", "image-2", "node-2", int32(9090), []byte(`{}`), 2, 1024, nil, nil, 200, userID, nil)

		mock.ExpectQuery(`SELECT \* FROM "custom_image_deployments" WHERE user_id = \$1`).
			WithArgs(userID).
			WillReturnRows(rows)

		// Call the method
		result, err := repo.FindUserDeployments(context.Background(), userID)

		// Assertions
		assert.NoError(t, err)
		assert.Len(t, result, 2)
		assert.Equal(t, deployment1ID, result[0].ID)
		assert.Equal(t, "deployment-1", result[0].DeploymentName)
		assert.Equal(t, deployment2ID, result[1].ID)
		assert.Equal(t, "deployment-2", result[1].DeploymentName)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		userID := uuid.New()

		// Setup mock expectations
		mock.ExpectQuery(`SELECT \* FROM "custom_image_deployments" WHERE user_id = \$1`).
			WithArgs(userID).
			WillReturnError(gorm.ErrInvalidDB)

		// Call the method
		result, err := repo.FindUserDeployments(context.Background(), userID)

		// Assertions
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Contains(t, err.Error(), "failed to find user deployments")

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestFindOrgDeployments(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		orgID := uuid.New()
		deployment1ID := uuid.New()
		deployment2ID := uuid.New()

		// Setup mock expectations
		rows := sqlmock.NewRows([]string{"id", "created_at", "updated_at", "deployment_name", "image_uri", "node_name", "port", "env", "num_cpu", "mem", "gpu_mem", "gpu_model", "proxy_body_size", "user_id", "org_id"}).
			AddRow(deployment1ID, time.Now(), time.Now(), "org-deployment-1", "image-1", "node-1", int32(8080), []byte(`{}`), 1, 512, nil, nil, 100, nil, orgID).
			AddRow(deployment2ID, time.Now(), time.Now(), "org-deployment-2", "image-2", "node-2", int32(9090), []byte(`{}`), 2, 1024, nil, nil, 200, nil, orgID)

		mock.ExpectQuery(`SELECT \* FROM "custom_image_deployments" WHERE org_id = \$1`).
			WithArgs(orgID).
			WillReturnRows(rows)

		// Call the method
		result, err := repo.FindOrgDeployments(context.Background(), orgID)

		// Assertions
		assert.NoError(t, err)
		assert.Len(t, result, 2)
		assert.Equal(t, deployment1ID, result[0].ID)
		assert.Equal(t, "org-deployment-1", result[0].DeploymentName)
		assert.Equal(t, deployment2ID, result[1].ID)
		assert.Equal(t, "org-deployment-2", result[1].DeploymentName)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		orgID := uuid.New()

		// Setup mock expectations
		mock.ExpectQuery(`SELECT \* FROM "custom_image_deployments" WHERE org_id = \$1`).
			WithArgs(orgID).
			WillReturnError(gorm.ErrInvalidDB)

		// Call the method
		result, err := repo.FindOrgDeployments(context.Background(), orgID)

		// Assertions
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Contains(t, err.Error(), "failed to find organization deployments")

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestDeleteECRDeployment(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		deploymentID := uuid.New()

		// Setup mock expectations
		mock.ExpectBegin()
		mock.ExpectExec(`DELETE FROM "custom_image_deployments" WHERE "custom_image_deployments"."id" = \$1`).
			WithArgs(deploymentID).
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		// Call the method
		err := repo.DeleteECRDeployment(context.Background(), deploymentID)

		// Assertions
		assert.NoError(t, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		deploymentID := uuid.New()

		// Setup mock expectations
		mock.ExpectBegin()
		mock.ExpectExec(`DELETE FROM "custom_image_deployments" WHERE "custom_image_deployments"."id" = \$1`).
			WithArgs(deploymentID).
			WillReturnError(gorm.ErrInvalidDB)
		mock.ExpectRollback()

		// Call the method
		err := repo.DeleteECRDeployment(context.Background(), deploymentID)

		// Assertions
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed to delete deployment")

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestCreateECRDeploymentEnv(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		deploymentID := uuid.New()
		input := CreateECRDeploymentEnvInput{
			ID:    deploymentID,
			Key:   "NEW_ENV_VAR",
			Value: "new_value",
		}

		// Setup mock expectations - GORM wraps UpdateColumn in a transaction
		mock.ExpectBegin()
		mock.ExpectExec(`UPDATE "custom_image_deployments" SET "env"`).
			WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), deploymentID). // JSONB_SET args and id
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		// Call the method
		err := repo.CreateECRDeploymentEnv(context.Background(), input)

		// Assertions
		assert.NoError(t, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		deploymentID := uuid.New()
		input := CreateECRDeploymentEnvInput{
			ID:    deploymentID,
			Key:   "NEW_ENV_VAR",
			Value: "new_value",
		}

		// Setup mock expectations - GORM wraps UpdateColumn in a transaction
		mock.ExpectBegin()
		mock.ExpectExec(`UPDATE "custom_image_deployments" SET "env"`).
			WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), deploymentID). // JSONB_SET args and id
			WillReturnError(gorm.ErrInvalidDB)
		mock.ExpectRollback()

		// Call the method
		err := repo.CreateECRDeploymentEnv(context.Background(), input)

		// Assertions
		assert.Error(t, err)
		assert.Equal(t, gorm.ErrInvalidDB, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestUpdateECRDeploymentEnv(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		deploymentID := uuid.New()
		input := UpdateECRDeploymentEnvInput{
			ID:     deploymentID,
			OldKey: "OLD_KEY",
			Key:    "NEW_KEY",
			Value:  "updated_value",
		}

		// Setup mock expectations for finding the deployment
		rows := sqlmock.NewRows([]string{"id", "created_at", "updated_at", "deployment_name", "image_uri", "node_name", "port", "env", "num_cpu", "mem", "gpu_mem", "gpu_model", "proxy_body_size", "user_id", "org_id"}).
			AddRow(deploymentID, time.Now(), time.Now(), "test-deployment", "test-image", "cpu-node", int32(8080), []byte(`{"OLD_KEY":"old_value","KEEP_KEY":"keep_value"}`), 1, 512, nil, nil, 100, nil, nil)

		mock.ExpectQuery(`SELECT \* FROM "custom_image_deployments" WHERE id = \$1 ORDER BY "custom_image_deployments"."id" LIMIT \$2`).
			WithArgs(deploymentID, 1).
			WillReturnRows(rows)

		// Setup mock expectations for updating the deployment
		mock.ExpectBegin()
		mock.ExpectExec(`UPDATE "custom_image_deployments"`).
			WithArgs(
				sqlmock.AnyArg(), // created_at
				sqlmock.AnyArg(), // updated_at
				sqlmock.AnyArg(), // deployment_name
				sqlmock.AnyArg(), // image_uri
				sqlmock.AnyArg(), // user_id
				sqlmock.AnyArg(), // org_id
				sqlmock.AnyArg(), // node_name
				sqlmock.AnyArg(), // port
				sqlmock.AnyArg(), // env
				sqlmock.AnyArg(), // num_cpu
				sqlmock.AnyArg(), // mem
				sqlmock.AnyArg(), // gpu_mem
				sqlmock.AnyArg(), // gpu_model
				sqlmock.AnyArg(), // proxy_body_size
				sqlmock.AnyArg(), // WHERE id
			).
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		// Call the method
		err := repo.UpdateECRDeploymentEnv(context.Background(), input)

		// Assertions
		assert.NoError(t, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("deployment not found", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		deploymentID := uuid.New()
		input := UpdateECRDeploymentEnvInput{
			ID:     deploymentID,
			OldKey: "OLD_KEY",
			Key:    "NEW_KEY",
			Value:  "updated_value",
		}

		// Setup mock expectations
		mock.ExpectQuery(`SELECT \* FROM "custom_image_deployments" WHERE id = \$1 ORDER BY "custom_image_deployments"."id" LIMIT \$2`).
			WithArgs(deploymentID, 1).
			WillReturnError(gorm.ErrRecordNotFound)

		// Call the method
		err := repo.UpdateECRDeploymentEnv(context.Background(), input)

		// Assertions
		assert.Error(t, err)
		assert.Equal(t, gorm.ErrRecordNotFound, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("save error after env update", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		deploymentID := uuid.New()
		input := UpdateECRDeploymentEnvInput{
			ID:     deploymentID,
			OldKey: "OLD_KEY",
			Key:    "NEW_KEY",
			Value:  "updated_value",
		}

		// Setup mock expectations for finding the deployment
		rows := sqlmock.NewRows([]string{"id", "created_at", "updated_at", "deployment_name", "image_uri", "node_name", "port", "env", "num_cpu", "mem", "gpu_mem", "gpu_model", "proxy_body_size", "user_id", "org_id"}).
			AddRow(deploymentID, time.Now(), time.Now(), "test-deployment", "test-image", "cpu-node", int32(8080), []byte(`{"OLD_KEY":"old_value","KEEP_KEY":"keep_value"}`), 1, 512, nil, nil, 100, nil, nil)

		mock.ExpectQuery(`SELECT \* FROM "custom_image_deployments" WHERE id = \$1 ORDER BY "custom_image_deployments"."id" LIMIT \$2`).
			WithArgs(deploymentID, 1).
			WillReturnRows(rows)

		// Setup mock expectations for updating the deployment with error
		mock.ExpectBegin()
		mock.ExpectExec(`UPDATE "custom_image_deployments"`).
			WithArgs(
				sqlmock.AnyArg(), // created_at
				sqlmock.AnyArg(), // updated_at
				sqlmock.AnyArg(), // deployment_name
				sqlmock.AnyArg(), // image_uri
				sqlmock.AnyArg(), // user_id
				sqlmock.AnyArg(), // org_id
				sqlmock.AnyArg(), // node_name
				sqlmock.AnyArg(), // port
				sqlmock.AnyArg(), // env
				sqlmock.AnyArg(), // num_cpu
				sqlmock.AnyArg(), // mem
				sqlmock.AnyArg(), // gpu_mem
				sqlmock.AnyArg(), // gpu_model
				sqlmock.AnyArg(), // proxy_body_size
				sqlmock.AnyArg(), // WHERE id
			).
			WillReturnError(gorm.ErrInvalidDB)
		mock.ExpectRollback()

		// Call the method
		err := repo.UpdateECRDeploymentEnv(context.Background(), input)

		// Assertions
		assert.Error(t, err)
		assert.Equal(t, gorm.ErrInvalidDB, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestDeleteECRDeploymentEnv(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		deploymentID := uuid.New()
		input := DeleteECRDeploymentEnv{
			ID:  deploymentID,
			Key: "DELETE_KEY",
		}

		// Setup mock expectations for finding the deployment
		rows := sqlmock.NewRows([]string{"id", "created_at", "updated_at", "deployment_name", "image_uri", "node_name", "port", "env", "num_cpu", "mem", "gpu_mem", "gpu_model", "proxy_body_size", "user_id", "org_id"}).
			AddRow(deploymentID, time.Now(), time.Now(), "test-deployment", "test-image", "cpu-node", int32(8080), []byte(`{"DELETE_KEY":"delete_value","KEEP_KEY":"keep_value"}`), 1, 512, nil, nil, 100, nil, nil)

		mock.ExpectQuery(`SELECT \* FROM "custom_image_deployments" WHERE id = \$1 ORDER BY "custom_image_deployments"."id" LIMIT \$2`).
			WithArgs(deploymentID, 1).
			WillReturnRows(rows)

		// Setup mock expectations for updating the deployment
		mock.ExpectBegin()
		mock.ExpectExec(`UPDATE "custom_image_deployments"`).
			WithArgs(
				sqlmock.AnyArg(), // created_at
				sqlmock.AnyArg(), // updated_at
				sqlmock.AnyArg(), // deployment_name
				sqlmock.AnyArg(), // image_uri
				sqlmock.AnyArg(), // user_id
				sqlmock.AnyArg(), // org_id
				sqlmock.AnyArg(), // node_name
				sqlmock.AnyArg(), // port
				sqlmock.AnyArg(), // env
				sqlmock.AnyArg(), // num_cpu
				sqlmock.AnyArg(), // mem
				sqlmock.AnyArg(), // gpu_mem
				sqlmock.AnyArg(), // gpu_model
				sqlmock.AnyArg(), // proxy_body_size
				sqlmock.AnyArg(), // WHERE id
			).
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		// Call the method
		err := repo.DeleteECRDeploymentEnv(context.Background(), input)

		// Assertions
		assert.NoError(t, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("deployment not found", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		deploymentID := uuid.New()
		input := DeleteECRDeploymentEnv{
			ID:  deploymentID,
			Key: "DELETE_KEY",
		}

		// Setup mock expectations
		mock.ExpectQuery(`SELECT \* FROM "custom_image_deployments" WHERE id = \$1 ORDER BY "custom_image_deployments"."id" LIMIT \$2`).
			WithArgs(deploymentID, 1).
			WillReturnError(gorm.ErrRecordNotFound)

		// Call the method
		err := repo.DeleteECRDeploymentEnv(context.Background(), input)

		// Assertions
		assert.Error(t, err)
		assert.Equal(t, gorm.ErrRecordNotFound, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestListECRDeployment(t *testing.T) {
	t.Run("success with pagination", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		userID := uuid.New()
		deployment1ID := uuid.New()
		deployment2ID := uuid.New()

		pagination := types.Pagination{
			PageNo:   1,
			PageSize: 10,
		}
		order := types.OrderBy{
			"id": "asc",
		}
		input := ListECRDeploymentInput{}

		// Setup mock expectations for count query
		countRows := sqlmock.NewRows([]string{"count"}).
			AddRow(2)

		mock.ExpectQuery(`SELECT count\(\*\) FROM "custom_image_deployments"`).
			WillReturnRows(countRows)

		// Setup mock expectations for deployments query
		rows := sqlmock.NewRows([]string{"id", "created_at", "updated_at", "deployment_name", "image_uri", "node_name", "port", "env", "num_cpu", "mem", "gpu_mem", "gpu_model", "proxy_body_size", "user_id", "org_id"}).
			AddRow(deployment1ID, time.Now(), time.Now(), "deployment-1", "image-1", "node-1", int32(8080), []byte(`{}`), 1, 512, nil, nil, 100, userID, nil).
			AddRow(deployment2ID, time.Now(), time.Now(), "deployment-2", "image-2", "node-2", int32(9090), []byte(`{}`), 2, 1024, nil, nil, 200, userID, nil)

		mock.ExpectQuery(`SELECT \* FROM "custom_image_deployments" ORDER BY id asc LIMIT \$1`).
			WithArgs(10).
			WillReturnRows(rows)

		// Setup mock expectations for User preload
		userRows := sqlmock.NewRows([]string{"id", "name", "username", "ref_git_userid", "role"}).
			AddRow(userID, "Test User", "testuser", 123, "user")

		mock.ExpectQuery(`SELECT \* FROM "public"."users" WHERE "users"."id" = \$1`).
			WithArgs(userID).
			WillReturnRows(userRows)

		// Call the method
		result, total, err := repo.ListECRDeployment(context.Background(), pagination, order, input)

		// Assertions
		assert.NoError(t, err)
		assert.Len(t, result, 2)
		assert.Equal(t, int64(2), total)
		assert.Equal(t, deployment1ID, result[0].ID)
		assert.Equal(t, "deployment-1", result[0].DeploymentName)
		assert.Equal(t, deployment2ID, result[1].ID)
		assert.Equal(t, "deployment-2", result[1].DeploymentName)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("success with user filter", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		userID := uuid.New()
		pagination := types.Pagination{
			PageNo:   1,
			PageSize: 10,
		}
		order := types.OrderBy{
			"id": "asc",
		}
		input := ListECRDeploymentInput{
			UserID: &userID,
		}

		// Setup mock expectations for count query
		countRows := sqlmock.NewRows([]string{"count"}).
			AddRow(1)

		mock.ExpectQuery(`SELECT count\(\*\) FROM "custom_image_deployments" WHERE user_id = \$1`).
			WithArgs(userID).
			WillReturnRows(countRows)

		// Setup mock expectations for deployments query
		rows := sqlmock.NewRows([]string{"id", "created_at", "updated_at", "deployment_name", "image_uri", "node_name", "port", "env", "num_cpu", "mem", "gpu_mem", "gpu_model", "proxy_body_size", "user_id", "org_id"}).
			AddRow(uuid.New(), time.Now(), time.Now(), "user-deployment", "image-1", "node-1", int32(8080), []byte(`{}`), 1, 512, nil, nil, 100, userID, nil)

		mock.ExpectQuery(`SELECT \* FROM "custom_image_deployments" WHERE user_id = \$1 ORDER BY id asc LIMIT \$2`).
			WithArgs(userID, 10).
			WillReturnRows(rows)

		// Setup mock expectations for User preload
		userRows := sqlmock.NewRows([]string{"id", "name", "username", "ref_git_userid", "role"}).
			AddRow(userID, "Test User", "testuser", 123, "user")

		mock.ExpectQuery(`SELECT \* FROM "public"."users" WHERE "users"."id" = \$1`).
			WithArgs(userID).
			WillReturnRows(userRows)

		// Call the method
		result, total, err := repo.ListECRDeployment(context.Background(), pagination, order, input)

		// Assertions
		assert.NoError(t, err)
		assert.Len(t, result, 1)
		assert.Equal(t, int64(1), total)
		assert.Equal(t, "user-deployment", result[0].DeploymentName)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("success with search filter", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		searchTerm := "test"
		pagination := types.Pagination{
			PageNo:   1,
			PageSize: 10,
		}
		order := types.OrderBy{
			"id": "asc",
		}
		input := ListECRDeploymentInput{
			Search: &searchTerm,
		}

		// Setup mock expectations for count query
		countRows := sqlmock.NewRows([]string{"count"}).
			AddRow(1)

		mock.ExpectQuery(`SELECT count\(\*\) FROM "custom_image_deployments" WHERE deployment_name ILIKE \$1`).
			WithArgs("%test%").
			WillReturnRows(countRows)

		// Setup mock expectations for deployments query
		rows := sqlmock.NewRows([]string{"id", "created_at", "updated_at", "deployment_name", "image_uri", "node_name", "port", "env", "num_cpu", "mem", "gpu_mem", "gpu_model", "proxy_body_size", "user_id", "org_id"}).
			AddRow(uuid.New(), time.Now(), time.Now(), "test-deployment", "image-1", "node-1", int32(8080), []byte(`{}`), 1, 512, nil, nil, 100, nil, nil)

		mock.ExpectQuery(`SELECT \* FROM "custom_image_deployments" WHERE deployment_name ILIKE \$1 ORDER BY id asc LIMIT \$2`).
			WithArgs("%test%", 10).
			WillReturnRows(rows)

		// Call the method
		result, total, err := repo.ListECRDeployment(context.Background(), pagination, order, input)

		// Assertions
		assert.NoError(t, err)
		assert.Len(t, result, 1)
		assert.Equal(t, int64(1), total)
		assert.Equal(t, "test-deployment", result[0].DeploymentName)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("count error", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		pagination := types.Pagination{
			PageNo:   1,
			PageSize: 10,
		}
		order := types.OrderBy{
			"id": "asc",
		}
		input := ListECRDeploymentInput{}

		// Setup mock expectations for count query
		mock.ExpectQuery(`SELECT count\(\*\) FROM "custom_image_deployments"`).
			WillReturnError(gorm.ErrInvalidDB)

		// Call the method
		result, total, err := repo.ListECRDeployment(context.Background(), pagination, order, input)

		// Assertions
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Equal(t, int64(0), total)
		assert.Contains(t, err.Error(), "failed to count deployments")

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("list error", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		pagination := types.Pagination{
			PageNo:   1,
			PageSize: 10,
		}
		order := types.OrderBy{
			"id": "asc",
		}
		input := ListECRDeploymentInput{}

		// Setup mock expectations for count query
		countRows := sqlmock.NewRows([]string{"count"}).
			AddRow(2)

		mock.ExpectQuery(`SELECT count\(\*\) FROM "custom_image_deployments"`).
			WillReturnRows(countRows)

		// Setup mock expectations for deployments query
		mock.ExpectQuery(`SELECT \* FROM "custom_image_deployments" ORDER BY id asc LIMIT \$1`).
			WithArgs(10).
			WillReturnError(gorm.ErrInvalidDB)

		// Call the method
		result, total, err := repo.ListECRDeployment(context.Background(), pagination, order, input)

		// Assertions
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Equal(t, int64(0), total)
		assert.Contains(t, err.Error(), "failed to list deployments")

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("success with org filter", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		orgID := uuid.New()
		pagination := types.Pagination{
			PageNo:   1,
			PageSize: 10,
		}
		order := types.OrderBy{
			"id": "asc",
		}
		input := ListECRDeploymentInput{
			OrgID: &orgID,
		}

		// Setup mock expectations for count query
		countRows := sqlmock.NewRows([]string{"count"}).
			AddRow(1)

		mock.ExpectQuery(`SELECT count\(\*\) FROM "custom_image_deployments" WHERE org_id = \$1`).
			WithArgs(orgID).
			WillReturnRows(countRows)

		// Setup mock expectations for deployments query
		rows := sqlmock.NewRows([]string{"id", "created_at", "updated_at", "deployment_name", "image_uri", "node_name", "port", "env", "num_cpu", "mem", "gpu_mem", "gpu_model", "proxy_body_size", "user_id", "org_id"}).
			AddRow(uuid.New(), time.Now(), time.Now(), "org-deployment", "image-1", "node-1", int32(8080), []byte(`{}`), 1, 512, nil, nil, 100, nil, orgID)

		mock.ExpectQuery(`SELECT \* FROM "custom_image_deployments" WHERE org_id = \$1 ORDER BY id asc LIMIT \$2`).
			WithArgs(orgID, 10).
			WillReturnRows(rows)

		// Call the method
		result, total, err := repo.ListECRDeployment(context.Background(), pagination, order, input)

		// Assertions
		assert.NoError(t, err)
		assert.Len(t, result, 1)
		assert.Equal(t, int64(1), total)
		assert.Equal(t, "org-deployment", result[0].DeploymentName)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}
