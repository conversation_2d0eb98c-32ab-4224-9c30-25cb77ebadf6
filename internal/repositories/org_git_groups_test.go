package repository

import (
	"context"
	"database/sql"
	"testing"
	"time"

	sqlmock "github.com/DATA-DOG/go-sqlmock"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"

	"api-server/internal/utils"
)

func TestCreateOrgGitGroup(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		// Setup
		db, mock := setupTestDB(t)
		repo := &repository{db: db}
		orgID := uuid.New()
		input := CreateOrgGitGroupInput{
			OrgID:            orgID,
			RefGitModelsID:   1,
			RefGitSpacesID:   2,
			RefGitDatasetsID: 3,
			RefGitComposesID: utils.Ptr(int64(4)),
		}

		// Mock the expected SQL query
		mock.ExpectBegin()
		mock.ExpectQuery(`INSERT INTO "org_git_groups" \("created_at","updated_at","ref_git_models_id","ref_git_spaces_id","ref_git_datasets_id","org_id","id"\) VALUES \(\$1,\$2,\$3,\$4,\$5,\$6,\$7\) RETURNING "id"`).
			WithArgs(
				sqlmock.AnyArg(), // created_at
				sqlmock.AnyArg(), // updated_at
				1,                // ref_git_models_id
				2,                // ref_git_spaces_id
				3,                // ref_git_datasets_id
				orgID,            // org_id
				sqlmock.AnyArg(), // id (let GORM generate this)
			).
			WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(uuid.New()))
		mock.ExpectCommit()

		// Execute
		result, err := repo.CreateOrgGitGroup(context.Background(), input)

		// Assert
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, int64(1), result.RefGitModelsID)
		assert.Equal(t, int64(2), result.RefGitSpacesID)
		assert.Equal(t, int64(3), result.RefGitDatasetsID)
		assert.Equal(t, int64(4), result.RefGitComposesID)
		assert.NotEmpty(t, result.ID)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		// Setup
		db, mock := setupTestDB(t)
		repo := &repository{db: db}
		orgID := uuid.New()
		input := CreateOrgGitGroupInput{
			OrgID:            orgID,
			RefGitModelsID:   1,
			RefGitSpacesID:   2,
			RefGitDatasetsID: 3,
			RefGitComposesID: utils.Ptr(int64(4)),
		}

		// Mock the expected SQL query with error
		mock.ExpectBegin()
		mock.ExpectQuery(`INSERT INTO "org_git_groups" \("created_at","updated_at","ref_git_models_id","ref_git_spaces_id","ref_git_datasets_id","org_id","id"\) VALUES \(\$1,\$2,\$3,\$4,\$5,\$6,\$7\) RETURNING "id"`).
			WithArgs(
				sqlmock.AnyArg(), // created_at
				sqlmock.AnyArg(), // updated_at
				1,                // ref_git_models_id
				2,                // ref_git_spaces_id
				3,                // ref_git_datasets_id
				orgID,            // org_id
				sqlmock.AnyArg(), // id (let GORM generate this)
			).
			WillReturnError(sql.ErrConnDone)
		mock.ExpectRollback()

		// Execute
		result, err := repo.CreateOrgGitGroup(context.Background(), input)

		// Assert
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestFindOrgGitGroup(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		// Setup
		db, mock := setupTestDB(t)
		repo := &repository{db: db}
		orgID := uuid.New()

		// Mock the expected SQL query
		now := time.Now()
		rows := sqlmock.NewRows([]string{"id", "org_id", "ref_git_models_id", "ref_git_spaces_id", "ref_git_datasets_id", "created_at", "updated_at"}).
			AddRow(uuid.New(), orgID, 1, 2, 3, now, now)
		mock.ExpectQuery(`SELECT \* FROM "org_git_groups" WHERE "org_git_groups"."org_id" = \$1 ORDER BY "org_git_groups"."ref_git_models_id" LIMIT \$2`).
			WithArgs(orgID, 1).
			WillReturnRows(rows)

		// Execute
		result, err := repo.FindOrgGitGroup(context.Background(), orgID)

		// Assert
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, int64(1), result.RefGitModelsID)
		assert.Equal(t, int64(2), result.RefGitSpacesID)
		assert.Equal(t, int64(3), result.RefGitDatasetsID)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("not found", func(t *testing.T) {
		// Setup
		db, mock := setupTestDB(t)
		repo := &repository{db: db}
		orgID := uuid.New()

		// Mock the expected SQL query with no rows
		mock.ExpectQuery(`SELECT \* FROM "org_git_groups" WHERE "org_git_groups"."org_id" = \$1 ORDER BY "org_git_groups"."ref_git_models_id" LIMIT \$2`).
			WithArgs(orgID, 1).
			WillReturnRows(sqlmock.NewRows([]string{"id", "org_id", "ref_git_models_id", "ref_git_spaces_id", "ref_git_datasets_id", "created_at", "updated_at"}))

		// Execute
		result, err := repo.FindOrgGitGroup(context.Background(), orgID)

		// Assert
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Equal(t, gorm.ErrRecordNotFound, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestDeleteOrgGitGroup(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		// Setup
		db, mock := setupTestDB(t)
		repo := &repository{db: db}
		orgID := uuid.New()

		// Mock the expected SQL query
		mock.ExpectBegin()
		mock.ExpectExec(`DELETE FROM "org_git_groups" WHERE org_id = \$1`).
			WithArgs(orgID).
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		// Execute
		err := repo.DeleteOrgGitGroup(context.Background(), orgID)

		// Assert
		assert.NoError(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		// Setup
		db, mock := setupTestDB(t)
		repo := &repository{db: db}
		orgID := uuid.New()

		// Mock the expected SQL query with error
		mock.ExpectBegin()
		mock.ExpectExec(`DELETE FROM "org_git_groups" WHERE org_id = \$1`).
			WithArgs(orgID).
			WillReturnError(sql.ErrConnDone)
		mock.ExpectRollback()

		// Execute
		err := repo.DeleteOrgGitGroup(context.Background(), orgID)

		// Assert
		assert.Error(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}
