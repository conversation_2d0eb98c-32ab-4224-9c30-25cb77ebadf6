package repository

import (
	"fmt"

	"gorm.io/gorm"

	"api-server/internal/types"
)

// paginate returns a GORM scope function that handles pagination.
//
// Parameters:
//   - pageNo: The page number (1-based)
//   - pageSize: The number of items per page
//
// Returns:
//   - func(db *gorm.DB) *gorm.DB: A GORM scope function that applies pagination
func paginate(pageNo, pageSize int) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		offset := (pageNo - 1) * pageSize
		return db.Offset(offset).Limit(pageSize)
	}
}

// orderBy returns a GORM scope function that handles ordering.
//
// Parameters:
//   - orderBy: A map of column names to their sort order
//
// Returns:
//   - func(db *gorm.DB) *gorm.DB: A GORM scope function that applies ordering
func orderBy(orderBy types.OrderBy) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		for column, order := range orderBy {
			db = db.Order(fmt.Sprintf("%s %s", column, order))
		}
		return db
	}
}
