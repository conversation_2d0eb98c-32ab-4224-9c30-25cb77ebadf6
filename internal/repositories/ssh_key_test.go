package repository

import (
	"context"
	"database/sql"
	"testing"
	"time"

	sqlmock "github.com/DATA-DOG/go-sqlmock"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"

	"api-server/internal/enums"
	"api-server/internal/types"
)

func TestGetSSHKeyByID(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		// Setup
		db, mock := setupTestDB(t)
		repo := &repository{db: db}
		keyID := uuid.New()
		now := time.Now()

		// Mock the expected SQL query
		rows := sqlmock.NewRows([]string{"id", "user_id", "name", "public_key", "ref_git_sshkeyid", "usage_type", "expires_at", "created_at", "updated_at"}).
			AddRow(keyID, uuid.New(), "test-key", "ssh-rsa key", 1, enums.UsageType_Auth, now, now, now)
		mock.ExpectQuery(`SELECT \* FROM "user_ssh_public_keys" WHERE id = \$1 ORDER BY "user_ssh_public_keys"."id" LIMIT \$2`).
			WithArgs(keyID, 1).
			WillReturnRows(rows)

		// Execute
		result, err := repo.GetSSHKeyByID(context.Background(), keyID)

		// Assert
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, keyID, result.ID)
		assert.Equal(t, "test-key", result.Name)
		assert.Equal(t, "ssh-rsa key", result.PublicKey)
		assert.Equal(t, int64(1), result.RefGitSSHKeyID)
		assert.Equal(t, enums.UsageType_Auth, result.UsageType)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("not found", func(t *testing.T) {
		// Setup
		db, mock := setupTestDB(t)
		repo := &repository{db: db}
		keyID := uuid.New()

		// Mock the expected SQL query with no rows
		mock.ExpectQuery(`SELECT \* FROM "user_ssh_public_keys" WHERE id = \$1 ORDER BY "user_ssh_public_keys"."id" LIMIT \$2`).
			WithArgs(keyID, 1).
			WillReturnError(gorm.ErrRecordNotFound)

		// Execute
		result, err := repo.GetSSHKeyByID(context.Background(), keyID)

		// Assert
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Equal(t, gorm.ErrRecordNotFound, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		// Setup
		db, mock := setupTestDB(t)
		repo := &repository{db: db}
		keyID := uuid.New()

		// Mock the expected SQL query with error
		mock.ExpectQuery(`SELECT \* FROM "user_ssh_public_keys" WHERE id = \$1 ORDER BY "user_ssh_public_keys"."id" LIMIT \$2`).
			WithArgs(keyID, 1).
			WillReturnError(sql.ErrConnDone)

		// Execute
		result, err := repo.GetSSHKeyByID(context.Background(), keyID)

		// Assert
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestListSSHKeys(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		// Setup
		db, mock := setupTestDB(t)
		repo := &repository{db: db}
		userID := uuid.New()
		now := time.Now()

		// Mock the expected SQL query
		rows := sqlmock.NewRows([]string{"id", "user_id", "name", "public_key", "ref_git_sshkeyid", "usage_type", "expires_at", "created_at", "updated_at"}).
			AddRow(uuid.New(), userID, "key1", "ssh-rsa key1", 1, enums.UsageType_Auth, now, now, now).
			AddRow(uuid.New(), userID, "key2", "ssh-rsa key2", 2, enums.UsageType_Signing, now, now, now)
		mock.ExpectQuery(`SELECT \* FROM "user_ssh_public_keys" WHERE user_id = \$1 ORDER BY created_at asc LIMIT \$2`).
			WithArgs(userID, 10).
			WillReturnRows(rows)

		// Execute
		result, err := repo.ListSSHKeys(context.Background(), 1, 10, types.OrderBy{enums.OrderByColumn_CreatedAt: enums.OrderByDirection_Asc}, GetSSHKeyQuery{UserId: userID})

		// Assert
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Len(t, result, 2)
		assert.Equal(t, "key1", result[0].Name)
		assert.Equal(t, "key2", result[1].Name)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		// Setup
		db, mock := setupTestDB(t)
		repo := &repository{db: db}
		userID := uuid.New()

		// Mock the expected SQL query with error
		mock.ExpectQuery(`SELECT \* FROM "user_ssh_public_keys" WHERE user_id = \$1 ORDER BY created_at asc LIMIT \$2`).
			WithArgs(userID, 10).
			WillReturnError(sql.ErrConnDone)

		// Execute
		result, err := repo.ListSSHKeys(context.Background(), 1, 10, types.OrderBy{enums.OrderByColumn_CreatedAt: enums.OrderByDirection_Asc}, GetSSHKeyQuery{UserId: userID})

		// Assert
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestCountAllSSHKeys(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		// Setup
		db, mock := setupTestDB(t)
		repo := &repository{db: db}
		userID := uuid.New()

		// Mock the expected SQL query
		rows := sqlmock.NewRows([]string{"count"}).AddRow(5)
		mock.ExpectQuery(`SELECT count\(\*\) FROM "user_ssh_public_keys" WHERE user_id = \$1`).
			WithArgs(userID).
			WillReturnRows(rows)

		// Execute
		result, err := repo.CountAllSSHKeys(context.Background(), GetSSHKeyQuery{UserId: userID})

		// Assert
		assert.NoError(t, err)
		assert.Equal(t, int64(5), result)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		// Setup
		db, mock := setupTestDB(t)
		repo := &repository{db: db}
		userID := uuid.New()

		// Mock the expected SQL query with error
		mock.ExpectQuery(`SELECT count\(\*\) FROM "user_ssh_public_keys" WHERE user_id = \$1`).
			WithArgs(userID).
			WillReturnError(sql.ErrConnDone)

		// Execute
		result, err := repo.CountAllSSHKeys(context.Background(), GetSSHKeyQuery{UserId: userID})

		// Assert
		assert.Error(t, err)
		assert.Equal(t, int64(0), result)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestDeleteSSHKeysByUserID(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		// Setup
		db, mock := setupTestDB(t)
		repo := &repository{db: db}
		userID := uuid.New()

		// Mock the expected SQL query
		mock.ExpectBegin()
		mock.ExpectExec(`DELETE FROM "user_ssh_public_keys" WHERE user_id = \$1`).
			WithArgs(userID).
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		// Execute
		err := repo.DeleteSSHKeysByUserID(context.Background(), userID)

		// Assert
		assert.NoError(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		// Setup
		db, mock := setupTestDB(t)
		repo := &repository{db: db}
		userID := uuid.New()

		// Mock the expected SQL query with error
		mock.ExpectBegin()
		mock.ExpectExec(`DELETE FROM "user_ssh_public_keys" WHERE user_id = \$1`).
			WithArgs(userID).
			WillReturnError(sql.ErrConnDone)
		mock.ExpectRollback()

		// Execute
		err := repo.DeleteSSHKeysByUserID(context.Background(), userID)

		// Assert
		assert.Error(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestIsDuplicateSSHKey(t *testing.T) {
	t.Run("key exists", func(t *testing.T) {
		// Setup
		db, mock := setupTestDB(t)
		repo := &repository{db: db}
		key := "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQ..."

		// Mock the expected SQL query
		rows := sqlmock.NewRows([]string{"id", "user_id", "name", "public_key", "ref_git_sshkeyid", "usage_type", "expires_at", "created_at", "updated_at"}).
			AddRow(uuid.New(), uuid.New(), "test-key", key, 1, enums.UsageType_Auth, time.Now(), time.Now(), time.Now())
		mock.ExpectQuery(`SELECT \* FROM "user_ssh_public_keys" WHERE public_key = \$1 ORDER BY "user_ssh_public_keys"."id" LIMIT \$2`).
			WithArgs(key, 1).
			WillReturnRows(rows)

		// Execute
		result, err := repo.IsDuplicateSSHKey(context.Background(), key)

		// Assert
		assert.NoError(t, err)
		assert.True(t, result)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("key does not exist", func(t *testing.T) {
		// Setup
		db, mock := setupTestDB(t)
		repo := &repository{db: db}
		key := "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQ..."

		// Mock the expected SQL query with no rows
		mock.ExpectQuery(`SELECT \* FROM "user_ssh_public_keys" WHERE public_key = \$1 ORDER BY "user_ssh_public_keys"."id" LIMIT \$2`).
			WithArgs(key, 1).
			WillReturnError(gorm.ErrRecordNotFound)

		// Execute
		result, err := repo.IsDuplicateSSHKey(context.Background(), key)

		// Assert
		assert.NoError(t, err)
		assert.False(t, result)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("database error", func(t *testing.T) {
		// Setup
		db, mock := setupTestDB(t)
		repo := &repository{db: db}
		key := "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQ..."

		// Mock the expected SQL query with error
		mock.ExpectQuery(`SELECT \* FROM "user_ssh_public_keys" WHERE public_key = \$1 ORDER BY "user_ssh_public_keys"."id" LIMIT \$2`).
			WithArgs(key, 1).
			WillReturnError(sql.ErrConnDone)

		// Execute
		result, err := repo.IsDuplicateSSHKey(context.Background(), key)

		// Assert
		assert.Error(t, err)
		assert.False(t, result)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}
