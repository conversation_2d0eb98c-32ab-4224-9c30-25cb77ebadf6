package repository

import (
	"context"
	"database/sql"
	"testing"
	"time"

	"api-server/internal/enums"
	"api-server/internal/types"

	sqlmock "github.com/DATA-DOG/go-sqlmock"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
)

func TestCreateAccessToken(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		// Setup
		db, mock := setupTestDB(t)
		repo := &repository{db: db}
		userID := uuid.New()
		now := time.Now()
		input := CreateAccessTokenInput{
			Name:        "test-token",
			AccessToken: "test-access-token",
			Scopes:      []enums.RepoAccessTokenScope{enums.RepoAccessToken_API},
			UserID:      userID,
			ExpiresAt:   &now,
		}

		// Mock the expected SQL query
		mock.ExpectBegin()
		mock.ExpectQuery(`INSERT INTO "user_access_tokens" \("created_at","updated_at","name","access_token","scopes","revoked","user_id","expires_at","id"\) VALUES \(\$1,\$2,\$3,\$4,\$5,\$6,\$7,\$8,\$9\) RETURNING "id"`).
			WithArgs(
				sqlmock.AnyArg(),    // created_at
				sqlmock.AnyArg(),    // updated_at
				"test-token",        // name
				"test-access-token", // access_token
				"api",               // scopes
				false,               // revoked
				userID,              // user_id
				now,                 // expires_at
				sqlmock.AnyArg(),    // id
			).
			WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(uuid.New()))
		mock.ExpectCommit()

		// Execute
		result, err := repo.CreateAccessToken(context.Background(), input)

		// Assert
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, "test-token", result.Name)
		assert.Equal(t, "test-access-token", result.AccessToken)
		assert.Equal(t, "api", result.Scopes)
		assert.Equal(t, userID, result.UserID)
		assert.Equal(t, now, *result.ExpiresAt)
		assert.False(t, result.Revoked)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		// Setup
		db, mock := setupTestDB(t)
		repo := &repository{db: db}
		userID := uuid.New()
		now := time.Now()
		input := CreateAccessTokenInput{
			Name:        "test-token",
			AccessToken: "test-access-token",
			Scopes:      []enums.RepoAccessTokenScope{enums.RepoAccessToken_API},
			UserID:      userID,
			ExpiresAt:   &now,
		}

		// Mock the expected SQL query with error
		mock.ExpectBegin()
		mock.ExpectQuery(`INSERT INTO "user_access_tokens" \("created_at","updated_at","name","access_token","scopes","revoked","user_id","expires_at","id"\) VALUES \(\$1,\$2,\$3,\$4,\$5,\$6,\$7,\$8,\$9\) RETURNING "id"`).
			WithArgs(
				sqlmock.AnyArg(),    // created_at
				sqlmock.AnyArg(),    // updated_at
				"test-token",        // name
				"test-access-token", // access_token
				"api",               // scopes
				false,               // revoked
				userID,              // user_id
				now,                 // expires_at
				sqlmock.AnyArg(),    // id
			).
			WillReturnError(sql.ErrConnDone)
		mock.ExpectRollback()

		// Execute
		result, err := repo.CreateAccessToken(context.Background(), input)

		// Assert
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestFindAccessToken(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		// Setup
		db, mock := setupTestDB(t)
		repo := &repository{db: db}
		now := time.Now()
		query := FindAccessTokenQuery{
			AccessToken: "test-access-token",
			Revoked:     false,
		}

		// Mock the expected SQL query
		rows := sqlmock.NewRows([]string{"id", "name", "access_token", "scopes", "user_id", "expires_at", "revoked", "created_at", "updated_at"}).
			AddRow(uuid.New(), "test-token", "test-access-token", "api", uuid.New(), now, false, now, now)
		mock.ExpectQuery(`SELECT \* FROM "user_access_tokens" WHERE "user_access_tokens"."access_token" = \$1 ORDER BY "user_access_tokens"."id" LIMIT \$2`).
			WithArgs("test-access-token", 1).
			WillReturnRows(rows)

		// Execute
		result, err := repo.FindAccessToken(context.Background(), query)

		// Assert
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, "test-token", result.Name)
		assert.Equal(t, "test-access-token", result.AccessToken)
		assert.Equal(t, "api", result.Scopes)
		assert.False(t, result.Revoked)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("not found", func(t *testing.T) {
		// Setup
		db, mock := setupTestDB(t)
		repo := &repository{db: db}
		query := FindAccessTokenQuery{
			AccessToken: "test-access-token",
			Revoked:     false,
		}

		// Mock the expected SQL query with no rows
		mock.ExpectQuery(`SELECT \* FROM "user_access_tokens" WHERE "user_access_tokens"."access_token" = \$1 ORDER BY "user_access_tokens"."id" LIMIT \$2`).
			WithArgs("test-access-token", 1).
			WillReturnRows(sqlmock.NewRows([]string{"id", "name", "access_token", "scopes", "user_id", "expires_at", "revoked", "created_at", "updated_at"}))

		// Execute
		result, err := repo.FindAccessToken(context.Background(), query)

		// Assert
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Equal(t, gorm.ErrRecordNotFound, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestDeleteAccessToken(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		// Setup
		db, mock := setupTestDB(t)
		repo := &repository{db: db}
		userID := uuid.New()
		tokenID := uuid.New()
		input := DeleteAccessTokenInput{
			UserID:        userID,
			AccessTokenID: tokenID,
		}

		// Mock the expected SQL query
		mock.ExpectBegin()
		mock.ExpectExec(`DELETE FROM "user_access_tokens" WHERE "user_access_tokens"."id" = \$1`).
			WithArgs(tokenID).
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		// Execute
		err := repo.DeleteAccessToken(context.Background(), input)

		// Assert
		assert.NoError(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		// Setup
		db, mock := setupTestDB(t)
		repo := &repository{db: db}
		userID := uuid.New()
		tokenID := uuid.New()
		input := DeleteAccessTokenInput{
			UserID:        userID,
			AccessTokenID: tokenID,
		}

		// Mock the expected SQL query with error
		mock.ExpectBegin()
		mock.ExpectExec(`DELETE FROM "user_access_tokens" WHERE "user_access_tokens"."id" = \$1`).
			WithArgs(tokenID).
			WillReturnError(sql.ErrConnDone)
		mock.ExpectRollback()

		// Execute
		err := repo.DeleteAccessToken(context.Background(), input)

		// Assert
		assert.Error(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestDeleteAllUserAccessToken(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		// Setup
		db, mock := setupTestDB(t)
		repo := &repository{db: db}
		userID := uuid.New()

		// Mock the expected SQL query
		mock.ExpectBegin()
		mock.ExpectExec(`DELETE FROM "user_access_tokens" WHERE user_id = \$1`).
			WithArgs(userID).
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		// Execute
		err := repo.DeleteAllUserAccessToken(context.Background(), userID)

		// Assert
		assert.NoError(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		// Setup
		db, mock := setupTestDB(t)
		repo := &repository{db: db}
		userID := uuid.New()

		// Mock the expected SQL query with error
		mock.ExpectBegin()
		mock.ExpectExec(`DELETE FROM "user_access_tokens" WHERE user_id = \$1`).
			WithArgs(userID).
			WillReturnError(sql.ErrConnDone)
		mock.ExpectRollback()

		// Execute
		err := repo.DeleteAllUserAccessToken(context.Background(), userID)

		// Assert
		assert.Error(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestListAccessToken(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		// Setup
		db, mock := setupTestDB(t)
		repo := &repository{db: db}
		userID := uuid.New()
		now := time.Now()
		query := ListAccessTokenQuery{
			UserID: userID,
		}
		pagination := types.Pagination{
			PageNo:   1,
			PageSize: 10,
		}
		order := types.OrderBy{
			enums.OrderByColumn_CreatedAt: enums.OrderByDirection_Asc,
		}

		// Mock the expected SQL query
		rows := sqlmock.NewRows([]string{"id", "name", "access_token", "scopes", "user_id", "expires_at", "revoked", "created_at", "updated_at"}).
			AddRow(uuid.New(), "test-token-1", "test-access-token-1", "api", userID, now, false, now, now).
			AddRow(uuid.New(), "test-token-2", "test-access-token-2", "api", userID, now, false, now, now)

		mock.ExpectQuery(`SELECT \* FROM "user_access_tokens" WHERE "user_access_tokens"."user_id" = \$1 ORDER BY created_at asc LIMIT \$2`).
			WithArgs(userID, 10).
			WillReturnRows(rows)

		// Execute
		result, err := repo.ListAccessToken(context.Background(), pagination, order, query)

		// Assert
		assert.NoError(t, err)
		assert.Len(t, result, 2)
		assert.Equal(t, "test-token-1", result[0].Name)
		assert.Equal(t, "test-token-2", result[1].Name)
		assert.Equal(t, userID, result[0].UserID)
		assert.Equal(t, userID, result[1].UserID)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		// Setup
		db, mock := setupTestDB(t)
		repo := &repository{db: db}
		userID := uuid.New()
		query := ListAccessTokenQuery{
			UserID: userID,
		}
		pagination := types.Pagination{
			PageNo:   1,
			PageSize: 10,
		}
		order := types.OrderBy{
			enums.OrderByColumn_CreatedAt: enums.OrderByDirection_Asc,
		}

		// Mock the expected SQL query with error
		mock.ExpectQuery(`SELECT \* FROM "user_access_tokens" WHERE "user_access_tokens"."user_id" = \$1 ORDER BY created_at asc LIMIT \$2`).
			WithArgs(userID, 10).
			WillReturnError(sql.ErrConnDone)

		// Execute
		result, err := repo.ListAccessToken(context.Background(), pagination, order, query)

		// Assert
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestCountAccessToken(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		// Setup
		db, mock := setupTestDB(t)
		repo := &repository{db: db}
		userID := uuid.New()
		query := ListAccessTokenQuery{
			UserID: userID,
		}

		// Mock the expected SQL query
		rows := sqlmock.NewRows([]string{"count"}).AddRow(5)
		mock.ExpectQuery(`SELECT count\(\*\) FROM "user_access_tokens" WHERE "user_access_tokens"."user_id" = \$1`).
			WithArgs(userID).
			WillReturnRows(rows)

		// Execute
		result, err := repo.CountAccessToken(context.Background(), query)

		// Assert
		assert.NoError(t, err)
		assert.Equal(t, int64(5), result)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		// Setup
		db, mock := setupTestDB(t)
		repo := &repository{db: db}
		userID := uuid.New()
		query := ListAccessTokenQuery{
			UserID: userID,
		}

		// Mock the expected SQL query with error
		mock.ExpectQuery(`SELECT count\(\*\) FROM "user_access_tokens" WHERE "user_access_tokens"."user_id" = \$1`).
			WithArgs(userID).
			WillReturnError(sql.ErrConnDone)

		// Execute
		result, err := repo.CountAccessToken(context.Background(), query)

		// Assert
		assert.Error(t, err)
		assert.Equal(t, int64(0), result)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}
