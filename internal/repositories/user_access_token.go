package repository

import (
	"context"
	"strings"
	"time"

	"github.com/google/uuid"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"

	"api-server/internal/entities"
	"api-server/internal/enums"
	"api-server/internal/types"
	"api-server/pkg/oteltrace"
	"api-server/pkg/otelzap"
)

// AccessTokenRepository defines the interface for user access token operations.
// It provides methods for managing user access tokens, including creating, listing,
// finding, and removing access tokens.
type AccessTokenRepository interface {
	// CreateAccessToken creates a new access token for a user.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - in: Input data containing token details
	//
	// Returns:
	//   - *entities.UserToken: The created access token if successful
	//   - error: nil if successful, otherwise the error that occurred
	CreateAccessToken(ctx context.Context, in CreateAccessTokenInput) (*entities.UserToken, error)

	// FindAccessToken retrieves a specific access token.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - in: Query parameters for finding the token
	//
	// Returns:
	//   - *entities.UserToken: The found access token if successful
	//   - error: nil if successful, otherwise the error that occurred
	FindAccessToken(ctx context.Context, in FindAccessTokenQuery) (*entities.UserToken, error)

	// ListAccessToken retrieves a paginated list of access tokens.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - pagination: Pagination parameters
	//   - order: Ordering parameters
	//   - in: Query parameters for filtering tokens
	//
	// Returns:
	//   - []entities.UserToken: List of access tokens
	//   - error: nil if successful, otherwise the error that occurred
	ListAccessToken(ctx context.Context, pagination types.Pagination, order types.OrderBy, in ListAccessTokenQuery) ([]entities.UserToken, error)

	// CountAccessToken counts the total number of access tokens.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - in: Query parameters for filtering tokens
	//
	// Returns:
	//   - int64: Total number of access tokens
	//   - error: nil if successful, otherwise the error that occurred
	CountAccessToken(ctx context.Context, in ListAccessTokenQuery) (int64, error)

	// DeleteAccessToken removes a specific access token.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - in: Input data containing token ID and user ID
	//
	// Returns:
	//   - error: nil if successful, otherwise the error that occurred
	DeleteAccessToken(ctx context.Context, in DeleteAccessTokenInput) error

	// DeleteAllUserAccessToken removes all access tokens for a user.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - userId: UUID of the user
	//
	// Returns:
	//   - error: nil if successful, otherwise the error that occurred
	DeleteAllUserAccessToken(ctx context.Context, userId uuid.UUID) error
}

var _ AccessTokenRepository = (*repository)(nil)

// CreateAccessTokenInput contains the data needed to create a new access token.
type CreateAccessTokenInput struct {
	Name        string
	AccessToken string
	Scopes      []enums.RepoAccessTokenScope
	UserID      uuid.UUID
	ExpiresAt   *time.Time
}

func (r *repository) CreateAccessToken(ctx context.Context, in CreateAccessTokenInput) (*entities.UserToken, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.access_token.CreateAccessToken",
		trace.WithAttributes(
			attribute.String("action", "CREATE_ACCESS_TOKEN"),
			attribute.String("user_id", in.UserID.String()),
		))
	defer span.End()

	otelzap.InfoWithContext(ctx, "creating access token",
		zap.String("action", "CREATE_ACCESS_TOKEN"),
		zap.String("user_id", in.UserID.String()))

	stringScopes := make([]string, len(in.Scopes))
	for i, scope := range in.Scopes {
		stringScopes[i] = string(scope)
	}

	data := entities.UserToken{
		Name:        in.Name,
		AccessToken: in.AccessToken,
		Scopes:      strings.Join(stringScopes, ","),
		UserID:      in.UserID,
		ExpiresAt:   in.ExpiresAt,
	}

	err := r.GetDB(ctx).Create(&data).Error
	if err != nil {
		span.SetStatus(codes.Error, "failed to create access token")
		span.RecordError(err)
		otelzap.ErrorWithContext(ctx, "failed to create access token", err,
			zap.String("action", "CREATE_ACCESS_TOKEN"),
			zap.String("user_id", in.UserID.String()),
			zap.String("status", "failed"))
		return nil, err
	}

	span.AddEvent("access token created successfully")
	span.SetStatus(codes.Ok, "access token created successfully")
	span.SetAttributes(attribute.String("status", "success"))
	otelzap.InfoWithContext(ctx, "access token created successfully",
		zap.String("action", "CREATE_ACCESS_TOKEN"),
		zap.String("user_id", in.UserID.String()),
		zap.String("status", "success"))
	return &data, nil
}

type FindAccessTokenQuery struct {
	AccessToken string
	Revoked     bool
}

func (r *repository) FindAccessToken(ctx context.Context, in FindAccessTokenQuery) (*entities.UserToken, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.access_token.FindAccessToken",
		trace.WithAttributes(
			attribute.String("action", "FIND_ACCESS_TOKEN"),
			attribute.String("access_token", in.AccessToken),
		))
	defer span.End()

	otelzap.InfoWithContext(ctx, "finding access token",
		zap.String("action", "FIND_ACCESS_TOKEN"),
		zap.String("access_token", in.AccessToken))

	var result entities.UserToken
	predicate := entities.UserToken{
		AccessToken: in.AccessToken,
		Revoked:     in.Revoked,
	}
	err := r.GetDB(ctx).Where(&predicate).First(&result).Error
	if err != nil {
		span.SetStatus(codes.Error, "failed to find access token")
		span.RecordError(err)
		otelzap.ErrorWithContext(ctx, "failed to find access token", err,
			zap.String("action", "FIND_ACCESS_TOKEN"),
			zap.String("access_token", in.AccessToken),
			zap.String("status", "failed"))
		return nil, err
	}

	span.AddEvent("access token found successfully")
	span.SetStatus(codes.Ok, "access token found successfully")
	span.SetAttributes(attribute.String("status", "success"))
	otelzap.InfoWithContext(ctx, "access token found successfully",
		zap.String("action", "FIND_ACCESS_TOKEN"),
		zap.String("access_token", in.AccessToken),
		zap.String("status", "success"))
	return &result, nil
}

// ListAccessTokenQuery contains the criteria for listing access tokens, typically by user ID.
type ListAccessTokenQuery struct {
	UserID uuid.UUID
}

func (r *repository) ListAccessToken(ctx context.Context, pagination types.Pagination, order types.OrderBy, in ListAccessTokenQuery) ([]entities.UserToken, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.access_token.ListAccessToken",
		trace.WithAttributes(
			attribute.String("action", "LIST_ACCESS_TOKENS"),
			attribute.String("user_id", in.UserID.String()),
		))
	defer span.End()

	otelzap.InfoWithContext(ctx, "listing access tokens",
		zap.String("action", "LIST_ACCESS_TOKENS"),
		zap.String("user_id", in.UserID.String()))

	var result []entities.UserToken
	predicate := entities.UserToken{
		UserID: in.UserID,
	}
	err := r.
		GetDB(ctx).
		Scopes(paginate(pagination.PageNo, pagination.PageSize), orderBy(order)).
		Where(&predicate).
		Find(&result).Error
	if err != nil {
		span.SetStatus(codes.Error, "failed to list access tokens")
		span.RecordError(err)
		otelzap.ErrorWithContext(ctx, "failed to list access tokens", err,
			zap.String("action", "LIST_ACCESS_TOKENS"),
			zap.String("user_id", in.UserID.String()),
			zap.String("status", "failed"))
		return nil, err
	}

	span.AddEvent("access tokens listed successfully")
	span.SetStatus(codes.Ok, "access tokens listed successfully")
	span.SetAttributes(attribute.String("status", "success"))
	otelzap.InfoWithContext(ctx, "access tokens listed successfully",
		zap.String("action", "LIST_ACCESS_TOKENS"),
		zap.String("user_id", in.UserID.String()),
		zap.String("status", "success"))
	return result, nil
}

func (r *repository) CountAccessToken(ctx context.Context, in ListAccessTokenQuery) (int64, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.access_token.CountAccessToken",
		trace.WithAttributes(
			attribute.String("action", "COUNT_ACCESS_TOKENS"),
			attribute.String("user_id", in.UserID.String()),
		))
	defer span.End()

	otelzap.InfoWithContext(ctx, "counting access tokens",
		zap.String("action", "COUNT_ACCESS_TOKENS"),
		zap.String("user_id", in.UserID.String()))

	var result int64
	predicate := entities.UserToken{
		UserID: in.UserID,
	}
	err := r.GetDB(ctx).Model(&entities.UserToken{}).Where(&predicate).Count(&result).Error
	if err != nil {
		span.SetStatus(codes.Error, "failed to count access tokens")
		span.RecordError(err)
		otelzap.ErrorWithContext(ctx, "failed to count access tokens", err,
			zap.String("action", "COUNT_ACCESS_TOKENS"),
			zap.String("user_id", in.UserID.String()),
			zap.String("status", "failed"))
		return 0, err
	}

	span.AddEvent("access tokens counted successfully")
	span.SetStatus(codes.Ok, "access tokens counted successfully")
	span.SetAttributes(attribute.String("status", "success"))
	otelzap.InfoWithContext(ctx, "access tokens counted successfully",
		zap.String("action", "COUNT_ACCESS_TOKENS"),
		zap.String("user_id", in.UserID.String()),
		zap.String("status", "success"))
	return result, nil
}

// DeleteAccessTokenInput contains the data needed to delete an access token.
type DeleteAccessTokenInput struct {
	AccessTokenID uuid.UUID
	UserID        uuid.UUID
}

func (r *repository) DeleteAccessToken(ctx context.Context, in DeleteAccessTokenInput) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.access_token.DeleteAccessToken",
		trace.WithAttributes(
			attribute.String("action", "DELETE_ACCESS_TOKEN"),
			attribute.String("access_token_id", in.AccessTokenID.String()),
			attribute.String("user_id", in.UserID.String()),
		))
	defer span.End()

	otelzap.InfoWithContext(ctx, "deleting access token",
		zap.String("action", "DELETE_ACCESS_TOKEN"),
		zap.String("access_token_id", in.AccessTokenID.String()),
		zap.String("user_id", in.UserID.String()))

	token := entities.UserToken{
		BaseModel: entities.BaseModel{
			ID: in.AccessTokenID,
		},
		UserID: in.UserID,
	}
	err := r.GetDB(ctx).Delete(&token).Error
	if err != nil {
		span.SetStatus(codes.Error, "failed to delete access token")
		span.RecordError(err)
		otelzap.ErrorWithContext(ctx, "failed to delete access token", err,
			zap.String("action", "DELETE_ACCESS_TOKEN"),
			zap.String("access_token_id", in.AccessTokenID.String()),
			zap.String("user_id", in.UserID.String()),
			zap.String("status", "failed"))
		return err
	}

	span.AddEvent("access token deleted successfully")
	span.SetStatus(codes.Ok, "access token deleted successfully")
	span.SetAttributes(attribute.String("status", "success"))
	otelzap.InfoWithContext(ctx, "access token deleted successfully",
		zap.String("action", "DELETE_ACCESS_TOKEN"),
		zap.String("access_token_id", in.AccessTokenID.String()),
		zap.String("user_id", in.UserID.String()),
		zap.String("status", "success"))
	return nil
}

func (r *repository) DeleteAllUserAccessToken(ctx context.Context, userId uuid.UUID) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.access_token.DeleteAllUserAccessToken",
		trace.WithAttributes(
			attribute.String("action", "DELETE_ALL_USER_ACCESS_TOKENS"),
			attribute.String("user_id", userId.String()),
		))
	defer span.End()

	otelzap.InfoWithContext(ctx, "deleting all user access tokens",
		zap.String("action", "DELETE_ALL_USER_ACCESS_TOKENS"),
		zap.String("user_id", userId.String()))

	err := r.GetDB(ctx).Delete(&entities.UserToken{}, "user_id = ?", userId).Error
	if err != nil {
		span.SetStatus(codes.Error, "failed to delete user all access tokens")
		span.RecordError(err)
		otelzap.ErrorWithContext(ctx, "failed to delete user all access tokens", err,
			zap.String("action", "DELETE_ALL_USER_ACCESS_TOKENS"),
			zap.String("user_id", userId.String()),
			zap.String("status", "failed"))
		return err
	}

	span.AddEvent("all access tokens deleted successfully for user")
	span.SetStatus(codes.Ok, "all access tokens deleted successfully for user")
	span.SetAttributes(attribute.String("status", "success"))
	otelzap.InfoWithContext(ctx, "all access tokens deleted successfully for user",
		zap.String("action", "DELETE_ALL_USER_ACCESS_TOKENS"),
		zap.String("user_id", userId.String()),
		zap.String("status", "success"))
	return nil
}
