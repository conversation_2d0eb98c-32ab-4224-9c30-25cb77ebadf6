package repository

import (
	"context"

	"github.com/google/uuid"
	"go.opentelemetry.io/otel/codes"

	"api-server/internal/entities"
	"api-server/internal/types"
	"api-server/pkg/oteltrace"
)

// HardwareRepository defines the interface for hardware operations.
// It provides methods for managing hardware resources, including listing,
// finding, and counting hardware items.
type HardwareRepository interface {
	// ListHardware retrieves a paginated list of hardware resources.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - pagination: Pagination parameters for the list
	//   - order: Ordering parameters for the list
	//
	// Returns:
	//   - []entities.Hardware: List of hardware resources
	//   - error: nil if successful, otherwise the error that occurred
	ListHardware(ctx context.Context, pagination types.Pagination, order types.OrderBy) ([]entities.Hardware, error)

	// CountHardware counts the total number of hardware resources.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//
	// Returns:
	//   - int64: Total number of hardware resources
	//   - error: nil if successful, otherwise the error that occurred
	CountHardware(ctx context.Context) (int64, error)

	// FindHardwareByID retrieves a specific hardware resource by ID.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - id: UUID of the hardware resource to find
	//
	// Returns:
	//   - *entities.Hardware: The found hardware resource if successful
	//   - error: nil if successful, otherwise the error that occurred
	FindHardwareByID(ctx context.Context, id uuid.UUID) (*entities.Hardware, error)

	// CreateHardware creates a new hardware resource in the system.
	// It initializes a new hardware entity with the given name and stores it in the database.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - hardware: Pointer to the hardware entity values
	//
	// Returns:
	//   - *entities.Hardware: Pointer to the created hardware entity
	//   - error: Any error that occurred during creation
	CreateHardware(ctx context.Context, hardware *entities.Hardware) (*entities.Hardware, error)

	// UpdateHardware updates an existing hardware resource in the system.
	// It modifies the hardware entity with new values and persists the changes to the database.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - hardware: Pointer to the hardware entity containing updated values
	//
	// Returns:
	//   - error: Any error that occurred during update
	UpdateHardware(ctx context.Context, hardware *entities.Hardware) error
}

var _ HardwareRepository = (*repository)(nil)

// ListHardware retrieves a paginated list of hardware resources.
func (r *repository) ListHardware(ctx context.Context, pagination types.Pagination, order types.OrderBy) ([]entities.Hardware, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.hardware.ListHardware")
	defer span.End()

	var result []entities.Hardware
	err := r.
		GetDB(ctx).
		Scopes(paginate(pagination.PageNo, pagination.PageSize), orderBy(order)).
		Find(&result).Error
	if err != nil {
		span.SetStatus(codes.Error, err.Error())
		span.RecordError(err)
		return nil, err
	}

	return result, nil
}

// FindHardwareByID retrieves a specific hardware resource by ID.
func (r *repository) FindHardwareByID(ctx context.Context, id uuid.UUID) (*entities.Hardware, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.hardware.FindHardwareByID")
	defer span.End()

	var result entities.Hardware
	predicate := entities.Hardware{
		BaseModel: entities.BaseModel{
			ID: id,
		},
	}
	err := r.
		GetDB(ctx).
		Where(&predicate).
		First(&result).Error
	if err != nil {
		span.SetStatus(codes.Error, err.Error())
		span.RecordError(err)
		return nil, err
	}

	return &result, nil
}

// CountHardware counts the total number of hardware resources.
func (r *repository) CountHardware(ctx context.Context) (int64, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.hardware.CountHardware")
	defer span.End()

	var result int64
	err := r.GetDB(ctx).Model(&entities.Hardware{}).Count(&result).Error
	if err != nil {
		span.SetStatus(codes.Error, err.Error())
		span.RecordError(err)
		return 0, err
	}

	return result, nil
}

// CreateHardware creates a new hardware resource in the system.
// It initializes a new hardware entity with the given name and stores it in the database.
//
// Parameters:
//   - ctx: Context for the operation
//   - hardware: Pointer to the hardware entity containing values
//
// Returns:
//   - *entities.Hardware: Pointer to the created hardware entity
//   - error: Any error that occurred during creation
func (r *repository) CreateHardware(ctx context.Context, hardware *entities.Hardware) (*entities.Hardware, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.hardware.CreateHardware")
	defer span.End()

	err := r.GetDB(ctx).Create(hardware).Error
	if err != nil {
		span.SetStatus(codes.Error, err.Error())
		span.RecordError(err)
		return nil, err
	}

	return hardware, nil
}

// UpdateHardware updates an existing hardware resource in the system.
// It modifies the hardware entity with new values and persists the changes to the database.
//
// Parameters:
//   - ctx: Context for the operation
//   - hardware: Pointer to the hardware entity containing updated values
//
// Returns:
//   - error: Any error that occurred during update
func (r *repository) UpdateHardware(ctx context.Context, hardware *entities.Hardware) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.hardware.UpdateHardware")
	defer span.End()

	err := r.GetDB(ctx).Save(hardware).Error
	if err != nil {
		span.SetStatus(codes.Error, err.Error())
		span.RecordError(err)
		return err
	}

	return nil
}
