package repository

import (
	"context"
	"testing"
	"time"

	sqlmock "github.com/DATA-DOG/go-sqlmock"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"

	"api-server/internal/enums"
	"api-server/internal/types"
)

func TestCreateRepoAccessToken(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		repoID := uuid.New()
		expiresAt := time.Now().Add(24 * time.Hour)
		scopes := []enums.RepoAccessTokenScope{enums.RepoAccessToken_API, enums.RepoAccessToken_READ_REPOSITORY}

		input := CreateRepoAccessTokenInput{
			Name:        "test-token",
			AccessToken: "test-access-token",
			Scopes:      scopes,
			RepoID:      repoID,
			ExpiresAt:   expiresAt,
			RefGitID:    123,
		}

		// Setup mock expectations
		mock.ExpectBegin()
		// Generate a UUID for the row result
		expectedID := uuid.New()
		rows := sqlmock.NewRows([]string{"id"}).AddRow(expectedID)
		mock.ExpectQuery(`INSERT INTO "repo_access_tokens" \("created_at","updated_at","repo_id","name","access_token","scopes","revoked","ref_git_id","expires_at","id"\) VALUES \(\$1,\$2,\$3,\$4,\$5,\$6,\$7,\$8,\$9,\$10\) RETURNING "id"`).
			WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), input.RepoID, input.Name, input.AccessToken, "api,read_repository", false, input.RefGitID, input.ExpiresAt, sqlmock.AnyArg()).
			WillReturnRows(rows)
		mock.ExpectCommit()

		// Call the method
		result, err := repo.CreateRepoAccessToken(context.Background(), input)

		// Assertions
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, expectedID, result.ID)
		assert.Equal(t, input.Name, result.Name)
		assert.Equal(t, input.AccessToken, result.AccessToken)
		assert.Equal(t, "api,read_repository", result.Scopes)
		assert.Equal(t, false, result.Revoked)
		assert.Equal(t, input.RefGitID, result.RefGitID)
		assert.Equal(t, input.ExpiresAt, result.ExpiresAt)
		assert.Equal(t, input.RepoID, result.RepoID)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		repoID := uuid.New()
		expiresAt := time.Now().Add(24 * time.Hour)
		scopes := []enums.RepoAccessTokenScope{enums.RepoAccessToken_API, enums.RepoAccessToken_READ_REPOSITORY}

		input := CreateRepoAccessTokenInput{
			Name:        "test-token",
			AccessToken: "test-access-token",
			Scopes:      scopes,
			RepoID:      repoID,
			ExpiresAt:   expiresAt,
			RefGitID:    123,
		}

		// Setup mock expectations
		mock.ExpectBegin()
		mock.ExpectQuery(`INSERT INTO "repo_access_tokens" \("created_at","updated_at","repo_id","name","access_token","scopes","revoked","ref_git_id","expires_at","id"\) VALUES \(\$1,\$2,\$3,\$4,\$5,\$6,\$7,\$8,\$9,\$10\) RETURNING "id"`).
			WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), input.RepoID, input.Name, input.AccessToken, "api,read_repository", false, input.RefGitID, input.ExpiresAt, sqlmock.AnyArg()).
			WillReturnError(gorm.ErrInvalidDB)
		mock.ExpectRollback()

		// Call the method
		result, err := repo.CreateRepoAccessToken(context.Background(), input)

		// Assertions
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Equal(t, gorm.ErrInvalidDB, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestListRepoAccessToken(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		tokenID := uuid.New()
		repoID := uuid.New()
		createdAt := time.Now()
		updatedAt := time.Now()
		expiresAt := time.Now().Add(24 * time.Hour)

		// Setup mock expectations
		rows := sqlmock.NewRows([]string{"id", "name", "access_token", "scopes", "revoked", "ref_git_id", "expires_at", "repo_id", "created_at", "updated_at"}).
			AddRow(tokenID, "test-token", "test-access-token", "api,read_repository", false, 123, expiresAt, repoID, createdAt, updatedAt)

		mock.ExpectQuery(`SELECT \* FROM "repo_access_tokens" WHERE "repo_access_tokens"."repo_id" = \$1 ORDER BY id asc LIMIT \$2`).
			WithArgs(repoID, 10).
			WillReturnRows(rows)

		// Call the method
		pagination := types.Pagination{
			PageNo:   1,
			PageSize: 10,
		}
		order := types.OrderBy{"id": "asc"}
		query := ListRepoAccessTokenQuery{
			RepoID: repoID,
		}
		result, err := repo.ListRepoAccessToken(context.Background(), pagination, order, query)

		// Assertions
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Len(t, result, 1)
		assert.Equal(t, tokenID, result[0].ID)
		assert.Equal(t, "test-token", result[0].Name)
		assert.Equal(t, "test-access-token", result[0].AccessToken)
		assert.Equal(t, "api,read_repository", result[0].Scopes)
		assert.Equal(t, false, result[0].Revoked)
		assert.Equal(t, int64(123), result[0].RefGitID)
		assert.Equal(t, expiresAt, result[0].ExpiresAt)
		assert.Equal(t, repoID, result[0].RepoID)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		repoID := uuid.New()

		// Setup mock expectations
		mock.ExpectQuery(`SELECT \* FROM "repo_access_tokens" WHERE "repo_access_tokens"."repo_id" = \$1 ORDER BY id asc LIMIT \$2`).
			WithArgs(repoID, 10).
			WillReturnError(gorm.ErrInvalidDB)

		// Call the method
		pagination := types.Pagination{
			PageNo:   1,
			PageSize: 10,
		}
		order := types.OrderBy{"id": "asc"}
		query := ListRepoAccessTokenQuery{
			RepoID: repoID,
		}
		result, err := repo.ListRepoAccessToken(context.Background(), pagination, order, query)

		// Assertions
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Equal(t, gorm.ErrInvalidDB, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestDeleteRepoAccessToken(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		tokenID := uuid.New()
		repoID := uuid.New()

		// Setup mock expectations
		mock.ExpectBegin()
		mock.ExpectExec(`DELETE FROM "repo_access_tokens" WHERE "repo_access_tokens"."id" = \$1`).
			WithArgs(tokenID).
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		// Call the method
		input := DeleteRepoAccessTokenInput{
			AccessTokenID: tokenID,
			RepoID:        repoID,
		}
		err := repo.DeleteRepoAccessToken(context.Background(), input)

		// Assertions
		assert.NoError(t, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		tokenID := uuid.New()
		repoID := uuid.New()

		// Setup mock expectations
		mock.ExpectBegin()
		mock.ExpectExec(`DELETE FROM "repo_access_tokens" WHERE "repo_access_tokens"."id" = \$1`).
			WithArgs(tokenID).
			WillReturnError(gorm.ErrInvalidDB)
		mock.ExpectRollback()

		// Call the method
		input := DeleteRepoAccessTokenInput{
			AccessTokenID: tokenID,
			RepoID:        repoID,
		}
		err := repo.DeleteRepoAccessToken(context.Background(), input)

		// Assertions
		assert.Error(t, err)
		assert.Equal(t, gorm.ErrInvalidDB, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestCountRepoAccessToken(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		repoID := uuid.New()
		expectedCount := int64(5)

		// Setup mock expectations
		rows := sqlmock.NewRows([]string{"count"}).AddRow(expectedCount)
		mock.ExpectQuery(`SELECT count\(\*\) FROM "repo_access_tokens" WHERE "repo_access_tokens"."repo_id" = \$1`).
			WithArgs(repoID).
			WillReturnRows(rows)

		// Call the method
		query := ListRepoAccessTokenQuery{
			RepoID: repoID,
		}
		result, err := repo.CountRepoAccessToken(context.Background(), query)

		// Assertions
		assert.NoError(t, err)
		assert.Equal(t, expectedCount, result)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		repoID := uuid.New()

		// Setup mock expectations
		mock.ExpectQuery(`SELECT count\(\*\) FROM "repo_access_tokens" WHERE "repo_access_tokens"."repo_id" = \$1`).
			WithArgs(repoID).
			WillReturnError(gorm.ErrInvalidDB)

		// Call the method
		query := ListRepoAccessTokenQuery{
			RepoID: repoID,
		}
		result, err := repo.CountRepoAccessToken(context.Background(), query)

		// Assertions
		assert.Error(t, err)
		assert.Equal(t, int64(0), result)
		assert.Equal(t, gorm.ErrInvalidDB, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestFindRepoAccessToken(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		tokenID := uuid.New()
		repoID := uuid.New()
		createdAt := time.Now()
		updatedAt := time.Now()
		expiresAt := time.Now().Add(24 * time.Hour)

		// Setup mock expectations
		rows := sqlmock.NewRows([]string{"id", "name", "access_token", "scopes", "revoked", "ref_git_id", "expires_at", "repo_id", "created_at", "updated_at"}).
			AddRow(tokenID, "test-token", "test-access-token", "api,read_repository", false, 123, expiresAt, repoID, createdAt, updatedAt)

		mock.ExpectQuery(`SELECT \* FROM "repo_access_tokens" WHERE "repo_access_tokens"."id" = \$1 ORDER BY "repo_access_tokens"."id" LIMIT \$2`).
			WithArgs(tokenID, 1).
			WillReturnRows(rows)

		// Call the method
		query := FindRepoAccessTokenQuery{
			ID: tokenID,
		}
		result, err := repo.FindRepoAccessToken(context.Background(), query)

		// Assertions
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, tokenID, result.ID)
		assert.Equal(t, "test-token", result.Name)
		assert.Equal(t, "test-access-token", result.AccessToken)
		assert.Equal(t, "api,read_repository", result.Scopes)
		assert.Equal(t, false, result.Revoked)
		assert.Equal(t, int64(123), result.RefGitID)
		assert.Equal(t, expiresAt, result.ExpiresAt)
		assert.Equal(t, repoID, result.RepoID)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("not found", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		tokenID := uuid.New()

		// Setup mock expectations
		mock.ExpectQuery(`SELECT \* FROM "repo_access_tokens" WHERE "repo_access_tokens"."id" = \$1 ORDER BY "repo_access_tokens"."id" LIMIT \$2`).
			WithArgs(tokenID, 1).
			WillReturnError(gorm.ErrRecordNotFound)

		// Call the method
		query := FindRepoAccessTokenQuery{
			ID: tokenID,
		}
		result, err := repo.FindRepoAccessToken(context.Background(), query)

		// Assertions
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Equal(t, gorm.ErrRecordNotFound, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		tokenID := uuid.New()

		// Setup mock expectations
		mock.ExpectQuery(`SELECT \* FROM "repo_access_tokens" WHERE "repo_access_tokens"."id" = \$1 ORDER BY "repo_access_tokens"."id" LIMIT \$2`).
			WithArgs(tokenID, 1).
			WillReturnError(gorm.ErrInvalidDB)

		// Call the method
		query := FindRepoAccessTokenQuery{
			ID: tokenID,
		}
		result, err := repo.FindRepoAccessToken(context.Background(), query)

		// Assertions
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Equal(t, gorm.ErrInvalidDB, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}
