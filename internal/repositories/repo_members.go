package repository

import (
	"context"

	"github.com/google/uuid"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"

	"api-server/internal/entities"
	"api-server/internal/enums"
	"api-server/pkg/oteltrace"
)

// RepoMemberRepository defines the interface for repository member operations.
// It provides methods for managing repository members, including creating,
// finding, and removing members.
type RepoMemberRepository interface {
	// DeleteRepositoryMemberByRepositoryID removes all members from a repository.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - repositoryID: ID of the repository
	//
	// Returns:
	//   - error: nil if successful, otherwise the error that occurred
	DeleteRepositoryMemberByRepositoryID(ctx context.Context, repositoryID uuid.UUID) error

	// CreateRepositoryMember adds a new member to a repository.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - input: Data for creating the repository member
	//
	// Returns:
	//   - error: nil if successful, otherwise the error that occurred
	CreateRepositoryMember(ctx context.Context, input *entities.RepoMember) error

	// DeleteRepositoryMemberByUserID removes a user from all repositories.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - userID: ID of the user to remove
	//
	// Returns:
	//   - error: nil if successful, otherwise the error that occurred
	DeleteRepositoryMemberByUserID(ctx context.Context, userID uuid.UUID) error

	// FindRepositoryMember retrieves a specific repository member based on filter criteria.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - filter: Criteria for finding the repository member
	//
	// Returns:
	//   - *entities.RepoMember: The found repository member if successful
	//   - error: nil if successful, otherwise the error that occurred
	FindRepositoryMember(ctx context.Context, filter FilterRepoMember) (*entities.RepoMember, error)
}

var _ RepoMemberRepository = (*repository)(nil)

// DeleteRepositoryMemberByRepositoryID removes all members from a repository.
func (r repository) DeleteRepositoryMemberByRepositoryID(ctx context.Context, repositoryID uuid.UUID) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.repo_members.DeleteRepositoryMemberByRepositoryID")
	defer span.End()

	err := r.GetDB(ctx).Where("repo_id = ?", repositoryID).Delete(&entities.RepoMember{}).Error
	if err != nil {
		span.SetStatus(codes.Error, "failed to delete repository member by repository ID")
		span.RecordError(err)
		return err
	}

	span.AddEvent("repository member deleted successfully by repository ID")
	span.SetStatus(codes.Ok, "repository member deleted successfully by repository ID")
	return nil
}

// CreateRepositoryMember adds a new member to a repository.
func (r repository) CreateRepositoryMember(ctx context.Context, input *entities.RepoMember) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.repo_members.CreateRepositoryMember")
	defer span.End()

	err := r.GetDB(ctx).WithContext(ctx).Create(input).Error
	if err != nil {
		span.SetStatus(codes.Error, "failed to create repository member")
		span.RecordError(err)
		return err
	}

	span.AddEvent("repository member created successfully")
	span.SetStatus(codes.Ok, "repository member created successfully")
	return nil
}

// DeleteRepositoryMemberByUserID removes a user from all repositories.
func (r repository) DeleteRepositoryMemberByUserID(ctx context.Context, userID uuid.UUID) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.repo_members.DeleteRepositoryMemberByUserID")
	defer span.End()

	err := r.GetDB(ctx).Where("user_id = ?", userID).Delete(&entities.RepoMember{}).Error
	if err != nil {
		span.SetStatus(codes.Error, "failed to delete repository member by user ID")
		span.RecordError(err)
		return err
	}

	span.AddEvent("repository member deleted successfully by user ID")
	span.SetStatus(codes.Ok, "repository member deleted successfully by user ID")
	return nil
}

// FilterRepoMember contains the criteria for finding a repository member.
type FilterRepoMember struct {
	UserID *uuid.UUID
	RepoID *uuid.UUID
	Role   enums.RepoRole
}

// FindRepositoryMember retrieves a specific repository member based on filter criteria.
func (r repository) FindRepositoryMember(ctx context.Context, filter FilterRepoMember) (*entities.RepoMember, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.repo_members.FindRepositoryMember")
	defer span.End()

	var result entities.RepoMember

	query := r.GetDB(ctx)

	if filter.UserID != nil {
		span.AddEvent("filter with", trace.WithAttributes(attribute.String("user_id", filter.UserID.String())))
		query = query.Where("user_id = ?", filter.UserID)
	}

	if filter.RepoID != nil {
		span.AddEvent("filter with", trace.WithAttributes(attribute.String("repo_id", filter.RepoID.String())))
		query = query.Where("repo_id = ?", filter.RepoID)
	}

	if filter.Role != "" {
		span.AddEvent("filter with", trace.WithAttributes(attribute.String("role", filter.Role.String())))
		query = query.Where("role = ?", filter.Role)
	}

	err := query.First(&result).Error
	if err != nil {
		span.SetStatus(codes.Error, "failed to find repository member")
		span.RecordError(err)
		return nil, err
	}

	span.AddEvent("repository member found successfully")
	span.SetStatus(codes.Ok, "repository member found successfully")
	return &result, nil
}
