package repository

import (
	"context"
	"testing"
	"time"

	sqlmock "github.com/DATA-DOG/go-sqlmock"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"

	"api-server/internal/entities"
	"api-server/internal/enums"
	"api-server/internal/types"
)

func TestCreateDeployment(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		userID := uuid.New()
		repoID := uuid.New()
		// hardwareID := uuid.New()
		workflowName := "test-workflow"
		status := enums.ArgoWorkflowStatus_Pending

		// Setup mock expectations
		mock.ExpectBegin()
		mock.ExpectQuery(`INSERT INTO "deployments"`).
			WithArgs(
				sqlmock.AnyArg(),  // created_at
				sqlmock.AnyArg(),  // updated_at
				"test-deployment", // name
				status,            // status
				workflowName,      // workflow_name
				float32(0),        // duration
				repoID,            // repo_id
				"main",            // revision
				"abc123",          // commit
				// hardwareID,        // hardware_id
				&userID,          // user_id
				sqlmock.AnyArg(), // id
			).
			WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(uuid.New()))
		mock.ExpectCommit()

		// Call the method
		result, err := repo.CreateDeployment(context.Background(), CreateDeploymentInput{
			Name:         "test-deployment",
			UserID:       userID,
			WorkflowName: workflowName,
			RepoID:       repoID,
			Revision:     "main",
			Status:       status,
			Commit:       "abc123",
			// HardwareID:   hardwareID,
		})

		// Assertions
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, workflowName, result.WorkflowName)
		assert.Equal(t, status, result.Status)
		assert.Equal(t, repoID, result.RepoID)
		// assert.Equal(t, hardwareID, result.HardwareID)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup mock expectations
		mock.ExpectBegin()
		mock.ExpectQuery(`INSERT INTO "deployments"`).
			WithArgs(
				sqlmock.AnyArg(), // created_at
				sqlmock.AnyArg(), // updated_at
				sqlmock.AnyArg(), // name
				sqlmock.AnyArg(), // status
				sqlmock.AnyArg(), // workflow_name
				sqlmock.AnyArg(), // duration
				sqlmock.AnyArg(), // repo_id
				sqlmock.AnyArg(), // revision
				sqlmock.AnyArg(), // commit
				// sqlmock.AnyArg(), // hardware_id
				sqlmock.AnyArg(), // user_id
				sqlmock.AnyArg(), // id
			).
			WillReturnError(gorm.ErrInvalidDB)
		mock.ExpectRollback()

		// Call the method
		result, err := repo.CreateDeployment(context.Background(), CreateDeploymentInput{
			Name:         "test-deployment",
			UserID:       uuid.New(),
			WorkflowName: "test-workflow",
			RepoID:       uuid.New(),
			Revision:     "main",
			Status:       enums.ArgoWorkflowStatus_Pending,
			Commit:       "abc123",
			// HardwareID:   uuid.New(),
		})

		// Assertions
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Equal(t, gorm.ErrInvalidDB, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestUpsertDeployment(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		userID := uuid.New()
		repoID := uuid.New()
		// hardwareID := uuid.New()
		workflowName := "test-workflow"
		status := enums.ArgoWorkflowStatus_Pending

		// Setup mock expectations
		mock.ExpectBegin()
		mock.ExpectQuery(`INSERT INTO "deployments"`).
			WithArgs(
				sqlmock.AnyArg(),  // created_at
				sqlmock.AnyArg(),  // updated_at
				"test-deployment", // name
				status,            // status
				workflowName,      // workflow_name
				float32(0),        // duration
				repoID,            // repo_id
				"main",            // revision
				"abc123",          // commit
				// hardwareID,        // hardware_id
				&userID,          // user_id
				sqlmock.AnyArg(), // id
				sqlmock.AnyArg(), // updated_at for ON CONFLICT
			).
			WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(uuid.New()))
		mock.ExpectCommit()

		// Call the method
		result, err := repo.UpsertDeployment(context.Background(), CreateDeploymentInput{
			Name:         "test-deployment",
			UserID:       userID,
			WorkflowName: workflowName,
			RepoID:       repoID,
			Revision:     "main",
			Status:       status,
			Commit:       "abc123",
			// HardwareID:   hardwareID,
		})

		// Assertions
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, workflowName, result.WorkflowName)
		assert.Equal(t, status, result.Status)
		assert.Equal(t, repoID, result.RepoID)
		// assert.Equal(t, hardwareID, result.HardwareID)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup mock expectations
		mock.ExpectBegin()
		mock.ExpectQuery(`INSERT INTO "deployments"`).
			WithArgs(
				sqlmock.AnyArg(), // created_at
				sqlmock.AnyArg(), // updated_at
				sqlmock.AnyArg(), // name
				sqlmock.AnyArg(), // status
				sqlmock.AnyArg(), // workflow_name
				sqlmock.AnyArg(), // duration
				sqlmock.AnyArg(), // repo_id
				sqlmock.AnyArg(), // revision
				sqlmock.AnyArg(), // commit
				// sqlmock.AnyArg(), // hardware_id
				sqlmock.AnyArg(), // user_id
				sqlmock.AnyArg(), // id
				sqlmock.AnyArg(), // updated_at for ON CONFLICT
			).
			WillReturnError(gorm.ErrInvalidDB)
		mock.ExpectRollback()

		// Call the method
		result, err := repo.UpsertDeployment(context.Background(), CreateDeploymentInput{
			Name:         "test-deployment",
			UserID:       uuid.New(),
			WorkflowName: "test-workflow",
			RepoID:       uuid.New(),
			Revision:     "main",
			Status:       enums.ArgoWorkflowStatus_Pending,
			Commit:       "abc123",
			// HardwareID:   uuid.New(),
		})

		// Assertions
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Equal(t, gorm.ErrInvalidDB, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestUpdateDeployment(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		workflowName := "test-workflow"
		status := enums.ArgoWorkflowStatus_Running
		duration := float32(60.5)

		// Setup mock expectations
		mock.ExpectBegin()
		mock.ExpectExec(`UPDATE "deployments"`).
			WithArgs(
				sqlmock.AnyArg(), // updated_at
				status,           // status
				duration,         // duration
				workflowName,     // workflow_name
			).
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		// Call the method
		result, err := repo.UpdateDeployment(context.Background(), UpdateDeploymentInput{
			WorkflowName: workflowName,
			Status:       status,
			Duration:     duration,
		})

		// Assertions
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, status, result.Status)
		assert.Equal(t, duration, result.Duration)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup mock expectations
		mock.ExpectBegin()
		mock.ExpectExec(`UPDATE "deployments"`).
			WithArgs(
				sqlmock.AnyArg(), // updated_at
				sqlmock.AnyArg(), // status
				sqlmock.AnyArg(), // duration
				sqlmock.AnyArg(), // workflow_name
			).
			WillReturnError(gorm.ErrInvalidDB)
		mock.ExpectRollback()

		// Call the method
		result, err := repo.UpdateDeployment(context.Background(), UpdateDeploymentInput{
			WorkflowName: "test-workflow",
			Status:       enums.ArgoWorkflowStatus_Running,
			Duration:     60.5,
		})

		// Assertions
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Equal(t, gorm.ErrInvalidDB, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestFindDeployment(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		repoID := uuid.New()
		deployment := entities.Deployment{
			BaseModel: entities.BaseModel{
				ID:        uuid.New(),
				CreatedAt: time.Now(),
				UpdatedAt: time.Now(),
			},
			Name:         "test-deployment",
			Status:       enums.ArgoWorkflowStatus_Running,
			WorkflowName: "test-workflow",
			RepoID:       repoID,
		}

		// Setup mock expectations
		rows := sqlmock.NewRows([]string{"id", "created_at", "updated_at", "name", "status", "workflow_name", "repo_id"}).
			AddRow(deployment.ID, deployment.CreatedAt, deployment.UpdatedAt, deployment.Name, deployment.Status, deployment.WorkflowName, deployment.RepoID)

		mock.ExpectQuery(`SELECT \* FROM "deployments" WHERE "deployments"."repo_id" = \$1 ORDER BY "deployments"."id" LIMIT \$2`).
			WithArgs(repoID, 1).
			WillReturnRows(rows)

		// Call the method
		result, err := repo.FindDeployment(context.Background(), repoID)

		// Assertions
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, deployment.ID, result.ID)
		assert.Equal(t, deployment.Name, result.Name)
		assert.Equal(t, deployment.Status, result.Status)
		assert.Equal(t, deployment.WorkflowName, result.WorkflowName)
		assert.Equal(t, deployment.RepoID, result.RepoID)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("not found", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		repoID := uuid.New()

		// Setup mock expectations
		mock.ExpectQuery(`SELECT \* FROM "deployments" WHERE "deployments"."repo_id" = \$1 ORDER BY "deployments"."id" LIMIT \$2`).
			WithArgs(repoID, 1).
			WillReturnError(gorm.ErrRecordNotFound)

		// Call the method
		result, err := repo.FindDeployment(context.Background(), repoID)

		// Assertions
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Equal(t, gorm.ErrRecordNotFound, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestListDeployment(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		userID1 := uuid.New()
		userID2 := uuid.New()
		deployment1 := entities.Deployment{
			BaseModel: entities.BaseModel{
				ID:        uuid.New(),
				CreatedAt: time.Now(),
				UpdatedAt: time.Now(),
			},
			Name:         "test-deployment-1",
			Status:       enums.ArgoWorkflowStatus_Running,
			WorkflowName: "test-workflow-1",
			RepoID:       uuid.New(),
			UserID:       &userID1,
		}
		deployment2 := entities.Deployment{
			BaseModel: entities.BaseModel{
				ID:        uuid.New(),
				CreatedAt: time.Now(),
				UpdatedAt: time.Now(),
			},
			Name:         "test-deployment-2",
			Status:       enums.ArgoWorkflowStatus_Running,
			WorkflowName: "test-workflow-2",
			RepoID:       uuid.New(),
			UserID:       &userID2,
		}

		// Setup mock expectations for deployments query
		rows := sqlmock.NewRows([]string{"id", "created_at", "updated_at", "name", "status", "workflow_name", "repo_id", "user_id"}).
			AddRow(deployment1.ID, deployment1.CreatedAt, deployment1.UpdatedAt, deployment1.Name, deployment1.Status, deployment1.WorkflowName, deployment1.RepoID, deployment1.UserID).
			AddRow(deployment2.ID, deployment2.CreatedAt, deployment2.UpdatedAt, deployment2.Name, deployment2.Status, deployment2.WorkflowName, deployment2.RepoID, deployment2.UserID)

		mock.ExpectQuery(`SELECT \* FROM "deployments" ORDER BY id asc LIMIT \$1`).
			WithArgs(10).
			WillReturnRows(rows)

		// Setup mock expectations for repositories query
		repoRows := sqlmock.NewRows([]string{"id", "name", "type", "namespace", "path_name"}).
			AddRow(deployment1.RepoID, "repo1", enums.RepoType_Spaces, "namespace1", "path1").
			AddRow(deployment2.RepoID, "repo2", enums.RepoType_Spaces, "namespace2", "path2")

		mock.ExpectQuery(`SELECT \* FROM "repositories" WHERE "repositories"."id" IN \(\$1,\$2\)`).
			WithArgs(deployment1.RepoID, deployment2.RepoID).
			WillReturnRows(repoRows)

		// Setup mock expectations for hardware query
		hardwareRows := sqlmock.NewRows([]string{"id", "name", "num_cpu", "mem", "gpu_mem", "gpu_model", "created_at", "updated_at", "repo_id"}).
			AddRow(uuid.New(), "hardware1", 1, 1, nil, nil, time.Now(), time.Now(), deployment1.RepoID).
			AddRow(uuid.New(), "hardware2", 2, 2, nil, nil, time.Now(), time.Now(), deployment2.RepoID)

		mock.ExpectQuery(`SELECT \* FROM "hardwares" WHERE "hardwares"."repo_id" IN \(\$1,\$2\)`).
			WithArgs(deployment1.RepoID, deployment2.RepoID).
			WillReturnRows(hardwareRows)

		// Setup mock expectations for users query
		userRows := sqlmock.NewRows([]string{"id", "name", "username"}).
			AddRow(userID1, "user1", "username1").
			AddRow(userID2, "user2", "username2")

		mock.ExpectQuery(`SELECT \* FROM "public"."users" WHERE "users"."id" IN \(\$1,\$2\)`).
			WithArgs(userID1, userID2).
			WillReturnRows(userRows)

		// Call the method
		pagination := types.Pagination{
			PageNo:   1,
			PageSize: 10,
		}
		order := types.OrderBy{
			"id": "asc",
		}
		result, err := repo.ListDeployment(context.Background(), pagination, order, ListDeploymentInput{})

		// Assertions
		assert.NoError(t, err)
		assert.Len(t, result, 2)
		assert.Equal(t, deployment1.ID, result[0].ID)
		assert.Equal(t, deployment1.Name, result[0].Name)
		assert.Equal(t, deployment1.Status, result[0].Status)
		assert.Equal(t, deployment2.ID, result[1].ID)
		assert.Equal(t, deployment2.Name, result[1].Name)
		assert.Equal(t, deployment2.Status, result[1].Status)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup mock expectations
		mock.ExpectQuery(`SELECT \* FROM "deployments" ORDER BY id asc LIMIT \$1`).
			WithArgs(10).
			WillReturnError(gorm.ErrInvalidDB)

		// Call the method
		pagination := types.Pagination{
			PageNo:   1,
			PageSize: 10,
		}
		order := types.OrderBy{
			"id": "asc",
		}
		result, err := repo.ListDeployment(context.Background(), pagination, order, ListDeploymentInput{})

		// Assertions
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Equal(t, gorm.ErrInvalidDB, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestCountDeployment(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup mock expectations
		rows := sqlmock.NewRows([]string{"count"}).
			AddRow(5)

		mock.ExpectQuery(`SELECT count\(\*\) FROM "deployments"`).
			WillReturnRows(rows)

		// Call the method
		count, err := repo.CountDeployment(context.Background(), ListDeploymentInput{})

		// Assertions
		assert.NoError(t, err)
		assert.Equal(t, int64(5), count)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup mock expectations
		mock.ExpectQuery(`SELECT count\(\*\) FROM "deployments"`).
			WillReturnError(gorm.ErrInvalidDB)

		// Call the method
		count, err := repo.CountDeployment(context.Background(), ListDeploymentInput{})

		// Assertions
		assert.Error(t, err)
		assert.Equal(t, int64(0), count)
		assert.Equal(t, gorm.ErrInvalidDB, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestDeleteDeploymentsByRepoID(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		repoID := uuid.New()

		// Setup mock expectations
		mock.ExpectBegin()
		mock.ExpectExec(`DELETE FROM "deployments" WHERE repo_id = \$1`).
			WithArgs(repoID).
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		// Call the method
		err := repo.DeleteDeploymentsByRepoID(context.Background(), repoID)

		// Assertions
		assert.NoError(t, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		repoID := uuid.New()

		// Setup mock expectations
		mock.ExpectBegin()
		mock.ExpectExec(`DELETE FROM "deployments" WHERE repo_id = \$1`).
			WithArgs(repoID).
			WillReturnError(gorm.ErrInvalidDB)
		mock.ExpectRollback()

		// Call the method
		err := repo.DeleteDeploymentsByRepoID(context.Background(), repoID)

		// Assertions
		assert.Error(t, err)
		assert.Equal(t, gorm.ErrInvalidDB, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestRemoveUserInDeployments(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		repoIDs := []uuid.UUID{uuid.New(), uuid.New()}

		// Setup mock expectations
		mock.ExpectBegin()
		mock.ExpectExec(`UPDATE "deployments" SET "user_id"=\$1,"updated_at"=\$2 WHERE repo_id IN \(\$3,\$4\)`).
			WithArgs(nil, sqlmock.AnyArg(), repoIDs[0], repoIDs[1]).
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		// Call the method
		err := repo.RemoveUserInDeployments(context.Background(), RemoveUserInDeploymentsInput{
			RepoIDs: repoIDs,
		})

		// Assertions
		assert.NoError(t, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		repoIDs := []uuid.UUID{uuid.New(), uuid.New()}

		// Setup mock expectations
		mock.ExpectBegin()
		mock.ExpectExec(`UPDATE "deployments" SET "user_id"=\$1,"updated_at"=\$2 WHERE repo_id IN \(\$3,\$4\)`).
			WithArgs(nil, sqlmock.AnyArg(), repoIDs[0], repoIDs[1]).
			WillReturnError(gorm.ErrInvalidDB)
		mock.ExpectRollback()

		// Call the method
		err := repo.RemoveUserInDeployments(context.Background(), RemoveUserInDeploymentsInput{
			RepoIDs: repoIDs,
		})

		// Assertions
		assert.Error(t, err)
		assert.Equal(t, gorm.ErrInvalidDB, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}
