package repository

import (
	"context"
	"fmt"
	"testing"
	"time"

	sqlmock "github.com/DATA-DOG/go-sqlmock"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"gorm.io/datatypes"
	"gorm.io/gorm"

	"api-server/internal/dto"
	"api-server/internal/entities"
	"api-server/internal/enums"
	"api-server/internal/types"
)

func TestListRepositories(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		repoID := uuid.New()
		userID := uuid.New()
		orgID := uuid.New()
		createdAt := time.Now()
		updatedAt := time.Now()

		// Setup mock expectations
		rows := sqlmock.NewRows([]string{"id", "name", "type", "visibility", "user_id", "org_id", "created_at", "updated_at", "user_email"}).
			AddRow(repoID, "test-repo", enums.RepoType_Spaces, enums.RepoVisibility_Private, userID, orgID, createdAt, updatedAt, "<EMAIL>")

		// Main repository query
		mock.ExpectQuery(`SELECT repositories.\*, au.email AS user_email FROM "repositories" LEFT JOIN users ON repositories.user_id = users.id LEFT JOIN organizations ON repositories.org_id = organizations.id LEFT JOIN auth.users au ON repositories.user_id = au.id WHERE repositories.type = \$1 LIMIT \$2`).
			WithArgs(enums.RepoType_Spaces, 10).
			WillReturnRows(rows)

		// Preload Deployments query (first preload)
		mock.ExpectQuery(`SELECT \* FROM "deployments" WHERE "deployments"."repo_id" = \$1`).
			WithArgs(repoID).
			WillReturnRows(sqlmock.NewRows([]string{"id", "repo_id", "created_at", "updated_at"}))

			// Preload Hardware query (fourth preload)
		mock.ExpectQuery(`SELECT \* FROM "hardwares" WHERE "hardwares"."repo_id" = \$1`).
			WithArgs(repoID).
			WillReturnRows(sqlmock.NewRows([]string{"id", "name", "num_cpu", "mem", "gpu_mem", "gpu_model", "created_at", "updated_at", "repo_id"}).
				AddRow(uuid.New(), "test-hardware", 4, 8192, nil, nil, createdAt, updatedAt, repoID))

		// Preload Organizations query (second preload)
		mock.ExpectQuery(`SELECT \* FROM "organizations" WHERE "organizations"."id" = \$1`).
			WithArgs(orgID).
			WillReturnRows(sqlmock.NewRows([]string{"id", "name", "path_name", "created_at", "updated_at"}).
				AddRow(orgID, "test-org", "test-org-path", createdAt, updatedAt))

		// Preload User query (third preload)
		mock.ExpectQuery(`SELECT \* FROM "public"."users" WHERE "users"."id" = \$1`).
			WithArgs(userID).
			WillReturnRows(sqlmock.NewRows([]string{"id", "name", "username", "created_at", "updated_at"}).
				AddRow(userID, "test-user", "test-username", createdAt, updatedAt))

		// Call the method
		input := dto.GetRepositoriesInput{
			RepositoryType: enums.RepoType_Spaces,
			Paginate: dto.PaginateRequest{
				Page:    1,
				PerPage: 10,
			},
		}
		result, err := repo.ListRepositories(context.Background(), input)

		// Assertions
		assert.NoError(t, err)
		assert.Len(t, result, 1)
		assert.Equal(t, repoID, result[0].ID)
		assert.Equal(t, "test-repo", result[0].Name)
		assert.Equal(t, enums.RepoType_Spaces, result[0].Type)
		assert.Equal(t, enums.RepoVisibility_Private, result[0].Visibility)
		assert.Equal(t, userID, *result[0].UserID)
		assert.Equal(t, orgID, *result[0].OrgID)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup mock expectations
		mock.ExpectQuery(`SELECT repositories.\*, au.email AS user_email FROM "repositories" LEFT JOIN users ON repositories.user_id = users.id LEFT JOIN organizations ON repositories.org_id = organizations.id LEFT JOIN auth.users au ON repositories.user_id = au.id WHERE repositories.type = \$1 LIMIT \$2`).
			WithArgs(enums.RepoType_Spaces, 10).
			WillReturnError(gorm.ErrInvalidDB)

		// Call the method
		input := dto.GetRepositoriesInput{
			RepositoryType: enums.RepoType_Spaces,
			Paginate: dto.PaginateRequest{
				Page:    1,
				PerPage: 10,
			},
		}
		result, err := repo.ListRepositories(context.Background(), input)

		// Assertions
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Equal(t, gorm.ErrInvalidDB, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestCountRepositories(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup mock expectations
		rows := sqlmock.NewRows([]string{"count"}).AddRow(5)
		mock.ExpectQuery(`SELECT count\(\*\) FROM "repositories"`).
			WillReturnRows(rows)

		// Call the method
		input := dto.GetRepositoriesInput{}
		count, err := repo.CountRepositories(context.Background(), input)

		// Assertions
		assert.NoError(t, err)
		assert.Equal(t, int64(5), count)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup mock expectations
		mock.ExpectQuery(`SELECT count\(\*\) FROM "repositories"`).
			WillReturnError(gorm.ErrInvalidDB)

		// Call the method
		input := dto.GetRepositoriesInput{}
		count, err := repo.CountRepositories(context.Background(), input)

		// Assertions
		assert.Error(t, err)
		assert.Equal(t, int64(0), count)
		assert.Equal(t, gorm.ErrInvalidDB, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestFindRepository(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		repoID := uuid.New()
		userID := uuid.New()
		orgID := uuid.New()
		createdAt := time.Now()
		updatedAt := time.Now()

		// Setup mock expectations
		rows := sqlmock.NewRows([]string{"id", "name", "type", "visibility", "user_id", "org_id", "created_at", "updated_at", "user_email"}).
			AddRow(repoID, "test-repo", enums.RepoType_Spaces, enums.RepoVisibility_Private, userID, orgID, createdAt, updatedAt, "<EMAIL>")

		// Main repository query
		mock.ExpectQuery(`SELECT repositories.\*, au.email AS user_email FROM "repositories" LEFT JOIN auth.users au ON repositories.user_id = au.id WHERE repositories.id = \$1 ORDER BY "repositories"."id" LIMIT \$2`).
			WithArgs(repoID, 1).
			WillReturnRows(rows)

		// Preload Deployment.Hardware query
		mock.ExpectQuery(`SELECT \* FROM "deployments" WHERE "deployments"."repo_id" = \$1`).
			WithArgs(repoID).
			WillReturnRows(sqlmock.NewRows([]string{"id", "repo_id", "created_at", "updated_at"}))

		// Query: could not match actual sql: "SELECT * FROM "deployments" WHERE "deployments"."repo_id" = $1" with expected regexp "SELECT \* FROM "hardwares" WHERE "hardwares"."repo_id" = \$1"

		mock.ExpectQuery(`SELECT \* FROM "hardwares" WHERE "hardwares"."repo_id" = \$1`).
			WithArgs(repoID).
			WillReturnRows(sqlmock.NewRows([]string{"id", "name", "num_cpu", "mem", "gpu_mem", "gpu_model", "created_at", "updated_at", "repo_id"}).
				AddRow(uuid.New(), "test-hardware", 4, 8192, nil, nil, createdAt, updatedAt, repoID))

		// Preload Organizations query
		mock.ExpectQuery(`SELECT \* FROM "organizations" WHERE "organizations"."id" = \$1`).
			WithArgs(orgID).
			WillReturnRows(sqlmock.NewRows([]string{"id", "name", "path_name", "created_at", "updated_at"}).
				AddRow(orgID, "test-org", "test-org-path", createdAt, updatedAt))

		// Preload User query
		mock.ExpectQuery(`SELECT \* FROM "public"."users" WHERE "users"."id" = \$1`).
			WithArgs(userID).
			WillReturnRows(sqlmock.NewRows([]string{"id", "name", "username", "created_at", "updated_at"}).
				AddRow(userID, "test-user", "test-username", createdAt, updatedAt))

		// Call the method
		filter := RepositoryFilter{
			Id: &repoID,
		}
		result, err := repo.FindRepository(context.Background(), filter)

		// Assertions
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, repoID, result.ID)
		assert.Equal(t, "test-repo", result.Name)
		assert.Equal(t, enums.RepoType_Spaces, result.Type)
		assert.Equal(t, enums.RepoVisibility_Private, result.Visibility)
		assert.Equal(t, userID, *result.UserID)
		assert.Equal(t, orgID, *result.OrgID)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("not found", func(t *testing.T) {
		repoId := uuid.New()
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup mock expectations with LEFT JOIN
		mock.ExpectQuery(`SELECT repositories.\*, au.email AS user_email FROM "repositories" LEFT JOIN auth.users au ON repositories.user_id = au.id WHERE repositories.id = \$1 ORDER BY "repositories"."id" LIMIT \$2`).
			WithArgs(repoId, 1).
			WillReturnError(gorm.ErrRecordNotFound)

		// Call the method
		filter := RepositoryFilter{
			Id: &repoId,
		}
		result, err := repo.FindRepository(context.Background(), filter)

		// Assertions
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Equal(t, gorm.ErrRecordNotFound, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		repoId := uuid.New()
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup mock expectations with LEFT JOIN
		mock.ExpectQuery(`SELECT repositories.\*, au.email AS user_email FROM "repositories" LEFT JOIN auth.users au ON repositories.user_id = au.id WHERE repositories.id = \$1 ORDER BY "repositories"."id" LIMIT \$2`).
			WithArgs(repoId, 1).
			WillReturnError(gorm.ErrInvalidDB)

		// Call the method
		filter := RepositoryFilter{
			Id: &repoId,
		}
		result, err := repo.FindRepository(context.Background(), filter)

		// Assertions
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Equal(t, gorm.ErrInvalidDB, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestUpdateRepository(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		repoID := uuid.New()
		repoEntity := &entities.Repository{
			BaseModel: entities.BaseModel{
				ID: repoID,
			},
			Name:       "updated-repo",
			Type:       enums.RepoType_Spaces,
			Visibility: enums.RepoVisibility_Private,
		}

		// Setup mock expectations
		mock.ExpectBegin()
		mock.ExpectExec(`UPDATE "repositories" SET "updated_at"=\$1,"id"=\$2,"name"=\$3,"visibility"=\$4,"type"=\$5 WHERE "repositories"."id" = \$6`).
			WithArgs(sqlmock.AnyArg(), repoID, repoEntity.Name, repoEntity.Visibility, repoEntity.Type, repoID).
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		// Call the method
		err := repo.UpdateRepository(context.Background(), *repoEntity)

		// Assertions
		assert.NoError(t, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		repoEntity := &entities.Repository{
			BaseModel: entities.BaseModel{
				ID: uuid.New(),
			},
		}

		// Setup mock expectations
		mock.ExpectBegin()
		mock.ExpectExec(`UPDATE "repositories" SET "updated_at"=\$1,"id"=\$2 WHERE "repositories"."id" = \$3`).
			WithArgs(sqlmock.AnyArg(), repoEntity.ID, repoEntity.ID).
			WillReturnError(gorm.ErrInvalidDB)
		mock.ExpectRollback()

		// Call the method
		err := repo.UpdateRepository(context.Background(), *repoEntity)

		// Assertions
		assert.Error(t, err)
		assert.Equal(t, gorm.ErrInvalidDB, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestListRepoMembers(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		memberID := uuid.New()
		userID := uuid.New()
		repoID := uuid.New()
		createdAt := time.Now()
		updatedAt := time.Now()

		// Setup mock expectations
		rows := sqlmock.NewRows([]string{"id", "user_id", "repo_id", "role", "created_at", "updated_at", "name", "email"}).
			AddRow(memberID, userID, repoID, enums.RepoRole_Owner, createdAt, updatedAt, "test-user", "<EMAIL>")

		mock.ExpectQuery(`SELECT repo_members.\*, users.name AS name, au.email AS email FROM "repo_members" LEFT JOIN users ON repo_members.user_id = users.id LEFT JOIN auth.users au ON users.id = au.id WHERE repo_members.repo_id = \$1 ORDER BY created_at asc LIMIT \$2`).
			WithArgs(repoID, 10).
			WillReturnRows(rows)

		// Call the method
		pagination := types.Pagination{
			PageNo:   1,
			PageSize: 10,
		}
		order := types.OrderBy{
			enums.OrderByColumn_CreatedAt: enums.OrderByDirection_Asc,
		}
		query := ListRepositoryMembersQuery{
			ID: repoID,
		}
		result, err := repo.ListRepoMembers(context.Background(), pagination, order, query)

		// Assertions
		assert.NoError(t, err)
		assert.Len(t, result, 1)
		assert.Equal(t, memberID, result[0].ID)
		assert.Equal(t, userID, result[0].UserID)
		assert.Equal(t, repoID, result[0].RepoID)
		assert.Equal(t, enums.RepoRole_Owner, result[0].Role)
		assert.Equal(t, "test-user", result[0].Name)
		assert.Equal(t, "<EMAIL>", result[0].Email)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup mock expectations
		mock.ExpectQuery(`SELECT repo_members.\*, users.name AS name, au.email AS email FROM "repo_members" LEFT JOIN users ON repo_members.user_id = users.id LEFT JOIN auth.users au ON users.id = au.id`).
			WillReturnError(gorm.ErrInvalidDB)

		// Call the method
		pagination := types.Pagination{}
		order := types.OrderBy{}
		query := ListRepositoryMembersQuery{}
		result, err := repo.ListRepoMembers(context.Background(), pagination, order, query)

		// Assertions
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Equal(t, gorm.ErrInvalidDB, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestCountRepoMembers(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup mock expectations
		rows := sqlmock.NewRows([]string{"count"}).AddRow(3)
		mock.ExpectQuery(`SELECT count\(\*\) FROM "repo_members"`).
			WillReturnRows(rows)

		// Call the method
		query := ListRepositoryMembersQuery{}
		count, err := repo.CountRepoMembers(context.Background(), query)

		// Assertions
		assert.NoError(t, err)
		assert.Equal(t, 3, count)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup mock expectations
		mock.ExpectQuery(`SELECT count\(\*\) FROM "repo_members"`).
			WillReturnError(gorm.ErrInvalidDB)

		// Call the method
		query := ListRepositoryMembersQuery{}
		count, err := repo.CountRepoMembers(context.Background(), query)

		// Assertions
		assert.Error(t, err)
		assert.Equal(t, 0, count)
		assert.Equal(t, gorm.ErrInvalidDB, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestRemoveUserInRepository(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		repoID := uuid.New()

		// Setup mock expectations
		mock.ExpectBegin()
		mock.ExpectExec(`UPDATE "repositories" SET "user_id"=\$1,"updated_at"=\$2 WHERE id IN \(\$3\)`).
			WithArgs(nil, sqlmock.AnyArg(), repoID).
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		// Call the method
		input := RemoveUserInRepositoryInput{
			IDs: []uuid.UUID{repoID},
		}
		err := repo.RemoveUserInRepository(context.Background(), input)

		// Assertions
		assert.NoError(t, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		repoID := uuid.New()

		// Setup mock expectations
		mock.ExpectBegin()
		mock.ExpectExec(`UPDATE "repositories" SET "user_id"=\$1,"updated_at"=\$2 WHERE id IN \(\$3\)`).
			WithArgs(nil, sqlmock.AnyArg(), repoID).
			WillReturnError(gorm.ErrInvalidDB)
		mock.ExpectRollback()

		// Call the method
		input := RemoveUserInRepositoryInput{
			IDs: []uuid.UUID{repoID},
		}
		err := repo.RemoveUserInRepository(context.Background(), input)

		// Assertions
		assert.Error(t, err)
		assert.Equal(t, gorm.ErrInvalidDB, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestGetEnv(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		repoID := uuid.New()
		envID := uuid.New()
		createdAt := time.Now()
		updatedAt := time.Now()
		envJSON := datatypes.JSON(`{"TEST_KEY":"test_value"}`)

		// Setup mock expectations
		rows := sqlmock.NewRows([]string{"id", "repo_id", "env", "created_at", "updated_at"}).
			AddRow(envID, repoID, envJSON, createdAt, updatedAt)

		mock.ExpectQuery(`SELECT \* FROM "repo_envs" WHERE repo_id = \$1 ORDER BY "repo_envs"."id" LIMIT \$2`).
			WithArgs(repoID, 1).
			WillReturnRows(rows)

		// Call the method
		result, err := repo.GetEnv(context.Background(), repoID)

		// Assertions
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, envID, result.ID)
		assert.Equal(t, repoID, result.RepoID)
		assert.Equal(t, envJSON, result.Env)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("not found", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		repoID := uuid.New()

		// Setup mock expectations
		mock.ExpectQuery(`SELECT \* FROM "repo_envs" WHERE repo_id = \$1 ORDER BY "repo_envs"."id" LIMIT \$2`).
			WithArgs(repoID, 1).
			WillReturnError(gorm.ErrRecordNotFound)

		// Call the method
		result, err := repo.GetEnv(context.Background(), repoID)

		// Assertions
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Equal(t, gorm.ErrRecordNotFound, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		repoID := uuid.New()

		// Setup mock expectations
		mock.ExpectQuery(`SELECT \* FROM "repo_envs" WHERE repo_id = \$1 ORDER BY "repo_envs"."id" LIMIT \$2`).
			WithArgs(repoID, 1).
			WillReturnError(gorm.ErrInvalidDB)

		// Call the method
		result, err := repo.GetEnv(context.Background(), repoID)

		// Assertions
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Equal(t, gorm.ErrInvalidDB, err)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestCreateEnv(t *testing.T) {
	t.Run("success - create new env", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		repoID := uuid.New()
		key := "TEST_KEY"
		value := "test_value"
		envJSON := datatypes.JSON(`{"TEST_KEY":"test_value"}`)

		// Setup mock expectations
		// First query to check if env exists
		mock.ExpectQuery(`SELECT \* FROM "repo_envs" WHERE repo_id = \$1 ORDER BY "repo_envs"."id" LIMIT \$2`).
			WithArgs(repoID, 1).
			WillReturnError(gorm.ErrRecordNotFound)

		// Second query to create new env
		mock.ExpectBegin()
		mock.ExpectQuery(`INSERT INTO "repo_envs" \("created_at","updated_at","repo_id","env","id"\) VALUES \(\$1,\$2,\$3,\$4,\$5\) RETURNING "id"`).
			WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), repoID, envJSON, sqlmock.AnyArg()).
			WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(uuid.New()))
		mock.ExpectCommit()

		// Call the method
		input := CreateEnvInput{
			RepoID: repoID,
			Key:    key,
			Value:  value,
		}
		err := repo.CreateEnv(context.Background(), input)

		// Assertions
		assert.NoError(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("success - update existing env", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		repoID := uuid.New()
		key := "TEST_KEY"
		value := "test_value"
		existingEnv := datatypes.JSON(`{"EXISTING_KEY":"existing_value"}`)

		// Setup mock expectations
		// First query to check if env exists
		rows := sqlmock.NewRows([]string{"id", "repo_id", "env", "created_at", "updated_at"}).
			AddRow(uuid.New(), repoID, existingEnv, time.Now(), time.Now())
		mock.ExpectQuery(`SELECT \* FROM "repo_envs" WHERE repo_id = \$1 ORDER BY "repo_envs"."id" LIMIT \$2`).
			WithArgs(repoID, 1).
			WillReturnRows(rows)

		// Second query to update env
		mock.ExpectBegin()
		mock.ExpectExec(`UPDATE "repo_envs" SET "env"=JSONB_SET\("env",\$1,\$2\) WHERE repo_id = \$3`).
			WithArgs(fmt.Sprintf("{%s}", key), fmt.Sprintf("\"%s\"", value), repoID).
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		// Call the method
		input := CreateEnvInput{
			RepoID: repoID,
			Key:    key,
			Value:  value,
		}
		err := repo.CreateEnv(context.Background(), input)

		// Assertions
		assert.NoError(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error - database error", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		repoID := uuid.New()
		key := "TEST_KEY"
		value := "test_value"

		// Setup mock expectations
		mock.ExpectQuery(`SELECT \* FROM "repo_envs" WHERE repo_id = \$1 ORDER BY "repo_envs"."id" LIMIT \$2`).
			WithArgs(repoID, 1).
			WillReturnError(gorm.ErrInvalidDB)

		// Call the method
		input := CreateEnvInput{
			RepoID: repoID,
			Key:    key,
			Value:  value,
		}
		err := repo.CreateEnv(context.Background(), input)

		// Assertions
		assert.Error(t, err)
		assert.Equal(t, gorm.ErrInvalidDB, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestDeleteEnv(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		repoID := uuid.New()
		key := "TEST_KEY"
		existingEnv := datatypes.JSON(`{"TEST_KEY":"test_value","OTHER_KEY":"other_value"}`)
		updatedEnv := datatypes.JSON(`{"OTHER_KEY":"other_value"}`)

		// Setup mock expectations
		// First query to get existing env
		rows := sqlmock.NewRows([]string{"id", "repo_id", "env", "created_at", "updated_at"}).
			AddRow(uuid.New(), repoID, existingEnv, time.Now(), time.Now())
		mock.ExpectQuery(`SELECT \* FROM "repo_envs" WHERE repo_id = \$1 ORDER BY "repo_envs"."id" LIMIT \$2`).
			WithArgs(repoID, 1).
			WillReturnRows(rows)

		// Second query to update env
		mock.ExpectBegin()
		mock.ExpectExec(`UPDATE "repo_envs" SET "created_at"=\$1,"updated_at"=\$2,"repo_id"=\$3,"env"=\$4 WHERE "id" = \$5`).
			WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), repoID, updatedEnv, sqlmock.AnyArg()).
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		// Call the method
		input := CreateEnvInput{
			RepoID: repoID,
			Key:    key,
		}
		err := repo.DeleteEnv(context.Background(), input)

		// Assertions
		assert.NoError(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error - env not found", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		repoID := uuid.New()
		key := "TEST_KEY"

		// Setup mock expectations
		mock.ExpectQuery(`SELECT \* FROM "repo_envs" WHERE repo_id = \$1 ORDER BY "repo_envs"."id" LIMIT \$2`).
			WithArgs(repoID, 1).
			WillReturnError(gorm.ErrRecordNotFound)

		// Call the method
		input := CreateEnvInput{
			RepoID: repoID,
			Key:    key,
		}
		err := repo.DeleteEnv(context.Background(), input)

		// Assertions
		assert.Error(t, err)
		assert.Equal(t, gorm.ErrRecordNotFound, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestUpdateEnv(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		repoID := uuid.New()
		oldKey := "OLD_KEY"
		newKey := "NEW_KEY"
		value := "new_value"
		existingEnv := datatypes.JSON(`{"OLD_KEY":"old_value","OTHER_KEY":"other_value"}`)
		updatedEnv := datatypes.JSON(`{"NEW_KEY":"new_value","OTHER_KEY":"other_value"}`)

		// Setup mock expectations
		// First query to get existing env
		rows := sqlmock.NewRows([]string{"id", "repo_id", "env", "created_at", "updated_at"}).
			AddRow(uuid.New(), repoID, existingEnv, time.Now(), time.Now())
		mock.ExpectQuery(`SELECT \* FROM "repo_envs" WHERE repo_id = \$1 ORDER BY "repo_envs"."id" LIMIT \$2`).
			WithArgs(repoID, 1).
			WillReturnRows(rows)

		// Second query to update env
		mock.ExpectBegin()
		mock.ExpectExec(`UPDATE "repo_envs" SET "created_at"=\$1,"updated_at"=\$2,"repo_id"=\$3,"env"=\$4 WHERE "id" = \$5`).
			WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), repoID, updatedEnv, sqlmock.AnyArg()).
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		// Call the method
		input := UpdateEnvInput{
			RepoID: repoID,
			OldKey: oldKey,
			Key:    newKey,
			Value:  value,
		}
		err := repo.UpdateEnv(context.Background(), input)

		// Assertions
		assert.NoError(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error - env not found", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		repoID := uuid.New()
		oldKey := "OLD_KEY"
		newKey := "NEW_KEY"
		value := "new_value"

		// Setup mock expectations
		mock.ExpectQuery(`SELECT \* FROM "repo_envs" WHERE repo_id = \$1 ORDER BY "repo_envs"."id" LIMIT \$2`).
			WithArgs(repoID, 1).
			WillReturnError(gorm.ErrRecordNotFound)

		// Call the method
		input := UpdateEnvInput{
			RepoID: repoID,
			OldKey: oldKey,
			Key:    newKey,
			Value:  value,
		}
		err := repo.UpdateEnv(context.Background(), input)

		// Assertions
		assert.Error(t, err)
		assert.Equal(t, gorm.ErrRecordNotFound, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestDeleteProjectsByOrganizationID(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		orgID := uuid.New()

		// Setup mock expectations
		mock.ExpectBegin()
		mock.ExpectExec(`DELETE FROM "repositories" WHERE org_id = \$1`).
			WithArgs(orgID).
			WillReturnResult(sqlmock.NewResult(0, 1))
		mock.ExpectCommit()

		// Call the method
		err := repo.DeleteProjectsByOrganizationID(context.Background(), orgID)

		// Assertions
		assert.NoError(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		orgID := uuid.New()

		// Setup mock expectations
		mock.ExpectBegin()
		mock.ExpectExec(`DELETE FROM "repositories" WHERE org_id = \$1`).
			WithArgs(orgID).
			WillReturnError(gorm.ErrInvalidDB)
		mock.ExpectRollback()

		// Call the method
		err := repo.DeleteProjectsByOrganizationID(context.Background(), orgID)

		// Assertions
		assert.Error(t, err)
		assert.Equal(t, gorm.ErrInvalidDB, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestDeleteProjectsByUserID(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		userID := uuid.New()

		// Setup mock expectations
		mock.ExpectBegin()
		mock.ExpectExec(`DELETE FROM "repositories" WHERE user_id = \$1`).
			WithArgs(userID).
			WillReturnResult(sqlmock.NewResult(0, 1))
		mock.ExpectCommit()

		// Call the method
		err := repo.DeleteProjectsByUserID(context.Background(), userID)

		// Assertions
		assert.NoError(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		db, mock := setupTestDB(t)
		repo := &repository{db: db}

		// Setup test data
		userID := uuid.New()

		// Setup mock expectations
		mock.ExpectBegin()
		mock.ExpectExec(`DELETE FROM "repositories" WHERE user_id = \$1`).
			WithArgs(userID).
			WillReturnError(gorm.ErrInvalidDB)
		mock.ExpectRollback()

		// Call the method
		err := repo.DeleteProjectsByUserID(context.Background(), userID)

		// Assertions
		assert.Error(t, err)
		assert.Equal(t, gorm.ErrInvalidDB, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}
