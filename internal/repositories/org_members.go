package repository

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"go.opentelemetry.io/otel/codes"

	"api-server/internal/dto"
	"api-server/internal/entities"
	"api-server/internal/enums"
	"api-server/internal/types"
	"api-server/pkg/oteltrace"
)

// OrgMemberRepository defines the interface for organization member operations.
// It provides methods for managing organization memberships, including creating,
// finding, listing, and removing members from organizations.
type OrgMemberRepository interface {
	// DeleteMembersByOrganizationID removes all members from an organization.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - organizationID: UUID of the organization to remove members from
	//
	// Returns:
	//   - error: nil if successful, otherwise the error that occurred
	DeleteMembersByOrganizationID(ctx context.Context, organizationID uuid.UUID) error

	// FindOrgMember retrieves a specific organization member based on filter criteria.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - filter: Criteria to find the organization member
	//
	// Returns:
	//   - *entities.OrgMember: The found organization member if successful
	//   - error: nil if successful, otherwise the error that occurred
	FindOrgMember(ctx context.Context, filter FilterOrgMember) (*entities.OrgMember, error)

	// ListMembers retrieves a paginated list of organization members.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - input: Criteria for listing members including pagination and filtering
	//
	// Returns:
	//   - []entities.OrgMemberInfo: List of organization members
	//   - error: nil if successful, otherwise the error that occurred
	ListMembers(ctx context.Context, input dto.ListOrgMembersInput) ([]entities.OrgMemberInfo, error)

	// CountMembers counts the total number of organization members.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - input: Criteria for counting members including filtering
	//
	// Returns:
	//   - int: Total number of members
	//   - error: nil if successful, otherwise the error that occurred
	CountMembers(ctx context.Context, input dto.ListOrgMembersInput) (int, error)

	// DeleteOrgMembersByUserID removes a user from all organizations.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - userID: UUID of the user to remove from all organizations
	//
	// Returns:
	//   - error: nil if successful, otherwise the error that occurred
	DeleteOrgMembersByUserID(ctx context.Context, userID uuid.UUID) error

	// DeleteOrgMemberByID removes a specific organization member by ID.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - id: UUID of the organization member to remove
	//
	// Returns:
	//   - error: nil if successful, otherwise the error that occurred
	DeleteOrgMemberByID(ctx context.Context, id uuid.UUID) error
}

var _ OrgMemberRepository = (*repository)(nil)

// DeleteMembersByOrganizationID removes all members from an organization.
func (r repository) DeleteMembersByOrganizationID(ctx context.Context, organizationID uuid.UUID) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.org_members.DeleteMembersByOrganizationID")
	defer span.End()

	err := r.GetDB(ctx).Where("org_id = ?", organizationID).Delete(&entities.OrgMember{}).Error
	if err != nil {
		span.SetStatus(codes.Error, "failed to delete members by organization ID")
		span.RecordError(err)
		return err
	}
	span.AddEvent("deleted members by organization ID")
	span.SetStatus(codes.Ok, "deleted members by organization ID")
	return nil
}

// FilterOrgMember contains the criteria for finding an organization member.
type FilterOrgMember struct {
	OrgID  *uuid.UUID
	UserID *uuid.UUID
	Role   enums.RepoRole
}

// FindOrgMember retrieves a specific organization member based on filter criteria.
func (r repository) FindOrgMember(ctx context.Context, filter FilterOrgMember) (*entities.OrgMember, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.org_members.FindOrgMember")
	defer span.End()

	var result entities.OrgMember

	query := r.GetDB(ctx)

	if filter.OrgID != nil {
		query = query.Where("org_id = ?", filter.OrgID)
	}

	if filter.UserID != nil {
		query = query.Where("user_id = ?", filter.UserID)
	}

	if filter.Role != "" {
		query = query.Where("role = ?", filter.Role)
	}

	if err := query.First(&result).Error; err != nil {
		span.SetStatus(codes.Error, "failed to find org member")
		span.RecordError(err)
		return nil, err
	}

	span.AddEvent("org member found")
	span.SetStatus(codes.Ok, "org member found")
	return &result, nil
}

// ListMembers retrieves a paginated list of organization members.
func (r repository) ListMembers(ctx context.Context, input dto.ListOrgMembersInput) ([]entities.OrgMemberInfo, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.org_members.ListMembers")
	defer span.End()

	var members []entities.OrgMemberInfo
	query := r.GetDB(ctx).Table("org_members").
		Select("org_members.*, users.name AS name, au.email AS email, users.avatar AS avatar").
		Joins("LEFT JOIN users ON org_members.user_id = users.id").
		Joins("LEFT JOIN auth.users au ON users.id = au.id").
		Preload("User").
		Preload("Org").
		Where("org_members.org_id = ?", input.OrgId)

	if input.Keyword != "" {
		keyword := fmt.Sprintf("%%%s%%", input.Keyword)
		query = query.Where("users.name ILIKE ? OR au.email ILIKE ?", keyword, keyword)
	}

	if input.Paginate.Page != 0 && input.Paginate.PerPage != 0 {
		query = query.Scopes(paginate(input.Paginate.Page, input.Paginate.PerPage))
	}

	if input.Paginate.OrderBy != "" && input.Paginate.Sort != "" {
		query = query.Scopes(orderBy(types.OrderBy{input.Paginate.OrderBy: input.Paginate.Sort}))
	}

	err := query.Find(&members).Error
	if err != nil {
		span.SetStatus(codes.Error, "failed to list members")
		span.RecordError(err)
		return nil, err
	}

	span.AddEvent("members listed")
	span.SetStatus(codes.Ok, "members listed")
	return members, nil
}

// CountMembers counts the total number of organization members.
func (r repository) CountMembers(ctx context.Context, input dto.ListOrgMembersInput) (int, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.org_members.CountMembers")
	defer span.End()

	var memberNum int64
	query := r.GetDB(ctx).Table("org_members").
		Select("org_members.*, users.name AS name, au.email AS email").
		Joins("LEFT JOIN users ON org_members.user_id = users.id").
		Joins("LEFT JOIN auth.users au ON users.id = au.id").
		Where("org_members.org_id = ?", input.OrgId)

	if input.Keyword != "" {
		keyword := fmt.Sprintf("%%%s%%", input.Keyword)
		query = query.Where("users.name ILIKE ? OR au.email ILIKE ?", keyword, keyword)
	}

	err := query.Count(&memberNum).Error

	if err != nil {
		span.SetStatus(codes.Error, "failed to count members")
		span.RecordError(err)
		return 0, err
	}

	span.AddEvent("members counted")
	span.SetStatus(codes.Ok, "members counted")
	return int(memberNum), nil
}

// DeleteOrgMembersByUserID removes a user from all organizations.
func (r repository) DeleteOrgMembersByUserID(ctx context.Context, userID uuid.UUID) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.org_members.DeleteOrgMembersByUserID")
	defer span.End()

	err := r.GetDB(ctx).Where("user_id = ?", userID).Delete(&entities.OrgMember{}).Error
	if err != nil {
		span.SetStatus(codes.Error, "failed to delete org members by user ID")
		span.RecordError(err)
		return err
	}
	span.AddEvent("deleted org members by user ID")
	span.SetStatus(codes.Ok, "deleted org members by user ID")
	return nil
}

// DeleteOrgMemberByID removes a specific organization member by ID.
func (r repository) DeleteOrgMemberByID(ctx context.Context, id uuid.UUID) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.org_members.DeleteOrgMemberByID")
	defer span.End()

	err := r.GetDB(ctx).Where("id = ?", id).Delete(&entities.OrgMember{}).Error
	if err != nil {
		span.SetStatus(codes.Error, "failed to delete org member by ID")
		span.RecordError(err)
		return err
	}
	span.AddEvent("deleted org member by ID")
	span.SetStatus(codes.Ok, "deleted org member by ID")
	return nil
}
