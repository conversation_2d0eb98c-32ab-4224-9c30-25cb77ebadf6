package repository

import (
	"context"
	"errors"

	"github.com/google/uuid"
	"go.opentelemetry.io/otel/codes"
	"gorm.io/gorm"

	"api-server/internal/entities"
	"api-server/pkg/oteltrace"
)

// SignupRequestRepository defines the interface for signup request operations.
// It provides methods for managing signup requests, including creating, listing,
// and retrieving signup requests.
type SignupRequestRepository interface {
	// UpsertSignupRequest creates or updates a signup request.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - input: The signup request data to create or update
	//
	// Returns:
	//   - error: nil if successful, otherwise the error that occurred
	UpsertSignupRequest(ctx context.Context, input *entities.SignupRequest) error

	// ListSignUpRequests retrieves all signup requests.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//
	// Returns:
	//   - []entities.SignupRequest: List of all signup requests
	//   - error: nil if successful, otherwise the error that occurred
	ListSignUpRequests(ctx context.Context) ([]entities.SignupRequest, error)

	// GetSignUpRequestByID retrieves a specific signup request by its ID.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - id: UUID of the signup request to retrieve
	//
	// Returns:
	//   - *entities.SignupRequest: The found signup request if successful
	//   - error: nil if successful, otherwise the error that occurred
	GetSignUpRequestByID(ctx context.Context, id uuid.UUID) (*entities.SignupRequest, error)
}

var _ SignupRequestRepository = (*repository)(nil)

// UpsertSignupRequest creates or updates a signup request.
func (r repository) UpsertSignupRequest(ctx context.Context, input *entities.SignupRequest) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.UpsertSignupRequest")
	defer span.End()

	var oldSignupRequest entities.SignupRequest
	err := r.GetDB(ctx).WithContext(ctx).
		Where("email = ?", input.Email).Or("username = ?", input.Username).
		First(&oldSignupRequest).Error

	if err == nil && oldSignupRequest.Email == input.Email {
		err = errors.New("email already exists")
		span.SetStatus(codes.Error, "email already exists")
		span.RecordError(err)
		return err
	}

	if err == nil && oldSignupRequest.Username == input.Username {
		err = errors.New("username already exists")
		span.SetStatus(codes.Error, "username already exists")
		span.RecordError(err)
		return err
	}

	if !errors.Is(err, gorm.ErrRecordNotFound) {
		span.SetStatus(codes.Error, "failed to insert/update signup request, db error")
		span.RecordError(err)
		return err
	}

	err = r.GetDB(ctx).WithContext(ctx).Create(input).Error
	if err != nil {
		span.SetStatus(codes.Error, "failed to create signup request")
		span.RecordError(err)
		return err
	}

	span.AddEvent("signup request upserted successfully")
	span.SetStatus(codes.Ok, "signup request upserted successfully")
	return nil
}

// ListSignUpRequests retrieves all signup requests.
func (r repository) ListSignUpRequests(ctx context.Context) ([]entities.SignupRequest, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.ListSignUpRequests")
	defer span.End()

	var result []entities.SignupRequest

	err := r.
		GetDB(ctx).
		Find(&result).Error
	if err != nil {
		span.SetStatus(codes.Error, "failed to list signup requests")
		span.RecordError(err)
		return nil, err
	}

	span.AddEvent("signup requests listed successfully")
	span.SetStatus(codes.Ok, "signup requests listed successfully")
	return result, nil
}

// GetSignUpRequestByID retrieves a specific signup request by its ID.
func (r repository) GetSignUpRequestByID(ctx context.Context, id uuid.UUID) (*entities.SignupRequest, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "repositories.GetSignUpRequestByID")
	defer span.End()

	var result entities.SignupRequest

	err := r.
		GetDB(ctx).
		Where("id = ?", id).
		First(&result).Error
	if err != nil {
		span.SetStatus(codes.Error, "failed to get signup request by ID")
		span.RecordError(err)
		return nil, err
	}

	span.AddEvent("signup request retrieved by ID successfully")
	span.SetStatus(codes.Ok, "signup request retrieved by ID successfully")
	return &result, nil
}
