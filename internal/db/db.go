package db

import (
	"database/sql"
	"fmt"

	"github.com/golang-migrate/migrate/v4"
	"github.com/golang-migrate/migrate/v4/database/postgres"
	_ "github.com/golang-migrate/migrate/v4/source/file"
	_ "github.com/lib/pq"

	"api-server/configs"
	"api-server/pkg/otelzap"
)

// NewDb creates a new database connection using the provided configuration.
// It initializes a PostgreSQL database connection using the connection URL from the config.
//
// Parameters:
//   - config: Database configuration containing connection details
//
// Returns:
//   - *sql.DB: A pointer to the database connection if successful
//   - error: Any error that occurred during connection initialization
func NewDb(config *configs.DatabaseConfig) (*sql.DB, error) {
	db, err := sql.Open("postgres", config.Url)
	if err != nil {
		return nil, err
	}

	otelzap.Logger.Info("initialized new database instance")
	return db, nil
}

// Migrate performs database migrations using the specified migration directory.
// It uses golang-migrate to apply all pending migrations to the database.
// The function will return an error if any migration fails, except when there are no changes to apply.
//
// Parameters:
//   - dir: Directory containing migration files
//   - config: Database configuration for connection details
//   - dbInstance: Active database connection instance
//
// Returns:
//   - error: Any error that occurred during migration, except ErrNoChange
func Migrate(dir string, config *configs.DatabaseConfig, dbInstance *sql.DB) error {
	otelzap.Logger.Debug("migrating database")
	driver, err := postgres.WithInstance(dbInstance, &postgres.Config{})
	if err != nil {
		return err
	}

	m, err := migrate.NewWithDatabaseInstance(
		fmt.Sprintf("file://%s", dir),
		config.Url,
		driver,
	)
	if err != nil {
		return err
	}

	if err := m.Up(); err != nil && err != migrate.ErrNoChange {
		return err
	}

	otelzap.Logger.Info("database migration completed")
	return nil
}

// HealthCheckPostgresDB performs a health check on the PostgreSQL database connection.
// It attempts to ping the database to verify the connection is alive and working.
//
// Parameters:
//   - config: Database configuration (unused in the current implementation)
//   - dbInstance: Active database connection instance to check
//
// Returns:
//   - bool: true if the database is healthy and responding, false otherwise
func HealthCheckPostgresDB(config *configs.DatabaseConfig, dbInstance *sql.DB) bool {
	if err := dbInstance.Ping(); err != nil {
		otelzap.Logger.Error(fmt.Sprintf("PostgreSQL ping failed: %v", err))
		return false
	}

	otelzap.Logger.Info("PostgreSQL is healthy ✅")
	return true
}
