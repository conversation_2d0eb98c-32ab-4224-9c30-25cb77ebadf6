package aws

import (
	"context"

	"github.com/aws/aws-sdk-go-v2/service/ecr"
	"go.opentelemetry.io/otel/codes"

	"api-server/pkg/oteltrace"
	"api-server/pkg/otelzap"
)

func (i *impl) DescribeRepositories(ctx context.Context, params *ecr.DescribeRepositoriesInput) (*ecr.DescribeRepositoriesOutput, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "gateways.aws.DescribeRepositories")
	defer span.End()

	otelzap.DebugWithContext(ctx, "describing repositories")

	output, err := i.ecrClient.DescribeRepositories(ctx, params)
	if err != nil {
		span.SetStatus(codes.Error, "failed to describe repositories")
		span.RecordError(err)
		return nil, err
	}

	otelzap.InfoWithContext(ctx, "describe repositories success")
	return output, nil
}
