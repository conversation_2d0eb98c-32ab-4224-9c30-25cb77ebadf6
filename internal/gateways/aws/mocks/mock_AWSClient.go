// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	context "context"

	ecr "github.com/aws/aws-sdk-go-v2/service/ecr"
	mock "github.com/stretchr/testify/mock"

	multipart "mime/multipart"

	sts "github.com/aws/aws-sdk-go-v2/service/sts"
)

// MockAWSClient is an autogenerated mock type for the AWSClient type
type MockAWSClient struct {
	mock.Mock
}

type MockAWSClient_Expecter struct {
	mock *mock.Mock
}

func (_m *MockAWSClient) EXPECT() *MockAWSClient_Expecter {
	return &MockAWSClient_Expecter{mock: &_m.Mock}
}

// DeleteImage provides a mock function with given fields: ctx, fileName
func (_m *MockAWSClient) DeleteImage(ctx context.Context, fileName string) error {
	ret := _m.Called(ctx, fileName)

	if len(ret) == 0 {
		panic("no return value specified for DeleteImage")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string) error); ok {
		r0 = rf(ctx, fileName)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockAWSClient_DeleteImage_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteImage'
type MockAWSClient_DeleteImage_Call struct {
	*mock.Call
}

// DeleteImage is a helper method to define mock.On call
//   - ctx context.Context
//   - fileName string
func (_e *MockAWSClient_Expecter) DeleteImage(ctx interface{}, fileName interface{}) *MockAWSClient_DeleteImage_Call {
	return &MockAWSClient_DeleteImage_Call{Call: _e.mock.On("DeleteImage", ctx, fileName)}
}

func (_c *MockAWSClient_DeleteImage_Call) Run(run func(ctx context.Context, fileName string)) *MockAWSClient_DeleteImage_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockAWSClient_DeleteImage_Call) Return(_a0 error) *MockAWSClient_DeleteImage_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockAWSClient_DeleteImage_Call) RunAndReturn(run func(context.Context, string) error) *MockAWSClient_DeleteImage_Call {
	_c.Call.Return(run)
	return _c
}

// DescribeImages provides a mock function with given fields: ctx, params
func (_m *MockAWSClient) DescribeImages(ctx context.Context, params *ecr.DescribeImagesInput) (*ecr.DescribeImagesOutput, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for DescribeImages")
	}

	var r0 *ecr.DescribeImagesOutput
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *ecr.DescribeImagesInput) (*ecr.DescribeImagesOutput, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *ecr.DescribeImagesInput) *ecr.DescribeImagesOutput); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*ecr.DescribeImagesOutput)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *ecr.DescribeImagesInput) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockAWSClient_DescribeImages_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DescribeImages'
type MockAWSClient_DescribeImages_Call struct {
	*mock.Call
}

// DescribeImages is a helper method to define mock.On call
//   - ctx context.Context
//   - params *ecr.DescribeImagesInput
func (_e *MockAWSClient_Expecter) DescribeImages(ctx interface{}, params interface{}) *MockAWSClient_DescribeImages_Call {
	return &MockAWSClient_DescribeImages_Call{Call: _e.mock.On("DescribeImages", ctx, params)}
}

func (_c *MockAWSClient_DescribeImages_Call) Run(run func(ctx context.Context, params *ecr.DescribeImagesInput)) *MockAWSClient_DescribeImages_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*ecr.DescribeImagesInput))
	})
	return _c
}

func (_c *MockAWSClient_DescribeImages_Call) Return(_a0 *ecr.DescribeImagesOutput, _a1 error) *MockAWSClient_DescribeImages_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockAWSClient_DescribeImages_Call) RunAndReturn(run func(context.Context, *ecr.DescribeImagesInput) (*ecr.DescribeImagesOutput, error)) *MockAWSClient_DescribeImages_Call {
	_c.Call.Return(run)
	return _c
}

// DescribeRepositories provides a mock function with given fields: ctx, params
func (_m *MockAWSClient) DescribeRepositories(ctx context.Context, params *ecr.DescribeRepositoriesInput) (*ecr.DescribeRepositoriesOutput, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for DescribeRepositories")
	}

	var r0 *ecr.DescribeRepositoriesOutput
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *ecr.DescribeRepositoriesInput) (*ecr.DescribeRepositoriesOutput, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *ecr.DescribeRepositoriesInput) *ecr.DescribeRepositoriesOutput); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*ecr.DescribeRepositoriesOutput)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *ecr.DescribeRepositoriesInput) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockAWSClient_DescribeRepositories_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DescribeRepositories'
type MockAWSClient_DescribeRepositories_Call struct {
	*mock.Call
}

// DescribeRepositories is a helper method to define mock.On call
//   - ctx context.Context
//   - params *ecr.DescribeRepositoriesInput
func (_e *MockAWSClient_Expecter) DescribeRepositories(ctx interface{}, params interface{}) *MockAWSClient_DescribeRepositories_Call {
	return &MockAWSClient_DescribeRepositories_Call{Call: _e.mock.On("DescribeRepositories", ctx, params)}
}

func (_c *MockAWSClient_DescribeRepositories_Call) Run(run func(ctx context.Context, params *ecr.DescribeRepositoriesInput)) *MockAWSClient_DescribeRepositories_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*ecr.DescribeRepositoriesInput))
	})
	return _c
}

func (_c *MockAWSClient_DescribeRepositories_Call) Return(_a0 *ecr.DescribeRepositoriesOutput, _a1 error) *MockAWSClient_DescribeRepositories_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockAWSClient_DescribeRepositories_Call) RunAndReturn(run func(context.Context, *ecr.DescribeRepositoriesInput) (*ecr.DescribeRepositoriesOutput, error)) *MockAWSClient_DescribeRepositories_Call {
	_c.Call.Return(run)
	return _c
}

// GenPreSignUrl provides a mock function with given fields: ctx, fileName
func (_m *MockAWSClient) GenPreSignUrl(ctx context.Context, fileName string) (string, error) {
	ret := _m.Called(ctx, fileName)

	if len(ret) == 0 {
		panic("no return value specified for GenPreSignUrl")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (string, error)); ok {
		return rf(ctx, fileName)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) string); ok {
		r0 = rf(ctx, fileName)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, fileName)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockAWSClient_GenPreSignUrl_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GenPreSignUrl'
type MockAWSClient_GenPreSignUrl_Call struct {
	*mock.Call
}

// GenPreSignUrl is a helper method to define mock.On call
//   - ctx context.Context
//   - fileName string
func (_e *MockAWSClient_Expecter) GenPreSignUrl(ctx interface{}, fileName interface{}) *MockAWSClient_GenPreSignUrl_Call {
	return &MockAWSClient_GenPreSignUrl_Call{Call: _e.mock.On("GenPreSignUrl", ctx, fileName)}
}

func (_c *MockAWSClient_GenPreSignUrl_Call) Run(run func(ctx context.Context, fileName string)) *MockAWSClient_GenPreSignUrl_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockAWSClient_GenPreSignUrl_Call) Return(_a0 string, _a1 error) *MockAWSClient_GenPreSignUrl_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockAWSClient_GenPreSignUrl_Call) RunAndReturn(run func(context.Context, string) (string, error)) *MockAWSClient_GenPreSignUrl_Call {
	_c.Call.Return(run)
	return _c
}

// STS provides a mock function with no fields
func (_m *MockAWSClient) STS() *sts.Client {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for STS")
	}

	var r0 *sts.Client
	if rf, ok := ret.Get(0).(func() *sts.Client); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*sts.Client)
		}
	}

	return r0
}

// MockAWSClient_STS_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'STS'
type MockAWSClient_STS_Call struct {
	*mock.Call
}

// STS is a helper method to define mock.On call
func (_e *MockAWSClient_Expecter) STS() *MockAWSClient_STS_Call {
	return &MockAWSClient_STS_Call{Call: _e.mock.On("STS")}
}

func (_c *MockAWSClient_STS_Call) Run(run func()) *MockAWSClient_STS_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockAWSClient_STS_Call) Return(_a0 *sts.Client) *MockAWSClient_STS_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockAWSClient_STS_Call) RunAndReturn(run func() *sts.Client) *MockAWSClient_STS_Call {
	_c.Call.Return(run)
	return _c
}

// UploadImage provides a mock function with given fields: ctx, file, prefix
func (_m *MockAWSClient) UploadImage(ctx context.Context, file multipart.FileHeader, prefix string) (string, error) {
	ret := _m.Called(ctx, file, prefix)

	if len(ret) == 0 {
		panic("no return value specified for UploadImage")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, multipart.FileHeader, string) (string, error)); ok {
		return rf(ctx, file, prefix)
	}
	if rf, ok := ret.Get(0).(func(context.Context, multipart.FileHeader, string) string); ok {
		r0 = rf(ctx, file, prefix)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(context.Context, multipart.FileHeader, string) error); ok {
		r1 = rf(ctx, file, prefix)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockAWSClient_UploadImage_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UploadImage'
type MockAWSClient_UploadImage_Call struct {
	*mock.Call
}

// UploadImage is a helper method to define mock.On call
//   - ctx context.Context
//   - file multipart.FileHeader
//   - prefix string
func (_e *MockAWSClient_Expecter) UploadImage(ctx interface{}, file interface{}, prefix interface{}) *MockAWSClient_UploadImage_Call {
	return &MockAWSClient_UploadImage_Call{Call: _e.mock.On("UploadImage", ctx, file, prefix)}
}

func (_c *MockAWSClient_UploadImage_Call) Run(run func(ctx context.Context, file multipart.FileHeader, prefix string)) *MockAWSClient_UploadImage_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(multipart.FileHeader), args[2].(string))
	})
	return _c
}

func (_c *MockAWSClient_UploadImage_Call) Return(_a0 string, _a1 error) *MockAWSClient_UploadImage_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockAWSClient_UploadImage_Call) RunAndReturn(run func(context.Context, multipart.FileHeader, string) (string, error)) *MockAWSClient_UploadImage_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockAWSClient creates a new instance of MockAWSClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockAWSClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockAWSClient {
	mock := &MockAWSClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
