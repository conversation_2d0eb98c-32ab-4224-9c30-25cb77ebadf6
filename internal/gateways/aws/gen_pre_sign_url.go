package aws

import (
	"context"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/s3"

	"api-server/pkg/oteltrace"
)

// GenPreSignUrl generates a pre-signed URL for accessing a file in AWS S3 bucket.
// The URL is valid for a specific duration defined by presignExpire configuration.
// This is useful for providing temporary access to private S3 objects without requiring AWS credentials.
//
// Parameters:
//   - ctx: Context for the operation
//   - fileName: The name of the file in the S3 bucket to generate the pre-signed URL for
//
// Returns:
//   - string: The pre-signed URL that can be used to access the file
//   - error: Any error that occurred during URL generation
func (i *impl) GenPreSignUrl(ctx context.Context, fileName string) (string, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "gateways.aws.GenPreSignUrl")
	defer span.End()

	req, err := i.presigner.PresignGetObject(ctx, &s3.GetObjectInput{
		Bucket: aws.String(i.bucket),
		Key:    aws.String(fileName),
	}, s3.WithPresignExpires(time.Duration(i.presignExpire)*time.Second))
	if err != nil {
		return "", err
	}

	return req.URL, nil
}
