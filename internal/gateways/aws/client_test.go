package aws

import (
	"context"
	"testing"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"api-server/configs"
)

// S3API defines the interface for S3 operations
type S3API interface {
	PutObject(ctx context.Context, params *s3.PutObjectInput, optFns ...func(*s3.Options)) (*s3.PutObjectOutput, error)
	DeleteObject(ctx context.Context, params *s3.DeleteObjectInput, optFns ...func(*s3.Options)) (*s3.DeleteObjectOutput, error)
}

// MockPresigner is a mock implementation of the presigner client
type MockPresigner struct {
	mock.Mock
}

// MockS3Client is a mock implementation of the S3API interface
type MockS3Client struct {
	mock.Mock
}

func (m *MockS3Client) PutObject(ctx context.Context, params *s3.PutObjectInput, optFns ...func(*s3.Options)) (*s3.PutObjectOutput, error) {
	args := m.Called(ctx, params)
	return args.Get(0).(*s3.PutObjectOutput), args.Error(1)
}

func (m *MockS3Client) DeleteObject(ctx context.Context, params *s3.DeleteObjectInput, optFns ...func(*s3.Options)) (*s3.DeleteObjectOutput, error) {
	args := m.Called(ctx, params)
	return args.Get(0).(*s3.DeleteObjectOutput), args.Error(1)
}

// testImpl is a test-specific implementation that uses S3API interface
type testImpl struct {
	region        string
	bucket        string
	awsConfig     *aws.Config
	s3Client      S3API
	presigner     *MockPresigner
	presignExpire int64
}

func TestNew(t *testing.T) {
	tests := []struct {
		name      string
		awsConfig *configs.AWSConfig
		wantErr   bool
	}{
		{
			name: "valid local config",
			awsConfig: &configs.AWSConfig{
				Region:        "us-east-1",
				ImageBucket:   "test-bucket",
				PreSignExpire: 3600,
				InCluster:     false,
				AccessKey:     "test-access-key",
				SecretKey:     "test-secret-key",
			},
			wantErr: false,
		},
		{
			name: "valid in-cluster config",
			awsConfig: &configs.AWSConfig{
				Region:        "us-east-1",
				ImageBucket:   "test-bucket",
				PreSignExpire: 3600,
				InCluster:     true,
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			client, err := New(tt.awsConfig)
			if tt.wantErr {
				assert.Error(t, err)
				return
			}
			assert.NoError(t, err)
			assert.NotNil(t, client)
			assert.Equal(t, tt.awsConfig.Region, client.region)
			assert.Equal(t, tt.awsConfig.ImageBucket, client.bucket)
			assert.Equal(t, tt.awsConfig.PreSignExpire, client.presignExpire)
		})
	}
}
