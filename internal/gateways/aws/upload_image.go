package aws

import (
	"context"
	"fmt"
	"mime/multipart"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"go.opentelemetry.io/otel/codes"

	"api-server/pkg/oteltrace"
	"api-server/pkg/otelzap"
)

// UploadImage uploads a file to AWS S3 bucket and returns the generated file name.
// The function takes a multipart file header and a prefix string to generate a unique file name.
// The file is stored with private ACL (Access Control List).
//
// Parameters:
//   - ctx: Context for the operation
//   - uploadFile: The file header containing the file to upload
//   - prefix: String prefix to be added to the file name
//
// Returns:
//   - string: The generated file name if successful
//   - error: Any error that occurred during the upload process
func (i *impl) UploadImage(ctx context.Context, uploadFile multipart.FileHeader, prefix string) (string, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "gateways.aws.UploadAvatarRepo")
	defer span.End()

	otelzap.DebugWithContext(ctx, "upload images")

	file, err := uploadFile.Open()
	if err != nil {
		span.SetStatus(codes.Error, "failed to open file")
		span.RecordError(err)
		return "", err
	}
	defer file.Close()

	fileName := fmt.Sprintf("%s_%d_%s", prefix, time.Now().UnixMicro(), uploadFile.Filename)
	input := &s3.PutObjectInput{
		Bucket: aws.String(i.bucket),
		Key:    aws.String(fileName),
		Body:   file,
		ACL:    "private",
	}

	_, err = i.s3Client.PutObject(ctx, input)
	if err != nil {
		span.SetStatus(codes.Error, "failed to add s3 image")
		span.RecordError(err)
		return "", err
	}

	otelzap.InfoWithContext(ctx, "upload image success")
	return fileName, nil
}

// DeleteImage deletes a file from AWS S3 bucket using the provided file name.
// The function removes the specified file from the configured S3 bucket.
//
// Parameters:
//   - ctx: Context for the operation
//   - fileName: The name of the file to delete
//
// Returns:
//   - error: Any error that occurred during the deletion process
func (i *impl) DeleteImage(ctx context.Context, fileName string) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "gateways.aws.DeleteImage")
	defer span.End()

	_, err := i.s3Client.DeleteObject(ctx, &s3.DeleteObjectInput{
		Bucket: aws.String(i.bucket),
		Key:    aws.String(fileName),
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to delete s3 image")
		span.RecordError(err)
		return err

	}

	otelzap.InfoWithContext(ctx, "delete image success")
	return nil
}
