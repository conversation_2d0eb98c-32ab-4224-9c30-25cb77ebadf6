package aws

import (
	"context"

	"github.com/aws/aws-sdk-go-v2/service/ecr"
	"go.opentelemetry.io/otel/codes"

	"api-server/pkg/oteltrace"
	"api-server/pkg/otelzap"
)

func (i *impl) DescribeImages(ctx context.Context, params *ecr.DescribeImagesInput) (*ecr.DescribeImagesOutput, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "gateways.aws.DescribeImages")
	defer span.End()

	otelzap.DebugWithContext(ctx, "describing images")

	output, err := i.ecrClient.DescribeImages(ctx, params)
	if err != nil {
		span.SetStatus(codes.Error, "failed to describe images")
		span.RecordError(err)
		return nil, err
	}

	otelzap.InfoWithContext(ctx, "describe images success")
	return output, nil
}
