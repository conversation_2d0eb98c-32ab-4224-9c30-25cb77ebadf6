package aws

import (
	"context"
	"fmt"
	"mime/multipart"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/ecr"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/aws/aws-sdk-go-v2/service/sts"

	"api-server/configs"
	"api-server/pkg/otelzap"
)

type AWSClient interface {
	// s3
	UploadImage(ctx context.Context, file multipart.FileHeader, prefix string) (string, error)
	GenPreSignUrl(ctx context.Context, fileName string) (string, error)
	DeleteImage(ctx context.Context, fileName string) error

	// ecr
	DescribeImages(ctx context.Context, params *ecr.DescribeImagesInput) (*ecr.DescribeImagesOutput, error)
	DescribeRepositories(ctx context.Context, params *ecr.DescribeRepositoriesInput) (*ecr.DescribeRepositoriesOutput, error)

	// sts
	STS() *sts.Client
}

var _ AWSClient = (*impl)(nil)

type impl struct {
	region    string
	bucket    string
	awsConfig *aws.Config

	s3Client      *s3.Client
	presigner     *s3.PresignClient
	presignExpire int64

	ecrClient *ecr.Client
	stsClient *sts.Client
}

func New(awsConfig *configs.AWSConfig) (*impl, error) {
	a := &impl{
		region:        awsConfig.Region,
		bucket:        awsConfig.ImageBucket,
		presignExpire: awsConfig.PreSignExpire,
	}

	var awsCfg aws.Config
	if !awsConfig.InCluster {
		customResolver := aws.CredentialsProviderFunc(func(ctx context.Context) (aws.Credentials, error) {
			return aws.Credentials{
				AccessKeyID:     awsConfig.AccessKey,
				SecretAccessKey: awsConfig.SecretKey,
			}, nil
		})

		cfg, err := config.LoadDefaultConfig(context.Background(), config.WithRegion(awsConfig.Region), config.WithCredentialsProvider(customResolver))
		if err != nil {
			otelzap.Logger.Fatal(fmt.Sprintf("init local AWS config error %+v", err))
		}
		awsCfg = cfg
		otelzap.Logger.Info("init local AWS config")
	} else {
		cfg, err := config.LoadDefaultConfig(context.Background(), config.WithRegion(awsConfig.Region))
		if err != nil {
			otelzap.Logger.Fatal(fmt.Sprintf("init in-cluster AWS config error %+v", err))
		}
		awsCfg = cfg
		otelzap.Logger.Info("init in-cluster AWS config")
	}

	a.awsConfig = &awsCfg

	a.s3Client = s3.NewFromConfig(awsCfg)
	otelzap.Logger.Info("initialized new AWS S3 client")

	a.presigner = s3.NewPresignClient(a.s3Client)
	otelzap.Logger.Info("initialized new AWS S3 presign client")

	a.ecrClient = ecr.NewFromConfig(awsCfg)
	otelzap.Logger.Info("initialized new AWS ECR client")

	a.stsClient = sts.NewFromConfig(awsCfg)
	otelzap.Logger.Info("initialized new AWS STS client")

	return a, nil
}

func (a *impl) ECR() *ecr.Client {
	return a.ecrClient
}

func (a *impl) STS() *sts.Client {
	return a.stsClient
}
