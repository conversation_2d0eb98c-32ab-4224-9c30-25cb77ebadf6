// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	mail "api-server/internal/gateways/mail"
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// MockMailClient is an autogenerated mock type for the MailClient type
type MockMailClient struct {
	mock.Mock
}

type MockMailClient_Expecter struct {
	mock *mock.Mock
}

func (_m *MockMailClient) EXPECT() *MockMailClient_Expecter {
	return &MockMailClient_Expecter{mock: &_m.Mock}
}

// SendMail provides a mock function with given fields: ctx, input
func (_m *MockMailClient) SendMail(ctx context.Context, input mail.SendEmailInput) error {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for SendMail")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, mail.SendEmailInput) error); ok {
		r0 = rf(ctx, input)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockMailClient_SendMail_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SendMail'
type MockMailClient_SendMail_Call struct {
	*mock.Call
}

// SendMail is a helper method to define mock.On call
//   - ctx context.Context
//   - input mail.SendEmailInput
func (_e *MockMailClient_Expecter) SendMail(ctx interface{}, input interface{}) *MockMailClient_SendMail_Call {
	return &MockMailClient_SendMail_Call{Call: _e.mock.On("SendMail", ctx, input)}
}

func (_c *MockMailClient_SendMail_Call) Run(run func(ctx context.Context, input mail.SendEmailInput)) *MockMailClient_SendMail_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(mail.SendEmailInput))
	})
	return _c
}

func (_c *MockMailClient_SendMail_Call) Return(_a0 error) *MockMailClient_SendMail_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockMailClient_SendMail_Call) RunAndReturn(run func(context.Context, mail.SendEmailInput) error) *MockMailClient_SendMail_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockMailClient creates a new instance of MockMailClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockMailClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockMailClient {
	mock := &MockMailClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
