package mail

import (
	"bytes"
	"context"
	"fmt"
	"html/template"
	"net/smtp"
	"os"
	"path/filepath"

	"github.com/jordan-wright/email"
	"go.opentelemetry.io/otel/codes"

	"api-server/pkg/oteltrace"
)

// MailClient defines the interface for email-related operations.
// It provides methods for sending emails using templates.
type MailClient interface {
	// SendMail sends an email to one or more recipients using a template.
	// The email content is generated from an HTML template and can include dynamic data.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - input: SendEmailInput containing recipient emails, template name, payload data, and email title
	//
	// Returns:
	//   - error: Any error that occurred during email sending
	SendMail(ctx context.Context, input SendEmailInput) error
}

var _ MailClient = (*client)(nil)

// EmailParams contains the configuration parameters for the email client.
// These parameters are used to authenticate and connect to the SMTP server.
type EmailParams struct {
	Host     string // SMTP server hostname
	Port     string // SMTP server port
	Username string // SMTP authentication username
	Password string // SMTP authentication password
	From     string // Sender email address
}

type client struct {
	Email EmailParams
}

// New creates a new instance of the email client.
// This function initializes the client with the provided email configuration parameters.
//
// Parameters:
//   - email: EmailParams containing SMTP server configuration
//
// Returns:
//   - *client: A new instance of the email client
func New(email EmailParams) *client {
	return &client{
		Email: email,
	}
}

// SendMail implements the MailClient interface for sending emails.
// This function reads an HTML template, processes it with the provided payload,
// and sends the email asynchronously to the specified recipients.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: SendEmailInput containing recipient emails, template name, payload data, and email title
//
// Returns:
//   - error: Any error that occurred during email sending
func (c client) SendMail(ctx context.Context, input SendEmailInput) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "gateways.mail.SendMail")
	defer span.End()

	path, err := os.Getwd()
	if err != nil {
		return err
	}
	content, err := os.ReadFile(filepath.Join(path, ".", "static", "invite", input.TemplateName))
	if err != nil {
		return err
	}

	// Parse the email template using html/template
	tmpl, err := template.New("email").Parse(string(content))
	if err != nil {
		return fmt.Errorf("failed to parse email template: %w", err)
	}

	// Execute the template with the provided payload
	var bodyBuffer bytes.Buffer
	if err := tmpl.Execute(&bodyBuffer, input.Payload); err != nil {
		return fmt.Errorf("failed to execute email template: %w", err)
	}

	// Parse the title as a template (to allow dynamic data in the subject)
	titleTemplate, err := template.New("title").Parse(input.Title)
	if err != nil {
		return fmt.Errorf("failed to parse email title template: %w", err)
	}

	// Execute the title template with the provided payload
	var titleBuffer bytes.Buffer
	if err := titleTemplate.Execute(&titleBuffer, input.Payload); err != nil {
		return fmt.Errorf("failed to execute email title template: %w", err)
	}

	// Prepare email
	e := email.NewEmail()
	e.From = c.Email.From
	e.To = input.UserEmails
	e.Subject = titleBuffer.String()
	e.HTML = bodyBuffer.Bytes()
	go func() {
		errEmail := e.Send(fmt.Sprintf("%s:%s", c.Email.Host, c.Email.Port), smtp.PlainAuth("", c.Email.Username, c.Email.Password, c.Email.Host))
		if errEmail != nil {
			span.SetStatus(codes.Error, errEmail.Error())
			span.RecordError(errEmail)
		}

		return
	}()

	return nil
}
