package gitlab

import (
	"fmt"
	"regexp"
	"strings"
)

// GenerateUsername creates a valid GitLab username from an email address.
// This function extracts the local part of the email (before @) and sanitizes it
// by replacing all special characters except dash, dot, and underscore with underscores.
//
// Parameters:
//   - email: The email address to generate a username from
//
// Returns:
//   - string: The generated username if successful
//   - error: Error if the email format is invalid
//
// Example:
//
//	email: "<EMAIL>" -> username: "john.doe_test"
func GenerateUsername(email string) (string, error) {
	emailParts := strings.Split(email, "@")
	if len(emailParts) < 2 {
		return "", fmt.Errorf("invalid email format")
	}
	// replace all special characters except dash, dot and underscore by underscore
	username := regexp.MustCompile(`[^0-9a-zA-Z\-\_]`).ReplaceAllString(emailParts[0], "_")

	return username, nil
}
