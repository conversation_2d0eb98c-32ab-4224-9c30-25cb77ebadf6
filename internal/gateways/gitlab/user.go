package gitlab

import (
	"api-server/pkg/oteltrace"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"

	"go.opentelemetry.io/otel/codes"
)

type CreateUserRequest struct {
	AdminAccessToken    string
	Username            string `json:"username"`
	Email               string `json:"email"`
	Name                string `json:"name"`
	ForceRandomPassword bool   `json:"force_random_password"`
	SkipConfirmation    bool   `json:"skip_confirmation"`
}

type GetUserRequest struct {
	AdminAccessToken string
	Email            string
}

type DeleteUserRequest struct {
	AdminAccessToken string
	Id               int64 `json:"id"`
}

type CreateUserResponse struct {
	Id       int64     `json:"id"`
	Username string    `json:"username"`
	Email    string    `json:"email"`
	State    UserState `json:"state"`
}

// Create<PERSON><PERSON> creates a new user in GitLab.
// This function requires administrator privileges to create new users.
// The user can be created with either a random password or a specified one.
//
// Parameters:
//   - ctx: Context for the operation
//   - request: CreateUserRequest containing user details and admin access token
//
// Returns:
//   - *CreateUserResponse: The created user information if successful
//   - error: Any error that occurred during user creation
//
// Reference: https://docs.gitlab.com/ee/api/users.html#user-creation
func (i *impl) CreateUser(ctx context.Context, request CreateUserRequest) (*CreateUserResponse, error) {
	c, span := oteltrace.Tracer.Start(ctx, "gateways.gitlab.CreateUser")
	defer span.End()

	url := fmt.Sprintf("%s/users", i.apiUrl)
	resp, err := i.newRequest(c, http.MethodPost, url, request, request.AdminAccessToken)
	if err != nil {
		span.SetStatus(codes.Error, "failed to create gitlab user")
		span.RecordError(err)
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusCreated {
		user := &CreateUserResponse{}
		err = json.NewDecoder(resp.Body).Decode(user)
		if err != nil {
			span.SetStatus(codes.Error, "failed to decode create gitlab user response body")
			span.RecordError(err)
			return nil, err
		}

		span.AddEvent(fmt.Sprintf("successfully create gitlab user %s", request.Email))
		span.SetStatus(codes.Ok, fmt.Sprintf("successfully create gitlab user %s", request.Email))
		return user, nil
	}

	var mapResp map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&mapResp); err != nil {
		span.SetStatus(codes.Error, "failed to decode create gitlab user response body")
		span.RecordError(err)
		return nil, err
	}

	err = fmt.Errorf("%v: %v", handleErrors(resp.StatusCode), mapResp)
	span.SetStatus(codes.Error, err.Error())
	span.RecordError(err)
	return nil, err
}

// DeleteUser permanently removes a user from GitLab.
// This function requires administrator privileges and performs a hard delete of the user.
//
// Parameters:
//   - ctx: Context for the operation
//   - request: DeleteUserRequest containing user ID and admin access token
//
// Returns:
//   - error: Any error that occurred during user deletion
//
// Reference: https://docs.gitlab.com/ee/api/users.html#delete-user
func (i *impl) DeleteUser(ctx context.Context, request DeleteUserRequest) error {
	c, span := oteltrace.Tracer.Start(ctx, "gateways.gitlab.DeleteUser")
	defer span.End()

	params := url.Values{}
	params.Add("hard_delete", "true")
	url := fmt.Sprintf("%s/users/%d?%s", i.apiUrl, request.Id, params.Encode())
	resp, err := i.newRequest(c, http.MethodDelete, url, nil, request.AdminAccessToken)
	if err != nil {
		span.SetStatus(codes.Error, "failed to delete gitlab user")
		span.RecordError(err)
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusNoContent {
		// User successfully deleted; no content to return
		span.AddEvent(fmt.Sprintf("successfully deleted gitlab user %d", request.Id))
		span.SetStatus(codes.Ok, fmt.Sprintf("successfully deleted gitlab user %d", request.Id))
		return nil
	}

	var mapResp map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&mapResp); err != nil {
		span.SetStatus(codes.Error, "failed to decode delete gitlab user response body")
		span.RecordError(err)
		return err
	}

	err = fmt.Errorf("%v: %v", handleErrors(resp.StatusCode), mapResp)
	span.SetStatus(codes.Error, "failed to decode delete gitlab user response body")
	span.RecordError(err)
	return err
}

// GetUser retrieves information about GitLab users.
// This function can search for users by email address.
//
// Parameters:
//   - ctx: Context for the operation
//   - request: GetUserRequest containing search criteria and admin access token
//
// Returns:
//   - []CreateUserResponse: List of matching users if successful
//   - error: Any error that occurred during user retrieval
//
// Reference: https://docs.gitlab.com/ee/api/users.html#list-users
func (i *impl) GetUser(ctx context.Context, request GetUserRequest) ([]CreateUserResponse, error) {
	params := url.Values{}
	if request.Email != "" {
		params.Add("search", request.Email)
	}
	url := fmt.Sprintf("%s/users?%s", i.apiUrl, params.Encode())
	resp, err := i.newRequest(ctx, http.MethodGet, url, nil, request.AdminAccessToken)
	if err != nil {
		return nil, err
	}

	defer resp.Body.Close()
	if resp.StatusCode == http.StatusOK {
		var output []CreateUserResponse
		err = json.NewDecoder(resp.Body).Decode(&output)
		if err != nil {
			return nil, err
		}

		return output, nil
	}

	var mapResp map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&mapResp); err != nil {
		return nil, err
	}

	err = handleErrors(resp.StatusCode)
	return nil, fmt.Errorf("%v: %v", err, mapResp)
}
