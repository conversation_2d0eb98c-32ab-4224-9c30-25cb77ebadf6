// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	gitlab "api-server/internal/gateways/gitlab"
	context "context"
	http "net/http"

	mock "github.com/stretchr/testify/mock"
)

// MockGitlabClient is an autogenerated mock type for the GitlabClient type
type MockGitlabClient struct {
	mock.Mock
}

type MockGitlabClient_Expecter struct {
	mock *mock.Mock
}

func (_m *MockGitlabClient) EXPECT() *MockGitlabClient_Expecter {
	return &MockGitlabClient_Expecter{mock: &_m.Mock}
}

// AddUserToGroup provides a mock function with given fields: ctx, input
func (_m *MockGitlabClient) AddUserToGroup(ctx context.Context, input gitlab.AddUserToGroupRequest) (*gitlab.AddUserToGroupResponse, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for AddUserToGroup")
	}

	var r0 *gitlab.AddUserToGroupResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.AddUserToGroupRequest) (*gitlab.AddUserToGroupResponse, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.AddUserToGroupRequest) *gitlab.AddUserToGroupResponse); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*gitlab.AddUserToGroupResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, gitlab.AddUserToGroupRequest) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockGitlabClient_AddUserToGroup_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AddUserToGroup'
type MockGitlabClient_AddUserToGroup_Call struct {
	*mock.Call
}

// AddUserToGroup is a helper method to define mock.On call
//   - ctx context.Context
//   - input gitlab.AddUserToGroupRequest
func (_e *MockGitlabClient_Expecter) AddUserToGroup(ctx interface{}, input interface{}) *MockGitlabClient_AddUserToGroup_Call {
	return &MockGitlabClient_AddUserToGroup_Call{Call: _e.mock.On("AddUserToGroup", ctx, input)}
}

func (_c *MockGitlabClient_AddUserToGroup_Call) Run(run func(ctx context.Context, input gitlab.AddUserToGroupRequest)) *MockGitlabClient_AddUserToGroup_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(gitlab.AddUserToGroupRequest))
	})
	return _c
}

func (_c *MockGitlabClient_AddUserToGroup_Call) Return(_a0 *gitlab.AddUserToGroupResponse, _a1 error) *MockGitlabClient_AddUserToGroup_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockGitlabClient_AddUserToGroup_Call) RunAndReturn(run func(context.Context, gitlab.AddUserToGroupRequest) (*gitlab.AddUserToGroupResponse, error)) *MockGitlabClient_AddUserToGroup_Call {
	_c.Call.Return(run)
	return _c
}

// AddUserToProject provides a mock function with given fields: ctx, input
func (_m *MockGitlabClient) AddUserToProject(ctx context.Context, input gitlab.AddUserToProjectRequest) (*gitlab.AddUserToProjectResponse, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for AddUserToProject")
	}

	var r0 *gitlab.AddUserToProjectResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.AddUserToProjectRequest) (*gitlab.AddUserToProjectResponse, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.AddUserToProjectRequest) *gitlab.AddUserToProjectResponse); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*gitlab.AddUserToProjectResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, gitlab.AddUserToProjectRequest) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockGitlabClient_AddUserToProject_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AddUserToProject'
type MockGitlabClient_AddUserToProject_Call struct {
	*mock.Call
}

// AddUserToProject is a helper method to define mock.On call
//   - ctx context.Context
//   - input gitlab.AddUserToProjectRequest
func (_e *MockGitlabClient_Expecter) AddUserToProject(ctx interface{}, input interface{}) *MockGitlabClient_AddUserToProject_Call {
	return &MockGitlabClient_AddUserToProject_Call{Call: _e.mock.On("AddUserToProject", ctx, input)}
}

func (_c *MockGitlabClient_AddUserToProject_Call) Run(run func(ctx context.Context, input gitlab.AddUserToProjectRequest)) *MockGitlabClient_AddUserToProject_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(gitlab.AddUserToProjectRequest))
	})
	return _c
}

func (_c *MockGitlabClient_AddUserToProject_Call) Return(_a0 *gitlab.AddUserToProjectResponse, _a1 error) *MockGitlabClient_AddUserToProject_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockGitlabClient_AddUserToProject_Call) RunAndReturn(run func(context.Context, gitlab.AddUserToProjectRequest) (*gitlab.AddUserToProjectResponse, error)) *MockGitlabClient_AddUserToProject_Call {
	_c.Call.Return(run)
	return _c
}

// CreateGroup provides a mock function with given fields: ctx, input
func (_m *MockGitlabClient) CreateGroup(ctx context.Context, input gitlab.CreateGroupRequest) (*gitlab.GitlabGroup, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for CreateGroup")
	}

	var r0 *gitlab.GitlabGroup
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.CreateGroupRequest) (*gitlab.GitlabGroup, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.CreateGroupRequest) *gitlab.GitlabGroup); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*gitlab.GitlabGroup)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, gitlab.CreateGroupRequest) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockGitlabClient_CreateGroup_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateGroup'
type MockGitlabClient_CreateGroup_Call struct {
	*mock.Call
}

// CreateGroup is a helper method to define mock.On call
//   - ctx context.Context
//   - input gitlab.CreateGroupRequest
func (_e *MockGitlabClient_Expecter) CreateGroup(ctx interface{}, input interface{}) *MockGitlabClient_CreateGroup_Call {
	return &MockGitlabClient_CreateGroup_Call{Call: _e.mock.On("CreateGroup", ctx, input)}
}

func (_c *MockGitlabClient_CreateGroup_Call) Run(run func(ctx context.Context, input gitlab.CreateGroupRequest)) *MockGitlabClient_CreateGroup_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(gitlab.CreateGroupRequest))
	})
	return _c
}

func (_c *MockGitlabClient_CreateGroup_Call) Return(_a0 *gitlab.GitlabGroup, _a1 error) *MockGitlabClient_CreateGroup_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockGitlabClient_CreateGroup_Call) RunAndReturn(run func(context.Context, gitlab.CreateGroupRequest) (*gitlab.GitlabGroup, error)) *MockGitlabClient_CreateGroup_Call {
	_c.Call.Return(run)
	return _c
}

// CreatePersonalAccessToken provides a mock function with given fields: ctx, input
func (_m *MockGitlabClient) CreatePersonalAccessToken(ctx context.Context, input gitlab.CreatePersonalAccessTokenRequest) (*gitlab.CreatePersonalAccessTokenResponse, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for CreatePersonalAccessToken")
	}

	var r0 *gitlab.CreatePersonalAccessTokenResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.CreatePersonalAccessTokenRequest) (*gitlab.CreatePersonalAccessTokenResponse, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.CreatePersonalAccessTokenRequest) *gitlab.CreatePersonalAccessTokenResponse); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*gitlab.CreatePersonalAccessTokenResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, gitlab.CreatePersonalAccessTokenRequest) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockGitlabClient_CreatePersonalAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreatePersonalAccessToken'
type MockGitlabClient_CreatePersonalAccessToken_Call struct {
	*mock.Call
}

// CreatePersonalAccessToken is a helper method to define mock.On call
//   - ctx context.Context
//   - input gitlab.CreatePersonalAccessTokenRequest
func (_e *MockGitlabClient_Expecter) CreatePersonalAccessToken(ctx interface{}, input interface{}) *MockGitlabClient_CreatePersonalAccessToken_Call {
	return &MockGitlabClient_CreatePersonalAccessToken_Call{Call: _e.mock.On("CreatePersonalAccessToken", ctx, input)}
}

func (_c *MockGitlabClient_CreatePersonalAccessToken_Call) Run(run func(ctx context.Context, input gitlab.CreatePersonalAccessTokenRequest)) *MockGitlabClient_CreatePersonalAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(gitlab.CreatePersonalAccessTokenRequest))
	})
	return _c
}

func (_c *MockGitlabClient_CreatePersonalAccessToken_Call) Return(_a0 *gitlab.CreatePersonalAccessTokenResponse, _a1 error) *MockGitlabClient_CreatePersonalAccessToken_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockGitlabClient_CreatePersonalAccessToken_Call) RunAndReturn(run func(context.Context, gitlab.CreatePersonalAccessTokenRequest) (*gitlab.CreatePersonalAccessTokenResponse, error)) *MockGitlabClient_CreatePersonalAccessToken_Call {
	_c.Call.Return(run)
	return _c
}

// CreateProject provides a mock function with given fields: ctx, input
func (_m *MockGitlabClient) CreateProject(ctx context.Context, input gitlab.CreateProjectRequest) (*gitlab.CreateProjectResponse, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for CreateProject")
	}

	var r0 *gitlab.CreateProjectResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.CreateProjectRequest) (*gitlab.CreateProjectResponse, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.CreateProjectRequest) *gitlab.CreateProjectResponse); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*gitlab.CreateProjectResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, gitlab.CreateProjectRequest) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockGitlabClient_CreateProject_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateProject'
type MockGitlabClient_CreateProject_Call struct {
	*mock.Call
}

// CreateProject is a helper method to define mock.On call
//   - ctx context.Context
//   - input gitlab.CreateProjectRequest
func (_e *MockGitlabClient_Expecter) CreateProject(ctx interface{}, input interface{}) *MockGitlabClient_CreateProject_Call {
	return &MockGitlabClient_CreateProject_Call{Call: _e.mock.On("CreateProject", ctx, input)}
}

func (_c *MockGitlabClient_CreateProject_Call) Run(run func(ctx context.Context, input gitlab.CreateProjectRequest)) *MockGitlabClient_CreateProject_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(gitlab.CreateProjectRequest))
	})
	return _c
}

func (_c *MockGitlabClient_CreateProject_Call) Return(_a0 *gitlab.CreateProjectResponse, _a1 error) *MockGitlabClient_CreateProject_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockGitlabClient_CreateProject_Call) RunAndReturn(run func(context.Context, gitlab.CreateProjectRequest) (*gitlab.CreateProjectResponse, error)) *MockGitlabClient_CreateProject_Call {
	_c.Call.Return(run)
	return _c
}

// CreateProjectCommit provides a mock function with given fields: ctx, input
func (_m *MockGitlabClient) CreateProjectCommit(ctx context.Context, input gitlab.CreateProjectCommitRequest) (*gitlab.RepositoryCommit, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for CreateProjectCommit")
	}

	var r0 *gitlab.RepositoryCommit
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.CreateProjectCommitRequest) (*gitlab.RepositoryCommit, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.CreateProjectCommitRequest) *gitlab.RepositoryCommit); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*gitlab.RepositoryCommit)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, gitlab.CreateProjectCommitRequest) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockGitlabClient_CreateProjectCommit_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateProjectCommit'
type MockGitlabClient_CreateProjectCommit_Call struct {
	*mock.Call
}

// CreateProjectCommit is a helper method to define mock.On call
//   - ctx context.Context
//   - input gitlab.CreateProjectCommitRequest
func (_e *MockGitlabClient_Expecter) CreateProjectCommit(ctx interface{}, input interface{}) *MockGitlabClient_CreateProjectCommit_Call {
	return &MockGitlabClient_CreateProjectCommit_Call{Call: _e.mock.On("CreateProjectCommit", ctx, input)}
}

func (_c *MockGitlabClient_CreateProjectCommit_Call) Run(run func(ctx context.Context, input gitlab.CreateProjectCommitRequest)) *MockGitlabClient_CreateProjectCommit_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(gitlab.CreateProjectCommitRequest))
	})
	return _c
}

func (_c *MockGitlabClient_CreateProjectCommit_Call) Return(_a0 *gitlab.RepositoryCommit, _a1 error) *MockGitlabClient_CreateProjectCommit_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockGitlabClient_CreateProjectCommit_Call) RunAndReturn(run func(context.Context, gitlab.CreateProjectCommitRequest) (*gitlab.RepositoryCommit, error)) *MockGitlabClient_CreateProjectCommit_Call {
	_c.Call.Return(run)
	return _c
}

// CreateProjectFromTemplate provides a mock function with given fields: ctx, input
func (_m *MockGitlabClient) CreateProjectFromTemplate(ctx context.Context, input gitlab.CreateProjectRequest) (*gitlab.CreateProjectResponse, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for CreateProjectFromTemplate")
	}

	var r0 *gitlab.CreateProjectResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.CreateProjectRequest) (*gitlab.CreateProjectResponse, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.CreateProjectRequest) *gitlab.CreateProjectResponse); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*gitlab.CreateProjectResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, gitlab.CreateProjectRequest) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockGitlabClient_CreateProjectFromTemplate_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateProjectFromTemplate'
type MockGitlabClient_CreateProjectFromTemplate_Call struct {
	*mock.Call
}

// CreateProjectFromTemplate is a helper method to define mock.On call
//   - ctx context.Context
//   - input gitlab.CreateProjectRequest
func (_e *MockGitlabClient_Expecter) CreateProjectFromTemplate(ctx interface{}, input interface{}) *MockGitlabClient_CreateProjectFromTemplate_Call {
	return &MockGitlabClient_CreateProjectFromTemplate_Call{Call: _e.mock.On("CreateProjectFromTemplate", ctx, input)}
}

func (_c *MockGitlabClient_CreateProjectFromTemplate_Call) Run(run func(ctx context.Context, input gitlab.CreateProjectRequest)) *MockGitlabClient_CreateProjectFromTemplate_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(gitlab.CreateProjectRequest))
	})
	return _c
}

func (_c *MockGitlabClient_CreateProjectFromTemplate_Call) Return(_a0 *gitlab.CreateProjectResponse, _a1 error) *MockGitlabClient_CreateProjectFromTemplate_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockGitlabClient_CreateProjectFromTemplate_Call) RunAndReturn(run func(context.Context, gitlab.CreateProjectRequest) (*gitlab.CreateProjectResponse, error)) *MockGitlabClient_CreateProjectFromTemplate_Call {
	_c.Call.Return(run)
	return _c
}

// CreateRepoPersonalAccessToken provides a mock function with given fields: ctx, input
func (_m *MockGitlabClient) CreateRepoPersonalAccessToken(ctx context.Context, input gitlab.CreateProjectAccessTokenRequest) (*gitlab.CreateProjectAccessTokenResponse, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for CreateRepoPersonalAccessToken")
	}

	var r0 *gitlab.CreateProjectAccessTokenResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.CreateProjectAccessTokenRequest) (*gitlab.CreateProjectAccessTokenResponse, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.CreateProjectAccessTokenRequest) *gitlab.CreateProjectAccessTokenResponse); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*gitlab.CreateProjectAccessTokenResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, gitlab.CreateProjectAccessTokenRequest) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockGitlabClient_CreateRepoPersonalAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateRepoPersonalAccessToken'
type MockGitlabClient_CreateRepoPersonalAccessToken_Call struct {
	*mock.Call
}

// CreateRepoPersonalAccessToken is a helper method to define mock.On call
//   - ctx context.Context
//   - input gitlab.CreateProjectAccessTokenRequest
func (_e *MockGitlabClient_Expecter) CreateRepoPersonalAccessToken(ctx interface{}, input interface{}) *MockGitlabClient_CreateRepoPersonalAccessToken_Call {
	return &MockGitlabClient_CreateRepoPersonalAccessToken_Call{Call: _e.mock.On("CreateRepoPersonalAccessToken", ctx, input)}
}

func (_c *MockGitlabClient_CreateRepoPersonalAccessToken_Call) Run(run func(ctx context.Context, input gitlab.CreateProjectAccessTokenRequest)) *MockGitlabClient_CreateRepoPersonalAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(gitlab.CreateProjectAccessTokenRequest))
	})
	return _c
}

func (_c *MockGitlabClient_CreateRepoPersonalAccessToken_Call) Return(_a0 *gitlab.CreateProjectAccessTokenResponse, _a1 error) *MockGitlabClient_CreateRepoPersonalAccessToken_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockGitlabClient_CreateRepoPersonalAccessToken_Call) RunAndReturn(run func(context.Context, gitlab.CreateProjectAccessTokenRequest) (*gitlab.CreateProjectAccessTokenResponse, error)) *MockGitlabClient_CreateRepoPersonalAccessToken_Call {
	_c.Call.Return(run)
	return _c
}

// CreateSSHKey provides a mock function with given fields: ctx, input
func (_m *MockGitlabClient) CreateSSHKey(ctx context.Context, input gitlab.CreateSSHKeyRequest) (*gitlab.CreateSSHKeyResponse, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for CreateSSHKey")
	}

	var r0 *gitlab.CreateSSHKeyResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.CreateSSHKeyRequest) (*gitlab.CreateSSHKeyResponse, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.CreateSSHKeyRequest) *gitlab.CreateSSHKeyResponse); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*gitlab.CreateSSHKeyResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, gitlab.CreateSSHKeyRequest) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockGitlabClient_CreateSSHKey_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateSSHKey'
type MockGitlabClient_CreateSSHKey_Call struct {
	*mock.Call
}

// CreateSSHKey is a helper method to define mock.On call
//   - ctx context.Context
//   - input gitlab.CreateSSHKeyRequest
func (_e *MockGitlabClient_Expecter) CreateSSHKey(ctx interface{}, input interface{}) *MockGitlabClient_CreateSSHKey_Call {
	return &MockGitlabClient_CreateSSHKey_Call{Call: _e.mock.On("CreateSSHKey", ctx, input)}
}

func (_c *MockGitlabClient_CreateSSHKey_Call) Run(run func(ctx context.Context, input gitlab.CreateSSHKeyRequest)) *MockGitlabClient_CreateSSHKey_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(gitlab.CreateSSHKeyRequest))
	})
	return _c
}

func (_c *MockGitlabClient_CreateSSHKey_Call) Return(_a0 *gitlab.CreateSSHKeyResponse, _a1 error) *MockGitlabClient_CreateSSHKey_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockGitlabClient_CreateSSHKey_Call) RunAndReturn(run func(context.Context, gitlab.CreateSSHKeyRequest) (*gitlab.CreateSSHKeyResponse, error)) *MockGitlabClient_CreateSSHKey_Call {
	_c.Call.Return(run)
	return _c
}

// CreateUser provides a mock function with given fields: ctx, request
func (_m *MockGitlabClient) CreateUser(ctx context.Context, request gitlab.CreateUserRequest) (*gitlab.CreateUserResponse, error) {
	ret := _m.Called(ctx, request)

	if len(ret) == 0 {
		panic("no return value specified for CreateUser")
	}

	var r0 *gitlab.CreateUserResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.CreateUserRequest) (*gitlab.CreateUserResponse, error)); ok {
		return rf(ctx, request)
	}
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.CreateUserRequest) *gitlab.CreateUserResponse); ok {
		r0 = rf(ctx, request)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*gitlab.CreateUserResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, gitlab.CreateUserRequest) error); ok {
		r1 = rf(ctx, request)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockGitlabClient_CreateUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateUser'
type MockGitlabClient_CreateUser_Call struct {
	*mock.Call
}

// CreateUser is a helper method to define mock.On call
//   - ctx context.Context
//   - request gitlab.CreateUserRequest
func (_e *MockGitlabClient_Expecter) CreateUser(ctx interface{}, request interface{}) *MockGitlabClient_CreateUser_Call {
	return &MockGitlabClient_CreateUser_Call{Call: _e.mock.On("CreateUser", ctx, request)}
}

func (_c *MockGitlabClient_CreateUser_Call) Run(run func(ctx context.Context, request gitlab.CreateUserRequest)) *MockGitlabClient_CreateUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(gitlab.CreateUserRequest))
	})
	return _c
}

func (_c *MockGitlabClient_CreateUser_Call) Return(_a0 *gitlab.CreateUserResponse, _a1 error) *MockGitlabClient_CreateUser_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockGitlabClient_CreateUser_Call) RunAndReturn(run func(context.Context, gitlab.CreateUserRequest) (*gitlab.CreateUserResponse, error)) *MockGitlabClient_CreateUser_Call {
	_c.Call.Return(run)
	return _c
}

// CreateUserGroup provides a mock function with given fields: ctx, gitUserID, username, path, token, parentID
func (_m *MockGitlabClient) CreateUserGroup(ctx context.Context, gitUserID int64, username string, path string, token string, parentID int64) (int64, error) {
	ret := _m.Called(ctx, gitUserID, username, path, token, parentID)

	if len(ret) == 0 {
		panic("no return value specified for CreateUserGroup")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64, string, string, string, int64) (int64, error)); ok {
		return rf(ctx, gitUserID, username, path, token, parentID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64, string, string, string, int64) int64); ok {
		r0 = rf(ctx, gitUserID, username, path, token, parentID)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64, string, string, string, int64) error); ok {
		r1 = rf(ctx, gitUserID, username, path, token, parentID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockGitlabClient_CreateUserGroup_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateUserGroup'
type MockGitlabClient_CreateUserGroup_Call struct {
	*mock.Call
}

// CreateUserGroup is a helper method to define mock.On call
//   - ctx context.Context
//   - gitUserID int64
//   - username string
//   - path string
//   - token string
//   - parentID int64
func (_e *MockGitlabClient_Expecter) CreateUserGroup(ctx interface{}, gitUserID interface{}, username interface{}, path interface{}, token interface{}, parentID interface{}) *MockGitlabClient_CreateUserGroup_Call {
	return &MockGitlabClient_CreateUserGroup_Call{Call: _e.mock.On("CreateUserGroup", ctx, gitUserID, username, path, token, parentID)}
}

func (_c *MockGitlabClient_CreateUserGroup_Call) Run(run func(ctx context.Context, gitUserID int64, username string, path string, token string, parentID int64)) *MockGitlabClient_CreateUserGroup_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64), args[2].(string), args[3].(string), args[4].(string), args[5].(int64))
	})
	return _c
}

func (_c *MockGitlabClient_CreateUserGroup_Call) Return(_a0 int64, _a1 error) *MockGitlabClient_CreateUserGroup_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockGitlabClient_CreateUserGroup_Call) RunAndReturn(run func(context.Context, int64, string, string, string, int64) (int64, error)) *MockGitlabClient_CreateUserGroup_Call {
	_c.Call.Return(run)
	return _c
}

// CreateWebhook provides a mock function with given fields: ctx, input
func (_m *MockGitlabClient) CreateWebhook(ctx context.Context, input gitlab.CreateProjectWebhookRequest) error {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for CreateWebhook")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.CreateProjectWebhookRequest) error); ok {
		r0 = rf(ctx, input)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockGitlabClient_CreateWebhook_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateWebhook'
type MockGitlabClient_CreateWebhook_Call struct {
	*mock.Call
}

// CreateWebhook is a helper method to define mock.On call
//   - ctx context.Context
//   - input gitlab.CreateProjectWebhookRequest
func (_e *MockGitlabClient_Expecter) CreateWebhook(ctx interface{}, input interface{}) *MockGitlabClient_CreateWebhook_Call {
	return &MockGitlabClient_CreateWebhook_Call{Call: _e.mock.On("CreateWebhook", ctx, input)}
}

func (_c *MockGitlabClient_CreateWebhook_Call) Run(run func(ctx context.Context, input gitlab.CreateProjectWebhookRequest)) *MockGitlabClient_CreateWebhook_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(gitlab.CreateProjectWebhookRequest))
	})
	return _c
}

func (_c *MockGitlabClient_CreateWebhook_Call) Return(_a0 error) *MockGitlabClient_CreateWebhook_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockGitlabClient_CreateWebhook_Call) RunAndReturn(run func(context.Context, gitlab.CreateProjectWebhookRequest) error) *MockGitlabClient_CreateWebhook_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteGroup provides a mock function with given fields: ctx, input
func (_m *MockGitlabClient) DeleteGroup(ctx context.Context, input gitlab.DeleteOrganizationRequest) error {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for DeleteGroup")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.DeleteOrganizationRequest) error); ok {
		r0 = rf(ctx, input)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockGitlabClient_DeleteGroup_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteGroup'
type MockGitlabClient_DeleteGroup_Call struct {
	*mock.Call
}

// DeleteGroup is a helper method to define mock.On call
//   - ctx context.Context
//   - input gitlab.DeleteOrganizationRequest
func (_e *MockGitlabClient_Expecter) DeleteGroup(ctx interface{}, input interface{}) *MockGitlabClient_DeleteGroup_Call {
	return &MockGitlabClient_DeleteGroup_Call{Call: _e.mock.On("DeleteGroup", ctx, input)}
}

func (_c *MockGitlabClient_DeleteGroup_Call) Run(run func(ctx context.Context, input gitlab.DeleteOrganizationRequest)) *MockGitlabClient_DeleteGroup_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(gitlab.DeleteOrganizationRequest))
	})
	return _c
}

func (_c *MockGitlabClient_DeleteGroup_Call) Return(_a0 error) *MockGitlabClient_DeleteGroup_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockGitlabClient_DeleteGroup_Call) RunAndReturn(run func(context.Context, gitlab.DeleteOrganizationRequest) error) *MockGitlabClient_DeleteGroup_Call {
	_c.Call.Return(run)
	return _c
}

// DeletePersonalAccessToken provides a mock function with given fields: ctx, accessToken
func (_m *MockGitlabClient) DeletePersonalAccessToken(ctx context.Context, accessToken string) error {
	ret := _m.Called(ctx, accessToken)

	if len(ret) == 0 {
		panic("no return value specified for DeletePersonalAccessToken")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string) error); ok {
		r0 = rf(ctx, accessToken)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockGitlabClient_DeletePersonalAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeletePersonalAccessToken'
type MockGitlabClient_DeletePersonalAccessToken_Call struct {
	*mock.Call
}

// DeletePersonalAccessToken is a helper method to define mock.On call
//   - ctx context.Context
//   - accessToken string
func (_e *MockGitlabClient_Expecter) DeletePersonalAccessToken(ctx interface{}, accessToken interface{}) *MockGitlabClient_DeletePersonalAccessToken_Call {
	return &MockGitlabClient_DeletePersonalAccessToken_Call{Call: _e.mock.On("DeletePersonalAccessToken", ctx, accessToken)}
}

func (_c *MockGitlabClient_DeletePersonalAccessToken_Call) Run(run func(ctx context.Context, accessToken string)) *MockGitlabClient_DeletePersonalAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockGitlabClient_DeletePersonalAccessToken_Call) Return(_a0 error) *MockGitlabClient_DeletePersonalAccessToken_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockGitlabClient_DeletePersonalAccessToken_Call) RunAndReturn(run func(context.Context, string) error) *MockGitlabClient_DeletePersonalAccessToken_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteProject provides a mock function with given fields: ctx, input
func (_m *MockGitlabClient) DeleteProject(ctx context.Context, input gitlab.DeleteProjectRequest) error {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for DeleteProject")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.DeleteProjectRequest) error); ok {
		r0 = rf(ctx, input)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockGitlabClient_DeleteProject_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteProject'
type MockGitlabClient_DeleteProject_Call struct {
	*mock.Call
}

// DeleteProject is a helper method to define mock.On call
//   - ctx context.Context
//   - input gitlab.DeleteProjectRequest
func (_e *MockGitlabClient_Expecter) DeleteProject(ctx interface{}, input interface{}) *MockGitlabClient_DeleteProject_Call {
	return &MockGitlabClient_DeleteProject_Call{Call: _e.mock.On("DeleteProject", ctx, input)}
}

func (_c *MockGitlabClient_DeleteProject_Call) Run(run func(ctx context.Context, input gitlab.DeleteProjectRequest)) *MockGitlabClient_DeleteProject_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(gitlab.DeleteProjectRequest))
	})
	return _c
}

func (_c *MockGitlabClient_DeleteProject_Call) Return(_a0 error) *MockGitlabClient_DeleteProject_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockGitlabClient_DeleteProject_Call) RunAndReturn(run func(context.Context, gitlab.DeleteProjectRequest) error) *MockGitlabClient_DeleteProject_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteRepoPersonalAccessToken provides a mock function with given fields: ctx, input
func (_m *MockGitlabClient) DeleteRepoPersonalAccessToken(ctx context.Context, input gitlab.DeleteProjectAccessTokenRequest) error {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for DeleteRepoPersonalAccessToken")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.DeleteProjectAccessTokenRequest) error); ok {
		r0 = rf(ctx, input)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockGitlabClient_DeleteRepoPersonalAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteRepoPersonalAccessToken'
type MockGitlabClient_DeleteRepoPersonalAccessToken_Call struct {
	*mock.Call
}

// DeleteRepoPersonalAccessToken is a helper method to define mock.On call
//   - ctx context.Context
//   - input gitlab.DeleteProjectAccessTokenRequest
func (_e *MockGitlabClient_Expecter) DeleteRepoPersonalAccessToken(ctx interface{}, input interface{}) *MockGitlabClient_DeleteRepoPersonalAccessToken_Call {
	return &MockGitlabClient_DeleteRepoPersonalAccessToken_Call{Call: _e.mock.On("DeleteRepoPersonalAccessToken", ctx, input)}
}

func (_c *MockGitlabClient_DeleteRepoPersonalAccessToken_Call) Run(run func(ctx context.Context, input gitlab.DeleteProjectAccessTokenRequest)) *MockGitlabClient_DeleteRepoPersonalAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(gitlab.DeleteProjectAccessTokenRequest))
	})
	return _c
}

func (_c *MockGitlabClient_DeleteRepoPersonalAccessToken_Call) Return(_a0 error) *MockGitlabClient_DeleteRepoPersonalAccessToken_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockGitlabClient_DeleteRepoPersonalAccessToken_Call) RunAndReturn(run func(context.Context, gitlab.DeleteProjectAccessTokenRequest) error) *MockGitlabClient_DeleteRepoPersonalAccessToken_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteSSHKey provides a mock function with given fields: ctx, input
func (_m *MockGitlabClient) DeleteSSHKey(ctx context.Context, input gitlab.DeleteSSHKeyRequest) error {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for DeleteSSHKey")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.DeleteSSHKeyRequest) error); ok {
		r0 = rf(ctx, input)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockGitlabClient_DeleteSSHKey_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteSSHKey'
type MockGitlabClient_DeleteSSHKey_Call struct {
	*mock.Call
}

// DeleteSSHKey is a helper method to define mock.On call
//   - ctx context.Context
//   - input gitlab.DeleteSSHKeyRequest
func (_e *MockGitlabClient_Expecter) DeleteSSHKey(ctx interface{}, input interface{}) *MockGitlabClient_DeleteSSHKey_Call {
	return &MockGitlabClient_DeleteSSHKey_Call{Call: _e.mock.On("DeleteSSHKey", ctx, input)}
}

func (_c *MockGitlabClient_DeleteSSHKey_Call) Run(run func(ctx context.Context, input gitlab.DeleteSSHKeyRequest)) *MockGitlabClient_DeleteSSHKey_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(gitlab.DeleteSSHKeyRequest))
	})
	return _c
}

func (_c *MockGitlabClient_DeleteSSHKey_Call) Return(_a0 error) *MockGitlabClient_DeleteSSHKey_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockGitlabClient_DeleteSSHKey_Call) RunAndReturn(run func(context.Context, gitlab.DeleteSSHKeyRequest) error) *MockGitlabClient_DeleteSSHKey_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteUser provides a mock function with given fields: ctx, request
func (_m *MockGitlabClient) DeleteUser(ctx context.Context, request gitlab.DeleteUserRequest) error {
	ret := _m.Called(ctx, request)

	if len(ret) == 0 {
		panic("no return value specified for DeleteUser")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.DeleteUserRequest) error); ok {
		r0 = rf(ctx, request)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockGitlabClient_DeleteUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteUser'
type MockGitlabClient_DeleteUser_Call struct {
	*mock.Call
}

// DeleteUser is a helper method to define mock.On call
//   - ctx context.Context
//   - request gitlab.DeleteUserRequest
func (_e *MockGitlabClient_Expecter) DeleteUser(ctx interface{}, request interface{}) *MockGitlabClient_DeleteUser_Call {
	return &MockGitlabClient_DeleteUser_Call{Call: _e.mock.On("DeleteUser", ctx, request)}
}

func (_c *MockGitlabClient_DeleteUser_Call) Run(run func(ctx context.Context, request gitlab.DeleteUserRequest)) *MockGitlabClient_DeleteUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(gitlab.DeleteUserRequest))
	})
	return _c
}

func (_c *MockGitlabClient_DeleteUser_Call) Return(_a0 error) *MockGitlabClient_DeleteUser_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockGitlabClient_DeleteUser_Call) RunAndReturn(run func(context.Context, gitlab.DeleteUserRequest) error) *MockGitlabClient_DeleteUser_Call {
	_c.Call.Return(run)
	return _c
}

// DownloadProject provides a mock function with given fields: ctx, input
func (_m *MockGitlabClient) DownloadProject(ctx context.Context, input gitlab.DownloadProjectRequest) (*http.Response, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for DownloadProject")
	}

	var r0 *http.Response
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.DownloadProjectRequest) (*http.Response, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.DownloadProjectRequest) *http.Response); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*http.Response)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, gitlab.DownloadProjectRequest) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockGitlabClient_DownloadProject_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DownloadProject'
type MockGitlabClient_DownloadProject_Call struct {
	*mock.Call
}

// DownloadProject is a helper method to define mock.On call
//   - ctx context.Context
//   - input gitlab.DownloadProjectRequest
func (_e *MockGitlabClient_Expecter) DownloadProject(ctx interface{}, input interface{}) *MockGitlabClient_DownloadProject_Call {
	return &MockGitlabClient_DownloadProject_Call{Call: _e.mock.On("DownloadProject", ctx, input)}
}

func (_c *MockGitlabClient_DownloadProject_Call) Run(run func(ctx context.Context, input gitlab.DownloadProjectRequest)) *MockGitlabClient_DownloadProject_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(gitlab.DownloadProjectRequest))
	})
	return _c
}

func (_c *MockGitlabClient_DownloadProject_Call) Return(_a0 *http.Response, _a1 error) *MockGitlabClient_DownloadProject_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockGitlabClient_DownloadProject_Call) RunAndReturn(run func(context.Context, gitlab.DownloadProjectRequest) (*http.Response, error)) *MockGitlabClient_DownloadProject_Call {
	_c.Call.Return(run)
	return _c
}

// EditUserInGroup provides a mock function with given fields: ctx, input
func (_m *MockGitlabClient) EditUserInGroup(ctx context.Context, input gitlab.EditUserInGroupRequest) (*gitlab.EditUserInGroupResponse, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for EditUserInGroup")
	}

	var r0 *gitlab.EditUserInGroupResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.EditUserInGroupRequest) (*gitlab.EditUserInGroupResponse, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.EditUserInGroupRequest) *gitlab.EditUserInGroupResponse); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*gitlab.EditUserInGroupResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, gitlab.EditUserInGroupRequest) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockGitlabClient_EditUserInGroup_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'EditUserInGroup'
type MockGitlabClient_EditUserInGroup_Call struct {
	*mock.Call
}

// EditUserInGroup is a helper method to define mock.On call
//   - ctx context.Context
//   - input gitlab.EditUserInGroupRequest
func (_e *MockGitlabClient_Expecter) EditUserInGroup(ctx interface{}, input interface{}) *MockGitlabClient_EditUserInGroup_Call {
	return &MockGitlabClient_EditUserInGroup_Call{Call: _e.mock.On("EditUserInGroup", ctx, input)}
}

func (_c *MockGitlabClient_EditUserInGroup_Call) Run(run func(ctx context.Context, input gitlab.EditUserInGroupRequest)) *MockGitlabClient_EditUserInGroup_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(gitlab.EditUserInGroupRequest))
	})
	return _c
}

func (_c *MockGitlabClient_EditUserInGroup_Call) Return(_a0 *gitlab.EditUserInGroupResponse, _a1 error) *MockGitlabClient_EditUserInGroup_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockGitlabClient_EditUserInGroup_Call) RunAndReturn(run func(context.Context, gitlab.EditUserInGroupRequest) (*gitlab.EditUserInGroupResponse, error)) *MockGitlabClient_EditUserInGroup_Call {
	_c.Call.Return(run)
	return _c
}

// EditUserInProject provides a mock function with given fields: ctx, input
func (_m *MockGitlabClient) EditUserInProject(ctx context.Context, input gitlab.EditUserInProjectRequest) (*gitlab.EditUserInProjectResponse, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for EditUserInProject")
	}

	var r0 *gitlab.EditUserInProjectResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.EditUserInProjectRequest) (*gitlab.EditUserInProjectResponse, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.EditUserInProjectRequest) *gitlab.EditUserInProjectResponse); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*gitlab.EditUserInProjectResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, gitlab.EditUserInProjectRequest) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockGitlabClient_EditUserInProject_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'EditUserInProject'
type MockGitlabClient_EditUserInProject_Call struct {
	*mock.Call
}

// EditUserInProject is a helper method to define mock.On call
//   - ctx context.Context
//   - input gitlab.EditUserInProjectRequest
func (_e *MockGitlabClient_Expecter) EditUserInProject(ctx interface{}, input interface{}) *MockGitlabClient_EditUserInProject_Call {
	return &MockGitlabClient_EditUserInProject_Call{Call: _e.mock.On("EditUserInProject", ctx, input)}
}

func (_c *MockGitlabClient_EditUserInProject_Call) Run(run func(ctx context.Context, input gitlab.EditUserInProjectRequest)) *MockGitlabClient_EditUserInProject_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(gitlab.EditUserInProjectRequest))
	})
	return _c
}

func (_c *MockGitlabClient_EditUserInProject_Call) Return(_a0 *gitlab.EditUserInProjectResponse, _a1 error) *MockGitlabClient_EditUserInProject_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockGitlabClient_EditUserInProject_Call) RunAndReturn(run func(context.Context, gitlab.EditUserInProjectRequest) (*gitlab.EditUserInProjectResponse, error)) *MockGitlabClient_EditUserInProject_Call {
	_c.Call.Return(run)
	return _c
}

// GetFileFromRepo provides a mock function with given fields: ctx, input
func (_m *MockGitlabClient) GetFileFromRepo(ctx context.Context, input gitlab.GetFileRequest) (*gitlab.GetFileResponse, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for GetFileFromRepo")
	}

	var r0 *gitlab.GetFileResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.GetFileRequest) (*gitlab.GetFileResponse, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.GetFileRequest) *gitlab.GetFileResponse); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*gitlab.GetFileResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, gitlab.GetFileRequest) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockGitlabClient_GetFileFromRepo_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFileFromRepo'
type MockGitlabClient_GetFileFromRepo_Call struct {
	*mock.Call
}

// GetFileFromRepo is a helper method to define mock.On call
//   - ctx context.Context
//   - input gitlab.GetFileRequest
func (_e *MockGitlabClient_Expecter) GetFileFromRepo(ctx interface{}, input interface{}) *MockGitlabClient_GetFileFromRepo_Call {
	return &MockGitlabClient_GetFileFromRepo_Call{Call: _e.mock.On("GetFileFromRepo", ctx, input)}
}

func (_c *MockGitlabClient_GetFileFromRepo_Call) Run(run func(ctx context.Context, input gitlab.GetFileRequest)) *MockGitlabClient_GetFileFromRepo_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(gitlab.GetFileRequest))
	})
	return _c
}

func (_c *MockGitlabClient_GetFileFromRepo_Call) Return(_a0 *gitlab.GetFileResponse, _a1 error) *MockGitlabClient_GetFileFromRepo_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockGitlabClient_GetFileFromRepo_Call) RunAndReturn(run func(context.Context, gitlab.GetFileRequest) (*gitlab.GetFileResponse, error)) *MockGitlabClient_GetFileFromRepo_Call {
	_c.Call.Return(run)
	return _c
}

// GetGroup provides a mock function with given fields: ctx, input
func (_m *MockGitlabClient) GetGroup(ctx context.Context, input gitlab.GetGroupRequest) ([]gitlab.GitlabGroup, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for GetGroup")
	}

	var r0 []gitlab.GitlabGroup
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.GetGroupRequest) ([]gitlab.GitlabGroup, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.GetGroupRequest) []gitlab.GitlabGroup); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]gitlab.GitlabGroup)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, gitlab.GetGroupRequest) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockGitlabClient_GetGroup_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetGroup'
type MockGitlabClient_GetGroup_Call struct {
	*mock.Call
}

// GetGroup is a helper method to define mock.On call
//   - ctx context.Context
//   - input gitlab.GetGroupRequest
func (_e *MockGitlabClient_Expecter) GetGroup(ctx interface{}, input interface{}) *MockGitlabClient_GetGroup_Call {
	return &MockGitlabClient_GetGroup_Call{Call: _e.mock.On("GetGroup", ctx, input)}
}

func (_c *MockGitlabClient_GetGroup_Call) Run(run func(ctx context.Context, input gitlab.GetGroupRequest)) *MockGitlabClient_GetGroup_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(gitlab.GetGroupRequest))
	})
	return _c
}

func (_c *MockGitlabClient_GetGroup_Call) Return(_a0 []gitlab.GitlabGroup, _a1 error) *MockGitlabClient_GetGroup_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockGitlabClient_GetGroup_Call) RunAndReturn(run func(context.Context, gitlab.GetGroupRequest) ([]gitlab.GitlabGroup, error)) *MockGitlabClient_GetGroup_Call {
	_c.Call.Return(run)
	return _c
}

// GetHeaderRawFileFromRepo provides a mock function with given fields: ctx, input
func (_m *MockGitlabClient) GetHeaderRawFileFromRepo(ctx context.Context, input gitlab.GetFileRequest) (*http.Response, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for GetHeaderRawFileFromRepo")
	}

	var r0 *http.Response
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.GetFileRequest) (*http.Response, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.GetFileRequest) *http.Response); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*http.Response)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, gitlab.GetFileRequest) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockGitlabClient_GetHeaderRawFileFromRepo_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetHeaderRawFileFromRepo'
type MockGitlabClient_GetHeaderRawFileFromRepo_Call struct {
	*mock.Call
}

// GetHeaderRawFileFromRepo is a helper method to define mock.On call
//   - ctx context.Context
//   - input gitlab.GetFileRequest
func (_e *MockGitlabClient_Expecter) GetHeaderRawFileFromRepo(ctx interface{}, input interface{}) *MockGitlabClient_GetHeaderRawFileFromRepo_Call {
	return &MockGitlabClient_GetHeaderRawFileFromRepo_Call{Call: _e.mock.On("GetHeaderRawFileFromRepo", ctx, input)}
}

func (_c *MockGitlabClient_GetHeaderRawFileFromRepo_Call) Run(run func(ctx context.Context, input gitlab.GetFileRequest)) *MockGitlabClient_GetHeaderRawFileFromRepo_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(gitlab.GetFileRequest))
	})
	return _c
}

func (_c *MockGitlabClient_GetHeaderRawFileFromRepo_Call) Return(_a0 *http.Response, _a1 error) *MockGitlabClient_GetHeaderRawFileFromRepo_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockGitlabClient_GetHeaderRawFileFromRepo_Call) RunAndReturn(run func(context.Context, gitlab.GetFileRequest) (*http.Response, error)) *MockGitlabClient_GetHeaderRawFileFromRepo_Call {
	_c.Call.Return(run)
	return _c
}

// GetMemberOfGroup provides a mock function with given fields: ctx, input
func (_m *MockGitlabClient) GetMemberOfGroup(ctx context.Context, input gitlab.GetMemberOfGroupRequest) (*gitlab.GetMemberOfGroupResponse, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for GetMemberOfGroup")
	}

	var r0 *gitlab.GetMemberOfGroupResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.GetMemberOfGroupRequest) (*gitlab.GetMemberOfGroupResponse, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.GetMemberOfGroupRequest) *gitlab.GetMemberOfGroupResponse); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*gitlab.GetMemberOfGroupResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, gitlab.GetMemberOfGroupRequest) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockGitlabClient_GetMemberOfGroup_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetMemberOfGroup'
type MockGitlabClient_GetMemberOfGroup_Call struct {
	*mock.Call
}

// GetMemberOfGroup is a helper method to define mock.On call
//   - ctx context.Context
//   - input gitlab.GetMemberOfGroupRequest
func (_e *MockGitlabClient_Expecter) GetMemberOfGroup(ctx interface{}, input interface{}) *MockGitlabClient_GetMemberOfGroup_Call {
	return &MockGitlabClient_GetMemberOfGroup_Call{Call: _e.mock.On("GetMemberOfGroup", ctx, input)}
}

func (_c *MockGitlabClient_GetMemberOfGroup_Call) Run(run func(ctx context.Context, input gitlab.GetMemberOfGroupRequest)) *MockGitlabClient_GetMemberOfGroup_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(gitlab.GetMemberOfGroupRequest))
	})
	return _c
}

func (_c *MockGitlabClient_GetMemberOfGroup_Call) Return(_a0 *gitlab.GetMemberOfGroupResponse, _a1 error) *MockGitlabClient_GetMemberOfGroup_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockGitlabClient_GetMemberOfGroup_Call) RunAndReturn(run func(context.Context, gitlab.GetMemberOfGroupRequest) (*gitlab.GetMemberOfGroupResponse, error)) *MockGitlabClient_GetMemberOfGroup_Call {
	_c.Call.Return(run)
	return _c
}

// GetMemberOfProject provides a mock function with given fields: ctx, input
func (_m *MockGitlabClient) GetMemberOfProject(ctx context.Context, input gitlab.GetMemberOfProjectRequest) (*gitlab.GetMemberOfProjectResponse, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for GetMemberOfProject")
	}

	var r0 *gitlab.GetMemberOfProjectResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.GetMemberOfProjectRequest) (*gitlab.GetMemberOfProjectResponse, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.GetMemberOfProjectRequest) *gitlab.GetMemberOfProjectResponse); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*gitlab.GetMemberOfProjectResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, gitlab.GetMemberOfProjectRequest) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockGitlabClient_GetMemberOfProject_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetMemberOfProject'
type MockGitlabClient_GetMemberOfProject_Call struct {
	*mock.Call
}

// GetMemberOfProject is a helper method to define mock.On call
//   - ctx context.Context
//   - input gitlab.GetMemberOfProjectRequest
func (_e *MockGitlabClient_Expecter) GetMemberOfProject(ctx interface{}, input interface{}) *MockGitlabClient_GetMemberOfProject_Call {
	return &MockGitlabClient_GetMemberOfProject_Call{Call: _e.mock.On("GetMemberOfProject", ctx, input)}
}

func (_c *MockGitlabClient_GetMemberOfProject_Call) Run(run func(ctx context.Context, input gitlab.GetMemberOfProjectRequest)) *MockGitlabClient_GetMemberOfProject_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(gitlab.GetMemberOfProjectRequest))
	})
	return _c
}

func (_c *MockGitlabClient_GetMemberOfProject_Call) Return(_a0 *gitlab.GetMemberOfProjectResponse, _a1 error) *MockGitlabClient_GetMemberOfProject_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockGitlabClient_GetMemberOfProject_Call) RunAndReturn(run func(context.Context, gitlab.GetMemberOfProjectRequest) (*gitlab.GetMemberOfProjectResponse, error)) *MockGitlabClient_GetMemberOfProject_Call {
	_c.Call.Return(run)
	return _c
}

// GetMergeRequests provides a mock function with given fields: ctx, input
func (_m *MockGitlabClient) GetMergeRequests(ctx context.Context, input gitlab.GetMergeRequestsRequest) ([]gitlab.MergeRequest, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for GetMergeRequests")
	}

	var r0 []gitlab.MergeRequest
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.GetMergeRequestsRequest) ([]gitlab.MergeRequest, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.GetMergeRequestsRequest) []gitlab.MergeRequest); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]gitlab.MergeRequest)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, gitlab.GetMergeRequestsRequest) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockGitlabClient_GetMergeRequests_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetMergeRequests'
type MockGitlabClient_GetMergeRequests_Call struct {
	*mock.Call
}

// GetMergeRequests is a helper method to define mock.On call
//   - ctx context.Context
//   - input gitlab.GetMergeRequestsRequest
func (_e *MockGitlabClient_Expecter) GetMergeRequests(ctx interface{}, input interface{}) *MockGitlabClient_GetMergeRequests_Call {
	return &MockGitlabClient_GetMergeRequests_Call{Call: _e.mock.On("GetMergeRequests", ctx, input)}
}

func (_c *MockGitlabClient_GetMergeRequests_Call) Run(run func(ctx context.Context, input gitlab.GetMergeRequestsRequest)) *MockGitlabClient_GetMergeRequests_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(gitlab.GetMergeRequestsRequest))
	})
	return _c
}

func (_c *MockGitlabClient_GetMergeRequests_Call) Return(_a0 []gitlab.MergeRequest, _a1 error) *MockGitlabClient_GetMergeRequests_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockGitlabClient_GetMergeRequests_Call) RunAndReturn(run func(context.Context, gitlab.GetMergeRequestsRequest) ([]gitlab.MergeRequest, error)) *MockGitlabClient_GetMergeRequests_Call {
	_c.Call.Return(run)
	return _c
}

// GetProjectBranches provides a mock function with given fields: ctx, input
func (_m *MockGitlabClient) GetProjectBranches(ctx context.Context, input gitlab.GetProjectBranchesRequest) ([]gitlab.RepositoryBranchInfo, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for GetProjectBranches")
	}

	var r0 []gitlab.RepositoryBranchInfo
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.GetProjectBranchesRequest) ([]gitlab.RepositoryBranchInfo, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.GetProjectBranchesRequest) []gitlab.RepositoryBranchInfo); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]gitlab.RepositoryBranchInfo)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, gitlab.GetProjectBranchesRequest) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockGitlabClient_GetProjectBranches_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetProjectBranches'
type MockGitlabClient_GetProjectBranches_Call struct {
	*mock.Call
}

// GetProjectBranches is a helper method to define mock.On call
//   - ctx context.Context
//   - input gitlab.GetProjectBranchesRequest
func (_e *MockGitlabClient_Expecter) GetProjectBranches(ctx interface{}, input interface{}) *MockGitlabClient_GetProjectBranches_Call {
	return &MockGitlabClient_GetProjectBranches_Call{Call: _e.mock.On("GetProjectBranches", ctx, input)}
}

func (_c *MockGitlabClient_GetProjectBranches_Call) Run(run func(ctx context.Context, input gitlab.GetProjectBranchesRequest)) *MockGitlabClient_GetProjectBranches_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(gitlab.GetProjectBranchesRequest))
	})
	return _c
}

func (_c *MockGitlabClient_GetProjectBranches_Call) Return(_a0 []gitlab.RepositoryBranchInfo, _a1 error) *MockGitlabClient_GetProjectBranches_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockGitlabClient_GetProjectBranches_Call) RunAndReturn(run func(context.Context, gitlab.GetProjectBranchesRequest) ([]gitlab.RepositoryBranchInfo, error)) *MockGitlabClient_GetProjectBranches_Call {
	_c.Call.Return(run)
	return _c
}

// GetProjectCommits provides a mock function with given fields: ctx, input
func (_m *MockGitlabClient) GetProjectCommits(ctx context.Context, input gitlab.GetProjectCommitsRequest) (*gitlab.HTTPResponse[[]gitlab.RepositoryCommit], error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for GetProjectCommits")
	}

	var r0 *gitlab.HTTPResponse[[]gitlab.RepositoryCommit]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.GetProjectCommitsRequest) (*gitlab.HTTPResponse[[]gitlab.RepositoryCommit], error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.GetProjectCommitsRequest) *gitlab.HTTPResponse[[]gitlab.RepositoryCommit]); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*gitlab.HTTPResponse[[]gitlab.RepositoryCommit])
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, gitlab.GetProjectCommitsRequest) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockGitlabClient_GetProjectCommits_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetProjectCommits'
type MockGitlabClient_GetProjectCommits_Call struct {
	*mock.Call
}

// GetProjectCommits is a helper method to define mock.On call
//   - ctx context.Context
//   - input gitlab.GetProjectCommitsRequest
func (_e *MockGitlabClient_Expecter) GetProjectCommits(ctx interface{}, input interface{}) *MockGitlabClient_GetProjectCommits_Call {
	return &MockGitlabClient_GetProjectCommits_Call{Call: _e.mock.On("GetProjectCommits", ctx, input)}
}

func (_c *MockGitlabClient_GetProjectCommits_Call) Run(run func(ctx context.Context, input gitlab.GetProjectCommitsRequest)) *MockGitlabClient_GetProjectCommits_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(gitlab.GetProjectCommitsRequest))
	})
	return _c
}

func (_c *MockGitlabClient_GetProjectCommits_Call) Return(_a0 *gitlab.HTTPResponse[[]gitlab.RepositoryCommit], _a1 error) *MockGitlabClient_GetProjectCommits_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockGitlabClient_GetProjectCommits_Call) RunAndReturn(run func(context.Context, gitlab.GetProjectCommitsRequest) (*gitlab.HTTPResponse[[]gitlab.RepositoryCommit], error)) *MockGitlabClient_GetProjectCommits_Call {
	_c.Call.Return(run)
	return _c
}

// GetProjectContributors provides a mock function with given fields: ctx, input
func (_m *MockGitlabClient) GetProjectContributors(ctx context.Context, input gitlab.GetProjectContributorsRequest) ([]gitlab.RepositoryContributor, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for GetProjectContributors")
	}

	var r0 []gitlab.RepositoryContributor
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.GetProjectContributorsRequest) ([]gitlab.RepositoryContributor, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.GetProjectContributorsRequest) []gitlab.RepositoryContributor); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]gitlab.RepositoryContributor)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, gitlab.GetProjectContributorsRequest) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockGitlabClient_GetProjectContributors_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetProjectContributors'
type MockGitlabClient_GetProjectContributors_Call struct {
	*mock.Call
}

// GetProjectContributors is a helper method to define mock.On call
//   - ctx context.Context
//   - input gitlab.GetProjectContributorsRequest
func (_e *MockGitlabClient_Expecter) GetProjectContributors(ctx interface{}, input interface{}) *MockGitlabClient_GetProjectContributors_Call {
	return &MockGitlabClient_GetProjectContributors_Call{Call: _e.mock.On("GetProjectContributors", ctx, input)}
}

func (_c *MockGitlabClient_GetProjectContributors_Call) Run(run func(ctx context.Context, input gitlab.GetProjectContributorsRequest)) *MockGitlabClient_GetProjectContributors_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(gitlab.GetProjectContributorsRequest))
	})
	return _c
}

func (_c *MockGitlabClient_GetProjectContributors_Call) Return(_a0 []gitlab.RepositoryContributor, _a1 error) *MockGitlabClient_GetProjectContributors_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockGitlabClient_GetProjectContributors_Call) RunAndReturn(run func(context.Context, gitlab.GetProjectContributorsRequest) ([]gitlab.RepositoryContributor, error)) *MockGitlabClient_GetProjectContributors_Call {
	_c.Call.Return(run)
	return _c
}

// GetProjectFiles provides a mock function with given fields: ctx, input
func (_m *MockGitlabClient) GetProjectFiles(ctx context.Context, input gitlab.GetProjectFilesRequest) ([]gitlab.RepositoryFile, *string, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for GetProjectFiles")
	}

	var r0 []gitlab.RepositoryFile
	var r1 *string
	var r2 error
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.GetProjectFilesRequest) ([]gitlab.RepositoryFile, *string, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.GetProjectFilesRequest) []gitlab.RepositoryFile); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]gitlab.RepositoryFile)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, gitlab.GetProjectFilesRequest) *string); ok {
		r1 = rf(ctx, input)
	} else {
		if ret.Get(1) != nil {
			r1 = ret.Get(1).(*string)
		}
	}

	if rf, ok := ret.Get(2).(func(context.Context, gitlab.GetProjectFilesRequest) error); ok {
		r2 = rf(ctx, input)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// MockGitlabClient_GetProjectFiles_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetProjectFiles'
type MockGitlabClient_GetProjectFiles_Call struct {
	*mock.Call
}

// GetProjectFiles is a helper method to define mock.On call
//   - ctx context.Context
//   - input gitlab.GetProjectFilesRequest
func (_e *MockGitlabClient_Expecter) GetProjectFiles(ctx interface{}, input interface{}) *MockGitlabClient_GetProjectFiles_Call {
	return &MockGitlabClient_GetProjectFiles_Call{Call: _e.mock.On("GetProjectFiles", ctx, input)}
}

func (_c *MockGitlabClient_GetProjectFiles_Call) Run(run func(ctx context.Context, input gitlab.GetProjectFilesRequest)) *MockGitlabClient_GetProjectFiles_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(gitlab.GetProjectFilesRequest))
	})
	return _c
}

func (_c *MockGitlabClient_GetProjectFiles_Call) Return(_a0 []gitlab.RepositoryFile, _a1 *string, _a2 error) *MockGitlabClient_GetProjectFiles_Call {
	_c.Call.Return(_a0, _a1, _a2)
	return _c
}

func (_c *MockGitlabClient_GetProjectFiles_Call) RunAndReturn(run func(context.Context, gitlab.GetProjectFilesRequest) ([]gitlab.RepositoryFile, *string, error)) *MockGitlabClient_GetProjectFiles_Call {
	_c.Call.Return(run)
	return _c
}

// GetProjectURLs provides a mock function with given fields: ctx, input
func (_m *MockGitlabClient) GetProjectURLs(ctx context.Context, input gitlab.GetProjectURLsRequest) (*gitlab.GetProjectURLsResponse, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for GetProjectURLs")
	}

	var r0 *gitlab.GetProjectURLsResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.GetProjectURLsRequest) (*gitlab.GetProjectURLsResponse, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.GetProjectURLsRequest) *gitlab.GetProjectURLsResponse); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*gitlab.GetProjectURLsResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, gitlab.GetProjectURLsRequest) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockGitlabClient_GetProjectURLs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetProjectURLs'
type MockGitlabClient_GetProjectURLs_Call struct {
	*mock.Call
}

// GetProjectURLs is a helper method to define mock.On call
//   - ctx context.Context
//   - input gitlab.GetProjectURLsRequest
func (_e *MockGitlabClient_Expecter) GetProjectURLs(ctx interface{}, input interface{}) *MockGitlabClient_GetProjectURLs_Call {
	return &MockGitlabClient_GetProjectURLs_Call{Call: _e.mock.On("GetProjectURLs", ctx, input)}
}

func (_c *MockGitlabClient_GetProjectURLs_Call) Run(run func(ctx context.Context, input gitlab.GetProjectURLsRequest)) *MockGitlabClient_GetProjectURLs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(gitlab.GetProjectURLsRequest))
	})
	return _c
}

func (_c *MockGitlabClient_GetProjectURLs_Call) Return(_a0 *gitlab.GetProjectURLsResponse, _a1 error) *MockGitlabClient_GetProjectURLs_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockGitlabClient_GetProjectURLs_Call) RunAndReturn(run func(context.Context, gitlab.GetProjectURLsRequest) (*gitlab.GetProjectURLsResponse, error)) *MockGitlabClient_GetProjectURLs_Call {
	_c.Call.Return(run)
	return _c
}

// GetRawFileFromRepo provides a mock function with given fields: ctx, input
func (_m *MockGitlabClient) GetRawFileFromRepo(ctx context.Context, input gitlab.GetFileRequest) (*http.Response, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for GetRawFileFromRepo")
	}

	var r0 *http.Response
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.GetFileRequest) (*http.Response, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.GetFileRequest) *http.Response); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*http.Response)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, gitlab.GetFileRequest) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockGitlabClient_GetRawFileFromRepo_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetRawFileFromRepo'
type MockGitlabClient_GetRawFileFromRepo_Call struct {
	*mock.Call
}

// GetRawFileFromRepo is a helper method to define mock.On call
//   - ctx context.Context
//   - input gitlab.GetFileRequest
func (_e *MockGitlabClient_Expecter) GetRawFileFromRepo(ctx interface{}, input interface{}) *MockGitlabClient_GetRawFileFromRepo_Call {
	return &MockGitlabClient_GetRawFileFromRepo_Call{Call: _e.mock.On("GetRawFileFromRepo", ctx, input)}
}

func (_c *MockGitlabClient_GetRawFileFromRepo_Call) Run(run func(ctx context.Context, input gitlab.GetFileRequest)) *MockGitlabClient_GetRawFileFromRepo_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(gitlab.GetFileRequest))
	})
	return _c
}

func (_c *MockGitlabClient_GetRawFileFromRepo_Call) Return(_a0 *http.Response, _a1 error) *MockGitlabClient_GetRawFileFromRepo_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockGitlabClient_GetRawFileFromRepo_Call) RunAndReturn(run func(context.Context, gitlab.GetFileRequest) (*http.Response, error)) *MockGitlabClient_GetRawFileFromRepo_Call {
	_c.Call.Return(run)
	return _c
}

// GetSingleProjectBrancheInfo provides a mock function with given fields: ctx, input
func (_m *MockGitlabClient) GetSingleProjectBrancheInfo(ctx context.Context, input gitlab.GetSingleProjectBrancheRequest) (*gitlab.RepositoryBranchInfo, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for GetSingleProjectBrancheInfo")
	}

	var r0 *gitlab.RepositoryBranchInfo
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.GetSingleProjectBrancheRequest) (*gitlab.RepositoryBranchInfo, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.GetSingleProjectBrancheRequest) *gitlab.RepositoryBranchInfo); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*gitlab.RepositoryBranchInfo)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, gitlab.GetSingleProjectBrancheRequest) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockGitlabClient_GetSingleProjectBrancheInfo_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetSingleProjectBrancheInfo'
type MockGitlabClient_GetSingleProjectBrancheInfo_Call struct {
	*mock.Call
}

// GetSingleProjectBrancheInfo is a helper method to define mock.On call
//   - ctx context.Context
//   - input gitlab.GetSingleProjectBrancheRequest
func (_e *MockGitlabClient_Expecter) GetSingleProjectBrancheInfo(ctx interface{}, input interface{}) *MockGitlabClient_GetSingleProjectBrancheInfo_Call {
	return &MockGitlabClient_GetSingleProjectBrancheInfo_Call{Call: _e.mock.On("GetSingleProjectBrancheInfo", ctx, input)}
}

func (_c *MockGitlabClient_GetSingleProjectBrancheInfo_Call) Run(run func(ctx context.Context, input gitlab.GetSingleProjectBrancheRequest)) *MockGitlabClient_GetSingleProjectBrancheInfo_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(gitlab.GetSingleProjectBrancheRequest))
	})
	return _c
}

func (_c *MockGitlabClient_GetSingleProjectBrancheInfo_Call) Return(_a0 *gitlab.RepositoryBranchInfo, _a1 error) *MockGitlabClient_GetSingleProjectBrancheInfo_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockGitlabClient_GetSingleProjectBrancheInfo_Call) RunAndReturn(run func(context.Context, gitlab.GetSingleProjectBrancheRequest) (*gitlab.RepositoryBranchInfo, error)) *MockGitlabClient_GetSingleProjectBrancheInfo_Call {
	_c.Call.Return(run)
	return _c
}

// GetSingleProjectCommit provides a mock function with given fields: ctx, input
func (_m *MockGitlabClient) GetSingleProjectCommit(ctx context.Context, input gitlab.GetSingleProjectCommitRequest) (*gitlab.RepositoryCommit, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for GetSingleProjectCommit")
	}

	var r0 *gitlab.RepositoryCommit
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.GetSingleProjectCommitRequest) (*gitlab.RepositoryCommit, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.GetSingleProjectCommitRequest) *gitlab.RepositoryCommit); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*gitlab.RepositoryCommit)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, gitlab.GetSingleProjectCommitRequest) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockGitlabClient_GetSingleProjectCommit_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetSingleProjectCommit'
type MockGitlabClient_GetSingleProjectCommit_Call struct {
	*mock.Call
}

// GetSingleProjectCommit is a helper method to define mock.On call
//   - ctx context.Context
//   - input gitlab.GetSingleProjectCommitRequest
func (_e *MockGitlabClient_Expecter) GetSingleProjectCommit(ctx interface{}, input interface{}) *MockGitlabClient_GetSingleProjectCommit_Call {
	return &MockGitlabClient_GetSingleProjectCommit_Call{Call: _e.mock.On("GetSingleProjectCommit", ctx, input)}
}

func (_c *MockGitlabClient_GetSingleProjectCommit_Call) Run(run func(ctx context.Context, input gitlab.GetSingleProjectCommitRequest)) *MockGitlabClient_GetSingleProjectCommit_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(gitlab.GetSingleProjectCommitRequest))
	})
	return _c
}

func (_c *MockGitlabClient_GetSingleProjectCommit_Call) Return(_a0 *gitlab.RepositoryCommit, _a1 error) *MockGitlabClient_GetSingleProjectCommit_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockGitlabClient_GetSingleProjectCommit_Call) RunAndReturn(run func(context.Context, gitlab.GetSingleProjectCommitRequest) (*gitlab.RepositoryCommit, error)) *MockGitlabClient_GetSingleProjectCommit_Call {
	_c.Call.Return(run)
	return _c
}

// GetUser provides a mock function with given fields: ctx, request
func (_m *MockGitlabClient) GetUser(ctx context.Context, request gitlab.GetUserRequest) ([]gitlab.CreateUserResponse, error) {
	ret := _m.Called(ctx, request)

	if len(ret) == 0 {
		panic("no return value specified for GetUser")
	}

	var r0 []gitlab.CreateUserResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.GetUserRequest) ([]gitlab.CreateUserResponse, error)); ok {
		return rf(ctx, request)
	}
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.GetUserRequest) []gitlab.CreateUserResponse); ok {
		r0 = rf(ctx, request)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]gitlab.CreateUserResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, gitlab.GetUserRequest) error); ok {
		r1 = rf(ctx, request)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockGitlabClient_GetUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUser'
type MockGitlabClient_GetUser_Call struct {
	*mock.Call
}

// GetUser is a helper method to define mock.On call
//   - ctx context.Context
//   - request gitlab.GetUserRequest
func (_e *MockGitlabClient_Expecter) GetUser(ctx interface{}, request interface{}) *MockGitlabClient_GetUser_Call {
	return &MockGitlabClient_GetUser_Call{Call: _e.mock.On("GetUser", ctx, request)}
}

func (_c *MockGitlabClient_GetUser_Call) Run(run func(ctx context.Context, request gitlab.GetUserRequest)) *MockGitlabClient_GetUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(gitlab.GetUserRequest))
	})
	return _c
}

func (_c *MockGitlabClient_GetUser_Call) Return(_a0 []gitlab.CreateUserResponse, _a1 error) *MockGitlabClient_GetUser_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockGitlabClient_GetUser_Call) RunAndReturn(run func(context.Context, gitlab.GetUserRequest) ([]gitlab.CreateUserResponse, error)) *MockGitlabClient_GetUser_Call {
	_c.Call.Return(run)
	return _c
}

// Health provides a mock function with given fields: ctx, token
func (_m *MockGitlabClient) Health(ctx context.Context, token string) (bool, error) {
	ret := _m.Called(ctx, token)

	if len(ret) == 0 {
		panic("no return value specified for Health")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (bool, error)); ok {
		return rf(ctx, token)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) bool); ok {
		r0 = rf(ctx, token)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, token)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockGitlabClient_Health_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Health'
type MockGitlabClient_Health_Call struct {
	*mock.Call
}

// Health is a helper method to define mock.On call
//   - ctx context.Context
//   - token string
func (_e *MockGitlabClient_Expecter) Health(ctx interface{}, token interface{}) *MockGitlabClient_Health_Call {
	return &MockGitlabClient_Health_Call{Call: _e.mock.On("Health", ctx, token)}
}

func (_c *MockGitlabClient_Health_Call) Run(run func(ctx context.Context, token string)) *MockGitlabClient_Health_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockGitlabClient_Health_Call) Return(_a0 bool, _a1 error) *MockGitlabClient_Health_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockGitlabClient_Health_Call) RunAndReturn(run func(context.Context, string) (bool, error)) *MockGitlabClient_Health_Call {
	_c.Call.Return(run)
	return _c
}

// RemoveUserFromGroup provides a mock function with given fields: ctx, input
func (_m *MockGitlabClient) RemoveUserFromGroup(ctx context.Context, input gitlab.RemoveUserFromGroupRequest) error {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for RemoveUserFromGroup")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.RemoveUserFromGroupRequest) error); ok {
		r0 = rf(ctx, input)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockGitlabClient_RemoveUserFromGroup_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RemoveUserFromGroup'
type MockGitlabClient_RemoveUserFromGroup_Call struct {
	*mock.Call
}

// RemoveUserFromGroup is a helper method to define mock.On call
//   - ctx context.Context
//   - input gitlab.RemoveUserFromGroupRequest
func (_e *MockGitlabClient_Expecter) RemoveUserFromGroup(ctx interface{}, input interface{}) *MockGitlabClient_RemoveUserFromGroup_Call {
	return &MockGitlabClient_RemoveUserFromGroup_Call{Call: _e.mock.On("RemoveUserFromGroup", ctx, input)}
}

func (_c *MockGitlabClient_RemoveUserFromGroup_Call) Run(run func(ctx context.Context, input gitlab.RemoveUserFromGroupRequest)) *MockGitlabClient_RemoveUserFromGroup_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(gitlab.RemoveUserFromGroupRequest))
	})
	return _c
}

func (_c *MockGitlabClient_RemoveUserFromGroup_Call) Return(_a0 error) *MockGitlabClient_RemoveUserFromGroup_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockGitlabClient_RemoveUserFromGroup_Call) RunAndReturn(run func(context.Context, gitlab.RemoveUserFromGroupRequest) error) *MockGitlabClient_RemoveUserFromGroup_Call {
	_c.Call.Return(run)
	return _c
}

// RemoveUserFromProject provides a mock function with given fields: ctx, input
func (_m *MockGitlabClient) RemoveUserFromProject(ctx context.Context, input gitlab.RemoveUserFromProjectRequest) error {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for RemoveUserFromProject")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.RemoveUserFromProjectRequest) error); ok {
		r0 = rf(ctx, input)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockGitlabClient_RemoveUserFromProject_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RemoveUserFromProject'
type MockGitlabClient_RemoveUserFromProject_Call struct {
	*mock.Call
}

// RemoveUserFromProject is a helper method to define mock.On call
//   - ctx context.Context
//   - input gitlab.RemoveUserFromProjectRequest
func (_e *MockGitlabClient_Expecter) RemoveUserFromProject(ctx interface{}, input interface{}) *MockGitlabClient_RemoveUserFromProject_Call {
	return &MockGitlabClient_RemoveUserFromProject_Call{Call: _e.mock.On("RemoveUserFromProject", ctx, input)}
}

func (_c *MockGitlabClient_RemoveUserFromProject_Call) Run(run func(ctx context.Context, input gitlab.RemoveUserFromProjectRequest)) *MockGitlabClient_RemoveUserFromProject_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(gitlab.RemoveUserFromProjectRequest))
	})
	return _c
}

func (_c *MockGitlabClient_RemoveUserFromProject_Call) Return(_a0 error) *MockGitlabClient_RemoveUserFromProject_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockGitlabClient_RemoveUserFromProject_Call) RunAndReturn(run func(context.Context, gitlab.RemoveUserFromProjectRequest) error) *MockGitlabClient_RemoveUserFromProject_Call {
	_c.Call.Return(run)
	return _c
}

// SetAuthToken provides a mock function with given fields: token
func (_m *MockGitlabClient) SetAuthToken(token string) {
	_m.Called(token)
}

// MockGitlabClient_SetAuthToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetAuthToken'
type MockGitlabClient_SetAuthToken_Call struct {
	*mock.Call
}

// SetAuthToken is a helper method to define mock.On call
//   - token string
func (_e *MockGitlabClient_Expecter) SetAuthToken(token interface{}) *MockGitlabClient_SetAuthToken_Call {
	return &MockGitlabClient_SetAuthToken_Call{Call: _e.mock.On("SetAuthToken", token)}
}

func (_c *MockGitlabClient_SetAuthToken_Call) Run(run func(token string)) *MockGitlabClient_SetAuthToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *MockGitlabClient_SetAuthToken_Call) Return() *MockGitlabClient_SetAuthToken_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockGitlabClient_SetAuthToken_Call) RunAndReturn(run func(string)) *MockGitlabClient_SetAuthToken_Call {
	_c.Run(run)
	return _c
}

// UpdateGroup provides a mock function with given fields: ctx, input
func (_m *MockGitlabClient) UpdateGroup(ctx context.Context, input gitlab.UpdateGroupRequest) error {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for UpdateGroup")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.UpdateGroupRequest) error); ok {
		r0 = rf(ctx, input)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockGitlabClient_UpdateGroup_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateGroup'
type MockGitlabClient_UpdateGroup_Call struct {
	*mock.Call
}

// UpdateGroup is a helper method to define mock.On call
//   - ctx context.Context
//   - input gitlab.UpdateGroupRequest
func (_e *MockGitlabClient_Expecter) UpdateGroup(ctx interface{}, input interface{}) *MockGitlabClient_UpdateGroup_Call {
	return &MockGitlabClient_UpdateGroup_Call{Call: _e.mock.On("UpdateGroup", ctx, input)}
}

func (_c *MockGitlabClient_UpdateGroup_Call) Run(run func(ctx context.Context, input gitlab.UpdateGroupRequest)) *MockGitlabClient_UpdateGroup_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(gitlab.UpdateGroupRequest))
	})
	return _c
}

func (_c *MockGitlabClient_UpdateGroup_Call) Return(_a0 error) *MockGitlabClient_UpdateGroup_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockGitlabClient_UpdateGroup_Call) RunAndReturn(run func(context.Context, gitlab.UpdateGroupRequest) error) *MockGitlabClient_UpdateGroup_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateProject provides a mock function with given fields: ctx, input
func (_m *MockGitlabClient) UpdateProject(ctx context.Context, input gitlab.UpdateProjectRequest) error {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for UpdateProject")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.UpdateProjectRequest) error); ok {
		r0 = rf(ctx, input)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockGitlabClient_UpdateProject_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateProject'
type MockGitlabClient_UpdateProject_Call struct {
	*mock.Call
}

// UpdateProject is a helper method to define mock.On call
//   - ctx context.Context
//   - input gitlab.UpdateProjectRequest
func (_e *MockGitlabClient_Expecter) UpdateProject(ctx interface{}, input interface{}) *MockGitlabClient_UpdateProject_Call {
	return &MockGitlabClient_UpdateProject_Call{Call: _e.mock.On("UpdateProject", ctx, input)}
}

func (_c *MockGitlabClient_UpdateProject_Call) Run(run func(ctx context.Context, input gitlab.UpdateProjectRequest)) *MockGitlabClient_UpdateProject_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(gitlab.UpdateProjectRequest))
	})
	return _c
}

func (_c *MockGitlabClient_UpdateProject_Call) Return(_a0 error) *MockGitlabClient_UpdateProject_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockGitlabClient_UpdateProject_Call) RunAndReturn(run func(context.Context, gitlab.UpdateProjectRequest) error) *MockGitlabClient_UpdateProject_Call {
	_c.Call.Return(run)
	return _c
}

// UploadAvatarRepository provides a mock function with given fields: ctx, input
func (_m *MockGitlabClient) UploadAvatarRepository(ctx context.Context, input gitlab.UploadAvatarRepositoryRequest) (*gitlab.UploadAvatarRepositoryResponse, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for UploadAvatarRepository")
	}

	var r0 *gitlab.UploadAvatarRepositoryResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.UploadAvatarRepositoryRequest) (*gitlab.UploadAvatarRepositoryResponse, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, gitlab.UploadAvatarRepositoryRequest) *gitlab.UploadAvatarRepositoryResponse); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*gitlab.UploadAvatarRepositoryResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, gitlab.UploadAvatarRepositoryRequest) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockGitlabClient_UploadAvatarRepository_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UploadAvatarRepository'
type MockGitlabClient_UploadAvatarRepository_Call struct {
	*mock.Call
}

// UploadAvatarRepository is a helper method to define mock.On call
//   - ctx context.Context
//   - input gitlab.UploadAvatarRepositoryRequest
func (_e *MockGitlabClient_Expecter) UploadAvatarRepository(ctx interface{}, input interface{}) *MockGitlabClient_UploadAvatarRepository_Call {
	return &MockGitlabClient_UploadAvatarRepository_Call{Call: _e.mock.On("UploadAvatarRepository", ctx, input)}
}

func (_c *MockGitlabClient_UploadAvatarRepository_Call) Run(run func(ctx context.Context, input gitlab.UploadAvatarRepositoryRequest)) *MockGitlabClient_UploadAvatarRepository_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(gitlab.UploadAvatarRepositoryRequest))
	})
	return _c
}

func (_c *MockGitlabClient_UploadAvatarRepository_Call) Return(_a0 *gitlab.UploadAvatarRepositoryResponse, _a1 error) *MockGitlabClient_UploadAvatarRepository_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockGitlabClient_UploadAvatarRepository_Call) RunAndReturn(run func(context.Context, gitlab.UploadAvatarRepositoryRequest) (*gitlab.UploadAvatarRepositoryResponse, error)) *MockGitlabClient_UploadAvatarRepository_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockGitlabClient creates a new instance of MockGitlabClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockGitlabClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockGitlabClient {
	mock := &MockGitlabClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
