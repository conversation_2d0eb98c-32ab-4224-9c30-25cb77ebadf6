// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import mock "github.com/stretchr/testify/mock"

// MockFromEntity is an autogenerated mock type for the FromEntity type
type MockFromEntity[M interface{}, D interface{}] struct {
	mock.Mock
}

type MockFromEntity_Expecter[M interface{}, D interface{}] struct {
	mock *mock.Mock
}

func (_m *MockFromEntity[M, D]) EXPECT() *MockFromEntity_Expecter[M, D] {
	return &MockFromEntity_Expecter[M, D]{mock: &_m.Mock}
}

// FromEntity provides a mock function with given fields: m
func (_m *MockFromEntity[M, D]) FromEntity(m M) D {
	ret := _m.Called(m)

	if len(ret) == 0 {
		panic("no return value specified for FromEntity")
	}

	var r0 D
	if rf, ok := ret.Get(0).(func(M) D); ok {
		r0 = rf(m)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(D)
		}
	}

	return r0
}

// MockFromEntity_FromEntity_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FromEntity'
type MockFromEntity_FromEntity_Call[M interface{}, D interface{}] struct {
	*mock.Call
}

// FromEntity is a helper method to define mock.On call
//   - m M
func (_e *MockFromEntity_Expecter[M, D]) FromEntity(m interface{}) *MockFromEntity_FromEntity_Call[M, D] {
	return &MockFromEntity_FromEntity_Call[M, D]{Call: _e.mock.On("FromEntity", m)}
}

func (_c *MockFromEntity_FromEntity_Call[M, D]) Run(run func(m M)) *MockFromEntity_FromEntity_Call[M, D] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(M))
	})
	return _c
}

func (_c *MockFromEntity_FromEntity_Call[M, D]) Return(_a0 D) *MockFromEntity_FromEntity_Call[M, D] {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockFromEntity_FromEntity_Call[M, D]) RunAndReturn(run func(M) D) *MockFromEntity_FromEntity_Call[M, D] {
	_c.Call.Return(run)
	return _c
}

// NewMockFromEntity creates a new instance of MockFromEntity. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockFromEntity[M interface{}, D interface{}](t interface {
	mock.TestingT
	Cleanup(func())
}) *MockFromEntity[M, D] {
	mock := &MockFromEntity[M, D]{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
