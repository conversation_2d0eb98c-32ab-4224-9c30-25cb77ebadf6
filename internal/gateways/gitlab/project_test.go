package gitlab_test

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"mime/multipart"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/require"

	"api-server/internal/gateways/gitlab"
)

func TestCreateProject(t *testing.T) {
	tests := []struct {
		name                    string
		input                   gitlab.CreateProjectRequest
		mockResponseBodySuccess gitlab.CreateProjectResponse
		mockResponseBodyFail    map[string]interface{}
		mockHttpStatus          int
		expErr                  error
		expOutput               *gitlab.CreateProjectResponse
	}{
		{
			name: "should return error when status code is not 201",
			input: gitlab.CreateProjectRequest{
				Name: "Test Project",
			},
			mockHttpStatus: http.StatusBadRequest,
			mockResponseBodyFail: map[string]interface{}{
				"message": "error",
			},
			expErr: fmt.Errorf("%v: map[message:error]", gitlab.ErrBadRequestResponse),
		},
		{
			name: "should return error when status code is 201",
			input: gitlab.CreateProjectRequest{
				Name: "Test Project",
			},
			mockHttpStatus: http.StatusCreated,
			mockResponseBodySuccess: gitlab.CreateProjectResponse{
				Id:   1,
				Name: "Test Project",
			},
			expOutput: &gitlab.CreateProjectResponse{
				Id:   1,
				Name: "Test Project",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			mockSvr := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				w.WriteHeader(tt.mockHttpStatus)
				if tt.mockHttpStatus == http.StatusCreated {
					byteArr, err := json.Marshal(tt.mockResponseBodySuccess)
					require.NoError(t, err)
					_, err = w.Write(byteArr)
					require.NoError(t, err)
				} else {
					byteArr, err := json.Marshal(tt.mockResponseBodyFail)
					require.NoError(t, err)
					_, err = w.Write(byteArr)
					require.NoError(t, err)
				}
			}))

			defer mockSvr.Close()
			i, _ := gitlab.New(mockSvr.URL)
			output, err := i.CreateProject(ctx, tt.input)
			if tt.expErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.expErr, err)
			} else {
				require.NoError(t, err)
				require.Equal(t, tt.expOutput, output)
			}
		})
	}
}

func TestDeleteProject(t *testing.T) {
	tests := []struct {
		name             string
		input            gitlab.DeleteProjectRequest
		mockHttpStatus   int
		mockResponseBody map[string]interface{}
		expErr           error
	}{
		{
			name: "should return error when status code is not 204",
			input: gitlab.DeleteProjectRequest{
				Id: 1,
			},
			mockHttpStatus: http.StatusBadRequest,
			mockResponseBody: map[string]interface{}{
				"message": "error",
			},
			expErr: fmt.Errorf("%v: map[message:error]", gitlab.ErrBadRequestResponse),
		},
		{
			name: "success",
			input: gitlab.DeleteProjectRequest{
				Id: 1,
			},
			mockHttpStatus: http.StatusAccepted,
			expErr:         nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			mockSvr := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				w.WriteHeader(tt.mockHttpStatus)
				if tt.mockResponseBody != nil {
					byteArr, err := json.Marshal(tt.mockResponseBody)
					require.NoError(t, err)
					_, err = w.Write(byteArr)
					require.NoError(t, err)
				}
			}))

			defer mockSvr.Close()
			i, _ := gitlab.New(mockSvr.URL)
			err := i.DeleteProject(ctx, tt.input)
			if tt.expErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.expErr, err)
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func TestUpdateProject(t *testing.T) {
	tests := []struct {
		name             string
		input            gitlab.UpdateProjectRequest
		mockHttpStatus   int
		mockResponseBody map[string]interface{}
		expErr           error
	}{
		{
			name: "should return error when status code is not 204",
			input: gitlab.UpdateProjectRequest{
				Id:     1,
				Avatar: nil,
			},
			mockHttpStatus: http.StatusBadRequest,
			mockResponseBody: map[string]interface{}{
				"message": "error",
			},
			expErr: fmt.Errorf("%v: map[message:error]", gitlab.ErrBadRequestResponse),
		},
		{
			name: "success",
			input: gitlab.UpdateProjectRequest{
				Id:     1,
				Avatar: nil,
			},
			mockHttpStatus: http.StatusOK,
			expErr:         nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			mockSvr := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				w.WriteHeader(tt.mockHttpStatus)
				if tt.mockResponseBody != nil {
					byteArr, err := json.Marshal(tt.mockResponseBody)
					require.NoError(t, err)
					_, err = w.Write(byteArr)
					require.NoError(t, err)
				}
			}))

			defer mockSvr.Close()
			i, _ := gitlab.New(mockSvr.URL)
			err := i.UpdateProject(ctx, tt.input)
			if tt.expErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.expErr, err)
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func TestGetProjectURL(t *testing.T) {
	tests := []struct {
		name                string
		input               gitlab.GetProjectURLsRequest
		mockHttpStatus      int
		mockResponseFail    map[string]interface{}
		mockResponseSuccess gitlab.GetProjectURLsResponse
		expErr              error
		expOutput           *gitlab.GetProjectURLsResponse
	}{
		{
			name: "should return error when status code is not 200",
			input: gitlab.GetProjectURLsRequest{
				Id: 1,
			},
			mockHttpStatus: http.StatusBadRequest,
			expErr:         fmt.Errorf("%v: map[]", gitlab.ErrBadRequestResponse),
		},
		{
			name: "success",
			input: gitlab.GetProjectURLsRequest{
				Id: 1,
			},
			mockHttpStatus: http.StatusOK,
			mockResponseSuccess: gitlab.GetProjectURLsResponse{
				SSHUrl: "ssh://gitlab.com",
			},
			expOutput: &gitlab.GetProjectURLsResponse{
				SSHUrl: "ssh://gitlab.com",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			mockSvr := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				w.WriteHeader(tt.mockHttpStatus)
				if tt.mockHttpStatus == http.StatusOK {
					byteArr, err := json.Marshal(tt.mockResponseSuccess)
					require.NoError(t, err)
					_, err = w.Write(byteArr)
					require.NoError(t, err)
				} else {
					byteArr, err := json.Marshal(tt.mockResponseFail)
					require.NoError(t, err)
					_, err = w.Write(byteArr)
					require.NoError(t, err)
				}
			}))

			defer mockSvr.Close()
			i, _ := gitlab.New(mockSvr.URL)
			output, err := i.GetProjectURLs(ctx, tt.input)
			if tt.expErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.expErr, err)
			} else {
				require.NoError(t, err)
				require.Equal(t, tt.expOutput, output)
			}
		})
	}
}

func TestGetFileFromRepo(t *testing.T) {
	tests := []struct {
		name                string
		input               gitlab.GetFileRequest
		mockHttpStatus      int
		mockResponseFail    map[string]interface{}
		mockResponseSuccess gitlab.GetFileResponse
		expErr              error
		expOutput           *gitlab.GetFileResponse
	}{
		{
			name: "should return error when status code is not 200",
			input: gitlab.GetFileRequest{
				ProjectId: 1,
				FilePath:  "test",
				Ref:       "master",
			},
			mockHttpStatus: http.StatusBadRequest,
			expErr:         fmt.Errorf("%v: map[]", gitlab.ErrBadRequestResponse),
		},
		{
			name: "success",
			input: gitlab.GetFileRequest{
				ProjectId: 1,
				FilePath:  "test",
				Ref:       "master",
			},
			mockHttpStatus: http.StatusOK,
			mockResponseSuccess: gitlab.GetFileResponse{
				Content: "test",
			},
			expOutput: &gitlab.GetFileResponse{
				Content: "test",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			mockSvr := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				w.WriteHeader(tt.mockHttpStatus)
				if tt.mockHttpStatus == http.StatusOK {
					byteArr, err := json.Marshal(tt.mockResponseSuccess)
					require.NoError(t, err)
					_, err = w.Write(byteArr)
					require.NoError(t, err)
				} else {
					byteArr, err := json.Marshal(tt.mockResponseFail)
					require.NoError(t, err)
					_, err = w.Write(byteArr)
					require.NoError(t, err)
				}
			}))

			defer mockSvr.Close()
			i, _ := gitlab.New(mockSvr.URL)
			output, err := i.GetFileFromRepo(ctx, tt.input)
			if tt.expErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.expErr, err)
			} else {
				require.NoError(t, err)
				require.Equal(t, tt.expOutput, output)
			}
		})
	}

}

func TestUploadAvatarRepository(t *testing.T) {
	input := mockUploadAvatarRequest()

	tests := []struct {
		name                string
		input               gitlab.UploadAvatarRepositoryRequest
		mockHttpStatus      int
		mockResponseFail    map[string]interface{}
		mockResponseSuccess gitlab.UploadAvatarRepositoryResponse
		expErr              error
		expOutput           *gitlab.UploadAvatarRepositoryResponse
	}{
		{
			name:             "should return error when status code is not 200",
			input:            input,
			mockHttpStatus:   http.StatusBadRequest,
			mockResponseFail: map[string]interface{}{"error": "Bad request"},
			expErr:           fmt.Errorf("%v: map[error:Bad request]", gitlab.ErrBadRequestResponse),
		},
		{
			name:           "success",
			input:          input,
			mockHttpStatus: http.StatusOK,
			mockResponseSuccess: gitlab.UploadAvatarRepositoryResponse{
				AvatarUrl: "http://some-url.com",
			},
			expOutput: &gitlab.UploadAvatarRepositoryResponse{
				AvatarUrl: "http://some-url.com",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()

			// Mock HTTP server
			mockSvr := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				w.WriteHeader(tt.mockHttpStatus)
				if tt.mockHttpStatus == http.StatusOK {
					byteArr, err := json.Marshal(tt.mockResponseSuccess)
					require.NoError(t, err)
					_, err = w.Write(byteArr)
					require.NoError(t, err)
				} else {
					byteArr, err := json.Marshal(tt.mockResponseFail)
					require.NoError(t, err)
					_, err = w.Write(byteArr)
					require.NoError(t, err)
				}
			}))

			defer mockSvr.Close()

			// Mock httpClient in the implementation
			i, _ := gitlab.New(mockSvr.URL)

			// Call the function
			output, err := i.UploadAvatarRepository(ctx, tt.input)

			// Handle assertions
			if tt.expErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.expErr, err)
			} else {
				require.NoError(t, err)
				require.Equal(t, tt.expOutput, output)
			}
		})
	}
}

func createMultipartFileRequest() (*http.Request, *multipart.FileHeader) {
	// Simulate a multipart form file upload
	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)

	// Create a form file field (avatar)
	part, _ := writer.CreateFormFile("avatar", "mock-avatar.png")

	// Write mock file content
	fileContent := "mocked avatar content"
	part.Write([]byte(fileContent))

	// Close the writer to finalize the multipart form
	writer.Close()

	// Create an HTTP POST request with the multipart body
	req := httptest.NewRequest(http.MethodPost, "/upload", body)
	req.Header.Set("Content-Type", writer.FormDataContentType())

	// Parse the multipart form to get the FileHeader
	req.ParseMultipartForm(32 << 20) // 32MB max memory

	// Get the file header from the form
	file, header, _ := req.FormFile("avatar")

	defer file.Close()

	return req, header
}

func mockUploadAvatarRequest() gitlab.UploadAvatarRepositoryRequest {
	// Create the request and file header
	_, fileHeader := createMultipartFileRequest()

	return gitlab.UploadAvatarRepositoryRequest{
		ProjectId: 1,
		Avatar:    fileHeader,
		Token:     "mocked-token",
	}
}

func TestListProjectBranches(t *testing.T) {
	tests := []struct {
		name                string
		input               gitlab.GetProjectBranchesRequest
		mockHttpStatus      int
		mockResponseFail    map[string]interface{}
		mockResponseSuccess gitlab.GetProjectBranchesResponse
		expErr              error
		expOutput           []gitlab.RepositoryBranchInfo
	}{
		{
			name: "should return error when status code is not 200",
			input: gitlab.GetProjectBranchesRequest{
				Id: 1,
			},
			mockHttpStatus: http.StatusBadRequest,
			expErr:         fmt.Errorf("%v: map[]", gitlab.ErrBadRequestResponse),
		},
		{
			name: "success",
			input: gitlab.GetProjectBranchesRequest{
				Id: 1,
			},
			mockHttpStatus: http.StatusOK,
			mockResponseSuccess: []gitlab.RepositoryBranchInfo{
				{RepositoryBranch: gitlab.RepositoryBranch{
					Name: "main",
				}},
			},
			expOutput: []gitlab.RepositoryBranchInfo{
				{RepositoryBranch: gitlab.RepositoryBranch{
					Name: "main",
				}},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			mockSvr := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				w.WriteHeader(tt.mockHttpStatus)
				if tt.mockHttpStatus == http.StatusOK {
					byteArr, err := json.Marshal(tt.mockResponseSuccess)
					require.NoError(t, err)
					_, err = w.Write(byteArr)
					require.NoError(t, err)
				} else {
					byteArr, err := json.Marshal(tt.mockResponseFail)
					require.NoError(t, err)
					_, err = w.Write(byteArr)
					require.NoError(t, err)
				}
			}))

			defer mockSvr.Close()
			i, _ := gitlab.New(mockSvr.URL)
			output, err := i.GetProjectBranches(ctx, tt.input)
			if tt.expErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.expErr, err)
			} else {
				require.NoError(t, err)
				require.Equal(t, tt.expOutput, output)
			}
		})
	}
}

func TestListProjectFiles(t *testing.T) {
	tests := []struct {
		name                string
		input               gitlab.GetProjectFilesRequest
		mockHttpStatus      int
		mockResponseFail    map[string]interface{}
		mockResponseSuccess []gitlab.RepositoryFile
		expErr              error
		expOutput           []gitlab.RepositoryFile
	}{
		{
			name: "should return error when status code is not 200",
			input: gitlab.GetProjectFilesRequest{
				Id: 1,
			},
			mockHttpStatus: http.StatusBadRequest,
			expErr:         fmt.Errorf("%v: map[]", gitlab.ErrBadRequestResponse),
		},
		{
			name: "success",
			input: gitlab.GetProjectFilesRequest{
				Id: 1,
			},
			mockHttpStatus: http.StatusOK,
			mockResponseSuccess: []gitlab.RepositoryFile{
				{
					Name: "main.go",
					Type: "blob",
				},
			},
			expOutput: []gitlab.RepositoryFile{
				{
					Name: "main.go",
					Type: "blob",
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			mockSvr := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				w.WriteHeader(tt.mockHttpStatus)
				if tt.mockHttpStatus == http.StatusOK {
					byteArr, err := json.Marshal(tt.mockResponseSuccess)
					require.NoError(t, err)
					_, err = w.Write(byteArr)
					require.NoError(t, err)
				} else {
					byteArr, err := json.Marshal(tt.mockResponseFail)
					require.NoError(t, err)
					_, err = w.Write(byteArr)
					require.NoError(t, err)
				}
			}))

			defer mockSvr.Close()
			i, _ := gitlab.New(mockSvr.URL)
			output, _, err := i.GetProjectFiles(ctx, tt.input)
			if tt.expErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.expErr, err)
			} else {
				require.NoError(t, err)
				require.Equal(t, tt.expOutput, output)
			}
		})
	}
}

func TestListProjectCommits(t *testing.T) {
	next := false
	prev := false
	tests := []struct {
		name                string
		input               gitlab.GetProjectCommitsRequest
		mockHttpStatus      int
		mockResponseFail    map[string]interface{}
		mockResponseSuccess []gitlab.RepositoryCommit
		expErr              error
		expOutput           *gitlab.HTTPResponse[[]gitlab.RepositoryCommit]
	}{
		{
			name: "should return error when status code is not 200",
			input: gitlab.GetProjectCommitsRequest{
				Id:      1,
				RefName: "main",
			},
			mockHttpStatus: http.StatusBadRequest,
			expErr:         fmt.Errorf("%v: map[]", gitlab.ErrBadRequestResponse),
		},
		{
			name: "success",
			input: gitlab.GetProjectCommitsRequest{
				Id:      1,
				RefName: "main",
			},
			mockHttpStatus: http.StatusOK,
			mockResponseSuccess: []gitlab.RepositoryCommit{
				{
					ID:      "1",
					ShortID: "1234",
				},
			},
			expOutput: &gitlab.HTTPResponse[[]gitlab.RepositoryCommit]{
				Data: &[]gitlab.RepositoryCommit{
					{
						ID:      "1",
						ShortID: "1234",
					},
				},
				Pagination: &gitlab.Pagination{
					Next:     &next,
					Previous: &prev,
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			mockSvr := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				w.WriteHeader(tt.mockHttpStatus)
				if tt.mockHttpStatus == http.StatusOK {
					byteArr, err := json.Marshal(tt.mockResponseSuccess)
					require.NoError(t, err)
					_, err = w.Write(byteArr)
					require.NoError(t, err)
				} else {
					byteArr, err := json.Marshal(tt.mockResponseFail)
					require.NoError(t, err)
					_, err = w.Write(byteArr)
					require.NoError(t, err)
				}
			}))

			defer mockSvr.Close()
			i, _ := gitlab.New(mockSvr.URL)
			output, err := i.GetProjectCommits(ctx, tt.input)
			if tt.expErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.expErr, err)
			} else {
				require.NoError(t, err)
				require.Equal(t, tt.expOutput, output)
			}
		})
	}
}

func TestListProjectContributors(t *testing.T) {
	tests := []struct {
		name                string
		input               gitlab.GetProjectContributorsRequest
		mockHttpStatus      int
		mockResponseFail    map[string]interface{}
		mockResponseSuccess []gitlab.RepositoryContributor
		expErr              error
		expOutput           []gitlab.RepositoryContributor
	}{
		{
			name: "should return error when status code is not 200",
			input: gitlab.GetProjectContributorsRequest{
				Id: 1,
			},
			mockHttpStatus: http.StatusBadRequest,
			expErr:         fmt.Errorf("%v: map[]", gitlab.ErrBadRequestResponse),
		},
		{
			name: "success",
			input: gitlab.GetProjectContributorsRequest{
				Id: 1,
			},
			mockHttpStatus: http.StatusOK,
			mockResponseSuccess: []gitlab.RepositoryContributor{
				{
					Name:  "admin",
					Email: "<EMAIL>",
				},
			},
			expOutput: []gitlab.RepositoryContributor{
				{
					Name:  "admin",
					Email: "<EMAIL>",
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			mockSvr := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				w.WriteHeader(tt.mockHttpStatus)
				if tt.mockHttpStatus == http.StatusOK {
					byteArr, err := json.Marshal(tt.mockResponseSuccess)
					require.NoError(t, err)
					_, err = w.Write(byteArr)
					require.NoError(t, err)
				} else {
					byteArr, err := json.Marshal(tt.mockResponseFail)
					require.NoError(t, err)
					_, err = w.Write(byteArr)
					require.NoError(t, err)
				}
			}))

			defer mockSvr.Close()
			i, _ := gitlab.New(mockSvr.URL)
			output, err := i.GetProjectContributors(ctx, tt.input)
			if tt.expErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.expErr, err)
			} else {
				require.NoError(t, err)
				require.Equal(t, tt.expOutput, output)
			}
		})
	}
}

func TestCreateProjectFromTemplate(t *testing.T) {

	var sourceProjectId int64 = 1
	tests := []struct {
		name                    string
		input                   gitlab.CreateProjectRequest
		mockResponseBodySuccess gitlab.CreateProjectResponse
		mockResponseBodyFail    map[string]interface{}
		mockHttpStatus          int
		expErr                  error
		expOutput               *gitlab.CreateProjectResponse
	}{
		{
			name: "should return error when status code is not 201",
			input: gitlab.CreateProjectRequest{
				Name:            "Test Project",
				SourceProjectId: &sourceProjectId,
			},
			mockHttpStatus: http.StatusBadRequest,
			mockResponseBodyFail: map[string]interface{}{
				"message": "error",
			},
			expErr: fmt.Errorf("%v: map[message:error]", gitlab.ErrBadRequestResponse),
		},
		{
			name: "should return error when status code is 201",
			input: gitlab.CreateProjectRequest{
				Name:            "Test Project",
				SourceProjectId: &sourceProjectId,
			},
			mockHttpStatus: http.StatusCreated,
			mockResponseBodySuccess: gitlab.CreateProjectResponse{
				Id:   1,
				Name: "Test Project",
			},
			expOutput: &gitlab.CreateProjectResponse{
				Id:   1,
				Name: "Test Project",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			mockSvr := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				w.WriteHeader(tt.mockHttpStatus)
				if tt.mockHttpStatus == http.StatusCreated {
					byteArr, err := json.Marshal(tt.mockResponseBodySuccess)
					require.NoError(t, err)
					_, err = w.Write(byteArr)
					require.NoError(t, err)
				} else {
					byteArr, err := json.Marshal(tt.mockResponseBodyFail)
					require.NoError(t, err)
					_, err = w.Write(byteArr)
					require.NoError(t, err)
				}
			}))

			defer mockSvr.Close()
			i, _ := gitlab.New(mockSvr.URL)
			output, err := i.CreateProjectFromTemplate(ctx, tt.input)
			if tt.expErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.expErr, err)
			} else {
				require.NoError(t, err)
				require.Equal(t, tt.expOutput, output)
			}
		})
	}
}
