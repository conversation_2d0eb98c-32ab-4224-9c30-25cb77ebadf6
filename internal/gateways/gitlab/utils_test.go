package gitlab

import "testing"

func TestGenerateUsername(t *testing.T) {
	tests := []struct {
		email       string
		expected    string
		expectError bool
	}{
		// Valid cases
		{"<EMAIL>", "john_doe", false},
		{"<EMAIL>", "user-name", false},
		{"<EMAIL>", "user_name", false},
		{"us!er$<EMAIL>", "us_er_name", false},
		{"<EMAIL>", "123_user", false},
		{"<EMAIL>", "UPPER_case-user", false},

		// Edge cases
		{"!@example.com", "_", false},
		{"...@example.com", "___", false},
		{"@@example.com", "", false},

		// Invalid cases
		{"no-at-symbol.com", "", true},
		{"", "", true},
	}

	for _, tt := range tests {
		result, err := GenerateUsername(tt.email)

		if tt.expectError {
			if err == nil {
				t.<PERSON><PERSON><PERSON>("expected error for email %q, got nil", tt.email)
			}
		} else {
			if err != nil {
				t.Errorf("unexpected error for email %q: %v", tt.email, err)
			}
			if result != tt.expected {
				t.Errorf("for email %q, expected %q, got %q", tt.email, tt.expected, result)
			}
		}
	}
}
