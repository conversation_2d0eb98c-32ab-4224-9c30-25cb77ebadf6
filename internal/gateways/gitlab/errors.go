package gitlab

import (
	"errors"
	"net/http"
)

type GitlabError struct {
	StatusCode int
	Message    string
	Data       map[string]any
}

func (e *GitlabError) Error() string {
	return e.Message
}

var (
	ErrBadRequestResponse = errors.New("gitlab: the request is invalid or malformed")
	ErrUnauthorized       = errors.New("gitlab: unauthorized")
	ErrForbidden          = errors.New("gitlab: forbidden")
	ErrNotFound           = errors.New("gitlab: not found")
	ErrBranchNotFound     = errors.New("gitlab: branch not found or repository has not been initialized")
	UnknownError          = errors.New("gitlab: unknown error")
)

func handleErrors(statusCode int) error {
	switch statusCode {
	case http.StatusBadRequest:
		return ErrBadRequestResponse
	case http.StatusUnauthorized:
		return ErrUnauthorized
	case http.StatusForbidden:
		return ErrForbidden
	case http.StatusNotFound:
		return ErrNotFound
	default:
		return UnknownError
	}
}
