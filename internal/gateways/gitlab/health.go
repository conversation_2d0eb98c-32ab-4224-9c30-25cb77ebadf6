package gitlab

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
)

// Health checks the health status of the GitLab instance.
// This function verifies if the GitLab instance is accessible and if the provided token is valid.
// If no token is provided, it checks if the instance is accessible without authentication.
// If a token is provided, it verifies if the token is valid.
//
// Parameters:
//   - ctx: Context for the operation
//   - token: Authentication token to verify (can be empty for unauthenticated check)
//
// Returns:
//   - bool: True if the GitLab instance is healthy and accessible, false otherwise
//   - error: Any error that occurred during the health check
//
// Reference: https://docs.gitlab.com/ee/api/version.html
func (i *impl) Health(ctx context.Context, token string) (bool, error) {
	url := fmt.Sprintf("%s/version", i.apiUrl)
	resp, err := i.newRequest(ctx, http.MethodGet, url, nil, token)
	if err != nil {
		return false, err
	}

	defer resp.Body.Close()
	if token == "" && resp.StatusCode == http.StatusUnauthorized {
		return true, nil
	}

	if token != "" && resp.StatusCode == http.StatusOK {
		return true, nil
	}

	var mapResp map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&mapResp); err != nil {
		return false, err
	}

	err = handleErrors(resp.StatusCode)
	return false, fmt.Errorf("%v: %v", err, mapResp)
}
