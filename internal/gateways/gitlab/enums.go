package gitlab

type ActionType string

const (
	ActionCreate ActionType = "create"
	ActionUpdate ActionType = "update"
	ActionDelete ActionType = "delete"
	ActionMove   ActionType = "move"
	ActionChmod  ActionType = "chmod"
)

// Reference: https://docs.gitlab.com/ee/api/access_requests.html#valid-access-levels
type OrganizationAccessLevel int

const (
	OrganizationAccessLevel_Owner     = 50
	OrganizationAccessLevel_Developer = 30
)

type UserState string

const (
	UserActive      UserState = "active"
	UserBlocked     UserState = "blocked"
	UserDeactivated UserState = "deactivated"
	UserBanned      UserState = "banned"
)
