package gitlab

import (
	"mime/multipart"
	"time"

	"api-server/internal/enums"
)

type DeleteOrganizationRequest struct {
	Id    int    `json:"id"`
	Token string `json:"token"`
}

type CreateSSHKeyRequest struct {
	UserId    int64           `json:"user_id" required:"true"`
	Title     string          `json:"title" required:"true"`
	Key       string          `json:"key" required:"true"`
	ExpiresAt *string         `json:"expires_at,omitempty"`
	UsageType enums.UsageType `json:"usage_type"`
	Token     string          `json:"token" required:"true"`
}

type CreateSSHKeyResponse struct {
	Id        int64           `json:"id"`
	Title     string          `json:"title" required:"true"`
	Key       string          `json:"key" required:"true"`
	ExpiresAt *string         `json:"expires_at,omitempty"` // Expiration date of the SSH key in ISO 8601 format (YYYY-MM-DDTHH:MM:SSZ)
	UsageType enums.UsageType `json:"usage_type"`           // Usage type of the SSH key. Possible values are: "auth", "signing", "auth_and_signing". Default: auth_and_signing
}

type DeleteSSHKeyRequest struct {
	KeyId int64  `json:"key_id" required:"true"`
	Token string `json:"token" required:"true"`
}

type CreateGroupRequest struct {
	Name     string                `form:"name"`
	Path     string                `form:"path"`
	ParentID int64                 `form:"parent_id"`
	Token    string                `json:"token"`
	Avatar   *multipart.FileHeader `form:"avatar"`
}

type GitlabGroup struct {
	Id        int    `json:"id"`
	Name      string `json:"name"`
	Path      string `json:"path"`
	AvatarUrl string `json:"avatar_url"`
}

type UpdateGroupRequest struct {
	Id                              int                             `json:"id"`
	Token                           string                          `json:"token"`
	Visibility                      string                          `json:"visibility"`
	DefaultBranchProtectionDefaults DefaultBranchProtectionDefaults `json:"default_branch_protection_defaults"`
}
type AccessLevel struct {
	AccessLevel OrganizationAccessLevel `json:"access_level"`
}
type DefaultBranchProtectionDefaults struct {
	AllowedToPush  []AccessLevel `json:"allowed_to_push"`
	AllowedToMerge []AccessLevel `json:"allowed_to_merge"`
	AllowForcePush bool          `json:"allow_force_push"`
}
type GetGroupRequest struct {
	Token string `json:"token"`
	Path  string `json:"path"`
}

type CreateProjectRequest struct {
	Name            string `json:"name"`
	Path            string `json:"path"`
	Visibility      string `json:"visibility"`
	NamespaceId     *int64 `json:"namespace_id"`
	Token           string `json:"token"`
	SourceProjectId *int64 `json:"source_project_id"`
}

type CreateProjectResponse struct {
	Id          int64                `json:"id"`
	Name        string               `json:"name"`
	Namespace   string               `json:"name_with_namespace"`
	Visibility  enums.RepoVisibility `json:"visibility"`
	NamespaceId string               `json:"namespace_id"`
}

type DeleteProjectRequest struct {
	Id    int64  `json:"id"`
	Token string `json:"token"`
}

type UpdateProjectRequest struct {
	Id     int64   `json:"id"`
	Token  string  `json:"token"`
	Avatar *string `json:"avatar"`
}

type GetProjectURLsRequest struct {
	Id    int64  `json:"id"`
	Token string `json:"token"`
}

type GetProjectURLsResponse struct {
	DefaultBranch string `json:"default_branch"`
	SSHUrl        string `json:"ssh_url_to_repo"`
	HTTPUrl       string `json:"http_url_to_repo"`
	WebUrl        string `json:"web_url"`
	ReadmeUrl     string `json:"readme_url"`
}

type CreatePersonalAccessTokenRequest struct {
	Name       string       `json:"name"`
	UserId     int64        `json:"user_id"`
	Scopes     []TokenScope `json:"scopes"`
	AdminToken string       `json:"token"`
}

type CreatePersonalAccessTokenResponse struct {
	Id        int64    `json:"id"`
	Name      string   `json:"name"`
	Revoked   bool     `json:"revoked"`
	Scopes    []string `json:"scopes"`
	UserId    int64    `json:"user_id"`
	Active    bool     `json:"active"`
	ExpiredAt string   `json:"expires_at"`
	Token     string   `json:"token"`
}

type GetFileRequest struct {
	ProjectId int64  `json:"project_id"`
	FilePath  string `json:"file_path"`
	Ref       string `json:"ref"`
	Token     string `json:"token"`
	LFS       *bool
}

type GetFileResponse struct {
	Content       string `json:"content"`
	FileName      string `json:"file_name"`
	FilePath      string `json:"file_path"`
	Size          int64  `json:"size"`
	Encoding      string `json:"encoding"`
	ContentSHA256 string `json:"content_sha256"`
	Ref           string `json:"ref"`
	BlobId        string `json:"blob_id"`
	CommitId      string `json:"commit_id"`
	LastCommitId  string `json:"last_commit_id"`
}

type UploadAvatarRepositoryRequest struct {
	ProjectId int64                 `json:"project_id"`
	Avatar    *multipart.FileHeader `json:"avatar"` // This represents the avatar file to be uploaded
	Token     string                `json:"token"`  // PRIVATE-TOKEN
}

type UploadAvatarRepositoryResponse struct {
	AvatarUrl string `json:"avatar_url"`
}

type AddUserToGroupRequest struct {
	GroupId     int64   `json:"group_id"`
	UserId      int64   `json:"user_id"`
	AccessLevel int     `json:"access_level"`
	Token       string  `json:"token"`
	ExpiresAt   *string `json:"expires_at,omitempty"`
}

type AddUserToGroupResponse struct {
	Status string `json:"status"`
}

type GetProjectBranchesRequest struct {
	Id    int64  `json:"id"`
	Token string `json:"token"`
}

type GetSingleProjectBrancheRequest struct {
	Id     int64  `json:"id"`
	Branch string `json:"branch"`
	Token  string `json:"token"`
}

type GetProjectBranchesResponse []RepositoryBranchInfo

type GetProjectFilesRequest struct {
	Id        int64  `json:"id"`
	Ref       string `json:"ref"`
	Path      string `json:"path"`
	Recursive bool   `json:"recursive"`
	Token     string `json:"token"` // Gitlab access token

	// for key-set pagination
	PageToken *string `json:"page_token"`
	PerPage   int     `json:"per_page"`

	// get all files without pagination
	All bool
}

type PaginateRequest struct {
	Page    int `json:"page"`
	PerPage int `json:"per_page"`
}

type GetProjectCommitsRequest struct {
	Id       int64  `json:"id"`
	RefName  string `json:"ref_name"`
	Path     string `json:"path"`
	Token    string `json:"token"`
	Paginate PaginateRequest
}

type CreateProjectCommitRequest struct {
	Id    int64  `json:"id"`
	Token string `json:"token"`
	CreateCommitInput
}

type CreateCommitInput struct {
	Branch        string         `json:"branch" binding:"required"`
	CommitMessage string         `json:"commit_message" binding:"required"`
	Actions       []GitLabAction `json:"actions" binding:"required"`
	StartBranch   *string        `json:"start_branch,omitempty"`
	StartSha      *string        `json:"start_sha,omitempty"`
	StartProject  *int           `json:"start_project,omitempty"`
	AuthorEmail   *string        `json:"author_email,omitempty"`
	AuthorName    *string        `json:"author_name,omitempty"`
	Stats         *bool          `json:"stats,omitempty"`
	Force         *bool          `json:"force,omitempty"`
}

type GitLabAction struct {
	Action          ActionType `json:"action" binding:"required,Enum=enums.ActionType"`
	FilePath        string     `json:"file_path"`
	PreviousPath    *string    `json:"previous_path,omitempty"`
	Content         *string    `json:"content,omitempty"`
	Encoding        *string    `json:"encoding,omitempty"`
	ExecuteFileMode *bool      `json:"execute_filemode,omitempty"`
}

type GetSingleProjectCommitRequest struct {
	Id    int64  `json:"id"`
	Sha   string `json:"sha"`
	Token string `json:"token"`
}

type GetProjectContributorsRequest struct {
	Id    int64  `json:"id"`
	Ref   string `json:"ref"`
	Token string `json:"token"`
}

type CreateProjectAccessTokenRequest struct {
	Name      string                       `json:"name"`
	RepoID    int64                        `json:"repo_id"`
	Scopes    []enums.RepoAccessTokenScope `json:"scopes"`
	UserToken string                       `json:"token"`
	ExpiresAt string                       `json:"expires_at"`
}

type CreateProjectAccessTokenResponse struct {
	Id        int64    `json:"id"`
	Name      string   `json:"name"`
	Revoked   bool     `json:"revoked"`
	Scopes    []string `json:"scopes"`
	Active    bool     `json:"active"`
	ExpiredAt string   `json:"expires_at"`
	Token     string   `json:"token"`
}

type DeleteProjectAccessTokenRequest struct {
	UserToken string `json:"token"`
	RepoID    int64  `json:"repo_id"`
	ID        int64  `json:"id"`
}

type RemoveUserFromGroupRequest struct {
	GroupId int64  `json:"group_id"`
	UserId  int64  `json:"user_id"`
	Token   string `json:"token"`
}

type RemoveUserFromProjectRequest struct {
	RepoId int64  `json:"repo_id"`
	UserId int64  `json:"user_id"`
	Token  string `json:"token"`
}

type RemoveUserFromGroupResponse struct {
	Status string `json:"status"`
}

type EditUserInGroupRequest struct {
	GroupId     int64   `json:"group_id"`
	UserId      int64   `json:"user_id"`
	AccessLevel int     `json:"access_level"`
	Token       string  `json:"token"`
	ExpiresAt   *string `json:"expires_at,omitempty"`
}

type EditUserInGroupResponse struct {
	Status string `json:"status"`
}

type AddUserToProjectRequest struct {
	RepoId      int64   `json:"repo_id"`
	UserId      int64   `json:"user_id"`
	AccessLevel int     `json:"access_level"`
	Token       string  `json:"token"`
	ExpiresAt   *string `json:"expires_at,omitempty"`
}

type AddUserToProjectResponse struct {
	Status string `json:"status"`
}

type EditUserInProjectRequest struct {
	RepoId      int64   `json:"repo_id"`
	UserId      int64   `json:"user_id"`
	AccessLevel int     `json:"access_level"`
	Token       string  `json:"token"`
	ExpiresAt   *string `json:"expires_at,omitempty"`
}

type EditUserInProjectResponse struct {
	Status string `json:"status"`
}

type CreateProjectWebhookRequest struct {
	Id                     int64  `json:"id"`
	Token                  string `json:"token"`
	Name                   string `json:"name"`
	Url                    string `json:"url"`
	PushEvent              bool   `json:"push_events"`
	PushEventsBranchFilter string `json:"push_events_branch_filter"`
}

type DownloadProjectRequest struct {
	Id    int64  `json:"id"`
	Type  string `json:"string"`
	Token string `json:"token"`
	Sha   string `json:"sha"`
}

type GetMergeRequestsRequest struct {
	Id       int64  `json:"id"`
	CommitId string `json:"commit_id"`
	Token    string `json:"token"`
}

type MergeRequest struct {
	ID            int       `json:"id"`
	Title         string    `json:"title"`
	Description   string    `json:"description"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
	TargetBranch  string    `json:"target_branch"`
	SourceBranch  string    `json:"source_branch"`
	SHA           string    `json:"sha"`
	Reference     string    `json:"reference"`
	Squash        bool      `json:"squash"`
	SquashOnMerge bool      `json:"squash_on_merge"`
}

type GetMemberOfGroupRequest struct {
	GroupId int64  `json:"group_id"`
	UserId  int64  `json:"user_id"`
	Token   string `json:"token"`
}

type GetMemberOfProjectRequest struct {
	RepoId int64  `json:"repo_id"`
	UserId int64  `json:"user_id"`
	Token  string `json:"token"`
}

type GetMemberOfProjectResponse struct {
	Id     int    `json:"id"`
	Status string `json:"status"`
}

type GetMemberOfGroupResponse struct {
	Id     int    `json:"id"`
	Status string `json:"status"`
}

type RepositoryBranchInfo struct {
	RepositoryBranch
	Commit RepositoryCommit `json:"commit"`
}

type RepositoryBranch struct {
	Name               string `json:"name"`
	Protected          bool   `json:"protected"`
	Default            bool   `json:"default"`
	DevelopersCanPush  bool   `json:"developers_can_push"`
	DevelopersCanMerge bool   `json:"developers_can_merge"`
	CanPush            bool   `json:"can_push"`
}

type RepositoryCommit struct {
	ID             string   `json:"id"`
	ShortID        string   `json:"short_id,omitempty"`
	CreatedAt      string   `json:"created_at,omitempty"`
	ParentIDs      []string `json:"parent_ids"`
	Title          string   `json:"title,omitempty"`
	Message        string   `json:"message"`
	AuthorName     string   `json:"author_name"`
	AuthorEmail    string   `json:"author_email"`
	AuthoredDate   string   `json:"authored_date"`
	CommitterName  string   `json:"committer_name"`
	CommitterEmail string   `json:"committer_email"`
	Status         *string  `json:"status,omitempty"`
}

type RepositoryFile struct {
	ID   string `json:"id"`
	Name string `json:"name"`
	Type string `json:"type"`
	Path string `json:"path"`
	Mode string `json:"mode"`
}

type RepositoryContributor struct {
	Name    string  `json:"name"`
	Email   string  `json:"email"`
	Commits int     `json:"commits"`
	Avatar  *string `json:"avatar,omitempty"`
}
