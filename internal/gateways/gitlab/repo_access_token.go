package gitlab

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
)

// CreateRepoPersonalAccessToken creates a new project access token for a GitLab repository.
// This function creates a token with the specified scopes and name for the given repository.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: CreateProjectAccessTokenRequest containing repository ID, token name, scopes, and user token
//
// Returns:
//   - *CreateProjectAccessTokenResponse: The created token information if successful
//   - error: Any error that occurred during token creation
//
// Reference: https://docs.gitlab.com/ee/api/project_access_tokens.html#create-a-project-access-token
func (i *impl) CreateRepoPersonalAccessToken(ctx context.Context, input CreateProjectAccessTokenRequest) (*CreateProjectAccessTokenResponse, error) {
	url := fmt.Sprintf("%s/projects/%d/access_tokens", i.apiUrl, input.RepoID)
	resp, err := i.newRequest(ctx, http.MethodPost, url, input, input.UserToken)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusCreated {
		token := &CreateProjectAccessTokenResponse{}
		err = json.NewDecoder(resp.Body).Decode(token)
		if err != nil {
			return nil, err
		}
		return token, nil
	}

	var mapResp map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&mapResp); err != nil {
		return nil, err
	}

	return nil, fmt.Errorf("%v: %v", handleErrors(resp.StatusCode), mapResp)
}

// DeleteRepoPersonalAccessToken deletes a project access token from a GitLab repository.
// This function revokes the specified access token, making it invalid for future use.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: DeleteProjectAccessTokenRequest containing repository ID, token ID, and user token
//
// Returns:
//   - error: Any error that occurred during token deletion
//
// Reference: https://docs.gitlab.com/ee/user/project/settings/project_access_tokens.html#revoke-a-project-access-token
func (i *impl) DeleteRepoPersonalAccessToken(ctx context.Context, input DeleteProjectAccessTokenRequest) error {
	url := fmt.Sprintf("%s/projects/%d/access_tokens/%d", i.apiUrl, input.RepoID, input.ID)
	resp, err := i.newRequest(ctx, http.MethodDelete, url, input, input.UserToken)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusNoContent {
		return nil
	}

	var mapResp map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&mapResp); err != nil {
		return err
	}

	return fmt.Errorf("%v: %v", handleErrors(resp.StatusCode), mapResp)
}
