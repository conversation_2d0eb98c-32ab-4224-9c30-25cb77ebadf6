package gitlab_test

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/require"

	"api-server/internal/gateways/gitlab"
)

func TestCreateGroup(t *testing.T) {
	tests := []struct {
		name                    string
		input                   gitlab.CreateGroupRequest
		mockResponseBodySuccess gitlab.GitlabGroup
		mockResponseBodyFail    map[string]interface{}
		mockHttpStatus          int
		expErr                  error
		expOutput               *gitlab.GitlabGroup
	}{
		{
			name: "should return error when status code is not 201",
			input: gitlab.CreateGroupRequest{
				Name: "Test Organization",
			},
			mockHttpStatus: http.StatusBadRequest,
			mockResponseBodyFail: map[string]interface{}{
				"message": "error",
			},
			expErr: fmt.Errorf("%v: map[message:error]", gitlab.ErrBadRequestResponse),
		},
		{
			name: "should return error when status code is 201",
			input: gitlab.CreateGroupRequest{
				Name: "Test Organization",
			},
			mockHttpStatus: http.StatusCreated,
			mockResponseBodySuccess: gitlab.GitlabGroup{
				Id:   1,
				Name: "Test Organization",
			},
			expOutput: &gitlab.GitlabGroup{
				Id:   1,
				Name: "Test Organization",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			mockSvr := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				w.WriteHeader(tt.mockHttpStatus)
				if tt.mockHttpStatus == http.StatusCreated {
					byteArr, err := json.Marshal(tt.mockResponseBodySuccess)
					require.NoError(t, err)
					_, err = w.Write(byteArr)
				} else {
					byteArr, err := json.Marshal(tt.mockResponseBodyFail)
					require.NoError(t, err)
					_, err = w.Write(byteArr)
				}
			}))
			defer mockSvr.Close()

			url := mockSvr.URL
			g, _ := gitlab.New(url)

			output, err := g.CreateGroup(ctx, tt.input)
			if tt.expErr != nil {
				require.EqualError(t, err, tt.expErr.Error())
			} else {
				require.NoError(t, err)
				require.Equal(t, tt.expOutput, output)
			}
		})
	}
}

func TestDeleteGroup(t *testing.T) {
	tests := []struct {
		name             string
		input            gitlab.DeleteOrganizationRequest
		mockHttpStatus   int
		mockResponseBody map[string]interface{}
		expErr           error
	}{
		{
			name: "should return error when status code is not 202",
			input: gitlab.DeleteOrganizationRequest{
				Id: 1,
			},
			mockHttpStatus: http.StatusBadRequest,
			mockResponseBody: map[string]interface{}{
				"message": "error",
			},
			expErr: fmt.Errorf("%v: map[message:error]", gitlab.ErrBadRequestResponse),
		},
		{
			name: "should return error when status code is 202",
			input: gitlab.DeleteOrganizationRequest{
				Id: 1,
			},
			mockHttpStatus: http.StatusAccepted,
			expErr:         nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			mockSvr := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				w.WriteHeader(tt.mockHttpStatus)
				byteArr, err := json.Marshal(tt.mockResponseBody)
				require.NoError(t, err)
				_, err = w.Write(byteArr)
			}))
			defer mockSvr.Close()

			url := mockSvr.URL
			g, _ := gitlab.New(url)

			err := g.DeleteGroup(ctx, tt.input)
			if tt.expErr != nil {
				require.EqualError(t, err, tt.expErr.Error())
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func TestCreateUser(t *testing.T) {
	tests := []struct {
		name                    string
		input                   gitlab.CreateUserRequest
		mockResponseBodySuccess gitlab.CreateUserResponse
		mockResponseBodyFail    map[string]interface{}
		mockHttpStatus          int
		expErr                  error
		expOutput               *gitlab.CreateUserResponse
	}{
		{
			name: "should return error when status code is not 201",
			input: gitlab.CreateUserRequest{
				Email: "",
			},
			mockHttpStatus:       http.StatusBadRequest,
			mockResponseBodyFail: map[string]interface{}{},
			expErr:               fmt.Errorf("%v: map[]", gitlab.ErrBadRequestResponse),
		},
		{
			name: "should return error when status code is 201",
			input: gitlab.CreateUserRequest{
				Email: "",
			},
			mockHttpStatus: http.StatusCreated,
			mockResponseBodySuccess: gitlab.CreateUserResponse{
				Id: 1,
			},
			expOutput: &gitlab.CreateUserResponse{
				Id: 1,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			mockSvr := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				w.WriteHeader(tt.mockHttpStatus)
				if tt.mockHttpStatus == http.StatusCreated {
					byteArr, err := json.Marshal(tt.mockResponseBodySuccess)
					require.NoError(t, err)
					_, err = w.Write(byteArr)
				} else {
					byteArr, err := json.Marshal(tt.mockResponseBodyFail)
					require.NoError(t, err)
					_, err = w.Write(byteArr)
				}
			}))
			defer mockSvr.Close()

			url := mockSvr.URL
			g, _ := gitlab.New(url)

			output, err := g.CreateUser(ctx, tt.input)
			if tt.expErr != nil {
				require.EqualError(t, err, tt.expErr.Error())
			} else {
				require.NoError(t, err)
				require.Equal(t, tt.expOutput, output)
			}
		})
	}
}

func TestCreatePersonalAccessToken(t *testing.T) {
	tests := []struct {
		name                    string
		input                   gitlab.CreatePersonalAccessTokenRequest
		mockResponseBodySuccess gitlab.CreatePersonalAccessTokenResponse
		mockResponseBodyFail    map[string]interface{}
		mockHttpStatus          int
		expErr                  error
		expOutput               *gitlab.CreatePersonalAccessTokenResponse
	}{
		{
			name: "should return error when status code is not 201",
			input: gitlab.CreatePersonalAccessTokenRequest{
				UserId: 1,
			},
			mockHttpStatus: http.StatusBadRequest,
			mockResponseBodyFail: map[string]interface{}{
				"message": "error",
			},
			expErr: fmt.Errorf("%v: map[message:error]", gitlab.ErrBadRequestResponse),
		},
		{
			name: "should return error when status code is 201",
			input: gitlab.CreatePersonalAccessTokenRequest{
				UserId: 1,
			},
			mockHttpStatus: http.StatusCreated,
			mockResponseBodySuccess: gitlab.CreatePersonalAccessTokenResponse{
				Id: 1,
			},
			expOutput: &gitlab.CreatePersonalAccessTokenResponse{Id: 1},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			mockSvr := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				w.WriteHeader(tt.mockHttpStatus)
				if tt.mockHttpStatus == http.StatusCreated {
					byteArr, err := json.Marshal(tt.mockResponseBodySuccess)
					require.NoError(t, err)
					_, err = w.Write(byteArr)
				} else {
					byteArr, err := json.Marshal(tt.mockResponseBodyFail)
					require.NoError(t, err)
					_, err = w.Write(byteArr)
				}
			}))
			defer mockSvr.Close()

			url := mockSvr.URL
			g, _ := gitlab.New(url)

			output, err := g.CreatePersonalAccessToken(ctx, tt.input)
			if tt.expErr != nil {
				require.EqualError(t, err, tt.expErr.Error())
			} else {
				require.NoError(t, err)
				require.Equal(t, tt.expOutput, output)
			}
		})
	}
}
