package gitlab

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"

	"api-server/pkg/oteltrace"
	"api-server/pkg/otelzap"

	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"
)

// CreateSSHKey adds a new SSH key to a GitLab user account.
// This function creates an SSH key with the specified title, key content, and usage type.
// The usage type can be:
//   - auth: for authentication purposes (cloning, pulling, pushing repositories)
//   - signing: for signing commits and tags
//   - auth_and_signing: for both authentication and signing
//
// Parameters:
//   - ctx: Context for the operation
//   - input: CreateSSHKeyRequest containing key title, key content, usage type, and authentication token
//
// Returns:
//   - *CreateSSHKeyResponse: The created SSH key information if successful
//   - error: Any error that occurred during key creation
//
// Reference: https://docs.gitlab.com/17.4/ee/api/user_keys.html#add-an-ssh-key-to-your-account
func (i *impl) CreateSSHKey(ctx context.Context, input CreateSSHKeyRequest) (*CreateSSHKeyResponse, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "gateways.gitlab.CreateSSHKey",
		trace.WithAttributes(
			attribute.String("action", "CREATE_SSH_KEY"),
			attribute.String("key_name", input.Title),
		))
	defer span.End()

	otelzap.InfoWithContext(ctx, "creating ssh key",
		zap.String("action", "CREATE_SSH_KEY"),
		zap.String("key_name", input.Title))

	apiUrl := fmt.Sprintf("%s/user/keys", i.apiUrl)
	resp, err := i.newRequest(ctx, http.MethodPost, apiUrl, input, input.Token)
	if err != nil {
		span.SetStatus(codes.Error, "failed to create ssh key")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "failed to create ssh key", err,
			zap.String("action", "CREATE_SSH_KEY"),
			zap.String("key_name", input.Title),
			zap.String("status", "failed"))
		return nil, err
	}
	defer resp.Body.Close()

	var mapResp map[string]interface{}
	switch code := resp.StatusCode; code {
	case http.StatusCreated:
		otelzap.InfoWithContext(ctx, "ssh key created successfully",
			zap.String("action", "CREATE_SSH_KEY"),
			zap.String("key_name", input.Title),
			zap.String("status", "success"))
		var output CreateSSHKeyResponse
		err = json.NewDecoder(resp.Body).Decode(&output)
		if err != nil {
			span.SetStatus(codes.Error, "failed to decode create ssh key response body")
			span.RecordError(err)
			span.SetAttributes(attribute.String("status", "failed"))
			otelzap.ErrorWithContext(ctx, "failed to decode create ssh key response body", err,
				zap.String("action", "CREATE_SSH_KEY"),
				zap.String("key_name", input.Title),
				zap.String("status", "failed"))
			return nil, err
		}
		span.AddEvent("ssh key created successfully")
		span.SetStatus(codes.Ok, "ssh key created successfully")
		span.SetAttributes(attribute.String("status", "success"))
		return &output, nil
	case http.StatusBadRequest:
		fallthrough
	case http.StatusUnauthorized:
		fallthrough
	default:
		span.AddEvent("ssh key creation failed", trace.WithAttributes(attribute.Int("status_code", resp.StatusCode)))
		otelzap.InfoWithContext(ctx, "ssh key creation failed",
			zap.String("action", "CREATE_SSH_KEY"),
			zap.String("key_name", input.Title),
			zap.Int("status_code", resp.StatusCode),
			zap.String("status", "failed"))
		err := json.NewDecoder(resp.Body).Decode(&mapResp)
		if err != nil {
			span.SetStatus(codes.Error, "failed to decode error response body")
			span.RecordError(err)
			span.SetAttributes(attribute.String("status", "failed"))
			otelzap.ErrorWithContext(ctx, "failed to decode error response body", err,
				zap.String("action", "CREATE_SSH_KEY"),
				zap.String("key_name", input.Title),
				zap.String("status", "failed"))
			return nil, err
		}
		err = fmt.Errorf("status code error: %s %v", resp.Status, mapResp)
		span.SetStatus(codes.Error, "gitlab API error")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "gitlab API error", err,
			zap.String("action", "CREATE_SSH_KEY"),
			zap.String("key_name", input.Title),
			zap.String("status", "failed"))
		return nil, err
	}
}

// DeleteSSHKey removes an SSH key from a GitLab user account.
// This function permanently deletes the specified SSH key, making it invalid for future use.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: DeleteSSHKeyRequest containing key ID and authentication token
//
// Returns:
//   - error: Any error that occurred during key deletion
//
// Reference: https://docs.gitlab.com/17.4/ee/api/user_keys.html#delete-an-ssh-key-from-your-account
func (i *impl) DeleteSSHKey(ctx context.Context, input DeleteSSHKeyRequest) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "gateways.gitlab.DeleteSSHKey",
		trace.WithAttributes(
			attribute.String("action", "DELETE_SSH_KEY"),
			attribute.Int64("key_id", int64(input.KeyId)),
		))
	defer span.End()

	otelzap.InfoWithContext(ctx, "deleting ssh key",
		zap.String("action", "DELETE_SSH_KEY"),
		zap.Int64("key_id", int64(input.KeyId)))

	apiUrl := fmt.Sprintf("%s/user/keys/%d", i.apiUrl, input.KeyId)
	resp, err := i.newRequest(ctx, http.MethodDelete, apiUrl, nil, input.Token)
	if err != nil {
		span.SetStatus(codes.Error, "failed to delete ssh key")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "failed to delete ssh key", err,
			zap.String("action", "DELETE_SSH_KEY"),
			zap.Int64("key_id", int64(input.KeyId)),
			zap.String("status", "failed"))
		return err
	}

	defer resp.Body.Close()
	var mapResp map[string]interface{}
	switch code := resp.StatusCode; code {
	case http.StatusNoContent:
		span.AddEvent("ssh key deleted successfully")
		span.SetStatus(codes.Ok, "ssh key deleted successfully")
		span.SetAttributes(attribute.String("status", "success"))
		otelzap.InfoWithContext(ctx, "ssh key deleted successfully",
			zap.String("action", "DELETE_SSH_KEY"),
			zap.Int64("key_id", int64(input.KeyId)),
			zap.String("status", "success"))
		return nil
	case http.StatusNotFound:
		fallthrough
	case http.StatusUnauthorized:
		fallthrough
	case http.StatusForbidden:
		fallthrough
	default:
		span.AddEvent("ssh key deletion failed", trace.WithAttributes(attribute.Int("status_code", resp.StatusCode)))
		otelzap.InfoWithContext(ctx, "ssh key deletion failed",
			zap.String("action", "DELETE_SSH_KEY"),
			zap.Int64("key_id", int64(input.KeyId)),
			zap.Int("status_code", resp.StatusCode),
			zap.String("status", "failed"))
		err := json.NewDecoder(resp.Body).Decode(&mapResp)
		if err != nil {
			span.SetStatus(codes.Error, "failed to decode error response body")
			span.RecordError(err)
			span.SetAttributes(attribute.String("status", "failed"))
			otelzap.ErrorWithContext(ctx, "failed to decode error response body", err,
				zap.String("action", "DELETE_SSH_KEY"),
				zap.Int64("key_id", int64(input.KeyId)),
				zap.String("status", "failed"))
			return err
		}

		err = fmt.Errorf("status code error: %s %v", resp.Status, mapResp)
		span.SetStatus(codes.Error, "gitlab API error")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "gitlab API error", err,
			zap.String("action", "DELETE_SSH_KEY"),
			zap.Int64("key_id", int64(input.KeyId)),
			zap.String("status", "failed"))
		return err
	}
}
