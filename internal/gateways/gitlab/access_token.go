package gitlab

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"

	"api-server/pkg/oteltrace"
	"api-server/pkg/otelzap"

	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"
)

// TokenScope represents the scope of permissions for a GitLab personal access token.
// Each scope defines a specific set of permissions that the token can have.
type TokenScope string

const (
	// ScopeAPI provides full API access
	ScopeAPI TokenScope = "api"
	// ScopeReadUser allows reading user information
	ScopeReadUser TokenScope = "read_user"
	// ScopeReadAPI allows reading API resources
	ScopeReadAPI TokenScope = "read_api"
	// ScopeReadRepository allows reading repository contents
	ScopeReadRepository TokenScope = "read_repository"
	// ScopeWriteRepository allows writing to repositories
	ScopeWriteRepository TokenScope = "write_repository"
	// ScopeReadRegistry allows reading from the container registry
	ScopeReadRegistry TokenScope = "read_registry"
	// ScopeWriteRegistry allows writing to the container registry
	ScopeWriteRegistry TokenScope = "write_registry"
	// ScopeSudo allows impersonating other users
	ScopeSudo TokenScope = "sudo"
	// ScopeAdminMode allows accessing admin features
	ScopeAdminMode TokenScope = "admin_mode"
	// ScopeCreateRunner allows creating GitLab runners
	ScopeCreateRunner TokenScope = "create_runner"
	// ScopeManageRunner allows managing GitLab runners
	ScopeManageRunner TokenScope = "manage_runner"
	// ScopeAIFeatures allows access to AI features
	ScopeAIFeatures TokenScope = "ai_features"
	// ScopeK8sProxy allows access to Kubernetes proxy
	ScopeK8sProxy TokenScope = "k8s_proxy"
	// ScopeReadServicePing allows reading service ping data
	ScopeReadServicePing TokenScope = "read_service_ping"
)

// CreatePersonalAccessToken creates a new personal access token for a GitLab user.
// This function requires administrator privileges to create tokens for other users.
// The token is created with the specified scopes and name.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: CreatePersonalAccessTokenRequest containing user ID, token name, scopes, and admin token
//
// Returns:
//   - *CreatePersonalAccessTokenResponse: The created token information if successful
//   - error: Any error that occurred during token creation
//
// Reference: https://docs.gitlab.com/ee/api/users.html#create-a-personal-access-token-with-limited-scopes-for-the-currently-authenticated-user
func (i *impl) CreatePersonalAccessToken(ctx context.Context, input CreatePersonalAccessTokenRequest) (*CreatePersonalAccessTokenResponse, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "gateways.gitlab.CreatePersonalAccessToken",
		trace.WithAttributes(
			attribute.String("action", "CREATE_PERSONAL_ACCESS_TOKEN"),
			attribute.Int64("user_id", int64(input.UserId)),
			attribute.String("token_name", input.Name),
		))
	defer span.End()

	otelzap.InfoWithContext(ctx, "creating personal access token",
		zap.String("action", "CREATE_PERSONAL_ACCESS_TOKEN"),
		zap.Int64("user_id", int64(input.UserId)),
		zap.String("token_name", input.Name))

	url := fmt.Sprintf("%s/users/%d/personal_access_tokens", i.apiUrl, input.UserId)
	resp, err := i.newRequest(ctx, http.MethodPost, url, input, input.AdminToken)
	if err != nil {
		span.SetStatus(codes.Error, "request failed")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "request failed", err,
			zap.String("action", "CREATE_PERSONAL_ACCESS_TOKEN"),
			zap.Int64("user_id", int64(input.UserId)),
			zap.String("status", "failed"))
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusCreated {
		token := &CreatePersonalAccessTokenResponse{}
		err = json.NewDecoder(resp.Body).Decode(token)
		if err != nil {
			span.SetStatus(codes.Error, "failed to decode response body")
			span.RecordError(err)
			span.SetAttributes(attribute.String("status", "failed"))
			otelzap.ErrorWithContext(ctx, "failed to decode response body", err,
				zap.String("action", "CREATE_PERSONAL_ACCESS_TOKEN"),
				zap.Int64("user_id", int64(input.UserId)),
				zap.String("status", "failed"))
			return nil, err
		}
		span.AddEvent("personal access token created successfully")
		span.SetStatus(codes.Ok, "personal access token created successfully")
		span.SetAttributes(attribute.String("status", "success"))
		otelzap.InfoWithContext(ctx, "personal access token created successfully",
			zap.String("action", "CREATE_PERSONAL_ACCESS_TOKEN"),
			zap.Int64("user_id", int64(input.UserId)),
			zap.String("status", "success"))
		return token, nil
	}

	span.AddEvent("personal access token creation failed", trace.WithAttributes(attribute.Int("status_code", resp.StatusCode)))
	var mapResp map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&mapResp); err != nil {
		span.SetStatus(codes.Error, "failed to decode error response body")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "failed to decode error response body", err,
			zap.String("action", "CREATE_PERSONAL_ACCESS_TOKEN"),
			zap.Int64("user_id", int64(input.UserId)),
			zap.String("status", "failed"))
		return nil, err
	}

	apiErr := fmt.Errorf("%v: %v", handleErrors(resp.StatusCode), mapResp)
	span.SetStatus(codes.Error, "API error")
	span.RecordError(apiErr)
	span.SetAttributes(attribute.String("status", "failed"))
	otelzap.ErrorWithContext(ctx, "API error", apiErr,
		zap.String("action", "CREATE_PERSONAL_ACCESS_TOKEN"),
		zap.Int64("user_id", int64(input.UserId)),
		zap.String("status", "failed"))
	return nil, apiErr
}

// DeletePersonalAccessToken deletes a personal access token from GitLab.
// This function revokes the specified access token, making it invalid for future use.
//
// Parameters:
//   - ctx: Context for the operation
//   - accessToken: The access token to be deleted
//
// Returns:
//   - error: Any error that occurred during token deletion
//
// Reference: https://docs.gitlab.com/ee/api/personal_access_tokens.html#rotate-a-personal-access-token
func (i *impl) DeletePersonalAccessToken(ctx context.Context, accessToken string) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "gateways.gitlab.DeletePersonalAccessToken",
		trace.WithAttributes(
			attribute.String("action", "DELETE_PERSONAL_ACCESS_TOKEN"),
		))
	defer span.End()

	otelzap.InfoWithContext(ctx, "deleting personal access token",
		zap.String("action", "DELETE_PERSONAL_ACCESS_TOKEN"))

	url := fmt.Sprintf("%s/personal_access_tokens/self", i.apiUrl)
	resp, err := i.newRequest(ctx, http.MethodDelete, url, nil, accessToken)
	if err != nil {
		span.SetStatus(codes.Error, "request failed")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "request failed", err,
			zap.String("action", "DELETE_PERSONAL_ACCESS_TOKEN"),
			zap.String("status", "failed"))
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusNoContent {
		span.AddEvent("personal access token deleted successfully")
		span.SetStatus(codes.Ok, "personal access token deleted successfully")
		span.SetAttributes(attribute.String("status", "success"))
		otelzap.InfoWithContext(ctx, "personal access token deleted successfully",
			zap.String("action", "DELETE_PERSONAL_ACCESS_TOKEN"),
			zap.String("status", "success"))
		return nil
	}

	span.AddEvent("personal access token deletion failed", trace.WithAttributes(attribute.Int("status_code", resp.StatusCode)))
	var mapResp map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&mapResp); err != nil {
		span.SetStatus(codes.Error, "failed to decode error response body")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "failed to decode error response body", err,
			zap.String("action", "DELETE_PERSONAL_ACCESS_TOKEN"),
			zap.String("status", "failed"))
		return err
	}

	apiErr := fmt.Errorf("%v: %v", handleErrors(resp.StatusCode), mapResp)
	span.SetStatus(codes.Error, "API error")
	span.RecordError(apiErr)
	span.SetAttributes(attribute.String("status", "failed"))
	otelzap.ErrorWithContext(ctx, "API error", apiErr,
		zap.String("action", "DELETE_PERSONAL_ACCESS_TOKEN"),
		zap.String("status", "failed"))
	return apiErr
}
