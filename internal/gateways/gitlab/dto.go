package gitlab

type HTTPResponse[T any] struct {
	Data *T `json:"data,omitempty"`
	*Pagination
}

type Pagination struct {
	Total    *int  `json:"total,omitempty"`
	Next     *bool `json:"next,omitempty"`
	Previous *bool `json:"previous,omitempty"`
	Page     int   `json:"page"`
	PerPage  int   `json:"per_page"`
}

func NewHTTPResponse[T any]() *HTTPResponse[T] {
	return &HTTPResponse[T]{}
}

func (r *HTTPResponse[T]) WithData(data T) *HTTPResponse[T] {
	r.Data = &data
	return r
}

func (r *HTTPResponse[T]) WithPagination(pagination Pagination) *HTTPResponse[T] {
	r.Pagination = &pagination
	return r
}

type FromEntity[M any, D any] interface {
	FromEntity(m M) D
}

func FromManyEntities[M any, D FromEntity[M, D]](entities []M) []D {
	dtos := make([]D, len(entities))
	for i, e := range entities {
		var data D
		dtos[i] = data.FromEntity(e)
	}
	return dtos
}
