package gitlab

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"net/url"
	"strconv"
	"strings"

	"api-server/internal/utils/linkheader"
	"api-server/pkg/oteltrace"

	"go.opentelemetry.io/otel/codes"
)

// CreateProject creates a new GitLab project with the specified parameters.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: CreateProjectRequest containing project details and authentication token
//
// Returns:
//   - *CreateProjectResponse: The created project information if successful
//   - error: Any error that occurred during project creation
//
// Reference: https://docs.gitlab.com/ee/api/projects.html#create-project
func (i *impl) CreateProject(ctx context.Context, input CreateProjectRequest) (*CreateProjectResponse, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "gateways.gitlab.CreateProject")
	defer span.End()

	url := fmt.Sprintf("%s/projects", i.apiUrl)
	resp, err := i.newRequest(ctx, http.MethodPost, url, input, input.Token)
	if err != nil {
		span.SetStatus(codes.Error, "newRequest failed")
		span.RecordError(err)
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusCreated {
		var output CreateProjectResponse
		err = json.NewDecoder(resp.Body).Decode(&output)
		if err != nil {
			span.SetStatus(codes.Error, "json.NewDecoder failed")
			span.RecordError(err)
			return nil, err
		}
		span.AddEvent("project created successfully")
		span.SetStatus(codes.Ok, "project created successfully")
		return &output, nil
	}

	var mapResp map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&mapResp); err != nil {
		span.SetStatus(codes.Error, "json.NewDecoder failed for error response")
		span.RecordError(err)
		return nil, err
	}

	apiErr := handleErrors(resp.StatusCode)
	err = fmt.Errorf("%v: %v", apiErr, mapResp)
	span.SetStatus(codes.Error, "creating of project failed")
	span.RecordError(err)
	return nil, err
}

// DeleteProject deletes a GitLab project by its ID.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: DeleteProjectRequest containing project ID and authentication token
//
// Returns:
//   - error: Any error that occurred during project deletion
//
// Reference: https://docs.gitlab.com/ee/api/projects.html#remove-project
func (i *impl) DeleteProject(ctx context.Context, input DeleteProjectRequest) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "gateways.gitlab.DeleteProject")
	defer span.End()

	url := fmt.Sprintf("%s/projects/%d", i.apiUrl, input.Id)
	resp, err := i.newRequest(ctx, http.MethodDelete, url, nil, input.Token)
	if err != nil {
		span.SetStatus(codes.Error, "newRequest failed")
		span.RecordError(err)
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusAccepted {
		span.AddEvent("project deletion accepted")
		span.SetStatus(codes.Ok, "project deletion accepted")
		return nil
	}

	var mapResp map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&mapResp); err != nil {
		span.SetStatus(codes.Error, "json.NewDecoder failed")
		span.RecordError(err)
		return err
	}

	apiErr := handleErrors(resp.StatusCode)
	err = fmt.Errorf("%v: %v", apiErr, mapResp)
	span.SetStatus(codes.Error, "delete project failed")
	span.RecordError(err)
	return err
}

// UpdateProject updates an existing GitLab project with new information.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: UpdateProjectRequest containing updated project details and authentication token
//
// Returns:
//   - error: Any error that occurred during project update
//
// Reference: https://docs.gitlab.com/ee/api/projects.html#edit-project
func (i *impl) UpdateProject(ctx context.Context, input UpdateProjectRequest) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "gateways.gitlab.UpdateProject")
	defer span.End()

	url := fmt.Sprintf("%s/projects/%d", i.apiUrl, input.Id)
	resp, err := i.newRequest(ctx, http.MethodPut, url, input, input.Token)
	if err != nil {
		span.SetStatus(codes.Error, "newRequest failed")
		span.RecordError(err)
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusOK {
		span.AddEvent("project updated successfully")
		span.SetStatus(codes.Ok, "project updated successfully")
		return nil
	}

	var mapResp map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&mapResp); err != nil {
		span.SetStatus(codes.Error, "json.NewDecoder failed")
		span.RecordError(err)
		return err
	}

	apiErr := handleErrors(resp.StatusCode)
	err = fmt.Errorf("%v: %v", apiErr, mapResp)
	span.SetStatus(codes.Error, "update project failed")
	span.RecordError(err)
	return err
}

// GetProjectURLs retrieves the URLs (HTTP and SSH) for a GitLab project.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: GetProjectURLsRequest containing project ID and authentication token
//
// Returns:
//   - *GetProjectURLsResponse: The project URLs information if successful
//   - error: Any error that occurred during retrieval
//
// Reference: https://docs.gitlab.com/ee/api/projects.html#get-single-project
func (i *impl) GetProjectURLs(ctx context.Context, input GetProjectURLsRequest) (*GetProjectURLsResponse, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "gateways.gitlab.GetProjectURLs")
	defer span.End()

	url := fmt.Sprintf("%s/projects/%d", i.apiUrl, input.Id)
	resp, err := i.newRequest(ctx, http.MethodGet, url, nil, input.Token)
	if err != nil {
		span.SetStatus(codes.Error, "newRequest failed")
		span.RecordError(err)
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusOK {
		var output GetProjectURLsResponse
		err = json.NewDecoder(resp.Body).Decode(&output)
		if err != nil {
			span.SetStatus(codes.Error, "json.NewDecoder failed")
			span.RecordError(err)
			return nil, err
		}
		output.SSHUrl = strings.Replace(output.SSHUrl, "localhost", i.customGitlabSshHost, 1)

		span.AddEvent("project URLs retrieved successfully")
		span.SetStatus(codes.Ok, "project URLs retrieved successfully")
		return &output, nil
	}

	var mapResp map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&mapResp); err != nil {
		span.SetStatus(codes.Error, "json.NewDecoder failed")
		span.RecordError(err)
		return nil, err
	}

	apiErr := handleErrors(resp.StatusCode)
	err = fmt.Errorf("%v: %v", apiErr, mapResp)
	span.SetStatus(codes.Error, "get project URLs failed")
	span.RecordError(err)
	return nil, err
}

// GetFileFromRepo retrieves information about a specific file in a GitLab repository.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: GetFileRequest containing project ID, file path, reference, and authentication token
//
// Returns:
//   - *GetFileResponse: The file information if successful
//   - error: Any error that occurred during retrieval
//
// Reference: https://docs.gitlab.com/ee/api/repository_files.html#get-file-from-repository
func (i *impl) GetFileFromRepo(ctx context.Context, input GetFileRequest) (*GetFileResponse, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "gateways.gitlab.GetFileFromRepo")
	defer span.End()

	url := fmt.Sprintf("%s/projects/%d/repository/files/%s?ref=%s", i.apiUrl, input.ProjectId, input.FilePath, input.Ref)
	resp, err := i.newRequest(ctx, http.MethodGet, url, nil, input.Token)
	if err != nil {
		span.SetStatus(codes.Error, "newRequest failed")
		span.RecordError(err)
		return nil, err
	}

	if resp.StatusCode == http.StatusOK {
		var output GetFileResponse
		err = json.NewDecoder(resp.Body).Decode(&output)
		if err != nil {
			span.SetStatus(codes.Error, "json.NewDecoder failed")
			span.RecordError(err)
			return nil, err
		}

		span.AddEvent("file retrieved from repository successfully")
		span.SetStatus(codes.Ok, "file retrieved from repository successfully")
		return &output, nil
	}
	if resp.StatusCode == http.StatusNotFound {
		return nil, ErrNotFound
	}

	var mapResp map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&mapResp); err != nil {
		span.SetStatus(codes.Error, "json.NewDecoder failed")
		span.RecordError(err)
		return nil, err
	}

	apiErr := handleErrors(resp.StatusCode)
	err = fmt.Errorf("%v: %v", apiErr, mapResp)
	span.SetStatus(codes.Error, "get file from repo failed")
	span.RecordError(err)
	return nil, err
}

// GetRawFileFromRepo retrieves the raw content of a file from a GitLab repository.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: GetFileRequest containing project ID, file path, reference, and authentication token
//
// Returns:
//   - *http.Response: The raw file content response if successful
//   - error: Any error that occurred during retrieval
//
// Reference: https://docs.gitlab.com/ee/api/repository_files.html#get-raw-file-from-repository
func (i *impl) GetRawFileFromRepo(ctx context.Context, input GetFileRequest) (*http.Response, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "gateways.gitlab.GetRawFileFromRepo")
	defer span.End()

	baseUrl := fmt.Sprintf("%s/projects/%d/repository/files/%s/raw?ref=%s", i.apiUrl, input.ProjectId, input.FilePath, input.Ref)
	if input.LFS != nil && *input.LFS {
		newUrl, err := addQuery(baseUrl, map[string]string{
			"lfs": "true",
		})
		if err != nil {
			span.SetStatus(codes.Error, "error adding new query params")
			span.RecordError(err)
			return nil, err
		}
		baseUrl = newUrl
	}

	resp, err := i.newRequest(ctx, http.MethodGet, baseUrl, nil, input.Token)
	if err != nil {
		span.SetStatus(codes.Error, "newRequest failed")
		span.RecordError(err)
		return nil, err
	}

	if resp.StatusCode == http.StatusOK {
		span.AddEvent("raw file retrieved from repository successfully")
		span.SetStatus(codes.Ok, "raw file retrieved from repository successfully")
		return resp, nil
	}

	var mapResp map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&mapResp); err != nil {
		span.SetStatus(codes.Error, "json.NewDecoder failed")
		span.RecordError(err)
		return nil, err
	}

	apiErr := handleErrors(resp.StatusCode)
	err = fmt.Errorf("%v: %v", apiErr, mapResp)
	span.SetStatus(codes.Error, "get raw file from repo failed")
	span.RecordError(err)
	return nil, err
}

// GetHeaderRawFileFromRepo retrieves the headers of a raw file from a GitLab repository.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: GetFileRequest containing project ID, file path, reference, and authentication token
//
// Returns:
//   - *http.Response: The file headers response if successful
//   - error: Any error that occurred during retrieval
//
// Reference: https://docs.gitlab.com/ee/api/repository_files.html#get-raw-file-from-repository
func (i *impl) GetHeaderRawFileFromRepo(ctx context.Context, input GetFileRequest) (*http.Response, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "gateways.gitlab.GetHeaderRawFileFromRepo")
	defer span.End()

	baseUrl := fmt.Sprintf("%s/projects/%d/repository/files/%s?ref=%s", i.apiUrl, input.ProjectId, input.FilePath, input.Ref)
	if input.LFS != nil && *input.LFS {
		newUrl, err := addQuery(baseUrl, map[string]string{
			"lfs": "true",
		})
		if err != nil {
			span.SetStatus(codes.Error, "error adding new query params")
			span.RecordError(err)
			return nil, err
		}
		baseUrl = newUrl
	}
	resp, err := i.newRequest(ctx, http.MethodHead, baseUrl, nil, input.Token)
	if err != nil {
		span.SetStatus(codes.Error, "newRequest failed")
		span.RecordError(err)
		return nil, err
	}

	if resp.StatusCode == http.StatusOK {
		span.AddEvent("header raw file retrieved from repository successfully")
		span.SetStatus(codes.Ok, "header raw file retrieved from repository successfully")
		return resp, nil
	}

	// Read the response body once
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		span.SetStatus(codes.Error, "failed to read response body")
		span.RecordError(err)
		return nil, err
	}

	var mapResp map[string]interface{}
	if len(body) != 0 {
		if err := json.Unmarshal(body, &mapResp); err != nil {
			span.SetStatus(codes.Error, "json.Unmarshal failed")
			span.RecordError(err)
			return nil, err
		}
	}

	apiErr := handleErrors(resp.StatusCode)
	gitlabErr := &GitlabError{
		StatusCode: resp.StatusCode,
		Message:    apiErr.Error(),
		Data:       mapResp,
	}
	span.SetStatus(codes.Error, gitlabErr.Error())
	span.RecordError(gitlabErr)
	return nil, gitlabErr
}

func addQuery(baseUrl string, queries map[string]string) (string, error) {
	parsedURL, err := url.Parse(baseUrl)
	if err != nil {
		return "", fmt.Errorf("error parsing URL %w", err)
	}
	// Get existing query parameters
	queryParams := parsedURL.Query()
	// Add new parameters
	for k, v := range queries {
		queryParams.Set(k, v)
	}
	// Encode and update the URL with new query params
	parsedURL.RawQuery = queryParams.Encode()
	queryUrl := parsedURL.String()
	return queryUrl, nil
}

// UploadAvatarRepository uploads an avatar image for a GitLab project.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: UploadAvatarRepositoryRequest containing project ID, avatar file, and authentication token
//
// Returns:
//   - *UploadAvatarRepositoryResponse: The updated project information if successful
//   - error: Any error that occurred during upload
//
// Reference: https://docs.gitlab.com/ee/api/projects.html#upload-a-project-avatar
func (i *impl) UploadAvatarRepository(ctx context.Context, input UploadAvatarRepositoryRequest) (*UploadAvatarRepositoryResponse, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "gateways.gitlab.UploadAvatarRepository")
	defer span.End()

	url := fmt.Sprintf("%s/projects/%d", i.apiUrl, input.ProjectId)
	resp, err := i.newRequestUploadAvatarFile(ctx, http.MethodPut, url, &input)
	if err != nil {
		span.SetStatus(codes.Error, "newRequestUploadAvatarFile failed")
		span.RecordError(err)
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusOK {
		var output UploadAvatarRepositoryResponse
		err = json.NewDecoder(resp.Body).Decode(&output)
		if err != nil {
			span.SetStatus(codes.Error, "json.NewDecoder failed")
			span.RecordError(err)
			return nil, err
		}

		span.AddEvent("repository avatar uploaded successfully")
		span.SetStatus(codes.Ok, "repository avatar uploaded successfully")
		return &output, nil
	}

	var mapResp map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&mapResp); err != nil {
		span.SetStatus(codes.Error, "json.NewDecoder failed")
		span.RecordError(err)
		return nil, err
	}

	apiErr := handleErrors(resp.StatusCode)
	err = fmt.Errorf("%v: %v", apiErr, mapResp)
	span.SetStatus(codes.Error, "upload avatar repository failed")
	span.RecordError(err)
	return nil, err
}

func (i *impl) newRequestUploadAvatarFile(ctx context.Context, method string, url string, request *UploadAvatarRepositoryRequest) (*http.Response, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "gateways.gitlab.newRequestUploadAvatarFile")
	defer span.End()

	file, err := request.Avatar.Open()
	if err != nil {
		span.SetStatus(codes.Error, "request.Avatar.Open failed")
		span.RecordError(err)
		return nil, err
	}
	defer file.Close()

	// Create body to store file
	var body bytes.Buffer
	// Create a new writer
	writer := multipart.NewWriter(&body)
	// Create a new form file
	fileWriter, err := writer.CreateFormFile("avatar", request.Avatar.Filename)
	if err != nil {
		span.SetStatus(codes.Error, "writer.CreateFormFile failed")
		span.RecordError(err)
		return nil, err
	}

	// Copy the file to the form file
	_, err = io.Copy(fileWriter, file)
	if err != nil {
		span.SetStatus(codes.Error, "io.Copy failed")
		span.RecordError(err)
		return nil, err
	}

	err = writer.WriteField("project_id", fmt.Sprintf("%d", request.ProjectId))
	if err != nil {
		span.SetStatus(codes.Error, "writer.WriteField failed")
		span.RecordError(err)
		return nil, err
	}

	// Close the writer
	err = writer.Close()
	if err != nil {
		span.SetStatus(codes.Error, "writer.Close failed")
		span.RecordError(err)
		return nil, err
	}

	req, err := http.NewRequestWithContext(ctx, method, url, &body)
	if err != nil {
		span.SetStatus(codes.Error, "http.NewRequestWithContext failed")
		span.RecordError(err)
		return nil, err
	}

	req.Header.Add("Content-Type", writer.FormDataContentType())
	req.Header.Add("PRIVATE-TOKEN", request.Token)

	res, err := i.httpClient.Do(req)
	if err != nil {
		span.SetStatus(codes.Error, "httpClient.Do failed")
		span.RecordError(err)
		return nil, err
	}

	span.AddEvent("upload avatar file request created successfully")
	span.SetStatus(codes.Ok, "upload avatar file request created successfully")
	return res, nil
}

// AddUserToGroup adds a user to a GitLab group with specified access level.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: AddUserToGroupRequest containing group ID, user ID, access level, and authentication token
//
// Returns:
//   - *AddUserToGroupResponse: The group membership information if successful
//   - error: Any error that occurred during the operation
//
// Reference: https://docs.gitlab.com/ee/api/members.html#add-a-member-to-a-group-or-project
func (i *impl) AddUserToGroup(ctx context.Context, input AddUserToGroupRequest) (*AddUserToGroupResponse, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "gateways.gitlab.AddUserToGroup")
	defer span.End()

	url := fmt.Sprintf("%s/groups/%d/members", i.apiUrl, input.GroupId)
	resp, err := i.newRequest(ctx, http.MethodPost, url, input, input.Token)
	if err != nil {
		span.SetStatus(codes.Error, "newRequest failed")
		span.RecordError(err)
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusCreated {
		var output AddUserToGroupResponse
		err = json.NewDecoder(resp.Body).Decode(&output)
		if err != nil {
			span.SetStatus(codes.Error, "json.NewDecoder failed")
			span.RecordError(err)
			return nil, err
		}

		span.AddEvent("user added to group successfully")
		span.SetStatus(codes.Ok, "user added to group successfully")
		return &output, nil
	}

	var mapResp map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&mapResp); err != nil {
		span.SetStatus(codes.Error, "json.NewDecoder failed")
		span.RecordError(err)
		return nil, err
	}

	apiErr := handleErrors(resp.StatusCode)
	err = fmt.Errorf("%v: %v", apiErr, mapResp)
	span.SetStatus(codes.Error, "add user to group failed")
	span.RecordError(err)
	return nil, err
}

// EditUserInGroup updates a user's access level in a GitLab group.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: EditUserInGroupRequest containing group ID, user ID, new access level, and authentication token
//
// Returns:
//   - *EditUserInGroupResponse: The updated group membership information if successful
//   - error: Any error that occurred during the operation
//
// Reference: https://docs.gitlab.com/ee/api/members.html#edit-a-member-of-a-group-or-project
func (i *impl) EditUserInGroup(ctx context.Context, input EditUserInGroupRequest) (*EditUserInGroupResponse, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "gateways.gitlab.EditUserInGroup")
	defer span.End()

	url := fmt.Sprintf("%s/groups/%d/members/%d", i.apiUrl, input.GroupId, input.UserId)
	resp, err := i.newRequest(ctx, http.MethodPut, url, input, input.Token)
	if err != nil {
		span.SetStatus(codes.Error, "newRequest failed")
		span.RecordError(err)
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusOK {
		var output EditUserInGroupResponse
		err = json.NewDecoder(resp.Body).Decode(&output)
		if err != nil {
			span.SetStatus(codes.Error, "json.NewDecoder failed")
			span.RecordError(err)
			return nil, err
		}

		span.AddEvent("user updated in group successfully")
		span.SetStatus(codes.Ok, "user updated in group successfully")
		return &output, nil
	}

	var mapResp map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&mapResp); err != nil {
		span.SetStatus(codes.Error, "json.NewDecoder failed")
		span.RecordError(err)
		return nil, err
	}

	apiErr := handleErrors(resp.StatusCode)
	err = fmt.Errorf("%v: %v", apiErr, mapResp)
	span.SetStatus(codes.Error, "edit user in group failed")
	span.RecordError(err)
	return nil, err
}

// AddUserToProject adds a user to a GitLab project with specified access level.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: AddUserToProjectRequest containing project ID, user ID, access level, and authentication token
//
// Returns:
//   - *AddUserToProjectResponse: The project membership information if successful
//   - error: Any error that occurred during the operation
//
// Reference: https://docs.gitlab.com/ee/api/members.html#add-a-member-to-a-group-or-project
func (i *impl) AddUserToProject(ctx context.Context, input AddUserToProjectRequest) (*AddUserToProjectResponse, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "gateways.gitlab.AddUserToProject")
	defer span.End()

	url := fmt.Sprintf("%s/projects/%d/members", i.apiUrl, input.RepoId)
	resp, err := i.newRequest(ctx, http.MethodPost, url, input, input.Token)
	if err != nil {
		span.SetStatus(codes.Error, "newRequest failed")
		span.RecordError(err)
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusCreated {
		var output AddUserToProjectResponse
		err = json.NewDecoder(resp.Body).Decode(&output)
		if err != nil {
			span.SetStatus(codes.Error, "json.NewDecoder failed")
			span.RecordError(err)
			return nil, err
		}

		span.AddEvent("user added to project successfully")
		span.SetStatus(codes.Ok, "user added to project successfully")
		return &output, nil
	}

	var mapResp map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&mapResp); err != nil {
		span.SetStatus(codes.Error, "json.NewDecoder failed")
		span.RecordError(err)
		return nil, err
	}

	apiErr := handleErrors(resp.StatusCode)
	err = fmt.Errorf("%v: %v", apiErr, mapResp)
	span.SetStatus(codes.Error, "add user to project failed")
	span.RecordError(err)
	return nil, err
}

// EditUserInProject updates a user's access level in a GitLab project.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: EditUserInProjectRequest containing project ID, user ID, new access level, and authentication token
//
// Returns:
//   - *EditUserInProjectResponse: The updated project membership information if successful
//   - error: Any error that occurred during the operation
//
// Reference: https://docs.gitlab.com/ee/api/members.html#edit-a-member-of-a-group-or-project
func (i *impl) EditUserInProject(ctx context.Context, input EditUserInProjectRequest) (*EditUserInProjectResponse, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "gateways.gitlab.EditUserInProject")
	defer span.End()

	url := fmt.Sprintf("%s/projects/%d/members/%d", i.apiUrl, input.RepoId, input.UserId)
	resp, err := i.newRequest(ctx, http.MethodPut, url, input, input.Token)
	if err != nil {
		span.SetStatus(codes.Error, "newRequest failed")
		span.RecordError(err)
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusOK {
		var output EditUserInProjectResponse
		err = json.NewDecoder(resp.Body).Decode(&output)
		if err != nil {
			span.SetStatus(codes.Error, "json.NewDecoder failed")
			span.RecordError(err)
			return nil, err
		}

		span.AddEvent("user updated in project successfully")
		span.SetStatus(codes.Ok, "user updated in project successfully")
		return &output, nil
	}

	var mapResp map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&mapResp); err != nil {
		span.SetStatus(codes.Error, "json.NewDecoder failed")
		span.RecordError(err)
		return nil, err
	}

	apiErr := handleErrors(resp.StatusCode)
	err = fmt.Errorf("%v: %v", apiErr, mapResp)
	span.SetStatus(codes.Error, "edit user in project failed")
	span.RecordError(err)
	return nil, err
}

// RemoveUserFromGroup removes a user from a GitLab group.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: RemoveUserFromGroupRequest containing group ID, user ID, and authentication token
//
// Returns:
//   - error: Any error that occurred during the operation
//
// Reference: https://docs.gitlab.com/ee/api/members.html#remove-a-member-from-a-group-or-project
func (i *impl) RemoveUserFromGroup(ctx context.Context, input RemoveUserFromGroupRequest) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "gateways.gitlab.RemoveUserFromGroup")
	defer span.End()

	params := url.Values{}
	params.Add("skip_subresources", "true")
	params.Add("unassign_issuables", "true")

	url := fmt.Sprintf("%s/groups/%d/members/%d?%s", i.apiUrl, input.GroupId, input.UserId, params.Encode())
	resp, err := i.newRequest(ctx, http.MethodDelete, url, nil, input.Token)
	if err != nil {
		span.SetStatus(codes.Error, "newRequest failed")
		span.RecordError(err)
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusNoContent {
		span.AddEvent("user removed from group successfully")
		span.SetStatus(codes.Ok, "user removed from group successfully")
		return nil
	}

	var mapResp map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&mapResp); err != nil {
		span.SetStatus(codes.Error, "json.NewDecoder failed")
		span.RecordError(err)
		return err
	}

	apiErr := handleErrors(resp.StatusCode)
	err = fmt.Errorf("%v: %v", apiErr, mapResp)
	span.SetStatus(codes.Error, "remove user from group failed")
	span.RecordError(err)
	return err
}

// RemoveUserFromProject removes a user from a GitLab project.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: RemoveUserFromProjectRequest containing project ID, user ID, and authentication token
//
// Returns:
//   - error: Any error that occurred during the operation
//
// Reference: https://docs.gitlab.com/ee/api/members.html#remove-a-member-from-a-group-or-project
func (i *impl) RemoveUserFromProject(ctx context.Context, input RemoveUserFromProjectRequest) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "gateways.gitlab.RemoveUserFromProject")
	defer span.End()

	params := url.Values{}
	params.Add("unassign_issuables", "true")

	url := fmt.Sprintf("%s/projects/%d/members/%d?%s", i.apiUrl, input.RepoId, input.UserId, params.Encode())
	resp, err := i.newRequest(ctx, http.MethodDelete, url, nil, input.Token)
	if err != nil {
		span.SetStatus(codes.Error, "newRequest failed")
		span.RecordError(err)
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusNoContent {
		span.AddEvent("user removed from project successfully")
		span.SetStatus(codes.Ok, "user removed from project successfully")
		return nil
	}

	var mapResp map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&mapResp); err != nil {
		span.SetStatus(codes.Error, "json.NewDecoder failed")
		span.RecordError(err)
		return err
	}

	apiErr := handleErrors(resp.StatusCode)
	err = fmt.Errorf("%v: %v", apiErr, mapResp)
	span.SetStatus(codes.Error, "remove user from project failed")
	span.RecordError(err)
	return err
}

// GetMemberOfProject retrieves information about a user's membership in a GitLab project.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: GetMemberOfProjectRequest containing project ID, user ID, and authentication token
//
// Returns:
//   - *GetMemberOfProjectResponse: The project membership information if successful
//   - error: Any error that occurred during retrieval
//
// Reference: https://docs.gitlab.com/ee/api/members.html#get-a-member-of-a-group-or-project
func (i *impl) GetMemberOfProject(ctx context.Context, input GetMemberOfProjectRequest) (*GetMemberOfProjectResponse, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "gateways.gitlab.RemoveUserFromProject")
	defer span.End()

	url := fmt.Sprintf("%s/projects/%d/members/%d", i.apiUrl, input.RepoId, input.UserId)
	resp, err := i.newRequest(ctx, http.MethodGet, url, nil, input.Token)
	if err != nil {
		span.SetStatus(codes.Error, "newRequest failed")
		span.RecordError(err)
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusOK {
		var output GetMemberOfProjectResponse
		err = json.NewDecoder(resp.Body).Decode(&output)
		if err != nil {
			span.SetStatus(codes.Error, "json.NewDecoder failed")
			span.RecordError(err)
			return nil, err
		}

		span.AddEvent("get user in project successfully")
		span.SetStatus(codes.Ok, "get user in project successfully")
		return &output, nil
	}

	var mapResp map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&mapResp); err != nil {
		span.SetStatus(codes.Error, "json.NewDecoder failed")
		span.RecordError(err)
		return nil, err
	}

	apiErr := handleErrors(resp.StatusCode)
	err = fmt.Errorf("%v: %v", apiErr, mapResp)
	span.SetStatus(codes.Error, "get user in project failed")
	span.RecordError(err)
	return nil, err
}

// GetMemberOfGroup retrieves information about a user's membership in a GitLab group.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: GetMemberOfGroupRequest containing group ID, user ID, and authentication token
//
// Returns:
//   - *GetMemberOfGroupResponse: The group membership information if successful
//   - error: Any error that occurred during retrieval
//
// Reference: https://docs.gitlab.com/ee/api/members.html#get-a-member-of-a-group-or-project
func (i *impl) GetMemberOfGroup(ctx context.Context, input GetMemberOfGroupRequest) (*GetMemberOfGroupResponse, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "gateways.gitlab.RemoveUserFromProject")
	defer span.End()

	url := fmt.Sprintf("%s/groups/%d/members/%d", i.apiUrl, input.GroupId, input.UserId)
	resp, err := i.newRequest(ctx, http.MethodGet, url, nil, input.Token)
	if err != nil {
		span.SetStatus(codes.Error, "newRequest failed")
		span.RecordError(err)
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusOK {
		var output GetMemberOfGroupResponse
		err = json.NewDecoder(resp.Body).Decode(&output)
		if err != nil {
			span.SetStatus(codes.Error, "json.NewDecoder failed")
			span.RecordError(err)
			return nil, err
		}

		span.AddEvent("get user in group successfully")
		span.SetStatus(codes.Ok, "get user in group successfully")
		return &output, nil
	}

	var mapResp map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&mapResp); err != nil {
		span.SetStatus(codes.Error, "json.NewDecoder failed")
		span.RecordError(err)
		return nil, err
	}

	apiErr := handleErrors(resp.StatusCode)
	err = fmt.Errorf("%v: %v", apiErr, mapResp)
	span.SetStatus(codes.Error, "get user in group failed")
	span.RecordError(err)
	return nil, err
}

// GetProjectBranches retrieves a list of branches for a GitLab project.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: GetProjectBranchesRequest containing project ID and authentication token
//
// Returns:
//   - []RepositoryBranchInfo: List of branch information if successful
//   - error: Any error that occurred during retrieval
//
// Reference: https://docs.gitlab.com/ee/api/branches.html#list-repository-branches
func (i *impl) GetProjectBranches(ctx context.Context, input GetProjectBranchesRequest) ([]RepositoryBranchInfo, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "gateways.gitlab.GetProjectBranches")
	defer span.End()

	url := fmt.Sprintf("%s/projects/%d/repository/branches", i.apiUrl, input.Id)
	resp, err := i.newRequest(ctx, http.MethodGet, url, nil, input.Token)
	if err != nil {
		span.SetStatus(codes.Error, "newRequest failed")
		span.RecordError(err)
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusOK {
		var output []RepositoryBranchInfo
		err = json.NewDecoder(resp.Body).Decode(&output)
		if err != nil {
			span.SetStatus(codes.Error, "json.NewDecoder failed")
			span.RecordError(err)
			return nil, err
		}

		span.AddEvent("project branches retrieved successfully")
		span.SetStatus(codes.Ok, "project branches retrieved successfully")
		return output, nil
	}

	var mapResp map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&mapResp); err != nil {
		span.SetStatus(codes.Error, "json.NewDecoder failed")
		span.RecordError(err)
		return nil, err
	}

	apiErr := handleErrors(resp.StatusCode)
	err = fmt.Errorf("%v: %v", apiErr, mapResp)
	span.SetStatus(codes.Error, "get project branches failed")
	span.RecordError(err)
	return nil, err
}

// GetSingleProjectBrancheInfo retrieves information about a specific branch in a GitLab project.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: GetSingleProjectBrancheRequest containing project ID, branch name, and authentication token
//
// Returns:
//   - *RepositoryBranchInfo: The branch information if successful
//   - error: Any error that occurred during retrieval
//
// Reference: https://docs.gitlab.com/ee/api/branches.html#get-single-repository-branch
func (i *impl) GetSingleProjectBrancheInfo(ctx context.Context, input GetSingleProjectBrancheRequest) (*RepositoryBranchInfo, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "gateways.gitlab.GetSingleProjectBrancheInfo")
	defer span.End()

	url := fmt.Sprintf("%s/projects/%d/repository/branches/%s", i.apiUrl, input.Id, input.Branch)
	resp, err := i.newRequest(ctx, http.MethodGet, url, nil, input.Token)
	if err != nil {
		span.SetStatus(codes.Error, "newRequest failed")
		span.RecordError(err)
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusOK {
		var output RepositoryBranchInfo
		err = json.NewDecoder(resp.Body).Decode(&output)
		if err != nil {
			span.SetStatus(codes.Error, "json.NewDecoder failed")
			span.RecordError(err)
			return nil, err
		}

		span.AddEvent("single project branch info retrieved successfully")
		span.SetStatus(codes.Ok, "single project branch info retrieved successfully")
		return &output, nil
	}
	if resp.StatusCode == http.StatusNotFound {
		return nil, ErrBranchNotFound
	}

	var mapResp map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&mapResp); err != nil {
		span.SetStatus(codes.Error, "json.NewDecoder failed")
		span.RecordError(err)
		return nil, err
	}

	apiErr := handleErrors(resp.StatusCode)
	gitlabErr := &GitlabError{
		StatusCode: resp.StatusCode,
		Message:    apiErr.Error(),
		Data:       mapResp,
	}
	span.SetStatus(codes.Error, "get single project branch info failed")
	span.RecordError(gitlabErr)
	return nil, gitlabErr
}

// GetProjectFiles retrieves a list of files and directories in a GitLab project.
// The API from Gitlab uses keyset pagination.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: GetProjectFilesRequest containing project ID, path, reference, and authentication token
//
// Returns:
//   - []RepositoryFile: List of file and directory information if successful
//   - *string: Pointer to the next page URL if more pages are available
//   - error: Any error that occurred during retrieval
//
// Reference: https://docs.gitlab.com/ee/api/repositories.html#list-repository-tree
func (i *impl) GetProjectFiles(ctx context.Context, input GetProjectFilesRequest) ([]RepositoryFile, *string, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "gateways.gitlab.GetProjectFiles")
	defer span.End()

	params := buildGetProjectFilesParams(&input)
	url := fmt.Sprintf("%s/projects/%d/repository/tree?%s", i.apiUrl, input.Id, params.Encode())
	var nextPage *string

	// store the Link header for key-set pagination
	var files []RepositoryFile
	for {
		resp, err := i.newRequest(ctx, http.MethodGet, url, nil, input.Token)
		if err != nil {
			span.SetStatus(codes.Error, "newRequest failed")
			span.RecordError(err)
			return nil, nil, err
		}
		defer resp.Body.Close()

		if resp.StatusCode == http.StatusOK {
			var output []RepositoryFile
			err = json.NewDecoder(resp.Body).Decode(&output)
			if err != nil {
				span.SetStatus(codes.Error, "json.NewDecoder failed")
				span.RecordError(err)
				return nil, nil, err
			}
			files = append(files, output...)

			// if Link header exists, it indicates more pages of results
			// else assign empty string to url to indicate no more page and break the loop
			if linkHeader := resp.Header.Get("Link"); linkHeader != "" {
				link := linkheader.Parse(linkHeader)[0]
				nextPage = &link.URL
				url = link.URL
			} else {
				nextPage = nil
				break
			}
		} else {
			var mapResp map[string]any
			if err := json.NewDecoder(resp.Body).Decode(&mapResp); err != nil {
				span.SetStatus(codes.Error, "json.NewDecoder failed")
				span.RecordError(err)
				return nil, nil, err
			}

			apiErr := handleErrors(resp.StatusCode)
			err := fmt.Errorf("%v: %v", apiErr, mapResp)
			span.SetStatus(codes.Error, "get project files failed")
			span.RecordError(err)
			return nil, nil, err
		}

		if !input.All {
			break
		}
	}

	span.AddEvent("project files retrieved successfully")
	span.SetStatus(codes.Ok, "project files retrieved successfully")
	return files, nextPage, nil
}

func buildGetProjectFilesParams(input *GetProjectFilesRequest) *url.Values {
	params := url.Values{}
	if input.Ref != "" {
		params.Add("ref", input.Ref)
	}
	if input.Path != "" {
		params.Add("path", input.Path)
	}
	if input.PageToken != nil {
		params.Add("page_token", *input.PageToken)
	}
	params.Add("pagination", "keyset")
	params.Add("per_page", strconv.Itoa(input.PerPage))
	params.Add("recursive", strconv.FormatBool(input.Recursive))

	return &params
}

// GetProjectCommits retrieves a list of commits for a GitLab project.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: GetProjectCommitsRequest containing project ID, reference, path, and pagination parameters
//
// Returns:
//   - *HTTPResponse[[]RepositoryCommit]: The commits information with pagination details if successful
//   - error: Any error that occurred during retrieval
//
// Reference: https://docs.gitlab.com/ee/api/commits.html#list-repository-commits
func (i *impl) GetProjectCommits(ctx context.Context, input GetProjectCommitsRequest) (*HTTPResponse[[]RepositoryCommit], error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "gateways.gitlab.GetProjectCommits")
	defer span.End()

	params := url.Values{}
	if input.RefName != "" {
		params.Add("ref_name", input.RefName)
	}
	if input.Path != "" {
		params.Add("path", input.Path)
	}
	if input.Paginate.Page > 0 {
		params.Add("page", fmt.Sprint(input.Paginate.Page))
	}
	if input.Paginate.PerPage > 0 {
		params.Add("per_page", fmt.Sprint(input.Paginate.PerPage))
	}
	url := fmt.Sprintf("%s/projects/%d/repository/commits?%s", i.apiUrl, input.Id, params.Encode())
	resp, err := i.newRequest(ctx, http.MethodGet, url, nil, input.Token)
	if err != nil {
		span.SetStatus(codes.Error, "newRequest failed")
		span.RecordError(err)
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusOK {
		var output []RepositoryCommit
		err = json.NewDecoder(resp.Body).Decode(&output)
		if err != nil {
			span.SetStatus(codes.Error, "json.NewDecoder failed")
			span.RecordError(err)
			return nil, err
		}

		next := resp.Header.Get("X-Next-Page") != ""
		prev := resp.Header.Get("X-Prev-Page") != ""
		span.AddEvent("project commits retrieved successfully")
		span.SetStatus(codes.Ok, "project commits retrieved successfully")
		return &HTTPResponse[[]RepositoryCommit]{
			Data: &output,
			Pagination: &Pagination{
				Next:     &next,
				Previous: &prev,
			},
		}, nil
	}

	var mapResp map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&mapResp); err != nil {
		span.SetStatus(codes.Error, "json.NewDecoder failed")
		span.RecordError(err)
		return nil, err
	}

	apiErr := handleErrors(resp.StatusCode)
	err = fmt.Errorf("%v: %v", apiErr, mapResp)
	span.SetStatus(codes.Error, "get project commits failed")
	span.RecordError(err)
	return nil, err
}

// CreateProjectCommit creates a new commit in a GitLab project.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: CreateProjectCommitRequest containing project ID, branch, commit message, and file changes
//
// Returns:
//   - *RepositoryCommit: The created commit information if successful
//   - error: Any error that occurred during commit creation
//
// Reference: https://docs.gitlab.com/ee/api/commits.html#create-a-commit-with-multiple-files-and-actions
func (i *impl) CreateProjectCommit(ctx context.Context, input CreateProjectCommitRequest) (*RepositoryCommit, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "gateways.gitlab.CreateProjectCommit")
	defer span.End()

	url := fmt.Sprintf("%s/projects/%d/repository/commits", i.apiUrl, input.Id)
	resp, err := i.newRequest(ctx, http.MethodPost, url, input, input.Token)
	if err != nil {
		span.SetStatus(codes.Error, "newRequest failed")
		span.RecordError(err)
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusCreated {
		var output RepositoryCommit
		err = json.NewDecoder(resp.Body).Decode(&output)
		if err != nil {
			span.SetStatus(codes.Error, "json.NewDecoder failed")
			span.RecordError(err)
			return nil, err
		}

		span.AddEvent("project commit created successfully")
		span.SetStatus(codes.Ok, "project commit created successfully")
		return &output, nil
	}

	var mapResp map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&mapResp); err != nil {
		span.SetStatus(codes.Error, "json.NewDecoder failed")
		span.RecordError(err)
		return nil, err
	}

	apiErr := handleErrors(resp.StatusCode)
	err = fmt.Errorf("%v: %v", apiErr, mapResp)
	span.SetStatus(codes.Error, "create project commit failed")
	span.RecordError(err)
	return nil, err
}

// GetSingleProjectCommit retrieves information about a specific commit in a GitLab project.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: GetSingleProjectCommitRequest containing project ID, commit SHA, and authentication token
//
// Returns:
//   - *RepositoryCommit: The commit information if successful
//   - error: Any error that occurred during retrieval
//
// Reference: https://docs.gitlab.com/ee/api/commits.html#get-a-single-commit
func (i *impl) GetSingleProjectCommit(ctx context.Context, input GetSingleProjectCommitRequest) (*RepositoryCommit, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "gateways.gitlab.GetSingleProjectCommit")
	defer span.End()

	url := fmt.Sprintf("%s/projects/%d/repository/commits/%s", i.apiUrl, input.Id, input.Sha)
	resp, err := i.newRequest(ctx, http.MethodGet, url, nil, input.Token)
	if err != nil {
		span.SetStatus(codes.Error, "newRequest failed")
		span.RecordError(err)
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusOK {
		var output RepositoryCommit
		err = json.NewDecoder(resp.Body).Decode(&output)
		if err != nil {
			span.SetStatus(codes.Error, "json.NewDecoder failed")
			span.RecordError(err)
			return nil, err
		}

		span.AddEvent("single project commit retrieved successfully")
		span.SetStatus(codes.Ok, "single project commit retrieved successfully")
		return &output, nil
	}

	var mapResp map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&mapResp); err != nil {
		span.SetStatus(codes.Error, "json.NewDecoder failed")
		span.RecordError(err)
		return nil, err
	}

	apiErr := handleErrors(resp.StatusCode)
	err = fmt.Errorf("%v: %v", apiErr, mapResp)
	span.SetStatus(codes.Error, "get single project commit failed")
	span.RecordError(err)
	return nil, err
}

// GetProjectContributors retrieves a list of contributors for a GitLab project.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: GetProjectContributorsRequest containing project ID, reference, and authentication token
//
// Returns:
//   - []RepositoryContributor: List of contributor information if successful
//   - error: Any error that occurred during retrieval
//
// Reference: https://docs.gitlab.com/ee/api/repositories.html#contributors
func (i *impl) GetProjectContributors(ctx context.Context, input GetProjectContributorsRequest) ([]RepositoryContributor, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "gateways.gitlab.GetProjectContributors")
	defer span.End()

	params := url.Values{}
	if input.Ref != "" {
		params.Add("ref", input.Ref)
	}

	url := fmt.Sprintf("%s/projects/%d/repository/contributors?%s", i.apiUrl, input.Id, params.Encode())
	resp, err := i.newRequest(ctx, http.MethodGet, url, nil, input.Token)
	if err != nil {
		span.SetStatus(codes.Error, "newRequest failed")
		span.RecordError(err)
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusOK {
		var output []RepositoryContributor
		err = json.NewDecoder(resp.Body).Decode(&output)
		if err != nil {
			span.SetStatus(codes.Error, "json.NewDecoder failed")
			span.RecordError(err)
			return nil, err
		}

		span.AddEvent("project contributors retrieved successfully")
		span.SetStatus(codes.Ok, "project contributors retrieved successfully")
		return output, nil
	}

	var mapResp map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&mapResp); err != nil {
		span.SetStatus(codes.Error, "json.NewDecoder failed")
		span.RecordError(err)
		return nil, err
	}

	apiErr := handleErrors(resp.StatusCode)
	err = fmt.Errorf("%v: %v", apiErr, mapResp)
	span.SetStatus(codes.Error, "get project contributors failed")
	span.RecordError(err)
	return nil, err
}

// CreateWebhook creates a new webhook for a GitLab project.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: CreateProjectWebhookRequest containing project ID, webhook URL, and trigger events
//
// Returns:
//   - error: Any error that occurred during webhook creation
//
// Reference: https://docs.gitlab.com/ee/api/projects.html#add-project-hook
func (i *impl) CreateWebhook(ctx context.Context, input CreateProjectWebhookRequest) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "gateways.gitlab.CreateWebhook")
	defer span.End()

	url := fmt.Sprintf("%s/projects/%d/hooks", i.apiUrl, input.Id)
	resp, err := i.newRequest(ctx, http.MethodPost, url, input, input.Token)
	if err != nil {
		span.SetStatus(codes.Error, "newRequest failed")
		span.RecordError(err)
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusCreated {
		span.AddEvent("webhook created successfully")
		span.SetStatus(codes.Ok, "webhook created successfully")
		return nil
	}

	var mapResp map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&mapResp); err != nil {
		span.SetStatus(codes.Error, "json.NewDecoder failed")
		span.RecordError(err)
		return err
	}

	apiErr := handleErrors(resp.StatusCode)
	err = fmt.Errorf("%v: %v", apiErr, mapResp)
	span.SetStatus(codes.Error, "create webhook failed")
	span.RecordError(err)
	return err
}

// CreateProjectFromTemplate creates a new project by forking an existing project.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: CreateProjectRequest containing source project ID and new project details
//
// Returns:
//   - *CreateProjectResponse: The created project information if successful
//   - error: Any error that occurred during project creation
//
// Reference: https://docs.gitlab.com/ee/api/projects.html#fork-project
func (i *impl) CreateProjectFromTemplate(ctx context.Context, input CreateProjectRequest) (*CreateProjectResponse, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "gateways.gitlab.CreateProjectFromTemplate")
	defer span.End()

	url := fmt.Sprintf("%s/projects/%d/fork", i.apiUrl, *input.SourceProjectId)
	resp, err := i.newRequest(ctx, http.MethodPost, url, input, input.Token)
	if err != nil {
		span.SetStatus(codes.Error, "newRequest failed")
		span.RecordError(err)
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusCreated {
		var output CreateProjectResponse
		err = json.NewDecoder(resp.Body).Decode(&output)
		if err != nil {
			span.SetStatus(codes.Error, "json.NewDecoder failed")
			span.RecordError(err)
			return nil, err
		}

		span.AddEvent("project created from template successfully")
		span.SetStatus(codes.Ok, "project created from template successfully")
		return &output, nil
	}

	var mapResp map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&mapResp); err != nil {
		span.SetStatus(codes.Error, "json.NewDecoder failed")
		span.RecordError(err)
		return nil, err
	}

	apiErr := handleErrors(resp.StatusCode)
	err = fmt.Errorf("%v: %v", apiErr, mapResp)
	span.SetStatus(codes.Error, "create project from template failed")
	span.RecordError(err)
	return nil, err
}

// DownloadProject downloads a project archive from GitLab.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: DownloadProjectRequest containing project ID, archive type, reference, and authentication token
//
// Returns:
//   - *http.Response: The archive file response if successful
//   - error: Any error that occurred during download
//
// Reference: https://docs.gitlab.com/ee/api/projects.html#download-project-archive
func (i *impl) DownloadProject(ctx context.Context, input DownloadProjectRequest) (*http.Response, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "gateways.gitlab.DownloadProject")
	defer span.End()

	url := fmt.Sprintf("%s/projects/%d/repository/archive.%s?sha=%s", i.apiUrl, input.Id, input.Type, input.Sha)
	resp, err := i.newRequest(ctx, http.MethodGet, url, input, input.Token)
	if err != nil {
		span.SetStatus(codes.Error, "newRequest failed")
		span.RecordError(err)
		return nil, err
	}

	if resp.StatusCode == http.StatusOK {
		span.AddEvent("project download successfully")
		span.SetStatus(codes.Ok, "project download successfully")
		return resp, nil
	}

	var mapResp map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&mapResp); err != nil {
		span.SetStatus(codes.Error, "json.NewDecoder failed")
		span.RecordError(err)
		return nil, err
	}

	apiErr := handleErrors(resp.StatusCode)
	err = fmt.Errorf("%v: %v", apiErr, mapResp)
	span.SetStatus(codes.Error, "download project failed")
	span.RecordError(err)
	return nil, err
}

// GetMergeRequests retrieves merge requests associated with a specific commit.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: GetMergeRequestsRequest containing project ID, commit ID, and authentication token
//
// Returns:
//   - []MergeRequest: List of merge request information if successful
//   - error: Any error that occurred during retrieval
//
// Reference: https://docs.gitlab.com/ee/api/commits.html#list-merge-requests-associated-with-a-commit
func (i *impl) GetMergeRequests(ctx context.Context, input GetMergeRequestsRequest) ([]MergeRequest, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "gateways.gitlab.GetMergeRequests")
	defer span.End()

	url := fmt.Sprintf("%s/projects/%d/repository/commits/%s/merge_requests", i.apiUrl, input.Id, input.CommitId)
	resp, err := i.newRequest(ctx, http.MethodGet, url, input, input.Token)
	if err != nil {
		span.SetStatus(codes.Error, "newRequest failed")
		span.RecordError(err)
		return nil, err
	}

	if resp.StatusCode == http.StatusOK {
		var output []MergeRequest
		err = json.NewDecoder(resp.Body).Decode(&output)
		if err != nil {
			span.SetStatus(codes.Error, "json.NewDecoder failed")
			span.RecordError(err)
			return nil, err
		}

		span.AddEvent("merge requests retrieved successfully")
		span.SetStatus(codes.Ok, "merge requests retrieved successfully")
		return output, nil
	}

	var mapResp map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&mapResp); err != nil {
		span.SetStatus(codes.Error, "json.NewDecoder failed")
		span.RecordError(err)
		return nil, err
	}

	apiErr := handleErrors(resp.StatusCode)
	err = fmt.Errorf("%v: %v", apiErr, mapResp)
	span.SetStatus(codes.Error, "get merge requests failed")
	span.RecordError(err)
	return nil, err
}
