package gitlab

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
)

type GitlabClient interface {
	CreateUser(ctx context.Context, request CreateUserRequest) (*CreateUserResponse, error)
	GetUser(ctx context.Context, request GetUserRequest) ([]CreateUserResponse, error)
	DeleteUser(ctx context.Context, request DeleteUserRequest) error
	CreatePersonalAccessToken(ctx context.Context, input CreatePersonalAccessTokenRequest) (*CreatePersonalAccessTokenResponse, error)
	DeletePersonalAccessToken(ctx context.Context, accessToken string) error
	CreateGroup(ctx context.Context, input CreateGroupRequest) (*GitlabGroup, error)
	CreateUserGroup(ctx context.Context, gitUserID int64, username, path, token string, parentID int64) (int64, error)
	GetGroup(ctx context.Context, input GetGroupRequest) ([]GitlabGroup, error)
	UpdateGroup(ctx context.Context, input UpdateGroupRequest) error
	DeleteGroup(ctx context.Context, input DeleteOrganizationRequest) error
	CreateProject(ctx context.Context, input CreateProjectRequest) (*CreateProjectResponse, error)
	DeleteProject(ctx context.Context, input DeleteProjectRequest) error
	UpdateProject(ctx context.Context, input UpdateProjectRequest) error
	GetProjectURLs(ctx context.Context, input GetProjectURLsRequest) (*GetProjectURLsResponse, error)
	GetFileFromRepo(ctx context.Context, input GetFileRequest) (*GetFileResponse, error)
	GetRawFileFromRepo(ctx context.Context, input GetFileRequest) (*http.Response, error)
	GetHeaderRawFileFromRepo(ctx context.Context, input GetFileRequest) (*http.Response, error)
	CreateSSHKey(ctx context.Context, input CreateSSHKeyRequest) (*CreateSSHKeyResponse, error)
	DeleteSSHKey(ctx context.Context, input DeleteSSHKeyRequest) error
	SetAuthToken(token string)
	UploadAvatarRepository(ctx context.Context, input UploadAvatarRepositoryRequest) (*UploadAvatarRepositoryResponse, error)
	AddUserToGroup(ctx context.Context, input AddUserToGroupRequest) (*AddUserToGroupResponse, error)
	EditUserInGroup(ctx context.Context, input EditUserInGroupRequest) (*EditUserInGroupResponse, error)
	AddUserToProject(ctx context.Context, input AddUserToProjectRequest) (*AddUserToProjectResponse, error)
	EditUserInProject(ctx context.Context, input EditUserInProjectRequest) (*EditUserInProjectResponse, error)
	RemoveUserFromGroup(ctx context.Context, input RemoveUserFromGroupRequest) error
	RemoveUserFromProject(ctx context.Context, input RemoveUserFromProjectRequest) error
	GetMemberOfGroup(ctx context.Context, input GetMemberOfGroupRequest) (*GetMemberOfGroupResponse, error)
	GetMemberOfProject(ctx context.Context, input GetMemberOfProjectRequest) (*GetMemberOfProjectResponse, error)
	GetProjectBranches(ctx context.Context, input GetProjectBranchesRequest) ([]RepositoryBranchInfo, error)
	GetSingleProjectBrancheInfo(ctx context.Context, input GetSingleProjectBrancheRequest) (*RepositoryBranchInfo, error)
	GetProjectFiles(ctx context.Context, input GetProjectFilesRequest) ([]RepositoryFile, *string, error)
	GetProjectCommits(ctx context.Context, input GetProjectCommitsRequest) (*HTTPResponse[[]RepositoryCommit], error)
	CreateProjectCommit(ctx context.Context, input CreateProjectCommitRequest) (*RepositoryCommit, error)
	GetSingleProjectCommit(ctx context.Context, input GetSingleProjectCommitRequest) (*RepositoryCommit, error)
	GetProjectContributors(ctx context.Context, input GetProjectContributorsRequest) ([]RepositoryContributor, error)
	CreateRepoPersonalAccessToken(ctx context.Context, input CreateProjectAccessTokenRequest) (*CreateProjectAccessTokenResponse, error)
	DeleteRepoPersonalAccessToken(ctx context.Context, input DeleteProjectAccessTokenRequest) error
	CreateWebhook(ctx context.Context, input CreateProjectWebhookRequest) error
	CreateProjectFromTemplate(ctx context.Context, input CreateProjectRequest) (*CreateProjectResponse, error)
	Health(ctx context.Context, token string) (bool, error)
	DownloadProject(ctx context.Context, input DownloadProjectRequest) (*http.Response, error)
	GetMergeRequests(ctx context.Context, input GetMergeRequestsRequest) ([]MergeRequest, error)
}

var _ GitlabClient = (*impl)(nil)

type impl struct {
	httpClient          *http.Client
	apiUrl              string
	authToken           string
	certificatePath     string
	customGitlabSshHost string
}

func New(hostname string, options ...func(*impl)) (*impl, error) {
	if len(hostname) == 0 {
		return nil, fmt.Errorf("missing 'hostname' argument")
	}

	// TODO: add certificate
	httpCli := &http.Client{
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: true,
			},
		},
	}

	c := &impl{
		httpClient: httpCli,
		apiUrl:     fmt.Sprintf("%s/api/v4", hostname),
	}

	for _, opt := range options {
		opt(c)
	}

	return c, nil
}

func WithCustomGitlabSshHost(host string) func(*impl) {
	return func(c *impl) {
		c.customGitlabSshHost = host
	}
}

func WithCertificatePath(path string) func(*impl) {
	return func(c *impl) {
		c.certificatePath = path
	}
}

func (i *impl) SetAuthToken(token string) {
	i.authToken = token
}

func (i *impl) newRequest(ctx context.Context, method string, url string, body any, token string) (*http.Response, error) {
	marshalled, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequestWithContext(ctx, method, url, bytes.NewReader(marshalled))
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("PRIVATE-TOKEN", token)

	res, err := i.httpClient.Do(req)
	if err != nil {
		return nil, err
	}

	return res, nil
}

func (i *impl) newRequestUploadFile(ctx context.Context, method string, url string, body any, token string) (*http.Response, error) {
	req, err := http.NewRequestWithContext(ctx, method, url, body.(io.Reader))
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", "multipart/form-data")
	req.Header.Add("PRIVATE-TOKEN", token)

	res, err := i.httpClient.Do(req)
	if err != nil {
		return nil, err
	}

	return res, nil
}
