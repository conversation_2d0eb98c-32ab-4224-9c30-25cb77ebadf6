package gitlab_test

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/stretchr/testify/require"

	"api-server/internal/enums"
	"api-server/internal/gateways/gitlab"
)

func TestCreateProjectAccessToken(t *testing.T) {
	now := time.Now()
	tests := []struct {
		name                    string
		input                   gitlab.CreateProjectAccessTokenRequest
		mockResponseBodySuccess gitlab.CreateProjectAccessTokenResponse
		mockResponseBodyFail    map[string]interface{}
		mockHttpStatus          int
		expErr                  error
		expOutput               *gitlab.CreateProjectAccessTokenResponse
	}{
		{
			name: "should return error when status code is not 201",
			input: gitlab.CreateProjectAccessTokenRequest{
				Name:      "Test Project",
				ExpiresAt: now.Format(time.RFC3339),
				Scopes:    []enums.RepoAccessTokenScope{"api"},
			},
			mockHttpStatus: http.StatusBadRequest,
			mockResponseBodyFail: map[string]interface{}{
				"message": "error",
			},
			expErr: fmt.Errorf("%v: map[message:error]", gitlab.ErrBadRequestResponse),
		},
		{
			name: "should return error when status code is 201",
			input: gitlab.CreateProjectAccessTokenRequest{
				Name:      "Test Project",
				ExpiresAt: "2024-12-15T15:30:00Z",
				Scopes:    []enums.RepoAccessTokenScope{"api"},
			},
			mockHttpStatus: http.StatusCreated,
			mockResponseBodySuccess: gitlab.CreateProjectAccessTokenResponse{
				Id:        1,
				Name:      "Test Project",
				ExpiredAt: now.Format(time.RFC3339),
				Scopes:    []string{"api"},
			},
			expOutput: &gitlab.CreateProjectAccessTokenResponse{
				Id:        1,
				Name:      "Test Project",
				ExpiredAt: now.Format(time.RFC3339),
				Scopes:    []string{"api"},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			mockSvr := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				w.WriteHeader(tt.mockHttpStatus)
				if tt.mockHttpStatus == http.StatusCreated {
					byteArr, err := json.Marshal(tt.mockResponseBodySuccess)
					require.NoError(t, err)
					_, err = w.Write(byteArr)
					require.NoError(t, err)
				} else {
					byteArr, err := json.Marshal(tt.mockResponseBodyFail)
					require.NoError(t, err)
					_, err = w.Write(byteArr)
					require.NoError(t, err)
				}
			}))

			defer mockSvr.Close()
			i, _ := gitlab.New(mockSvr.URL)
			output, err := i.CreateRepoPersonalAccessToken(ctx, tt.input)
			if tt.expErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.expErr, err)
			} else {
				require.NoError(t, err)
				require.Equal(t, tt.expOutput, output)
			}
		})
	}
}

func TestDeleteProjectAccessToken(t *testing.T) {
	tests := []struct {
		name             string
		input            gitlab.DeleteProjectAccessTokenRequest
		mockHttpStatus   int
		mockResponseBody map[string]interface{}
		expErr           error
	}{
		{
			name: "should return error when status code is not 204",
			input: gitlab.DeleteProjectAccessTokenRequest{
				ID:        1,
				UserToken: "",
				RepoID:    2,
			},
			mockHttpStatus: http.StatusBadRequest,
			mockResponseBody: map[string]interface{}{
				"message": "error",
			},
			expErr: fmt.Errorf("%v: map[message:error]", gitlab.ErrBadRequestResponse),
		},
		{
			name: "success",
			input: gitlab.DeleteProjectAccessTokenRequest{
				ID:        1,
				UserToken: "",
				RepoID:    2,
			},
			mockHttpStatus: http.StatusNoContent,
			expErr:         nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			mockSvr := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				w.WriteHeader(tt.mockHttpStatus)
				if tt.mockResponseBody != nil {
					byteArr, err := json.Marshal(tt.mockResponseBody)
					require.NoError(t, err)
					_, err = w.Write(byteArr)
					require.NoError(t, err)
				}
			}))

			defer mockSvr.Close()
			i, _ := gitlab.New(mockSvr.URL)
			err := i.DeleteRepoPersonalAccessToken(ctx, tt.input)
			if tt.expErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.expErr, err)
			} else {
				require.NoError(t, err)
			}
		})
	}
}
