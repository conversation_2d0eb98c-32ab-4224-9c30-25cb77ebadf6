package gitlab

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"net/url"

	"api-server/internal/enums"
	"api-server/pkg/oteltrace"

	"go.opentelemetry.io/otel/codes"
)

// CreateGroup creates a new GitLab group with the specified parameters.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: CreateOrganizationRequest containing group details and authentication token
//
// Returns:
//   - *CreateOrganizationResponse: The created group information if successful
//   - error: Any error that occurred during group creation
//
// Reference: https://docs.gitlab.com/ee/api/groups.html#new-group
func (i *impl) CreateGroup(ctx context.Context, input CreateGroupRequest) (*GitlabGroup, error) {
	c, span := oteltrace.Tracer.Start(ctx, "gateways.gitlab.CreateGroup")
	defer span.End()

	apiUrl := fmt.Sprintf("%s/groups", i.apiUrl)
	resp, err := i.newRequestCreateOrg(c, http.MethodPost, apiUrl, &input)
	if err != nil {
		span.SetStatus(codes.Error, "failed to create request to gitlab")
		span.RecordError(err)
		return nil, err
	}
	defer resp.Body.Close()

	var mapResp map[string]interface{}
	if resp.StatusCode == http.StatusCreated {
		var output GitlabGroup
		if err := json.NewDecoder(resp.Body).Decode(&output); err != nil {
			span.SetStatus(codes.Error, "failed to decode response body")
			span.RecordError(err)
			return nil, err
		}

		span.SetStatus(codes.Ok, "successfully created gitlab group")
		span.AddEvent("Gitlab Group Created")
		return &output, nil
	}

	if err := json.NewDecoder(resp.Body).Decode(&mapResp); err != nil {
		span.SetStatus(codes.Error, "failed to decode error response body")
		span.RecordError(err)
		return nil, err
	}

	apiErr := handleErrors(resp.StatusCode)
	err = fmt.Errorf("%w: %v", apiErr, mapResp)
	span.SetStatus(codes.Error, "gitlab API error")
	span.RecordError(err)
	return nil, err
}

// DeleteGroup deletes a GitLab group by its ID.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: DeleteOrganizationRequest containing group ID and authentication token
//
// Returns:
//   - error: Any error that occurred during group deletion
//
// Reference: https://docs.gitlab.com/ee/api/groups.html#remove-group
func (i *impl) DeleteGroup(ctx context.Context, input DeleteOrganizationRequest) error {
	c, span := oteltrace.Tracer.Start(ctx, "gateways.gitlab.DeleteGroup")
	defer span.End()

	apiUrl := fmt.Sprintf("%s/groups/%d", i.apiUrl, input.Id)
	resp, err := i.newRequest(c, http.MethodDelete, apiUrl, nil, input.Token)
	if err != nil {
		span.SetStatus(codes.Error, "failed to create request to gitlab")
		span.RecordError(err)
		return err
	}
	defer resp.Body.Close()

	var mapResp map[string]interface{}
	if resp.StatusCode == http.StatusAccepted {
		span.SetStatus(codes.Ok, "successfully deleted gitlab group")
		span.AddEvent("Gitlab Group Deleted")
		return nil
	}

	if err := json.NewDecoder(resp.Body).Decode(&mapResp); err != nil {
		span.SetStatus(codes.Error, "failed to decode error response body")
		span.RecordError(err)
		return err
	}

	apiErr := handleErrors(resp.StatusCode)
	err = fmt.Errorf("%w: %v", apiErr, mapResp)
	span.SetStatus(codes.Error, "gitlab API error")
	span.RecordError(err)
	return err
}

// GetGroup retrieves information about GitLab groups based on search criteria.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: GetOrganizationRequest containing search parameters and authentication token
//
// Returns:
//   - []CreateOrganizationResponse: List of matching groups if successful
//   - error: Any error that occurred during retrieval
//
// Reference: https://docs.gitlab.com/ee/api/groups.html#list-groups
func (i *impl) GetGroup(ctx context.Context, input GetGroupRequest) ([]GitlabGroup, error) {
	params := url.Values{}
	if input.Path != "" {
		params.Add("search", input.Path)
	}
	url := fmt.Sprintf("%s/groups?%s", i.apiUrl, params.Encode())
	resp, err := i.newRequest(ctx, http.MethodGet, url, nil, input.Token)
	if err != nil {
		return nil, err
	}

	defer resp.Body.Close()
	if resp.StatusCode == http.StatusOK {
		var output []GitlabGroup
		err = json.NewDecoder(resp.Body).Decode(&output)
		if err != nil {
			return nil, err
		}

		for _, group := range output {
			if group.Path == input.Path {
				return []GitlabGroup{group}, nil
			}
		}
		return []GitlabGroup{}, nil
	}

	var mapResp map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&mapResp); err != nil {
		return nil, err
	}

	err = handleErrors(resp.StatusCode)
	return nil, fmt.Errorf("%w: %v", err, mapResp)
}

// UpdateGroup updates an existing GitLab group with new information.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: UpdateOrganizationRequest containing updated group details and authentication token
//
// Returns:
//   - error: Any error that occurred during group update
//
// Reference: https://docs.gitlab.com/ee/api/groups.html#update-group
func (i *impl) UpdateGroup(ctx context.Context, input UpdateGroupRequest) error {
	url := fmt.Sprintf("%s/groups/%d", i.apiUrl, input.Id)
	resp, err := i.newRequest(ctx, http.MethodPut, url, input, input.Token)
	if err != nil {
		return err
	}

	defer resp.Body.Close()
	if resp.StatusCode == http.StatusOK {
		return nil
	}

	var mapResp map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&mapResp); err != nil {
		return err
	}

	err = handleErrors(resp.StatusCode)
	return fmt.Errorf("%w: %v", err, mapResp)
}

// newRequestCreateOrg creates a new HTTP request for organization creation with multipart form data.
//
// Parameters:
//   - ctx: Context for the operation
//   - method: HTTP method to use
//   - url: Target URL for the request
//   - request: CreateOrganizationRequest containing group details and avatar file
//
// Returns:
//   - *http.Response: The HTTP response from the server
//   - error: Any error that occurred during request creation
func (i *impl) newRequestCreateOrg(ctx context.Context, method string, url string, request *CreateGroupRequest) (*http.Response, error) {
	c, span := oteltrace.Tracer.Start(ctx, "gateways.gitlab.newRequestCreateOrg")
	defer span.End()

	var body bytes.Buffer
	writer := multipart.NewWriter(&body)

	if err := writer.WriteField("name", request.Name); err != nil {
		span.SetStatus(codes.Error, "failed to write form field 'name'")
		span.RecordError(err)
		return nil, err
	}

	if err := writer.WriteField("path", request.Path); err != nil {
		span.SetStatus(codes.Error, "failed to write form field 'path'")
		span.RecordError(err)
		return nil, err
	}

	if err := writer.WriteField("parent_id", fmt.Sprintf("%d", request.ParentID)); err != nil {
		span.SetStatus(codes.Error, "failed to write form field 'parent_id'")
		span.RecordError(err)
		return nil, err
	}

	if request.Avatar != nil {
		file, err := request.Avatar.Open()
		if err != nil {
			span.SetStatus(codes.Error, "failed to open avatar file")
			span.RecordError(err)
			return nil, err
		}
		defer file.Close()

		fileWriter, err := writer.CreateFormFile("avatar", request.Avatar.Filename)
		if err != nil {
			span.SetStatus(codes.Error, "failed to create form file for avatar")
			span.RecordError(err)
			return nil, err
		}

		_, err = io.Copy(fileWriter, file)
		if err != nil {
			span.SetStatus(codes.Error, "failed to copy avatar file")
			span.RecordError(err)
			return nil, err
		}
	}

	writer.Close()

	req, err := http.NewRequestWithContext(c, method, url, &body)
	if err != nil {
		span.SetStatus(codes.Error, "failed to create http request")
		span.RecordError(err)
		return nil, err
	}

	req.Header.Add("Content-Type", writer.FormDataContentType())
	req.Header.Add("PRIVATE-TOKEN", request.Token)

	res, err := i.httpClient.Do(req)
	if err != nil {
		span.SetStatus(codes.Error, "failed to execute http request")
		span.RecordError(err)
		return nil, err
	}

	span.SetStatus(codes.Ok, "successfully created http request")
	span.AddEvent("HTTP Request Created")

	return res, nil
}

// CreateUserGroup creates a new GitLab group for a user.
// It handles the creation of a GitLab group with the specified parameters.
//
// Parameters:
//   - ctx: Context for the operation
//   - gitUserID: GitLab user ID to associate with the group
//   - username: Username to use for the group
//   - token: GitLab access token for API calls
//   - parentID: ID of the parent group (if any)
//
// Returns:
//   - int64: ID of the created GitLab group
//   - error: Any error that occurred during group creation
func (i *impl) CreateUserGroup(ctx context.Context, gitUserID int64, username, path, token string, parentID int64) (int64, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "gateways.gitlab.CreateUserGroup")
	defer span.End()

	// find group first
	var group GitlabGroup
	groups, err := i.GetGroup(ctx, GetGroupRequest{
		Token: token,
		Path:  path,
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to get group in gitlab")
		span.RecordError(err)
		return 0, err
	}
	if len(groups) > 0 {
		span.AddEvent("group already exists")
		for _, g := range groups {
			if g.Path == path {
				group = g
				break
			}
		}
	} else {
		span.AddEvent("group does not exist, creating new group")
		req := CreateGroupRequest{
			Name:     username,
			Path:     username,
			Token:    token,
			ParentID: parentID,
		}
		resp, err := i.CreateGroup(ctx, req)
		if err != nil {
			span.SetStatus(codes.Error, "failed to create group in gitlab")
			span.RecordError(err)
			return 0, err
		}
		group = *resp
	}

	// set default protected branch for developer to push and merge
	reqUpdate := UpdateGroupRequest{
		Id:         group.Id,
		Token:      token,
		Visibility: string(enums.RepoVisibility_Internal),
		DefaultBranchProtectionDefaults: DefaultBranchProtectionDefaults{
			AllowedToPush: []AccessLevel{
				{
					AccessLevel: OrganizationAccessLevel_Developer,
				},
			},
			AllowedToMerge: []AccessLevel{
				{
					AccessLevel: OrganizationAccessLevel_Developer,
				},
			},
			AllowForcePush: false,
		},
	}
	if err = i.UpdateGroup(ctx, reqUpdate); err != nil {
		span.SetStatus(codes.Error, "failed to update git group")
		span.RecordError(err)
		return 0, err
	}

	_, err = i.AddUserToGroup(ctx, AddUserToGroupRequest{
		GroupId:     int64(group.Id),
		UserId:      gitUserID,
		AccessLevel: OrganizationAccessLevel_Owner,
		Token:       token,
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to add user to group in gitlab")
		span.RecordError(err)
		return 0, err
	}

	span.AddEvent("group created successfully")
	span.SetStatus(codes.Ok, "group created successfully")
	return int64(group.Id), nil
}
