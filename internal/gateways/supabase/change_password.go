package supabase

import (
	"context"
	"fmt"
	"net/http"
)

// ChangePassword implements the SupabaseClient interface for updating a user's password.
// This function sends a PUT request to the Supabase API to change the user's password.
//
// Parameters:
//   - ctx: Context for the operation
//   - accessToken: The user's access token for authentication
//   - input: ChangePasswordRequest containing the new password
//
// Returns:
//   - error: Any error that occurred during password change
func (i *impl) ChangePassword(ctx context.Context, accessToken string, input ChangePasswordRequest) error {
	url := fmt.Sprintf("%s/user", i.apiUrl)
	resp, err := i.newRequest(http.MethodPut, url, input, accessToken)
	if err != nil {
		return err
	}

	if resp.StatusCode == http.StatusOK {
		return nil
	}

	return handleErrors(resp.StatusCode)
}
