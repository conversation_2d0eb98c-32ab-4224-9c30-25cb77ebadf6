package supabase

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
)

// SupabaseClient defines the interface for Supabase authentication operations.
// It provides methods for user management including invitation, signup, deletion, and password changes.
type SupabaseClient interface {
	// Invite<PERSON><PERSON> sends an invitation to a user to join the platform.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - input: InviteUserRequest containing the email to invite
	//
	// Returns:
	//   - *InviteUserResponse: The created user information if successful
	//   - error: Any error that occurred during invitation
	InviteUser(ctx context.Context, input InviteUserRequest) (*InviteUserResponse, error)

	// SignUp creates a new user account in Supabase.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - input: SignupRequest containing email and password
	//
	// Returns:
	//   - *SignupResponse: The created user information if successful
	//   - error: Any error that occurred during signup
	SignUp(ctx context.Context, input SignupRequest) (*SignupResponse, error)

	// DeleteUser removes a user from Supabase.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - input: DeleteUserRequest containing the user ID to delete
	//
	// Returns:
	//   - error: Any error that occurred during deletion
	DeleteUser(ctx context.Context, input DeleteUserRequest) error

	// ChangePassword updates a user's password in Supabase.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - accessToken: The user's access token for authentication
	//   - input: ChangePasswordRequest containing the new password
	//
	// Returns:
	//   - error: Any error that occurred during password change
	ChangePassword(ctx context.Context, accessToken string, input ChangePasswordRequest) error
}

var _ SupabaseClient = (*impl)(nil)

type impl struct {
	httpClient       *http.Client
	apiUrl           string
	serviceToken     string
	inviteRedirectTo string // redirect URL when invite user
}

// New creates a new instance of the Supabase client.
// This function initializes the client with the provided configuration parameters.
//
// Parameters:
//   - hostname: The Supabase API hostname
//   - serviceToken: The service token for authentication
//   - inviteRedirectTo: The redirect URL for user invitations
//
// Returns:
//   - *impl: A new instance of the Supabase client
//   - error: Any error that occurred during initialization
func New(hostname string, serviceToken string, inviteRedirectTo string) (*impl, error) {
	if len(hostname) == 0 {
		return nil, fmt.Errorf("missing 'hostname' argument")
	}

	return &impl{
		httpClient:       &http.Client{},
		apiUrl:           hostname,
		serviceToken:     serviceToken,
		inviteRedirectTo: inviteRedirectTo,
	}, nil
}

// newRequest creates and sends an HTTP request to the Supabase API.
// This is an internal helper function that handles request creation and authentication.
//
// Parameters:
//   - method: The HTTP method to use
//   - url: The target URL
//   - body: The request body to send
//   - token: The authentication token
//   - headers: Optional additional headers to include
//
// Returns:
//   - *http.Response: The HTTP response if successful
//   - error: Any error that occurred during the request
func (i *impl) newRequest(method, url string, body any, token string, headers ...map[string]string) (*http.Response, error) {
	marshalled, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest(method, url, bytes.NewReader(marshalled))
	if err != nil {
		return nil, err
	}
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", token))
	if len(headers) > 0 {
		for k, v := range headers[0] {
			req.Header.Set(k, v)
		}
	}

	res, err := i.httpClient.Do(req)
	if err != nil {
		return nil, err
	}

	return res, nil
}
