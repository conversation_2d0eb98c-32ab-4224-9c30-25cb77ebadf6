package supabase

import (
	"context"
	"fmt"
	"net/http"
)

// Delete<PERSON><PERSON> implements the SupabaseClient interface for removing a user from Supabase.
// This function sends a DELETE request to the Supabase admin API to permanently remove a user.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: DeleteUserRequest containing the user ID to delete
//
// Returns:
//   - error: Any error that occurred during deletion
func (i *impl) DeleteUser(ctx context.Context, input DeleteUserRequest) error {
	url := fmt.Sprintf("%s/admin/users/%s", i.apiUrl, input.ID)
	resp, err := i.newRequest(http.MethodDelete, url, input, i.serviceToken)
	if err != nil {
		return err
	}

	if resp.StatusCode == http.StatusOK {
		return nil
	}

	return handleErrors(resp.StatusCode)
}
