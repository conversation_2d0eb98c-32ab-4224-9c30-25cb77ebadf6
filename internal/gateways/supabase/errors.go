package supabase

import (
	"errors"
	"net/http"
)

var (
	ErrBadRequestResponse = errors.New("supabase: the request is invalid or malformed")
	ErrUnauthorized       = errors.New("supabase: unauthorized")
	ErrForbidden          = errors.New("supabase: forbidden")
	ErrNotFound           = errors.New("supabase: not found")
	ErrUnknown            = errors.New("supabase: unknown error")
)

func handleErrors(statusCode int) error {
	switch statusCode {
	case http.StatusBadRequest:
		return ErrBadRequestResponse
	case http.StatusUnauthorized:
		return ErrUnauthorized
	case http.StatusForbidden:
		return ErrForbidden
	case http.StatusNotFound:
		return ErrNotFound
	default:
		return ErrUnknown
	}
}
