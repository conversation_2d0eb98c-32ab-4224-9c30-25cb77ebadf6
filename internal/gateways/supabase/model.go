package supabase

import "github.com/google/uuid"

type SignupRequest struct {
	Email    string `json:"email"`
	Password string `json:"password"`
}

type InviteUserRequest struct {
	Email string `json:"email"`
}

type SignupResponse struct {
	ID    uuid.UUID `json:"id"`
	Email string    `json:"email"`
}

type InviteUserResponse struct {
	ID    uuid.UUID `json:"id"`
	Email string    `json:"email"`
}

type DeleteUserRequest struct {
	ID uuid.UUID `json:"id"`
}

type ChangePasswordRequest struct {
	NewPassword string `json:"password"`
}
