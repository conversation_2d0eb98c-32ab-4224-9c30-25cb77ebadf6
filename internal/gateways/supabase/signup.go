package supabase

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"

	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"

	"api-server/pkg/oteltrace"
)

// SignUp implements the SupabaseClient interface for creating a new user account.
// This function sends a POST request to the Supabase API to register a new user.
// The function includes OpenTelemetry tracing for monitoring the signup process.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: SignupRequest containing email and password for the new user
//
// Returns:
//   - *SignupResponse: The created user information if successful
//   - error: Any error that occurred during signup
func (i *impl) SignUp(ctx context.Context, input SignupRequest) (*SignupResponse, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "gateways.supabase.SignUp")
	defer span.End()

	url := fmt.Sprintf("%s/signup", i.apiUrl)
	resp, err := i.newRequest(http.MethodPost, url, input, "")
	if err != nil {
		span.SetStatus(codes.Error, "request to signup failed")
		span.RecordError(err)
		return nil, err
	}
	defer resp.Body.Close()

	var mapResp map[string]interface{}
	if resp.StatusCode == http.StatusOK {
		var signupResponse SignupResponse
		if err := json.NewDecoder(resp.Body).Decode(&signupResponse); err != nil {
			span.SetStatus(codes.Error, "json decode signup response body failed")
			span.RecordError(err)
			return nil, err
		}

		span.AddEvent("user signed up successfully")
		span.SetStatus(codes.Ok, "user signed up successfully")
		return &signupResponse, nil
	}

	if err := json.NewDecoder(resp.Body).Decode(&mapResp); err != nil {
		span.SetStatus(codes.Error, "json decode error response body failed")
		span.RecordError(err)
		return nil, err
	}

	span.AddEvent("signup failed", trace.WithAttributes(attribute.Int("status_code", resp.StatusCode)))
	err = fmt.Errorf("signup failed with status code %d, response: %v", resp.StatusCode, mapResp)
	span.SetStatus(codes.Error, "signup failed")
	span.RecordError(err)
	return nil, err
}
