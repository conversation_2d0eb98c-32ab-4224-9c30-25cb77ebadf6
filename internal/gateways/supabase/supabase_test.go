package supabase_test

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"api-server/internal/gateways/supabase"
)

func TestSignUp(t *testing.T) {
	type dependencies struct {
		httpClient *http.Client
	}
	tests := []struct {
		name             string
		input            supabase.SignupRequest
		mockResponseFail map[string]interface{}
		mockHttpStatus   int
		expectErr        error
		expStatusCode    int
	}{
		{
			name: "should return error when http client returns error",
			input: supabase.SignupRequest{
				Email:    "",
				Password: "",
			},
			mockHttpStatus: http.StatusOK,
			expectErr:      nil,
			expStatusCode:  http.StatusOK,
		},
		{
			name: "should return error when http client returns error",
			input: supabase.SignupRequest{
				Email:    "",
				Password: "",
			},
			mockHttpStatus: http.StatusBadRequest,
			mockResponseFail: map[string]interface{}{
				"message": "error",
			},
			expectErr:     fmt.Errorf("signup failed with status code 400, response: map[message:error]"),
			expStatusCode: http.StatusBadRequest,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			mockSvr := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				w.WriteHeader(tt.mockHttpStatus)
				if tt.mockHttpStatus == http.StatusOK {
					byteArr, err := json.Marshal(tt.mockResponseFail)
					require.NoError(t, err)
					_, err = w.Write(byteArr)
					require.NoError(t, err)
					return
				}
				byteArr, err := json.Marshal(tt.mockResponseFail)
				require.NoError(t, err)
				_, err = w.Write(byteArr)
				require.NoError(t, err)
			}))
			defer mockSvr.Close()

			i, err := supabase.New(mockSvr.URL, "", "")
			assert.NoError(t, err)
			_, err = i.SignUp(ctx, tt.input)
			if tt.expectErr != nil {
				require.EqualError(t, err, tt.expectErr.Error())
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func TestInviteUser(t *testing.T) {
	tests := []struct {
		name             string
		input            supabase.InviteUserRequest
		mockResponseFail map[string]interface{}
		mockHttpStatus   int
		expectErr        error
		expStatusCode    int
	}{
		{
			name: "should return error when status code is not 200",
			input: supabase.InviteUserRequest{
				Email: "<EMAIL>",
			},
			mockHttpStatus:   http.StatusBadRequest,
			mockResponseFail: map[string]interface{}{},
			expectErr:        supabase.ErrBadRequestResponse,
		},
		{
			name: "success",
			input: supabase.InviteUserRequest{
				Email: "<EMAIL>",
			},
			mockHttpStatus: http.StatusOK,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			mockSvr := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				w.WriteHeader(tt.mockHttpStatus)
				byteArr, err := json.Marshal(tt.mockResponseFail)
				require.NoError(t, err)
				_, err = w.Write(byteArr)
				require.NoError(t, err)
			}))
			defer mockSvr.Close()

			i, err := supabase.New(mockSvr.URL, "", "")
			assert.NoError(t, err)
			_, err = i.InviteUser(ctx, tt.input)
			if tt.expectErr != nil {
				require.EqualError(t, err, tt.expectErr.Error())
			} else {
				require.NoError(t, err)
			}
		})
	}
}
