package supabase

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
)

// InviteUser implements the SupabaseClient interface for inviting a user to the platform.
// This function sends a POST request to the Supabase API to create an invitation for a new user.
// The invitation includes a redirect URL where the user will be sent after accepting the invitation.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: InviteUserRequest containing the email to invite
//
// Returns:
//   - *InviteUserResponse: The created user information if successful
//   - error: Any error that occurred during invitation
func (i *impl) InviteUser(ctx context.Context, input InviteUserRequest) (*InviteUserResponse, error) {
	url := fmt.Sprintf("%s/invite", i.apiUrl)
	resp, err := i.newRequest(http.MethodPost, url, input, i.serviceToken, map[string]string{
		"X-JWT-AUD": "authenticated",
		"Referer":   i.inviteRedirectTo,
	})
	if err != nil {
		return nil, err
	}

	if resp.StatusCode == http.StatusOK {
		var inviteUserResponse InviteUserResponse
		if err := json.NewDecoder(resp.Body).Decode(&inviteUserResponse); err != nil {
			return nil, err
		}
		return &inviteUserResponse, nil
	}

	return nil, handleErrors(resp.StatusCode)
}
