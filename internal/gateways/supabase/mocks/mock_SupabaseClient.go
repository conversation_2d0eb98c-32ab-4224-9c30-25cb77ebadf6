// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	supabase "api-server/internal/gateways/supabase"
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// MockSupabaseClient is an autogenerated mock type for the SupabaseClient type
type MockSupabaseClient struct {
	mock.Mock
}

type MockSupabaseClient_Expecter struct {
	mock *mock.Mock
}

func (_m *MockSupabaseClient) EXPECT() *MockSupabaseClient_Expecter {
	return &MockSupabaseClient_Expecter{mock: &_m.Mock}
}

// ChangePassword provides a mock function with given fields: ctx, accessToken, input
func (_m *MockSupabaseClient) ChangePassword(ctx context.Context, accessToken string, input supabase.ChangePasswordRequest) error {
	ret := _m.Called(ctx, accessToken, input)

	if len(ret) == 0 {
		panic("no return value specified for ChangePassword")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, supabase.ChangePasswordRequest) error); ok {
		r0 = rf(ctx, accessToken, input)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockSupabaseClient_ChangePassword_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ChangePassword'
type MockSupabaseClient_ChangePassword_Call struct {
	*mock.Call
}

// ChangePassword is a helper method to define mock.On call
//   - ctx context.Context
//   - accessToken string
//   - input supabase.ChangePasswordRequest
func (_e *MockSupabaseClient_Expecter) ChangePassword(ctx interface{}, accessToken interface{}, input interface{}) *MockSupabaseClient_ChangePassword_Call {
	return &MockSupabaseClient_ChangePassword_Call{Call: _e.mock.On("ChangePassword", ctx, accessToken, input)}
}

func (_c *MockSupabaseClient_ChangePassword_Call) Run(run func(ctx context.Context, accessToken string, input supabase.ChangePasswordRequest)) *MockSupabaseClient_ChangePassword_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(supabase.ChangePasswordRequest))
	})
	return _c
}

func (_c *MockSupabaseClient_ChangePassword_Call) Return(_a0 error) *MockSupabaseClient_ChangePassword_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockSupabaseClient_ChangePassword_Call) RunAndReturn(run func(context.Context, string, supabase.ChangePasswordRequest) error) *MockSupabaseClient_ChangePassword_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteUser provides a mock function with given fields: ctx, input
func (_m *MockSupabaseClient) DeleteUser(ctx context.Context, input supabase.DeleteUserRequest) error {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for DeleteUser")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, supabase.DeleteUserRequest) error); ok {
		r0 = rf(ctx, input)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockSupabaseClient_DeleteUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteUser'
type MockSupabaseClient_DeleteUser_Call struct {
	*mock.Call
}

// DeleteUser is a helper method to define mock.On call
//   - ctx context.Context
//   - input supabase.DeleteUserRequest
func (_e *MockSupabaseClient_Expecter) DeleteUser(ctx interface{}, input interface{}) *MockSupabaseClient_DeleteUser_Call {
	return &MockSupabaseClient_DeleteUser_Call{Call: _e.mock.On("DeleteUser", ctx, input)}
}

func (_c *MockSupabaseClient_DeleteUser_Call) Run(run func(ctx context.Context, input supabase.DeleteUserRequest)) *MockSupabaseClient_DeleteUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(supabase.DeleteUserRequest))
	})
	return _c
}

func (_c *MockSupabaseClient_DeleteUser_Call) Return(_a0 error) *MockSupabaseClient_DeleteUser_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockSupabaseClient_DeleteUser_Call) RunAndReturn(run func(context.Context, supabase.DeleteUserRequest) error) *MockSupabaseClient_DeleteUser_Call {
	_c.Call.Return(run)
	return _c
}

// InviteUser provides a mock function with given fields: ctx, input
func (_m *MockSupabaseClient) InviteUser(ctx context.Context, input supabase.InviteUserRequest) (*supabase.InviteUserResponse, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for InviteUser")
	}

	var r0 *supabase.InviteUserResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, supabase.InviteUserRequest) (*supabase.InviteUserResponse, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, supabase.InviteUserRequest) *supabase.InviteUserResponse); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*supabase.InviteUserResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, supabase.InviteUserRequest) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockSupabaseClient_InviteUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'InviteUser'
type MockSupabaseClient_InviteUser_Call struct {
	*mock.Call
}

// InviteUser is a helper method to define mock.On call
//   - ctx context.Context
//   - input supabase.InviteUserRequest
func (_e *MockSupabaseClient_Expecter) InviteUser(ctx interface{}, input interface{}) *MockSupabaseClient_InviteUser_Call {
	return &MockSupabaseClient_InviteUser_Call{Call: _e.mock.On("InviteUser", ctx, input)}
}

func (_c *MockSupabaseClient_InviteUser_Call) Run(run func(ctx context.Context, input supabase.InviteUserRequest)) *MockSupabaseClient_InviteUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(supabase.InviteUserRequest))
	})
	return _c
}

func (_c *MockSupabaseClient_InviteUser_Call) Return(_a0 *supabase.InviteUserResponse, _a1 error) *MockSupabaseClient_InviteUser_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockSupabaseClient_InviteUser_Call) RunAndReturn(run func(context.Context, supabase.InviteUserRequest) (*supabase.InviteUserResponse, error)) *MockSupabaseClient_InviteUser_Call {
	_c.Call.Return(run)
	return _c
}

// SignUp provides a mock function with given fields: ctx, input
func (_m *MockSupabaseClient) SignUp(ctx context.Context, input supabase.SignupRequest) (*supabase.SignupResponse, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for SignUp")
	}

	var r0 *supabase.SignupResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, supabase.SignupRequest) (*supabase.SignupResponse, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, supabase.SignupRequest) *supabase.SignupResponse); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*supabase.SignupResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, supabase.SignupRequest) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockSupabaseClient_SignUp_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SignUp'
type MockSupabaseClient_SignUp_Call struct {
	*mock.Call
}

// SignUp is a helper method to define mock.On call
//   - ctx context.Context
//   - input supabase.SignupRequest
func (_e *MockSupabaseClient_Expecter) SignUp(ctx interface{}, input interface{}) *MockSupabaseClient_SignUp_Call {
	return &MockSupabaseClient_SignUp_Call{Call: _e.mock.On("SignUp", ctx, input)}
}

func (_c *MockSupabaseClient_SignUp_Call) Run(run func(ctx context.Context, input supabase.SignupRequest)) *MockSupabaseClient_SignUp_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(supabase.SignupRequest))
	})
	return _c
}

func (_c *MockSupabaseClient_SignUp_Call) Return(_a0 *supabase.SignupResponse, _a1 error) *MockSupabaseClient_SignUp_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockSupabaseClient_SignUp_Call) RunAndReturn(run func(context.Context, supabase.SignupRequest) (*supabase.SignupResponse, error)) *MockSupabaseClient_SignUp_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockSupabaseClient creates a new instance of MockSupabaseClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockSupabaseClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockSupabaseClient {
	mock := &MockSupabaseClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
