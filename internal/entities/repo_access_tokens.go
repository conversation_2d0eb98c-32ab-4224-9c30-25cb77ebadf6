package entities

import (
	"time"

	"github.com/google/uuid"
)

type RepoAccessToken struct {
	BaseModel
	RepoID      uuid.UUID `json:"repo_id" gorm:"column:repo_id;not null"`
	Name        string    `json:"name" gorm:"column:name;not null"`
	AccessToken string    `json:"access_token" gorm:"column:access_token;not null"`
	Scopes      string    `json:"scopes" gorm:"column:scopes;not null"`
	Revoked     bool      `json:"revoked" gorm:"column:revoked;not null"`
	RefGitID    int64     `json:"-" gorm:"column:ref_git_id;not null"`
	ExpiresAt   time.Time `json:"expires_at" gorm:"column:expires_at;not null"`
}

func (RepoAccessToken) TableName() string {
	return "repo_access_tokens"
}
