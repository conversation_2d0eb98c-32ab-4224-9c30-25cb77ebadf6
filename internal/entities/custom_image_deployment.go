package entities

import (
	"github.com/google/uuid"
	"github.com/lib/pq"
	"gorm.io/datatypes"

	"api-server/internal/types"
)

// CustomImageDeployment represents a custom image deployment in the system
type CustomImageDeployment struct {
	BaseModel
	DeploymentName string         `gorm:"column:deployment_name;not null"`
	ImageURI       string         `gorm:"column:image_uri;not null"`
	UserID         *uuid.UUID     `gorm:"column:user_id;type:uuid;index"`
	OrgID          *uuid.UUID     `gorm:"column:org_id;type:uuid;index"`
	NodeName       string         `gorm:"column:node_name"`
	Port           int32          `gorm:"column:port"`
	Env            datatypes.JSON `gorm:"column:env"`
	CPU            int            `gorm:"column:num_cpu"`
	Mem            int            `gorm:"column:mem"`     // in mebibytes
	GPUMem         *int           `gorm:"column:gpu_mem"` // in mebibytes
	GPUModel       *string        `gorm:"column:gpu_model"`
	ProxyBodySize  int            `gorm:"column:proxy_body_size;default:100"` // in megabytes

	Volumes        pq.StringArray                   `gorm:"column:volumes;type:text[];default:ARRAY[]::TEXT[]"`
	Author         *string                          `gorm:"column:author"`
	ComposePorts   pq.StringArray                   `gorm:"column:compose_ports;type:text[];default:ARRAY[]::TEXT[]"`
	RestartPolicy  *string                          `gorm:"column:restart_policy"`
	DeploymentType *types.CustomImageDeploymentType `gorm:"column:deployment_type;default:'single'"`
	RepoID         *uuid.UUID                       `gorm:"column:repo_id;type:uuid"`
	Namespace      *string                          `gorm:"column:namespace"`

	User *User
	Repo *Repository
}

// TableName specifies the table name for the CustomImageDeployment entity
func (CustomImageDeployment) TableName() string {
	return "custom_image_deployments"
}
