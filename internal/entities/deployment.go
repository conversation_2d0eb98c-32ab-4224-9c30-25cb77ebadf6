package entities

import (
	"github.com/google/uuid"

	"api-server/internal/enums"
)

type Deployment struct {
	BaseModel
	Name         string                   `gorm:"column:name;not null;unique"`
	Status       enums.ArgoWorkflowStatus `gorm:"column:status"`
	WorkflowName string                   `gorm:"column:workflow_name"`
	// WorkflowStatus enums.ArgoWorkflowStatus `gorm:"column:workflow_status"`
	Duration float32     `gorm:"column:duration"`
	RepoID   uuid.UUID   `gorm:"column:repo_id"`
	Repo     *Repository `gorm:"foreignKey:RepoID"`
	Revision string
	Commit   string

	User   *User      `gorm:"foreignKey:UserID;references:ID"`
	UserID *uuid.UUID `gorm:"column:user_id"`
}

func (Deployment) TableName() string {
	return "deployments"
}
