package entities

import (
	"time"

	"github.com/google/uuid"
)

type UserToken struct {
	BaseModel
	Name        string     `json:"name" gorm:"column:name;not null"`
	AccessToken string     `json:"access_token" gorm:"column:access_token"`
	Scopes      string     `json:"scopes" gorm:"column:scopes;not null"`
	Revoked     bool       `json:"revoked" gorm:"column:revoked;not null"`
	UserID      uuid.UUID  `json:"user_id" gorm:"column:user_id"`
	User        *User      `json:"user,omitempty"`
	ExpiresAt   *time.Time `json:"expires_at" gorm:"column:expires_at"`
}

func (UserToken) TableName() string {
	return "user_access_tokens"
}
