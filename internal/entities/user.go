package entities

import (
	"time"

	"api-server/internal/enums"
)

type User struct {
	BaseModel
	Email                      string           `json:"email" gorm:"-"`
	Name                       string           `json:"name" gorm:"column:name;not null"`
	Username                   string           `json:"username" gorm:"column:username;not null;unique"`
	Avatar                     *string          `json:"avatar" gorm:"column:avatar"`
	RefGitUserID               int64            `json:"ref_git_userid" gorm:"column:ref_git_userid;not null;unique"`
	GitlabAccessToken          *string          `json:"git_access_token" gorm:"column:git_access_token"`
	GitlabAccessTokenExpiresAt *time.Time       `json:"git_access_token_expires_at" gorm:"column:git_access_token_expires_at"`
	Role                       enums.UserRole   `json:"role" gorm:"column:role;not null"`
	Status                     enums.UserStatus `json:"status" gorm:"-"`
}

func (User) TableName() string {
	return "public.users"
}

type UserInfo struct {
	User
	Email            string     `json:"email"`
	EmailConfirmedAt *time.Time `json:"email_confirmed_at"`
}

func (u UserInfo) ToEntity() User {
	u.User.Email = u.Email
	if u.EmailConfirmedAt != nil {
		u.User.Status = enums.UserStatus_Assigned
	} else {
		u.User.Status = enums.UserStatus_Email_Unverified
	}

	return u.User
}
