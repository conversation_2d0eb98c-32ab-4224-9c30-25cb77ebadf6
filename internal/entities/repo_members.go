package entities

import (
	"time"

	"github.com/google/uuid"

	"api-server/internal/enums"
)

type RepoMember struct {
	BaseModel
	UserID uuid.UUID `json:"user_id" gorm:"column:user_id"`
	RepoID uuid.UUID `json:"repo_id" gorm:"column:repo_id"`
	User   *User     `json:"user,omitempty" gorm:"foreignKey:UserID;references:ID"`
	// Repo      *Repository    `json:"repo" gorm:"foreignKey:RepoID;references:ID"`
	Role      enums.RepoRole `json:"role" gorm:"column:role"`
	ExpiresAt *time.Time     `json:"expires_at" gorm:"column:expires_at"`
}

func (RepoMember) TableName() string {
	return "repo_members"
}

type RepoMemberInfo struct {
	RepoMember
	Name  string `json:"name"`
	Email string `json:"email"`
}
