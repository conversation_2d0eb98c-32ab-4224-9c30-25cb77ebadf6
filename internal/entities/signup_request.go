package entities

type SignupRequest struct {
	BaseModel
	Email    string `json:"email" gorm:"column:email;not null;unique"`
	Password string `json:"password" gorm:"column:password; not null"`
	Name     string `json:"name" gorm:"column:name;not null"`
	Username string `json:"username" gorm:"column:username;not null;unique"`
	// UserId   *uuid.UUID `json:"user_id" gorm:"column:user_id;null"`
	// User     *User      `json:"user" gorm:"foreignKey:UserId;references:ID"`
}

func (SignupRequest) TableName() string {
	return "signup_requests"
}
