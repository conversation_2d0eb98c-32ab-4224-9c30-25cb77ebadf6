package entities

import (
	"time"

	"github.com/google/uuid"
)

type OrgToken struct {
	BaseModel
	Name        string     `json:"name" gorm:"column:name;not null"`
	AccessToken string     `json:"access_token" gorm:"column:access_token"`
	Scopes      string     `json:"scopes" gorm:"column:scopes;not null"`
	Revoked     bool       `json:"revoked" gorm:"column:revoked;not null"`
	OrgID       uuid.UUID  `json:"org_id" gorm:"column:org_id"`
	Org         *User      `json:"org,omitempty"`
	ExpiresAt   *time.Time `json:"expires_at" gorm:"column:expires_at"`
}

func (OrgToken) TableName() string {
	return "org_access_tokens"
}
