package entities

import (
	"time"

	"github.com/google/uuid"
)

type AuthUser struct {
	ID                uuid.UUID  `json:"id" gorm:"column:id"`
	Email             string     `json:"email" gorm:"column:email"`
	EmailConfirmedAt  *time.Time `json:"email_confirmed_at" gorm:"column:email_confirmed_at"`
	EncryptedPassword string     `json:"-" gorm:"column:encrypted_password"`
}

func (AuthUser) TableName() string {
	return "auth.users"
}
