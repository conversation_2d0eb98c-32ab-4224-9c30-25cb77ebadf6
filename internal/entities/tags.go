package entities

import "github.com/lib/pq"

type Tag struct {
	BaseModel
	Type      string         `json:"type" gorm:"column:type"`
	SubType   string         `json:"sub_type" gorm:"column:sub_type"`
	Value     string         `json:"value" gorm:"column:value"`
	Name      string         `json:"name" gorm:"column:name"`
	IconUrl   string         `json:"icon_url" gorm:"column:icon_url"`
	Query     string         `json:"query" gorm:"column:query"`
	RepoTypes pq.StringArray `json:"repo_types" gorm:"type:text[],column:repo_types"`
}

func (Tag) TableName() string {
	return "tags"
}
