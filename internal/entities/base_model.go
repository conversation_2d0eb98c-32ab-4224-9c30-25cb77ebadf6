package entities

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type BaseModel struct {
	CreatedAt time.Time `json:"created_at" gorm:"column:created_at"`
	UpdatedAt time.Time `json:"updated_at" gorm:"column:updated_at"`
	ID        uuid.UUID `json:"id" gorm:"column:id;type:uuid;default:uuid_generate_v4();primaryKey;"`
}

// BeforeCreate will auto gen id when create
func (base *BaseModel) BeforeCreate(tx *gorm.DB) (err error) {
	if base.ID == uuid.Nil {
		base.ID = uuid.New() // Generate a new UUID if not already set
	}
	return
}

type DefaultGitGroup struct {
	RefGitModelsID   int64  `json:"ref_git_models_id,omitempty" gorm:"column:ref_git_models_id"`
	RefGitSpacesID   int64  `json:"ref_git_spaces_id,omitempty" gorm:"column:ref_git_spaces_id"`
	RefGitDatasetsID int64  `json:"ref_git_datasets_id,omitempty" gorm:"column:ref_git_datasets_id"`
	RefGitComposesID *int64 `json:"ref_git_composes_id,omitempty" gorm:"column:ref_git_composes_id"`
}
