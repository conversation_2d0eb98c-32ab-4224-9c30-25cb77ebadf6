package entities

import (
	"time"

	"github.com/google/uuid"

	"api-server/internal/enums"
)

type OrgMember struct {
	BaseModel
	User      *User         `json:"user,omitempty" gorm:"foreignKey:UserID;references:ID"`
	UserID    uuid.UUID     `json:"user_id,omitempty" gorm:"column:user_id"`
	Org       *Organization `json:"org,omitempty" gorm:"foreignKey:OrgID;references:ID"`
	OrgID     uuid.UUID     `json:"org_id,omitempty" gorm:"column:org_id"`
	Role      enums.OrgRole `json:"role" gorm:"type:org_role;not null;column:role"`
	ExpiresAt *time.Time    `json:"expires_at" gorm:"column:expires_at"`
}

func (OrgMember) TableName() string {
	return "org_members"
}

type OrgMemberInfo struct {
	OrgMember
	Name   string  `json:"name"`
	Email  string  `json:"email"`
	Avatar *string `json:"avatar"`
}
