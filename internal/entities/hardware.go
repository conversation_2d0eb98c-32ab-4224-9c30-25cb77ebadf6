package entities

import (
	"github.com/google/uuid"
)

type Hardware struct {
	BaseModel
	Name     string  `json:"name" gorm:"column:name"`
	CPU      int     `json:"cpu"  gorm:"column:num_cpu"`
	Mem      int     `json:"mem"  gorm:"column:mem"`     // in mebibytes
	GPUMem   *int    `json:"gpu"  gorm:"column:gpu_mem"` // in mebibytes
	GPUModel *string `json:"gpu_model" gorm:"column:gpu_model"`

	RepoID *uuid.UUID `gorm:"column:repo_id"`
	// Repo   Repository `gorm:"foreignKey:RepoID;references:ID"`
}

func (Hardware) TableName() string {
	return "hardwares"
}
