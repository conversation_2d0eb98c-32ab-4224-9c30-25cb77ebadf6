package entities

import (
	"time"

	"github.com/google/uuid"

	"api-server/internal/enums"
)

type SSHKey struct {
	BaseModel
	User           *User           `json:"user" gorm:"foreignKey:UserID;references:ID"`
	UserID         uuid.UUID       `json:"user_id" gorm:"column:user_id"`
	Name           string          `json:"name" gorm:"column:name;not null"`
	PublicKey      string          `json:"key" gorm:"column:public_key"`
	RefGitSSHKeyID int64           `json:"ref_git_ssh_key_id" gorm:"column:ref_git_sshkeyid"`
	UsageType      enums.UsageType `json:"usage_type" gorm:"column:usage_type"`
	ExpiresAt      *time.Time      `json:"expires_at" gorm:"column:expires_at"`
}

func (SSHKey) TableName() string {
	return "user_ssh_public_keys"
}
