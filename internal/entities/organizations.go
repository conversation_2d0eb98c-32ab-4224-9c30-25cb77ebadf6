package entities

import "api-server/internal/enums"

type Organization struct {
	BaseModel
	Name          string                 `json:"name" gorm:"column:name"`
	PathName      string                 `json:"path_name" gorm:"column:path_name"`
	Type          enums.OrganizationType `json:"type" gorm:"column:type"`
	RefGitGroupID int                    `json:"-" gorm:"column:ref_git_groupid"`
	Avatar        *string                `json:"avatar" gorm:"column:avatar"`
	Interest      *string                `json:"interest" gorm:"column:interest"`
}

func (Organization) TableName() string {
	return "organizations"
}
