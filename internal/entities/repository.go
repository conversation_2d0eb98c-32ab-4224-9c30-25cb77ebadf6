package entities

import (
	"encoding/json"

	"github.com/google/uuid"

	"api-server/internal/enums"
)

type Repository struct {
	BaseModel
	RefGitRepoID int64                `json:"ref_git_repoid,omitempty" gorm:"column:ref_git_repoid"`
	Name         string               `json:"name" gorm:"column:name"`
	Avatar       *string              `json:"avatar,omitempty" gorm:"column:avatar"`
	Visibility   enums.RepoVisibility `json:"visibility" gorm:"column:visibility"`
	Type         enums.RepoType       `json:"type" gorm:"column:type"`
	User         *User                `json:"user,omitempty" gorm:"foreignKey:UserID;references:ID"`
	UserID       *uuid.UUID           `json:"user_id" gorm:"column:user_id"`
	Org          *Organization        `json:"org,omitempty" gorm:"foreignKey:OrgID;references:ID"`
	OrgID        *uuid.UUID           `json:"org_id" gorm:"column:org_id"`
	ReadmeFile   *string              `json:"readme_file,omitempty" gorm:"column:readme_file"`
	Metadata     json.RawMessage      `json:"metadata,omitempty" gorm:"column:metadata;type:jsonb"`

	// HardwareID *uuid.UUID  `gorm:"column:hardware_id"`
	Hardware   *Hardware   `gorm:"foreignKey:RepoID"`
	Deployment *Deployment `gorm:"foreignKey:RepoID"`
}

func (Repository) TableName() string {
	return "repositories"
}

type RepositoryInfo struct {
	Repository
	UserEmail string `json:"user_email"`
}

func (u RepositoryInfo) ToEntity() Repository {
	if u.User != nil {
		u.User.Email = u.UserEmail
	}

	return u.Repository
}
