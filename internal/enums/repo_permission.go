package enums

type RepoPermission string // @name RepoPermission

const (
	RepoPermission_Edit          RepoPermission = "repo.edit"
	RepoPermission_Delete        RepoPermission = "repo.delete"
	RepoPermission_MembersEdit   RepoPermission = "repo.members.edit"
	RepoPermission_MembersDelete RepoPermission = "repo.members.delete"
	RepoPermission_DeployRun     RepoPermission = "repo.deploy.run"
	RepoPermission_DeployStop    RepoPermission = "repo.deploy.stop"
)

func (r RepoPermission) String() string {
	return string(r)
}
