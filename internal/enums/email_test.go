package enums

import (
	"testing"
)

func TestEmailType_String(t *testing.T) {
	tests := []struct {
		name      string
		emailType EmailType
		want      string
	}{
		{
			name:      "SignUp email type",
			emailType: EmailType_SignUp,
			want:      "signup",
		},
		{
			name:      "Invite email type",
			emailType: EmailType_Invite,
			want:      "invite",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.emailType.String(); got != tt.want {
				t.<PERSON>("EmailType.String() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestEmailType_Constants(t *testing.T) {
	if EmailType_SignUp != "signup" {
		t.<PERSON>("EmailType_SignUp = %v, want %v", EmailType_SignUp, "signup")
	}
	if EmailType_Invite != "invite" {
		t.<PERSON><PERSON>("EmailType_Invite = %v, want %v", EmailType_Invite, "invite")
	}
}
