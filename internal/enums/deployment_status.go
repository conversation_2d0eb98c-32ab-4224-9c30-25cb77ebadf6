package enums

type DeploymentStatus string // @name DeploymentStatus

const (
	DEPLOYMENT_STATUS_PENDING  DeploymentStatus = "pending"
	DEPLOYMENT_STATUS_BUILDING DeploymentStatus = "building"
	DEPLOYMENT_STATUS_RUNNING  DeploymentStatus = "running"
	DEPLOYMENT_STATUS_FAILED   DeploymentStatus = "failed"
	DEPLOYMENT_STATUS_OFFLINE  DeploymentStatus = "offline"
)

func (d DeploymentStatus) String() string {
	return string(d)
}
