package enums

type AppPermission string // @name AppPermission

const (
	AppPermission_UsersRead         AppPermission = "users.read"
	AppPermission_UsersEdit         AppPermission = "users.edit"
	AppPermission_UsersDelete       AppPermission = "users.delete"
	AppPermission_UsersApprove      AppPermission = "users.approve"
	AppPermission_UsersInvite       AppPermission = "users.invite"
	AppPermission_KeysRead          AppPermission = "keys.read"
	AppPermission_KeysEdit          AppPermission = "keys.edit"
	AppPermission_KeysDelete        AppPermission = "keys.delete"
	AppPermission_OrgsRead          AppPermission = "organizations.read"
	AppPermission_OrgsEdit          AppPermission = "organizations.edit"
	AppPermission_OrgsDelete        AppPermission = "organizations.delete"
	AppPermission_SpacesAdd         AppPermission = "spaces.add"
	AppPermission_SpacesRead        AppPermission = "spaces.read"
	AppPermission_SpacesFilesRead   AppPermission = "spaces.files.read"
	AppPermission_DatasetsAdd       AppPermission = "datasets.add"
	AppPermission_DatasetsRead      AppPermission = "datasets.read"
	AppPermission_DatasetsFilesRead AppPermission = "datasets.files.read"
	AppPermission_ModelsAdd         AppPermission = "models.add"
	AppPermission_ModelsRead        AppPermission = "models.read"
	AppPermission_ModelsFilesRead   AppPermission = "models.files.read"
	AppPermission_ECRRead           AppPermission = "ecr.read"
	AppPermission_ECREdit           AppPermission = "ecr.edit"
	AppPermission_ECRDelete         AppPermission = "ecr.delete"
)

func (r AppPermission) String() string {
	return string(r)
}
