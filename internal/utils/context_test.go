package utils_test

import (
	"context"
	"testing"

	"api-server/internal/utils"

	"github.com/google/uuid"
)

func TestWithUserId(t *testing.T) {
	// Create a test UUID
	testUUID := uuid.New()

	// Create a base context
	baseCtx := context.Background()

	// Test case 1: Add user ID to empty context
	t.Run("Add user ID to empty context", func(t *testing.T) {
		ctx := utils.WithUserId(baseCtx, testUUID)

		// Verify the user ID was added correctly
		if userId, ok := ctx.Value(utils.ContextKeyUserId).(uuid.UUID); !ok {
			t.Error("Failed to retrieve user ID from context")
		} else if userId != testUUID {
			t.Errorf("Expected user ID %v, got %v", testUUID, userId)
		}
	})

	// Test case 2: Override existing user ID
	t.Run("Override existing user ID", func(t *testing.T) {
		// First add an initial user ID
		initialUUID := uuid.New()
		ctx := utils.WithUserId(baseCtx, initialUUID)

		// Then override it with a new user ID
		newUUID := uuid.New()
		ctx = utils.WithUserId(ctx, newUUID)

		// Verify the new user ID was set correctly
		if userId, ok := ctx.Value(utils.ContextKeyUserId).(uuid.UUID); !ok {
			t.Error("Failed to retrieve user ID from context")
		} else if userId != newUUID {
			t.Errorf("Expected user ID %v, got %v", newUUID, userId)
		}
	})

	// Test case 3: Verify context inheritance
	t.Run("Verify context inheritance", func(t *testing.T) {
		// Create a context with a user ID
		ctx := utils.WithUserId(baseCtx, testUUID)

		// Create a new context with a timeout
		timeoutCtx, cancel := context.WithTimeout(ctx, 0)
		defer cancel()

		// Verify the user ID is still accessible in the child context
		if userId, ok := timeoutCtx.Value(utils.ContextKeyUserId).(uuid.UUID); !ok {
			t.Error("Failed to retrieve user ID from child context")
		} else if userId != testUUID {
			t.Errorf("Expected user ID %v, got %v", testUUID, userId)
		}
	})
}
