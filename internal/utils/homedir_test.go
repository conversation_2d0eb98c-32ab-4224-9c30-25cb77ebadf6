package utils_test

import (
	"os"
	"testing"

	"api-server/internal/utils"
)

func TestHomeDir(t *testing.T) {
	// Save original environment variables
	originalHome := os.Getenv("HOME")
	originalUserProfile := os.Getenv("USERPROFILE")

	// Cleanup function to restore original environment
	defer func() {
		if originalHome != "" {
			os.Setenv("HOME", originalHome)
		} else {
			os.Unsetenv("HOME")
		}
		if originalUserProfile != "" {
			os.Setenv("USERPROFILE", originalUserProfile)
		} else {
			os.Unsetenv("USERPROFILE")
		}
	}()

	tests := []struct {
		name           string
		setHome        string
		setUserProfile string
		want           string
	}{
		{
			name:           "Unix home directory",
			setHome:        "/home/<USER>",
			setUserProfile: "",
			want:           "/home/<USER>",
		},
		{
			name:           "Windows home directory",
			setHome:        "",
			setUserProfile: "C:\\Users\\<USER>\\Users\\user",
		},
		{
			name:           "Unix home takes precedence",
			setHome:        "/home/<USER>",
			setUserProfile: "C:\\Users\\<USER>