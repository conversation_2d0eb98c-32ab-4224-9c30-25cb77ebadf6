package utils

import (
	"errors"
	"fmt"
	"regexp"
	"strings"
)

// ValidatePath checks if the given path is valid and returns an error if not.
func ValidatePath(path string) error {
	// Define the allowed character pattern (non-accented letters, digits, '_', '-', '.')
	validPathRegex := regexp.MustCompile(`^[a-zA-Z0-9_.-]+$`)

	// Trim leading/trailing spaces from the path
	trimmedPath := strings.TrimSpace(path)

	// Check if the path contains only valid characters
	if !validPathRegex.MatchString(trimmedPath) {
		return errors.New("path contains invalid characters. Only non-accented letters, digits, '_', '-', and '.' are allowed")
	}

	// Check if the path starts with '-'
	if strings.HasPrefix(trimmedPath, "-") {
		return errors.New("path cannot start with '-'")
	}

	// Define invalid suffixes
	invalidSuffixes := []string{".", ".git", ".atom"}

	// Check if the path ends with any invalid suffix
	for _, suffix := range invalidSuffixes {
		if strings.HasSuffix(trimmedPath, suffix) {
			return errors.New(fmt.Sprintf("path cannot end with '%s'", suffix))
		}
	}

	// All checks passed, the path is valid
	return nil
}

func IsValidEmail(email string) bool {
	// Simple regex for email validation
	const emailRegex = `^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`
	re := regexp.MustCompile(emailRegex)
	return re.MatchString(email)
}
