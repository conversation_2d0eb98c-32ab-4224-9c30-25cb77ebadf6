package utils_test

import (
	"context"
	"testing"
	"time"

	"api-server/internal/utils"
)

func TestSleep(t *testing.T) {
	tests := []struct {
		name           string
		duration       time.Duration
		cancelDuration time.Duration
		shouldTimeout  bool
	}{
		{
			name:           "sleep completes before cancellation",
			duration:       100 * time.Millisecond,
			cancelDuration: 200 * time.Millisecond,
			shouldTimeout:  true,
		},
		{
			name:           "cancellation before sleep completes",
			duration:       200 * time.Millisecond,
			cancelDuration: 100 * time.Millisecond,
			shouldTimeout:  false,
		},
		{
			name:           "zero duration",
			duration:       0,
			cancelDuration: 100 * time.Millisecond,
			shouldTimeout:  true,
		},
		{
			name:           "immediate cancellation",
			duration:       100 * time.Millisecond,
			cancelDuration: 0,
			shouldTimeout:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx, cancel := context.WithCancel(context.Background())
			start := time.Now()

			// Start a goroutine to cancel the context after cancelDuration
			if tt.cancelDuration > 0 {
				go func() {
					time.Sleep(tt.cancelDuration)
					cancel()
				}()
			} else {
				cancel() // Immediate cancellation
			}

			// Call Sleep
			utils.Sleep(ctx, tt.duration)

			// Calculate actual duration
			actualDuration := time.Since(start)

			// Verify the behavior
			if tt.shouldTimeout {
				// If we expect a timeout, the actual duration should be close to the sleep duration
				if actualDuration < tt.duration {
					t.Errorf("Sleep() completed too early: got %v, want at least %v", actualDuration, tt.duration)
				}
			} else {
				// If we expect cancellation, the actual duration should be close to the cancel duration
				if actualDuration > tt.cancelDuration+50*time.Millisecond {
					t.Errorf("Sleep() took too long to cancel: got %v, want around %v", actualDuration, tt.cancelDuration)
				}
			}
		})
	}
}
