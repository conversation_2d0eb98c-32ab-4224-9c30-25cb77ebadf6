package utils

import (
	"regexp"
	"strings"
)

func Slugify(input string) string {
	// Convert to lowercase
	input = strings.ToLower(input)

	// Replace all non-alphanumeric characters with '-'
	re := regexp.MustCompile(`[^a-z0-9]+`)
	slug := re.ReplaceAllString(input, "-")

	// Trim any leading or trailing '-'
	slug = strings.Trim(slug, "-")

	return slug
}

func TrimNodeName(nodeName string) string {
	// Use regex to extract the GPU model from the label: Tesla-T4-16GB -> Tesla-T4
	// (?i) makes the pattern case-insensitive
	re := regexp.MustCompile(`(?i)-(\d+GB)$`)
	return re.ReplaceAllString(nodeName, "")
}
