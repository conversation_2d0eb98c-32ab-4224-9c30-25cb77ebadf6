package utils_test

import (
	"testing"

	"api-server/internal/utils"
)

func TestIsJWT(t *testing.T) {
	tests := []struct {
		name  string
		token string
		want  bool
	}{
		{
			name:  "valid JWT token",
			token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",
			want:  true,
		},
		{
			name:  "empty token",
			token: "",
			want:  false,
		},
		{
			name:  "token with one part",
			token: "header",
			want:  false,
		},
		{
			name:  "token with two parts",
			token: "header.payload",
			want:  false,
		},
		{
			name:  "token with four parts",
			token: "header.payload.signature.extra",
			want:  false,
		},
		{
			name:  "token with no dots",
			token: "headerpayloadsignature",
			want:  false,
		},
		{
			name:  "token with empty parts",
			token: "..",
			want:  true,
		},
		{
			name:  "token with spaces",
			token: "header . payload . signature",
			want:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := utils.IsJWT(tt.token)
			if got != tt.want {
				t.Errorf("IsJWT() = %v, want %v", got, tt.want)
			}
		})
	}
}
