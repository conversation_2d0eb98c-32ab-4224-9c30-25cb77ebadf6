package utils_test

import (
	"testing"

	"api-server/internal/utils"

	"github.com/stretchr/testify/assert"
)

func TestSlugify(t *testing.T) {
	tests := []struct {
		input  string
		expect string
	}{
		{"SPACES/OWNER/AAA.BBB_CCC", "spaces-owner-aaa-bbb-ccc"},
		{"spaces/owner/aaa", "spaces-owner-aaa"},
		{" spaces/owner/aaa ", "spaces-owner-aaa"},
		{" spaces/owner+/aaa ", "spaces-owner-aaa"},
		{"/spaces//owner//aaa/", "spaces-owner-aaa"},
	}

	for _, test := range tests {
		output := utils.Slugify(test.input)
		assert.Equal(t, test.expect, output)
	}
}

func TestTrimNodeName(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "Tesla T4 with 16GB",
			input:    "Tesla-T4-16GB",
			expected: "Tesla-T4",
		},
		{
			name:     "Tesla V100 with 32GB",
			input:    "Tesla-V100-32GB",
			expected: "Tesla-V100",
		},
		{
			name:     "A100 with 80GB",
			input:    "A100-80GB",
			expected: "A100",
		},
		{
			name:     "no memory suffix",
			input:    "Tesla-T4",
			expected: "Tesla-T4",
		},
		{
			name:     "empty string",
			input:    "",
			expected: "",
		},
		{
			name:     "multiple memory suffixes",
			input:    "Tesla-T4-16GB-32GB",
			expected: "Tesla-T4-16GB",
		},
		{
			name:     "memory suffix in middle",
			input:    "Tesla-16GB-T4",
			expected: "Tesla-16GB-T4",
		},
		{
			name:     "lowercase memory suffix",
			input:    "Tesla-T4-16gb",
			expected: "Tesla-T4",
		},
		{
			name:     "mixed case memory suffix",
			input:    "Tesla-T4-16Gb",
			expected: "Tesla-T4",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := utils.TrimNodeName(tt.input)
			assert.Equal(t, tt.expected, got, "TrimNodeName(%q) = %q, want %q", tt.input, got, tt.expected)
		})
	}
}
