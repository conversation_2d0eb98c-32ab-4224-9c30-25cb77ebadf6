package utils_test

import (
	"encoding/base64"
	"testing"

	"api-server/internal/utils"

	"github.com/stretchr/testify/assert"
)

// TestEncrypt checks that the Encrypt function returns a valid base64-encoded string.
func TestEncrypt(t *testing.T) {
	plaintext := "test plaintext"
	secretKey := "N1PCdw3M2B1TfJhoaY2mL736p2vCUc47"
	// Call the Encrypt function
	encryptedText, err := utils.Encrypt(plaintext, secretKey)
	if err != nil {
		t.Fatalf("Encrypt failed with error: %v", err)
	}

	// Test: Ensure the encrypted text is not empty
	if len(encryptedText) == 0 {
		t.<PERSON>rror("Encrypt returned an empty string")
	}

	// Test: Check if encrypted text is a valid base64 string
	_, err = base64.StdEncoding.DecodeString(encryptedText)
	if err != nil {
		t.<PERSON><PERSON>rf("Encrypt returned an invalid base64 string: %v", err)
	}
}

// TestDecrypt checks that the Decrypt function successfully decrypts the encrypted text.
func TestDecrypt(t *testing.T) {
	plaintext := "test plaintext"
	secretKey := "N1PCdw3M2B1TfJhoaY2mL736p2vCUc47"
	// First, encrypt the plaintext
	encryptedText, err := utils.Encrypt(plaintext, secretKey)
	if err != nil {
		t.Fatalf("Encrypt failed with error: %v", err)
	}

	// Then, decrypt the ciphertext
	decryptedText, err := utils.Decrypt(encryptedText, secretKey)
	if err != nil {
		t.Fatalf("Decrypt failed with error: %v", err)
	}

	// Test: Ensure the decrypted text matches the original plaintext
	if decryptedText != plaintext {
		t.Errorf("Decrypted text (%s) does not match original plaintext (%s)", decryptedText, plaintext)
	}
}

// TestEncryptDecrypt is a combined test to ensure that Encrypt followed by Decrypt returns the original plaintext.
func TestEncryptDecrypt(t *testing.T) {
	plaintext := "this is a secret message"
	secretKey := "N1PCdw3M2B1TfJhoaY2mL736p2vCUc47"
	// Encrypt the plaintext
	encryptedText, err := utils.Encrypt(plaintext, secretKey)
	if err != nil {
		t.Fatalf("Encrypt failed with error: %v", err)
	}

	// Decrypt the ciphertext
	decryptedText, err := utils.Decrypt(encryptedText, secretKey)
	if err != nil {
		t.Fatalf("Decrypt failed with error: %v", err)
	}

	// Test: Ensure the decrypted text matches the original plaintext
	if decryptedText != plaintext {
		t.Errorf("Decrypted text (%s) does not match original plaintext (%s)", decryptedText, plaintext)
	}
}

func TestHashPassword(t *testing.T) {
	tests := []struct {
		name     string
		password string
		wantErr  bool
	}{
		{
			name:     "Valid password",
			password: "validPassword123!",
			wantErr:  false,
		},
		{
			name:     "Empty password",
			password: "",
			wantErr:  false,
		},
		{
			name:     "Long password",
			password: "ThisIsAVeryLongPasswordThatShouldStillWork123!@#$%^&*()",
			wantErr:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			hashedPassword, err := utils.HashPassword(tt.password)
			if tt.wantErr {
				assert.Error(t, err)
				return
			}

			assert.NoError(t, err)
			assert.NotEmpty(t, hashedPassword)
			assert.NotEqual(t, tt.password, hashedPassword)
		})
	}
}

func TestComparePassword(t *testing.T) {
	// First, create a hashed password to use in tests
	password := "testPassword123!"
	hashedPassword, err := utils.HashPassword(password)
	assert.NoError(t, err)

	tests := []struct {
		name           string
		hashedPassword string
		password       string
		wantErr        bool
	}{
		{
			name:           "Valid password match",
			hashedPassword: hashedPassword,
			password:       password,
			wantErr:        false,
		},
		{
			name:           "Invalid password",
			hashedPassword: hashedPassword,
			password:       "wrongPassword123!",
			wantErr:        true,
		},
		{
			name:           "Empty password",
			hashedPassword: hashedPassword,
			password:       "",
			wantErr:        true,
		},
		{
			name:           "Empty hashed password",
			hashedPassword: "",
			password:       password,
			wantErr:        true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := utils.ComparePassword(tt.hashedPassword, tt.password)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestHashPasswordAndCompare(t *testing.T) {
	// Test that a hashed password can be compared with the original password
	password := "testPassword123!"

	// Hash the password
	hashedPassword, err := utils.HashPassword(password)
	assert.NoError(t, err)
	assert.NotEmpty(t, hashedPassword)

	// Compare the hashed password with the original
	err = utils.ComparePassword(hashedPassword, password)
	assert.NoError(t, err)

	// Compare the hashed password with a wrong password
	err = utils.ComparePassword(hashedPassword, "wrongPassword123!")
	assert.Error(t, err)
}
