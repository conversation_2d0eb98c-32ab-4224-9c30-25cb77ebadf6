package linkheader_test

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"api-server/internal/utils/linkheader"
)

func TestNameValidator(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected linkheader.Link
	}{
		{"Valid", "<url>; rel=\"foo\"", linkheader.Link{
			URL:    "url",
			Rel:    "foo",
			Params: map[string]string{},
		}},
		{"Valid url", "<https://example.com?num=1&boolean=true&foo=bar>; rel=\"foo\"", linkheader.Link{
			URL:    "https://example.com?num=1&boolean=true&foo=bar",
			Rel:    "foo",
			Params: map[string]string{},
		}},
		{"Valid with params", "<https://example.com?num=1&boolean=true&foo=bar>; rel=\"foo\"; a=1; b=2", linkheader.Link{
			URL: "https://example.com?num=1&boolean=true&foo=bar",
			Rel: "foo",
			Params: map[string]string{
				"a": "1",
				"b": "2",
			},
		}},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			links := linkheader.Parse(tt.input)

			for _, link := range links {
				assert.Equal(t, tt.expected, link)
			}
		})
	}

}
