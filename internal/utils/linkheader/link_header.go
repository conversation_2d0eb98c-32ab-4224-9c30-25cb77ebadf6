package linkheader

import (
	"strings"
)

// A Link is a single URL and related parameters
type Link struct {
	URL    string
	Rel    string
	Params map[string]string
}

type Links []Link

// Parse parses a raw Link header in the form:
//
//	<url>; rel="foo", <url>; rel="bar"; wat="dis"
//
// returning a slice of Link structs
func Parse(raw string) Links {
	var links Links

	// One chunk: <url>; rel="foo"
	for _, chunk := range strings.Split(raw, ",") {

		link := Link{URL: "", Rel: "", Params: make(map[string]string)}

		// Figure out what each piece of the chunk is
		for _, piece := range strings.Split(chunk, ";") {

			piece = strings.Trim(piece, " ")
			if piece == "" {
				continue
			}

			// URL
			if piece[0] == '<' && piece[len(piece)-1] == '>' {
				link.URL = strings.Trim(piece, "<>")
				continue
			}

			// Params
			key, val := parseParam(piece)
			if key == "" {
				continue
			}

			// Special case for rel
			if strings.ToLower(key) == "rel" {
				link.Rel = val
			} else {
				link.Params[key] = val
			}
		}

		if link.URL != "" {
			links = append(links, link)
		}
	}

	return links
}

// parseParam takes a raw param in the form key="val" and
// returns the key and value as seperate strings
func parseParam(raw string) (key, val string) {

	parts := strings.SplitN(raw, "=", 2)
	if len(parts) == 1 {
		return parts[0], ""
	}
	if len(parts) != 2 {
		return "", ""
	}

	key = parts[0]
	val = strings.Trim(parts[1], "\"")

	return key, val

}
