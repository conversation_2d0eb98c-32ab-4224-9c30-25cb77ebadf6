package utils

import (
	"errors"
	"regexp"
	"strings"
)

// ECRImageInfo contains parsed information from an ECR image URI
type ECRImageInfo struct {
	RegistryID     string
	Region         string
	RepositoryName string
	Tag            string
	Digest         string
}

// ParseECRImageURI parses an ECR image URI and extracts its components
// Expected format: <registry-id>.dkr.ecr.<region>.amazonaws.com/<repository-name>:<tag>
// Or with digest: <registry-id>.dkr.ecr.<region>.amazonaws.com/<repository-name>@<digest>
func ParseECRImageURI(imageURI string) (*ECRImageInfo, error) {
	if imageURI == "" {
		return nil, errors.New("image URI cannot be empty")
	}

	// ECR URI pattern: <registry-id>.dkr.ecr.<region>.amazonaws.com/<repository-name>:<tag>
	// or with digest: <registry-id>.dkr.ecr.<region>.amazonaws.com/<repository-name>@<digest>
	ecrPattern := regexp.MustCompile(`^(\d+)\.dkr\.ecr\.([a-z0-9-]+)\.amazonaws\.com\/([a-zA-Z0-9][a-zA-Z0-9._/-]*[a-zA-Z0-9])(?::([a-zA-Z0-9._-]+)|@(sha256:[a-f0-9]{64}))$`)

	matches := ecrPattern.FindStringSubmatch(imageURI)
	if len(matches) == 0 {
		return nil, errors.New("invalid ECR image URI format")
	}

	info := &ECRImageInfo{
		RegistryID:     matches[1],
		Region:         matches[2],
		RepositoryName: matches[3],
	}

	// Check if it's a tag or digest
	// nolint
	if matches[4] != "" {
		info.Tag = matches[4]
	} else if matches[5] != "" {
		info.Digest = matches[5]
	} else {
		return nil, errors.New("image URI must contain either a tag or digest")
	}

	return info, nil
}

// IsValidECRImageURI checks if the given string is a valid ECR image URI format
func IsValidECRImageURI(imageURI string) bool {
	_, err := ParseECRImageURI(imageURI)
	return err == nil
}

// GetImageIdentifier returns the image identifier (tag or digest) from the ECR image info
func (info *ECRImageInfo) GetImageIdentifier() string {
	if info.Tag != "" {
		return info.Tag
	}
	return info.Digest
}

// IsTaggedImage returns true if the image is identified by a tag (not digest)
func (info *ECRImageInfo) IsTaggedImage() bool {
	return info.Tag != ""
}

// IsDigestImage returns true if the image is identified by a digest (not tag)
func (info *ECRImageInfo) IsDigestImage() bool {
	return info.Digest != ""
}

// GetFullRepositoryURI returns the full repository URI without the tag/digest
func (info *ECRImageInfo) GetFullRepositoryURI() string {
	return info.RegistryID + ".dkr.ecr." + info.Region + ".amazonaws.com/" + info.RepositoryName
}

// ValidateECRImageURI validates an ECR image URI and returns detailed error messages
func ValidateECRImageURI(imageURI string) error {
	if imageURI == "" {
		return errors.New("image URI cannot be empty")
	}

	// Trim whitespace
	imageURI = strings.TrimSpace(imageURI)

	// Check basic format
	if !strings.Contains(imageURI, ".dkr.ecr.") {
		return errors.New("image URI must be from Amazon ECR (should contain '.dkr.ecr.')")
	}

	if !strings.Contains(imageURI, ".amazonaws.com") {
		return errors.New("image URI must be from Amazon ECR (should contain '.amazonaws.com')")
	}

	// Parse and validate
	_, err := ParseECRImageURI(imageURI)
	if err != nil {
		return err
	}

	return nil
}
