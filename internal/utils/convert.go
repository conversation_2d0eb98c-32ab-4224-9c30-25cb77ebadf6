package utils

import (
	"time"
	"unicode"
	"unicode/utf8"
)

func ConvertStrToTime(timeStr, format string) (time.Time, error) {
	result, err := time.Parse(format, timeStr)
	if err != nil {
		return time.Time{}, err
	}
	return result, nil
}

func ConvertTimeToString(i *time.Time) *string {
	if i != nil {
		formattedTime := i.Format(time.RFC3339)
		return &formattedTime
	}

	return nil
}

func Ptr[T any](v T) *T {
	return &v
}

func CapitalizeFirst(s string) string {
	if len(s) == 0 {
		return s
	}
	// if first character is not a letter, return the string as is
	firstRune, size := utf8.DecodeRuneInString(s)
	if firstRune == utf8.RuneError && size == 1 {
		return s
	}
	// if first character is a letter, capitalize it
	upperFirst := unicode.ToUpper(firstRune)
	if upperFirst == firstRune {
		return s
	}

	return string(upperFirst) + s[size:]
}
