package utils_test

import (
	"bytes"
	"errors"
	"io"
	"mime/multipart"
	"testing"

	"api-server/internal/utils"
)

// TestValidatePath tests the validatePath function for various cases
func TestValidatePath(t *testing.T) {
	tests := []struct {
		path          string
		expectedError bool   // We want to check if there's an error (true means error expected)
		errorMessage  string // The expected error message
	}{
		{"valid_path", false, ""},
		{"-invalid_start", true, "path cannot start with '-'"},
		{"invalid_end.git", true, "path cannot end with '.git'"},
		{"invalid_end.atom", true, "path cannot end with '.atom'"},
		{"invalid.end.", true, "path cannot end with '.'"},
		{"valid.path_123", false, ""},
		{"invalid#path", true, "path contains invalid characters. Only non-accented letters, digits, '_', '-', and '.' are allowed"},
	}

	for _, test := range tests {
		err := utils.ValidatePath(test.path)
		if test.expectedError {
			if err == nil {
				t.<PERSON>rf("Expected an error for path '%s', but got nil", test.path)
			} else if err.Error() != test.errorMessage {
				t.Errorf("Expected error message '%s', but got '%s'", test.errorMessage, err.Error())
			}
		} else {
			if err != nil {
				t.Errorf("Expected no error for path '%s', but got error: %s", test.path, err)
			}
		}
	}
}

// TestIsValidEmail tests the IsValidEmail function for various cases
func TestIsValidEmail(t *testing.T) {
	tests := []struct {
		email         string
		expectedValid bool
	}{
		{"<EMAIL>", true},
		{"<EMAIL>", true},
		{"<EMAIL>", true},
		{"user@localserver", false},
		{"@example.com", false},
		{"plainaddress", false},
		{"user@com", false},
	}

	for _, test := range tests {
		valid := utils.IsValidEmail(test.email)
		if valid != test.expectedValid {
			t.Errorf("Expected IsValidEmail(%s) to be %v, but got %v", test.email, test.expectedValid, valid)
		}
	}
}

// createTestFile creates a multipart.FileHeader for testing
func createTestFile(content []byte, filename string) *multipart.FileHeader {
	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)
	part, _ := writer.CreateFormFile("file", filename)
	part.Write(content)
	writer.Close()

	reader := multipart.NewReader(body, writer.Boundary())
	form, _ := reader.ReadForm(0)
	return form.File["file"][0]
}

func TestValidateImage(t *testing.T) {
	// Create test image data (minimal valid PNG)
	pngData := []byte{
		0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, // PNG signature
		0x00, 0x00, 0x00, 0x0D, // IHDR chunk length
		0x49, 0x48, 0x44, 0x52, // "IHDR"
		0x00, 0x00, 0x00, 0x01, // width
		0x00, 0x00, 0x00, 0x01, // height
		0x08, // bit depth
		0x06, // color type
		0x00, // compression method
		0x00, // filter method
		0x00, // interlace method
	}

	// Create a large PNG by repeating the minimal PNG data
	largePngData := make([]byte, 6*1024*1024) // 6MB
	copy(largePngData, pngData)

	// Create test text data
	textData := []byte("This is not an image file")

	tests := []struct {
		name        string
		file        *multipart.FileHeader
		maxSize     int64
		expectError error
	}{
		{
			name:        "valid PNG image",
			file:        createTestFile(pngData, "test.png"),
			maxSize:     1024 * 1024, // 1MB
			expectError: nil,
		},
		{
			name:        "non-image file",
			file:        createTestFile(textData, "test.txt"),
			maxSize:     1024 * 1024,
			expectError: errors.New("only image files are allowed"),
		},
		{
			name:        "file too large",
			file:        createTestFile(largePngData, "large.png"),
			maxSize:     5 * 1024 * 1024, // 5MB
			expectError: errors.New("file size exceeds limit"),
		},
		{
			name:        "zero max size (should use default)",
			file:        createTestFile(pngData, "test.png"),
			maxSize:     0,
			expectError: nil,
		},
		{
			name:        "empty file",
			file:        createTestFile([]byte{}, "empty.png"),
			maxSize:     1024 * 1024,
			expectError: io.EOF,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := utils.ValidateImage(tt.maxSize, tt.file)
			if tt.expectError == nil {
				if err != nil {
					t.Errorf("ValidateImage() error = %v, want nil", err)
				}
			} else {
				if err == nil {
					t.Errorf("ValidateImage() error = nil, want %v", tt.expectError)
				} else if err.Error() != tt.expectError.Error() {
					t.Errorf("ValidateImage() error = %v, want %v", err, tt.expectError)
				}
			}
		})
	}
}
