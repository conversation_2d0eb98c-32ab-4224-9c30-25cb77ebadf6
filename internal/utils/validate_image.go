package utils

import (
	"errors"
	"mime/multipart"
	"net/http"
	"strings"
)

func ValidateImage(imageMaxsize int64, file *multipart.FileHeader) error {
	src, err := file.Open()
	if err != nil {
		return err
	}
	defer src.Close()

	buffer := make([]byte, 512)
	_, err = src.Read(buffer)
	if err != nil {
		return err
	}

	contentType := http.DetectContentType(buffer)
	if !strings.HasPrefix(contentType, "image/") {
		return errors.New("only image files are allowed")
	}

	if imageMaxsize == 0 {
		imageMaxsize = 5 * 1024 * 1024 //default: 5MB
	}

	if file.Size > imageMaxsize {
		return errors.New("file size exceeds limit")
	}

	return nil
}
