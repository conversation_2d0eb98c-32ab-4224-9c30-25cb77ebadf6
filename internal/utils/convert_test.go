package utils_test

import (
	"testing"
	"time"

	"api-server/internal/utils"
)

func TestConvertStringToTime(t *testing.T) {
	tests := []struct {
		name    string
		timeStr string
		format  string
		want    time.Time
	}{
		{
			name:    "success with RFC3339",
			timeStr: "2026-01-02T15:04:05Z",
			format:  time.RFC3339,
			want:    time.Date(2026, 1, 2, 15, 4, 5, 0, time.UTC),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := utils.ConvertStrToTime(tt.timeStr, tt.format)
			if err != nil {
				t.<PERSON>rf("ConvertStringToTime() error = %v", err)
				return
			}
			if !got.Equal(tt.want) {
				t.<PERSON>rf("ConvertStringToTime() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCapitalizeFirst(t *testing.T) {
	tests := []struct {
		name  string
		input string
		want  string
	}{
		{
			name:  "capitalize normal word",
			input: "hello",
			want:  "Hello",
		},
		{
			name:  "already capitalized",
			input: "Hello",
			want:  "Hello",
		},
		{
			name:  "empty string",
			input: "",
			want:  "",
		},
		{
			name:  "non-letter start",
			input: "123abc",
			want:  "123abc",
		},
		{
			name:  "emoji start",
			input: "😎cool",
			want:  "😎cool",
		},
		{
			name:  "single lowercase letter",
			input: "a",
			want:  "A",
		},
		{
			name:  "single uppercase letter",
			input: "A",
			want:  "A",
		},
		{
			name:  "unicode letter",
			input: "ñandú",
			want:  "Ñandú",
		},
		{
			name:  "already capitalized unicode letter",
			input: "Ñandú",
			want:  "Ñandú",
		},
		{
			name:  "empty",
			input: "",
			want:  "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := utils.CapitalizeFirst(tt.input)
			if got != tt.want {
				t.Errorf("CapitalizeFirst() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestConvertTimeToString(t *testing.T) {
	now := time.Now()
	utcTime := time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC)

	tests := []struct {
		name     string
		input    *time.Time
		expected *string
	}{
		{
			name:     "nil time",
			input:    nil,
			expected: nil,
		},
		{
			name:     "current time",
			input:    &now,
			expected: utils.Ptr(now.Format(time.RFC3339)),
		},
		{
			name:     "UTC time",
			input:    &utcTime,
			expected: utils.Ptr("2024-01-01T12:00:00Z"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := utils.ConvertTimeToString(tt.input)
			if tt.expected == nil {
				if got != nil {
					t.Errorf("ConvertTimeToString() = %v, want nil", got)
				}
				return
			}
			if got == nil {
				t.Errorf("ConvertTimeToString() = nil, want %v", *tt.expected)
				return
			}
			if *got != *tt.expected {
				t.Errorf("ConvertTimeToString() = %v, want %v", *got, *tt.expected)
			}
		})
	}
}

func TestPtr(t *testing.T) {
	tests := []struct {
		name     string
		input    interface{}
		expected interface{}
	}{
		{
			name:     "string pointer",
			input:    "test",
			expected: "test",
		},
		{
			name:     "int pointer",
			input:    42,
			expected: 42,
		},
		{
			name:     "bool pointer",
			input:    true,
			expected: true,
		},
		{
			name:     "float pointer",
			input:    3.14,
			expected: 3.14,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			switch v := tt.input.(type) {
			case string:
				got := utils.Ptr(v)
				if *got != tt.expected {
					t.Errorf("Ptr() = %v, want %v", *got, tt.expected)
				}
			case int:
				got := utils.Ptr(v)
				if *got != tt.expected {
					t.Errorf("Ptr() = %v, want %v", *got, tt.expected)
				}
			case bool:
				got := utils.Ptr(v)
				if *got != tt.expected {
					t.Errorf("Ptr() = %v, want %v", *got, tt.expected)
				}
			case float64:
				got := utils.Ptr(v)
				if *got != tt.expected {
					t.Errorf("Ptr() = %v, want %v", *got, tt.expected)
				}
			}
		})
	}
}
