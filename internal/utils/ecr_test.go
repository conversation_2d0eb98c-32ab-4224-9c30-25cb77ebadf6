package utils_test

import (
	"testing"

	"api-server/internal/utils"
)

func TestParseECRImageURI(t *testing.T) {
	tests := []struct {
		name        string
		imageURI    string
		expected    *utils.ECRImageInfo
		expectError bool
	}{
		{
			name:     "valid ECR URI with tag",
			imageURI: "123456789012.dkr.ecr.us-west-2.amazonaws.com/my-repo:latest",
			expected: &utils.ECRImageInfo{
				RegistryID:     "123456789012",
				Region:         "us-west-2",
				RepositoryName: "my-repo",
				Tag:            "latest",
			},
			expectError: false,
		},
		{
			name:     "valid ECR URI with digest",
			imageURI: "123456789012.dkr.ecr.us-east-1.amazonaws.com/my-app@sha256:abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890",
			expected: &utils.ECRImageInfo{
				RegistryID:     "123456789012",
				Region:         "us-east-1",
				RepositoryName: "my-app",
				Digest:         "sha256:abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890",
			},
			expectError: false,
		},
		{
			name:     "valid ECR URI with nested repository name",
			imageURI: "123456789012.dkr.ecr.eu-west-1.amazonaws.com/team/project/service:v1.0.0",
			expected: &utils.ECRImageInfo{
				RegistryID:     "123456789012",
				Region:         "eu-west-1",
				RepositoryName: "team/project/service",
				Tag:            "v1.0.0",
			},
			expectError: false,
		},
		{
			name:        "empty URI",
			imageURI:    "",
			expected:    nil,
			expectError: true,
		},
		{
			name:        "invalid format - not ECR",
			imageURI:    "docker.io/library/nginx:latest",
			expected:    nil,
			expectError: true,
		},
		{
			name:        "invalid format - missing tag/digest",
			imageURI:    "123456789012.dkr.ecr.us-west-2.amazonaws.com/my-repo",
			expected:    nil,
			expectError: true,
		},
		{
			name:        "invalid format - wrong domain",
			imageURI:    "123456789012.dkr.ecr.us-west-2.example.com/my-repo:latest",
			expected:    nil,
			expectError: true,
		},
		{
			name:        "invalid registry ID format",
			imageURI:    "invalid.dkr.ecr.us-west-2.amazonaws.com/my-repo:latest",
			expected:    nil,
			expectError: true,
		},
		{
			name:        "invalid region format",
			imageURI:    "123456789012.dkr.ecr.invalid_region.amazonaws.com/my-repo:latest",
			expected:    nil,
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := utils.ParseECRImageURI(tt.imageURI)

			if tt.expectError {
				if err == nil {
					t.Errorf("expected error but got none")
				}
				return
			}

			if err != nil {
				t.Errorf("unexpected error: %v", err)
				return
			}

			if result == nil {
				t.Errorf("expected result but got nil")
				return
			}

			if result.RegistryID != tt.expected.RegistryID {
				t.Errorf("expected RegistryID %s, got %s", tt.expected.RegistryID, result.RegistryID)
			}

			if result.Region != tt.expected.Region {
				t.Errorf("expected Region %s, got %s", tt.expected.Region, result.Region)
			}

			if result.RepositoryName != tt.expected.RepositoryName {
				t.Errorf("expected RepositoryName %s, got %s", tt.expected.RepositoryName, result.RepositoryName)
			}

			if result.Tag != tt.expected.Tag {
				t.Errorf("expected Tag %s, got %s", tt.expected.Tag, result.Tag)
			}

			if result.Digest != tt.expected.Digest {
				t.Errorf("expected Digest %s, got %s", tt.expected.Digest, result.Digest)
			}
		})
	}
}

func TestIsValidECRImageURI(t *testing.T) {
	tests := []struct {
		name     string
		imageURI string
		expected bool
	}{
		{
			name:     "valid ECR URI with tag",
			imageURI: "123456789012.dkr.ecr.us-west-2.amazonaws.com/my-repo:latest",
			expected: true,
		},
		{
			name:     "valid ECR URI with digest",
			imageURI: "123456789012.dkr.ecr.us-east-1.amazonaws.com/my-app@sha256:abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890",
			expected: true,
		},
		{
			name:     "invalid format",
			imageURI: "docker.io/library/nginx:latest",
			expected: false,
		},
		{
			name:     "empty URI",
			imageURI: "",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := utils.IsValidECRImageURI(tt.imageURI)
			if result != tt.expected {
				t.Errorf("expected %v, got %v", tt.expected, result)
			}
		})
	}
}

func TestECRImageInfo_Methods(t *testing.T) {
	// Test with tag
	taggedImage := &utils.ECRImageInfo{
		RegistryID:     "123456789012",
		Region:         "us-west-2",
		RepositoryName: "my-repo",
		Tag:            "latest",
	}

	if !taggedImage.IsTaggedImage() {
		t.Error("expected IsTaggedImage to return true")
	}

	if taggedImage.IsDigestImage() {
		t.Error("expected IsDigestImage to return false")
	}

	if taggedImage.GetImageIdentifier() != "latest" {
		t.Errorf("expected GetImageIdentifier to return 'latest', got %s", taggedImage.GetImageIdentifier())
	}

	expectedRepoURI := "123456789012.dkr.ecr.us-west-2.amazonaws.com/my-repo"
	if taggedImage.GetFullRepositoryURI() != expectedRepoURI {
		t.Errorf("expected GetFullRepositoryURI to return %s, got %s", expectedRepoURI, taggedImage.GetFullRepositoryURI())
	}

	// Test with digest
	digestImage := &utils.ECRImageInfo{
		RegistryID:     "123456789012",
		Region:         "us-east-1",
		RepositoryName: "my-app",
		Digest:         "sha256:abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890",
	}

	if digestImage.IsTaggedImage() {
		t.Error("expected IsTaggedImage to return false")
	}

	if !digestImage.IsDigestImage() {
		t.Error("expected IsDigestImage to return true")
	}

	expectedDigest := "sha256:abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890"
	if digestImage.GetImageIdentifier() != expectedDigest {
		t.Errorf("expected GetImageIdentifier to return %s, got %s", expectedDigest, digestImage.GetImageIdentifier())
	}
}

func TestValidateECRImageURI(t *testing.T) {
	tests := []struct {
		name        string
		imageURI    string
		expectError bool
		errorMsg    string
	}{
		{
			name:        "valid ECR URI",
			imageURI:    "123456789012.dkr.ecr.us-west-2.amazonaws.com/my-repo:latest",
			expectError: false,
		},
		{
			name:        "empty URI",
			imageURI:    "",
			expectError: true,
			errorMsg:    "image URI cannot be empty",
		},
		{
			name:        "not ECR - missing dkr.ecr",
			imageURI:    "docker.io/library/nginx:latest",
			expectError: true,
			errorMsg:    "image URI must be from Amazon ECR (should contain '.dkr.ecr.')",
		},
		{
			name:        "not ECR - missing amazonaws.com",
			imageURI:    "123456789012.dkr.ecr.us-west-2.example.com/my-repo:latest",
			expectError: true,
			errorMsg:    "image URI must be from Amazon ECR (should contain '.amazonaws.com')",
		},
		{
			name:        "invalid format",
			imageURI:    "invalid.dkr.ecr.us-west-2.amazonaws.com/my-repo:latest",
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := utils.ValidateECRImageURI(tt.imageURI)

			if tt.expectError {
				if err == nil {
					t.Errorf("expected error but got none")
					return
				}
				if tt.errorMsg != "" && err.Error() != tt.errorMsg {
					t.Errorf("expected error message '%s', got '%s'", tt.errorMsg, err.Error())
				}
			} else {
				if err != nil {
					t.Errorf("unexpected error: %v", err)
				}
			}
		})
	}
}
