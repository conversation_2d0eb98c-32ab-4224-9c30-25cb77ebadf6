package middlewares

import (
	"net/http"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

// NewCORSConfig initializes and returns a CORS middleware configuration for the Gin framework.
// This middleware handles Cross-Origin Resource Sharing (CORS) headers and permissions.
// It allows specified origins, HTTP methods, and headers while maintaining security.
//
// Parameters:
//   - origins: List of allowed origin URLs that can access the API
//
// Returns:
//   - gin.HandlerFunc: Gin middleware function that handles CORS headers and preflight requests
func NewCORSConfig(origins []string) gin.HandlerFunc {
	return cors.New(cors.Config{
		AllowOrigins: origins,
		AllowMethods: []string{http.MethodGet, http.MethodPost, http.MethodDelete, http.MethodPut, http.MethodPatch},
		AllowHeaders: []string{
			"Accept",
			"Authorization",
			"Content-Type",
		},
		AllowCredentials: true,
	})
}
