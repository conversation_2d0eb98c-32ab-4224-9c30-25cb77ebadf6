package middlewares_test

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap/zaptest"

	"api-server/internal/middlewares"
	"api-server/pkg/otelzap"
)

type testResponse struct {
	Message string `json:"message"`
}

func TestLogging(t *testing.T) {
	// Set Gin to test mode
	gin.SetMode(gin.TestMode)

	// Create a test logger
	logger := zaptest.NewLogger(t)
	otelzap.Logger = logger

	tests := []struct {
		name           string
		method         string
		path           string
		contentType    string
		requestBody    interface{}
		responseBody   interface{}
		expectedStatus int
		checkBody      bool
	}{
		{
			name:           "JSON request and response",
			method:         http.MethodPost,
			path:           "/api/test",
			contentType:    "application/json",
			requestBody:    map[string]string{"key": "value"},
			responseBody:   testResponse{Message: "success"},
			expectedStatus: http.StatusOK,
			checkBody:      true,
		},
		{
			name:           "Non-JSON request",
			method:         http.MethodGet,
			path:           "/api/test",
			contentType:    "text/plain",
			requestBody:    nil,
			responseBody:   nil,
			expectedStatus: http.StatusOK,
			checkBody:      false,
		},
		{
			name:           "Swagger endpoint",
			method:         http.MethodGet,
			path:           "/swagger/index.html",
			contentType:    "application/json",
			requestBody:    map[string]string{"key": "value"},
			responseBody:   testResponse{Message: "swagger response"},
			expectedStatus: http.StatusOK,
			checkBody:      true,
		},
		{
			name:           "Error response",
			method:         http.MethodPost,
			path:           "/api/error",
			contentType:    "application/json",
			requestBody:    map[string]string{"key": "value"},
			responseBody:   nil,
			expectedStatus: http.StatusBadRequest,
			checkBody:      true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create a new Gin router
			router := gin.New()
			router.Use(middlewares.Logging())

			// Add test endpoint
			router.Handle(tt.method, tt.path, func(c *gin.Context) {
				if tt.expectedStatus == http.StatusBadRequest {
					c.Error(gin.Error{Err: assert.AnError, Type: gin.ErrorTypePrivate})
				}
				if tt.responseBody != nil {
					c.JSON(tt.expectedStatus, tt.responseBody)
				} else {
					c.Status(tt.expectedStatus)
				}
			})

			// Create test request
			var reqBody []byte
			if tt.requestBody != nil {
				reqBody, _ = json.Marshal(tt.requestBody)
			}
			req := httptest.NewRequest(tt.method, tt.path, bytes.NewBuffer(reqBody))
			req.Header.Set("Content-Type", tt.contentType)

			// Create response recorder
			w := httptest.NewRecorder()

			// Perform request
			router.ServeHTTP(w, req)

			// Verify response
			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.checkBody {
				if tt.responseBody != nil {
					var response testResponse
					err := json.Unmarshal(w.Body.Bytes(), &response)
					assert.NoError(t, err)
					assert.Equal(t, tt.responseBody.(testResponse).Message, response.Message)
				}
			}
		})
	}
}

func TestLoggingWithTrace(t *testing.T) {
	// Set Gin to test mode
	gin.SetMode(gin.TestMode)

	// Create a test logger
	logger := zaptest.NewLogger(t)
	otelzap.Logger = logger

	// Create a new Gin router
	router := gin.New()
	router.Use(middlewares.Logging())

	// Add test endpoint
	router.GET("/api/trace", func(c *gin.Context) {
		// Create a mock span context
		spanCtx := trace.NewSpanContext(trace.SpanContextConfig{
			TraceID: trace.TraceID{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16},
			SpanID:  trace.SpanID{1, 2, 3, 4, 5, 6, 7, 8},
		})
		ctx := trace.ContextWithSpanContext(c.Request.Context(), spanCtx)
		c.Request = c.Request.WithContext(ctx)
		c.JSON(http.StatusOK, testResponse{Message: "traced"})
	})

	// Create test request
	req := httptest.NewRequest(http.MethodGet, "/api/trace", nil)
	w := httptest.NewRecorder()

	// Perform request
	router.ServeHTTP(w, req)

	// Verify response
	assert.Equal(t, http.StatusOK, w.Code)
}
