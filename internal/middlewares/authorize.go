package middlewares

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"

	"api-server/internal/dto"
	"api-server/internal/enums"
	"api-server/internal/types"
	orgusecase "api-server/internal/usecase/organization"
	userusecase "api-server/internal/usecase/user"
	"api-server/internal/utils"
	"api-server/pkg/jwt"
	"api-server/pkg/oteltrace"
	"api-server/pkg/otelzap"
)

// BearerAuth is a middleware that validates Bearer access tokens in the Authorization header.
// It supports both JWT tokens and API keys for authentication.
// The middleware includes OpenTelemetry tracing for monitoring the authentication process.
//
// Parameters:
//   - jwtSecret: Secret key for JWT validation
//   - user: UserUsecase interface for user-related operations
//
// Returns:
//   - gin.HandlerFunc: Gin middleware function that validates the token and sets user context
func BearerAuth(jwtSecret string, user userusecase.UserUsecase) gin.HandlerFunc {
	prefix := "Bearer "
	jwtSecretBytes := []byte(jwtSecret)
	return func(c *gin.Context) {
		ctx, span := oteltrace.Tracer.Start(c.Request.Context(), "middlewares.BearerAuth", trace.WithAttributes(attribute.String("user_id", "")))
		defer span.End()

		bearer := c.GetHeader("Authorization")

		if bearer == "" || !strings.HasPrefix(bearer, prefix) {
			span.SetStatus(codes.Error, ErrMissingAuthorization.Error())
			span.RecordError(ErrMissingAuthorization)
			otelzap.ErrorWithContext(ctx, "missing authorization header", ErrMissingAuthorization)
			c.JSON(http.StatusUnauthorized, dto.NewHTTPError(http.StatusUnauthorized, ErrMissingAuthorization))
			c.Abort()
			return
		}

		token := strings.TrimPrefix(bearer, prefix)

		var userID *uuid.UUID
		if utils.IsJWT(token) {
			// JWT
			claims, err := jwt.ValidateJWT(ctx, token, jwtSecretBytes)
			if err != nil {
				span.SetStatus(codes.Error, ErrInvalidToken.Error())
				span.RecordError(ErrInvalidToken)
				otelzap.ErrorWithContext(ctx, "invalid jwt token", ErrInvalidToken)
				c.JSON(http.StatusUnauthorized, dto.NewHTTPError(http.StatusUnauthorized, ErrInvalidToken))
				c.Abort()
				return
			}
			userID = &claims.UserID

			otelzap.InfoWithContext(ctx, "authorized user via bearer auth", zap.String("user_id", userID.String()))
		} else {
			// API key
			data, err := user.AuthorizeAccessToken(ctx, token)
			if err != nil {
				span.SetStatus(codes.Error, err.Error())
				span.RecordError(err)
				otelzap.ErrorWithContext(ctx, "unauthorized api token", err)
				dto.ErrorResponse(c, err)
				c.Abort()
				return
			}
			if data == nil {
				span.SetStatus(codes.Error, ErrInvalidToken.Error())
				span.RecordError(ErrInvalidToken)
				otelzap.ErrorWithContext(ctx, "invalid api token", ErrInvalidToken)
				dto.ErrorResponse(c, dto.NewUnauthorizedError(ErrInvalidToken))
				c.Abort()
				return
			}
			userID = data

			otelzap.InfoWithContext(ctx, "authorized user via api token", zap.String("user_id", userID.String()))
		}

		if err := user.UpsertUser(ctx, *userID); err != nil {
			span.SetStatus(codes.Error, err.Error())
			span.RecordError(err)
			dto.ErrorResponse(c, dto.NewInternalError(err, "internal error"))
			c.Abort()
			return
		}

		span.SetAttributes(attribute.KeyValue{Key: "user_id", Value: attribute.StringValue(userID.String())})
		c.Set(enums.USER_ID, *userID)
		c.Next()
	}
}

// TokenAuthOnQuery is a middleware that validates access tokens from query parameters.
// It checks for the token in the format `/a?access_token=BEARER_TOKEN`.
// The middleware includes OpenTelemetry tracing for monitoring the authentication process.
//
// Parameters:
//   - jwtSecret: Secret key for JWT validation
//
// Returns:
//   - gin.HandlerFunc: Gin middleware function that validates the token and sets user context
func TokenAuthOnQuery(jwtSecret string) gin.HandlerFunc {

	jwtSecretBytes := []byte(jwtSecret)
	return func(c *gin.Context) {
		ctx, span := oteltrace.Tracer.Start(c.Request.Context(), "middlewares.TokenAuthOnQuery")
		defer span.End()

		token, exists := c.GetQuery("access_token")
		if !exists {
			span.SetStatus(codes.Error, ErrMissingAccessToken.Error())
			span.RecordError(ErrMissingAccessToken)
			otelzap.ErrorWithContext(ctx, "missing access_token query parameter", ErrMissingAccessToken)
			c.JSON(http.StatusUnauthorized, dto.NewHTTPError(http.StatusUnauthorized, ErrMissingAccessToken))
			c.Abort()
			return
		}

		claims, err := jwt.ValidateJWT(ctx, token, jwtSecretBytes)
		if err != nil {
			span.SetStatus(codes.Error, ErrInvalidToken.Error())
			span.RecordError(ErrInvalidToken)
			otelzap.ErrorWithContext(ctx, "invalid jwt token", ErrInvalidToken)
			c.JSON(http.StatusUnauthorized, dto.NewHTTPError(http.StatusUnauthorized, ErrInvalidToken))
			c.Abort()
			return
		}

		c.Set(enums.USER_ID, claims.UserID)
		otelzap.InfoWithContext(ctx, "authorized user via token on query", zap.String("user_id", claims.UserID.String()))
		span.SetAttributes(attribute.KeyValue{Key: "user_id", Value: attribute.StringValue(claims.UserID.String())})
		c.Next()
	}
}

type PlatformPermissionHandlerFunc func(requiredPermissions ...enums.AppPermission) gin.HandlerFunc

// PlatformPermission is a middleware that checks user permissions for platform-level access.
// It verifies if the user has the required permissions to access platform features.
// The middleware includes OpenTelemetry tracing for monitoring the authorization process.
//
// Parameters:
//   - user: UserUsecase interface for user-related operations
//
// Returns:
//   - PlatformPermissionHandlerFunc: Function that creates a middleware for checking platform permissions
func PlatformPermission(user userusecase.UserUsecase) PlatformPermissionHandlerFunc {
	return func(requiredPermissions ...enums.AppPermission) gin.HandlerFunc {
		return func(c *gin.Context) {
			ctx, span := oteltrace.Tracer.Start(c.Request.Context(), "middlewares.PlatformPermission")
			defer span.End()

			userID, exists := c.Get(enums.USER_ID)
			if !exists {
				span.SetStatus(codes.Error, ErrUnauthenticated.Error())
				span.RecordError(ErrUnauthenticated)
				otelzap.ErrorWithContext(ctx, "missing user id", ErrUnauthenticated)
				dto.ErrorResponse(c, dto.NewUnauthorizedError(ErrUnauthenticated))
				c.Abort()
				return
			}

			isAuthorized, err := user.AuthorizePlatform(c.Request.Context(), userID.(uuid.UUID), requiredPermissions)
			if err != nil {
				span.SetStatus(codes.Error, err.Error())
				span.RecordError(err)
				otelzap.ErrorWithContext(ctx, "failed to authorize user to platform", err, zap.String("user_id", userID.(uuid.UUID).String()))
				dto.ErrorResponse(c, err)
				c.Abort()
				return
			}

			if !isAuthorized {
				span.SetStatus(codes.Error, ErrNoPermission.Error())
				span.RecordError(ErrNoPermission)
				otelzap.ErrorWithContext(ctx, "unauthorized access to platform", ErrNoPermission, zap.String("user_id", userID.(uuid.UUID).String()))
				dto.ErrorResponse(c, dto.NewForbiddenError(ErrNoPermission))
				c.Abort()
				return
			}
			otelzap.InfoWithContext(ctx, "authorized platform user", zap.String("user_id", userID.(uuid.UUID).String()))
			span.SetAttributes(attribute.KeyValue{Key: "user_id", Value: attribute.StringValue(userID.(uuid.UUID).String())})
			c.Next()
		}
	}
}

type OrgPermissionHandlerFunc func(requiredPermissions ...enums.RepoPermission) gin.HandlerFunc

// OrgPermission is a middleware that checks user permissions for organization-level access.
// It verifies if the user has the required permissions to access organization features.
// The middleware includes OpenTelemetry tracing for monitoring the authorization process.
//
// Parameters:
//   - user: UserUsecase interface for user-related operations
//
// Returns:
//   - OrgPermissionHandlerFunc: Function that creates a middleware for checking organization permissions
func OrgPermission(user userusecase.UserUsecase) OrgPermissionHandlerFunc {
	return func(requiredPermissions ...enums.RepoPermission) gin.HandlerFunc {
		return func(c *gin.Context) {
			ctx, span := oteltrace.Tracer.Start(c.Request.Context(), "middlewares.OrgPermission")
			defer span.End()

			// TODO: get orgIDParam from query and body
			orgIDParam := c.Param(enums.ORG_ID) // get orgID from path
			orgID, err := uuid.Parse(orgIDParam)
			if err != nil {
				span.SetStatus(codes.Error, err.Error())
				span.RecordError(err)
				otelzap.ErrorWithContext(ctx, "failed to parse org id", err)
				dto.ErrorResponse(c, dto.NewBadRequestError(err))
				c.Abort()
				return
			}

			userID, exists := c.Get(enums.USER_ID)
			if !exists {
				span.SetStatus(codes.Error, ErrUnauthenticated.Error())
				span.RecordError(ErrUnauthenticated)
				otelzap.ErrorWithContext(ctx, "missing user id", ErrUnauthenticated)
				dto.ErrorResponse(c, dto.NewUnauthorizedError(ErrUnauthenticated))
				c.Abort()
				return
			}

			isAdmin, err := user.IsAdmin(c.Request.Context(), userID.(uuid.UUID))
			if err != nil {
				otelzap.ErrorWithContext(ctx, "failed to check user is admin", err, zap.String("user_id", userID.(uuid.UUID).String()))
				dto.ErrorResponse(c, err)
				c.Abort()
				return
			}

			if !isAdmin {
				isAuthorized, err := user.AuthorizeOrg(c.Request.Context(), userID.(uuid.UUID), orgID, requiredPermissions)
				if err != nil {
					span.SetStatus(codes.Error, err.Error())
					span.RecordError(err)
					otelzap.ErrorWithContext(ctx, "failed to authorize user to organization", err, zap.String("user_id", userID.(uuid.UUID).String()), zap.String("org_id", orgID.String()))
					dto.ErrorResponse(c, err)
					c.Abort()
					return
				}

				if !isAuthorized {
					span.SetStatus(codes.Error, ErrNoPermission.Error())
					span.RecordError(ErrNoPermission)
					otelzap.ErrorWithContext(ctx, "unauthorized access to organization", err, zap.String("user_id", userID.(uuid.UUID).String()), zap.String("org_id", orgID.String()))
					dto.ErrorResponse(c, dto.NewForbiddenError(ErrNoPermission))
					c.Abort()
					return
				}
			}

			otelzap.InfoWithContext(ctx, "authorized organization user", zap.String("user_id", userID.(uuid.UUID).String()), zap.String("org_id", orgID.String()))
			span.SetAttributes(
				attribute.KeyValue{Key: "user_id", Value: attribute.StringValue(userID.(uuid.UUID).String())},
				attribute.KeyValue{Key: "org_id", Value: attribute.StringValue(orgID.String())},
			)
			c.Next()
		}
	}
}

type RepoPermissionHandlerFunc func(requiredPermissions ...enums.RepoPermission) gin.HandlerFunc

// RepoPermission is a middleware that checks user permissions for repository-level access.
// It verifies if the user has the required permissions to access repository features.
// The middleware includes OpenTelemetry tracing for monitoring the authorization process.
//
// Parameters:
//   - user: UserUsecase interface for user-related operations
//
// Returns:
//   - RepoPermissionHandlerFunc: Function that creates a middleware for checking repository permissions
func RepoPermission(user userusecase.UserUsecase) RepoPermissionHandlerFunc {
	return func(requiredPermissions ...enums.RepoPermission) gin.HandlerFunc {
		return func(c *gin.Context) {
			ctx, span := oteltrace.Tracer.Start(c.Request.Context(), "middlewares.RepoPermission")
			defer span.End()

			repoID, err := types.RepoID{}.FromGinContext(c)
			if err != nil {
				span.SetStatus(codes.Error, err.Error())
				span.RecordError(err)
				otelzap.ErrorWithContext(ctx, "failed to retrieve repo id", err)
				dto.ErrorResponse(c, dto.NewBadRequestError(err, err.Error()))
				c.Abort()
				return
			}

			userID, exists := c.Get(enums.USER_ID)
			if !exists {
				span.SetStatus(codes.Error, ErrUnauthenticated.Error())
				span.RecordError(ErrUnauthenticated)
				otelzap.ErrorWithContext(ctx, "missing user id", ErrUnauthenticated)
				dto.ErrorResponse(c, dto.NewUnauthorizedError(ErrUnauthenticated))
				c.Abort()
				return
			}

			isAdmin, err := user.IsAdmin(c.Request.Context(), userID.(uuid.UUID))
			if err != nil {
				otelzap.ErrorWithContext(ctx, "failed to check user is admin", err, zap.String("user_id", userID.(uuid.UUID).String()))
				dto.ErrorResponse(c, err)
				c.Abort()
				return
			}

			if !isAdmin {
				isAuthorized, err := user.AuthorizeRepo(c.Request.Context(), userID.(uuid.UUID), repoID, requiredPermissions)
				if err != nil {
					span.SetStatus(codes.Error, err.Error())
					span.RecordError(err)
					otelzap.ErrorWithContext(ctx, "failed to authorize user to repository", err, zap.String("user_id", userID.(uuid.UUID).String()), zap.String("repo_id", repoID.String()))
					dto.ErrorResponse(c, err)
					c.Abort()
					return
				}

				if !isAuthorized {
					span.SetStatus(codes.Error, ErrNoPermission.Error())
					span.RecordError(ErrNoPermission)
					otelzap.ErrorWithContext(ctx, "unauthorized access to repository", ErrNoPermission, zap.String("user_id", userID.(uuid.UUID).String()), zap.String("repo_id", repoID.String()))
					dto.ErrorResponse(c, dto.NewForbiddenError(ErrNoPermission))
					c.Abort()
					return
				}
			}

			otelzap.InfoWithContext(ctx, "authorized repository user", zap.String("user_id", userID.(uuid.UUID).String()), zap.String("repo_id", repoID.String()))
			span.SetAttributes(
				attribute.KeyValue{Key: "user_id", Value: attribute.StringValue(userID.(uuid.UUID).String())},
				attribute.KeyValue{Key: "repo_id", Value: attribute.StringValue(repoID.String())},
			)
			c.Next()
		}
	}
}

type ECRPermissionHandlerFunc func(requiredPermissions ...enums.ECRPermission) gin.HandlerFunc

// ECRPermission is a middleware that checks user permissions for ECR-level access.
// It verifies if the user has the required permissions to access ECR features.
// The middleware includes OpenTelemetry tracing for monitoring the authorization process.
func ECRPermission(user userusecase.UserUsecase) ECRPermissionHandlerFunc {
	return func(requiredPermissions ...enums.ECRPermission) gin.HandlerFunc {
		return func(c *gin.Context) {
			ctx, span := oteltrace.Tracer.Start(c.Request.Context(), "middlewares.ECRPermission")
			defer span.End()

			ecrID, err := uuid.Parse(c.Param(enums.ECR_ID))
			if err != nil {
				span.SetStatus(codes.Error, "invalid ecr ID")
				span.RecordError(err)
				otelzap.ErrorWithContext(ctx, "failed to parse ecr id", err)
				dto.ErrorResponse(c, dto.NewBadRequestError(err, "invalid ecr ID"))
				c.Abort()
				return
			}

			userID, exists := c.Get(enums.USER_ID)
			if !exists {
				span.SetStatus(codes.Error, ErrUnauthenticated.Error())
				span.RecordError(ErrUnauthenticated)
				otelzap.ErrorWithContext(ctx, "missing user id", ErrUnauthenticated)
				dto.ErrorResponse(c, dto.NewUnauthorizedError(ErrUnauthenticated))
				c.Abort()
				return
			}

			isAdmin, err := user.IsAdmin(c.Request.Context(), userID.(uuid.UUID))
			if err != nil {
				otelzap.ErrorWithContext(ctx, "failed to check user is admin", err, zap.String("user_id", userID.(uuid.UUID).String()))
				dto.ErrorResponse(c, err)
				c.Abort()
				return
			}

			if !isAdmin {
				isAuthorized, err := user.AuthorizeECR(c.Request.Context(), userID.(uuid.UUID), ecrID, requiredPermissions)
				if err != nil {
					span.SetStatus(codes.Error, err.Error())
					span.RecordError(err)
					otelzap.ErrorWithContext(ctx, "failed to authorize user to ecr", err, zap.String("user_id", userID.(uuid.UUID).String()), zap.String("ecr_id", ecrID.String()))
					dto.ErrorResponse(c, err)
					c.Abort()
					return
				}

				if !isAuthorized {
					span.SetStatus(codes.Error, ErrNoPermission.Error())
					span.RecordError(ErrNoPermission)
					otelzap.ErrorWithContext(ctx, "unauthorized access to ecr", ErrNoPermission, zap.String("user_id", userID.(uuid.UUID).String()), zap.String("ecr_id", ecrID.String()))
					dto.ErrorResponse(c, dto.NewForbiddenError(ErrNoPermission))
					c.Abort()
					return
				}
			}

			otelzap.InfoWithContext(ctx, "authorized repository user", zap.String("user_id", userID.(uuid.UUID).String()), zap.String("ecr_deployment_id", ecrID.String()))
			span.SetAttributes(
				attribute.KeyValue{Key: "user_id", Value: attribute.StringValue(userID.(uuid.UUID).String())},
				attribute.KeyValue{Key: "ecr_deployment_id", Value: attribute.StringValue(ecrID.String())},
			)
			c.Next()
		}
	}
}

// AccessTokenAuth is a middleware that validates access tokens in the Authorization header.
// It specifically handles access token authentication for API requests.
// The middleware includes OpenTelemetry tracing for monitoring the authentication process.
//
// Parameters:
//   - user: UserUsecase interface for user-related operations
//
// Returns:
//   - gin.HandlerFunc: Gin middleware function that validates the access token and sets user context
func AccessTokenAuth(user userusecase.UserUsecase, org orgusecase.OrganizationUsecase) gin.HandlerFunc {
	prefix := "Bearer "
	return func(c *gin.Context) {
		ctx, span := oteltrace.Tracer.Start(c.Request.Context(), "middlewares.AccessTokenAuth")
		defer span.End()

		bearer := c.GetHeader("Authorization")

		if bearer == "" || !strings.HasPrefix(bearer, prefix) {
			span.SetStatus(codes.Error, ErrMissingAuthorization.Error())
			span.RecordError(ErrMissingAuthorization)
			otelzap.ErrorWithContext(ctx, "missing access token", ErrMissingAuthorization)
			c.JSON(http.StatusUnauthorized, dto.NewHTTPError(http.StatusUnauthorized, ErrMissingAuthorization))
			c.Abort()
			return
		}

		accessToken := strings.TrimPrefix(bearer, prefix)

		userID, err := user.AuthorizeAccessToken(c.Request.Context(), accessToken)
		if err != nil {
			span.SetStatus(codes.Error, err.Error())
			span.RecordError(err)
			otelzap.ErrorWithContext(ctx, "failed to authorize user access token", err, zap.String("access_token", accessToken))
			dto.ErrorResponse(c, err)
			c.Abort()
			return
		}

		if userID == nil {
			orgID, err := org.AuthorizeAccessToken(c.Request.Context(), accessToken)
			if err != nil {
				span.SetStatus(codes.Error, err.Error())
				span.RecordError(err)
				otelzap.ErrorWithContext(ctx, "failed to authorize org access token", err, zap.String("access_token", accessToken))
				dto.ErrorResponse(c, err)
				c.Abort()
				return
			}

			// if both userID == nil and orgID == nil, return invalid token error
			if orgID == nil {
				span.SetStatus(codes.Error, ErrInvalidToken.Error())
				span.RecordError(ErrInvalidToken)
				otelzap.ErrorWithContext(ctx, "failed to authorize access token", ErrInvalidToken, zap.String("access_token", accessToken))
				dto.ErrorResponse(c, dto.NewUnauthorizedError(ErrInvalidToken))
				c.Abort()
				return
			}

			repoID, err := types.RepoID{}.FromGinContext(c)
			if err != nil {
				span.SetStatus(codes.Error, "invalid repository ID")
				span.RecordError(err)
				dto.ErrorResponse(c, dto.NewBadRequestError(err, err.Error()))
				return
			}

			checked, err := org.CheckRepositoryInOrg(c.Request.Context(), *orgID, repoID)
			if !checked {
				span.SetStatus(codes.Error, err.Error())
				span.RecordError(err)
				otelzap.ErrorWithContext(ctx, "failed to check repository in org", err, zap.String("access_token", accessToken), zap.String("org_id", orgID.String()))
				dto.ErrorResponse(c, err)
				c.Abort()
				return
			}

			c.Set(enums.ORG_ID, *orgID)
			span.SetAttributes(attribute.KeyValue{Key: "org_id", Value: attribute.StringValue(orgID.String())})
		} else {
			c.Set(enums.USER_ID, *userID)
			span.SetAttributes(attribute.KeyValue{Key: "user_id", Value: attribute.StringValue(userID.String())})
		}

		otelzap.InfoWithContext(ctx, "authorized via access token", zap.String("access_token", accessToken))
		span.SetAttributes(attribute.KeyValue{Key: "access_token", Value: attribute.StringValue(accessToken)})
		c.Next()
	}
}

// IsAdmin is a middleware that verifies if the authenticated user has admin privileges.
// It checks the user's admin status before allowing access to admin-only routes.
//
// Parameters:
//   - user: UserUsecase interface for user-related operations
//
// Returns:
//   - gin.HandlerFunc: Gin middleware function that validates admin status
func IsAdmin(user userusecase.UserUsecase) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID, exists := c.Get(enums.USER_ID)
		if !exists {
			dto.ErrorResponse(c, dto.NewUnauthorizedError(ErrUnauthenticated))
			c.Abort()
			return
		}

		isAdmin, err := user.IsAdmin(c.Request.Context(), userID.(uuid.UUID))
		if err != nil {
			dto.ErrorResponse(c, err)
			c.Abort()
			return
		}

		if !isAdmin {
			dto.ErrorResponse(c, dto.NewForbiddenError(ErrNoPermission))
			c.Abort()
			return
		}

		c.Next()
	}
}
