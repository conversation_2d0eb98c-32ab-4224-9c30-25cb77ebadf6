package middlewares_test

import (
	"context"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"api-server/internal/enums"
	"api-server/internal/middlewares"
	"api-server/internal/types"
	"api-server/internal/usecase"
	orgMocks "api-server/internal/usecase/organization/mocks"
	"api-server/internal/usecase/user/mocks"
	userMocks "api-server/internal/usecase/user/mocks"
	"api-server/pkg/jwt"
)

func newTestServer(jwtSecret string, userusecase *mocks.MockUserUsecase) *gin.Engine {
	gin.SetMode(gin.TestMode)

	r := gin.Default()

	r.GET("/", middlewares.BearerAuth(jwtSecret, userusecase), func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"message": "ok",
		})
	})

	return r
}

func TestBearerAuth(t *testing.T) {
	jwtSecret := "jwt_secret"
	validToken, err := jwt.GenerateJWT(context.Background(), jwt.CustomClaims{}, []byte(jwtSecret), time.Duration(600)*time.Second)
	if err != nil {
		t.Errorf("err %s", err)
	}

	userID := uuid.New()

	t.Run("Missing Authorization Header", func(t *testing.T) {
		req := httptest.NewRequest(http.MethodGet, "/", nil)
		rr := httptest.NewRecorder()

		user := &mocks.MockUserUsecase{}
		// user.On("AuthorizeAccessToken", context.Background(), mock.Anything).Return(true, nil)
		mockServer := newTestServer(jwtSecret, user)
		mockServer.ServeHTTP(rr, req)

		if status := rr.Code; status != http.StatusUnauthorized {
			t.Errorf("Expected status %d, got %d", http.StatusUnauthorized, status)
		}
	})

	t.Run("Invalid Authorization Header Format", func(t *testing.T) {
		req := httptest.NewRequest(http.MethodGet, "/", nil)
		req.Header.Set("Authorization", "InvalidTokenFormat")
		rr := httptest.NewRecorder()

		user := &mocks.MockUserUsecase{}
		// user.On("AuthorizeAccessToken", context.Background(), mock.Anything).Return(true, nil)
		mockServer := newTestServer(jwtSecret, user)
		mockServer.ServeHTTP(rr, req)

		if status := rr.Code; status != http.StatusUnauthorized {
			t.Errorf("Expected status %d, got %d", http.StatusUnauthorized, status)
		}
	})

	t.Run("Invalid Token", func(t *testing.T) {
		req := httptest.NewRequest(http.MethodGet, "/", nil)
		req.Header.Set("Authorization", "Bearer invalid_token")
		rr := httptest.NewRecorder()

		user := &mocks.MockUserUsecase{}
		user.On("AuthorizeAccessToken", mock.Anything, mock.Anything).Return(nil, nil)
		mockServer := newTestServer(jwtSecret, user)
		mockServer.ServeHTTP(rr, req)

		if status := rr.Code; status != http.StatusUnauthorized {
			t.Errorf("Expected status %d, got %d", http.StatusUnauthorized, status)
		}

		expected := "{\"code\":401,\"message\":\"Invalid token\"}"
		if rr.Body.String() != expected {
			t.Errorf("Expected body %q, got %q", expected, rr.Body.String())
		}
	})

	t.Run("Valid Token", func(t *testing.T) {
		req := httptest.NewRequest(http.MethodGet, "/", nil)
		req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", *validToken))
		rr := httptest.NewRecorder()

		user := &mocks.MockUserUsecase{}
		user.On("AuthorizeAccessToken", mock.Anything, mock.Anything).Return(&userID, nil)
		user.On("UpsertUser", mock.Anything, mock.Anything).Return(nil)
		mockServer := newTestServer(jwtSecret, user)
		mockServer.ServeHTTP(rr, req)

		if status := rr.Code; status != http.StatusOK {
			t.Errorf("Expected status %d, got %d", http.StatusOK, status)
		}
	})
}

func TestPlatformPermission(t *testing.T) {
	gin.SetMode(gin.TestMode)

	mockUserID := uuid.New()
	mockPermissions := []enums.AppPermission{}
	tests := []struct {
		name             string
		ctx              context.Context
		mockFn           func(u *mocks.MockUserUsecase)
		setUserIDContext bool
		expectBody       string
		expectHTTPCode   int
	}{
		{
			name:             "should return Unauthenticated error if missing userID from context",
			mockFn:           func(u *mocks.MockUserUsecase) {},
			setUserIDContext: false,
			expectBody:       "{\"code\":401,\"message\":\"Unauthenticated\"}",
			expectHTTPCode:   http.StatusUnauthorized,
		},
		{
			name: "should return Forbidden error if AuthorizePlatform return false",
			mockFn: func(u *mocks.MockUserUsecase) {
				u.On("AuthorizePlatform", context.Background(), mockUserID, mockPermissions).Return(false, nil)
			},
			setUserIDContext: true,
			expectBody:       "{\"code\":403,\"message\":\"No permission\"}",
			expectHTTPCode:   http.StatusForbidden,
		},
		{
			name: "should return Interal Server error if AuthorizePlatform return usecase.ErrInternal",
			mockFn: func(u *mocks.MockUserUsecase) {
				u.On("AuthorizePlatform", context.Background(), mockUserID, mockPermissions).Return(false, usecase.ErrInternal)
			},
			setUserIDContext: true,
			expectBody:       "{\"code\":500,\"message\":\"Internal error\"}",
			expectHTTPCode:   http.StatusInternalServerError,
		},
		{
			name: "should pass",
			mockFn: func(u *mocks.MockUserUsecase) {
				u.On("AuthorizePlatform", context.Background(), mockUserID, mockPermissions).Return(true, nil)
			},
			setUserIDContext: true,
			expectBody:       "{\"message\":\"ok\"}",
			expectHTTPCode:   http.StatusOK,
		},
	}

	for _, testcase := range tests {
		t.Run(testcase.name, func(t *testing.T) {
			u := &mocks.MockUserUsecase{}
			testcase.mockFn(u)

			guard := middlewares.PlatformPermission(u)

			// set up mock Gin server
			gin.SetMode(gin.TestMode)
			r := gin.Default()
			r.GET("/",
				func(c *gin.Context) {
					if testcase.setUserIDContext {
						c.Set(enums.USER_ID, mockUserID)
					}
					c.Next()
				},
				guard(mockPermissions...),
				func(c *gin.Context) {
					c.JSON(http.StatusOK, gin.H{
						"message": "ok",
					})
				})
			// make a reqeuest to mock Gin server
			req := httptest.NewRequest(http.MethodGet, "/", nil)
			rr := httptest.NewRecorder()
			r.ServeHTTP(rr, req)

			// assertion
			assert.Equal(t, testcase.expectHTTPCode, rr.Code)
			assert.Equal(t, testcase.expectBody, rr.Body.String())
		})
	}
}

func TestOrgPermission(t *testing.T) {
	gin.SetMode(gin.TestMode)

	mockUserID := uuid.New()
	mockOrgID := uuid.New()
	mockPermissions := []enums.RepoPermission{}
	tests := []struct {
		name             string
		ctx              context.Context
		mockFn           func(u *mocks.MockUserUsecase)
		setUserIDContext bool
		expectBody       string
		expectHTTPCode   int
	}{
		{
			name:             "should return Unauthenticated error if missing userID from context",
			mockFn:           func(u *mocks.MockUserUsecase) {},
			setUserIDContext: false,
			expectBody:       "{\"code\":401,\"message\":\"Unauthenticated\"}",
			expectHTTPCode:   http.StatusUnauthorized,
		},
		{
			name: "should return Forbidden error if AuthorizeOrg return false",
			mockFn: func(u *mocks.MockUserUsecase) {
				u.On("IsAdmin", context.Background(), mockUserID).Return(false, nil)
				u.On("AuthorizeOrg", context.Background(), mockUserID, mockOrgID, mockPermissions).Return(false, nil)
			},
			setUserIDContext: true,
			expectBody:       "{\"code\":403,\"message\":\"No permission\"}",
			expectHTTPCode:   http.StatusForbidden,
		},
		{
			name: "should return Interal Server error if AuthorizeOrg return usecase.ErrInternal",
			mockFn: func(u *mocks.MockUserUsecase) {
				u.On("IsAdmin", context.Background(), mockUserID).Return(false, nil)
				u.On("AuthorizeOrg", context.Background(), mockUserID, mockOrgID, mockPermissions).Return(false, usecase.ErrInternal)
			},
			setUserIDContext: true,
			expectBody:       "{\"code\":500,\"message\":\"Internal error\"}",
			expectHTTPCode:   http.StatusInternalServerError,
		},
		{
			name: "should pass",
			mockFn: func(u *mocks.MockUserUsecase) {
				u.On("IsAdmin", context.Background(), mockUserID).Return(false, nil)
				u.On("AuthorizeOrg", context.Background(), mockUserID, mockOrgID, mockPermissions).Return(true, nil)
			},
			setUserIDContext: true,
			expectBody:       "{\"message\":\"ok\"}",
			expectHTTPCode:   http.StatusOK,
		},
		{
			name: "should pass when user is admin",
			mockFn: func(u *mocks.MockUserUsecase) {
				u.On("IsAdmin", context.Background(), mockUserID).Return(true, nil)
			},
			setUserIDContext: true,
			expectBody:       "{\"message\":\"ok\"}",
			expectHTTPCode:   http.StatusOK,
		},
	}

	for _, testcase := range tests {
		t.Run(testcase.name, func(t *testing.T) {
			u := &mocks.MockUserUsecase{}
			testcase.mockFn(u)

			guard := middlewares.OrgPermission(u)

			// set up mock Gin server
			r := gin.Default()
			r.GET("/:org_id/get",
				func(c *gin.Context) {
					if testcase.setUserIDContext {
						c.Set(enums.USER_ID, mockUserID)
					}
					c.Next()
				},
				guard(mockPermissions...),
				func(c *gin.Context) {
					c.JSON(http.StatusOK, gin.H{
						"message": "ok",
					})
				})
			// make a reqeuest to mock Gin server
			req := httptest.NewRequest(http.MethodGet, fmt.Sprintf("/%s/get", mockOrgID), nil)
			rr := httptest.NewRecorder()
			r.ServeHTTP(rr, req)

			// assertion
			assert.Equal(t, testcase.expectHTTPCode, rr.Code)
			assert.Equal(t, testcase.expectBody, rr.Body.String())
		})
	}
}

func TestRepoPermission(t *testing.T) {
	gin.SetMode(gin.TestMode)

	mockUserID := uuid.New()

	repoType := enums.RepoType_Models
	namespace := "namespace"
	repoName := "repo-name"
	mockRepoID := *types.NewRepoID(repoType, namespace, repoName)

	mockPermissions := []enums.RepoPermission{}
	tests := []struct {
		name             string
		ctx              context.Context
		mockFn           func(u *mocks.MockUserUsecase)
		setUserIDContext bool
		expectBody       string
		expectHTTPCode   int
	}{
		{
			name:             "should return Unauthenticated error if missing userID from context",
			mockFn:           func(u *mocks.MockUserUsecase) {},
			setUserIDContext: false,
			expectBody:       "{\"code\":401,\"message\":\"Unauthenticated\"}",
			expectHTTPCode:   http.StatusUnauthorized,
		},
		{
			name: "should return Forbidden error if AuthorizeRepo return false",
			mockFn: func(u *mocks.MockUserUsecase) {
				u.On("IsAdmin", context.Background(), mockUserID).Return(false, nil)
				u.On("AuthorizeRepo", context.Background(), mockUserID, mockRepoID, mockPermissions).Return(false, nil)
			},
			setUserIDContext: true,
			expectBody:       "{\"code\":403,\"message\":\"No permission\"}",
			expectHTTPCode:   http.StatusForbidden,
		},
		{
			name: "should return Interal Server error if AuthorizeOrg return usecase.ErrInternal",
			mockFn: func(u *mocks.MockUserUsecase) {
				u.On("IsAdmin", context.Background(), mockUserID).Return(false, nil)
				u.On("AuthorizeRepo", context.Background(), mockUserID, mockRepoID, mockPermissions).Return(false, usecase.ErrInternal)
			},
			setUserIDContext: true,
			expectBody:       "{\"code\":500,\"message\":\"Internal error\"}",
			expectHTTPCode:   http.StatusInternalServerError,
		},
		{
			name: "should pass",
			mockFn: func(u *mocks.MockUserUsecase) {
				u.On("IsAdmin", context.Background(), mockUserID).Return(false, nil)
				u.On("AuthorizeRepo", context.Background(), mockUserID, mockRepoID, mockPermissions).Return(true, nil)
			},
			setUserIDContext: true,
			expectBody:       "{\"message\":\"ok\"}",
			expectHTTPCode:   http.StatusOK,
		},
		{
			name: "should pass when user is admin",
			mockFn: func(u *mocks.MockUserUsecase) {
				u.On("IsAdmin", context.Background(), mockUserID).Return(true, nil)
			},
			setUserIDContext: true,
			expectBody:       "{\"message\":\"ok\"}",
			expectHTTPCode:   http.StatusOK,
		},
	}

	for _, testcase := range tests {
		t.Run(testcase.name, func(t *testing.T) {
			u := &mocks.MockUserUsecase{}
			testcase.mockFn(u)

			guard := middlewares.RepoPermission(u)

			// set up mock Gin server
			r := gin.Default()
			r.GET("/:repo_type/:namespace/:repo_name/get",
				func(c *gin.Context) {
					if testcase.setUserIDContext {
						c.Set(enums.USER_ID, mockUserID)
					}
					c.Next()
				},
				guard(mockPermissions...),
				func(c *gin.Context) {
					c.JSON(http.StatusOK, gin.H{
						"message": "ok",
					})
				})
			// make a reqeuest to mock Gin server
			req := httptest.NewRequest(http.MethodGet, fmt.Sprintf("/%s/get", mockRepoID.String()), nil)
			rr := httptest.NewRecorder()
			r.ServeHTTP(rr, req)

			// assertion
			assert.Equal(t, testcase.expectHTTPCode, rr.Code)
			assert.Equal(t, testcase.expectBody, rr.Body.String())
		})
	}
}

func TestAccessTokenAuth(t *testing.T) {
	gin.SetMode(gin.TestMode)

	mockUserID := uuid.New()

	type dependencies struct {
		user *userMocks.MockUserUsecase
		org  *orgMocks.MockOrganizationUsecase
	}

	tests := []struct {
		name           string
		mockFn         func(u *dependencies)
		mockHeader     func(h *http.Header)
		expectBody     string
		expectHTTPCode int
	}{
		{
			name:           "should return ErrMissingAuthorization error if missing Authorization header from context",
			mockFn:         func(u *dependencies) {},
			mockHeader:     func(h *http.Header) {},
			expectBody:     "{\"code\":401,\"message\":\"Authorization header missing or invalid\"}",
			expectHTTPCode: http.StatusUnauthorized,
		},
		{
			name:   "should return ErrMissingAuthorization error if Authorization header is empty",
			mockFn: func(u *dependencies) {},
			mockHeader: func(h *http.Header) {
				h.Set("Authorization", "")
			},
			expectBody:     "{\"code\":401,\"message\":\"Authorization header missing or invalid\"}",
			expectHTTPCode: http.StatusUnauthorized,
		},
		{
			name:   "should return ErrMissingAuthorization error if Authorization header has invalid format",
			mockFn: func(u *dependencies) {},
			mockHeader: func(h *http.Header) {
				h.Set("Authorization", "InvalidToken")
			},
			expectBody:     "{\"code\":401,\"message\":\"Authorization header missing or invalid\"}",
			expectHTTPCode: http.StatusUnauthorized,
		},
		{
			name: "should return ErrInvalidToken error if both user and organization AuthorizeAccessToken fail",
			mockFn: func(u *dependencies) {
				u.user.On("AuthorizeAccessToken", context.Background(), mock.Anything).Return(nil, nil)
				u.org.On("AuthorizeAccessToken", context.Background(), mock.Anything).Return(nil, nil)
			},
			mockHeader: func(h *http.Header) {
				h.Set("Authorization", "Bearer token")
			},
			expectBody:     "{\"code\":401,\"message\":\"Invalid token\"}",
			expectHTTPCode: http.StatusUnauthorized,
		},
		{
			name: "should return Interal Server error if user AuthorizeAccessToken return usecase.ErrInternal",
			mockFn: func(u *dependencies) {
				u.user.On("AuthorizeAccessToken", context.Background(), mock.Anything).Return(nil, usecase.ErrInternal)
				u.org.On("AuthorizeAccessToken", context.Background(), mock.Anything).Return(nil, nil)
			},
			mockHeader: func(h *http.Header) {
				h.Set("Authorization", "Bearer token")
			},
			expectBody:     "{\"code\":500,\"message\":\"Internal error\"}",
			expectHTTPCode: http.StatusInternalServerError,
		},
		{
			name: "should return Interal Server error if organization AuthorizeAccessToken return usecase.ErrInternal",
			mockFn: func(u *dependencies) {
				u.user.On("AuthorizeAccessToken", context.Background(), mock.Anything).Return(nil, nil)
				u.org.On("AuthorizeAccessToken", context.Background(), mock.Anything).Return(nil, usecase.ErrInternal)
			},
			mockHeader: func(h *http.Header) {
				h.Set("Authorization", "Bearer token")
			},
			expectBody:     "{\"code\":500,\"message\":\"Internal error\"}",
			expectHTTPCode: http.StatusInternalServerError,
		},
		{
			name: "user AuthorizeAccessToken should pass",
			mockFn: func(u *dependencies) {
				u.user.On("AuthorizeAccessToken", context.Background(), mock.Anything).Return(&mockUserID, nil)
				u.org.On("AuthorizeAccessToken", context.Background(), mock.Anything).Return(nil, nil)
			},
			mockHeader: func(h *http.Header) {
				h.Set("Authorization", "Bearer token")
			},
			expectBody:     "{\"message\":\"ok\"}",
			expectHTTPCode: http.StatusOK,
		},
		// {
		// 	name: "organization AuthorizeAccessToken should pass",
		// 	mockFn: func(u *dependencies) {
		// 		u.user.On("AuthorizeAccessToken", context.Background(), mock.Anything).Return(nil, nil)
		// 		u.org.On("AuthorizeAccessToken", context.Background(), mock.Anything).Return(&mockUserID, nil)
		// 		u.org.On("CheckRepositoryInOrg", context.Background(), mock.Anything).Return(&mockUserID, nil)
		// 	},
		// 	mockHeader: func(h *http.Header) {
		// 		h.Set("Authorization", "Bearer token")
		// 	},
		// 	expectBody:     "{\"message\":\"ok\"}",
		// 	expectHTTPCode: http.StatusOK,
		// },
	}

	for _, testcase := range tests {
		t.Run(testcase.name, func(t *testing.T) {
			d := &dependencies{
				user: &userMocks.MockUserUsecase{},
				org:  &orgMocks.MockOrganizationUsecase{},
			}
			testcase.mockFn(d)

			guard := middlewares.AccessTokenAuth(d.user, d.org)

			// set up mock Gin server
			gin.SetMode(gin.TestMode)
			r := gin.Default()
			r.GET("/",
				guard,
				func(c *gin.Context) {
					c.JSON(http.StatusOK, gin.H{
						"message": "ok",
					})
				})
			// make a reqeuest to mock Gin server
			req := httptest.NewRequest(http.MethodGet, "/", nil)
			testcase.mockHeader(&req.Header)

			rr := httptest.NewRecorder()
			r.ServeHTTP(rr, req)

			// assertion
			assert.Equal(t, testcase.expectHTTPCode, rr.Code)
			assert.Equal(t, testcase.expectBody, rr.Body.String())
		})
	}
}

func TestIsAdmin(t *testing.T) {
	gin.SetMode(gin.TestMode)

	mockUserID := uuid.New()
	tests := []struct {
		name             string
		mockFn           func(u *mocks.MockUserUsecase)
		setUserIDContext bool
		expectBody       string
		expectHTTPCode   int
	}{
		{
			name:             "should return Unauthenticated error if missing userID from context",
			mockFn:           func(u *mocks.MockUserUsecase) {},
			setUserIDContext: false,
			expectBody:       "{\"code\":401,\"message\":\"Unauthenticated\"}",
			expectHTTPCode:   http.StatusUnauthorized,
		},
		{
			name: "should return Forbidden error if user is not admin",
			mockFn: func(u *mocks.MockUserUsecase) {
				u.On("IsAdmin", context.Background(), mockUserID).Return(false, nil)
			},
			setUserIDContext: true,
			expectBody:       "{\"code\":403,\"message\":\"No permission\"}",
			expectHTTPCode:   http.StatusForbidden,
		},
		{
			name: "should return Internal Server error if IsAdmin returns error",
			mockFn: func(u *mocks.MockUserUsecase) {
				u.On("IsAdmin", context.Background(), mockUserID).Return(false, usecase.ErrInternal)
			},
			setUserIDContext: true,
			expectBody:       "{\"code\":500,\"message\":\"Internal error\"}",
			expectHTTPCode:   http.StatusInternalServerError,
		},
		{
			name: "should pass when user is admin",
			mockFn: func(u *mocks.MockUserUsecase) {
				u.On("IsAdmin", context.Background(), mockUserID).Return(true, nil)
			},
			setUserIDContext: true,
			expectBody:       "{\"message\":\"ok\"}",
			expectHTTPCode:   http.StatusOK,
		},
	}

	for _, testcase := range tests {
		t.Run(testcase.name, func(t *testing.T) {
			u := &mocks.MockUserUsecase{}
			testcase.mockFn(u)

			guard := middlewares.IsAdmin(u)

			// set up mock Gin server
			r := gin.Default()
			r.GET("/",
				func(c *gin.Context) {
					if testcase.setUserIDContext {
						c.Set(enums.USER_ID, mockUserID)
					}
					c.Next()
				},
				guard,
				func(c *gin.Context) {
					c.JSON(http.StatusOK, gin.H{
						"message": "ok",
					})
				})

			// make a request to mock Gin server
			req := httptest.NewRequest(http.MethodGet, "/", nil)
			rr := httptest.NewRecorder()
			r.ServeHTTP(rr, req)

			// assertion
			assert.Equal(t, testcase.expectHTTPCode, rr.Code)
			assert.Equal(t, testcase.expectBody, rr.Body.String())
		})
	}
}

func TestTokenAuthOnQuery(t *testing.T) {
	gin.SetMode(gin.TestMode)

	jwtSecret := "jwt_secret"
	validToken, err := jwt.GenerateJWT(context.Background(), jwt.CustomClaims{}, []byte(jwtSecret), time.Duration(600)*time.Second)
	if err != nil {
		t.Errorf("err %s", err)
	}

	tests := []struct {
		name           string
		queryParams    string
		expectBody     string
		expectHTTPCode int
	}{
		{
			name:           "should return Unauthorized error if missing access_token query parameter",
			queryParams:    "",
			expectBody:     "{\"code\":401,\"message\":\"Missing access token\"}",
			expectHTTPCode: http.StatusUnauthorized,
		},
		{
			name:           "should return Unauthorized error if access_token is empty",
			queryParams:    "access_token=",
			expectBody:     "{\"code\":401,\"message\":\"Invalid token\"}",
			expectHTTPCode: http.StatusUnauthorized,
		},
		{
			name:           "should return Unauthorized error if token is invalid",
			queryParams:    "access_token=invalid_token",
			expectBody:     "{\"code\":401,\"message\":\"Invalid token\"}",
			expectHTTPCode: http.StatusUnauthorized,
		},
		{
			name:           "should pass with valid token",
			queryParams:    fmt.Sprintf("access_token=%s", *validToken),
			expectBody:     "{\"message\":\"ok\"}",
			expectHTTPCode: http.StatusOK,
		},
	}

	for _, testcase := range tests {
		t.Run(testcase.name, func(t *testing.T) {
			// set up mock Gin server
			r := gin.Default()
			r.GET("/",
				middlewares.TokenAuthOnQuery(jwtSecret),
				func(c *gin.Context) {
					// Verify that user ID is set in context
					userID, exists := c.Get(enums.USER_ID)
					if testcase.expectHTTPCode == http.StatusOK {
						assert.True(t, exists)
						assert.IsType(t, uuid.UUID{}, userID)
					}
					c.JSON(http.StatusOK, gin.H{
						"message": "ok",
					})
				})

			// make a request to mock Gin server
			req := httptest.NewRequest(http.MethodGet, "/?"+testcase.queryParams, nil)
			rr := httptest.NewRecorder()
			r.ServeHTTP(rr, req)

			// assertion
			assert.Equal(t, testcase.expectHTTPCode, rr.Code)
			assert.Equal(t, testcase.expectBody, rr.Body.String())
		})
	}
}
