package middlewares

import "errors"

var (
	ErrNoPermission         = errors.New("No permission")
	ErrInvalidToken         = errors.New("Invalid token")
	ErrUnauthenticated      = errors.New("Unauthenticated")
	ErrMissingAuthorization = errors.New("Authorization header missing or invalid")
	ErrMissingAccessToken   = errors.New("Missing access token")
	ErrUserNotExist         = errors.New("User not exist")
)
