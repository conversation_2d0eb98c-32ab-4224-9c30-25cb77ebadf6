package middlewares

import (
	"bytes"
	"io"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"

	"api-server/pkg/otelzap"
)

// Logging is a middleware that provides comprehensive request and response logging.
// It captures and logs HTTP request/response details including headers, bodies, timing,
// and OpenTelemetry trace information. The middleware handles JSON content specially
// and includes detailed request metrics.
//
// Returns:
//   - gin.HandlerFunc: Gin middleware function that handles request logging
func Logging() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Capture request body
		var requestBody []byte
		var responseBody *bytes.Buffer
		if c.ContentType() == "application/json" {
			if c.Request.Body != nil {
				requestBody, _ = io.ReadAll(c.Request.Body)
				c.Request.Body = io.NopCloser(bytes.NewBuffer(requestBody))
			}

			// Capture response body
			responseBody = new(bytes.Buffer)
			writer := &bodyWriter{body: responseBody, ResponseWriter: c.Writer}
			c.Writer = writer
		}

		// Process request
		startTime := time.Now()
		c.Next()
		latency := time.Since(startTime)

		fields := []zap.Field{
			zap.String("ClientIP", c.ClientIP()),
			zap.String("StartTime", startTime.Format(time.RFC1123)),
			zap.String("Method", c.Request.Method),
			zap.String("URL.Path", c.Request.URL.Path),
			zap.String("Proto", c.Request.Proto),
			zap.Int("ResponseStatus", c.Writer.Status()),
			zap.String("Latency", latency.String()),
			zap.String("UserAgent", c.Request.UserAgent()),
			zap.String("Error", c.Errors.ByType(gin.ErrorTypePrivate).String()),
		}
		// log traceid and space id
		if trace.SpanFromContext(c.Request.Context()).SpanContext().IsValid() {
			fields = append(fields, zap.String("trace_id", trace.SpanFromContext(c.Request.Context()).SpanContext().TraceID().String()))
			fields = append(fields, zap.String("span_id", trace.SpanFromContext(c.Request.Context()).SpanContext().SpanID().String()))
		}

		if c.ContentType() == "application/json" {
			if strings.Contains(c.Request.URL.String(), "/swagger") {
				fields = append(fields, zap.String("Request Body", string(requestBody)))
				fields = append(fields, zap.String("Response Body", "truncated"))
			} else {
				fields = append(fields, zap.String("Request Body", string(requestBody)))
				fields = append(fields, zap.String("Response Body", responseBody.String()))
			}
		}

		otelzap.Logger.Info("new request", fields...)
	}
}

// bodyWriter is a custom response writer that captures the response body
// while still writing it to the original response writer.
// It implements the gin.ResponseWriter interface to intercept response data.
//
// Fields:
//   - ResponseWriter: The original gin.ResponseWriter
//   - body: Buffer to store the response body
type bodyWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

// Write implements the io.Writer interface for bodyWriter.
// It writes the response data to both the buffer and the original response writer.
//
// Parameters:
//   - b: Byte slice containing the response data to write
//
// Returns:
//   - int: Number of bytes written
//   - error: Any error that occurred during writing
func (w bodyWriter) Write(b []byte) (int, error) {
	w.body.Write(b)
	return w.ResponseWriter.Write(b)
}
