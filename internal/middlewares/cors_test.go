package middlewares_test

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"

	"api-server/internal/middlewares"
)

func TestNewCORSConfig(t *testing.T) {
	// Set Gin to test mode
	gin.SetMode(gin.TestMode)

	tests := []struct {
		name           string
		origins        []string
		requestOrigin  string
		expectedStatus int
		expectedAllow  bool
	}{
		{
			name:           "Allow specific origin",
			origins:        []string{"http://localhost:3000"},
			requestOrigin:  "http://localhost:3000",
			expectedStatus: http.StatusOK,
			expectedAllow:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create a new Gin router
			router := gin.New()

			// Apply CORS middleware
			router.Use(middlewares.NewCORSConfig(tt.origins))

			// Add test endpoints
			router.OPTIONS("/test", func(c *gin.Context) {
				c.Status(http.StatusNoContent)
			})
			router.GET("/test", func(c *gin.Context) {
				c.Status(http.StatusOK)
			})

			// Test preflight request
			preflightReq := httptest.NewRequest(http.MethodOptions, "/test", nil)
			preflightReq.Header.Set("Origin", tt.requestOrigin)
			preflightReq.Header.Set("Access-Control-Request-Method", "GET")
			preflightReq.Header.Set("Access-Control-Request-Headers", "Content-Type")

			preflightW := httptest.NewRecorder()
			router.ServeHTTP(preflightW, preflightReq)

			// Check preflight response
			if tt.expectedAllow {
				assert.Equal(t, http.StatusNoContent, preflightW.Code)
				if tt.origins[0] == "*" {
					assert.Equal(t, "*", preflightW.Header().Get("Access-Control-Allow-Origin"))
				} else {
					assert.Equal(t, tt.requestOrigin, preflightW.Header().Get("Access-Control-Allow-Origin"))
				}
				assert.Equal(t, "true", preflightW.Header().Get("Access-Control-Allow-Credentials"))
				assert.Contains(t, preflightW.Header().Get("Access-Control-Allow-Methods"), "GET")
				assert.Contains(t, preflightW.Header().Get("Access-Control-Allow-Methods"), "POST")
				assert.Contains(t, preflightW.Header().Get("Access-Control-Allow-Methods"), "PUT")
				assert.Contains(t, preflightW.Header().Get("Access-Control-Allow-Methods"), "DELETE")
				assert.Contains(t, preflightW.Header().Get("Access-Control-Allow-Methods"), "PATCH")
				assert.Contains(t, preflightW.Header().Get("Access-Control-Allow-Headers"), "Accept")
				assert.Contains(t, preflightW.Header().Get("Access-Control-Allow-Headers"), "Authorization")
				assert.Contains(t, preflightW.Header().Get("Access-Control-Allow-Headers"), "Content-Type")
			} else {
				assert.Equal(t, http.StatusNoContent, preflightW.Code)
				assert.Empty(t, preflightW.Header().Get("Access-Control-Allow-Origin"))
			}

			// Test actual request
			req := httptest.NewRequest(http.MethodGet, "/test", nil)
			req.Header.Set("Origin", tt.requestOrigin)

			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			// Check response status
			assert.Equal(t, tt.expectedStatus, w.Code)

			// Check CORS headers for actual request
			allowOrigin := w.Header().Get("Access-Control-Allow-Origin")
			if tt.expectedAllow {
				if tt.origins[0] == "*" {
					assert.Equal(t, "*", allowOrigin)
				} else {
					assert.Equal(t, tt.requestOrigin, allowOrigin)
				}
			} else {
				assert.Empty(t, allowOrigin)
			}
		})
	}
}

func TestCORSWithPreflight(t *testing.T) {
	// Set Gin to test mode
	gin.SetMode(gin.TestMode)

	// Create a new Gin router
	router := gin.New()

	// Apply CORS middleware
	router.Use(middlewares.NewCORSConfig([]string{"http://localhost:3000"}))

	// Add test endpoints
	router.OPTIONS("/test", func(c *gin.Context) {
		c.Status(http.StatusNoContent)
	})
	router.GET("/test", func(c *gin.Context) {
		c.Status(http.StatusOK)
	})

	// Test preflight request
	req := httptest.NewRequest(http.MethodOptions, "/test", nil)
	req.Header.Set("Origin", "http://localhost:3000")
	req.Header.Set("Access-Control-Request-Method", "GET")
	req.Header.Set("Access-Control-Request-Headers", "Content-Type")

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Check preflight response
	assert.Equal(t, http.StatusNoContent, w.Code)
	assert.Equal(t, "http://localhost:3000", w.Header().Get("Access-Control-Allow-Origin"))
	assert.Equal(t, "true", w.Header().Get("Access-Control-Allow-Credentials"))
	assert.Contains(t, w.Header().Get("Access-Control-Allow-Methods"), "GET")
	assert.Contains(t, w.Header().Get("Access-Control-Allow-Headers"), "Content-Type")
}
