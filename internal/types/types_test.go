package types_test

import (
	"testing"

	"api-server/internal/enums"
	"api-server/internal/types"

	"github.com/stretchr/testify/assert"
)

func TestHardwareFromMiB(t *testing.T) {
	tests := []struct {
		mib        uint
		expectMiB  uint
		expectUnit enums.MemoryUnit
	}{
		{256, 256, enums.MemoryUnit_MiB},
		{512, 512, enums.MemoryUnit_MiB},
		{1025, 1025, enums.MemoryUnit_MiB},

		{1024, 1, enums.MemoryUnit_GiB},
		{4096, 4, enums.MemoryUnit_GiB},
		{8192, 8, enums.MemoryUnit_GiB},
		{16_384, 16, enums.MemoryUnit_GiB},
	}

	for _, test := range tests {
		mem := types.HardwareMem{}.FromMiB(test.mib)

		assert.Equal(t, test.expectMiB, mem.Amount)
		assert.Equal(t, test.expectUnit, mem.Unit)
	}
}

func TestHardwareToMiB(t *testing.T) {
	tests := []struct {
		hardware  types.HardwareMem
		expectMiB uint
	}{
		{types.HardwareMem{Amount: 256, Unit: enums.MemoryUnit_MiB}, 256},
		{types.HardwareMem{Amount: 512, Unit: enums.MemoryUnit_MiB}, 512},
		{types.HardwareMem{Amount: 1024, Unit: enums.MemoryUnit_MiB}, 1024},
		{types.HardwareMem{Amount: 1, Unit: enums.MemoryUnit_GiB}, 1024},
		{types.HardwareMem{Amount: 2, Unit: enums.MemoryUnit_GiB}, 2048},
		{types.HardwareMem{Amount: 4, Unit: enums.MemoryUnit_GiB}, 4096},
		{types.HardwareMem{Amount: 8, Unit: enums.MemoryUnit_GiB}, 8192},
		{types.HardwareMem{Amount: 16, Unit: enums.MemoryUnit_GiB}, 16384},
	}

	for _, test := range tests {
		mib := test.hardware.ToMiB()

		assert.Equal(t, test.expectMiB, mib)
	}
}
