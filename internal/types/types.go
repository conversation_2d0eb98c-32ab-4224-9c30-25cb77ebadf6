package types

import (
	"encoding/json"
	"errors"
	"fmt"
	"strings"

	"github.com/gin-gonic/gin"

	"api-server/internal/enums"
)

type OrderBy map[enums.OrderByColumn]enums.OrderByDirection

type Pagination struct {
	PageNo   int
	PageSize int
}

type RepoID struct {
	repoType  enums.RepoType
	namespace string
	repoName  string
}

var (
	ErrInvalidRepoType = errors.New("invalid repository type")
)

func (id *RepoID) MarshalJSON() ([]byte, error) {
	return json.Marshal(id.String())
}

func NewRepoID(repoType enums.RepoType, namespace, repoName string) *RepoID {
	return &RepoID{
		repoType,
		namespace,
		repoName,
	}
}

func (id RepoID) FromString(str string) (RepoID, error) {
	parts := strings.Split(str, "/")
	if len(parts) != 3 {
		return RepoID{}, errors.New("invalid repo ID format")
	}

	var repoType enums.RepoType
	switch parts[0] {
	case enums.RepoType_Models.String():
		repoType = enums.RepoType_Models
	case enums.RepoType_Datasets.String():
		repoType = enums.RepoType_Datasets
	case enums.RepoType_Spaces.String():
		repoType = enums.RepoType_Spaces
	case enums.RepoType_Composes.String():
		repoType = enums.RepoType_Composes
	default:
		return RepoID{}, ErrInvalidRepoType
	}

	return RepoID{
		repoType:  repoType,
		namespace: parts[1],
		repoName:  parts[2],
	}, nil
}

func (id RepoID) FromGinContext(ctx *gin.Context) (RepoID, error) {
	repoType := ctx.Param(enums.REPO_TYPE)
	switch repoType {
	case enums.RepoType_Models.String():
		id.repoType = enums.RepoType_Models
	case enums.RepoType_Datasets.String():
		id.repoType = enums.RepoType_Datasets
	case enums.RepoType_Spaces.String():
		id.repoType = enums.RepoType_Spaces
	case enums.RepoType_Composes.String():
		id.repoType = enums.RepoType_Composes
	default:
		return RepoID{}, ErrInvalidRepoType
	}

	id.namespace = ctx.Param(enums.NAMESPACE)
	id.repoName = ctx.Param(enums.REPO_NAME)

	return id, nil
}

func (id RepoID) RepoType() *enums.RepoType {
	return &id.repoType
}

func (id RepoID) Namespace() *string {
	return &id.namespace
}

func (id RepoID) RepoName() *string {
	return &id.repoName
}

func (id RepoID) String() string {
	return fmt.Sprintf("%s/%s/%s", id.repoType.String(), id.namespace, id.repoName)
}

type HardwareMem struct {
	Amount uint             `json:"amount" validate:"required,min=1"`
	Unit   enums.MemoryUnit `json:"unit"   validate:"required,oneof=MiB GiB"`
}

func (h HardwareMem) String() string {
	return fmt.Sprintf("%d%s", h.Amount, h.Unit)
}

type K8SHardwareMem string

func (h K8SHardwareMem) String() string {
	return string(h)
}

func (h HardwareMem) ToK8SHardwareMem() K8SHardwareMem {
	return K8SHardwareMem(fmt.Sprintf("%d%s", h.Amount, h.Unit.ToK8SMemUnit()))
}

// convert from Mebibytes
func (m HardwareMem) FromMiB(mib uint) *HardwareMem {
	const mibPerGiB = 1024

	if mib >= mibPerGiB && mib%mibPerGiB == 0 {
		return &HardwareMem{
			Amount: mib / mibPerGiB,
			Unit:   enums.MemoryUnit_GiB,
		}
	}

	return &HardwareMem{
		Amount: mib,
		Unit:   enums.MemoryUnit_MiB,
	}
}

// convert to Mebibytes
func (m HardwareMem) ToMiB() uint {
	const mibPerGiB = 1024

	var mib uint
	switch m.Unit {
	case enums.MemoryUnit_MiB:
		return m.Amount
	case enums.MemoryUnit_GiB:
		return m.Amount * mibPerGiB
	}

	return mib
}

type CustomImageDeploymentType string

const (
	CustomImageDeploymentType_Single  CustomImageDeploymentType = "single"
	CustomImageDeploymentType_Compose CustomImageDeploymentType = "compose"
)
