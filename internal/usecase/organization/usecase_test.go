package organization_test

import (
	"bytes"
	"context"
	"errors"
	"io"
	"mime/multipart"
	"strings"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"gorm.io/gorm"

	"api-server/configs"
	"api-server/internal/dto"
	"api-server/internal/entities"
	"api-server/internal/enums"
	aws_mocks "api-server/internal/gateways/aws/mocks"
	"api-server/internal/gateways/gitlab"
	gitlab_mocks "api-server/internal/gateways/gitlab/mocks"
	mail_mocks "api-server/internal/gateways/mail/mocks"
	repository_mocks "api-server/internal/repositories/mocks"
	"api-server/internal/types"
	"api-server/internal/usecase"
	"api-server/internal/usecase/organization"
	"api-server/internal/utils"
)

func TestCreateOrganization(t *testing.T) {
	createMockFileHeader := func(fieldName, fileName, fileContent string) *multipart.FileHeader {
		var buffer bytes.Buffer
		writer := multipart.NewWriter(&buffer)

		part, _ := writer.CreateFormFile(fieldName, fileName)
		io.Copy(part, strings.NewReader(fileContent))

		writer.Close()

		reader := multipart.NewReader(&buffer, writer.Boundary())
		form, _ := reader.ReadForm(1024)

		return form.File[fieldName][0]
	}

	mockFileHeader := createMockFileHeader("avatar", "mockfile.txt", "This is a mock file content")

	uuidInput := uuid.New()
	type dependencies struct {
		config       *configs.GlobalConfig
		repo         *repository_mocks.MockRepository
		gitlabClient *gitlab_mocks.MockGitlabClient
		mailClient   *mail_mocks.MockMailClient
		awsClient    *aws_mocks.MockAWSClient
	}

	tests := []struct {
		name      string
		ctx       context.Context
		input     dto.CreateOrganizationInput
		mockFn    func(d *dependencies)
		expectErr error
	}{
		{
			name: "should return error when repository.FindUserByID return error",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.CreateOrganizationInput{
				Name:          "test",
				UserCurrentId: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), uuidInput))
				}).Return(errors.New("error"))
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(nil, errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return error when repository.FindUserByID return error",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.CreateOrganizationInput{
				Name:          "test",
				UserCurrentId: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), uuidInput))
				}).Return(errors.New("error"))
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{}, nil)
				d.repo.On("FindOrganization", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return error when duplicate name",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.CreateOrganizationInput{
				Name:          "test",
				UserCurrentId: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), uuidInput))
				}).Return(errors.New("error"))
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{}, nil)
				d.repo.On("FindOrganization", mock.Anything, mock.Anything).Return(&entities.Organization{}, nil)
			},
			expectErr: usecase.ErrDuplicatedOrganization,
		},
		{
			name: "should return error when repository.Create return error",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.CreateOrganizationInput{
				Name:          "test",
				UserCurrentId: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), uuidInput))
				}).Return(errors.New("error"))
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{
					GitlabAccessToken: nil,
				}, nil)
				d.repo.On("FindOrganization", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
				d.repo.On("Create", mock.Anything, mock.Anything).Return(errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return error when repository.FindUser returns error",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.CreateOrganizationInput{
				Name:          "test",
				UserCurrentId: uuidInput,
			},
			mockFn: func(d *dependencies) {
				err := errors.New("error")
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), uuidInput))
				}).Return(err)
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{
					GitlabAccessToken: nil,
				}, nil)
				d.repo.On("FindOrganization", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
				d.repo.On("Create", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(nil, err)
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return error when repository.Save returns error",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.CreateOrganizationInput{
				Name:          "test",
				UserCurrentId: uuidInput,
				File:          mockFileHeader,
			},
			mockFn: func(d *dependencies) {
				err := errors.New("error")
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), uuidInput))
				}).Return(err)
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{
					GitlabAccessToken: nil,
				}, nil)
				d.repo.On("FindOrganization", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
				d.repo.On("Create", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{
					Avatar:            new(string),
					GitlabAccessToken: new(string),
				}, nil)
				d.repo.On("FindUserGitGroup", mock.Anything, mock.Anything).Return(&entities.DefaultGitGroup{}, nil)
				d.awsClient.On("UploadImage", mock.Anything, mock.Anything, mock.Anything).Return("http://imageURL", nil)
				d.gitlabClient.On("GetGroup", mock.Anything, mock.Anything).Return([]gitlab.GitlabGroup{}, nil)
				d.gitlabClient.On("CreateGroup", mock.Anything, mock.Anything).Return(&gitlab.GitlabGroup{}, nil)
				d.gitlabClient.On("UpdateGroup", mock.Anything, mock.Anything).Return(nil)
				d.gitlabClient.On("AddUserToGroup", mock.Anything, mock.Anything).Return(&gitlab.AddUserToGroupResponse{}, nil)
				d.repo.On("CreateOrgGitGroup", mock.Anything, mock.Anything).Return(&entities.OrgGitGroup{}, nil)
				d.repo.On("Save", mock.Anything, mock.Anything).Return(err)
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return error when update group returns error",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.CreateOrganizationInput{
				Name:          "test",
				UserCurrentId: uuidInput,
				File:          mockFileHeader,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), uuidInput))
				}).Return(errors.New("error"))
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{
					GitlabAccessToken: nil,
				}, nil)
				d.repo.On("FindOrganization", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
				d.repo.On("Create", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{
					Avatar:            new(string),
					GitlabAccessToken: new(string),
				}, nil)
				d.repo.On("FindUserGitGroup", mock.Anything, mock.Anything).Return(&entities.DefaultGitGroup{}, nil)
				d.awsClient.On("UploadImage", mock.Anything, mock.Anything, mock.Anything).Return("http://imageURL", nil)
				d.gitlabClient.On("GetGroup", mock.Anything, mock.Anything).Return([]gitlab.GitlabGroup{}, nil)
				d.gitlabClient.On("CreateGroup", mock.Anything, mock.Anything).Return(&gitlab.GitlabGroup{}, nil)
				d.gitlabClient.On("UpdateGroup", mock.Anything, mock.Anything).Return(errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "success",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.CreateOrganizationInput{
				Name:          "test",
				UserCurrentId: uuidInput,
				File:          mockFileHeader,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), uuidInput))
				}).Return(nil)
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{
					GitlabAccessToken: nil,
				}, nil)
				d.repo.On("FindOrganization", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
				d.repo.On("Create", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{
					Avatar:            new(string),
					GitlabAccessToken: new(string),
				}, nil)
				d.repo.On("FindUserGitGroup", mock.Anything, mock.Anything).Return(&entities.DefaultGitGroup{}, nil)
				d.awsClient.On("UploadImage", mock.Anything, mock.Anything, mock.Anything).Return("http://imageURL", nil)
				d.gitlabClient.On("GetGroup", mock.Anything, mock.Anything).Return([]gitlab.GitlabGroup{}, nil)
				d.gitlabClient.On("CreateGroup", mock.Anything, mock.Anything).Return(&gitlab.GitlabGroup{}, nil)
				d.gitlabClient.On("UpdateGroup", mock.Anything, mock.Anything).Return(nil)
				d.gitlabClient.On("AddUserToGroup", mock.Anything, mock.Anything).Return(&gitlab.AddUserToGroupResponse{}, nil)
				d.repo.On("CreateOrgGitGroup", mock.Anything, mock.Anything).Return(&entities.OrgGitGroup{}, nil)
				d.repo.On("Save", mock.Anything, mock.Anything).Return(nil)
			},
			expectErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				config: &configs.GlobalConfig{Gitlab: &configs.GitlabConfig{
					VolvoApiServerHost: "http://localhost",
				}},
				repo:         &repository_mocks.MockRepository{},
				gitlabClient: &gitlab_mocks.MockGitlabClient{},
				mailClient:   &mail_mocks.MockMailClient{},
				awsClient:    &aws_mocks.MockAWSClient{},
			}
			tt.mockFn(d)

			u := organization.New(d.config, d.repo, d.gitlabClient, d.mailClient, d.awsClient)
			_, err := u.CreateOrganization(tt.ctx, tt.input)
			require.Equal(t, tt.expectErr, err)
		})
	}
}

func TestDeleteOrganization(t *testing.T) {
	uuidInput := uuid.New()
	type dependencies struct {
		config       *configs.GlobalConfig
		repo         *repository_mocks.MockRepository
		gitlabClient *gitlab_mocks.MockGitlabClient
		mailClient   *mail_mocks.MockMailClient
	}

	tests := []struct {
		name      string
		ctx       context.Context
		input     dto.DeleteOrganizationInput
		mockFn    func(d *dependencies)
		expectErr error
	}{
		{
			name: "should return error when repository.FindUserByID return error",
			ctx:  utils.WithUserId(context.Background(), uuid.New()),
			input: dto.DeleteOrganizationInput{
				Id:            uuidInput,
				CurrentUserId: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), uuidInput))
				}).Return(errors.New("error"))
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(nil, errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return error when repository.FindUserByID return not found",
			ctx:  utils.WithUserId(context.Background(), uuid.New()),
			input: dto.DeleteOrganizationInput{
				Id:            uuidInput,
				CurrentUserId: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), uuidInput))
				}).Return(usecase.ErrUserNotFound)
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(nil, gorm.ErrRecordNotFound)
			},
			expectErr: usecase.ErrUserNotFound,
		},
		{
			name: "should return error when user have no permission return no permission",
			ctx:  utils.WithUserId(context.Background(), uuid.New()),
			input: dto.DeleteOrganizationInput{
				Id:            uuidInput,
				CurrentUserId: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), uuidInput))
				}).Return(usecase.ErrNoPermission)
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{Role: enums.UserRole_User}, nil)
				d.repo.On("FindOrganizationByID", mock.Anything, uuidInput).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Developer}, nil)
			},
			expectErr: usecase.ErrNoPermission,
		},
		{
			name: "should return error when repository.FindOrganizationByID return error",
			ctx:  utils.WithUserId(context.Background(), uuid.New()),
			input: dto.DeleteOrganizationInput{
				Id:            uuidInput,
				CurrentUserId: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), uuidInput))
				}).Return(errors.New("error"))
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{}, nil)
				d.repo.On("FindOrganizationByID", mock.Anything, uuidInput).Return(nil, errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return error when repository.FindOrganizationByID return no found",
			ctx:  utils.WithUserId(context.Background(), uuid.New()),
			input: dto.DeleteOrganizationInput{
				Id:            uuidInput,
				CurrentUserId: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), uuidInput))
				}).Return(dto.NewNotFoundError(gorm.ErrRecordNotFound))
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{}, nil)
				d.repo.On("FindOrganizationByID", mock.Anything, uuidInput).Return(nil, gorm.ErrRecordNotFound)
			},
			expectErr: dto.NewNotFoundError(gorm.ErrRecordNotFound),
		},
		{
			name: "should return error when repository.DeleteMembersByOrganizationID return error",
			ctx:  utils.WithUserId(context.Background(), uuid.New()),
			input: dto.DeleteOrganizationInput{
				Id:            uuidInput,
				CurrentUserId: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), uuidInput))
				}).Return(errors.New("error"))
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{Role: enums.UserRole_User}, nil)
				d.repo.On("FindOrganizationByID", mock.Anything, uuidInput).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil)
				d.repo.On("DeleteMembersByOrganizationID", mock.Anything, uuidInput).Return(errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return error when gitlabClient.DeleteProject return error",
			ctx:  utils.WithUserId(context.Background(), uuid.New()),
			input: dto.DeleteOrganizationInput{
				Id:            uuidInput,
				CurrentUserId: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), uuidInput))
				}).Return(errors.New("error"))
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{Role: enums.UserRole_User}, nil)
				d.repo.On("FindOrganizationByID", mock.Anything, uuidInput).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil)
				d.repo.On("DeleteMembersByOrganizationID", mock.Anything, uuidInput).Return(nil)
				d.repo.On("ListRepositories", mock.Anything, mock.Anything).Return([]entities.Repository{
					{
						RefGitRepoID: 1,
						UserID:       &uuidInput,
						OrgID:        &uuidInput,
						BaseModel: entities.BaseModel{
							ID: uuidInput,
						},
					},
				}, nil)
				d.gitlabClient.On("DeleteProject", mock.Anything, mock.Anything).Return(errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return error when repository.DeleteRepositoryMemberByRepositoryID return error",
			ctx:  utils.WithUserId(context.Background(), uuid.New()),
			input: dto.DeleteOrganizationInput{
				Id:            uuidInput,
				CurrentUserId: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), uuidInput))
				}).Return(errors.New("error"))
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{Role: enums.UserRole_User}, nil)
				d.repo.On("FindOrganizationByID", mock.Anything, uuidInput).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil)
				d.repo.On("DeleteMembersByOrganizationID", mock.Anything, uuidInput).Return(nil)
				d.repo.On("ListRepositories", mock.Anything, mock.Anything).Return([]entities.Repository{
					{
						RefGitRepoID: 1,
						UserID:       &uuidInput,
						OrgID:        &uuidInput,
						BaseModel: entities.BaseModel{
							ID: uuidInput,
						},
					},
				}, nil)
				d.gitlabClient.On("DeleteProject", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("DeleteRepositoryMemberByRepositoryID", mock.Anything, mock.Anything).Return(errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return error when repository.DeleteDeploymentsByRepoID return error",
			ctx:  utils.WithUserId(context.Background(), uuid.New()),
			input: dto.DeleteOrganizationInput{
				Id:            uuidInput,
				CurrentUserId: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), uuidInput))
				}).Return(errors.New("error"))
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{Role: enums.UserRole_User}, nil)
				d.repo.On("FindOrganizationByID", mock.Anything, uuidInput).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil)
				d.repo.On("DeleteMembersByOrganizationID", mock.Anything, uuidInput).Return(nil)
				d.repo.On("ListRepositories", mock.Anything, mock.Anything).Return([]entities.Repository{
					{
						RefGitRepoID: 1,
						UserID:       &uuidInput,
						OrgID:        &uuidInput,
						BaseModel: entities.BaseModel{
							ID: uuidInput,
						},
					},
				}, nil)
				d.gitlabClient.On("DeleteProject", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("DeleteRepositoryMemberByRepositoryID", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("DeleteDeploymentsByRepoID", mock.Anything, mock.Anything).Return(errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return error when repository.DeleteProjectsByOrganizationID return error",
			ctx:  utils.WithUserId(context.Background(), uuid.New()),
			input: dto.DeleteOrganizationInput{
				Id:            uuidInput,
				CurrentUserId: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), uuidInput))
				}).Return(errors.New("error"))
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{Role: enums.UserRole_User}, nil)
				d.repo.On("FindOrganizationByID", mock.Anything, uuidInput).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil)
				d.repo.On("DeleteMembersByOrganizationID", mock.Anything, uuidInput).Return(nil)
				d.repo.On("ListRepositories", mock.Anything, mock.Anything).Return([]entities.Repository{
					{
						RefGitRepoID: 1,
						UserID:       &uuidInput,
						OrgID:        &uuidInput,
						BaseModel: entities.BaseModel{
							ID: uuidInput,
						},
					},
				}, nil)
				d.gitlabClient.On("DeleteProject", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("DeleteRepositoryMemberByRepositoryID", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("DeleteDeploymentsByRepoID", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("DeleteProjectsByOrganizationID", mock.Anything, uuidInput).Return(errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return error when repository.ListRepositories return error",
			ctx:  utils.WithUserId(context.Background(), uuid.New()),
			input: dto.DeleteOrganizationInput{
				Id:            uuidInput,
				CurrentUserId: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), uuidInput))
				}).Return(errors.New("error"))
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{Role: enums.UserRole_User}, nil)
				d.repo.On("FindOrganizationByID", mock.Anything, uuidInput).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil)
				d.repo.On("DeleteMembersByOrganizationID", mock.Anything, uuidInput).Return(nil)
				d.repo.On("ListRepositories", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return error when gitlabClient.DeleteOrgGitGroup return error",
			ctx:  utils.WithUserId(context.Background(), uuid.New()),
			input: dto.DeleteOrganizationInput{
				Id:            uuidInput,
				CurrentUserId: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), uuidInput))
				}).Return(errors.New("error"))
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{Role: enums.UserRole_User}, nil)
				d.repo.On("FindOrganizationByID", mock.Anything, uuidInput).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil)
				d.repo.On("DeleteMembersByOrganizationID", mock.Anything, uuidInput).Return(nil)
				d.repo.On("ListRepositories", mock.Anything, mock.Anything).Return([]entities.Repository{
					{
						RefGitRepoID: 1,
						UserID:       &uuidInput,
						OrgID:        &uuidInput,
						BaseModel: entities.BaseModel{
							ID: uuidInput,
						},
					},
				}, nil)
				d.gitlabClient.On("DeleteProject", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("DeleteRepositoryMemberByRepositoryID", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("DeleteDeploymentsByRepoID", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("DeleteProjectsByOrganizationID", mock.Anything, uuidInput).Return(nil)
				d.repo.On("FindOrgGitGroup", mock.Anything, mock.Anything).Return(&entities.DefaultGitGroup{}, nil)
				d.gitlabClient.On("DeleteGroup", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("DeleteOrgGitGroup", mock.Anything, mock.Anything).Return(errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return error when repository.FindOrgGitGroup return error",
			ctx:  utils.WithUserId(context.Background(), uuid.New()),
			input: dto.DeleteOrganizationInput{
				Id:            uuidInput,
				CurrentUserId: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), uuidInput))
				}).Return(errors.New("error"))
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{Role: enums.UserRole_User}, nil)
				d.repo.On("FindOrganizationByID", mock.Anything, uuidInput).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil)
				d.repo.On("DeleteMembersByOrganizationID", mock.Anything, uuidInput).Return(nil)
				d.repo.On("ListRepositories", mock.Anything, mock.Anything).Return([]entities.Repository{
					{
						RefGitRepoID: 1,
						UserID:       &uuidInput,
						OrgID:        &uuidInput,
						BaseModel: entities.BaseModel{
							ID: uuidInput,
						},
					},
				}, nil)
				d.gitlabClient.On("DeleteProject", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("DeleteRepositoryMemberByRepositoryID", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("DeleteDeploymentsByRepoID", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("DeleteProjectsByOrganizationID", mock.Anything, uuidInput).Return(nil)
				d.repo.On("FindOrgGitGroup", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return error when repository.DeleteById return error",
			ctx:  utils.WithUserId(context.Background(), uuid.New()),
			input: dto.DeleteOrganizationInput{
				Id:            uuidInput,
				CurrentUserId: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), uuidInput))
				}).Return(errors.New("error"))
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{Role: enums.UserRole_User}, nil)
				d.repo.On("FindOrganizationByID", mock.Anything, uuidInput).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil)
				d.repo.On("DeleteMembersByOrganizationID", mock.Anything, uuidInput).Return(nil)
				d.repo.On("ListRepositories", mock.Anything, mock.Anything).Return([]entities.Repository{
					{
						RefGitRepoID: 1,
						UserID:       &uuidInput,
						OrgID:        &uuidInput,
						BaseModel: entities.BaseModel{
							ID: uuidInput,
						},
					},
				}, nil)
				d.gitlabClient.On("DeleteProject", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("DeleteRepositoryMemberByRepositoryID", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("DeleteDeploymentsByRepoID", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("DeleteProjectsByOrganizationID", mock.Anything, uuidInput).Return(nil)
				d.repo.On("FindOrgGitGroup", mock.Anything, mock.Anything).Return(&entities.DefaultGitGroup{}, nil)
				d.gitlabClient.On("DeleteGroup", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("DeleteOrgGitGroup", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("DeleteById", mock.Anything, mock.Anything, mock.Anything).Return(errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return error when gitlabClient.DeleteGroup return error",
			ctx:  utils.WithUserId(context.Background(), uuid.New()),
			input: dto.DeleteOrganizationInput{
				Id:            uuidInput,
				CurrentUserId: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), uuidInput))
				}).Return(errors.New("error"))
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{Role: enums.UserRole_User}, nil)
				d.repo.On("FindOrganizationByID", mock.Anything, uuidInput).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil)
				d.repo.On("DeleteMembersByOrganizationID", mock.Anything, uuidInput).Return(nil)
				d.repo.On("ListRepositories", mock.Anything, mock.Anything).Return([]entities.Repository{
					{
						RefGitRepoID: 1,
						UserID:       &uuidInput,
						OrgID:        &uuidInput,
						BaseModel: entities.BaseModel{
							ID: uuidInput,
						},
					},
				}, nil)
				d.gitlabClient.On("DeleteProject", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("DeleteRepositoryMemberByRepositoryID", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("DeleteDeploymentsByRepoID", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("DeleteProjectsByOrganizationID", mock.Anything, uuidInput).Return(nil)
				d.repo.On("FindOrgGitGroup", mock.Anything, mock.Anything).Return(&entities.DefaultGitGroup{}, nil)
				d.gitlabClient.On("DeleteGroup", mock.Anything, mock.Anything).Return(errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "return error when gitlabClient.DeleteOrgGitGroup error",
			ctx:  utils.WithUserId(context.Background(), uuid.New()),
			input: dto.DeleteOrganizationInput{
				Id:            uuidInput,
				CurrentUserId: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), uuidInput))
				}).Return(errors.New("error"))
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{Role: enums.UserRole_User}, nil)
				d.repo.On("FindOrganizationByID", mock.Anything, uuidInput).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil)
				d.repo.On("DeleteMembersByOrganizationID", mock.Anything, uuidInput).Return(nil)
				d.repo.On("ListRepositories", mock.Anything, mock.Anything).Return([]entities.Repository{
					{
						RefGitRepoID: 1,
						UserID:       &uuidInput,
						OrgID:        &uuidInput,
						BaseModel: entities.BaseModel{
							ID: uuidInput,
						},
					},
				}, nil)
				d.gitlabClient.On("DeleteProject", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("DeleteRepositoryMemberByRepositoryID", mock.Anything, uuidInput).Return(nil)
				d.repo.On("DeleteDeploymentsByRepoID", mock.Anything, uuidInput).Return(nil)
				d.repo.On("DeleteById", mock.Anything, mock.Anything, mock.Anything).Return(nil)
				d.repo.On("DeleteProjectsByOrganizationID", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("FindOrgGitGroup", mock.Anything, mock.Anything).Return(&entities.DefaultGitGroup{}, nil)
				d.gitlabClient.On("DeleteGroup", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("DeleteOrgGitGroup", mock.Anything, mock.Anything).Return(errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "success",
			ctx:  utils.WithUserId(context.Background(), uuid.New()),
			input: dto.DeleteOrganizationInput{
				Id:            uuidInput,
				CurrentUserId: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), uuidInput))
				}).Return(nil)
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{Role: enums.UserRole_User}, nil)
				d.repo.On("FindOrganizationByID", mock.Anything, uuidInput).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil)
				d.repo.On("DeleteMembersByOrganizationID", mock.Anything, uuidInput).Return(nil)
				d.repo.On("ListRepositories", mock.Anything, mock.Anything).Return([]entities.Repository{
					{
						RefGitRepoID: 1,
						UserID:       &uuidInput,
						OrgID:        &uuidInput,
						BaseModel: entities.BaseModel{
							ID: uuidInput,
						},
					},
				}, nil)
				d.gitlabClient.On("DeleteProject", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("DeleteRepositoryMemberByRepositoryID", mock.Anything, uuidInput).Return(nil)
				d.repo.On("DeleteDeploymentsByRepoID", mock.Anything, uuidInput).Return(nil)
				d.repo.On("DeleteById", mock.Anything, mock.Anything, mock.Anything).Return(nil)
				d.repo.On("DeleteProjectsByOrganizationID", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("FindOrgGitGroup", mock.Anything, mock.Anything).Return(&entities.DefaultGitGroup{}, nil)
				d.gitlabClient.On("DeleteGroup", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("DeleteOrgGitGroup", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("DeleteById", mock.Anything, mock.Anything, mock.Anything).Return(nil)
			},
			expectErr: nil,
		}, {
			name: "success when user is admin",
			ctx:  utils.WithUserId(context.Background(), uuid.New()),
			input: dto.DeleteOrganizationInput{
				Id:            uuidInput,
				CurrentUserId: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), uuidInput))
				}).Return(nil)
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{Role: enums.UserRole_Admin}, nil)
				d.repo.On("FindOrganizationByID", mock.Anything, uuidInput).Return(&entities.Organization{}, nil)
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{RefGitUserID: enums.AdminRefGitID}, nil)
				d.repo.On("DeleteMembersByOrganizationID", mock.Anything, uuidInput).Return(nil)
				d.repo.On("ListRepositories", mock.Anything, mock.Anything).Return([]entities.Repository{
					{
						RefGitRepoID: 1,
						UserID:       &uuidInput,
						OrgID:        &uuidInput,
						BaseModel: entities.BaseModel{
							ID: uuidInput,
						},
					},
				}, nil)
				d.gitlabClient.On("DeleteProject", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("DeleteRepositoryMemberByRepositoryID", mock.Anything, uuidInput).Return(nil)
				d.repo.On("DeleteDeploymentsByRepoID", mock.Anything, uuidInput).Return(nil)
				d.repo.On("DeleteById", mock.Anything, mock.Anything, mock.Anything).Return(nil)
				d.repo.On("DeleteProjectsByOrganizationID", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("FindOrgGitGroup", mock.Anything, mock.Anything).Return(&entities.DefaultGitGroup{}, nil)
				d.gitlabClient.On("DeleteGroup", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("DeleteOrgGitGroup", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("DeleteById", mock.Anything, mock.Anything, mock.Anything).Return(nil)
			},
			expectErr: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				config: &configs.GlobalConfig{Gitlab: &configs.GitlabConfig{
					VolvoApiServerHost: "http://localhost",
				}},
				repo:         &repository_mocks.MockRepository{},
				gitlabClient: &gitlab_mocks.MockGitlabClient{},
				mailClient:   &mail_mocks.MockMailClient{},
			}
			tt.mockFn(d)

			u := organization.New(d.config, d.repo, d.gitlabClient, d.mailClient, nil)
			err := u.DeleteOrganization(tt.ctx, tt.input)
			require.Equal(t, tt.expectErr, err)
		})
	}
}

func TestInviteUser(t *testing.T) {
	userId := uuid.New()
	orgId := uuid.New()
	type dependencies struct {
		config       *configs.GlobalConfig
		repo         *repository_mocks.MockRepository
		gitlabClient *gitlab_mocks.MockGitlabClient
		mailClient   *mail_mocks.MockMailClient
	}

	token := "token"
	expiredTime := time.Now().AddDate(0, 0, -1)
	expireTime := time.Date(2026, 1, 2, 15, 4, 5, 0, time.UTC)
	tests := []struct {
		name      string
		ctx       context.Context
		input     dto.InviteOrgMemberInput
		mockFn    func(d *dependencies)
		expectErr error
	}{
		{
			name: "should return error when repository.ErrUserNotFound return error",
			ctx:  context.Background(),
			input: dto.InviteOrgMemberInput{
				OrgId:    uuid.New(),
				UserId:   userId,
				Role:     "owner",
				ExpireAt: &expireTime,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return error when time is past",
			ctx:  context.Background(),
			input: dto.InviteOrgMemberInput{
				OrgId:    uuid.New(),
				UserId:   userId,
				Role:     "owner",
				ExpireAt: &expiredTime,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
			},
			expectErr: usecase.ErrTimeIsPast,
		},
		{
			name: "should return error when repository.FindUserByID return gorm.ErrRecordNotFound",
			ctx:  context.Background(),
			input: dto.InviteOrgMemberInput{
				OrgId:         orgId,
				UserId:        userId,
				Role:          "owner",
				ExpireAt:      &expireTime,
				CurrentUserID: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, userId).Return(nil, gorm.ErrRecordNotFound)
			},
			expectErr: usecase.ErrUserNotFound,
		},
		{
			name: "should return error when repository.FindOrganizationByID return error",
			ctx:  context.Background(),
			input: dto.InviteOrgMemberInput{
				OrgId:         orgId,
				UserId:        userId,
				Role:          "owner",
				ExpireAt:      &expireTime,
				CurrentUserID: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, userId).Return(&entities.User{}, nil)
				d.repo.On("FindOrganizationByID", mock.Anything, orgId).Return(nil, errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return error when user have no permission return no permission",
			ctx:  context.Background(),
			input: dto.InviteOrgMemberInput{
				OrgId:         orgId,
				UserId:        userId,
				Role:          "owner",
				ExpireAt:      &expireTime,
				CurrentUserID: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, userId).Return(&entities.User{}, nil)
				d.repo.On("FindOrganizationByID", mock.Anything, orgId).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Developer}, nil)
			},
			expectErr: usecase.ErrNoPermission,
		},
		{
			name: "should return error when repository.FindOrganizationByID return gorm.ErrRecordNotFound",
			ctx:  context.Background(),
			input: dto.InviteOrgMemberInput{
				OrgId:         orgId,
				UserId:        userId,
				Role:          "owner",
				ExpireAt:      &expireTime,
				CurrentUserID: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, userId).Return(&entities.User{}, nil)
				d.repo.On("FindOrganizationByID", mock.Anything, orgId).Return(nil, gorm.ErrRecordNotFound)
			},
			expectErr: usecase.ErrOrganizationNotFound,
		},
		{
			name: "should return error when repository.FindUserByID return error",
			ctx:  context.Background(),
			input: dto.InviteOrgMemberInput{
				OrgId:         orgId,
				UserId:        userId,
				Role:          "owner",
				ExpireAt:      &expireTime,
				CurrentUserID: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, userId).Return(&entities.User{}, nil).Once()
				d.repo.On("FindOrganizationByID", mock.Anything, orgId).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil).Once()
				d.repo.On("FindUserByID", mock.Anything, userId).Return(nil, errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return error when repository.FindUserByID return error",
			ctx:  context.Background(),
			input: dto.InviteOrgMemberInput{
				OrgId:         orgId,
				UserId:        userId,
				Role:          "owner",
				ExpireAt:      &expireTime,
				CurrentUserID: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, userId).Return(&entities.User{}, nil).Once()
				d.repo.On("FindOrganizationByID", mock.Anything, orgId).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil).Once()
				d.repo.On("FindUserByID", mock.Anything, userId).Return(nil, gorm.ErrRecordNotFound)
			},
			expectErr: usecase.ErrUserNotFound,
		},
		{
			name: "should return error when repository.FindUser return error",
			ctx:  context.Background(),
			input: dto.InviteOrgMemberInput{
				OrgId:         orgId,
				UserId:        userId,
				Role:          "owner",
				ExpireAt:      &expireTime,
				CurrentUserID: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, userId).Return(&entities.User{Role: enums.UserRole_Admin}, nil)
				d.repo.On("FindOrganizationByID", mock.Anything, orgId).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil)
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return error when repository.Create return error when in role admin",
			ctx:  context.Background(),
			input: dto.InviteOrgMemberInput{
				OrgId:         orgId,
				UserId:        userId,
				Role:          "owner",
				ExpireAt:      &expireTime,
				CurrentUserID: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, userId).Return(&entities.User{Role: enums.UserRole_Admin}, nil)
				d.repo.On("FindOrganizationByID", mock.Anything, orgId).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil).Once()
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{GitlabAccessToken: &token}, nil)
				d.repo.On("FindOrgGitGroup", mock.Anything, mock.Anything).Return(&entities.DefaultGitGroup{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{}, nil).Once()
				d.repo.On("Transaction", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), userId))
				}).Return(errors.New("error"))
				d.gitlabClient.On("GetMemberOfGroup", mock.Anything, mock.Anything).Return(nil, errors.New("user not found"))
				d.gitlabClient.On("AddUserToGroup", mock.Anything, mock.Anything).Return(&gitlab.AddUserToGroupResponse{}, nil)
				d.repo.On("Create", mock.Anything, mock.Anything).Return(errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return error when repository.Create return error",
			ctx:  context.Background(),
			input: dto.InviteOrgMemberInput{
				OrgId:         orgId,
				UserId:        userId,
				Role:          "owner",
				ExpireAt:      &expireTime,
				CurrentUserID: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, userId).Return(&entities.User{}, nil)
				d.repo.On("FindOrganizationByID", mock.Anything, orgId).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil).Once()
				d.repo.On("FindOrgGitGroup", mock.Anything, mock.Anything).Return(&entities.DefaultGitGroup{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{}, nil).Once()
				d.repo.On("Transaction", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), userId))
				}).Return(errors.New("error"))
				d.gitlabClient.On("GetMemberOfGroup", mock.Anything, mock.Anything).Return(nil, errors.New("user not found"))
				d.gitlabClient.On("AddUserToGroup", mock.Anything, mock.Anything).Return(&gitlab.AddUserToGroupResponse{}, nil)
				d.repo.On("Create", mock.Anything, mock.Anything).Return(errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return error when mailClient.SendMail return error",
			ctx:  context.Background(),
			input: dto.InviteOrgMemberInput{
				OrgId:         orgId,
				UserId:        userId,
				Role:          "owner",
				ExpireAt:      &expireTime,
				CurrentUserID: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, userId).Return(&entities.User{GitlabAccessToken: &token}, nil)
				d.repo.On("FindOrganizationByID", mock.Anything, orgId).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil).Once()
				d.repo.On("FindOrgGitGroup", mock.Anything, mock.Anything).Return(&entities.DefaultGitGroup{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{}, nil).Once()
				d.repo.On("Transaction", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), userId))
				}).Return(usecase.ErrSendMail)
				d.gitlabClient.On("GetMemberOfGroup", mock.Anything, mock.Anything).Return(nil, errors.New("user not found"))
				d.gitlabClient.On("AddUserToGroup", mock.Anything, mock.Anything).Return(&gitlab.AddUserToGroupResponse{}, nil)
				d.repo.On("Create", mock.Anything, mock.Anything).Return(nil)
				d.mailClient.On("SendMail", mock.Anything, mock.Anything).Return(usecase.ErrSendMail)
			},
			expectErr: usecase.ErrSendMail,
		},
		{
			name: "should return error when repo.FindOrgGitGroup return error",
			ctx:  context.Background(),
			input: dto.InviteOrgMemberInput{
				OrgId:         orgId,
				UserId:        userId,
				Role:          "owner",
				ExpireAt:      &expireTime,
				CurrentUserID: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, userId).Return(&entities.User{}, nil)
				d.repo.On("FindOrganizationByID", mock.Anything, orgId).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil)
				d.repo.On("FindOrgGitGroup", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return error when SendMail return error",
			ctx:  context.Background(),
			input: dto.InviteOrgMemberInput{
				OrgId:         orgId,
				UserId:        userId,
				Role:          "owner",
				ExpireAt:      &expireTime,
				CurrentUserID: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, userId).Return(&entities.User{}, nil)
				d.repo.On("FindOrganizationByID", mock.Anything, orgId).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil).Once()
				d.repo.On("FindOrgGitGroup", mock.Anything, mock.Anything).Return(&entities.DefaultGitGroup{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{}, nil).Once()
				d.repo.On("Transaction", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), userId))
				}).Return(nil)
				d.gitlabClient.On("GetMemberOfGroup", mock.Anything, mock.Anything).Return(nil, errors.New("user not found"))
				d.gitlabClient.On("AddUserToGroup", mock.Anything, mock.Anything).Return(&gitlab.AddUserToGroupResponse{}, nil)
				d.repo.On("Create", mock.Anything, mock.Anything).Return(nil)
				d.mailClient.On("SendMail", mock.Anything, mock.Anything).Return(usecase.ErrSendMail)
			},
			expectErr: usecase.ErrSendMail,
		},
		{
			name: "success with invite with expire time",
			ctx:  context.Background(),
			input: dto.InviteOrgMemberInput{
				OrgId:         orgId,
				UserId:        userId,
				Role:          "owner",
				ExpireAt:      &expireTime,
				CurrentUserID: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, userId).Return(&entities.User{}, nil)
				d.repo.On("FindOrganizationByID", mock.Anything, orgId).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil).Once()
				d.repo.On("FindOrgGitGroup", mock.Anything, mock.Anything).Return(&entities.DefaultGitGroup{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{}, nil).Once()
				d.repo.On("Transaction", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), userId))
				}).Return(nil)
				d.gitlabClient.On("GetMemberOfGroup", mock.Anything, mock.Anything).Return(nil, errors.New("user not found"))
				d.gitlabClient.On("AddUserToGroup", mock.Anything, mock.Anything).Return(&gitlab.AddUserToGroupResponse{}, nil)
				d.repo.On("Create", mock.Anything, mock.Anything).Return(nil)
				d.mailClient.On("SendMail", mock.Anything, mock.Anything).Return(nil)
			},
			expectErr: nil,
		},
		{
			name: "success with invite without expire time",
			ctx:  context.Background(),
			input: dto.InviteOrgMemberInput{
				OrgId:         orgId,
				UserId:        userId,
				Role:          "owner",
				CurrentUserID: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, userId).Return(&entities.User{}, nil)
				d.repo.On("FindOrganizationByID", mock.Anything, orgId).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil).Once()
				d.repo.On("FindOrgGitGroup", mock.Anything, mock.Anything).Return(&entities.DefaultGitGroup{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{}, nil).Once()
				d.repo.On("Transaction", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), userId))
				}).Return(nil)
				d.gitlabClient.On("GetMemberOfGroup", mock.Anything, mock.Anything).Return(nil, errors.New("user not found"))
				d.gitlabClient.On("AddUserToGroup", mock.Anything, mock.Anything).Return(&gitlab.AddUserToGroupResponse{}, nil)
				d.repo.On("Create", mock.Anything, mock.Anything).Return(nil)
				d.mailClient.On("SendMail", mock.Anything, mock.Anything).Return(nil)
			},
			expectErr: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				config: &configs.GlobalConfig{Gitlab: &configs.GitlabConfig{
					VolvoApiServerHost: "http://localhost",
				}},
				repo:         &repository_mocks.MockRepository{},
				gitlabClient: &gitlab_mocks.MockGitlabClient{},
				mailClient:   &mail_mocks.MockMailClient{},
			}
			tt.mockFn(d)

			u := organization.New(d.config, d.repo, d.gitlabClient, d.mailClient, nil)
			err := u.InviteUser(tt.ctx, tt.input)
			require.Equal(t, tt.expectErr, err)
		})
	}
}

func TestInviteMultiUser(t *testing.T) {
	userId := uuid.New()

	type dependencies struct {
		config       *configs.GlobalConfig
		repo         *repository_mocks.MockRepository
		gitlabClient *gitlab_mocks.MockGitlabClient
		mailClient   *mail_mocks.MockMailClient
	}

	token := "token"
	expiredTime := time.Now().AddDate(0, 0, -1)
	expireTime := time.Date(2026, 1, 2, 15, 4, 5, 0, time.UTC)
	tests := []struct {
		name      string
		ctx       context.Context
		input     dto.InviteOrgMembersInput
		mockFn    func(d *dependencies)
		expectErr error
	}{
		{
			name: "should return error when repository.ErrUserNotFound return error",
			ctx:  context.Background(),
			input: dto.InviteOrgMembersInput{
				OrgId: uuid.New(),
				Members: []dto.OrgMemberInviteInput{
					{
						UserId:   userId,
						Role:     "owner",
						ExpireAt: &expireTime,
					},
				},
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return error when repository.FindUserByID return gorm.ErrRecordNotFound",
			ctx:  context.Background(),
			input: dto.InviteOrgMembersInput{
				OrgId: uuid.New(),
				Members: []dto.OrgMemberInviteInput{
					{
						UserId:   userId,
						Role:     "owner",
						ExpireAt: &expiredTime,
					},
				},
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expectErr: usecase.ErrUserNotFound,
		},
		{
			name: "should return error when repository.FindOrganizationByID return error",
			ctx:  context.Background(),
			input: dto.InviteOrgMembersInput{
				OrgId: uuid.New(),
				Members: []dto.OrgMemberInviteInput{
					{
						UserId:   userId,
						Role:     "owner",
						ExpireAt: &expiredTime,
					},
				},
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindOrganizationByID", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return error when user have no permission return no permission",
			ctx:  context.Background(),
			input: dto.InviteOrgMembersInput{
				OrgId: uuid.New(),
				Members: []dto.OrgMemberInviteInput{
					{
						UserId:   userId,
						Role:     "owner",
						ExpireAt: &expiredTime,
					},
				},
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindOrganizationByID", mock.Anything, mock.Anything).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Developer}, nil)
			},
			expectErr: usecase.ErrNoPermission,
		},
		{
			name: "should return error when repository.FindOrganizationByID return gorm.ErrRecordNotFound",
			ctx:  context.Background(),
			input: dto.InviteOrgMembersInput{
				OrgId: uuid.New(),
				Members: []dto.OrgMemberInviteInput{
					{
						UserId:   userId,
						Role:     "owner",
						ExpireAt: &expiredTime,
					},
				},
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindOrganizationByID", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expectErr: usecase.ErrOrganizationNotFound,
		},
		{
			name: "should return error when repository.FindUser return error",
			ctx:  context.Background(),
			input: dto.InviteOrgMembersInput{
				OrgId: uuid.New(),
				Members: []dto.OrgMemberInviteInput{
					{
						UserId:   userId,
						Role:     "owner",
						ExpireAt: &expiredTime,
					},
				},
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{Role: enums.UserRole_Admin}, nil)
				d.repo.On("FindOrganizationByID", mock.Anything, mock.Anything).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil)
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return error when repository.Create return expired time when in role admin",
			ctx:  context.Background(),
			input: dto.InviteOrgMembersInput{
				OrgId: uuid.New(),
				Members: []dto.OrgMemberInviteInput{
					{
						UserId:   userId,
						Role:     "owner",
						ExpireAt: &expiredTime,
					},
				},
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{Role: enums.UserRole_Admin}, nil)
				d.repo.On("FindOrganizationByID", mock.Anything, mock.Anything).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil).Once()
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{GitlabAccessToken: &token}, nil)
				d.repo.On("FindOrgGitGroup", mock.Anything, mock.Anything).Return(&entities.DefaultGitGroup{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{}, nil).Once()
				d.repo.On("Transaction", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), userId))
				}).Return(errors.New("error"))
				d.gitlabClient.On("GetMemberOfGroup", mock.Anything, mock.Anything).Return(nil, errors.New("user not found"))
				d.gitlabClient.On("AddUserToGroup", mock.Anything, mock.Anything).Return(&gitlab.AddUserToGroupResponse{}, nil)
				d.repo.On("Create", mock.Anything, mock.Anything).Return(errors.New("error"))
			},
			expectErr: usecase.ErrTimeIsPast,
		},
		{
			name: "should return error when repository.Create return error when in role admin",
			ctx:  context.Background(),
			input: dto.InviteOrgMembersInput{
				OrgId: uuid.New(),
				Members: []dto.OrgMemberInviteInput{
					{
						UserId:   userId,
						Role:     "owner",
						ExpireAt: &expireTime,
					},
				},
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{Role: enums.UserRole_Admin}, nil)
				d.repo.On("FindOrganizationByID", mock.Anything, mock.Anything).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil).Once()
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{GitlabAccessToken: &token}, nil)
				d.repo.On("FindOrgGitGroup", mock.Anything, mock.Anything).Return(&entities.DefaultGitGroup{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{}, nil).Once()
				d.repo.On("Transaction", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), userId))
				}).Return(errors.New("error"))
				d.gitlabClient.On("GetMemberOfGroup", mock.Anything, mock.Anything).Return(nil, errors.New("user not found"))
				d.gitlabClient.On("AddUserToGroup", mock.Anything, mock.Anything).Return(&gitlab.AddUserToGroupResponse{}, nil)
				d.repo.On("Create", mock.Anything, mock.Anything).Return(errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return error when repository.Create return error",
			ctx:  context.Background(),
			input: dto.InviteOrgMembersInput{
				OrgId: uuid.New(),
				Members: []dto.OrgMemberInviteInput{
					{
						UserId:   userId,
						Role:     "owner",
						ExpireAt: &expireTime,
					},
				},
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindOrganizationByID", mock.Anything, mock.Anything).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil).Once()
				d.repo.On("FindOrgGitGroup", mock.Anything, mock.Anything).Return(&entities.DefaultGitGroup{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{}, nil).Once()
				d.repo.On("Transaction", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), userId))
				}).Return(errors.New("error"))
				d.gitlabClient.On("GetMemberOfGroup", mock.Anything, mock.Anything).Return(nil, errors.New("user not found"))
				d.gitlabClient.On("AddUserToGroup", mock.Anything, mock.Anything).Return(&gitlab.AddUserToGroupResponse{}, nil)
				d.repo.On("Create", mock.Anything, mock.Anything).Return(errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return error when mailClient.SendMail return error",
			ctx:  context.Background(),
			input: dto.InviteOrgMembersInput{
				OrgId: uuid.New(),
				Members: []dto.OrgMemberInviteInput{
					{
						UserId:   userId,
						Role:     "owner",
						ExpireAt: &expireTime,
					},
				},
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{GitlabAccessToken: &token}, nil)
				d.repo.On("FindOrganizationByID", mock.Anything, mock.Anything).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil).Once()
				d.repo.On("FindOrgGitGroup", mock.Anything, mock.Anything).Return(&entities.DefaultGitGroup{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{}, nil).Once()
				d.repo.On("Transaction", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), userId))
				}).Return(usecase.ErrSendMail)
				d.gitlabClient.On("GetMemberOfGroup", mock.Anything, mock.Anything).Return(nil, errors.New("user not found"))
				d.gitlabClient.On("AddUserToGroup", mock.Anything, mock.Anything).Return(&gitlab.AddUserToGroupResponse{}, nil)
				d.repo.On("Create", mock.Anything, mock.Anything).Return(nil)
				d.mailClient.On("SendMail", mock.Anything, mock.Anything).Return(usecase.ErrSendMail)
			},
			expectErr: usecase.ErrSendMail,
		},
		{
			name: "should return error when repo.FindOrgGitGroup return error",
			ctx:  context.Background(),
			input: dto.InviteOrgMembersInput{
				OrgId: uuid.New(),
				Members: []dto.OrgMemberInviteInput{
					{
						UserId:   userId,
						Role:     "owner",
						ExpireAt: &expireTime,
					},
				},
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindOrganizationByID", mock.Anything, mock.Anything).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil)
				d.repo.On("FindOrgGitGroup", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return error when SendMail return error",
			ctx:  context.Background(),
			input: dto.InviteOrgMembersInput{
				OrgId: uuid.New(),
				Members: []dto.OrgMemberInviteInput{
					{
						UserId:   userId,
						Role:     "owner",
						ExpireAt: &expireTime,
					},
				},
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindOrganizationByID", mock.Anything, mock.Anything).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil).Once()
				d.repo.On("FindOrgGitGroup", mock.Anything, mock.Anything).Return(&entities.DefaultGitGroup{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{}, nil).Once()
				d.repo.On("Transaction", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), userId))
				}).Return(nil)
				d.gitlabClient.On("GetMemberOfGroup", mock.Anything, mock.Anything).Return(nil, errors.New("user not found"))
				d.gitlabClient.On("AddUserToGroup", mock.Anything, mock.Anything).Return(&gitlab.AddUserToGroupResponse{}, nil)
				d.repo.On("Create", mock.Anything, mock.Anything).Return(nil)
				d.mailClient.On("SendMail", mock.Anything, mock.Anything).Return(usecase.ErrSendMail)
			},
			expectErr: usecase.ErrSendMail,
		},
		{
			name: "success with invite with expire time",
			ctx:  context.Background(),
			input: dto.InviteOrgMembersInput{
				OrgId: uuid.New(),
				Members: []dto.OrgMemberInviteInput{
					{
						UserId:   userId,
						Role:     "owner",
						ExpireAt: &expireTime,
					},
				},
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindOrganizationByID", mock.Anything, mock.Anything).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil).Once()
				d.repo.On("FindOrgGitGroup", mock.Anything, mock.Anything).Return(&entities.DefaultGitGroup{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{}, nil).Once()
				d.repo.On("Transaction", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), userId))
				}).Return(nil)
				d.gitlabClient.On("GetMemberOfGroup", mock.Anything, mock.Anything).Return(nil, errors.New("user not found"))
				d.gitlabClient.On("AddUserToGroup", mock.Anything, mock.Anything).Return(&gitlab.AddUserToGroupResponse{}, nil)
				d.repo.On("Create", mock.Anything, mock.Anything).Return(nil)
				d.mailClient.On("SendMail", mock.Anything, mock.Anything).Return(nil)
			},
			expectErr: nil,
		},
		{
			name: "success with invite without expire time",
			ctx:  context.Background(),
			input: dto.InviteOrgMembersInput{
				OrgId: uuid.New(),
				Members: []dto.OrgMemberInviteInput{
					{
						UserId: userId,
						Role:   "owner",
					},
				},
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindOrganizationByID", mock.Anything, mock.Anything).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil).Once()
				d.repo.On("FindOrgGitGroup", mock.Anything, mock.Anything).Return(&entities.DefaultGitGroup{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{}, nil).Once()
				d.repo.On("Transaction", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), userId))
				}).Return(nil)
				d.gitlabClient.On("GetMemberOfGroup", mock.Anything, mock.Anything).Return(nil, errors.New("user not found"))
				d.gitlabClient.On("AddUserToGroup", mock.Anything, mock.Anything).Return(&gitlab.AddUserToGroupResponse{}, nil)
				d.repo.On("Create", mock.Anything, mock.Anything).Return(nil)
				d.mailClient.On("SendMail", mock.Anything, mock.Anything).Return(nil)
			},
			expectErr: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				config: &configs.GlobalConfig{Gitlab: &configs.GitlabConfig{
					VolvoApiServerHost: "http://localhost",
				}},
				repo:         &repository_mocks.MockRepository{},
				gitlabClient: &gitlab_mocks.MockGitlabClient{},
				mailClient:   &mail_mocks.MockMailClient{},
			}
			tt.mockFn(d)

			u := organization.New(d.config, d.repo, d.gitlabClient, d.mailClient, nil)
			err := u.InviteMultipleOrgMembers(tt.ctx, tt.input)
			require.Equal(t, tt.expectErr, err)
		})
	}
}

func TestListCurrentUserOrganizations(t *testing.T) {
	uuidInput := uuid.New()
	avatarUrl := "https://avatar.com"
	type dependencies struct {
		config    *configs.GlobalConfig
		repo      *repository_mocks.MockRepository
		awsClient *aws_mocks.MockAWSClient
	}

	tests := []struct {
		name      string
		ctx       context.Context
		input     dto.ListOrganizationsInput
		mockFn    func(d *dependencies)
		expectErr error
		output    *dto.ListOrganizationsOutput
	}{
		{
			name: "should return error when repository.ListOrganizations returns error",
			ctx:  context.Background(),
			input: dto.ListOrganizationsInput{
				UserId: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("ListOrganizations", mock.Anything, mock.Anything).Return(0, nil, errors.New("error"))
			},
			expectErr: errors.New("error"),
			output:    nil,
		},
		{
			name: "should return error when repo avatar ListRepositories returns error",
			ctx:  context.Background(),
			input: dto.ListOrganizationsInput{
				UserId: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("ListOrganizations", mock.Anything, mock.Anything).Return(1, []entities.Organization{
					{
						BaseModel: entities.BaseModel{
							ID: uuidInput,
						},
						Name: "test",
					},
				}, nil)
				d.repo.On("ListRepositories", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectErr: errors.New("error"),
			output:    nil,
		},
		{
			name: "should error when users avatar  awsClient.GenPreSignUrl returns error",
			ctx:  context.Background(),
			input: dto.ListOrganizationsInput{
				UserId: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("ListOrganizations", mock.Anything, mock.Anything).Return(1, []entities.Organization{
					{
						BaseModel: entities.BaseModel{
							ID: uuidInput,
						},
						Name:   "test",
						Avatar: &avatarUrl,
					},
				}, nil)
				d.repo.On("ListRepositories", mock.Anything, mock.Anything).Return([]entities.Repository{
					{
						BaseModel: entities.BaseModel{
							ID: uuidInput,
						},
						Name:   "test",
						Avatar: &avatarUrl,
						User:   &entities.User{Avatar: &avatarUrl},
						Org:    &entities.Organization{Avatar: &avatarUrl},
					},
				}, nil)
				d.awsClient.On("GenPreSignUrl", mock.Anything, mock.Anything).Return("", errors.New("error"))
			},
			expectErr: errors.New("error"),
			output:    nil,
		},
		{
			name: "should error when orgs avatar awsClient.GenPreSignUrl returns error",
			ctx:  context.Background(),
			input: dto.ListOrganizationsInput{
				UserId: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("ListOrganizations", mock.Anything, mock.Anything).Return(1, []entities.Organization{
					{
						BaseModel: entities.BaseModel{
							ID: uuidInput,
						},
						Name:   "test",
						Avatar: &avatarUrl,
					},
				}, nil)
				d.repo.On("ListRepositories", mock.Anything, mock.Anything).Return([]entities.Repository{
					{
						BaseModel: entities.BaseModel{
							ID: uuidInput,
						},
						Name:   "test",
						Avatar: &avatarUrl,
						User:   &entities.User{Avatar: &avatarUrl},
						Org:    &entities.Organization{Avatar: &avatarUrl},
					},
				}, nil)
				d.awsClient.On("GenPreSignUrl", mock.Anything, mock.Anything).Return(avatarUrl, nil).Once()
				d.awsClient.On("GenPreSignUrl", mock.Anything, mock.Anything).Return("", errors.New("error")).Once()
			},
			expectErr: errors.New("error"),
			output:    nil,
		},
		{
			name: "should error when awsClient.GenPreSignUrl returns error",
			ctx:  context.Background(),
			input: dto.ListOrganizationsInput{
				UserId: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("ListOrganizations", mock.Anything, mock.Anything).Return(1, []entities.Organization{
					{
						BaseModel: entities.BaseModel{
							ID: uuidInput,
						},
						Name:   "test",
						Avatar: &avatarUrl,
					},
				}, nil)
				d.repo.On("ListRepositories", mock.Anything, mock.Anything).Return([]entities.Repository{
					{
						BaseModel: entities.BaseModel{
							ID: uuidInput,
						},
						Name:   "test",
						Avatar: &avatarUrl,
						User:   &entities.User{Avatar: &avatarUrl},
						Org:    &entities.Organization{Avatar: &avatarUrl},
					},
				}, nil)
				d.awsClient.On("GenPreSignUrl", mock.Anything, mock.Anything).Return(avatarUrl, nil).Once()
				d.awsClient.On("GenPreSignUrl", mock.Anything, mock.Anything).Return(avatarUrl, nil).Once()
				d.awsClient.On("GenPreSignUrl", mock.Anything, mock.Anything).Return("", errors.New("error")).Once()
			},
			expectErr: errors.New("error"),
			output:    nil,
		},
		{
			name: "should error when org avatar awsClient.GenPreSignUrl returns error",
			ctx:  context.Background(),
			input: dto.ListOrganizationsInput{
				UserId: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("ListOrganizations", mock.Anything, mock.Anything).Return(1, []entities.Organization{
					{
						BaseModel: entities.BaseModel{
							ID: uuidInput,
						},
						Name:   "test",
						Avatar: &avatarUrl,
					},
				}, nil)
				d.repo.On("ListRepositories", mock.Anything, mock.Anything).Return([]entities.Repository{
					{
						BaseModel: entities.BaseModel{
							ID: uuidInput,
						},
						Name:   "test",
						Avatar: &avatarUrl,
						User:   &entities.User{Avatar: &avatarUrl},
						Org:    &entities.Organization{Avatar: &avatarUrl},
					},
				}, nil)
				d.awsClient.On("GenPreSignUrl", mock.Anything, mock.Anything).Return(avatarUrl, nil).Once()
				d.awsClient.On("GenPreSignUrl", mock.Anything, mock.Anything).Return(avatarUrl, nil).Once()
				d.awsClient.On("GenPreSignUrl", mock.Anything, mock.Anything).Return(avatarUrl, nil).Once()
				d.awsClient.On("GenPreSignUrl", mock.Anything, mock.Anything).Return("", errors.New("error")).Once()
			},
			expectErr: errors.New("error"),
			output:    nil,
		},
		{
			name: "should success with list repositories",
			ctx:  context.Background(),
			input: dto.ListOrganizationsInput{
				UserId: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("ListOrganizations", mock.Anything, mock.Anything).Return(1, []entities.Organization{
					{
						BaseModel: entities.BaseModel{
							ID: uuidInput,
						},
						Name:   "test",
						Avatar: &avatarUrl,
					},
				}, nil)
				d.repo.On("ListRepositories", mock.Anything, mock.Anything).Return([]entities.Repository{
					{
						BaseModel: entities.BaseModel{
							ID: uuidInput,
						},
						Name:   "test",
						Avatar: &avatarUrl,
						User:   &entities.User{Avatar: &avatarUrl},
						Org:    &entities.Organization{Avatar: &avatarUrl},
					},
				}, nil)
				d.awsClient.On("GenPreSignUrl", mock.Anything, mock.Anything).Return(avatarUrl, nil)
			},
			expectErr: nil,
			output: &dto.ListOrganizationsOutput{
				Data: &[]dto.OrgReposInfo{
					{
						Org: entities.Organization{
							BaseModel: entities.BaseModel{
								ID: uuidInput,
							},
							Name:   "test",
							Avatar: &avatarUrl,
						},
						Repositories: []entities.Repository{
							{
								BaseModel: entities.BaseModel{
									ID: uuidInput,
								},
								Name:   "test",
								Avatar: &avatarUrl,
								Org:    &entities.Organization{Avatar: &avatarUrl},
								User:   &entities.User{Avatar: &avatarUrl},
							},
						},
					},
				},
				Pagination: &dto.Pagination{
					Total: 1,
				},
			},
		},
		{
			name: "success",
			ctx:  context.Background(),
			input: dto.ListOrganizationsInput{
				UserId: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("ListOrganizations", mock.Anything, mock.Anything).Return(1, []entities.Organization{
					{
						BaseModel: entities.BaseModel{
							ID: uuidInput,
						},
						Name: "test",
					},
				}, nil)
				d.repo.On("ListRepositories", mock.Anything, mock.Anything).Return([]entities.Repository{}, nil)
			},
			expectErr: nil,
			output: &dto.ListOrganizationsOutput{
				Data: &[]dto.OrgReposInfo{
					{
						Org: entities.Organization{
							BaseModel: entities.BaseModel{
								ID: uuidInput,
							},
							Name: "test",
						},
						Repositories: []entities.Repository{},
					},
				},
				Pagination: &dto.Pagination{
					Total: 1,
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				config: &configs.GlobalConfig{Gitlab: &configs.GitlabConfig{
					VolvoApiServerHost: "http://localhost",
				}},
				repo:      &repository_mocks.MockRepository{},
				awsClient: &aws_mocks.MockAWSClient{},
			}
			tt.mockFn(d)

			u := organization.New(d.config, d.repo, nil, nil, d.awsClient)
			output, err := u.ListOrganizations(tt.ctx, tt.input)
			require.Equal(t, tt.expectErr, err)
			require.Equal(t, tt.output, output)
		})
	}
}

func TestListAllOrganizations(t *testing.T) {
	uuidInput := uuid.New()
	type dependencies struct {
		config *configs.GlobalConfig
		repo   *repository_mocks.MockRepository
	}

	tests := []struct {
		name      string
		ctx       context.Context
		input     dto.ListOrganizationsInput
		mockFn    func(d *dependencies)
		expectErr error
		output    *dto.ListOrganizationsOutput
	}{
		{
			name: "should return error when repository.ListOrganizations returns error",
			ctx:  context.Background(),
			input: dto.ListOrganizationsInput{
				UserId: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("ListOrganizations", mock.Anything, mock.Anything).Return(0, nil, errors.New("error"))
			},
			expectErr: errors.New("error"),
			output:    nil,
		},
		{
			name: "success",
			ctx:  context.Background(),
			input: dto.ListOrganizationsInput{
				UserId:  uuidInput,
				Keyword: "name",
			},
			mockFn: func(d *dependencies) {
				d.repo.On("ListOrganizations", mock.Anything, mock.Anything).Return(1, []entities.Organization{
					{
						BaseModel: entities.BaseModel{
							ID: uuidInput,
						},
						Name: "test",
					},
				}, nil)
				d.repo.On("ListRepositories", mock.Anything, mock.Anything).Return([]entities.Repository{}, nil)
			},
			expectErr: nil,
			output: &dto.ListOrganizationsOutput{
				Data: &[]dto.OrgReposInfo{
					{
						Org: entities.Organization{
							BaseModel: entities.BaseModel{
								ID: uuidInput,
							},
							Name: "test",
						},
						Repositories: []entities.Repository{},
					},
				},
				Pagination: &dto.Pagination{
					Total: 1,
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				config: &configs.GlobalConfig{Gitlab: &configs.GitlabConfig{
					VolvoApiServerHost: "http://localhost",
				}},
				repo: &repository_mocks.MockRepository{},
			}
			tt.mockFn(d)

			u := organization.New(d.config, d.repo, nil, nil, nil)
			output, err := u.ListOrganizations(tt.ctx, tt.input)
			require.Equal(t, tt.expectErr, err)
			require.Equal(t, tt.output, output)
		})
	}
}

func TestGetOrganization(t *testing.T) {
	uuidInput := uuid.New()
	type dependencies struct {
		config    *configs.GlobalConfig
		repo      *repository_mocks.MockRepository
		awsClient *aws_mocks.MockAWSClient
	}

	userId := uuid.New()
	avatarUrl := "https://gitlab.com/avatar.png"
	tests := []struct {
		name      string
		ctx       context.Context
		input     dto.FindOrganizationInput
		mockFn    func(d *dependencies)
		expectErr error
		output    *dto.FindOrganizationOutput
	}{
		{
			name: "should return error when repository.FindOrganization returns error",
			ctx:  context.Background(),
			input: dto.FindOrganizationInput{
				OrgId:         uuidInput,
				CurrentUserID: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindOrganization", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectErr: errors.New("error"),
			output:    nil,
		},
		{
			name: "should return error when repository.FindOrganization returns error",
			ctx:  context.Background(),
			input: dto.FindOrganizationInput{
				OrgId:         uuidInput,
				CurrentUserID: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindOrganization", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectErr: errors.New("error"),
			output:    nil,
		},
		{
			name: "success error when return avatar",
			ctx:  context.Background(),
			input: dto.FindOrganizationInput{
				OrgId:         uuidInput,
				CurrentUserID: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindOrganization", mock.Anything, mock.Anything).Return(&entities.Organization{
					Name:     "org",
					PathName: "org",
					BaseModel: entities.BaseModel{
						ID: uuidInput,
					},
					Avatar: &avatarUrl,
				}, nil)
				d.awsClient.On("GenPreSignUrl", mock.Anything, mock.Anything).Return("", errors.New("error"))
			},
			expectErr: errors.New("error"),
			output:    nil,
		},
		{
			name: "success with avatar",
			ctx:  context.Background(),
			input: dto.FindOrganizationInput{
				OrgId:         uuidInput,
				CurrentUserID: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindOrganization", mock.Anything, mock.Anything).Return(&entities.Organization{
					Name:     "org",
					PathName: "org",
					BaseModel: entities.BaseModel{
						ID: uuidInput,
					},
					Avatar: &avatarUrl,
				}, nil)
				d.awsClient.On("GenPreSignUrl", mock.Anything, mock.Anything).Return(avatarUrl, nil)
			},
			expectErr: nil,
			output: &dto.FindOrganizationOutput{
				Organization: entities.Organization{
					Name:     "org",
					PathName: "org",
					BaseModel: entities.BaseModel{
						ID: uuidInput,
					},
					Avatar: &avatarUrl,
				},
			},
		},
		{
			name: "success",
			ctx:  context.Background(),
			input: dto.FindOrganizationInput{
				OrgId:         uuidInput,
				CurrentUserID: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindOrganization", mock.Anything, mock.Anything).Return(&entities.Organization{
					Name:     "org",
					PathName: "org",
					BaseModel: entities.BaseModel{
						ID: uuidInput,
					},
				}, nil)
			},
			expectErr: nil,
			output: &dto.FindOrganizationOutput{
				Organization: entities.Organization{
					Name:     "org",
					PathName: "org",
					BaseModel: entities.BaseModel{
						ID: uuidInput,
					},
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				config: &configs.GlobalConfig{Gitlab: &configs.GitlabConfig{
					VolvoApiServerHost: "http://localhost",
				}},
				repo:      &repository_mocks.MockRepository{},
				awsClient: &aws_mocks.MockAWSClient{},
			}
			tt.mockFn(d)

			u := organization.New(d.config, d.repo, nil, nil, d.awsClient)
			output, err := u.FindOrganizationByID(tt.ctx, tt.input)
			require.Equal(t, tt.expectErr, err)
			require.Equal(t, tt.output, output)
		})
	}
}

func TestListMemberOfOrganization(t *testing.T) {
	uuidInput := uuid.New()
	type dependencies struct {
		config *configs.GlobalConfig
		repo   *repository_mocks.MockRepository
	}

	userId := uuid.New()
	tests := []struct {
		name      string
		ctx       context.Context
		input     dto.ListOrgMembersInput
		mockFn    func(d *dependencies)
		expectErr error
		output    *dto.ListOrgMembersOutput
	}{
		{
			name: "should return error when repository.CountMembers returns error",
			ctx:  context.Background(),
			input: dto.ListOrgMembersInput{
				OrgId:         uuidInput,
				CurrentUserID: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("CountMembers", mock.Anything, mock.Anything).Return(0, errors.New("error"))
			},
			expectErr: errors.New("error"),
			output:    nil,
		},
		{
			name: "should return error when repository.ListMembers returns error",
			ctx:  context.Background(),
			input: dto.ListOrgMembersInput{
				OrgId:         uuidInput,
				CurrentUserID: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("CountMembers", mock.Anything, mock.Anything).Return(1, nil)
				d.repo.On("ListMembers", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectErr: errors.New("error"),
			output:    nil,
		},
		{
			name: "success",
			ctx:  context.Background(),
			input: dto.ListOrgMembersInput{
				OrgId:         uuidInput,
				CurrentUserID: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("CountMembers", mock.Anything, mock.Anything).Return(1, nil)
				d.repo.On("ListMembers", mock.Anything, mock.Anything).Return([]entities.OrgMemberInfo{
					{
						Name: "org",
						OrgMember: entities.OrgMember{
							BaseModel: entities.BaseModel{
								ID: userId,
							},
						},
					},
				}, nil)
			},
			expectErr: nil,
			output: &dto.ListOrgMembersOutput{
				Data: &[]entities.OrgMemberInfo{
					{
						Name: "org",
						OrgMember: entities.OrgMember{
							BaseModel: entities.BaseModel{
								ID: userId,
							},
						},
					},
				},
				Pagination: &dto.Pagination{
					Total: 1,
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				config: &configs.GlobalConfig{Gitlab: &configs.GitlabConfig{
					VolvoApiServerHost: "http://localhost",
				}},
				repo: &repository_mocks.MockRepository{},
			}
			tt.mockFn(d)

			u := organization.New(d.config, d.repo, nil, nil, nil)
			output, err := u.ListOrganizationMembers(tt.ctx, tt.input)
			require.Equal(t, tt.expectErr, err)
			require.Equal(t, tt.output, output)
		})
	}
}

func TestRemoveMemberInOrganization(t *testing.T) {
	userId := uuid.New()
	orgId := uuid.New()
	type dependencies struct {
		config       *configs.GlobalConfig
		repo         *repository_mocks.MockRepository
		gitlabClient *gitlab_mocks.MockGitlabClient
	}

	tests := []struct {
		name      string
		ctx       context.Context
		input     dto.RemoveMemberOrganizaionInput
		mockFn    func(d *dependencies)
		expectErr error
	}{
		{
			name: "should return error when repository.FindUserByID return error",
			ctx:  context.Background(),
			input: dto.RemoveMemberOrganizaionInput{
				OrgId:    orgId,
				MemberId: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return error when repository.FindUserByID return gorm.ErrRecordNotFound",
			ctx:  context.Background(),
			input: dto.RemoveMemberOrganizaionInput{
				OrgId:    orgId,
				MemberId: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expectErr: usecase.ErrUserNotFound,
		},
		{
			name: "should return error when repository.FindOrganizationByID return error",
			ctx:  context.Background(),
			input: dto.RemoveMemberOrganizaionInput{
				OrgId:    orgId,
				MemberId: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindOrganizationByID", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return error when repository.FindOrganizationByID return gorm.ErrRecordNotFound",
			ctx:  context.Background(),
			input: dto.RemoveMemberOrganizaionInput{
				OrgId:    orgId,
				MemberId: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindOrganizationByID", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expectErr: usecase.ErrOrganizationNotFound,
		},
		{
			name: "should return error when user have no permission return no permission",
			ctx:  context.Background(),
			input: dto.RemoveMemberOrganizaionInput{
				OrgId:    orgId,
				MemberId: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindOrganizationByID", mock.Anything, mock.Anything).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Developer}, nil)
			},
			expectErr: usecase.ErrNoPermission,
		},
		{
			name: "should return error when DeleteOrgMemberByID return error",
			ctx:  context.Background(),
			input: dto.RemoveMemberOrganizaionInput{
				OrgId:    orgId,
				MemberId: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindOrganizationByID", mock.Anything, mock.Anything).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil)
				d.repo.On("FindOrgGitGroup", mock.Anything, mock.Anything).Return(&entities.DefaultGitGroup{}, nil)
				d.gitlabClient.On("GetMemberOfGroup", mock.Anything, mock.Anything).Return(&gitlab.GetMemberOfGroupResponse{}, nil)
				d.gitlabClient.On("RemoveUserFromGroup", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("DeleteOrgMemberByID", mock.Anything, mock.Anything).Return(errors.New("error"))
			},
			expectErr: errors.New("error"),
		},

		{
			name: "success",
			ctx:  context.Background(),
			input: dto.RemoveMemberOrganizaionInput{
				OrgId:    orgId,
				MemberId: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindOrganizationByID", mock.Anything, mock.Anything).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil)
				d.repo.On("FindOrgGitGroup", mock.Anything, mock.Anything).Return(&entities.DefaultGitGroup{}, nil)
				d.gitlabClient.On("GetMemberOfGroup", mock.Anything, mock.Anything).Return(&gitlab.GetMemberOfGroupResponse{}, nil)
				d.gitlabClient.On("RemoveUserFromGroup", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("DeleteOrgMemberByID", mock.Anything, mock.Anything).Return(nil)
			},
			expectErr: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				config: &configs.GlobalConfig{Gitlab: &configs.GitlabConfig{
					VolvoApiServerHost: "http://localhost",
				}},
				repo:         &repository_mocks.MockRepository{},
				gitlabClient: &gitlab_mocks.MockGitlabClient{},
			}
			tt.mockFn(d)

			u := organization.New(d.config, d.repo, d.gitlabClient, nil, nil)
			err := u.RemoveMember(tt.ctx, tt.input)
			require.Equal(t, tt.expectErr, err)
		})
	}
}

func TestUpdateMemberInOrganization(t *testing.T) {
	userId := uuid.New()
	orgId := uuid.New()
	now, _ := time.Parse(time.RFC3339, "2025-12-30T15:04:05Z")
	type dependencies struct {
		repo         *repository_mocks.MockRepository
		gitlabClient *gitlab_mocks.MockGitlabClient
	}

	tests := []struct {
		name      string
		ctx       context.Context
		input     dto.UpdateMemberOrganizaionInput
		mockFn    func(d *dependencies)
		expectErr error
	}{
		{
			name: "should return error when repository.FindUserByID return error",
			ctx:  context.Background(),
			input: dto.UpdateMemberOrganizaionInput{
				OrgId:    orgId,
				MemberId: userId,
				ExpireAt: &now,
				Role:     enums.OrgRole_Developer,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return error when repository.FindUserByID return gorm.ErrRecordNotFound",
			ctx:  context.Background(),
			input: dto.UpdateMemberOrganizaionInput{
				OrgId:    orgId,
				MemberId: userId,
				ExpireAt: &now,
				Role:     enums.OrgRole_Developer,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expectErr: usecase.ErrUserNotFound,
		},
		{
			name: "should return error when repository.FindOrganizationByID return error",
			ctx:  context.Background(),
			input: dto.UpdateMemberOrganizaionInput{
				OrgId:    orgId,
				MemberId: userId,
				ExpireAt: &now,
				Role:     enums.OrgRole_Developer,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindOrganizationByID", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return error when repository.FindOrganizationByID return gorm.ErrRecordNotFound",
			ctx:  context.Background(),
			input: dto.UpdateMemberOrganizaionInput{
				OrgId:    orgId,
				MemberId: userId,
				ExpireAt: &now,
				Role:     enums.OrgRole_Developer,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindOrganizationByID", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expectErr: usecase.ErrOrganizationNotFound,
		},
		{
			name: "should return error when user have no permission return no permission",
			ctx:  context.Background(),
			input: dto.UpdateMemberOrganizaionInput{
				OrgId:    orgId,
				MemberId: userId,
				ExpireAt: &now,
				Role:     enums.OrgRole_Developer,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindOrganizationByID", mock.Anything, mock.Anything).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Developer}, nil)
			},
			expectErr: usecase.ErrNoPermission,
		},
		{
			name: "should return error when EditUserInGroup return error",
			ctx:  context.Background(),
			input: dto.UpdateMemberOrganizaionInput{
				OrgId:    orgId,
				MemberId: userId,
				ExpireAt: &now,
				Role:     enums.OrgRole_Developer,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindOrganizationByID", mock.Anything, mock.Anything).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil)
				d.repo.On("FindOrgGitGroup", mock.Anything, mock.Anything).Return(&entities.DefaultGitGroup{}, nil)
				d.gitlabClient.On("GetMemberOfGroup", mock.Anything, mock.Anything).Return(&gitlab.GetMemberOfGroupResponse{}, nil)
				d.gitlabClient.On("EditUserInGroup", mock.Anything, mock.Anything).Return(&gitlab.EditUserInGroupResponse{}, nil)
				d.repo.On("Save", mock.Anything, mock.Anything).Return(errors.New("error"))
			},
			expectErr: errors.New("error"),
		},

		{
			name: "success",
			ctx:  context.Background(),
			input: dto.UpdateMemberOrganizaionInput{
				OrgId:    orgId,
				MemberId: userId,
				ExpireAt: &now,
				Role:     enums.OrgRole_Developer,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindOrganizationByID", mock.Anything, mock.Anything).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil)
				d.repo.On("FindOrgGitGroup", mock.Anything, mock.Anything).Return(&entities.DefaultGitGroup{}, nil)
				d.gitlabClient.On("GetMemberOfGroup", mock.Anything, mock.Anything).Return(&gitlab.GetMemberOfGroupResponse{}, nil)
				d.gitlabClient.On("EditUserInGroup", mock.Anything, mock.Anything).Return(&gitlab.EditUserInGroupResponse{}, nil)
				d.repo.On("Save", mock.Anything, mock.Anything).Return(nil)
			},
			expectErr: nil,
		},
		{
			name: "success without expire time",
			ctx:  context.Background(),
			input: dto.UpdateMemberOrganizaionInput{
				OrgId:    orgId,
				MemberId: userId,
				Role:     enums.OrgRole_Developer,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindOrganizationByID", mock.Anything, mock.Anything).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil)
				d.repo.On("FindOrgGitGroup", mock.Anything, mock.Anything).Return(&entities.DefaultGitGroup{}, nil)
				d.gitlabClient.On("GetMemberOfGroup", mock.Anything, mock.Anything).Return(&gitlab.GetMemberOfGroupResponse{}, nil)
				d.gitlabClient.On("EditUserInGroup", mock.Anything, mock.Anything).Return(&gitlab.EditUserInGroupResponse{}, nil)
				d.repo.On("Save", mock.Anything, mock.Anything).Return(nil)
			},
			expectErr: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				repo:         &repository_mocks.MockRepository{},
				gitlabClient: &gitlab_mocks.MockGitlabClient{},
			}
			tt.mockFn(d)

			u := organization.New(nil, d.repo, d.gitlabClient, nil, nil)
			err := u.UpdateMember(tt.ctx, tt.input)
			require.Equal(t, tt.expectErr, err)
		})
	}
}

func TestUploadAvatarOrganization(t *testing.T) {
	type dependencies struct {
		repo         *repository_mocks.MockRepository
		gitlabClient *gitlab_mocks.MockGitlabClient
		awsClient    *aws_mocks.MockAWSClient
	}

	createMockFileHeader := func(fieldName, fileName, fileContent string) *multipart.FileHeader {
		var buffer bytes.Buffer
		writer := multipart.NewWriter(&buffer)

		part, _ := writer.CreateFormFile(fieldName, fileName)
		io.Copy(part, strings.NewReader(fileContent))

		writer.Close()

		reader := multipart.NewReader(&buffer, writer.Boundary())
		form, _ := reader.ReadForm(1024)

		return form.File[fieldName][0]
	}

	orgId := uuid.New()

	mockFileHeader := createMockFileHeader("avatar", "mockfile.txt", "This is a mock file content")

	tests := []struct {
		name     string
		ctx      context.Context
		mockFn   func(d *dependencies)
		input    dto.UploadOrganizationAvatarInput
		expError error
	}{
		{
			name: "should return error if FindUserByID fail",
			ctx:  context.TODO(),
			input: dto.UploadOrganizationAvatarInput{
				OrgID:         orgId,
				CurrentUserId: uuid.New(),
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(nil, errors.New("errors"))
			},
			expError: errors.New("errors"),
		},
		{
			name: "should return error if FindUserByID not found",
			ctx:  context.TODO(),
			input: dto.UploadOrganizationAvatarInput{
				OrgID:         orgId,
				CurrentUserId: uuid.New(),
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expError: usecase.ErrUserNotFound,
		},
		{
			name: "should return error if FindOrganizationByID fail",
			ctx:  context.TODO(),
			input: dto.UploadOrganizationAvatarInput{
				OrgID:         orgId,
				CurrentUserId: uuid.New(),
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindOrganizationByID", mock.Anything, mock.Anything).Return(nil, errors.New("errors"))
			},
			expError: errors.New("errors"),
		},
		{
			name: "should return error if FindOrganizationByID not found",
			ctx:  context.TODO(),
			input: dto.UploadOrganizationAvatarInput{
				OrgID:         orgId,
				CurrentUserId: uuid.New(),
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindOrganizationByID", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expError: usecase.ErrOrganizationNotFound,
		},
		{
			name: "should return error if user have no permission return no permission",
			ctx:  context.TODO(),
			input: dto.UploadOrganizationAvatarInput{
				OrgID:         orgId,
				CurrentUserId: uuid.New(),
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{Role: enums.UserRole_User}, nil)
				d.repo.On("FindOrganizationByID", mock.Anything, mock.Anything).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Developer}, nil)

			},
			expError: usecase.ErrNoPermission,
		},
		{
			name: "should return error if UploadImage fail",
			ctx:  context.TODO(),
			input: dto.UploadOrganizationAvatarInput{
				OrgID:         orgId,
				CurrentUserId: uuid.New(),
				File:          mockFileHeader,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{Role: enums.UserRole_User}, nil)
				d.repo.On("FindOrganizationByID", mock.Anything, mock.Anything).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil)
				d.awsClient.On("UploadImage", mock.Anything, mock.Anything, mock.Anything).Return("", errors.New("errors"))
			},
			expError: errors.New("errors"),
		},
		{
			name: "should return error if Save fail",
			ctx:  context.TODO(),
			input: dto.UploadOrganizationAvatarInput{
				OrgID:         orgId,
				CurrentUserId: uuid.New(),
				File:          mockFileHeader,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{Role: enums.UserRole_User}, nil)
				d.repo.On("FindOrganizationByID", mock.Anything, mock.Anything).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil)
				d.awsClient.On("UploadImage", mock.Anything, mock.Anything, mock.Anything).Return("http://imageURL", nil)
				d.repo.On("Save", mock.Anything, mock.Anything).Return(errors.New("errors"))
			},
			expError: errors.New("errors"),
		},
		{
			name: "should success",
			ctx:  context.TODO(),
			input: dto.UploadOrganizationAvatarInput{
				OrgID:         orgId,
				CurrentUserId: uuid.New(),
				File:          mockFileHeader,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{Role: enums.UserRole_User}, nil)
				d.repo.On("FindOrganizationByID", mock.Anything, mock.Anything).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil)
				d.awsClient.On("UploadImage", mock.Anything, mock.Anything, mock.Anything).Return("http://imageURL", nil)
				d.repo.On("Save", mock.Anything, mock.Anything).Return(nil)
			},
		},
		{
			name: "should success when user is admin",
			ctx:  context.TODO(),
			input: dto.UploadOrganizationAvatarInput{
				OrgID:         orgId,
				CurrentUserId: uuid.New(),
				File:          mockFileHeader,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{Role: enums.UserRole_Admin}, nil)
				d.repo.On("FindOrganizationByID", mock.Anything, mock.Anything).Return(&entities.Organization{}, nil)
				d.awsClient.On("UploadImage", mock.Anything, mock.Anything, mock.Anything).Return("http://imageURL", nil)
				d.repo.On("Save", mock.Anything, mock.Anything).Return(nil)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				repo:         &repository_mocks.MockRepository{},
				gitlabClient: &gitlab_mocks.MockGitlabClient{},
				awsClient:    &aws_mocks.MockAWSClient{},
			}
			tt.mockFn(d)

			u := organization.New(nil, d.repo, d.gitlabClient, nil, d.awsClient)
			_, err := u.UploadAvatarOrganization(tt.ctx, tt.input)
			require.Equal(t, tt.expError, err)
		})
	}
}

func TestGetMemberInOrganization(t *testing.T) {
	userId := uuid.New()
	orgId := uuid.New()
	type dependencies struct {
		repo         *repository_mocks.MockRepository
		gitlabClient *gitlab_mocks.MockGitlabClient
	}

	tests := []struct {
		name      string
		ctx       context.Context
		input     dto.GetMemberOrganizaionInput
		mockFn    func(d *dependencies)
		expectErr error
		output    *entities.OrgMember
	}{
		{
			name: "should return error when repository.FindOrganizationByID return error",
			ctx:  context.Background(),
			input: dto.GetMemberOrganizaionInput{
				OrgId:    orgId,
				MemberId: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindOrganizationByID", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectErr: errors.New("error"),
			output:    nil,
		},
		{
			name: "should return error when repository.FindOrganizationByID return gorm.ErrRecordNotFound",
			ctx:  context.Background(),
			input: dto.GetMemberOrganizaionInput{
				OrgId:    orgId,
				MemberId: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindOrganizationByID", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expectErr: usecase.ErrOrganizationNotFound,
			output:    nil,
		},
		{
			name: "should return error when repository.FindUserByID return error",
			ctx:  context.Background(),
			input: dto.GetMemberOrganizaionInput{
				OrgId:    orgId,
				MemberId: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindOrganizationByID", mock.Anything, mock.Anything).Return(&entities.Organization{}, nil)
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectErr: errors.New("error"),
			output:    nil,
		},
		{
			name: "should return blank value when repository.FindUserByID return gorm.ErrRecordNotFound",
			ctx:  context.Background(),
			input: dto.GetMemberOrganizaionInput{
				OrgId:    orgId,
				MemberId: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindOrganizationByID", mock.Anything, mock.Anything).Return(&entities.Organization{}, nil)
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expectErr: nil,
			output:    &entities.OrgMember{},
		},
		{
			name: "should return blank value when member not exist in organization",
			ctx:  context.Background(),
			input: dto.GetMemberOrganizaionInput{
				OrgId:    orgId,
				MemberId: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindOrganizationByID", mock.Anything, mock.Anything).Return(&entities.Organization{}, nil)
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{}, nil)
			},
			expectErr: nil,
			output:    &entities.OrgMember{},
		},
		{
			name: "should return error when repository.FindOrgMember return unkown error",
			ctx:  context.Background(),
			input: dto.GetMemberOrganizaionInput{
				OrgId:    orgId,
				MemberId: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindOrganizationByID", mock.Anything, mock.Anything).Return(&entities.Organization{}, nil)
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectErr: errors.New("error"),
			output:    nil,
		},
		{
			name: "success",
			ctx:  context.Background(),
			input: dto.GetMemberOrganizaionInput{
				OrgId:    orgId,
				MemberId: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindOrganizationByID", mock.Anything, mock.Anything).Return(&entities.Organization{}, nil)
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil)
			},
			expectErr: nil,
			output:    &entities.OrgMember{Role: enums.OrgRole_Owner},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				repo:         &repository_mocks.MockRepository{},
				gitlabClient: &gitlab_mocks.MockGitlabClient{},
			}
			tt.mockFn(d)

			u := organization.New(nil, d.repo, d.gitlabClient, nil, nil)
			output, err := u.GetMember(tt.ctx, tt.input)
			require.Equal(t, tt.expectErr, err)
			require.Equal(t, tt.output, output)
		})
	}
}
func TestAuthorizeAccessToken(t *testing.T) {
	orgId := uuid.New()
	token := "token"
	type dependencies struct {
		repo *repository_mocks.MockRepository
	}

	tests := []struct {
		name        string
		ctx         context.Context
		accessToken string
		mockFn      func(d *dependencies)
		expectErr   error
		orgId       *uuid.UUID
	}{
		{
			name:        "should return error",
			ctx:         context.Background(),
			accessToken: token,
			mockFn: func(d *dependencies) {
				d.repo.On("FindOrgAccessToken", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expectErr: nil,
			orgId:     nil,
		},
		{
			name:        "should return error",
			ctx:         context.Background(),
			accessToken: token,
			mockFn: func(d *dependencies) {
				d.repo.On("FindOrgAccessToken", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectErr: usecase.ErrInternal,
			orgId:     nil,
		},
		{
			name:        "success",
			ctx:         context.Background(),
			accessToken: token,
			mockFn: func(d *dependencies) {
				d.repo.On("FindOrgAccessToken", mock.Anything, mock.Anything).Return(&entities.OrgToken{
					OrgID: orgId,
				}, nil)
			},
			expectErr: nil,
			orgId:     &orgId,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				repo: &repository_mocks.MockRepository{},
			}
			tt.mockFn(d)

			u := organization.New(nil, d.repo, nil, nil, nil)
			output, err := u.AuthorizeAccessToken(tt.ctx, tt.accessToken)
			require.Equal(t, tt.expectErr, err)
			require.Equal(t, tt.orgId, output)
		})
	}
}

func TestCheckRepositoryInOrg(t *testing.T) {
	orgId := uuid.New()
	repoType := enums.RepoType_Models
	namespace := "namespace"
	repoName := "repo-name"
	repoID := *types.NewRepoID(repoType, namespace, repoName)

	type dependencies struct {
		repo *repository_mocks.MockRepository
	}

	tests := []struct {
		name      string
		ctx       context.Context
		orgId     uuid.UUID
		repoID    types.RepoID
		mockFn    func(d *dependencies)
		expectErr error
		result    bool
	}{
		{
			name:   "should return error",
			ctx:    context.Background(),
			orgId:  orgId,
			repoID: repoID,
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expectErr: usecase.ErrRepositoryNotFound,
			result:    false,
		},
		{
			name:   "should return not found",
			ctx:    context.Background(),
			orgId:  orgId,
			repoID: repoID,
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expectErr: usecase.ErrRepositoryNotFound,
			result:    false,
		},
		{
			name:   "should return false",
			ctx:    context.Background(),
			orgId:  orgId,
			repoID: repoID,
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectErr: errors.New("error"),
			result:    false,
		},
		{
			name:   "should return false when orgId is not match",
			ctx:    context.Background(),
			orgId:  orgId,
			repoID: repoID,
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{}, nil)
			},
			expectErr: nil,
			result:    false,
		},
		{
			name:   "should success",
			ctx:    context.Background(),
			orgId:  orgId,
			repoID: repoID,
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					OrgID: &orgId,
				}, nil)
			},
			expectErr: nil,
			result:    true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				repo: &repository_mocks.MockRepository{},
			}
			tt.mockFn(d)

			u := organization.New(nil, d.repo, nil, nil, nil)
			output, err := u.CheckRepositoryInOrg(tt.ctx, tt.orgId, tt.repoID)
			require.Equal(t, tt.expectErr, err)
			require.Equal(t, tt.result, output)
		})
	}
}
