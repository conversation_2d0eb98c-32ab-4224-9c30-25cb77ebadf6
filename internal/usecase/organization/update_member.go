package organization

import (
	"api-server/internal/dto"
	"api-server/internal/enums"
	"api-server/internal/gateways/gitlab"
	repository "api-server/internal/repositories"
	"api-server/internal/usecase"
	"api-server/internal/utils"
	"api-server/pkg/oteltrace"
	"context"
	"errors"
	"time"

	"go.opentelemetry.io/otel/codes"
	"gorm.io/gorm"
)

// UpdateMember implements the OrganizationUsecase interface for updating member information.
// It handles member role updates and GitLab group member updates.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: Input data containing member update details including organization ID, user ID, and new role
//
// Returns:
//   - error: Any error that occurred during update
func (u *impl) UpdateMember(ctx context.Context, input dto.UpdateMemberOrganizaionInput) error {
	c, span := oteltrace.Tracer.Start(ctx, "usecase.organization.UpdateMember")
	defer span.End()

	if input.ExpireAt != nil && input.ExpireAt.Before(time.Now()) {
		return usecase.ErrTimeIsPast
	}

	currentUser, err := u.repository.FindUserByID(c, input.CurrentUserID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return usecase.ErrUserNotFound
		}
		span.SetStatus(codes.Error, "failed to find current user")
		span.RecordError(err)
		return err
	}

	member, err := u.repository.FindUserByID(c, input.MemberId)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return usecase.ErrUserNotFound
		}
		span.SetStatus(codes.Error, "failed to find member")
		span.RecordError(err)
		return err
	}

	org, err := u.repository.FindOrganizationByID(c, input.OrgId)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return usecase.ErrOrganizationNotFound
		}
		span.SetStatus(codes.Error, "failed to find organization")
		span.RecordError(err)
		return err
	}
	//get user permission accesstoken
	accessToken, err := getAccessToken(c, currentUser, *u, org, span)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get right access token")
		span.RecordError(err)
		return err
	}

	orgMember, err := u.repository.FindOrgMember(c, repository.FilterOrgMember{
		OrgID:  &org.ID,
		UserID: &member.ID,
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find organization member")
		span.RecordError(err)
		return err
	}

	orgGitGroup, err := u.repository.FindOrgGitGroup(c, org.ID)
	if err != nil {
		span.SetStatus(codes.Error, "failed to find git group")
		span.RecordError(err)
		return err
	}

	orgGitGroups := []int64{
		orgGitGroup.RefGitSpacesID,
		orgGitGroup.RefGitModelsID,
		orgGitGroup.RefGitDatasetsID,
	}

	role := gitlab.OrganizationAccessLevel_Developer
	if input.Role == enums.OrgRole_Owner {
		role = gitlab.OrganizationAccessLevel_Owner
	}

	for _, groupID := range orgGitGroups {
		gitlabMember, err := u.gitlabClient.GetMemberOfGroup(c, gitlab.GetMemberOfGroupRequest{
			GroupId: groupID,
			UserId:  member.RefGitUserID,
			Token:   accessToken,
		})
		if err != nil {
			span.SetStatus(codes.Error, "failed to get member in gitlab group")
			span.RecordError(err)
		}

		if gitlabMember != nil {
			_, err = u.gitlabClient.EditUserInGroup(c, gitlab.EditUserInGroupRequest{
				GroupId:     groupID,
				UserId:      member.RefGitUserID,
				AccessLevel: role,
				Token:       accessToken,
				ExpiresAt:   utils.ConvertTimeToString(input.ExpireAt),
			})
			if err != nil {
				span.SetStatus(codes.Error, "failed to edit user in gitlab group")
				span.RecordError(err)
				return err
			}
		}
	}

	orgMember.Role = input.Role
	orgMember.ExpiresAt = input.ExpireAt
	err = u.repository.Save(c, orgMember)
	if err != nil {
		span.SetStatus(codes.Error, "failed to save org member")
		span.RecordError(err)
		return err
	}

	span.AddEvent("member updated successfully")
	span.SetStatus(codes.Ok, "member updated successfully")
	return nil
}
