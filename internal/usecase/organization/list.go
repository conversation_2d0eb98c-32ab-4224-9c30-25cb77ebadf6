package organization

import (
	"api-server/internal/dto"
	"api-server/pkg/oteltrace"
	"context"

	"go.opentelemetry.io/otel/codes"
)

// ListOrganizations implements the OrganizationUsecase interface for retrieving multiple organizations.
// It supports pagination, filtering, and sorting of organization data.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: Input data containing list parameters including pagination and filtering options
//
// Returns:
//   - *dto.ListOrganizationsOutput: List of organization information with pagination metadata
//   - error: Any error that occurred during retrieval
func (u impl) ListOrganizations(ctx context.Context, input dto.ListOrganizationsInput) (*dto.ListOrganizationsOutput, error) {
	c, span := oteltrace.Tracer.Start(ctx, "usecase.organization.ListOrganizations")
	defer span.End()

	total, orgs, err := u.repository.ListOrganizations(c, input)
	if err != nil {
		span.SetStatus(codes.Error, "failed to list organizations")
		span.RecordError(err)
		return nil, err
	}

	var output dto.ListOrganizationsOutput

	var orgReposInfos []dto.OrgReposInfo

	for _, org := range orgs {
		repos, err := u.repository.ListRepositories(c, dto.GetRepositoriesInput{
			OrgID: org.ID.String(),
		})
		if err != nil {
			span.SetStatus(codes.Error, "failed to list repositories for organization")
			span.RecordError(err)
			return nil, err
		}
		// Pre-sign URL for repository avatars
		for i := 0; i < len(repos); i++ {
			if repos[i].Avatar != nil {
				repoImage, err := u.awsClient.GenPreSignUrl(c, *repos[i].Avatar)
				if err != nil {
					span.SetStatus(codes.Error, "failed to generate pre-signed URL for repository avatar")
					span.RecordError(err)
					return nil, err
				}
				repos[i].Avatar = &repoImage
			}

			if repos[i].User != nil && repos[i].User.Avatar != nil {
				repoImage, err := u.awsClient.GenPreSignUrl(c, *repos[i].User.Avatar)
				if err != nil {
					span.SetStatus(codes.Error, "failed to generate pre-signed URL for user avatar")
					span.RecordError(err)
					return nil, err
				}
				repos[i].User.Avatar = &repoImage
			}

			if repos[i].Org != nil && repos[i].Org.Avatar != nil {
				repoImage, err := u.awsClient.GenPreSignUrl(c, *repos[i].Org.Avatar)
				if err != nil {
					span.SetStatus(codes.Error, "failed to generate pre-signed URL for organization avatar")
					span.RecordError(err)
					return nil, err
				}
				repos[i].Org.Avatar = &repoImage
			}
		}

		// Pre-sign URL for organization avatar
		if org.Avatar != nil {
			orgImage, err := u.awsClient.GenPreSignUrl(c, *org.Avatar)
			if err != nil {
				span.SetStatus(codes.Error, "failed to generate pre-signed URL for organization avatar")
				span.RecordError(err)
				return nil, err
			}
			org.Avatar = &orgImage
		}

		orgReposInfos = append(orgReposInfos, dto.OrgReposInfo{
			Org:          org,
			Repositories: repos,
		})
	}
	output.Data = &orgReposInfos
	output.Pagination = &dto.Pagination{
		Total:    total,
		PageNo:   input.Paginate.Page,
		PageSize: input.Paginate.PerPage,
	}

	span.AddEvent("successfully retrieved organizations and repositories")
	span.SetStatus(codes.Ok, "successfully retrieved organizations and repositories")
	return &output, nil
}
