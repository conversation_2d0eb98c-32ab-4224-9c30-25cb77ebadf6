package organization

import (
	"context"
	"errors"

	"go.opentelemetry.io/otel/codes"
	"gorm.io/gorm"

	"api-server/internal/dto"
	"api-server/internal/entities"
	"api-server/internal/gateways/gitlab"

	"api-server/internal/usecase"
	"api-server/pkg/oteltrace"
)

// DeleteOrganization implements the OrganizationUsecase interface for removing an organization.
// It handles the cleanup of all organization resources including:
// - Organization members
// - Repositories and their members
// - Deployments
// - Projects
// - GitLab groups
//
// Parameters:
//   - ctx: Context for the operation
//   - input: Input data containing organization ID and current user ID
//
// Returns:
//   - error: Any error that occurred during deletion
func (u impl) DeleteOrganization(ctx context.Context, input dto.DeleteOrganizationInput) error {
	c, span := oteltrace.Tracer.Start(ctx, "usecase.organization.DeleteOrganization")
	defer span.End()

	return u.repository.Transaction(c, func(ctx context.Context) error {
		currentUser, err := u.repository.FindUserByID(c, input.CurrentUserId)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				span.SetStatus(codes.Error, "user not found")
				span.RecordError(err)
				return usecase.ErrRepositoryNotFound
			}

			span.SetStatus(codes.Error, "failed to find user")
			span.RecordError(err)
			return err
		}
		// Find the organization
		organization, err := u.repository.FindOrganizationByID(c, input.Id)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				span.SetStatus(codes.Error, "organization not found")
				span.RecordError(err)
				return dto.NewNotFoundError(err)
			}

			span.SetStatus(codes.Error, "failed to find organization")
			span.RecordError(err)
			return err
		}

		//get user permission accesstoken
		token, err := getAccessToken(c, currentUser, u, organization, span)
		if err != nil {
			span.SetStatus(codes.Error, "failed to get right access token")
			span.RecordError(err)
			return err
		}

		// Delete all members of the organization
		if err := u.repository.DeleteMembersByOrganizationID(c, input.Id); err != nil {
			span.SetStatus(codes.Error, "failed to delete members")
			span.RecordError(err)
			return err
		}
		//Delete the organization's repositories
		repos, err := u.repository.ListRepositories(c, dto.GetRepositoriesInput{
			OrgID: input.Id.String(),
		})
		if err != nil {
			span.SetStatus(codes.Error, "failed to list repositories")
			span.RecordError(err)
			return err
		}

		for _, repo := range repos {
			if err := u.gitlabClient.DeleteProject(c, gitlab.DeleteProjectRequest{
				Id:    repo.RefGitRepoID,
				Token: token,
			}); err != nil {
				span.SetStatus(codes.Error, "failed to delete git project")
				span.RecordError(err)
				return err
			}

			if err := u.repository.DeleteRepositoryMemberByRepositoryID(c, repo.ID); err != nil {
				span.SetStatus(codes.Error, "failed to delete repository members")
				span.RecordError(err)
				return err
			}
			//delete repo's deployments
			if err := u.repository.DeleteDeploymentsByRepoID(c, repo.ID); err != nil {
				span.SetStatus(codes.Error, "failed to delete deployments")
				span.RecordError(err)
				return err
			}
		}
		// Delete all projects of the organization
		if err := u.repository.DeleteProjectsByOrganizationID(c, input.Id); err != nil {
			span.SetStatus(codes.Error, "failed to delete projects")
			span.RecordError(err)
			return err
		}
		//delete org git groups
		orgGitGroup, err := u.repository.FindOrgGitGroup(c, organization.ID)
		if err != nil {
			span.SetStatus(codes.Error, "failed to find org git group")
			span.RecordError(err)
			return err
		}

		orgGitGroups := []int64{
			orgGitGroup.RefGitSpacesID,
			orgGitGroup.RefGitModelsID,
			orgGitGroup.RefGitDatasetsID,
		}
		//delete gitlab groups
		for _, groupID := range orgGitGroups {
			if err := u.gitlabClient.DeleteGroup(c, gitlab.DeleteOrganizationRequest{
				Id:    int(groupID),
				Token: token,
			}); err != nil {
				span.SetStatus(codes.Error, "failed to delete git group")
				span.RecordError(err)
				return err
			}
		}

		if err := u.repository.DeleteOrgGitGroup(c, organization.ID); err != nil {
			span.SetStatus(codes.Error, "failed to delete org git group")
			span.RecordError(err)
			return err
		}
		// Delete the organization
		if err := u.repository.DeleteById(c, entities.Organization{}, organization.ID); err != nil {
			span.SetStatus(codes.Error, "failed to delete organization")
			span.RecordError(err)
			return err
		}

		span.AddEvent("organization deleted successfully")
		span.SetStatus(codes.Ok, "organization deleted successfully")
		return nil
	})
}
