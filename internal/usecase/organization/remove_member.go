package organization

import (
	"api-server/internal/dto"
	"api-server/internal/gateways/gitlab"
	repository "api-server/internal/repositories"
	"api-server/internal/usecase"
	"api-server/pkg/oteltrace"
	"context"
	"errors"

	"go.opentelemetry.io/otel/codes"
	"gorm.io/gorm"
)

// RemoveMember implements the OrganizationUsecase interface for removing a member from an organization.
// It handles the removal process including GitLab group member removal.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: Input data containing member removal details including organization ID and user ID
//
// Returns:
//   - error: Any error that occurred during removal
func (u *impl) RemoveMember(ctx context.Context, input dto.RemoveMemberOrganizaionInput) error {
	c, span := oteltrace.Tracer.Start(ctx, "usecase.organization.RemoveMember")
	defer span.End()

	currentUser, err := u.repository.FindUserByID(c, input.CurrentUserID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return usecase.ErrUserNotFound
		}
		span.SetStatus(codes.Error, "failed to find current user")
		span.RecordError(err)
		return err
	}

	member, err := u.repository.FindUserByID(c, input.MemberId)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return usecase.ErrUserNotFound
		}
		span.SetStatus(codes.Error, "failed to find member user")
		span.RecordError(err)
		return err
	}

	org, err := u.repository.FindOrganizationByID(c, input.OrgId)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return usecase.ErrOrganizationNotFound
		}
		span.SetStatus(codes.Error, "failed to find organization")
		span.RecordError(err)
		return err
	}

	//get user permission accesstoken
	accessToken, err := getAccessToken(c, currentUser, *u, org, span)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get right access token")
		span.RecordError(err)
		return err
	}

	orgMember, err := u.repository.FindOrgMember(c, repository.FilterOrgMember{
		OrgID:  &org.ID,
		UserID: &member.ID,
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find org member")
		span.RecordError(err)
		return err
	}

	orgGitGroup, err := u.repository.FindOrgGitGroup(c, org.ID)
	if err != nil {
		span.SetStatus(codes.Error, "failed to find organization git group")
		span.RecordError(err)
		return err
	}

	orgGitGroups := []int64{
		orgGitGroup.RefGitSpacesID,
		orgGitGroup.RefGitModelsID,
		orgGitGroup.RefGitDatasetsID,
	}

	// Remove from groups
	for _, groupID := range orgGitGroups {
		gitlabMember, err := u.gitlabClient.GetMemberOfGroup(ctx, gitlab.GetMemberOfGroupRequest{
			GroupId: groupID,
			UserId:  member.RefGitUserID,
			Token:   accessToken,
		})
		if err != nil {
			span.SetStatus(codes.Error, "failed to get member in gitlab group")
			span.RecordError(err)
		}
		if gitlabMember != nil {
			if err = u.gitlabClient.RemoveUserFromGroup(c, gitlab.RemoveUserFromGroupRequest{
				GroupId: groupID,
				UserId:  member.RefGitUserID,
				Token:   accessToken,
			}); err != nil {
				span.SetStatus(codes.Error, "failed to remove user from gitlab group")
				span.RecordError(err)
				return err
			}
		}

	}

	if err := u.repository.DeleteOrgMemberByID(c, orgMember.ID); err != nil {
		span.SetStatus(codes.Error, "failed to delete org member")
		span.RecordError(err)
		return err
	}

	span.AddEvent("successfully removed member from organization")
	span.SetStatus(codes.Ok, "successfully removed member from organization")
	return nil
}
