package organization

import (
	"context"

	"github.com/google/uuid"

	"api-server/configs"
	"api-server/internal/dto"
	"api-server/internal/entities"
	"api-server/internal/gateways/aws"
	"api-server/internal/gateways/gitlab"
	"api-server/internal/gateways/mail"
	repository "api-server/internal/repositories"
	"api-server/internal/types"
)

// OrganizationUsecase defines the interface for organization-related operations.
// It provides methods for managing organizations, their members, and avatars.
type OrganizationUsecase interface {
	// CreateOrganization creates a new organization in the system.
	// This function handles the creation of an organization with the specified details
	// and sets up necessary resources and permissions.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - input: The input data for creating the organization
	//
	// Returns:
	//   - *dto.CreateOrganizationOutput: The created organization information
	//   - error: Any error that occurred during creation
	CreateOrganization(ctx context.Context, input dto.CreateOrganizationInput) (*dto.CreateOrganizationOutput, error)

	// DeleteOrganization permanently removes an organization from the system.
	// This function handles the cleanup of all organization resources and permissions.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - input: The input data for deleting the organization
	//
	// Returns:
	//   - error: Any error that occurred during deletion
	DeleteOrganization(ctx context.Context, input dto.DeleteOrganizationInput) error

	// InviteUser invites a user to join an organization.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - input: The input data for inviting the user
	//
	// Returns:
	//   - error: Any error that occurred during invitation
	InviteUser(ctx context.Context, input dto.InviteOrgMemberInput) error

	// InviteMultipleOrgMembers invites multiple users to join an organization.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - input: The input data for inviting the users
	//
	// Returns:
	//   - error: Any error that occurred during invitation
	InviteMultipleOrgMembers(ctx context.Context, input dto.InviteOrgMembersInput) error

	// FindOrganizationByID retrieves information about a specific organization.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - input: The input data for finding the organization
	//
	// Returns:
	//   - *dto.FindOrganizationOutput: The organization information
	//   - error: Any error that occurred during retrieval
	FindOrganizationByID(ctx context.Context, input dto.FindOrganizationInput) (*dto.FindOrganizationOutput, error)

	// ListOrganizations retrieves a list of organizations based on input criteria.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - input: The input data for retrieving organizations
	//
	// Returns:
	//   - *dto.ListOrganizationsOutput: The list of organization information
	//   - error: Any error that occurred during retrieval
	ListOrganizations(ctx context.Context, input dto.ListOrganizationsInput) (*dto.ListOrganizationsOutput, error)

	// ListOrganizationMembers retrieves a list of members for an organization.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - input: The input data for retrieving members
	//
	// Returns:
	//   - *dto.ListOrgMembersOutput: The list of member information
	//   - error: Any error that occurred during retrieval
	ListOrganizationMembers(ctx context.Context, input dto.ListOrgMembersInput) (*dto.ListOrgMembersOutput, error)

	// RemoveMember removes a member from an organization.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - input: The input data for removing the member
	//
	// Returns:
	//   - error: Any error that occurred during removal
	RemoveMember(ctx context.Context, input dto.RemoveMemberOrganizaionInput) error

	// UpdateMember updates an organization member's information.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - input: The input data for updating the member
	//
	// Returns:
	//   - error: Any error that occurred during update
	UpdateMember(ctx context.Context, input dto.UpdateMemberOrganizaionInput) error

	// UploadAvatarOrganization uploads an avatar for an organization.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - input: The input data for uploading the avatar
	//
	// Returns:
	//   - string: The URL of the uploaded avatar
	//   - error: Any error that occurred during upload
	UploadAvatarOrganization(ctx context.Context, input dto.UploadOrganizationAvatarInput) (string, error)

	// DeleteAvatarOrganization deletes an organization's avatar.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - input: The input data for deleting the avatar
	//
	// Returns:
	//   - error: Any error that occurred during deletion
	DeleteAvatarOrganization(ctx context.Context, input dto.DeleteOrganizationAvatarInput) error

	// GetMember retrieves information about a specific organization member.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - input: The input data for retrieving member info
	//
	// Returns:
	//   - *entities.OrgMember: The member information
	//   - error: Any error that occurred during retrieval
	GetMember(ctx context.Context, input dto.GetMemberOrganizaionInput) (*entities.OrgMember, error)

	// AuthorizeAccessToken verifies if an access token is valid and returns the associated organization ID.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - accessToken: The access token to verify
	//
	// Returns:
	//   - *uuid.UUID: The ID of the organization associated with the token, if valid
	//   - error: Any error that occurred during verification
	AuthorizeAccessToken(ctx context.Context, accessToken string) (*uuid.UUID, error)

	// CheckRepositoryInOrg verifies if a repository belongs to a specific organization.
	// It checks if the repository exists and if its organization ID matches the provided orgId.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - orgId: UUID of the organization to check against
	//   - repoID: Repository identifier containing type, namespace and name
	//
	// Returns:
	//   - bool: true if the repository belongs to the organization, false otherwise
	//   - error: Any error that occurred during the check, including:
	//   - ErrRepositoryNotFound if the repository doesn't exist
	//   - Other database or internal errors
	CheckRepositoryInOrg(ctx context.Context, orgId uuid.UUID, repoID types.RepoID) (bool, error)
}

var _ OrganizationUsecase = (*impl)(nil)

type impl struct {
	config       *configs.GlobalConfig
	repository   repository.Repository
	gitlabClient gitlab.GitlabClient
	mailClient   mail.MailClient
	awsClient    aws.AWSClient
}

// New creates a new instance of the organization usecase implementation.
// It initializes the usecase with the required dependencies including configuration,
// repository, GitLab client, mail client, and AWS client.
//
// Parameters:
//   - config: Global application configuration
//   - repository: Repository interface for data access
//   - gitlabClient: Client for GitLab API interactions
//   - mailClient: Client for sending emails
//   - awsClient: Client for AWS services (e.g., S3 for avatars)
//
// Returns:
//   - *impl: New instance of the organization usecase
func New(
	config *configs.GlobalConfig,
	repository repository.Repository,
	gitlabClient gitlab.GitlabClient,
	mailClient mail.MailClient,
	awsClient aws.AWSClient,
) *impl {
	return &impl{
		config:       config,
		repository:   repository,
		gitlabClient: gitlabClient,
		mailClient:   mailClient,
		awsClient:    awsClient,
	}
}
