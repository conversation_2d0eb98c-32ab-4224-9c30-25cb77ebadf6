package organization

import (
	"context"
	"errors"

	repository "api-server/internal/repositories"
	"api-server/internal/types"
	"api-server/internal/usecase"
	"api-server/pkg/oteltrace"

	"github.com/google/uuid"
	"go.opentelemetry.io/otel/codes"
	"gorm.io/gorm"
)

// AuthorizeAccessToken validates an organization-specific access token.
// It checks if the token exists, is not revoked, and returns the associated organization ID.
// If the token is not found, it returns nil for both organization ID and error, indicating
// the token is simply not an organization token, rather than an error condition.
//
// Parameters:
//   - ctx: The context for the operation.
//   - accessToken: The organization access token string to validate.
//
// Returns:
//   - *uuid.UUID: A pointer to the organization ID if the token is valid, otherwise nil.
//   - error: An error if an internal issue occurs during token lookup, otherwise nil.
func (i impl) AuthorizeAccessToken(ctx context.Context, accessToken string) (*uuid.UUID, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.organization.AuthorizeOrgAccessToken")
	defer span.End()

	token, err := i.repository.FindOrgAccessToken(ctx, repository.FindOrgAccessTokenQuery{
		OrgAccessToken: accessToken,
		Revoked:        false,
	})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			span.AddEvent("org access token not found")
			span.SetStatus(codes.Ok, "org access token not found")
			return nil, nil
		}
		span.SetStatus(codes.Error, "failed to find org access token")
		span.RecordError(err)
		return nil, usecase.ErrInternal
	}

	orgId := &token.OrgID
	span.AddEvent("org access token authorization checked")
	span.SetStatus(codes.Ok, "org access token authorization checked")
	return orgId, nil
}

// CheckRepositoryInOrg verifies if a repository belongs to a specific organization.
// It checks if the repository exists and if its organization ID matches the provided orgId.
//
// Parameters:
//   - ctx: Context for the operation
//   - orgId: UUID of the organization to check against
//   - repoID: Repository identifier containing type, namespace and name
//
// Returns:
//   - bool: true if the repository belongs to the organization, false otherwise
//   - error: Any error that occurred during the check, including:
//   - ErrRepositoryNotFound if the repository doesn't exist
//   - Other database or internal errors
func (i impl) CheckRepositoryInOrg(ctx context.Context, orgId uuid.UUID, repoID types.RepoID) (bool, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.organization.CheckRepositoryInOrg")
	defer span.End()

	repo, err := i.repository.FindRepository(ctx, repository.RepositoryFilter{
		Type:      repoID.RepoType(),
		Namespace: repoID.Namespace(),
		Name:      repoID.RepoName(),
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find repository")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return false, usecase.ErrRepositoryNotFound
		}
		return false, err
	}

	span.AddEvent("checked org access token authorization")
	span.SetStatus(codes.Ok, "checked org access token authorization")
	if repo.OrgID != nil && *repo.OrgID == orgId {
		return true, nil
	}

	return false, nil
}
