package organization

import (
	"api-server/internal/dto"
	"api-server/internal/entities"
	repository "api-server/internal/repositories"
	"api-server/internal/usecase"
	"api-server/pkg/oteltrace"
	"api-server/pkg/otelzap"
	"context"
	"errors"

	"go.opentelemetry.io/otel/codes"
	"gorm.io/gorm"
)

// GetMember implements the OrganizationUsecase interface for retrieving member information.
// It fetches detailed information about a specific organization member.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: Input data containing member retrieval details including organization ID and user ID
//
// Returns:
//   - *entities.OrgMember: Member information including role and user details
//   - error: Any error that occurred during retrieval
func (u impl) GetMember(ctx context.Context, input dto.GetMemberOrganizaionInput) (*entities.OrgMember, error) {
	c, span := oteltrace.Tracer.Start(ctx, "usecase.organization.GetMember")
	defer span.End()

	org, err := u.repository.FindOrganizationByID(c, input.OrgId)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			span.SetStatus(codes.Error, err.Error())
			span.RecordError(err)
			otelzap.ErrorWithContext(c, "organization not found", err)
			return nil, usecase.ErrOrganizationNotFound
		}

		span.SetStatus(codes.Error, err.Error())
		span.RecordError(err)
		otelzap.ErrorWithContext(c, "failed to find organization", err)
		return nil, err
	}

	member, err := u.repository.FindUserByID(c, input.MemberId)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			span.SetStatus(codes.Error, err.Error())
			span.RecordError(err)
			otelzap.ErrorWithContext(c, "member not found", err)
			return &entities.OrgMember{}, nil //  return blank value if user is not exist
		}

		span.SetStatus(codes.Error, err.Error())
		span.RecordError(err)
		otelzap.ErrorWithContext(c, "failed to find member", err)
		return nil, err
	}

	orgMember, err := u.repository.FindOrgMember(c, repository.FilterOrgMember{
		OrgID:  &org.ID,
		UserID: &member.ID,
	})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			span.SetStatus(codes.Error, err.Error())
			span.RecordError(err)
			otelzap.ErrorWithContext(c, "org member not found", err)
			return &entities.OrgMember{}, nil // return blank value if user is not in organization
		}

		span.SetStatus(codes.Error, err.Error())
		span.RecordError(err)
		otelzap.ErrorWithContext(c, "failed to find org member", err)
		return nil, err
	}

	otelzap.InfoWithContext(c, "member retrieved successfully")
	return orgMember, nil
}
