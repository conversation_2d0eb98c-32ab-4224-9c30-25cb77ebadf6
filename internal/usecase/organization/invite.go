package organization

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/google/uuid"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
	"gorm.io/gorm"

	"api-server/internal/dto"
	"api-server/internal/entities"
	"api-server/internal/enums"
	"api-server/internal/gateways/gitlab"
	"api-server/internal/gateways/mail"
	repository "api-server/internal/repositories"
	"api-server/internal/usecase"
	"api-server/internal/utils"
	"api-server/pkg/oteltrace"
)

// InviteUser implements the OrganizationUsecase interface for inviting a user to an organization.
// It handles the invitation process including email notifications and member setup.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: Input data containing invitation details including email and role
//
// Returns:
//   - error: Any error that occurred during invitation
func (u impl) InviteUser(ctx context.Context, input dto.InviteOrgMemberInput) error {
	c, span := oteltrace.Tracer.Start(ctx, "usecase.organization.InviteUser")
	defer span.End()

	currentUser, err := u.repository.FindUserByID(c, input.CurrentUserID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return usecase.ErrUserNotFound
		}
		span.SetStatus(codes.Error, "failed to find current user")
		span.RecordError(err)
		return err
	}

	if input.ExpireAt != nil && input.ExpireAt.Before(time.Now()) {
		err := errors.New("expire time is in the past")
		span.SetStatus(codes.Error, err.Error())
		span.RecordError(err)
		return usecase.ErrTimeIsPast
	}
	// query organization by id
	org, err := u.repository.FindOrganizationByID(c, input.OrgId)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return usecase.ErrOrganizationNotFound
		}
		span.SetStatus(codes.Error, "failed to find organization")
		span.RecordError(err)
		return err
	}

	//get user permission accesstoken
	accessToken, err := getAccessToken(c, currentUser, u, org, span)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get right access token")
		span.RecordError(err)
		return err
	}
	// query user by id
	user, err := u.repository.FindUserByID(c, input.UserId)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return usecase.ErrUserNotFound
		}
		span.SetStatus(codes.Error, "failed to find user")
		span.RecordError(err)
		return err
	}
	//is admin
	if user.RefGitUserID == enums.AdminRefGitID {
		return usecase.ErrUserHasBeenInvited
	}

	err = checkMemberExistInOrg(ctx, u, span, org.ID, input.UserId)
	if err != nil {
		return err
	}
	//invite member
	orgGitGroup, err := u.repository.FindOrgGitGroup(c, org.ID)
	if err != nil {
		span.SetStatus(codes.Error, "failed to find GitLab organization group")
		span.RecordError(err)
		return err
	}
	orgGitGroups := []int64{
		orgGitGroup.RefGitSpacesID,
		orgGitGroup.RefGitModelsID,
		orgGitGroup.RefGitDatasetsID,
	}

	role := gitlab.OrganizationAccessLevel_Developer
	if input.Role == enums.OrgRole_Owner {
		role = gitlab.OrganizationAccessLevel_Owner
	}

	err = u.repository.Transaction(ctx, func(ctx context.Context) error {
		// create organization member
		if err := u.repository.Create(c, &entities.OrgMember{
			User:      user,
			Org:       org,
			Role:      input.Role,
			ExpiresAt: input.ExpireAt,
			UserID:    user.ID,
			OrgID:     org.ID,
		}); err != nil {
			span.SetStatus(codes.Error, "failed to create organization member")
			span.RecordError(err)
			return err
		}
		//add new member to groups
		for _, groupID := range orgGitGroups {
			gitlabMember, err := u.gitlabClient.GetMemberOfGroup(c, gitlab.GetMemberOfGroupRequest{
				GroupId: groupID,
				UserId:  user.RefGitUserID,
				Token:   accessToken,
			})
			if err != nil {
				span.SetStatus(codes.Error, "failed to get member in gitlab group")
				span.RecordError(err)
			}
			if gitlabMember == nil {
				_, err = u.gitlabClient.AddUserToGroup(c, gitlab.AddUserToGroupRequest{
					GroupId:     groupID,
					UserId:      user.RefGitUserID,
					AccessLevel: role,
					Token:       accessToken,
					ExpiresAt:   utils.ConvertTimeToString(input.ExpireAt),
				})
				if err != nil {
					span.SetStatus(codes.Error, "failed to add user to GitLab group")
					span.RecordError(err)
					return err
				}
			}
		}
		span.AddEvent("org member updated successfully")
		span.SetStatus(codes.Ok, "org member updated successfully")
		return nil
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to execute transaction")
		span.RecordError(err)
		return err
	}

	// sent invitation email
	if err := u.mailClient.SendMail(c, mail.SendEmailInput{
		TemplateName: "invite_org.html",
		Title:        fmt.Sprintf("You’re Invited to Join %s on the %s Platform", org.Name, u.config.PlatformName),
		UserEmails:   []string{user.Email},
		Payload: map[string]interface{}{
			"PlatformName":     u.config.PlatformName,
			"Name":             user.Name,
			"OrganizationName": org.Name,
		},
	}); err != nil {
		span.SetStatus(codes.Error, "failed to send invitation email")
		span.RecordError(err)
		return usecase.ErrSendMail
	}

	span.AddEvent("user invited successfully")
	span.SetStatus(codes.Ok, "user invited successfully")
	return nil
}

// getAccessToken retrieves the appropriate GitLab access token for the current user.
// It handles token retrieval based on user role and organization permissions.
//
// Parameters:
//   - c: Context for the operation
//   - currentUser: The user requesting the access token
//   - u: The organization usecase implementation
//   - org: The organization context
//   - span: OpenTelemetry span for tracing
//
// Returns:
//   - string: The GitLab access token
//   - error: Any error that occurred during token retrieval
func getAccessToken(c context.Context, currentUser *entities.User, u impl, org *entities.Organization, span trace.Span) (string, error) {
	var accessToken string
	if currentUser.Role != enums.UserRole_Admin {
		err := checkOrgMemberPermission(c, u, org, currentUser, span)
		if err != nil {
			span.SetStatus(codes.Error, "no permission")
			span.RecordError(err)
			return "", err
		}
		if currentUser.GitlabAccessToken != nil {
			accessToken = *currentUser.GitlabAccessToken
		}
	} else {
		adminRefGitID := enums.AdminRefGitID
		adminUser, err := u.repository.FindUser(c, repository.FindUserFilter{RefGitUserID: &adminRefGitID})
		if err != nil {
			span.SetStatus(codes.Error, "failed to find admin user")
			span.RecordError(err)
			return "", err
		}
		if adminUser.GitlabAccessToken != nil {
			accessToken = *adminUser.GitlabAccessToken
		}
	}
	return accessToken, nil
}

// checkOrgMemberPermission verifies if a user has the required permissions in an organization.
// It checks if the user is an owner of the organization.
//
// Parameters:
//   - c: Context for the operation
//   - u: The organization usecase implementation
//   - org: The organization to check permissions against
//   - currentUser: The user whose permissions are being checked
//   - span: OpenTelemetry span for tracing
//
// Returns:
//   - error: Any error that occurred during permission check, including ErrNoPermission
func checkOrgMemberPermission(c context.Context, u impl, org *entities.Organization, currentUser *entities.User, span trace.Span) error {
	currentUserOrg, err := u.repository.FindOrgMember(c, repository.FilterOrgMember{
		OrgID:  &org.ID,
		UserID: &currentUser.ID,
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find current user's organization membership")
		span.RecordError(err)
		return err
	}

	if currentUserOrg.Role != enums.OrgRole_Owner {
		span.SetStatus(codes.Error, usecase.ErrNoPermission.Error())
		span.RecordError(usecase.ErrNoPermission)
		return usecase.ErrNoPermission
	}
	return nil
}

// checkMemberExistInOrg verifies if a user is already a member of an organization.
// It prevents duplicate memberships and handles error cases.
//
// Parameters:
//   - ctx: Context for the operation
//   - u: The organization usecase implementation
//   - span: OpenTelemetry span for tracing
//   - orgID: The ID of the organization to check
//   - memberUserID: The ID of the user to check membership for
//
// Returns:
//   - error: Any error that occurred during membership check, including ErrMemberAlreadyExistsInOrg
func checkMemberExistInOrg(ctx context.Context, u impl, span trace.Span, orgID, memberUserID uuid.UUID) error {
	userOrg, err := u.repository.FindOrgMember(ctx, repository.FilterOrgMember{
		OrgID:  &orgID,
		UserID: &memberUserID,
	})
	if err != nil && err != gorm.ErrRecordNotFound {
		span.SetStatus(codes.Error, "failed to find org member")
		span.RecordError(err)
		return err
	}

	// check if member already exists in org
	if userOrg != nil && userOrg.UserID == memberUserID {
		span.SetStatus(codes.Error, usecase.ErrMemberAlreadyExistsInOrg.Error())
		span.RecordError(usecase.ErrMemberAlreadyExistsInOrg)
		return usecase.ErrMemberAlreadyExistsInOrg
	}

	return nil
}

// InviteMultipleOrgMembers implements the OrganizationUsecase interface for inviting multiple users.
// It handles batch invitations including email notifications and member setup.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: Input data containing multiple invitation details including emails and roles
//
// Returns:
//   - error: Any error that occurred during invitations
func (u impl) InviteMultipleOrgMembers(ctx context.Context, input dto.InviteOrgMembersInput) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.organization.InviteMultipleOrgMembers")
	defer span.End()

	currentUser, err := u.repository.FindUserByID(ctx, input.CurrentUserID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return usecase.ErrUserNotFound
		}
		span.SetStatus(codes.Error, "failed to find current user")
		span.RecordError(err)
		return err
	}

	// query organization by id
	org, err := u.repository.FindOrganizationByID(ctx, input.OrgId)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return usecase.ErrOrganizationNotFound
		}
		span.SetStatus(codes.Error, "failed to find organization")
		span.RecordError(err)
		return err
	}

	//get user permission accesstoken
	accessToken, err := getAccessToken(ctx, currentUser, u, org, span)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get right access token")
		span.RecordError(err)
		return err
	}

	//invite member
	orgGitGroup, err := u.repository.FindOrgGitGroup(ctx, org.ID)
	if err != nil {
		span.SetStatus(codes.Error, "failed to find GitLab organization group")
		span.RecordError(err)
		return err
	}
	orgGitGroups := []int64{
		orgGitGroup.RefGitSpacesID,
		orgGitGroup.RefGitModelsID,
		orgGitGroup.RefGitDatasetsID,
	}

	for _, memberInput := range input.Members {
		if memberInput.ExpireAt != nil && memberInput.ExpireAt.Before(time.Now()) {
			span.SetStatus(codes.Error, "expired time in the past for a member")
			span.RecordError(usecase.ErrTimeIsPast)
			return usecase.ErrTimeIsPast
		}
		role := gitlab.OrganizationAccessLevel_Developer
		if memberInput.Role == enums.OrgRole_Owner {
			role = gitlab.OrganizationAccessLevel_Owner
		}
		// query user by id
		user, err := u.repository.FindUserByID(ctx, memberInput.UserId)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return usecase.ErrUserNotFound
			}
			span.SetStatus(codes.Error, "failed to find user")
			span.RecordError(err)
			return err
		}
		//is admin
		if user.RefGitUserID == enums.AdminRefGitID {
			continue
		}

		err = checkMemberExistInOrg(ctx, u, span, org.ID, memberInput.UserId)
		if err != nil {
			return err
		}

		err = u.repository.Transaction(ctx, func(ctx context.Context) error {
			// create organization member
			if err := u.repository.Create(ctx, &entities.OrgMember{
				User:      user,
				Org:       org,
				Role:      memberInput.Role,
				ExpiresAt: memberInput.ExpireAt,
				UserID:    user.ID,
				OrgID:     org.ID,
			}); err != nil {
				span.SetStatus(codes.Error, "failed to create organization member")
				span.RecordError(err)
				return err
			}
			//add new member to groups
			for _, groupID := range orgGitGroups {
				gitlabMember, err := u.gitlabClient.GetMemberOfGroup(ctx, gitlab.GetMemberOfGroupRequest{
					GroupId: groupID,
					UserId:  user.RefGitUserID,
					Token:   accessToken,
				})
				if err != nil {
					span.SetStatus(codes.Error, "failed to get member in gitlab group")
					span.RecordError(err)
				}

				if gitlabMember == nil {
					_, err = u.gitlabClient.AddUserToGroup(ctx, gitlab.AddUserToGroupRequest{
						GroupId:     groupID,
						UserId:      user.RefGitUserID,
						AccessLevel: role,
						Token:       accessToken,
						ExpiresAt:   utils.ConvertTimeToString(memberInput.ExpireAt),
					})
					if err != nil {
						span.SetStatus(codes.Error, "failed to add user to GitLab group")
						span.RecordError(err)
						return err
					}
				}
			}
			span.AddEvent("org member updated successfully")
			span.SetStatus(codes.Ok, "org member updated successfully")
			return nil
		})
		if err != nil {
			span.SetStatus(codes.Error, "failed to execute transaction")
			span.RecordError(err)
			return err
		}
		// sent invitation email
		if err := u.mailClient.SendMail(ctx, mail.SendEmailInput{
			TemplateName: "invite_org.html",
			Title:        fmt.Sprintf("You’re Invited to Join %s on the %s Platform", org.Name, u.config.PlatformName),
			UserEmails:   []string{user.Email},
			Payload: map[string]interface{}{
				"PlatformName":     u.config.PlatformName,
				"Name":             user.Name,
				"OrganizationName": org.Name,
			},
		}); err != nil {
			span.SetStatus(codes.Error, "failed to send invitation email")
			span.RecordError(err)
			return usecase.ErrSendMail
		}

		span.AddEvent(
			"invite user to organization successfully",
			trace.WithAttributes(
				attribute.String("user_id", memberInput.UserId.String()),
				attribute.String("role", memberInput.Role.String()),
			),
		)
	}

	span.AddEvent("invite users to organization successfully")
	span.SetStatus(codes.Ok, "invite users to organization successfully")
	return nil
}
