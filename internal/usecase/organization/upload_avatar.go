package organization

import (
	"api-server/internal/dto"
	"api-server/internal/enums"
	"api-server/internal/usecase"
	"api-server/pkg/oteltrace"
	"context"
	"errors"

	"go.opentelemetry.io/otel/codes"
	"gorm.io/gorm"
)

// UploadAvatarOrganization implements the OrganizationUsecase interface for uploading organization avatars.
// It handles file upload to AWS S3 and updates organization avatar URL.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: Input data containing avatar upload details including organization ID and file data
//
// Returns:
//   - string: URL of the uploaded avatar
//   - error: Any error that occurred during upload
func (u *impl) UploadAvatarOrganization(ctx context.Context, input dto.UploadOrganizationAvatarInput) (string, error) {
	c, span := oteltrace.Tracer.Start(ctx, "usecase.organization.UploadAvatarOrganization")
	defer span.End()

	currentUser, err := u.repository.FindUserByID(c, input.CurrentUserId)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return "", usecase.ErrUserNotFound
		}
		span.SetStatus(codes.Error, "failed to find current user")
		span.RecordError(err)
		return "", err
	}

	org, err := u.repository.FindOrganizationByID(c, input.OrgID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return "", usecase.ErrOrganizationNotFound
		}
		span.SetStatus(codes.Error, "failed to find organization")
		span.RecordError(err)
		return "", err
	}

	if currentUser.Role != enums.UserRole_Admin {
		err := checkOrgMemberPermission(c, *u, org, currentUser, span)
		if err != nil {
			span.SetStatus(codes.Error, "no permission")
			span.RecordError(err)
			return "", err
		}
	}

	imageUrl, err := u.awsClient.UploadImage(c, *input.File, string(enums.S3BucketPrefix_Avatar))
	if err != nil {
		span.SetStatus(codes.Error, "failed to upload image")
		span.RecordError(err)
		return "", err
	}

	org.Avatar = &imageUrl

	if err := u.repository.Save(c, org); err != nil {
		span.SetStatus(codes.Error, "failed to save organization")
		span.RecordError(err)
		return "", err
	}

	span.AddEvent("avatar uploaded successfully")
	span.SetStatus(codes.Ok, "avatar uploaded successfully")
	return imageUrl, nil
}

// DeleteAvatarOrganization implements the OrganizationUsecase interface for removing organization avatars.
// It handles file deletion from AWS S3 and updates organization avatar URL.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: Input data containing avatar deletion details including organization ID
//
// Returns:
//   - error: Any error that occurred during deletion
func (u *impl) DeleteAvatarOrganization(ctx context.Context, input dto.DeleteOrganizationAvatarInput) error {
	c, span := oteltrace.Tracer.Start(ctx, "usecase.organization.DeleteAvatarOrganization")
	defer span.End()

	currentUser, err := u.repository.FindUserByID(c, input.CurrentUserId)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return usecase.ErrUserNotFound
		}
		span.SetStatus(codes.Error, "failed to find current user")
		span.RecordError(err)
		return err
	}

	org, err := u.repository.FindOrganizationByID(c, input.OrgID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return usecase.ErrOrganizationNotFound
		}
		span.SetStatus(codes.Error, "failed to find organization")
		span.RecordError(err)
		return err
	}

	if currentUser.Role != enums.UserRole_Admin {
		err := checkOrgMemberPermission(c, *u, org, currentUser, span)
		if err != nil {
			span.SetStatus(codes.Error, "no permission")
			span.RecordError(err)
			return err
		}
	}
	if org.Avatar != nil {
		if err = u.awsClient.DeleteImage(c, *org.Avatar); err != nil {
			span.SetStatus(codes.Error, "failed to delete s3 image")
			span.RecordError(err)
			return err
		}
	}

	org.Avatar = nil
	if err := u.repository.Save(c, org); err != nil {
		span.SetStatus(codes.Error, "failed to save organization")
		span.RecordError(err)
		return err
	}

	span.AddEvent("avatar deleted successfully")
	span.SetStatus(codes.Ok, "avatar deleted successfully")
	return nil
}
