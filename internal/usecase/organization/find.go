package organization

import (
	"api-server/internal/dto"
	repository "api-server/internal/repositories"
	"api-server/pkg/oteltrace"
	"context"

	"go.opentelemetry.io/otel/codes"
)

// FindOrganizationByID implements the OrganizationUsecase interface for retrieving organization details.
// It fetches comprehensive information about a specific organization including its members and GitLab group.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: Input data containing organization ID
//
// Returns:
//   - *dto.FindOrganizationOutput: Organization information including members and GitLab group details
//   - error: Any error that occurred during retrieval
func (u impl) FindOrganizationByID(ctx context.Context, input dto.FindOrganizationInput) (*dto.FindOrganizationOutput, error) {
	c, span := oteltrace.Tracer.Start(ctx, "usecase.organization.FindOrganizationByID")
	defer span.End()

	org, err := u.repository.FindOrganization(c, repository.FilterOrganization{
		OrgId: &input.OrgId,
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find organization")
		span.RecordError(err)
		return nil, err
	}

	// Pre-sign URL
	if org.Avatar != nil {
		orgImage, err := u.awsClient.GenPreSignUrl(c, *org.Avatar)
		if err != nil {
			span.SetStatus(codes.Error, "failed to generate pre-signed URL")
			span.RecordError(err)
			return nil, err
		}
		org.Avatar = &orgImage
	}

	span.AddEvent("organization retrieved successfully")
	span.SetStatus(codes.Ok, "organization retrieved successfully")
	return &dto.FindOrganizationOutput{
		Organization: *org,
	}, nil
}
