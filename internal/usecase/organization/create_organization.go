package organization

import (
	"context"
	"errors"
	"fmt"

	"api-server/internal/dto"
	"api-server/internal/entities"
	"api-server/internal/enums"
	"api-server/internal/gateways/gitlab"
	repository "api-server/internal/repositories"
	"api-server/internal/usecase"
	"api-server/internal/usecase/signup_request"
	"api-server/pkg/oteltrace"

	"github.com/google/uuid"
	"go.opentelemetry.io/otel/codes"
	"gorm.io/gorm"
)

// CreateOrganization implements the OrganizationUsecase interface for creating a new organization.
// It handles the creation process including setting up GitLab groups, member setup, and avatar upload.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: Input data containing organization details including name, path, type, and optional avatar
//
// Returns:
//   - *dto.CreateOrganizationOutput: Created organization information including ID
//   - error: Any error that occurred during creation
func (u impl) CreateOrganization(ctx context.Context, input dto.CreateOrganizationInput) (*dto.CreateOrganizationOutput, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.organization.CreateOrganization")
	defer span.End()

	currentUser, err := u.repository.FindUserByID(ctx, input.UserCurrentId)
	if err != nil {
		span.SetStatus(codes.Error, "failed to find user")
		span.RecordError(err)
		return nil, err
	}

	org, err := u.repository.FindOrganization(ctx, repository.FilterOrganization{
		Name:     &input.Name,
		PathName: &input.PathName,
	})
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		span.SetStatus(codes.Error, "failed to find organization")
		span.RecordError(err)
		return nil, err
	}

	if org != nil {
		span.SetStatus(codes.Error, usecase.ErrDuplicatedOrganization.Error())
		span.RecordError(usecase.ErrDuplicatedOrganization)
		return nil, usecase.ErrDuplicatedOrganization
	}

	var orgId uuid.UUID
	err = u.repository.Transaction(ctx, func(ctx context.Context) error {
		organizationEntity := &entities.Organization{
			Name:     input.Name,
			PathName: input.PathName,
			Type:     input.OrganizationType,
			Interest: &input.Interest,
		}
		err = u.repository.Create(ctx, organizationEntity)
		if err != nil {
			span.SetStatus(codes.Error, "failed to create organization")
			span.RecordError(err)
			return err
		}

		adminRefGitID := enums.AdminRefGitID
		adminUser, err := u.repository.FindUser(ctx, repository.FindUserFilter{RefGitUserID: &adminRefGitID})
		if err != nil {
			span.SetStatus(codes.Error, "failed to find admin user")
			span.RecordError(err)
			return err
		}

		if input.File != nil {
			imageUrl, err := u.awsClient.UploadImage(ctx, *input.File, string(enums.S3BucketPrefix_Avatar))
			if err != nil {
				span.SetStatus(codes.Error, "failed to upload image")
				span.RecordError(err)
				return err
			}
			if imageUrl != "" {
				organizationEntity.Avatar = &imageUrl
			}
		}

		gitGroups, err := createGitGroups(ctx, u, organizationEntity.PathName, currentUser.RefGitUserID, adminUser.ID, *adminUser.GitlabAccessToken)
		if err != nil {
			span.SetStatus(codes.Error, "failed to create organization git group")
			span.RecordError(err)
			return err
		}
		createOrgGitGroupInput := repository.CreateOrgGitGroupInput{
			OrgID:            organizationEntity.ID,
			RefGitSpacesID:   gitGroups[fmt.Sprintf("%s/%s", enums.RepoType_Spaces, organizationEntity.PathName)].UserGitGroupId,
			RefGitDatasetsID: gitGroups[fmt.Sprintf("%s/%s", enums.RepoType_Datasets, organizationEntity.PathName)].UserGitGroupId,
			RefGitModelsID:   gitGroups[fmt.Sprintf("%s/%s", enums.RepoType_Models, organizationEntity.PathName)].UserGitGroupId,
		}
		if val, ok := gitGroups[fmt.Sprintf("%s/%s", enums.RepoType_Composes, organizationEntity.PathName)]; ok {
			createOrgGitGroupInput.RefGitComposesID = &val.UserGitGroupId
		}
		_, err = u.repository.CreateOrgGitGroup(ctx, createOrgGitGroupInput)
		if err != nil {
			span.SetStatus(codes.Error, "failed to create organization git group")
			span.RecordError(err)
			return err
		}

		err = u.repository.Save(ctx, organizationEntity)
		if err != nil {
			span.SetStatus(codes.Error, "failed to save organization")
			span.RecordError(err)
			return err
		}

		member := &entities.OrgMember{
			UserID: currentUser.ID,
			OrgID:  organizationEntity.ID,
			Role:   enums.OrgRole_Owner,
		}
		if err := u.repository.Create(ctx, member); err != nil {
			span.SetStatus(codes.Error, "failed to create organization member")
			span.RecordError(err)
			return err
		}

		orgId = organizationEntity.ID
		return nil
	})
	if err != nil {
		span.SetStatus(codes.Error, "transaction failed")
		span.RecordError(err)
		return nil, err
	}

	span.SetStatus(codes.Ok, "organization created successfully")
	span.AddEvent("organization created successfully")
	return &dto.CreateOrganizationOutput{
		ID: orgId,
	}, nil
}

// createGitGroups creates GitLab groups for an organization.
// It sets up groups for spaces, datasets, and models repositories.
//
// Parameters:
//   - ctx: Context for the operation
//   - u: The organization usecase implementation
//   - pathName: The path name for the organization
//   - gitlabUserID: The GitLab user ID
//   - adminUserID: The admin user ID
//   - token: The GitLab access token
//
// Returns:
//   - map[string]signup_request.Group: Map of created GitLab groups
//   - error: Any error that occurred during group creation
func createGitGroups(ctx context.Context, u impl, pathName string, gitlabUserID int64, adminUserID uuid.UUID, token string) (map[string]signup_request.Group, error) {
	adminGitGroupInfo, err := u.repository.FindUserGitGroup(ctx, adminUserID)
	if err != nil {
		return nil, err
	}

	gitGroups := map[string]signup_request.Group{
		fmt.Sprintf("%s/%s", enums.RepoType_Spaces, pathName): {
			DefaultGitGroupId: adminGitGroupInfo.RefGitSpacesID,
		},
		fmt.Sprintf("%s/%s", enums.RepoType_Datasets, pathName): {
			DefaultGitGroupId: adminGitGroupInfo.RefGitDatasetsID,
		},
		fmt.Sprintf("%s/%s", enums.RepoType_Models, pathName): {
			DefaultGitGroupId: adminGitGroupInfo.RefGitModelsID,
		},
		// fmt.Sprintf("%s/%s", enums.RepoType_Composes, pathName): {
		// 	DefaultGitGroupId: adminGitGroupInfo.RefGitComposesID,
		// },
	}
	if adminGitGroupInfo.RefGitComposesID != nil {
		gitGroups[fmt.Sprintf("%s/%s", enums.RepoType_Composes, pathName)] = signup_request.Group{
			DefaultGitGroupId: *adminGitGroupInfo.RefGitComposesID,
		}
	}

	for key := range gitGroups {
		orgs, err := u.gitlabClient.GetGroup(ctx, gitlab.GetGroupRequest{
			Token: token,
			Path:  key,
		})
		if err != nil {
			return nil, err
		}

		if len(orgs) > 0 {
			gitGroups[key] = signup_request.Group{
				UserGitGroupId: int64(orgs[0].Id),
			}
		} else {
			group, err := u.createGroup(ctx, gitlabUserID, pathName, token, gitGroups[key].DefaultGitGroupId)
			if err != nil {
				return nil, err
			}

			gitGroups[key] = signup_request.Group{
				UserGitGroupId: int64(group.Id),
			}
		}
	}
	return gitGroups, nil
}

// createGroup creates a single GitLab group with proper branch protection settings.
// It also adds the user to the group with appropriate access level.
//
// Parameters:
//   - ctx: Context for the operation
//   - gitUserID: The GitLab user ID to add to the group
//   - name: The name of the group
//   - token: The GitLab access token
//   - parentID: The parent group ID
//
// Returns:
//   - *gitlab.CreateOrganizationResponse: The created GitLab group information
//   - error: Any error that occurred during group creation
func (u *impl) createGroup(ctx context.Context, gitUserID int64, name, token string, parentID int64) (*gitlab.GitlabGroup, error) {
	c, span := oteltrace.Tracer.Start(ctx, "usecase.organization.createGroup")
	req := gitlab.CreateGroupRequest{
		Name:     name,
		Path:     name,
		Token:    token,
		ParentID: parentID,
	}

	group, err := u.gitlabClient.CreateGroup(c, req)
	if err != nil {
		span.SetStatus(codes.Error, "failed to create git group")
		span.RecordError(err)
		return nil, err
	}
	//set default protected branch for developer to push and merge
	reqUpdate := gitlab.UpdateGroupRequest{
		Id:         group.Id,
		Token:      token,
		Visibility: string(enums.RepoVisibility_Internal),
		DefaultBranchProtectionDefaults: gitlab.DefaultBranchProtectionDefaults{
			AllowedToPush: []gitlab.AccessLevel{
				{
					AccessLevel: gitlab.OrganizationAccessLevel_Developer,
				},
			},
			AllowedToMerge: []gitlab.AccessLevel{
				{
					AccessLevel: gitlab.OrganizationAccessLevel_Developer,
				},
			},
			AllowForcePush: false,
		},
	}
	if err = u.gitlabClient.UpdateGroup(c, reqUpdate); err != nil {
		span.SetStatus(codes.Error, "failed to update git group")
		span.RecordError(err)
		return nil, err
	}

	//add git user if user is not admin
	if gitUserID != enums.AdminRefGitID {
		_, err = u.gitlabClient.AddUserToGroup(c, gitlab.AddUserToGroupRequest{
			GroupId:     int64(group.Id),
			UserId:      gitUserID,
			AccessLevel: gitlab.OrganizationAccessLevel_Owner,
			Token:       token,
		})
		if err != nil {
			span.SetStatus(codes.Error, "failed to add user to git group")
			span.RecordError(err)
			return nil, err
		}
	}

	span.SetStatus(codes.Ok, "git group created successfully")
	span.AddEvent("git group created successfully")
	return group, nil
}
