package organization

import (
	"api-server/internal/dto"
	"api-server/pkg/oteltrace"
	"context"

	"go.opentelemetry.io/otel/codes"
)

// ListOrganizationMembers implements the OrganizationUsecase interface for retrieving organization members.
// It supports pagination and filtering of member data.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: Input data containing list parameters including pagination and filtering options
//
// Returns:
//   - *dto.ListOrgMembersOutput: List of member information with pagination metadata
//   - error: Any error that occurred during retrieval
func (u impl) ListOrganizationMembers(ctx context.Context, input dto.ListOrgMembersInput) (*dto.ListOrgMembersOutput, error) {
	c, span := oteltrace.Tracer.Start(ctx, "usecase.organization.ListOrganizationMembers")
	defer span.End()

	memberNum, err := u.repository.CountMembers(c, input)
	if err != nil {
		span.SetStatus(codes.Error, "failed to count members")
		span.RecordError(err)
		return nil, err
	}

	members, err := u.repository.ListMembers(c, input)
	if err != nil {
		span.SetStatus(codes.Error, "failed to list members")
		span.RecordError(err)
		return nil, err
	}

	// Pre-sign URL for avatars
	for i := 0; i < len(members); i++ {
		if members[i].Avatar != nil {
			repoImage, err := u.awsClient.GenPreSignUrl(c, *members[i].Avatar)
			if err != nil {
				span.SetStatus(codes.Error, "failed to generate pre-signed URL for avatar")
				span.RecordError(err)
				return nil, err
			}
			members[i].Avatar = &repoImage
		}
	}

	span.AddEvent("successfully retrieved organization members")
	span.SetStatus(codes.Ok, "successfully retrieved organization members")
	return &dto.ListOrgMembersOutput{
		Data: &members,
		Pagination: &dto.Pagination{
			Total:    memberNum,
			PageNo:   input.Paginate.Page,
			PageSize: input.Paginate.PerPage,
		},
	}, nil
}
