// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// MockHealthcheckUsecase is an autogenerated mock type for the HealthcheckUsecase type
type MockHealthcheckUsecase struct {
	mock.Mock
}

type MockHealthcheckUsecase_Expecter struct {
	mock *mock.Mock
}

func (_m *MockHealthcheckUsecase) EXPECT() *MockHealthcheckUsecase_Expecter {
	return &MockHealthcheckUsecase_Expecter{mock: &_m.Mock}
}

// Check provides a mock function with given fields: ctx
func (_m *MockHealthcheckUsecase) Check(ctx context.Context) error {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for Check")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context) error); ok {
		r0 = rf(ctx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockHealthcheckUsecase_Check_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Check'
type MockHealthcheckUsecase_Check_Call struct {
	*mock.Call
}

// Check is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockHealthcheckUsecase_Expecter) Check(ctx interface{}) *MockHealthcheckUsecase_Check_Call {
	return &MockHealthcheckUsecase_Check_Call{Call: _e.mock.On("Check", ctx)}
}

func (_c *MockHealthcheckUsecase_Check_Call) Run(run func(ctx context.Context)) *MockHealthcheckUsecase_Check_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockHealthcheckUsecase_Check_Call) Return(_a0 error) *MockHealthcheckUsecase_Check_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockHealthcheckUsecase_Check_Call) RunAndReturn(run func(context.Context) error) *MockHealthcheckUsecase_Check_Call {
	_c.Call.Return(run)
	return _c
}

// Ping provides a mock function with given fields: ctx
func (_m *MockHealthcheckUsecase) Ping(ctx context.Context) {
	_m.Called(ctx)
}

// MockHealthcheckUsecase_Ping_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Ping'
type MockHealthcheckUsecase_Ping_Call struct {
	*mock.Call
}

// Ping is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockHealthcheckUsecase_Expecter) Ping(ctx interface{}) *MockHealthcheckUsecase_Ping_Call {
	return &MockHealthcheckUsecase_Ping_Call{Call: _e.mock.On("Ping", ctx)}
}

func (_c *MockHealthcheckUsecase_Ping_Call) Run(run func(ctx context.Context)) *MockHealthcheckUsecase_Ping_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockHealthcheckUsecase_Ping_Call) Return() *MockHealthcheckUsecase_Ping_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockHealthcheckUsecase_Ping_Call) RunAndReturn(run func(context.Context)) *MockHealthcheckUsecase_Ping_Call {
	_c.Run(run)
	return _c
}

// NewMockHealthcheckUsecase creates a new instance of MockHealthcheckUsecase. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockHealthcheckUsecase(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockHealthcheckUsecase {
	mock := &MockHealthcheckUsecase{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
