package heathcheck

import (
	"context"
	"database/sql"
	"errors"
	"time"

	"go.uber.org/zap"
	"golang.org/x/sync/errgroup"

	"api-server/internal/gateways/gitlab"
	"api-server/pkg/oteltrace"
	"api-server/pkg/otelzap"
)

// HealthcheckUsecase defines the interface for health check operations.
// It provides methods for monitoring the health of various system components.
type HealthcheckUsecase interface {
	// Ping performs a health check on all system components.
	// It checks the health of services like GitLab and Postgres,
	// and starts a background ticker for continuous monitoring.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//
	// Returns:
	//   - None
	Ping(ctx context.Context)
	// Check implements the HealthcheckUsecase interface for performing a single health check.
	// It verifies the health status of all configured services (PostgreSQL and GitLab)
	// and returns any errors encountered during the check.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//
	// Returns:
	//   - error: Any error that occurred during the health check, nil if all services are healthy
	Check(ctx context.Context) error
}

var _ HealthcheckUsecase = (*impl)(nil)

type impl struct {
	gitlabClient gitlab.GitlabClient
	db           *sql.DB
}

// New creates a new instance of the health check usecase implementation.
// It initializes the usecase with required dependencies for health monitoring.
//
// Parameters:
//   - gitlabClient: Client for GitLab API interactions
//   - db: Database connection for health checks
//
// Returns:
//   - *impl: New instance of the health check usecase
func New(gitlabClient gitlab.GitlabClient, db *sql.DB) *impl {
	return &impl{
		gitlabClient,
		db,
	}
}

type res struct {
	name  string
	ready bool
}

// pingGitlab checks the health status of the GitLab service.
// It verifies the connection and availability of the GitLab instance.
//
// Parameters:
//   - ctx: Context for the operation
//
// Returns:
//   - *res: Health check result containing service name and status
//   - error: Any error that occurred during the health check
func (u *impl) pingGitlab(ctx context.Context) (*res, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.heathcheck.pingGitlab")
	defer span.End()

	isReady, err := u.gitlabClient.Health(ctx, "")

	return &res{
		name:  "Gitlab",
		ready: isReady,
	}, err
}

// pingPostgres checks the health status of the PostgreSQL database.
// It verifies the database connection and availability.
//
// Parameters:
//   - ctx: Context for the operation
//
// Returns:
//   - *res: Health check result containing service name and status
//   - error: Any error that occurred during the health check
func (u *impl) pingPostgres(ctx context.Context) (*res, error) {
	_, span := oteltrace.Tracer.Start(ctx, "usecase.heathcheck.pingPostgres")
	defer span.End()

	ready := true

	err := u.db.Ping()
	if err != nil {
		ready = false
	}

	return &res{
		name:  "Postgres",
		ready: ready,
	}, err
}

// pingService performs health checks on all configured services concurrently.
// It uses errgroup for parallel execution of health checks and logs results.
//
// Parameters:
//   - ctx: Context for the operation
//
// Returns:
//   - error: Any error that occurred during health checks
func (u *impl) pingService(ctx context.Context) error {
	fns := []func(ctx context.Context) (*res, error){
		u.pingPostgres,
		u.pingGitlab,
	}

	g, _ := errgroup.WithContext(ctx)
	g.SetLimit(8)
	for _, fn := range fns {
		fn := fn // Create new variable to avoid closure capture issue
		g.Go(func() error {
			res, err := fn(ctx)
			if err != nil {
				otelzap.Logger.Error("ping", zap.String("service", res.name), zap.Bool("ready", res.ready), zap.Error(err))
			} else if !res.ready {
				err = errors.New("connection error")
				otelzap.Logger.Error("ping", zap.String("service", res.name), zap.Bool("ready", res.ready), zap.Error(err))
			} else {
				otelzap.Logger.Info("ping", zap.String("service", res.name), zap.Bool("ready", res.ready))
			}

			return err
		})
	}

	if err := g.Wait(); err != nil {
		return err
	}

	return nil
}

// Ping implements the HealthcheckUsecase interface for continuous health monitoring.
// It performs initial health check and then runs periodic checks with adaptive intervals:
// - Starts with 30 second intervals
// - After 3 consecutive successes/failures, increases to 1 minutes
// - Resets to 30 seconds if status changes
func (u *impl) Ping(ctx context.Context) {
	err := u.pingService(ctx)
	if err != nil {
		otelzap.Logger.Fatal("Ping service status error")
	}

	// Initial intervals
	shortInterval := 30 * time.Second
	longInterval := 1 * time.Minute
	currentInterval := shortInterval

	// Counters for consecutive status
	consecutiveCount := 0
	lastStatus := err == nil // true for success, false for failure

	ticker := time.NewTicker(currentInterval)
	go func() {
		defer ticker.Stop()
		for {
			select {
			case <-ticker.C:
				err := u.pingService(ctx)
				currentStatus := err == nil

				// Check if status changed
				if currentStatus != lastStatus {
					// Reset to short interval and counter
					currentInterval = shortInterval
					consecutiveCount = 1
					ticker.Reset(currentInterval)
					otelzap.Logger.Info("Status changed, resetting to short interval",
						zap.String("new_status", map[bool]string{true: "success", false: "failure"}[currentStatus]),
						zap.Duration("interval", currentInterval))
				} else {
					// Increment counter for same status
					consecutiveCount++

					// If we have 3 consecutive same results, switch to long interval
					if consecutiveCount >= 3 && currentInterval == shortInterval {
						currentInterval = longInterval
						ticker.Reset(currentInterval)
						otelzap.Logger.Info("Switching to long interval health checks",
							zap.String("status", map[bool]string{true: "success", false: "failure"}[currentStatus]),
							zap.Duration("interval", currentInterval),
							zap.Int("consecutive_count", consecutiveCount))
					}
				}

				lastStatus = currentStatus

			case <-ctx.Done():
				otelzap.Logger.Info("Health check monitoring stopped")
				return
			}
		}
	}()
}

// Check implements the HealthcheckUsecase interface for performing a single health check.
// It verifies the health status of all configured services (PostgreSQL and GitLab)
// and returns any errors encountered during the check.
//
// Parameters:
//   - ctx: Context for the operation
//
// Returns:
//   - error: Any error that occurred during the health check, nil if all services are healthy
func (u *impl) Check(ctx context.Context) error {
	_, span := oteltrace.Tracer.Start(ctx, "usecase.heathcheck.HealthCheck")
	defer span.End()

	err := u.pingService(ctx)
	if err != nil {
		return err
	}

	return nil
}
