package heathcheck_test

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	gitlab_mocks "api-server/internal/gateways/gitlab/mocks"
	heathcheck "api-server/internal/usecase/healthcheck" // Corrected package name
)

func TestCheck(t *testing.T) {
	ctx := context.Background()

	type mockArgs struct {
		gitlabHealthReturnBool bool
		gitlabHealthReturnErr  error
		dbPingReturnErr        error
	}

	tests := []struct {
		name     string
		mockArgs mockArgs
		wantErr  bool
		errStr   string
	}{
		{
			name: "success - all services healthy",
			mockArgs: mockArgs{
				gitlabHealthReturnBool: true,
				gitlabHealthReturnErr:  nil,
				dbPingReturnErr:        nil,
			},
			wantErr: false,
		},
		{
			name: "error - gitlab health returns error",
			mockArgs: mockArgs{
				gitlabHealthReturnBool: false,
				gitlabHealthReturnErr:  errors.New("gitlab API error"),
				dbPingReturnErr:        nil, // DB might be fine, or might not be called if gitlab fails first in errgroup
			},
			wantErr: true,
			errStr:  "gitlab API error",
		},
		{
			name: "error - gitlab health returns not ready",
			mockArgs: mockArgs{
				gitlabHealthReturnBool: false, // Not ready
				gitlabHealthReturnErr:  nil,
				dbPingReturnErr:        nil,
			},
			wantErr: true,
			errStr:  "connection error", // Specific error string from pingService when !res.ready
		},
		{
			name: "error - postgres ping returns error",
			mockArgs: mockArgs{
				gitlabHealthReturnBool: true,
				gitlabHealthReturnErr:  nil,
				dbPingReturnErr:        errors.New("postgres ping failed"),
			},
			wantErr: true,
			errStr:  "postgres ping failed",
		},
		{
			name: "error - both services fail",
			mockArgs: mockArgs{
				gitlabHealthReturnBool: false,
				gitlabHealthReturnErr:  errors.New("gitlab major outage"),
				dbPingReturnErr:        errors.New("postgres is down"),
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockGitlabClient := new(gitlab_mocks.MockGitlabClient)
			db, mockDB, err := sqlmock.New(sqlmock.MonitorPingsOption(true))
			if err != nil {
				t.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
			}
			defer db.Close()

			mockGitlabClient.On("Health", mock.Anything, "").Return(tt.mockArgs.gitlabHealthReturnBool, tt.mockArgs.gitlabHealthReturnErr).Maybe()
			if tt.mockArgs.dbPingReturnErr != nil {
				mockDB.ExpectPing().WillReturnError(tt.mockArgs.dbPingReturnErr)
			} else {
				mockDB.ExpectPing()
			}

			uc := heathcheck.New(mockGitlabClient, db)
			err = uc.Check(ctx)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.errStr != "" {
					assert.Contains(t, err.Error(), tt.errStr)
				}
			} else {
				assert.NoError(t, err)
			}

			mockGitlabClient.AssertExpectations(t)
			err = mockDB.ExpectationsWereMet()
			assert.NoError(t, err, "sqlmock expectations were not met")
		})
	}
}

func TestPing(t *testing.T) {
	// Create a context with timeout to prevent test from hanging
	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()

	type mockArgs struct {
		gitlabHealthReturnBool bool
		gitlabHealthReturnErr  error
		dbPingReturnErr        error
	}

	tests := []struct {
		name     string
		mockArgs mockArgs
		wantErr  bool
		errStr   string
	}{
		{
			name: "success - all services healthy",
			mockArgs: mockArgs{
				gitlabHealthReturnBool: true,
				gitlabHealthReturnErr:  nil,
				dbPingReturnErr:        nil,
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			testCtx, testCancel := context.WithCancel(ctx)
			defer testCancel()

			mockGitlabClient := new(gitlab_mocks.MockGitlabClient)
			db, mockDB, err := sqlmock.New(sqlmock.MonitorPingsOption(true))
			if err != nil {
				t.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
			}
			defer db.Close()

			// Set up mock expectations
			mockGitlabClient.On("Health", mock.Anything, "").Return(tt.mockArgs.gitlabHealthReturnBool, tt.mockArgs.gitlabHealthReturnErr).Maybe()
			if tt.mockArgs.dbPingReturnErr != nil {
				mockDB.ExpectPing().WillReturnError(tt.mockArgs.dbPingReturnErr)
			} else {
				mockDB.ExpectPing()
			}

			// Create usecase
			uc := heathcheck.New(mockGitlabClient, db)

			done := make(chan struct{})

			go func() {
				defer close(done)
				uc.Ping(testCtx)
			}()

			select {
			case <-done:
				if tt.wantErr {
					t.Log("Health check completed with expected error")
				}
			case <-testCtx.Done():
				// Context timed out
				if !tt.wantErr {
					t.Error("Test timed out waiting for health check to complete")
				}
			}

			mockGitlabClient.AssertExpectations(t)
			err = mockDB.ExpectationsWereMet()
			assert.NoError(t, err, "sqlmock expectations were not met")
		})
	}
}
