// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	dto "api-server/internal/dto"
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// MockSshKeyUsecase is an autogenerated mock type for the SshKeyUsecase type
type MockSshKeyUsecase struct {
	mock.Mock
}

type MockSshKeyUsecase_Expecter struct {
	mock *mock.Mock
}

func (_m *MockSshKeyUsecase) EXPECT() *MockSshKeyUsecase_Expecter {
	return &MockSshKeyUsecase_Expecter{mock: &_m.Mock}
}

// CreateSSHKey provides a mock function with given fields: ctx, input
func (_m *MockSshKeyUsecase) CreateSSHKey(ctx context.Context, input dto.CreateSSHKeyInput) error {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for CreateSSHKey")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.CreateSSHKeyInput) error); ok {
		r0 = rf(ctx, input)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockSshKeyUsecase_CreateSSHKey_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateSSHKey'
type MockSshKeyUsecase_CreateSSHKey_Call struct {
	*mock.Call
}

// CreateSSHKey is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.CreateSSHKeyInput
func (_e *MockSshKeyUsecase_Expecter) CreateSSHKey(ctx interface{}, input interface{}) *MockSshKeyUsecase_CreateSSHKey_Call {
	return &MockSshKeyUsecase_CreateSSHKey_Call{Call: _e.mock.On("CreateSSHKey", ctx, input)}
}

func (_c *MockSshKeyUsecase_CreateSSHKey_Call) Run(run func(ctx context.Context, input dto.CreateSSHKeyInput)) *MockSshKeyUsecase_CreateSSHKey_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.CreateSSHKeyInput))
	})
	return _c
}

func (_c *MockSshKeyUsecase_CreateSSHKey_Call) Return(_a0 error) *MockSshKeyUsecase_CreateSSHKey_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockSshKeyUsecase_CreateSSHKey_Call) RunAndReturn(run func(context.Context, dto.CreateSSHKeyInput) error) *MockSshKeyUsecase_CreateSSHKey_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteSSHKey provides a mock function with given fields: ctx, input
func (_m *MockSshKeyUsecase) DeleteSSHKey(ctx context.Context, input dto.DeleteSSHKeyInput) error {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for DeleteSSHKey")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.DeleteSSHKeyInput) error); ok {
		r0 = rf(ctx, input)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockSshKeyUsecase_DeleteSSHKey_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteSSHKey'
type MockSshKeyUsecase_DeleteSSHKey_Call struct {
	*mock.Call
}

// DeleteSSHKey is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.DeleteSSHKeyInput
func (_e *MockSshKeyUsecase_Expecter) DeleteSSHKey(ctx interface{}, input interface{}) *MockSshKeyUsecase_DeleteSSHKey_Call {
	return &MockSshKeyUsecase_DeleteSSHKey_Call{Call: _e.mock.On("DeleteSSHKey", ctx, input)}
}

func (_c *MockSshKeyUsecase_DeleteSSHKey_Call) Run(run func(ctx context.Context, input dto.DeleteSSHKeyInput)) *MockSshKeyUsecase_DeleteSSHKey_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.DeleteSSHKeyInput))
	})
	return _c
}

func (_c *MockSshKeyUsecase_DeleteSSHKey_Call) Return(_a0 error) *MockSshKeyUsecase_DeleteSSHKey_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockSshKeyUsecase_DeleteSSHKey_Call) RunAndReturn(run func(context.Context, dto.DeleteSSHKeyInput) error) *MockSshKeyUsecase_DeleteSSHKey_Call {
	_c.Call.Return(run)
	return _c
}

// GetAllSSHKeys provides a mock function with given fields: ctx, input
func (_m *MockSshKeyUsecase) GetAllSSHKeys(ctx context.Context, input dto.GetSSHKeyInput) (*dto.GetSSHKeyOutput, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for GetAllSSHKeys")
	}

	var r0 *dto.GetSSHKeyOutput
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.GetSSHKeyInput) (*dto.GetSSHKeyOutput, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, dto.GetSSHKeyInput) *dto.GetSSHKeyOutput); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*dto.GetSSHKeyOutput)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, dto.GetSSHKeyInput) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockSshKeyUsecase_GetAllSSHKeys_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllSSHKeys'
type MockSshKeyUsecase_GetAllSSHKeys_Call struct {
	*mock.Call
}

// GetAllSSHKeys is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.GetSSHKeyInput
func (_e *MockSshKeyUsecase_Expecter) GetAllSSHKeys(ctx interface{}, input interface{}) *MockSshKeyUsecase_GetAllSSHKeys_Call {
	return &MockSshKeyUsecase_GetAllSSHKeys_Call{Call: _e.mock.On("GetAllSSHKeys", ctx, input)}
}

func (_c *MockSshKeyUsecase_GetAllSSHKeys_Call) Run(run func(ctx context.Context, input dto.GetSSHKeyInput)) *MockSshKeyUsecase_GetAllSSHKeys_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.GetSSHKeyInput))
	})
	return _c
}

func (_c *MockSshKeyUsecase_GetAllSSHKeys_Call) Return(_a0 *dto.GetSSHKeyOutput, _a1 error) *MockSshKeyUsecase_GetAllSSHKeys_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockSshKeyUsecase_GetAllSSHKeys_Call) RunAndReturn(run func(context.Context, dto.GetSSHKeyInput) (*dto.GetSSHKeyOutput, error)) *MockSshKeyUsecase_GetAllSSHKeys_Call {
	_c.Call.Return(run)
	return _c
}

// GetSSHKey provides a mock function with given fields: ctx, input
func (_m *MockSshKeyUsecase) GetSSHKey(ctx context.Context, input dto.GetSingleSSHKeyInput) (*dto.GetSingleSSHKeyOutput, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for GetSSHKey")
	}

	var r0 *dto.GetSingleSSHKeyOutput
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.GetSingleSSHKeyInput) (*dto.GetSingleSSHKeyOutput, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, dto.GetSingleSSHKeyInput) *dto.GetSingleSSHKeyOutput); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*dto.GetSingleSSHKeyOutput)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, dto.GetSingleSSHKeyInput) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockSshKeyUsecase_GetSSHKey_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetSSHKey'
type MockSshKeyUsecase_GetSSHKey_Call struct {
	*mock.Call
}

// GetSSHKey is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.GetSingleSSHKeyInput
func (_e *MockSshKeyUsecase_Expecter) GetSSHKey(ctx interface{}, input interface{}) *MockSshKeyUsecase_GetSSHKey_Call {
	return &MockSshKeyUsecase_GetSSHKey_Call{Call: _e.mock.On("GetSSHKey", ctx, input)}
}

func (_c *MockSshKeyUsecase_GetSSHKey_Call) Run(run func(ctx context.Context, input dto.GetSingleSSHKeyInput)) *MockSshKeyUsecase_GetSSHKey_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.GetSingleSSHKeyInput))
	})
	return _c
}

func (_c *MockSshKeyUsecase_GetSSHKey_Call) Return(_a0 *dto.GetSingleSSHKeyOutput, _a1 error) *MockSshKeyUsecase_GetSSHKey_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockSshKeyUsecase_GetSSHKey_Call) RunAndReturn(run func(context.Context, dto.GetSingleSSHKeyInput) (*dto.GetSingleSSHKeyOutput, error)) *MockSshKeyUsecase_GetSSHKey_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockSshKeyUsecase creates a new instance of MockSshKeyUsecase. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockSshKeyUsecase(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockSshKeyUsecase {
	mock := &MockSshKeyUsecase{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
