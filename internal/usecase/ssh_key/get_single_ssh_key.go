package user_keys

import (
	"context"
	"errors"

	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"api-server/internal/dto"
	"api-server/pkg/oteltrace"
	"api-server/pkg/otelzap"
)

// GetSSHKey implements the SSHKeyUsecase interface for retrieving a single SSH key.
// It fetches the SSH key details from the database by its ID.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: GetSingleSSHKeyInput containing the SSH key ID
//
// Returns:
//   - *dto.GetSingleSSHKeyOutput: Output containing the SSH key details
//   - error: Any error that occurred during retrieval
func (u *impl) GetSSHKey(ctx context.Context, input dto.GetSingleSSHKeyInput) (*dto.GetSingleSSHKeyOutput, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.user_keys.GetSSHKey",
		trace.WithAttributes(
			attribute.String("action", "GET_SSH_KEY"),
			attribute.String("key_id", input.Id.String()),
		))
	defer span.End()

	otelzap.InfoWithContext(ctx, "retrieving ssh key",
		zap.String("action", "GET_SSH_KEY"),
		zap.String("key_id", input.Id.String()))

	sshKey, err := u.repository.GetSSHKeyByID(ctx, input.Id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			notFoundErr := dto.NewNotFoundError(errors.New("ssh key not found"))
			span.SetStatus(codes.Error, "ssh key not found")
			span.RecordError(notFoundErr)
			span.SetAttributes(attribute.String("status", "failed"))
			otelzap.ErrorWithContext(ctx, "ssh key not found", notFoundErr,
				zap.String("action", "GET_SSH_KEY"),
				zap.String("key_id", input.Id.String()),
				zap.String("status", "failed"))
			return nil, notFoundErr
		}
		span.SetStatus(codes.Error, "failed to get ssh key by id")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "failed to get ssh key by id", err,
			zap.String("action", "GET_SSH_KEY"),
			zap.String("key_id", input.Id.String()),
			zap.String("status", "failed"))
		return nil, err
	}

	span.AddEvent("SSH key retrieved successfully")
	span.SetStatus(codes.Ok, "SSH key retrieved successfully")
	span.SetAttributes(attribute.String("status", "success"))
	otelzap.InfoWithContext(ctx, "SSH key retrieved successfully",
		zap.String("action", "GET_SSH_KEY"),
		zap.String("key_id", input.Id.String()),
		zap.String("status", "success"))
	return &dto.GetSingleSSHKeyOutput{
		CreatedAt: sshKey.CreatedAt,
		UpdatedAt: sshKey.UpdatedAt,
		Id:        sshKey.ID,
		UserId:    sshKey.UserID,
		Title:     sshKey.Name,
		PublicKey: sshKey.PublicKey,
		ExpiresAt: sshKey.ExpiresAt,
		UsageType: sshKey.UsageType,
	}, nil

}
