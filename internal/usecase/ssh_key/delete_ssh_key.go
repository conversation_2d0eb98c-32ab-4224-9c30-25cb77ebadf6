package user_keys

import (
	"api-server/internal/gateways/gitlab"
	"context"
	"errors"

	"gorm.io/gorm"

	"api-server/internal/dto"
	"api-server/pkg/oteltrace"
	"api-server/pkg/otelzap"

	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"
)

// DeleteSSHKey implements the SSHKeyUsecase interface for deleting an SSH key.
// It handles the deletion of SSH keys from both the database and GitLab, including
// ownership verification and transaction management.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: DeleteSSHKeyInput containing the SSH key ID and user ID
//
// Returns:
//   - error: Any error that occurred during SSH key deletion
func (u *impl) DeleteSSHKey(ctx context.Context, input dto.DeleteSSHKeyInput) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.user_keys.DeleteSSHKey",
		trace.WithAttributes(
			attribute.String("action", "DELETE_SSH_KEY"),
			attribute.String("key_id", input.Id.String()),
		))
	defer span.End()

	otelzap.InfoWithContext(ctx, "deleting ssh key",
		zap.String("action", "DELETE_SSH_KEY"),
		zap.String("key_id", input.Id.String()))

	err := u.repository.Transaction(ctx, func(ctx context.Context) error {
		sshKey, err := u.repository.GetSSHKeyByID(ctx, input.Id)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				span.SetStatus(codes.Error, "ssh key not found")
				span.RecordError(err)
				span.SetAttributes(attribute.String("status", "failed"))
				otelzap.ErrorWithContext(ctx, "ssh key not found", err,
					zap.String("action", "DELETE_SSH_KEY"),
					zap.String("key_id", input.Id.String()),
					zap.String("status", "failed"))
				return dto.NewNotFoundError(errors.New("ssh key not found"))
			}
			span.SetStatus(codes.Error, "failed to get ssh key from db")
			span.RecordError(err)
			span.SetAttributes(attribute.String("status", "failed"))
			otelzap.ErrorWithContext(ctx, "failed to get ssh key from db", err,
				zap.String("action", "DELETE_SSH_KEY"),
				zap.String("key_id", input.Id.String()),
				zap.String("status", "failed"))
			return err
		}

		if sshKey.UserID != input.UserId {
			span.SetStatus(codes.Error, "user does not have this ssh key")
			span.RecordError(errors.New("user does not have this ssh key"))
			span.SetAttributes(attribute.String("status", "failed"))
			otelzap.ErrorWithContext(ctx, "user does not have this ssh key", errors.New("user does not have this ssh key"),
				zap.String("action", "DELETE_SSH_KEY"),
				zap.String("key_id", input.Id.String()),
				zap.String("status", "failed"))
			return dto.NewBadRequestError(errors.New("user does not have this ssh key"))
		}

		currentUser, err := u.repository.FindUserByID(ctx, input.UserId)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				span.SetStatus(codes.Error, "user not found")
				span.RecordError(err)
				span.SetAttributes(attribute.String("status", "failed"))
				otelzap.ErrorWithContext(ctx, "user not found", err,
					zap.String("action", "DELETE_SSH_KEY"),
					zap.String("key_id", input.Id.String()),
					zap.String("status", "failed"))
				return dto.NewNotFoundError(errors.New("user not found"))
			}
			span.SetStatus(codes.Error, "failed to find user in db")
			span.RecordError(err)
			span.SetAttributes(attribute.String("status", "failed"))
			otelzap.ErrorWithContext(ctx, "failed to find user in db", err,
				zap.String("action", "DELETE_SSH_KEY"),
				zap.String("key_id", input.Id.String()),
				zap.String("status", "failed"))
			return err
		}

		err = u.repository.DeleteById(ctx, sshKey, input.Id)
		if err != nil {
			span.SetStatus(codes.Error, "failed to delete ssh key from db")
			span.RecordError(err)
			span.SetAttributes(attribute.String("status", "failed"))
			otelzap.ErrorWithContext(ctx, "failed to delete ssh key from db", err,
				zap.String("action", "DELETE_SSH_KEY"),
				zap.String("key_id", input.Id.String()),
				zap.String("status", "failed"))
			return err
		}

		token := ""
		if currentUser.GitlabAccessToken != nil {
			token = *currentUser.GitlabAccessToken
		}

		requestDeleteKey := gitlab.DeleteSSHKeyRequest{
			KeyId: sshKey.RefGitSSHKeyID,
			Token: token,
		}

		err = u.gitlabClient.DeleteSSHKey(ctx, requestDeleteKey)
		if err != nil {
			span.SetStatus(codes.Error, "failed to delete ssh key from gitlab")
			span.RecordError(err)
			span.SetAttributes(attribute.String("status", "failed"))
			otelzap.ErrorWithContext(ctx, "failed to delete ssh key from gitlab", err,
				zap.String("action", "DELETE_SSH_KEY"),
				zap.String("key_id", input.Id.String()),
				zap.String("status", "failed"))
			return err
		}
		span.AddEvent("SSH key deleted successfully")
		span.SetStatus(codes.Ok, "SSH key deleted successfully")
		span.SetAttributes(attribute.String("status", "success"))
		otelzap.InfoWithContext(ctx, "SSH key deleted successfully",
			zap.String("action", "DELETE_SSH_KEY"),
			zap.String("key_id", input.Id.String()),
			zap.String("status", "success"))
		return nil
	})

	return err
}
