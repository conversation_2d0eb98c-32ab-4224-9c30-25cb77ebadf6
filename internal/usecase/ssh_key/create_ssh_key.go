package user_keys

import (
	"context"
	"time"

	"api-server/internal/dto"
	"api-server/internal/entities"
	"api-server/internal/gateways/gitlab"
	"api-server/internal/usecase"
	"api-server/pkg/oteltrace"
	"api-server/pkg/otelzap"

	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"
)

// CreateSSH<PERSON>ey implements the SSHKeyUsecase interface for creating a new SSH key.
// It handles the creation of SSH keys in both the database and GitLab, including duplicate checking
// and transaction management.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: CreateSSHKeyInput containing the SSH key details (title, key, usage type, expiration)
//
// Returns:
//   - error: Any error that occurred during SSH key creation
func (u *impl) CreateSSHKey(ctx context.Context, input dto.CreateSSHKeyInput) error { // nolint
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.user_keys.CreateSSHKey",
		trace.WithAttributes(
			attribute.String("action", "CREATE_SSH_KEY"),
			attribute.String("key_name", input.Title),
			// attribute.String("usage_type", input.UsageType), // UsageType is already in input.UsageType
		))
	defer span.End()

	otelzap.InfoWithContext(ctx, "creating ssh key",
		zap.String("action", "CREATE_SSH_KEY"),
		zap.String("key_name", input.Title))

	currentUser, err := u.repository.FindUserByID(ctx, input.UserId)
	if err != nil {
		span.SetStatus(codes.Error, "failed to find current user")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "failed to find current user", err,
			zap.String("action", "CREATE_SSH_KEY"),
			zap.String("key_name", input.Title),
			zap.String("status", "failed"))
		return err
	}

	user, err := u.repository.FindUserByID(ctx, input.UserId)
	if err != nil {
		span.SetStatus(codes.Error, "failed to find user")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "failed to find user", err,
			zap.String("action", "CREATE_SSH_KEY"),
			zap.String("key_name", input.Title),
			zap.String("status", "failed"))
		return err
	}
	// Check if the SSH key already exists
	isDuplicate, err := u.repository.IsDuplicateSSHKey(ctx, input.Key)
	if err != nil {
		span.SetStatus(codes.Error, "failed to check duplicated ssh key in db")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		return err
	}
	if isDuplicate {
		span.SetStatus(codes.Error, "duplicated SSH key")
		span.RecordError(err)
		return usecase.ErrDuplicatedSSHKey
	}

	err = u.repository.Transaction(ctx, func(ctx context.Context) error {
		sshKeyEntity := &entities.SSHKey{
			PublicKey: input.Key,
			Name:      input.Title,
			ExpiresAt: input.ExpiresAt,
			UsageType: input.UsageType,
			User:      user,
		}

		if err := u.repository.Create(ctx, sshKeyEntity); err != nil {
			span.SetStatus(codes.Error, "failed to create ssh key entity in db")
			span.RecordError(err)
			span.SetAttributes(attribute.String("status", "failed"))
			otelzap.ErrorWithContext(ctx, "failed to create ssh key entity in db", err,
				zap.String("action", "CREATE_SSH_KEY"),
				zap.String("key_name", input.Title),
				zap.String("status", "failed"))
			return err
		}

		accessToken := ""
		if currentUser.GitlabAccessToken != nil {
			accessToken = *currentUser.GitlabAccessToken
		}
		requestCreateSSHKey := gitlab.CreateSSHKeyRequest{
			UserId:    user.RefGitUserID,
			Title:     input.Title,
			Key:       input.Key,
			UsageType: input.UsageType,
			Token:     accessToken,
		}
		if input.ExpiresAt != nil {
			exp := input.ExpiresAt.Format(time.RFC3339)
			requestCreateSSHKey.ExpiresAt = &exp
		}

		outputCreateSSHKeys, err := u.gitlabClient.CreateSSHKey(ctx, requestCreateSSHKey)
		if err != nil {
			span.SetStatus(codes.Error, "failed to create ssh key in gitlab")
			span.RecordError(err)
			span.SetAttributes(attribute.String("status", "failed"))
			otelzap.ErrorWithContext(ctx, "failed to create ssh key in gitlab", err,
				zap.String("action", "CREATE_SSH_KEY"),
				zap.String("key_name", input.Title),
				zap.String("status", "failed"))
			return err
		}

		sshKeyEntity.RefGitSSHKeyID = outputCreateSSHKeys.Id
		if err := u.repository.Save(ctx, sshKeyEntity); err != nil {
			span.SetStatus(codes.Error, "failed to save ssh key entity in db after gitlab creation")
			span.RecordError(err)
			span.SetAttributes(attribute.String("status", "failed"))
			otelzap.ErrorWithContext(ctx, "failed to save ssh key entity in db after gitlab creation", err,
				zap.String("action", "CREATE_SSH_KEY"),
				zap.String("key_name", input.Title),
				zap.String("status", "failed"))
			return err
		}
		span.AddEvent("SSH key created successfully")
		span.SetStatus(codes.Ok, "SSH key created successfully")
		span.SetAttributes(attribute.String("status", "success"))
		otelzap.InfoWithContext(ctx, "SSH key created successfully",
			zap.String("action", "CREATE_SSH_KEY"),
			zap.String("key_name", input.Title),
			zap.String("status", "success"))
		return nil
	})
	return err
}
