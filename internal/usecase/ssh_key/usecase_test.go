package user_keys_test

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	"api-server/internal/dto"
	"api-server/internal/entities"
	"api-server/internal/gateways/gitlab"
	gitlab_mocks "api-server/internal/gateways/gitlab/mocks"
	repository_mocks "api-server/internal/repositories/mocks"
	"api-server/internal/usecase"
	user_keys "api-server/internal/usecase/ssh_key"
	"api-server/internal/utils"
)

func TestCreateSSHKey(t *testing.T) {
	uuid := uuid.New()
	type dependencies struct {
		repository   *repository_mocks.MockRepository
		gitlabClient *gitlab_mocks.MockGitlabClient
	}

	now := time.Now()
	tests := []struct {
		name      string
		ctx       context.Context
		input     dto.CreateSSHKeyInput
		mockFn    func(d *dependencies)
		expectErr error
	}{
		{
			name: "should return error when repository.FindUserByID return error",
			ctx:  utils.WithUserId(context.Background(), uuid),
			input: dto.CreateSSHKeyInput{
				UserRefId: 1,
				UserId:    uuid,
				Title:     "title",
				Key:       "",
				ExpiresAt: &now,
			},
			mockFn: func(d *dependencies) {
				d.repository.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), uuid))
				}).Return(errors.New("error"))
				d.repository.On("FindUserByID", mock.Anything, uuid).Return(nil, errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return error when IsDuplicatedSSHKey return error",
			ctx:  utils.WithUserId(context.Background(), uuid),
			input: dto.CreateSSHKeyInput{
				UserRefId: 1,
				UserId:    uuid,
				Title:     "title",
				Key:       "",
				ExpiresAt: &now,
			},
			mockFn: func(d *dependencies) {
				d.repository.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), uuid))
				}).Return(errors.New("error"))
				d.repository.On("FindUserByID", mock.Anything, uuid).Return(&entities.User{}, nil)
				d.repository.On("IsDuplicateSSHKey", mock.Anything, mock.Anything).Return(false, errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return error when key SSH duplicate",
			ctx:  utils.WithUserId(context.Background(), uuid),
			input: dto.CreateSSHKeyInput{
				UserRefId: 1,
				UserId:    uuid,
				Title:     "title",
				Key:       "",
				ExpiresAt: &now,
			},
			mockFn: func(d *dependencies) {
				d.repository.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), uuid))
				}).Return(usecase.ErrDuplicatedSSHKey)
				d.repository.On("FindUserByID", mock.Anything, uuid).Return(&entities.User{}, nil)
				d.repository.On("IsDuplicateSSHKey", mock.Anything, mock.Anything).Return(true, nil)
			},
			expectErr: usecase.ErrDuplicatedSSHKey,
		},
		{
			name: "should return error when repository.Create return error",
			ctx:  utils.WithUserId(context.Background(), uuid),
			input: dto.CreateSSHKeyInput{
				UserRefId: 1,
				UserId:    uuid,
				Title:     "title",
				Key:       "",
				ExpiresAt: &now,
			},
			mockFn: func(d *dependencies) {
				d.repository.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), uuid))
				}).Return(errors.New("error"))
				d.repository.On("FindUserByID", mock.Anything, uuid).Return(&entities.User{}, nil)
				d.repository.On("IsDuplicateSSHKey", mock.Anything, mock.Anything).Return(false, nil)
				d.repository.On("Create", mock.Anything, mock.Anything).Return(errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return error when gitlabClient.CreateSSHKey return error",
			ctx:  utils.WithUserId(context.Background(), uuid),
			input: dto.CreateSSHKeyInput{
				UserRefId: 1,
				UserId:    uuid,
				Title:     "title",
				Key:       "",
				ExpiresAt: &now,
			},
			mockFn: func(d *dependencies) {
				d.repository.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(context.Background())
				}).Return(errors.New("error"))
				d.repository.On("FindUserByID", mock.Anything, uuid).Return(&entities.User{}, nil)
				d.repository.On("IsDuplicateSSHKey", mock.Anything, mock.Anything).Return(false, nil)
				d.repository.On("Create", mock.Anything, mock.Anything).Return(nil)
				d.gitlabClient.On("CreateSSHKey", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return error when repository.Save return error",
			ctx:  utils.WithUserId(context.Background(), uuid),
			input: dto.CreateSSHKeyInput{
				UserRefId: 1,
				UserId:    uuid,
				Title:     "title",
				Key:       "",
				ExpiresAt: &now,
			},
			mockFn: func(d *dependencies) {
				d.repository.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(context.Background())
				}).Return(errors.New("error"))
				d.repository.On("FindUserByID", mock.Anything, uuid).Return(&entities.User{}, nil)
				d.repository.On("IsDuplicateSSHKey", mock.Anything, mock.Anything).Return(false, nil)
				d.repository.On("Create", mock.Anything, mock.Anything).Return(nil)
				d.gitlabClient.On("CreateSSHKey", mock.Anything, mock.Anything).Return(&gitlab.CreateSSHKeyResponse{Id: 1}, nil)
				d.repository.On("Save", mock.Anything, mock.Anything).Return(errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "success",
			ctx:  utils.WithUserId(context.Background(), uuid),
			input: dto.CreateSSHKeyInput{
				UserRefId: 1,
				UserId:    uuid,
				Title:     "title",
				Key:       "",
				ExpiresAt: &now,
			},
			mockFn: func(d *dependencies) {
				d.repository.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(context.Background())
				}).Return(nil)
				d.repository.On("FindUserByID", mock.Anything, uuid).Return(&entities.User{}, nil)
				d.repository.On("IsDuplicateSSHKey", mock.Anything, mock.Anything).Return(false, nil)
				d.repository.On("Create", mock.Anything, mock.Anything).Return(nil)
				d.gitlabClient.On("CreateSSHKey", mock.Anything, mock.Anything).Return(&gitlab.CreateSSHKeyResponse{Id: 1}, nil)
				d.repository.On("Save", mock.Anything, mock.Anything).Return(nil)
			},
			expectErr: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				repository:   &repository_mocks.MockRepository{},
				gitlabClient: &gitlab_mocks.MockGitlabClient{},
			}
			tt.mockFn(d)

			u := user_keys.New(d.repository, d.gitlabClient)
			err := u.CreateSSHKey(tt.ctx, tt.input)
			require.Equal(t, tt.expectErr, err)
		})
	}
}

func TestGetAllSSHKeys(t *testing.T) {
	type dependencies struct {
		repository *repository_mocks.MockRepository
	}

	userId := uuid.New()
	mockUserKeys := []entities.SSHKey{{Name: mock.Anything}}

	tests := []struct {
		name      string
		ctx       context.Context
		input     dto.GetSSHKeyInput
		mockFn    func(d *dependencies)
		expectErr error
	}{
		{
			name: "should return error when repository.CountAllSSHKeys return error",
			ctx:  utils.WithUserId(context.Background(), userId),
			input: dto.GetSSHKeyInput{
				UserId: userId,
			},
			mockFn: func(d *dependencies) {
				d.repository.On("CountAllSSHKeys", mock.Anything, mock.Anything).Return(int64(0), errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return error when repository.GetAllSSHKeys return error",
			ctx:  utils.WithUserId(context.Background(), userId),
			input: dto.GetSSHKeyInput{
				UserId: userId,
			},
			mockFn: func(d *dependencies) {
				d.repository.On("CountAllSSHKeys", mock.Anything, mock.Anything).Return(int64(1), nil)
				d.repository.On("ListSSHKeys", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "success",
			ctx:  utils.WithUserId(context.Background(), userId),
			input: dto.GetSSHKeyInput{
				UserId: userId,
			},
			mockFn: func(d *dependencies) {
				d.repository.On("CountAllSSHKeys", mock.Anything, mock.Anything).Return(int64(1), nil)
				d.repository.On("ListSSHKeys", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(mockUserKeys, nil)
			},
			expectErr: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				repository: &repository_mocks.MockRepository{},
			}
			tt.mockFn(d)

			u := user_keys.New(d.repository, nil)
			resp, err := u.GetAllSSHKeys(tt.ctx, tt.input)

			require.Equal(t, tt.expectErr, err)

			if err == nil {
				require.Equal(t, mockUserKeys[0].Name, (*resp.Data)[0].Title)
			}
		})
	}
}

func TestDeleteSSHKey(t *testing.T) {
	uuidInput := uuid.New()
	type dependencies struct {
		repository   *repository_mocks.MockRepository
		gitlabClient *gitlab_mocks.MockGitlabClient
	}

	tests := []struct {
		name      string
		ctx       context.Context
		input     dto.DeleteSSHKeyInput
		mockFn    func(d *dependencies)
		expectErr error
	}{
		{
			name: "should return error when repository.GetSSHKeyByID return error",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.DeleteSSHKeyInput{
				Id:     uuidInput,
				UserId: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repository.On("Transaction", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), uuidInput))
				}).Return(errors.New("error"))
				d.repository.On("GetSSHKeyByID", mock.Anything, uuidInput).Return(nil, errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return error when repository.FindUserByID return error",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.DeleteSSHKeyInput{
				Id:     uuidInput,
				UserId: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repository.On("Transaction", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), uuidInput))
				}).Return(errors.New("error"))
				d.repository.On("GetSSHKeyByID", mock.Anything, uuidInput).Return(&entities.SSHKey{
					UserID: uuidInput,
				}, nil)
				d.repository.On("FindUserByID", mock.Anything, uuidInput).Return(nil, errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return error when sshKey.UserID != input.CurrentUserId",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.DeleteSSHKeyInput{
				Id:     uuidInput,
				UserId: uuid.New(),
			},
			mockFn: func(d *dependencies) {
				d.repository.On("Transaction", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), uuidInput))
				}).Return(errors.New("user does not have this ssh key"))
				d.repository.On("GetSSHKeyByID", mock.Anything, uuidInput).Return(&entities.SSHKey{
					UserID: uuidInput,
				}, nil)
			},
			expectErr: errors.New("user does not have this ssh key"),
		},
		{
			name: "should return error when repository.DeleteById return error",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.DeleteSSHKeyInput{
				Id:     uuidInput,
				UserId: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repository.On("Transaction", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), uuidInput))
				}).Return(errors.New("error"))
				d.repository.On("GetSSHKeyByID", mock.Anything, uuidInput).Return(&entities.SSHKey{
					UserID: uuidInput,
				}, nil)
				d.repository.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{
					BaseModel: entities.BaseModel{ID: uuid.New()},
				}, nil)
				d.repository.On("DeleteById", mock.Anything, mock.Anything, uuidInput).Return(errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return error when gitlabClient.DeleteSSHKey return error",
			ctx:  utils.WithUserId(context.Background(), uuidInput), input: dto.DeleteSSHKeyInput{
				Id:     uuidInput,
				UserId: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repository.On("Transaction", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), uuidInput))
				}).Return(errors.New("error"))
				d.repository.On("GetSSHKeyByID", mock.Anything, uuidInput).Return(&entities.SSHKey{
					UserID: uuidInput,
				}, nil)
				d.repository.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{
					BaseModel: entities.BaseModel{ID: uuid.New()},
				}, nil)
				d.repository.On("DeleteById", mock.Anything, mock.Anything, uuidInput).Return(nil)
				d.gitlabClient.On("DeleteSSHKey", mock.Anything, mock.Anything).Return(errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "success",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.DeleteSSHKeyInput{
				Id:     uuidInput,
				UserId: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repository.On("Transaction", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), uuidInput))
				}).Return(nil)
				d.repository.On("GetSSHKeyByID", mock.Anything, uuidInput).Return(&entities.SSHKey{
					UserID: uuidInput,
				}, nil)
				d.repository.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{}, nil)
				d.repository.On("DeleteById", mock.Anything, mock.Anything, uuidInput).Return(nil)
				d.gitlabClient.On("DeleteSSHKey", mock.Anything, mock.Anything).Return(nil)
			},
			expectErr: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				repository:   &repository_mocks.MockRepository{},
				gitlabClient: &gitlab_mocks.MockGitlabClient{},
			}
			tt.mockFn(d)

			u := user_keys.New(d.repository, d.gitlabClient)
			err := u.DeleteSSHKey(tt.ctx, tt.input)
			require.Equal(t, tt.expectErr, err)
		})
	}
}

func TestGetSingleSSHKey(t *testing.T) {
	uuidInput := uuid.New()
	now := time.Now()
	type dependencies struct {
		repository *repository_mocks.MockRepository
	}

	tests := []struct {
		name      string
		ctx       context.Context
		input     dto.GetSingleSSHKeyInput
		mockFn    func(d *dependencies)
		exOutput  *dto.GetSingleSSHKeyOutput
		expectErr error
	}{
		{
			name: "should return error when repository.GetSSHKeyByID return error",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.GetSingleSSHKeyInput{
				Id:     uuidInput,
				UserId: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repository.On("GetSSHKeyByID", mock.Anything, uuidInput).Return(nil, errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "success",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.GetSingleSSHKeyInput{
				Id:     uuidInput,
				UserId: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repository.On("GetSSHKeyByID", mock.Anything, uuidInput).Return(&entities.SSHKey{
					UserID:    uuidInput,
					ExpiresAt: &now,
				}, nil)
			},
			expectErr: nil,
			exOutput: &dto.GetSingleSSHKeyOutput{
				UserId:    uuidInput,
				ExpiresAt: &now,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				repository: &repository_mocks.MockRepository{},
			}
			tt.mockFn(d)

			u := user_keys.New(d.repository, nil)
			output, err := u.GetSSHKey(tt.ctx, tt.input)

			require.Equal(t, tt.exOutput, output)
			require.Equal(t, tt.expectErr, err)

		})
	}
}
