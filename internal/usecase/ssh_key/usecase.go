package user_keys

import (
	"context"

	"api-server/internal/dto"
	"api-server/internal/gateways/gitlab"
	repository "api-server/internal/repositories"
)

// SshKeyUsecase defines the interface for managing SSH keys.
// It provides methods for creating, deleting, and retrieving SSH keys for users.
type SshKeyUsecase interface {
	// Create<PERSON><PERSON><PERSON><PERSON> creates a new SSH key for a user.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - input: The input data for creating the SSH key
	//
	// Returns:
	//   - error: Any error that occurred during creation
	Create<PERSON>HKey(ctx context.Context, input dto.CreateSSHKeyInput) error

	// DeleteSS<PERSON><PERSON><PERSON> deletes an existing SSH key.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - input: The input data for deleting the SSH key
	//
	// Returns:
	//   - error: Any error that occurred during deletion
	DeleteSSHKey(ctx context.Context, input dto.DeleteSSHKeyInput) error

	// GetAllSSHKeys retrieves all SSH keys for a user.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - input: The input data for retrieving SSH keys
	//
	// Returns:
	//   - *dto.GetSSHKeyOutput: The list of SSH keys
	//   - error: Any error that occurred during retrieval
	GetAllSSHKeys(ctx context.Context, input dto.GetSSHKeyInput) (*dto.GetSSHKeyOutput, error)

	// GetSSHKey retrieves a specific SSH key.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - input: The input data for retrieving the SSH key
	//
	// Returns:
	//   - *dto.GetSingleSSHKeyOutput: The SSH key information
	//   - error: Any error that occurred during retrieval
	GetSSHKey(ctx context.Context, input dto.GetSingleSSHKeyInput) (*dto.GetSingleSSHKeyOutput, error)
}

var _ SshKeyUsecase = (*impl)(nil)

type impl struct {
	repository   repository.Repository
	gitlabClient gitlab.GitlabClient
}

func New(repository repository.Repository, gitlabClient gitlab.GitlabClient) *impl {
	return &impl{
		repository:   repository,
		gitlabClient: gitlabClient,
	}
}
