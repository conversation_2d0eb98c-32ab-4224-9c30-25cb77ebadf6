package user_keys

import (
	"context"

	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"

	"api-server/internal/dto"
	"api-server/internal/entities"
	repository "api-server/internal/repositories"
	"api-server/internal/types"
	"api-server/pkg/oteltrace"
	"api-server/pkg/otelzap"
)

// GetAllSSHKeys implements the SSHKeyUsecase interface for retrieving a list of SSH keys.
// It fetches SSH keys with pagination, sorting, and filtering capabilities.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: GetSSHKeyInput containing pagination parameters and user ID
//
// Returns:
//   - *dto.GetSSHKeyOutput: Output containing the list of SSH keys and pagination info
//   - error: Any error that occurred during retrieval
func (u *impl) GetAllSSHKeys(ctx context.Context, input dto.GetSSHKeyInput) (*dto.GetSSHKeyOutput, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.user_keys.GetAllSSHKeys",
		trace.WithAttributes(
			attribute.String("action", "GET_ALL_SSH_KEYS"),
			attribute.Int("page_no", input.Paginate.Page),
			attribute.Int("page_size", input.Paginate.PerPage),
			attribute.String("order_by", string(input.Paginate.OrderBy)),
			attribute.String("sort_direction", string(input.Paginate.Sort)),
		))
	defer span.End()

	otelzap.InfoWithContext(ctx, "retrieving all ssh keys",
		zap.String("action", "GET_ALL_SSH_KEYS"),
		zap.Int("page_no", input.Paginate.Page),
		zap.Int("page_size", input.Paginate.PerPage),
		zap.String("order_by", string(input.Paginate.OrderBy)),
		zap.String("sort_direction", string(input.Paginate.Sort)))

	query := repository.GetSSHKeyQuery{
		UserId: input.UserId,
	}

	span.AddEvent("count all SSH keys")
	count, err := u.repository.CountAllSSHKeys(ctx, query)
	if err != nil {
		span.SetStatus(codes.Error, "failed to count all ssh keys")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "failed to count all ssh keys", err,
			zap.String("action", "GET_ALL_SSH_KEYS"),
			zap.String("status", "failed"))
		return nil, err
	}

	span.AddEvent("get all SSH keys")
	orderBy := types.OrderBy{input.Paginate.OrderBy: input.Paginate.Sort}
	keys, err := u.repository.ListSSHKeys(ctx, input.Paginate.Page, input.Paginate.PerPage, orderBy, query)
	if err != nil {
		span.SetStatus(codes.Error, "failed to list ssh keys")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "failed to list ssh keys", err,
			zap.String("action", "GET_ALL_SSH_KEYS"),
			zap.String("status", "failed"))
		return nil, err
	}

	data := dto.FromManyEntities[entities.SSHKey, dto.SSHKey](keys)
	output := &dto.GetSSHKeyOutput{
		Data: &data,
		Pagination: &dto.Pagination{
			Total:    int(count),
			PageNo:   input.Paginate.Page,
			PageSize: input.Paginate.PerPage,
		},
	}

	span.AddEvent("SSH keys retrieved successfully")
	span.SetStatus(codes.Ok, "SSH keys retrieved successfully")
	span.SetAttributes(attribute.String("status", "success"))
	otelzap.InfoWithContext(ctx, "SSH keys retrieved successfully",
		zap.String("action", "GET_ALL_SSH_KEYS"),
		zap.String("status", "success"))
	return output, nil
}
