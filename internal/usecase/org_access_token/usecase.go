package org_access_token

import (
	"context"
	"errors"
	"time"

	"github.com/google/uuid"
	gonanoid "github.com/matoous/go-nanoid/v2"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"api-server/internal/dto"
	"api-server/internal/entities"
	"api-server/internal/enums"
	repository "api-server/internal/repositories"
	"api-server/internal/types"
	"api-server/internal/usecase"
	"api-server/pkg/oteltrace"
	"api-server/pkg/otelzap"
)

const NANOID_KEY_LENGTH = 36

// OrgAccessTokenUsecase defines the interface for managing organization access tokens.
// It provides methods for creating, listing, verifying, and managing organization access tokens.
type OrgAccessTokenUsecase interface {
	// CreateOrgAccessToken creates a new access token for an organization.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - req: The request data for creating the token
	//
	// Returns:
	//   - *dto.CreateOrgAccessTokenResponse: The created token information
	//   - error: Any error that occurred during creation
	CreateOrgAccessToken(ctx context.Context, req dto.CreateOrgAccessTokenRequest) (*dto.CreateOrgAccessTokenResponse, error)

	// ListOrgAccessToken retrieves a list of access tokens for an organization.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - req: The request data for listing tokens
	//
	// Returns:
	//   - *dto.ListOrgAccessTokenResponse: The list of token information
	//   - error: Any error that occurred during retrieval
	ListOrgAccessToken(ctx context.Context, req dto.ListOrgAccessTokenRequest) (*dto.ListOrgAccessTokenResponse, error)

	// GetOrgAccessToken retrieves information about a specific organization access token.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - accessToken: The access token to retrieve
	//
	// Returns:
	//   - *dto.OrgAccessToken: The token information
	//   - error: Any error that occurred during retrieval
	GetOrgAccessToken(ctx context.Context, accessToken string) (*dto.OrgAccessToken, error)

	// DeleteOrgAccessToken deletes an organization access token.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - userId: The ID of the user performing the deletion
	//   - orgID: The ID of the organization
	//   - accessTokenID: The ID of the token to delete
	//
	// Returns:
	//   - error: Any error that occurred during deletion
	DeleteOrgAccessToken(ctx context.Context, userId uuid.UUID, orgID uuid.UUID, accessTokenID uuid.UUID) error

	// VerifyOrgAccessToken verifies the validity of an organization access token.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - token: The token to verify
	//
	// Returns:
	//   - *dto.VerifyOrgAccessTokenResponse: The verification result
	//   - error: Any error that occurred during verification
	VerifyOrgAccessToken(ctx context.Context, token string) (*dto.VerifyOrgAccessTokenResponse, error)
}

type impl struct {
	repository repository.Repository
}

var _ OrgAccessTokenUsecase = (*impl)(nil)

// New creates a new instance of the organization access token usecase implementation.
// It initializes the usecase with the required repository dependency.
//
// Parameters:
//   - repository: Repository interface for organization access token data access
//
// Returns:
//   - *impl: New instance of the organization access token usecase
func New(repository repository.Repository) *impl {
	return &impl{
		repository: repository,
	}
}

func (i *impl) ListOrgAccessToken(ctx context.Context, req dto.ListOrgAccessTokenRequest) (*dto.ListOrgAccessTokenResponse, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.access_token.ListOrgAccessToken",
		trace.WithAttributes(
			attribute.String("action", "LIST_ACCESS_TOKENS"),
			attribute.String("org_id", req.OrgID.String()),
		))
	defer span.End()

	otelzap.InfoWithContext(ctx, "listing access tokens",
		zap.String("action", "LIST_ACCESS_TOKENS"),
		zap.String("org_id", req.OrgID.String()))

	pred := repository.ListOrgAccessTokenQuery{
		OrgID: req.OrgID,
	}

	total, err := i.repository.CountOrgAccessToken(ctx, pred)
	if err != nil {
		span.SetStatus(codes.Error, "repository count access token failed")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "repository count access token failed", err,
			zap.String("action", "LIST_ACCESS_TOKENS"),
			zap.String("org_id", req.OrgID.String()),
			zap.String("status", "failed"))
		return nil, usecase.ErrInternal
	}

	pagination := types.Pagination{
		PageNo:   req.Paginate.Page,
		PageSize: req.Paginate.PerPage,
	}
	orderBy := types.OrderBy{req.Paginate.OrderBy: req.Paginate.Sort}
	resp, err := i.repository.ListOrgAccessToken(ctx, pagination, orderBy, pred)
	if err != nil {
		span.SetStatus(codes.Error, "repository list access token failed")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "repository list access token failed", err,
			zap.String("action", "LIST_ACCESS_TOKENS"),
			zap.String("org_id", req.OrgID.String()),
			zap.String("status", "failed"))
		return nil, usecase.ErrInternal
	}

	data := dto.FromManyEntities[entities.OrgToken, dto.OrgAccessToken](resp)
	result := dto.ListOrgAccessTokenResponse{
		Data: &data,
		Pagination: &dto.Pagination{
			Total:    int(total),
			PageNo:   pagination.PageNo,
			PageSize: pagination.PageSize,
		},
	}

	span.AddEvent("access tokens listed successfully")
	span.SetStatus(codes.Ok, "access tokens listed successfully")
	span.SetAttributes(attribute.String("status", "success"))
	otelzap.InfoWithContext(ctx, "access tokens listed successfully",
		zap.String("action", "LIST_ACCESS_TOKENS"),
		zap.String("org_id", req.OrgID.String()),
		zap.String("status", "success"))
	return &result, nil
}

func (i *impl) GetOrgAccessToken(ctx context.Context, accessToken string) (*dto.OrgAccessToken, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.access_token.GetOrgAccessToken",
		trace.WithAttributes(
			attribute.String("action", "GET_ACCESS_TOKEN"),
			attribute.String("access_token", accessToken),
		))
	defer span.End()

	otelzap.InfoWithContext(ctx, "getting access token",
		zap.String("action", "GET_ACCESS_TOKEN"),
		zap.String("access_token", accessToken))

	resp, err := i.repository.FindOrgAccessToken(ctx, repository.FindOrgAccessTokenQuery{
		OrgAccessToken: accessToken,
		Revoked:        false,
	})
	if err != nil {
		span.SetStatus(codes.Error, "repository find access token failed")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "repository find access token failed", err,
			zap.String("action", "GET_ACCESS_TOKEN"),
			zap.String("access_token", accessToken),
			zap.String("status", "failed"))
		return nil, usecase.ErrInternal
	}

	var result dto.OrgAccessToken
	result = result.FromEntity(*resp)

	span.AddEvent("access token retrieved successfully")
	span.SetStatus(codes.Ok, "access token retrieved successfully")
	span.SetAttributes(attribute.String("status", "success"))
	otelzap.InfoWithContext(ctx, "access token retrieved successfully",
		zap.String("action", "GET_ACCESS_TOKEN"),
		zap.String("access_token", accessToken),
		zap.String("status", "success"))
	return &result, nil
}

func (i *impl) CreateOrgAccessToken(ctx context.Context, req dto.CreateOrgAccessTokenRequest) (*dto.CreateOrgAccessTokenResponse, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.access_token.CreateOrgAccessToken",
		trace.WithAttributes(
			attribute.String("action", "CREATE_ACCESS_TOKEN"),
			attribute.String("org_id", req.OrgID.String()),
		))
	defer span.End()
	// query organization by id
	_, err := i.repository.FindOrganizationByID(ctx, req.OrgID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, usecase.ErrOrganizationNotFound
		}
		span.SetStatus(codes.Error, "failed to find organization")
		span.RecordError(err)
		return nil, err
	}
	// check if user is org owner or developer
	if err := checkMemberPermission(ctx, *i, span, req.OrgID, req.UserID); err != nil {
		span.SetStatus(codes.Error, "no permission")
		span.RecordError(err)
		return nil, err
	}

	otelzap.InfoWithContext(ctx, "creating access token",
		zap.String("action", "CREATE_ACCESS_TOKEN"),
		zap.String("org_id", req.OrgID.String()))

	accessToken, err := gonanoid.New(NANOID_KEY_LENGTH)
	if err != nil {
		span.SetStatus(codes.Error, "nanoid generate failed")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "nanoid generate failed", err,
			zap.String("action", "CREATE_ACCESS_TOKEN"),
			zap.String("org_id", req.OrgID.String()),
			zap.String("status", "failed"))
		return nil, err
	}

	resp, err := i.repository.CreateOrgAccessToken(ctx, repository.CreateOrgAccessTokenInput{
		Name:           req.Name,
		OrgAccessToken: accessToken,
		Scopes:         req.Scopes,
		OrgID:          req.OrgID,
		ExpiresAt:      req.ExpiresAt,
	})
	if err != nil {
		span.SetStatus(codes.Error, "repository create access token failed")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "repository create access token failed", err,
			zap.String("action", "CREATE_ACCESS_TOKEN"),
			zap.String("org_id", req.OrgID.String()),
			zap.String("status", "failed"))
		return nil, usecase.ErrInternal
	}

	var data dto.OrgAccessToken
	data = data.FromEntity(*resp)
	data.AccessToken = resp.AccessToken
	result := dto.CreateOrgAccessTokenResponse{
		Data: &data,
	}

	span.AddEvent("access token created successfully")
	span.SetStatus(codes.Ok, "access token created successfully")
	span.SetAttributes(attribute.String("status", "success"))
	otelzap.InfoWithContext(ctx, "access token created successfully",
		zap.String("action", "CREATE_ACCESS_TOKEN"),
		zap.String("org_id", req.OrgID.String()),
		zap.String("status", "success"))
	return &result, nil
}

func (i *impl) DeleteOrgAccessToken(ctx context.Context, userId uuid.UUID, orgID uuid.UUID, accessTokenID uuid.UUID) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.access_token.DeleteOrgAccessToken",
		trace.WithAttributes(
			attribute.String("action", "DELETE_ACCESS_TOKEN"),
			attribute.String("access_token_id", accessTokenID.String()),
			attribute.String("org_id", orgID.String()),
		))
	defer span.End()
	// query organization by id
	_, err := i.repository.FindOrganizationByID(ctx, orgID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return usecase.ErrOrganizationNotFound
		}
		span.SetStatus(codes.Error, "failed to find organization")
		span.RecordError(err)
		return err
	}
	// check if user is org owner or developer
	if err := checkMemberPermission(ctx, *i, span, orgID, userId); err != nil {
		span.SetStatus(codes.Error, "no permission")
		span.RecordError(err)
		return err
	}

	otelzap.InfoWithContext(ctx, "deleting access token",
		zap.String("action", "DELETE_ACCESS_TOKEN"),
		zap.String("access_token_id", accessTokenID.String()),
		zap.String("org_id", orgID.String()))

	if err := i.repository.DeleteOrgAccessToken(ctx, repository.DeleteOrgAccessTokenInput{
		OrgAccessTokenID: accessTokenID,
		OrgID:            orgID,
	}); err != nil {
		span.SetStatus(codes.Error, "repository delete access token failed")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "repository delete access token failed", err,
			zap.String("action", "DELETE_ACCESS_TOKEN"),
			zap.String("access_token_id", accessTokenID.String()),
			zap.String("org_id", orgID.String()),
			zap.String("status", "failed"))
		return usecase.ErrInternal
	}

	span.AddEvent("access token deleted successfully")
	span.SetStatus(codes.Ok, "access token deleted successfully")
	span.SetAttributes(attribute.String("status", "success"))
	otelzap.InfoWithContext(ctx, "access token deleted successfully",
		zap.String("action", "DELETE_ACCESS_TOKEN"),
		zap.String("access_token_id", accessTokenID.String()),
		zap.String("org_id", orgID.String()),
		zap.String("status", "success"))
	return nil
}

func (i *impl) VerifyOrgAccessToken(ctx context.Context, token string) (*dto.VerifyOrgAccessTokenResponse, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.access_token.VerifyOrgAccessToken",
		trace.WithAttributes(
			attribute.String("action", "VERIFY_ACCESS_TOKEN"),
			attribute.String("access_token", token),
		))
	defer span.End()

	otelzap.InfoWithContext(ctx, "verifying access token",
		zap.String("action", "VERIFY_ACCESS_TOKEN"),
		zap.String("access_token", token))

	accessToken, err := i.repository.FindOrgAccessToken(ctx, repository.FindOrgAccessTokenQuery{
		OrgAccessToken: token,
		Revoked:        false,
	})
	if err != nil {
		span.AddEvent("invalid access token")
		span.SetStatus(codes.Ok, "invalid access token") // Still OK status as per original code, but event added
		span.SetAttributes(attribute.String("status", "invalid"))
		otelzap.DebugWithContext(ctx, "invalid access token",
			zap.String("action", "VERIFY_ACCESS_TOKEN"),
			zap.String("access_token", token),
			zap.String("status", "invalid"))
		return nil, usecase.ErrInvalidToken
	}

	// Check if token has expired
	if accessToken.ExpiresAt != nil && time.Now().After(*accessToken.ExpiresAt) {
		span.AddEvent("expired access token")
		span.SetStatus(codes.Ok, "expired access token") // Still OK status as per original code, but event added
		span.SetAttributes(attribute.String("status", "expired"))
		otelzap.DebugWithContext(ctx, "expired access token",
			zap.String("action", "VERIFY_ACCESS_TOKEN"),
			zap.String("access_token", token),
			zap.String("status", "expired"))
		return nil, usecase.ErrTokenExpired
	}

	span.AddEvent("access token verified successfully")
	span.SetStatus(codes.Ok, "access token verified successfully")
	span.SetAttributes(attribute.String("status", "success"))
	otelzap.InfoWithContext(ctx, "access token verified successfully",
		zap.String("action", "VERIFY_ACCESS_TOKEN"),
		zap.String("access_token", token),
		zap.String("status", "success"))
	return &dto.VerifyOrgAccessTokenResponse{
		Valid: true,
	}, nil
}

// checkMemberPermission verifies if a user has sufficient permissions to manage organization access tokens.
// It checks if the user is either an organization owner or developer.
// The function includes OpenTelemetry tracing for monitoring the permission check.
//
// Parameters:
//   - ctx: Context for the operation
//   - u: The usecase implementation
//   - span: OpenTelemetry span for tracing
//   - orgID: ID of the organization
//   - memberUserID: ID of the user to check permissions for
//
// Returns:
//   - error: Any error that occurred during permission check, including ErrNoPermission if user lacks required role
func checkMemberPermission(ctx context.Context, u impl, span trace.Span, orgID, memberUserID uuid.UUID) error {
	userOrg, err := u.repository.FindOrgMember(ctx, repository.FilterOrgMember{
		OrgID:  &orgID,
		UserID: &memberUserID,
	})
	if err != nil && err != gorm.ErrRecordNotFound {
		span.SetStatus(codes.Error, "failed to find org member")
		span.RecordError(err)
		return err
	}
	// Allow only org owner and developer to create access token
	if userOrg != nil && (userOrg.Role != enums.OrgRole_Developer && userOrg.Role != enums.OrgRole_Owner) {
		span.SetStatus(codes.Error, usecase.ErrNoPermission.Error())
		span.RecordError(usecase.ErrNoPermission)
		return usecase.ErrNoPermission
	}

	return nil
}
