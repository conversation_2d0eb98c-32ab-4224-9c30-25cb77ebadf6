package org_access_token_test

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"gorm.io/gorm"

	"api-server/internal/dto"
	"api-server/internal/entities"
	"api-server/internal/enums"
	repository "api-server/internal/repositories"
	repository_mocks "api-server/internal/repositories/mocks"
	"api-server/internal/usecase"
	"api-server/internal/usecase/org_access_token"
)

func TestCreateOrgOrgAccessToken(t *testing.T) {
	mockOrgID := uuid.New()

	testcases := []struct {
		name      string
		mockFn    func(r *repository_mocks.MockRepository)
		expectErr error
	}{
		{
			name: "should return usecase.ErrInternal if FindOrganizationByID return error",
			mockFn: func(r *repository_mocks.MockRepository) {
				r.On("FindOrganizationByID", mock.Anything, mock.Anything).Return(nil, usecase.ErrInternal)
			},
			expectErr: usecase.ErrInternal,
		},
		{
			name: "should return usecase.ErrInternal if FindOrgMember return error",
			mockFn: func(r *repository_mocks.MockRepository) {
				r.On("FindOrganizationByID", mock.Anything, mock.Anything).Return(&entities.Organization{}, nil)
				r.On("FindOrgMember", mock.Anything, mock.Anything).Return(nil, usecase.ErrInternal)
			},
			expectErr: usecase.ErrInternal,
		},
		{
			name: "should return usecase.ErrInternal if CreateOrgAccessToken return error",
			mockFn: func(r *repository_mocks.MockRepository) {
				r.On("FindOrganizationByID", mock.Anything, mock.Anything).Return(&entities.Organization{}, nil)
				r.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil)
				r.On("CreateOrgAccessToken", mock.Anything, mock.Anything).Return(nil, errors.New("error"))

			},
			expectErr: usecase.ErrInternal,
		},
		{
			name: "should pass",
			mockFn: func(r *repository_mocks.MockRepository) {
				r.On("FindOrganizationByID", mock.Anything, mock.Anything).Return(&entities.Organization{}, nil)
				r.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil)
				r.On("CreateOrgAccessToken", mock.Anything, mock.Anything).Return(&entities.OrgToken{
					Name:        "Name",
					AccessToken: "GitlabOrgAccessToken",
					Revoked:     false,
					OrgID:       mockOrgID,
					ExpiresAt:   nil,
				}, nil)
			},
			expectErr: nil,
		},
	}

	for _, testcase := range testcases {
		t.Run(testcase.name, func(t *testing.T) {
			r := &repository_mocks.MockRepository{}
			testcase.mockFn(r)

			usecase := org_access_token.New(r)
			_, err := usecase.CreateOrgAccessToken(context.Background(), dto.CreateOrgAccessTokenRequest{
				Name:      mock.Anything,
				ExpiresAt: nil,
				UserID:    mockOrgID,
			})

			// assertion
			require.Equal(t, testcase.expectErr, err)
		})
	}
}

func TestListOrgAccessToken(t *testing.T) {
	mockOrgID := uuid.New()

	testcases := []struct {
		name      string
		mockFn    func(r *repository_mocks.MockRepository)
		expectErr error
	}{
		{
			name: "should return usecase.ErrInternal if CountOrgAccessToken return error",
			mockFn: func(r *repository_mocks.MockRepository) {
				r.On("CountOrgAccessToken", mock.Anything, mock.Anything).Return(int64(0), errors.New("error"))
			},
			expectErr: usecase.ErrInternal,
		},
		{
			name: "should return usecase.ErrInternal if ListOrgAccessToken return error",
			mockFn: func(r *repository_mocks.MockRepository) {
				r.On("CountOrgAccessToken", mock.Anything, mock.Anything).Return(int64(0), nil)
				r.On("ListOrgAccessToken", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectErr: usecase.ErrInternal,
		},
		{
			name: "should pass",
			mockFn: func(r *repository_mocks.MockRepository) {
				r.On("CountOrgAccessToken", mock.Anything, mock.Anything).Return(int64(0), nil)
				r.On("ListOrgAccessToken", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]entities.OrgToken{}, nil)
			},
			expectErr: nil,
		},
	}

	for _, testcase := range testcases {
		t.Run(testcase.name, func(t *testing.T) {
			r := &repository_mocks.MockRepository{}
			testcase.mockFn(r)

			usecase := org_access_token.New(r)
			_, err := usecase.ListOrgAccessToken(context.Background(), dto.ListOrgAccessTokenRequest{
				UserID:   mockOrgID,
				Paginate: dto.PaginateRequest{},
			})

			// assertion
			require.Equal(t, testcase.expectErr, err)
		})
	}
}

func TestDeleteOrgAccessToken(t *testing.T) {
	mockUserID := uuid.New()
	mockOrgId := uuid.New()
	mockOrgAccessTokenID := uuid.New()

	testcases := []struct {
		name      string
		mockFn    func(r *repository_mocks.MockRepository)
		expectErr error
	}{
		{
			name: "should return usecase.ErrInternal if FindOrganizationByID return error",
			mockFn: func(r *repository_mocks.MockRepository) {
				r.On("FindOrganizationByID", mock.Anything, mock.Anything).Return(nil, usecase.ErrInternal)
			},
			expectErr: usecase.ErrInternal,
		},
		{
			name: "should return usecase.ErrInternal if FindOrgMember return error",
			mockFn: func(r *repository_mocks.MockRepository) {
				r.On("FindOrganizationByID", mock.Anything, mock.Anything).Return(&entities.Organization{}, nil)
				r.On("FindOrgMember", mock.Anything, mock.Anything).Return(nil, usecase.ErrInternal)
			},
			expectErr: usecase.ErrInternal,
		},
		{
			name: "should return usecase.ErrInternal if DeleteOrgAccessToken return error",
			mockFn: func(r *repository_mocks.MockRepository) {
				r.On("FindOrganizationByID", mock.Anything, mock.Anything).Return(&entities.Organization{}, nil)
				r.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil)
				r.On("DeleteOrgAccessToken", mock.Anything, mock.Anything, mock.Anything).Return(errors.New("error"))
			},
			expectErr: usecase.ErrInternal,
		},
		{
			name: "should pass",
			mockFn: func(r *repository_mocks.MockRepository) {
				r.On("FindOrganizationByID", mock.Anything, mock.Anything).Return(&entities.Organization{}, nil)
				r.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil)
				r.On("DeleteOrgAccessToken", mock.Anything, mock.Anything, mock.Anything).Return(nil)
			},
			expectErr: nil,
		},
	}

	for _, testcase := range testcases {
		t.Run(testcase.name, func(t *testing.T) {
			r := &repository_mocks.MockRepository{}
			testcase.mockFn(r)

			usecase := org_access_token.New(r)
			err := usecase.DeleteOrgAccessToken(context.Background(), mockUserID, mockOrgId, mockOrgAccessTokenID)

			// assertion
			require.Equal(t, testcase.expectErr, err)
		})
	}
}

func TestGetOrgAccessToken(t *testing.T) {
	mockOrgAccessToken := "test_token_string"
	mockOrgID := uuid.New()

	testcases := []struct {
		name      string
		mockFn    func(r *repository_mocks.MockRepository)
		expectErr error
	}{
		{
			name: "should return usecase.ErrInternal if FindOrgAccessToken returns an error",
			mockFn: func(r *repository_mocks.MockRepository) {
				r.On("FindOrgAccessToken", mock.Anything, mock.Anything).Return(nil, errors.New("db error"))
			},
			expectErr: usecase.ErrInternal,
		},
		{
			name: "should pass and return access token data",
			mockFn: func(r *repository_mocks.MockRepository) {
				r.On("FindOrgAccessToken", mock.Anything, mock.Anything).Return(&entities.OrgToken{
					BaseModel:   entities.BaseModel{ID: uuid.New()},
					Name:        "TestToken",
					AccessToken: mockOrgAccessToken,
					Revoked:     false,
					OrgID:       mockOrgID,
					ExpiresAt:   nil,
				}, nil)
			},
			expectErr: nil,
		},
	}

	for _, testcase := range testcases {
		t.Run(testcase.name, func(t *testing.T) {
			r := &repository_mocks.MockRepository{}
			testcase.mockFn(r)

			uc := org_access_token.New(r)
			_, err := uc.GetOrgAccessToken(context.Background(), mockOrgAccessToken)

			// assertion
			require.Equal(t, testcase.expectErr, err)
		})
	}
}

func TestVerifyOrgAccessToken(t *testing.T) {
	mockTokenString := "valid_token_string"
	validExpiresAt := time.Now().Add(time.Hour * 24)
	expiredExpiresAt := time.Now().Add(-time.Hour * 24)

	testcases := []struct {
		name                  string
		mockFn                func(r *repository_mocks.MockRepository)
		expectedResponseValid bool
		expectErr             error
	}{
		{
			name: "should return valid true for a valid token",
			mockFn: func(r *repository_mocks.MockRepository) {
				r.On("FindOrgAccessToken", mock.Anything, mock.MatchedBy(func(query repository.FindOrgAccessTokenQuery) bool {
					return query.OrgAccessToken == mockTokenString && !query.Revoked
				})).Return(&entities.OrgToken{
					AccessToken: mockTokenString,
					OrgID:       uuid.New(),
					Revoked:     false,
					ExpiresAt:   &validExpiresAt,
				}, nil)
			},
			expectedResponseValid: true,
			expectErr:             nil,
		},
		{
			name: "should return ErrRecordNotFound if FindOrgAccessToken returns gorm.ErrRecordNotFound",
			mockFn: func(r *repository_mocks.MockRepository) {
				r.On("FindOrgAccessToken", mock.Anything, mock.MatchedBy(func(query repository.FindOrgAccessTokenQuery) bool {
					return query.OrgAccessToken == mockTokenString && !query.Revoked
				})).Return(nil, gorm.ErrRecordNotFound)
			},
			expectedResponseValid: false, // DTO will have Valid: false by default if response is nil
			expectErr:             usecase.ErrInvalidToken,
		},
		{
			name: "should return usecase.ErrInternal if FindOrgAccessToken returns a generic error",
			mockFn: func(r *repository_mocks.MockRepository) {
				r.On("FindOrgAccessToken", mock.Anything, mock.MatchedBy(func(query repository.FindOrgAccessTokenQuery) bool {
					return query.OrgAccessToken == mockTokenString && !query.Revoked
				})).Return(nil, errors.New("random db error"))
			},
			expectedResponseValid: false,
			expectErr:             usecase.ErrInvalidToken,
		},
		{
			name: "should return ErrTokenExpired if token is expired",
			mockFn: func(r *repository_mocks.MockRepository) {
				r.On("FindOrgAccessToken", mock.Anything, mock.MatchedBy(func(query repository.FindOrgAccessTokenQuery) bool {
					return query.OrgAccessToken == mockTokenString && !query.Revoked
				})).Return(&entities.OrgToken{
					AccessToken: mockTokenString,
					OrgID:       uuid.New(),
					Revoked:     false,
					ExpiresAt:   &expiredExpiresAt, // This token is expired
				}, nil)
			},
			expectedResponseValid: false,
			expectErr:             usecase.ErrTokenExpired,
		},
	}

	for _, tc := range testcases {
		t.Run(tc.name, func(t *testing.T) {
			r := &repository_mocks.MockRepository{}
			tc.mockFn(r)

			uc := org_access_token.New(r)
			resp, err := uc.VerifyOrgAccessToken(context.Background(), mockTokenString)

			assert.Equal(t, tc.expectErr, err)
			if tc.expectErr == nil {
				assert.NotNil(t, resp)
				assert.Equal(t, tc.expectedResponseValid, resp.Valid)
			} else {
				assert.Nil(t, resp)
			}

			r.AssertExpectations(t)
		})
	}
}
