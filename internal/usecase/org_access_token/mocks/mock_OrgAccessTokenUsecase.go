// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	dto "api-server/internal/dto"
	context "context"

	mock "github.com/stretchr/testify/mock"

	uuid "github.com/google/uuid"
)

// MockOrgAccessTokenUsecase is an autogenerated mock type for the OrgAccessTokenUsecase type
type MockOrgAccessTokenUsecase struct {
	mock.Mock
}

type MockOrgAccessTokenUsecase_Expecter struct {
	mock *mock.Mock
}

func (_m *MockOrgAccessTokenUsecase) EXPECT() *MockOrgAccessTokenUsecase_Expecter {
	return &MockOrgAccessTokenUsecase_Expecter{mock: &_m.Mock}
}

// CreateOrgAccessToken provides a mock function with given fields: ctx, req
func (_m *MockOrgAccessTokenUsecase) CreateOrgAccessToken(ctx context.Context, req dto.CreateOrgAccessTokenRequest) (*dto.CreateOrgAccessTokenResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CreateOrgAccessToken")
	}

	var r0 *dto.CreateOrgAccessTokenResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.CreateOrgAccessTokenRequest) (*dto.CreateOrgAccessTokenResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, dto.CreateOrgAccessTokenRequest) *dto.CreateOrgAccessTokenResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*dto.CreateOrgAccessTokenResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, dto.CreateOrgAccessTokenRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockOrgAccessTokenUsecase_CreateOrgAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateOrgAccessToken'
type MockOrgAccessTokenUsecase_CreateOrgAccessToken_Call struct {
	*mock.Call
}

// CreateOrgAccessToken is a helper method to define mock.On call
//   - ctx context.Context
//   - req dto.CreateOrgAccessTokenRequest
func (_e *MockOrgAccessTokenUsecase_Expecter) CreateOrgAccessToken(ctx interface{}, req interface{}) *MockOrgAccessTokenUsecase_CreateOrgAccessToken_Call {
	return &MockOrgAccessTokenUsecase_CreateOrgAccessToken_Call{Call: _e.mock.On("CreateOrgAccessToken", ctx, req)}
}

func (_c *MockOrgAccessTokenUsecase_CreateOrgAccessToken_Call) Run(run func(ctx context.Context, req dto.CreateOrgAccessTokenRequest)) *MockOrgAccessTokenUsecase_CreateOrgAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.CreateOrgAccessTokenRequest))
	})
	return _c
}

func (_c *MockOrgAccessTokenUsecase_CreateOrgAccessToken_Call) Return(_a0 *dto.CreateOrgAccessTokenResponse, _a1 error) *MockOrgAccessTokenUsecase_CreateOrgAccessToken_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockOrgAccessTokenUsecase_CreateOrgAccessToken_Call) RunAndReturn(run func(context.Context, dto.CreateOrgAccessTokenRequest) (*dto.CreateOrgAccessTokenResponse, error)) *MockOrgAccessTokenUsecase_CreateOrgAccessToken_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteOrgAccessToken provides a mock function with given fields: ctx, userId, orgID, accessTokenID
func (_m *MockOrgAccessTokenUsecase) DeleteOrgAccessToken(ctx context.Context, userId uuid.UUID, orgID uuid.UUID, accessTokenID uuid.UUID) error {
	ret := _m.Called(ctx, userId, orgID, accessTokenID)

	if len(ret) == 0 {
		panic("no return value specified for DeleteOrgAccessToken")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID, uuid.UUID, uuid.UUID) error); ok {
		r0 = rf(ctx, userId, orgID, accessTokenID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockOrgAccessTokenUsecase_DeleteOrgAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteOrgAccessToken'
type MockOrgAccessTokenUsecase_DeleteOrgAccessToken_Call struct {
	*mock.Call
}

// DeleteOrgAccessToken is a helper method to define mock.On call
//   - ctx context.Context
//   - userId uuid.UUID
//   - orgID uuid.UUID
//   - accessTokenID uuid.UUID
func (_e *MockOrgAccessTokenUsecase_Expecter) DeleteOrgAccessToken(ctx interface{}, userId interface{}, orgID interface{}, accessTokenID interface{}) *MockOrgAccessTokenUsecase_DeleteOrgAccessToken_Call {
	return &MockOrgAccessTokenUsecase_DeleteOrgAccessToken_Call{Call: _e.mock.On("DeleteOrgAccessToken", ctx, userId, orgID, accessTokenID)}
}

func (_c *MockOrgAccessTokenUsecase_DeleteOrgAccessToken_Call) Run(run func(ctx context.Context, userId uuid.UUID, orgID uuid.UUID, accessTokenID uuid.UUID)) *MockOrgAccessTokenUsecase_DeleteOrgAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID), args[2].(uuid.UUID), args[3].(uuid.UUID))
	})
	return _c
}

func (_c *MockOrgAccessTokenUsecase_DeleteOrgAccessToken_Call) Return(_a0 error) *MockOrgAccessTokenUsecase_DeleteOrgAccessToken_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockOrgAccessTokenUsecase_DeleteOrgAccessToken_Call) RunAndReturn(run func(context.Context, uuid.UUID, uuid.UUID, uuid.UUID) error) *MockOrgAccessTokenUsecase_DeleteOrgAccessToken_Call {
	_c.Call.Return(run)
	return _c
}

// GetOrgAccessToken provides a mock function with given fields: ctx, accessToken
func (_m *MockOrgAccessTokenUsecase) GetOrgAccessToken(ctx context.Context, accessToken string) (*dto.OrgAccessToken, error) {
	ret := _m.Called(ctx, accessToken)

	if len(ret) == 0 {
		panic("no return value specified for GetOrgAccessToken")
	}

	var r0 *dto.OrgAccessToken
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*dto.OrgAccessToken, error)); ok {
		return rf(ctx, accessToken)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *dto.OrgAccessToken); ok {
		r0 = rf(ctx, accessToken)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*dto.OrgAccessToken)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, accessToken)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockOrgAccessTokenUsecase_GetOrgAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetOrgAccessToken'
type MockOrgAccessTokenUsecase_GetOrgAccessToken_Call struct {
	*mock.Call
}

// GetOrgAccessToken is a helper method to define mock.On call
//   - ctx context.Context
//   - accessToken string
func (_e *MockOrgAccessTokenUsecase_Expecter) GetOrgAccessToken(ctx interface{}, accessToken interface{}) *MockOrgAccessTokenUsecase_GetOrgAccessToken_Call {
	return &MockOrgAccessTokenUsecase_GetOrgAccessToken_Call{Call: _e.mock.On("GetOrgAccessToken", ctx, accessToken)}
}

func (_c *MockOrgAccessTokenUsecase_GetOrgAccessToken_Call) Run(run func(ctx context.Context, accessToken string)) *MockOrgAccessTokenUsecase_GetOrgAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockOrgAccessTokenUsecase_GetOrgAccessToken_Call) Return(_a0 *dto.OrgAccessToken, _a1 error) *MockOrgAccessTokenUsecase_GetOrgAccessToken_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockOrgAccessTokenUsecase_GetOrgAccessToken_Call) RunAndReturn(run func(context.Context, string) (*dto.OrgAccessToken, error)) *MockOrgAccessTokenUsecase_GetOrgAccessToken_Call {
	_c.Call.Return(run)
	return _c
}

// ListOrgAccessToken provides a mock function with given fields: ctx, req
func (_m *MockOrgAccessTokenUsecase) ListOrgAccessToken(ctx context.Context, req dto.ListOrgAccessTokenRequest) (*dto.ListOrgAccessTokenResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for ListOrgAccessToken")
	}

	var r0 *dto.ListOrgAccessTokenResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.ListOrgAccessTokenRequest) (*dto.ListOrgAccessTokenResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, dto.ListOrgAccessTokenRequest) *dto.ListOrgAccessTokenResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*dto.ListOrgAccessTokenResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, dto.ListOrgAccessTokenRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockOrgAccessTokenUsecase_ListOrgAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListOrgAccessToken'
type MockOrgAccessTokenUsecase_ListOrgAccessToken_Call struct {
	*mock.Call
}

// ListOrgAccessToken is a helper method to define mock.On call
//   - ctx context.Context
//   - req dto.ListOrgAccessTokenRequest
func (_e *MockOrgAccessTokenUsecase_Expecter) ListOrgAccessToken(ctx interface{}, req interface{}) *MockOrgAccessTokenUsecase_ListOrgAccessToken_Call {
	return &MockOrgAccessTokenUsecase_ListOrgAccessToken_Call{Call: _e.mock.On("ListOrgAccessToken", ctx, req)}
}

func (_c *MockOrgAccessTokenUsecase_ListOrgAccessToken_Call) Run(run func(ctx context.Context, req dto.ListOrgAccessTokenRequest)) *MockOrgAccessTokenUsecase_ListOrgAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.ListOrgAccessTokenRequest))
	})
	return _c
}

func (_c *MockOrgAccessTokenUsecase_ListOrgAccessToken_Call) Return(_a0 *dto.ListOrgAccessTokenResponse, _a1 error) *MockOrgAccessTokenUsecase_ListOrgAccessToken_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockOrgAccessTokenUsecase_ListOrgAccessToken_Call) RunAndReturn(run func(context.Context, dto.ListOrgAccessTokenRequest) (*dto.ListOrgAccessTokenResponse, error)) *MockOrgAccessTokenUsecase_ListOrgAccessToken_Call {
	_c.Call.Return(run)
	return _c
}

// VerifyOrgAccessToken provides a mock function with given fields: ctx, token
func (_m *MockOrgAccessTokenUsecase) VerifyOrgAccessToken(ctx context.Context, token string) (*dto.VerifyOrgAccessTokenResponse, error) {
	ret := _m.Called(ctx, token)

	if len(ret) == 0 {
		panic("no return value specified for VerifyOrgAccessToken")
	}

	var r0 *dto.VerifyOrgAccessTokenResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*dto.VerifyOrgAccessTokenResponse, error)); ok {
		return rf(ctx, token)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *dto.VerifyOrgAccessTokenResponse); ok {
		r0 = rf(ctx, token)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*dto.VerifyOrgAccessTokenResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, token)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockOrgAccessTokenUsecase_VerifyOrgAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'VerifyOrgAccessToken'
type MockOrgAccessTokenUsecase_VerifyOrgAccessToken_Call struct {
	*mock.Call
}

// VerifyOrgAccessToken is a helper method to define mock.On call
//   - ctx context.Context
//   - token string
func (_e *MockOrgAccessTokenUsecase_Expecter) VerifyOrgAccessToken(ctx interface{}, token interface{}) *MockOrgAccessTokenUsecase_VerifyOrgAccessToken_Call {
	return &MockOrgAccessTokenUsecase_VerifyOrgAccessToken_Call{Call: _e.mock.On("VerifyOrgAccessToken", ctx, token)}
}

func (_c *MockOrgAccessTokenUsecase_VerifyOrgAccessToken_Call) Run(run func(ctx context.Context, token string)) *MockOrgAccessTokenUsecase_VerifyOrgAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockOrgAccessTokenUsecase_VerifyOrgAccessToken_Call) Return(_a0 *dto.VerifyOrgAccessTokenResponse, _a1 error) *MockOrgAccessTokenUsecase_VerifyOrgAccessToken_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockOrgAccessTokenUsecase_VerifyOrgAccessToken_Call) RunAndReturn(run func(context.Context, string) (*dto.VerifyOrgAccessTokenResponse, error)) *MockOrgAccessTokenUsecase_VerifyOrgAccessToken_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockOrgAccessTokenUsecase creates a new instance of MockOrgAccessTokenUsecase. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockOrgAccessTokenUsecase(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockOrgAccessTokenUsecase {
	mock := &MockOrgAccessTokenUsecase{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
