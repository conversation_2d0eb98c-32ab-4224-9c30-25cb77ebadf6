package user_test

import (
	"bytes"
	"context"
	"errors"
	"io"
	"mime/multipart"
	"strings"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"gorm.io/gorm"

	"api-server/internal/dto"
	"api-server/internal/entities"
	"api-server/internal/enums"
	aws_mocks "api-server/internal/gateways/aws/mocks"
	"api-server/internal/gateways/gitlab"
	gitlab_mocks "api-server/internal/gateways/gitlab/mocks"
	repository "api-server/internal/repositories"
	repository_mocks "api-server/internal/repositories/mocks"
	"api-server/internal/types"
	"api-server/internal/usecase"
	"api-server/internal/usecase/user"
	"api-server/internal/utils"
)

func TestAuthorizePlatform(t *testing.T) {
	mockUserID := uuid.New()
	testcases := []struct {
		name                    string
		mockRequiredPermissions []enums.AppPermission
		mockUserPermissions     []entities.AppPermission
		mockFn                  func(r *repository_mocks.MockRepository, perms []entities.AppPermission)
		expectResult            bool
		expectErr               error
	}{
		{
			name:                    "should return usecase.ErrInternal if GetAppPermission return error",
			mockRequiredPermissions: []enums.AppPermission{},
			mockUserPermissions:     []entities.AppPermission{},
			mockFn: func(r *repository_mocks.MockRepository, mockUserPermissions []entities.AppPermission) {
				r.On("GetAppPermission", mock.Anything, mockUserID).Return(mockUserPermissions, usecase.ErrInternal)
			},
			expectResult: false,
			expectErr:    usecase.ErrInternal,
		},
		{
			name:                    "should return true if required permissions is empty",
			mockRequiredPermissions: []enums.AppPermission{},
			mockUserPermissions:     []entities.AppPermission{},
			mockFn: func(r *repository_mocks.MockRepository, mockUserPermissions []entities.AppPermission) {
				r.On("GetAppPermission", mock.Anything, mockUserID).Return(mockUserPermissions, nil)
			},
			expectResult: true,
			expectErr:    nil,
		},
		{
			name:                    "should return true if user has required permissions",
			mockRequiredPermissions: []enums.AppPermission{enums.AppPermission_DatasetsRead, enums.AppPermission_OrgsRead},
			mockUserPermissions: []entities.AppPermission{
				{Permission: enums.AppPermission_DatasetsRead},
				{Permission: enums.AppPermission_OrgsRead},
			},
			mockFn: func(r *repository_mocks.MockRepository, mockUserPermissions []entities.AppPermission) {
				r.On("GetAppPermission", mock.Anything, mockUserID).Return(mockUserPermissions, nil)
			},
			expectResult: true,
			expectErr:    nil,
		},
		{
			name:                    "should return false if user does not have required permissions",
			mockRequiredPermissions: []enums.AppPermission{enums.AppPermission_DatasetsRead, enums.AppPermission_OrgsRead},
			mockUserPermissions: []entities.AppPermission{
				{Permission: enums.AppPermission_DatasetsRead},
			},
			mockFn: func(r *repository_mocks.MockRepository, mockUserPermissions []entities.AppPermission) {
				r.On("GetAppPermission", mock.Anything, mockUserID).Return(mockUserPermissions, nil)
			},
			expectResult: false,
			expectErr:    nil,
		},
	}

	for _, testcase := range testcases {
		t.Run(testcase.name, func(t *testing.T) {
			r := &repository_mocks.MockRepository{}
			testcase.mockFn(r, testcase.mockUserPermissions)

			userUsecase := user.New(r, nil, nil, nil, nil, nil)
			isAuthorized, err := userUsecase.AuthorizePlatform(context.Background(), mockUserID, testcase.mockRequiredPermissions)

			// assertion
			assert.Equal(t, testcase.expectErr, err)
			assert.Equal(t, testcase.expectResult, isAuthorized)
		})
	}
}

func TestAuthorizeOrg(t *testing.T) {
	mockUserID := uuid.New()
	mockOrgID := uuid.New()
	testcases := []struct {
		name                    string
		mockRequiredPermissions []enums.RepoPermission
		mockUserPermissions     []entities.RepoPermission
		mockFn                  func(r *repository_mocks.MockRepository, perms []entities.RepoPermission)
		expectResult            bool
		expectErr               error
	}{
		{
			name:                    "should return usecase.ErrInternal if GetOrgPermission return error",
			mockRequiredPermissions: []enums.RepoPermission{},
			mockUserPermissions:     []entities.RepoPermission{},
			mockFn: func(r *repository_mocks.MockRepository, mockUserPermissions []entities.RepoPermission) {
				r.On("GetOrgPermission", mock.Anything, mockUserID, mockOrgID).Return(mockUserPermissions, usecase.ErrInternal)
			},
			expectResult: false,
			expectErr:    usecase.ErrInternal,
		},
		{
			name:                    "should return true if required permissions is empty",
			mockRequiredPermissions: []enums.RepoPermission{},
			mockUserPermissions:     []entities.RepoPermission{},
			mockFn: func(r *repository_mocks.MockRepository, mockUserPermissions []entities.RepoPermission) {
				r.On("GetOrgPermission", mock.Anything, mockUserID, mockOrgID).Return(mockUserPermissions, nil)
			},
			expectResult: true,
			expectErr:    nil,
		},
		{
			name:                    "should return true if user has required permissions",
			mockRequiredPermissions: []enums.RepoPermission{enums.RepoPermission_Edit, enums.RepoPermission_MembersEdit},
			mockUserPermissions: []entities.RepoPermission{
				{Permission: enums.RepoPermission_Edit},
				{Permission: enums.RepoPermission_MembersEdit},
			},
			mockFn: func(r *repository_mocks.MockRepository, mockUserPermissions []entities.RepoPermission) {
				r.On("GetOrgPermission", mock.Anything, mockUserID, mockOrgID).Return(mockUserPermissions, nil)
			},
			expectResult: true,
			expectErr:    nil,
		},
		{
			name:                    "should return false if user does not have required permissions",
			mockRequiredPermissions: []enums.RepoPermission{enums.RepoPermission_Edit, enums.RepoPermission_MembersEdit},
			mockUserPermissions: []entities.RepoPermission{
				{Permission: enums.RepoPermission_Edit},
			},
			mockFn: func(r *repository_mocks.MockRepository, mockUserPermissions []entities.RepoPermission) {
				r.On("GetOrgPermission", mock.Anything, mockUserID, mockOrgID).Return(mockUserPermissions, nil)
			},
			expectResult: false,
			expectErr:    nil,
		},
	}

	for _, testcase := range testcases {
		t.Run(testcase.name, func(t *testing.T) {
			r := &repository_mocks.MockRepository{}
			testcase.mockFn(r, testcase.mockUserPermissions)

			userUsecase := user.New(r, nil, nil, nil, nil, nil)
			isAuthorized, err := userUsecase.AuthorizeOrg(context.Background(), mockUserID, mockOrgID, testcase.mockRequiredPermissions)

			// assertion
			assert.Equal(t, testcase.expectErr, err)
			assert.Equal(t, testcase.expectResult, isAuthorized)
		})
	}
}

func TestAuthorizeRepo(t *testing.T) {
	mockUserID := uuid.New()

	repoType := enums.RepoType_Models
	namespace := "namespace"
	repoName := "repo-name"
	mockRepoID := *types.NewRepoID(repoType, namespace, repoName)

	testcases := []struct {
		name                    string
		mockRequiredPermissions []enums.RepoPermission
		mockUserPermissions     []entities.RepoPermission
		mockFn                  func(r *repository_mocks.MockRepository, perms []entities.RepoPermission)
		expectResult            bool
		expectErr               error
	}{
		{
			name:                    "should return FindRepository return error",
			mockRequiredPermissions: []enums.RepoPermission{},
			mockUserPermissions:     []entities.RepoPermission{},
			mockFn: func(r *repository_mocks.MockRepository, mockUserPermissions []entities.RepoPermission) {
				r.On("FindRepository", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectResult: false,
			expectErr:    errors.New("error"),
		},
		{
			name:                    "should return FindRepository return not found",
			mockRequiredPermissions: []enums.RepoPermission{},
			mockUserPermissions:     []entities.RepoPermission{},
			mockFn: func(r *repository_mocks.MockRepository, mockUserPermissions []entities.RepoPermission) {
				r.On("FindRepository", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expectResult: false,
			expectErr:    usecase.ErrRepositoryNotFound,
		},
		{
			name:                    "should return usecase.ErrInternal if GetRepoPermission return not found",
			mockRequiredPermissions: []enums.RepoPermission{},
			mockUserPermissions:     []entities.RepoPermission{},
			mockFn: func(r *repository_mocks.MockRepository, mockUserPermissions []entities.RepoPermission) {
				r.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{}, nil)
				r.On("GetRepoPermission", mock.Anything, mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expectResult: false,
			expectErr:    nil,
		},
		{
			name:                    "should return usecase.ErrInternal if GetRepoPermission return error",
			mockRequiredPermissions: []enums.RepoPermission{},
			mockUserPermissions:     []entities.RepoPermission{},
			mockFn: func(r *repository_mocks.MockRepository, mockUserPermissions []entities.RepoPermission) {
				r.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{}, nil)
				r.On("GetRepoPermission", mock.Anything, mock.Anything, mock.Anything).Return(mockUserPermissions, usecase.ErrInternal)
			},
			expectResult: false,
			expectErr:    usecase.ErrInternal,
		},
		{
			name:                    "should return true if required permissions is empty",
			mockRequiredPermissions: []enums.RepoPermission{},
			mockUserPermissions:     []entities.RepoPermission{},
			mockFn: func(r *repository_mocks.MockRepository, mockUserPermissions []entities.RepoPermission) {
				r.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{}, nil)
				r.On("GetRepoPermission", mock.Anything, mock.Anything, mock.Anything).Return(mockUserPermissions, nil)
			},
			expectResult: true,
			expectErr:    nil,
		},
		{
			name:                    "should return true if user has required permissions",
			mockRequiredPermissions: []enums.RepoPermission{enums.RepoPermission_Edit, enums.RepoPermission_MembersEdit},
			mockUserPermissions: []entities.RepoPermission{
				{Permission: enums.RepoPermission_Edit},
				{Permission: enums.RepoPermission_MembersEdit},
			},
			mockFn: func(r *repository_mocks.MockRepository, mockUserPermissions []entities.RepoPermission) {
				r.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{}, nil)
				r.On("GetRepoPermission", mock.Anything, mock.Anything, mock.Anything).Return(mockUserPermissions, nil)
			},
			expectResult: true,
			expectErr:    nil,
		},
		{
			name:                    "should return false if user does not have required permissions",
			mockRequiredPermissions: []enums.RepoPermission{enums.RepoPermission_Edit, enums.RepoPermission_MembersEdit},
			mockUserPermissions: []entities.RepoPermission{
				{Permission: enums.RepoPermission_Edit},
			},
			mockFn: func(r *repository_mocks.MockRepository, mockUserPermissions []entities.RepoPermission) {
				r.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{}, nil)
				r.On("GetRepoPermission", mock.Anything, mock.Anything, mock.Anything).Return(mockUserPermissions, nil)
			},
			expectResult: false,
			expectErr:    nil,
		},
	}

	for _, testcase := range testcases {
		t.Run(testcase.name, func(t *testing.T) {
			r := &repository_mocks.MockRepository{}
			testcase.mockFn(r, testcase.mockUserPermissions)

			userUsecase := user.New(r, nil, nil, nil, nil, nil)
			isAuthorized, err := userUsecase.AuthorizeRepo(context.Background(), mockUserID, mockRepoID, testcase.mockRequiredPermissions)

			// assertion
			assert.Equal(t, testcase.expectErr, err)
			assert.Equal(t, testcase.expectResult, isAuthorized)
		})
	}
}

func TestListUsers(t *testing.T) {
	avatarUrl := "http://imageURL"
	repoId := uuid.New()
	repo := "spaces/sample/sample"
	testcases := []struct {
		name         string
		input        dto.ListUsersInput
		mockFn       func(r *repository_mocks.MockRepository, awsClient *aws_mocks.MockAWSClient)
		expectResult dto.ListUsersOutput
		expectErr    error
	}{
		{
			name: "should return usecase.ErrInternal if repository not found",
			input: dto.ListUsersInput{
				Paginate: dto.PaginateRequest{
					Page:    1,
					PerPage: 10,
				},
				Except: dto.ExceptFilter{
					NotInRepo: &repo,
				},
			},
			mockFn: func(r *repository_mocks.MockRepository, awsClient *aws_mocks.MockAWSClient) {
				r.On("FindRepository", mock.Anything, mock.Anything).Return(nil, usecase.ErrRecordNotFound)
			},
			expectErr: usecase.ErrRecordNotFound,
		},
		{
			name: "should return usecase.ErrInternal if CountUsers returns error",
			input: dto.ListUsersInput{
				Paginate: dto.PaginateRequest{
					Page:    1,
					PerPage: 10,
				},
			},
			mockFn: func(r *repository_mocks.MockRepository, awsClient *aws_mocks.MockAWSClient) {
				r.On("CountUsers", mock.Anything, repository.ListUsersInput{
					Paginate: repository.PaginateRequest{
						Page:    1,
						PerPage: 10,
					},
				}).Return(int64(0), usecase.ErrInternal)
			},
			expectErr: usecase.ErrInternal,
		},
		{
			name: "should return usecase.ErrInternal if ListUsers returns error",
			input: dto.ListUsersInput{
				Paginate: dto.PaginateRequest{
					Page:    1,
					PerPage: 10,
				},
			},
			mockFn: func(r *repository_mocks.MockRepository, awsClient *aws_mocks.MockAWSClient) {
				r.On("CountUsers", mock.Anything, repository.ListUsersInput{
					Paginate: repository.PaginateRequest{
						Page:    1,
						PerPage: 10,
					},
				}).Return(int64(1), nil)
				r.On("ListUsers", mock.Anything, repository.ListUsersInput{
					Paginate: repository.PaginateRequest{
						Page:    1,
						PerPage: 10,
					},
				}).Return(nil, usecase.ErrInternal)
			},
			expectErr: usecase.ErrInternal,
		},
		{
			name: "should return users if ListUsers succeeds",
			input: dto.ListUsersInput{
				Paginate: dto.PaginateRequest{
					Page:    1,
					PerPage: 10,
				},
			},
			mockFn: func(r *repository_mocks.MockRepository, awsClient *aws_mocks.MockAWSClient) {
				r.On("CountUsers", mock.Anything, repository.ListUsersInput{
					Paginate: repository.PaginateRequest{
						Page:    1,
						PerPage: 10,
					},
				}).Return(int64(1), nil)
				r.On("ListUsers", mock.Anything, repository.ListUsersInput{
					Paginate: repository.PaginateRequest{
						Page:    1,
						PerPage: 10,
					},
				}).Return([]entities.User{}, nil)
			},
			expectErr: nil,
		},

		{
			name: "should return users if ListUsers succeeds with filter except repository",
			input: dto.ListUsersInput{
				Paginate: dto.PaginateRequest{
					Page:    1,
					PerPage: 10,
				},
				Except: dto.ExceptFilter{
					NotInRepo: &repo,
				},
			},
			mockFn: func(r *repository_mocks.MockRepository, awsClient *aws_mocks.MockAWSClient) {
				r.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{BaseModel: entities.BaseModel{ID: repoId}}, nil)
				r.On("CountUsers", mock.Anything, repository.ListUsersInput{
					Paginate: repository.PaginateRequest{
						Page:    1,
						PerPage: 10,
					},
					Except: repository.ExceptFilter{
						NotInRepId: &repoId,
						NotInOrgId: nil,
					},
				}).Return(int64(1), nil)
				r.On("ListUsers", mock.Anything, repository.ListUsersInput{
					Paginate: repository.PaginateRequest{
						Page:    1,
						PerPage: 10,
					},
					Except: repository.ExceptFilter{
						NotInRepId: &repoId,
						NotInOrgId: nil,
					},
				}).Return([]entities.User{}, nil)
			},
			expectErr: nil,
		},
		{
			name: "should return usecase.ErrRepositoryNotFound if repository not found",
			input: dto.ListUsersInput{
				Paginate: dto.PaginateRequest{
					Page:    1,
					PerPage: 10,
				},
				Except: dto.ExceptFilter{
					NotInRepo: &repo,
				},
			},
			mockFn: func(r *repository_mocks.MockRepository, awsClient *aws_mocks.MockAWSClient) {
				r.On("FindRepository", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expectErr: usecase.ErrRepositoryNotFound,
		},
		{
			name: "should return users if ListUsers succeeds with filter except repository",
			input: dto.ListUsersInput{
				Paginate: dto.PaginateRequest{
					Page:    1,
					PerPage: 10,
				},
				Except: dto.ExceptFilter{
					NotInRepo: &repo,
				},
			},
			mockFn: func(r *repository_mocks.MockRepository, awsClient *aws_mocks.MockAWSClient) {
				r.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{BaseModel: entities.BaseModel{ID: repoId}}, nil)
				r.On("CountUsers", mock.Anything, repository.ListUsersInput{
					Paginate: repository.PaginateRequest{
						Page:    1,
						PerPage: 10,
					},
					Except: repository.ExceptFilter{
						NotInRepId: &repoId,
						NotInOrgId: nil,
					},
				}).Return(int64(1), nil)
				r.On("ListUsers", mock.Anything, repository.ListUsersInput{
					Paginate: repository.PaginateRequest{
						Page:    1,
						PerPage: 10,
					},
					Except: repository.ExceptFilter{
						NotInRepId: &repoId,
						NotInOrgId: nil,
					},
				}).Return([]entities.User{
					{
						BaseModel: entities.BaseModel{ID: uuid.New()},
						Role:      enums.UserRole_User,
						Avatar:    &avatarUrl,
					},
				}, nil)
				awsClient.On("GenPreSignUrl", mock.Anything, mock.Anything).Return("http", nil)
			},
			expectErr: nil,
		},
		{
			name: "should return error if GenPreSignUrl returns error",
			input: dto.ListUsersInput{
				Paginate: dto.PaginateRequest{
					Page:    1,
					PerPage: 10,
				},
				Except: dto.ExceptFilter{
					NotInRepo: &repo,
				},
			},
			mockFn: func(r *repository_mocks.MockRepository, awsClient *aws_mocks.MockAWSClient) {
				r.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{BaseModel: entities.BaseModel{ID: repoId}}, nil)
				r.On("CountUsers", mock.Anything, repository.ListUsersInput{
					Paginate: repository.PaginateRequest{
						Page:    1,
						PerPage: 10,
					},
					Except: repository.ExceptFilter{
						NotInRepId: &repoId,
						NotInOrgId: nil,
					},
				}).Return(int64(1), nil)
				r.On("ListUsers", mock.Anything, repository.ListUsersInput{
					Paginate: repository.PaginateRequest{
						Page:    1,
						PerPage: 10,
					},
					Except: repository.ExceptFilter{
						NotInRepId: &repoId,
						NotInOrgId: nil,
					},
				}).Return([]entities.User{
					{
						BaseModel: entities.BaseModel{ID: uuid.New()},
						Role:      enums.UserRole_User,
						Avatar:    &avatarUrl,
					},
				}, nil)
				awsClient.On("GenPreSignUrl", mock.Anything, mock.Anything).Return("", errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
	}

	for _, testcase := range testcases {
		t.Run(testcase.name, func(t *testing.T) {
			r := &repository_mocks.MockRepository{}
			awsClient := &aws_mocks.MockAWSClient{}
			testcase.mockFn(r, awsClient)

			userUsecase := user.New(r, nil, nil, awsClient, nil, nil)
			_, err := userUsecase.ListUsers(context.Background(), testcase.input)

			// assertion
			assert.Equal(t, testcase.expectErr, err)
		})
	}
}

func TestUpdateRoleUser(t *testing.T) {
	uuidUser := uuid.New()
	testcases := []struct {
		name      string
		input     dto.UpdateRoleInput
		mockFn    func(r *repository_mocks.MockRepository)
		expectErr error
	}{
		{
			name: "should return usecase.ErrUserNotFound fail",
			input: dto.UpdateRoleInput{
				UserID: uuidUser,
				Role:   enums.UserRole_User,
			},
			mockFn: func(r *repository_mocks.MockRepository) {
				r.On("FindUserByID", mock.Anything, uuidUser).Return(nil, errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return usecase.ErrUserNotFound if user not found",
			input: dto.UpdateRoleInput{
				UserID: uuidUser,
				Role:   enums.UserRole_User,
			},
			mockFn: func(r *repository_mocks.MockRepository) {
				r.On("FindUserByID", mock.Anything, uuidUser).Return(nil, gorm.ErrRecordNotFound)
			},
			expectErr: usecase.ErrUserNotFound,
		},
		{
			name: "should return usecase.ErrInternal if Save returns error",
			input: dto.UpdateRoleInput{
				UserID: uuidUser,
				Role:   enums.UserRole_User,
			},
			mockFn: func(r *repository_mocks.MockRepository) {
				r.On("FindUserByID", mock.Anything, uuidUser).Return(&entities.User{}, nil)
				r.On("Save", mock.Anything, &entities.User{Role: enums.UserRole_User}).Return(errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return nil if Save succeeds",
			input: dto.UpdateRoleInput{
				UserID: uuidUser,
				Role:   enums.UserRole_User,
			},
			mockFn: func(r *repository_mocks.MockRepository) {
				r.On("FindUserByID", mock.Anything, uuidUser).Return(&entities.User{}, nil)
				r.On("Save", mock.Anything, &entities.User{Role: enums.UserRole_User}).Return(nil)
			},
			expectErr: nil,
		},
	}

	for _, testcase := range testcases {
		t.Run(testcase.name, func(t *testing.T) {
			r := &repository_mocks.MockRepository{}
			testcase.mockFn(r)

			userUsecase := user.New(r, nil, nil, nil, nil, nil)
			err := userUsecase.UpdateRoleUser(context.Background(), testcase.input)

			// assertion
			assert.Equal(t, testcase.expectErr, err)
		})
	}
}

func TestGetCurrentUser(t *testing.T) {
	avatarUrl := "http://imageURL"
	mockUserID := uuid.New()
	testcases := []struct {
		name      string
		mockFn    func(r *repository_mocks.MockRepository, awsClient *aws_mocks.MockAWSClient)
		expectErr error
	}{
		{
			name: "should return usecase.ErrUserNotFound if user not found",
			mockFn: func(r *repository_mocks.MockRepository, awsClient *aws_mocks.MockAWSClient) {
				r.On("FindUserByID", mock.Anything, mockUserID).Return(nil, gorm.ErrRecordNotFound)
			},
			expectErr: usecase.ErrUserNotFound,
		},
		{
			name: "should return usecase.ErrInternal if FindUserByID returns error",
			mockFn: func(r *repository_mocks.MockRepository, awsClient *aws_mocks.MockAWSClient) {
				r.On("FindUserByID", mock.Anything, mockUserID).Return(nil, errors.New("error"))
			},
			expectErr: usecase.ErrInternal,
		},
		{
			name: "should return user if GenPreSignUrl fail",
			mockFn: func(r *repository_mocks.MockRepository, awsClient *aws_mocks.MockAWSClient) {
				r.On("FindUserByID", mock.Anything, mockUserID).Return(&entities.User{
					Avatar: &avatarUrl,
				}, nil)
				awsClient.On("GenPreSignUrl", mock.Anything, mock.Anything).Return("", errors.New("error"))

			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return user if FindUserByID succeeds",
			mockFn: func(r *repository_mocks.MockRepository, awsClient *aws_mocks.MockAWSClient) {
				r.On("FindUserByID", mock.Anything, mockUserID).Return(&entities.User{
					Avatar: &avatarUrl,
				}, nil)
				awsClient.On("GenPreSignUrl", mock.Anything, mock.Anything).Return("http", nil)

			},
			expectErr: nil,
		},
	}

	for _, testcase := range testcases {
		t.Run(testcase.name, func(t *testing.T) {
			r := &repository_mocks.MockRepository{}
			awsClient := &aws_mocks.MockAWSClient{}
			testcase.mockFn(r, awsClient)

			userUsecase := user.New(r, nil, nil, awsClient, nil, nil)
			_, err := userUsecase.GetCurrentUser(context.Background(), mockUserID)

			// assertion
			assert.Equal(t, testcase.expectErr, err)
		})
	}
}

func TestUpsertUser(t *testing.T) {
	mockUserID := uuid.New()
	testcases := []struct {
		name      string
		mockFn    func(r *repository_mocks.MockRepository, gitlab *gitlab_mocks.MockGitlabClient)
		expectErr error
	}{
		{
			name: "should return usecase.ErrInternal if FindUserByID returns error",
			mockFn: func(r *repository_mocks.MockRepository, g *gitlab_mocks.MockGitlabClient) {
				r.On("FindUserByID", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return usecase.FindAuthUser if user not found",
			mockFn: func(r *repository_mocks.MockRepository, g *gitlab_mocks.MockGitlabClient) {
				r.On("FindUserByID", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
				r.On("FindAuthUser", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expectErr: usecase.ErrUserNotFound,
		},
		{
			name: "should return usecase.FindAuthUser returns error",
			mockFn: func(r *repository_mocks.MockRepository, g *gitlab_mocks.MockGitlabClient) {
				r.On("FindUserByID", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
				r.On("FindAuthUser", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return user if FindUser err",
			mockFn: func(r *repository_mocks.MockRepository, g *gitlab_mocks.MockGitlabClient) {
				r.On("FindUserByID", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
				r.On("FindAuthUser", mock.Anything, mock.Anything).Return(&entities.AuthUser{Email: "<EMAIL>"}, nil)
				r.On("FindUser", mock.Anything, mock.Anything).Return(nil, errors.New("error"))

			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return user if GetUser err",
			mockFn: func(r *repository_mocks.MockRepository, g *gitlab_mocks.MockGitlabClient) {
				r.On("FindUserByID", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
				r.On("FindAuthUser", mock.Anything, mock.Anything).Return(&entities.AuthUser{Email: "<EMAIL>"}, nil)
				r.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{GitlabAccessToken: utils.Ptr("token")}, nil)
				g.On("GetUser", mock.Anything, mock.Anything).Return(nil, errors.New("error"))

			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return user if GetGroup err",
			mockFn: func(r *repository_mocks.MockRepository, g *gitlab_mocks.MockGitlabClient) {
				r.On("FindUserByID", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
				r.On("FindAuthUser", mock.Anything, mock.Anything).Return(&entities.AuthUser{Email: "<EMAIL>"}, nil)
				r.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{GitlabAccessToken: utils.Ptr("token")}, nil)
				g.On("GetUser", mock.Anything, mock.Anything).Return([]gitlab.CreateUserResponse{}, nil)
				g.On("GetGroup", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return user if CreatePersonalAccessToken err",
			mockFn: func(r *repository_mocks.MockRepository, g *gitlab_mocks.MockGitlabClient) {
				r.On("FindUserByID", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
				r.On("FindAuthUser", mock.Anything, mock.Anything).Return(&entities.AuthUser{Email: "<EMAIL>"}, nil)
				r.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{GitlabAccessToken: utils.Ptr("token")}, nil)
				g.On("GetUser", mock.Anything, mock.Anything).Return([]gitlab.CreateUserResponse{}, nil)
				g.On("GetGroup", mock.Anything, mock.Anything).Return([]gitlab.GitlabGroup{}, nil)
				g.On("CreatePersonalAccessToken", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectErr: errors.New("error"),
		},

		{
			name: "should return Create err",
			mockFn: func(r *repository_mocks.MockRepository, g *gitlab_mocks.MockGitlabClient) {
				r.On("FindUserByID", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
				r.On("FindAuthUser", mock.Anything, mock.Anything).Return(&entities.AuthUser{Email: "<EMAIL>"}, nil)
				r.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{GitlabAccessToken: utils.Ptr("token")}, nil)
				g.On("GetUser", mock.Anything, mock.Anything).Return([]gitlab.CreateUserResponse{}, nil)
				g.On("GetGroup", mock.Anything, mock.Anything).Return([]gitlab.GitlabGroup{}, nil)
				g.On("CreatePersonalAccessToken", mock.Anything, mock.Anything).Return(&gitlab.CreatePersonalAccessTokenResponse{
					ExpiredAt: "2006-01-02",
				}, nil)
				r.On("Transaction", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), mockUserID))
				}).Return(errors.New("error"))
				r.On("Create", mock.Anything, mock.Anything).Return(errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return CreateUserGitGroup err",
			mockFn: func(r *repository_mocks.MockRepository, g *gitlab_mocks.MockGitlabClient) {
				r.On("FindUserByID", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
				r.On("FindAuthUser", mock.Anything, mock.Anything).Return(&entities.AuthUser{Email: "<EMAIL>"}, nil)
				r.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{GitlabAccessToken: utils.Ptr("token")}, nil)
				g.On("GetUser", mock.Anything, mock.Anything).Return([]gitlab.CreateUserResponse{}, nil)
				g.On("GetGroup", mock.Anything, mock.Anything).Return([]gitlab.GitlabGroup{}, nil)
				g.On("CreatePersonalAccessToken", mock.Anything, mock.Anything).Return(&gitlab.CreatePersonalAccessTokenResponse{
					ExpiredAt: "2006-01-02",
				}, nil)
				r.On("Transaction", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), mockUserID))
				}).Return(errors.New("error"))
				r.On("Create", mock.Anything, mock.Anything).Return(nil)
				r.On("CreateUserGitGroup", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return success",
			mockFn: func(r *repository_mocks.MockRepository, g *gitlab_mocks.MockGitlabClient) {
				r.On("FindUserByID", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
				r.On("FindAuthUser", mock.Anything, mock.Anything).Return(&entities.AuthUser{Email: "<EMAIL>"}, nil)
				r.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{GitlabAccessToken: utils.Ptr("token")}, nil)
				g.On("GetUser", mock.Anything, mock.Anything).Return([]gitlab.CreateUserResponse{}, nil)
				g.On("GetGroup", mock.Anything, mock.Anything).Return([]gitlab.GitlabGroup{}, nil)
				g.On("CreatePersonalAccessToken", mock.Anything, mock.Anything).Return(&gitlab.CreatePersonalAccessTokenResponse{
					ExpiredAt: "2006-01-02",
				}, nil)
				r.On("Transaction", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), mockUserID))
				}).Return(nil)
				r.On("Create", mock.Anything, mock.Anything).Return(nil)
				r.On("CreateUserGitGroup", mock.Anything, mock.Anything).Return(&entities.UserGitGroup{}, nil)
			},
			expectErr: nil,
		},
	}

	for _, testcase := range testcases {
		t.Run(testcase.name, func(t *testing.T) {
			r := &repository_mocks.MockRepository{}
			gitlab := &gitlab_mocks.MockGitlabClient{}
			testcase.mockFn(r, gitlab)

			userUsecase := user.New(r, gitlab, nil, nil, nil, nil)
			err := userUsecase.UpsertUser(context.Background(), mockUserID)

			// assertion
			assert.Equal(t, testcase.expectErr, err)
		})
	}
}

func TestUploadAvatarRepo(t *testing.T) {
	type dependencies struct {
		repo      *repository_mocks.MockRepository
		awsClient *aws_mocks.MockAWSClient
	}

	createMockFileHeader := func(fieldName, fileName, fileContent string) *multipart.FileHeader {
		var buffer bytes.Buffer
		writer := multipart.NewWriter(&buffer)

		part, _ := writer.CreateFormFile(fieldName, fileName)
		io.Copy(part, strings.NewReader(fileContent))

		writer.Close()

		reader := multipart.NewReader(&buffer, writer.Boundary())
		form, _ := reader.ReadForm(1024)

		return form.File[fieldName][0]
	}

	mockFileHeader := createMockFileHeader("avatar", "mockfile.txt", "This is a mock file content")

	tests := []struct {
		name     string
		ctx      context.Context
		mockFn   func(d *dependencies)
		input    dto.UploadUserAvatarInput
		expError error
	}{
		{
			name: "should return error if FindUserByID fail",
			ctx:  context.TODO(),
			input: dto.UploadUserAvatarInput{
				UserId: uuid.New(),
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(nil, errors.New("errors"))
			},
			expError: errors.New("errors"),
		},
		{
			name: "should return error if FindUserByID not found",
			ctx:  context.TODO(),
			input: dto.UploadUserAvatarInput{
				UserId: uuid.New(),
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expError: usecase.ErrUserNotFound,
		},
		{
			name: "should return error if UploadImage fail",
			ctx:  context.TODO(),
			input: dto.UploadUserAvatarInput{
				UserId: uuid.New(),
				File:   mockFileHeader,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{Role: enums.UserRole_User}, nil)
				d.awsClient.On("UploadImage", mock.Anything, mock.Anything, mock.Anything).Return("", errors.New("errors"))
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything).Return(&entities.RepoMember{Role: enums.RepoRole_Owner}, nil)
			},
			expError: errors.New("errors"),
		},
		{
			name: "should return error if Save fail",
			ctx:  context.TODO(),
			input: dto.UploadUserAvatarInput{
				UserId: uuid.New(),
				File:   mockFileHeader,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{Role: enums.UserRole_User}, nil)
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything).Return(&entities.RepoMember{Role: enums.RepoRole_Owner}, nil)
				d.awsClient.On("UploadImage", mock.Anything, mock.Anything, mock.Anything).Return("http://imageURL", nil)
				d.repo.On("Save", mock.Anything, mock.Anything).Return(errors.New("errors"))
			},
			expError: errors.New("errors"),
		},
		{
			name: "should success",
			ctx:  context.TODO(),
			input: dto.UploadUserAvatarInput{
				UserId: uuid.New(),
				File:   mockFileHeader,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{Role: enums.UserRole_User}, nil)
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything).Return(&entities.RepoMember{Role: enums.RepoRole_Owner}, nil)
				d.awsClient.On("UploadImage", mock.Anything, mock.Anything, mock.Anything).Return("http://imageURL", nil)
				d.repo.On("Save", mock.Anything, mock.Anything).Return(nil)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				repo:      &repository_mocks.MockRepository{},
				awsClient: &aws_mocks.MockAWSClient{},
			}
			tt.mockFn(d)

			u := user.New(d.repo, nil, nil, d.awsClient, nil, nil)
			_, err := u.UploadAvatarUser(tt.ctx, tt.input)
			require.Equal(t, tt.expError, err)
		})
	}
}

func TestDeleteAvatarUser(t *testing.T) {
	type dependencies struct {
		repo      *repository_mocks.MockRepository
		awsClient *aws_mocks.MockAWSClient
	}

	repoId := uuid.New()
	avatarUrl := "http://imageURL"
	tests := []struct {
		name     string
		ctx      context.Context
		mockFn   func(d *dependencies)
		repoId   uuid.UUID
		expError error
	}{
		{
			name:   "should return error if FindUserByID fail",
			ctx:    context.TODO(),
			repoId: repoId,
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(nil, errors.New("errors"))
			},
			expError: errors.New("errors"),
		},
		{
			name:   "should return error if FindUserByID not found",
			ctx:    context.TODO(),
			repoId: repoId,
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expError: usecase.ErrUserNotFound,
		},
		{
			name:   "should return error if DeleteImage fail",
			ctx:    context.TODO(),
			repoId: repoId,
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{Role: enums.UserRole_User, Avatar: &avatarUrl}, nil)
				d.awsClient.On("DeleteImage", mock.Anything, mock.Anything).Return(errors.New("errors"))
			},
			expError: errors.New("errors"),
		},
		{
			name:   "should return error if Save fail",
			ctx:    context.TODO(),
			repoId: repoId,
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{Role: enums.UserRole_User, Avatar: &avatarUrl}, nil)
				d.awsClient.On("DeleteImage", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("Save", mock.Anything, mock.Anything).Return(errors.New("errors"))
			},
			expError: errors.New("errors"),
		},
		{
			name:   "should success",
			ctx:    context.TODO(),
			repoId: repoId,
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{Role: enums.UserRole_User}, nil)
				d.awsClient.On("DeleteImage", mock.Anything, mock.Anything, mock.Anything).Return(nil)
				d.repo.On("Save", mock.Anything, mock.Anything).Return(nil)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				repo:      &repository_mocks.MockRepository{},
				awsClient: &aws_mocks.MockAWSClient{},
			}
			tt.mockFn(d)

			u := user.New(d.repo, nil, nil, d.awsClient, nil, nil)
			err := u.DeleteAvatarUser(tt.ctx, tt.repoId)
			require.Equal(t, tt.expError, err)
		})
	}
}

func TestIsAdmin(t *testing.T) {
	type dependencies struct {
		repo      *repository_mocks.MockRepository
		awsClient *aws_mocks.MockAWSClient
	}

	userId := uuid.New()
	tests := []struct {
		name     string
		ctx      context.Context
		mockFn   func(d *dependencies)
		repoId   uuid.UUID
		expError error
	}{
		{
			name: "should return error if FindUserByID fail",
			ctx:  context.TODO(),
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(nil, errors.New("errors"))
			},
			expError: usecase.ErrInternal,
		},
		{
			name: "should return error if FindUserByID not found",
			ctx:  context.TODO(),
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expError: usecase.ErrUserNotFound,
		},
		{
			name: "should success",
			ctx:  context.TODO(),
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{Role: enums.UserRole_Admin}, nil)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				repo:      &repository_mocks.MockRepository{},
				awsClient: &aws_mocks.MockAWSClient{},
			}
			tt.mockFn(d)

			u := user.New(d.repo, nil, nil, d.awsClient, nil, nil)
			_, err := u.IsAdmin(tt.ctx, userId)
			require.Equal(t, tt.expError, err)
		})
	}
}

func TestAuthorizeOrgAccessToken(t *testing.T) {
	type dependencies struct {
		repo      *repository_mocks.MockRepository
		awsClient *aws_mocks.MockAWSClient
	}

	userId := uuid.New()
	tests := []struct {
		name     string
		ctx      context.Context
		mockFn   func(d *dependencies)
		repoId   uuid.UUID
		expError error
	}{
		{
			name: "should return error if FindAccessToken fail",
			ctx:  context.TODO(),
			mockFn: func(d *dependencies) {
				d.repo.On("FindAccessToken", mock.Anything, mock.Anything).Return(nil, errors.New("errors"))
			},
			expError: usecase.ErrInternal,
		},
		{
			name: "should return error if FindAccessToken not found",
			ctx:  context.TODO(),
			mockFn: func(d *dependencies) {
				d.repo.On("FindAccessToken", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expError: nil,
		},
		{
			name: "should success",
			ctx:  context.TODO(),
			mockFn: func(d *dependencies) {
				d.repo.On("FindAccessToken", mock.Anything, mock.Anything).Return(&entities.UserToken{UserID: userId}, nil)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				repo:      &repository_mocks.MockRepository{},
				awsClient: &aws_mocks.MockAWSClient{},
			}
			tt.mockFn(d)

			u := user.New(d.repo, nil, nil, d.awsClient, nil, nil)
			_, err := u.AuthorizeAccessToken(tt.ctx, "token")
			require.Equal(t, tt.expError, err)
		})
	}
}
