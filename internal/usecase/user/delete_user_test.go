package user_test

import (
	"context"
	"errors"
	"testing"

	gitlab_mocks "api-server/internal/gateways/gitlab/mocks"
	"api-server/internal/utils"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gorm.io/gorm"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/kubernetes/fake"
	kubetesting "k8s.io/client-go/testing"

	"api-server/internal/dto"
	"api-server/internal/entities"
	"api-server/internal/enums"
	"api-server/internal/gateways/gitlab"
	"api-server/internal/gateways/supabase"
	supabase_mocks "api-server/internal/gateways/supabase/mocks"
	repository_mocks "api-server/internal/repositories/mocks"
	"api-server/internal/usecase"
	"api-server/internal/usecase/user"
	workflow_mocks "api-server/internal/usecase/workflow/mocks"
)

func TestDeleteUser(t *testing.T) {
	uuidAdmin := uuid.New()
	uuidUser := uuid.New()
	uuidOrg := uuid.New()

	type dependencies struct {
		repo            *repository_mocks.MockRepository
		gitlabClient    *gitlab_mocks.MockGitlabClient
		supabaseClient  *supabase_mocks.MockSupabaseClient
		kubeClient      kubernetes.Interface
		workflowUsecase *workflow_mocks.MockWorkflowUsecase
	}

	testcases := []struct {
		name      string
		adminID   uuid.UUID
		userID    uuid.UUID
		mockFn    func(r *dependencies)
		expectErr error
	}{
		{
			name:    "should return usecase.ErrUserNotFound err",
			adminID: uuidAdmin,
			userID:  uuidUser,
			mockFn: func(r *dependencies) {
				r.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(nil, errors.New("error")).Once()
			},
			expectErr: usecase.ErrInternal,
		},
		{
			name:    "should return usecase.ErrUserNotFound if admin user not found",
			adminID: uuidAdmin,
			userID:  uuidUser,
			mockFn: func(r *dependencies) {
				r.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expectErr: usecase.ErrUserNotFound,
		},
		{
			name:    "should return usecase.ErrUserNotFound if user not found",
			adminID: uuidAdmin,
			userID:  uuidUser,
			mockFn: func(r *dependencies) {
				r.repo.On("FindUserByID", mock.Anything, uuidAdmin).Return(&entities.User{
					BaseModel: entities.BaseModel{
						ID: uuidAdmin,
					},
					Username:     "admin",
					RefGitUserID: 1,
					Role:         enums.UserRole_Admin,
				}, nil).Once()
				r.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expectErr: usecase.ErrUserNotFound,
		},
		{
			name:    "should return usecase.ErrUserNotFound err",
			adminID: uuidAdmin,
			userID:  uuidUser,
			mockFn: func(r *dependencies) {
				r.repo.On("FindUserByID", mock.Anything, uuidAdmin).Return(&entities.User{
					BaseModel: entities.BaseModel{
						ID: uuidAdmin,
					},
					Username:     "admin",
					RefGitUserID: 1,
					Role:         enums.UserRole_Admin,
				}, nil).Once()
				r.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectErr: usecase.ErrInternal,
		},
		{
			name:    "should return error if try to delete root user",
			adminID: uuidAdmin,
			userID:  uuidUser,
			mockFn: func(r *dependencies) {
				r.repo.On("FindUserByID", mock.Anything, uuidAdmin).Return(&entities.User{
					BaseModel: entities.BaseModel{
						ID: uuidAdmin,
					},
					Username:     "admin",
					RefGitUserID: 1,
					Role:         enums.UserRole_Admin,
				}, nil).Once()
				r.repo.On("FindUserByID", mock.Anything, uuidUser).Return(&entities.User{
					BaseModel: entities.BaseModel{
						ID: uuidUser,
					},
					Username:     "testuser",
					RefGitUserID: 1,
					Role:         enums.UserRole_User,
				}, nil).Once()
			},
			expectErr: usecase.ErrCannotDeleteRootUser,
		},
		{
			name:    "should return error if find root user returns error",
			adminID: uuidAdmin,
			userID:  uuidUser,
			mockFn: func(r *dependencies) {
				r.repo.On("FindUserByID", mock.Anything, uuidAdmin).Return(&entities.User{
					BaseModel: entities.BaseModel{
						ID: uuidAdmin,
					},
					Username:     "admin",
					RefGitUserID: 1,
					Role:         enums.UserRole_Admin,
				}, nil).Once()
				r.repo.On("FindUserByID", mock.Anything, uuidUser).Return(&entities.User{
					BaseModel: entities.BaseModel{
						ID: uuidUser,
					},
					Username:     "testuser",
					RefGitUserID: 2,
					Role:         enums.UserRole_User,
				}, nil).Once()
				r.repo.On("FindUser", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name:    "should return error if Delete returns error",
			adminID: uuidAdmin,
			userID:  uuidUser,
			mockFn: func(r *dependencies) {
				r.repo.On("FindUserByID", mock.Anything, uuidAdmin).Return(&entities.User{
					BaseModel: entities.BaseModel{
						ID: uuidAdmin,
					},
					Username:     "admin",
					RefGitUserID: 1,
					Role:         enums.UserRole_Admin,
				}, nil).Once()
				r.repo.On("FindUserByID", mock.Anything, uuidUser).Return(&entities.User{
					BaseModel: entities.BaseModel{
						ID: uuidUser,
					},
					Username:     "testuser",
					RefGitUserID: 2,
					Role:         enums.UserRole_User,
				}, nil).Once()
				r.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{
					BaseModel: entities.BaseModel{
						ID: uuidUser,
					},
					Username:          "rootuser",
					RefGitUserID:      1,
					Role:              enums.UserRole_Admin,
					GitlabAccessToken: utils.Ptr("gitlab_access_token"),
				}, nil)
				r.repo.On("Transaction", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
				}).Return(errors.New("delete error"))

			},
			expectErr: errors.New("delete error"),
		},
		{
			name:    "should return nil if Delete succeeds",
			adminID: uuidAdmin,
			userID:  uuidUser,
			mockFn: func(r *dependencies) {
				r.repo.On("FindUserByID", mock.Anything, uuidAdmin).Return(&entities.User{
					BaseModel: entities.BaseModel{
						ID: uuidAdmin,
					},
					Username:     "admin",
					RefGitUserID: 1,
					Role:         enums.UserRole_Admin,
				}, nil).Once()
				r.repo.On("FindUserByID", mock.Anything, uuidUser).Return(&entities.User{
					BaseModel: entities.BaseModel{
						ID: uuidUser,
					},
					Username:     "testuser",
					RefGitUserID: 2,
					Role:         enums.UserRole_User,
				}, nil).Once()
				r.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{
					BaseModel: entities.BaseModel{
						ID: uuidUser,
					},
					Username:          "rootuser",
					RefGitUserID:      1,
					Role:              enums.UserRole_Admin,
					GitlabAccessToken: utils.Ptr("gitlab_access_token"),
				}, nil)

				r.repo.On("DeleteRepositoryMemberByUserID", mock.Anything, uuidUser).Return(nil)

				userRepoUUID := uuid.New()
				r.repo.On("ListRepositories", mock.Anything, dto.GetRepositoriesInput{
					UserID: uuidUser.String(),
				}).Return([]entities.Repository{
					{
						BaseModel: entities.BaseModel{
							ID: uuid.New(),
						},
						RefGitRepoID: 1,
						Name:         "org_repo",
						UserID:       &uuidUser,
						OrgID:        &uuidOrg,
						Deployment: &entities.Deployment{
							BaseModel: entities.BaseModel{
								ID: uuid.New(),
							},
						},
					},
					{
						BaseModel: entities.BaseModel{
							ID: userRepoUUID,
						},
						RefGitRepoID: 2,
						Name:         "user_repo",
						UserID:       &uuidUser,
						Deployment: &entities.Deployment{
							BaseModel: entities.BaseModel{
								ID: uuid.New(),
							},
						},
					},
				}, nil)

				r.repo.On("RemoveUserInRepository", mock.Anything, mock.Anything).Return(nil)
				r.repo.On("RemoveUserInDeployments", mock.Anything, mock.Anything).Return(nil)
				r.gitlabClient.On("GetMemberOfProject", mock.Anything, mock.Anything).Return(&gitlab.GetMemberOfProjectResponse{}, nil)
				r.gitlabClient.On("RemoveUserFromProject", mock.Anything, mock.Anything).Return(nil)

				fakeClientSet := fake.NewSimpleClientset()
				fakeClientSet.PrependReactor("delete", "deployments", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, nil, nil
				})
				fakeClientSet.PrependReactor("delete", "services", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, nil, nil
				})
				fakeClientSet.PrependReactor("delete", "ingresses", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, nil, nil
				})
				r.kubeClient = fakeClientSet

				r.repo.On("DeleteRepositoryMemberByRepositoryID", mock.Anything, mock.Anything).Return(nil)
				r.repo.On("DeleteDeploymentsByRepoID", mock.Anything, mock.Anything).Return(nil)
				r.repo.On("DeleteById", mock.Anything, mock.Anything, userRepoUUID).Return(nil)
				r.gitlabClient.On("DeleteProject", mock.Anything, mock.Anything).Return(nil)

				orgID := uuid.New()
				r.repo.On("ListOrganizations", mock.Anything, dto.ListOrganizationsInput{UserId: uuidUser}).Return(1, []entities.Organization{
					{
						BaseModel: entities.BaseModel{
							ID: orgID,
						},
						Name:          "org",
						RefGitGroupID: 1,
					},
				}, nil)
				r.repo.On("FindOrgGitGroup", mock.Anything, orgID).Return(&entities.DefaultGitGroup{}, nil)
				r.gitlabClient.On("GetMemberOfGroup", mock.Anything, mock.Anything).Return(&gitlab.GetMemberOfGroupResponse{}, nil)
				r.gitlabClient.On("RemoveUserFromGroup", mock.Anything, mock.Anything).Return(nil)
				r.repo.On("DeleteOrgMembersByUserID", mock.Anything, mock.Anything).Return(nil)

				r.repo.On("DeleteSSHKeysByUserID", mock.Anything, uuidUser).Return(nil)
				r.repo.On("DeleteAllUserAccessToken", mock.Anything, uuidUser).Return(nil)

				r.repo.On("FindUserGitGroup", mock.Anything, uuidUser).Return(&entities.DefaultGitGroup{}, nil)
				r.gitlabClient.On("DeleteGroup", mock.Anything, mock.Anything).Return(nil)
				r.repo.On("DeleteUserGitGroup", mock.Anything, uuidUser).Return(nil)
				r.repo.On("DeleteById", mock.Anything, mock.Anything, uuidUser).Return(nil)
				r.gitlabClient.On("DeleteUser", mock.Anything, mock.Anything).Return(nil)
				r.supabaseClient.On("DeleteUser", mock.Anything, supabase.DeleteUserRequest{
					ID: uuidUser,
				}).Return(nil)

				r.workflowUsecase.On("TerminateWorkflow", mock.Anything, mock.Anything, mock.Anything).Return(nil)

				r.repo.On("Transaction", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(context.Background())
				}).Return(nil)

			},
			expectErr: nil,
		},
	}

	for _, testcase := range testcases {
		t.Run(testcase.name, func(t *testing.T) {
			d := &dependencies{
				repo:            &repository_mocks.MockRepository{},
				gitlabClient:    &gitlab_mocks.MockGitlabClient{},
				supabaseClient:  &supabase_mocks.MockSupabaseClient{},
				workflowUsecase: &workflow_mocks.MockWorkflowUsecase{},
			}
			testcase.mockFn(d)

			userUsecase := user.New(d.repo, d.gitlabClient, d.supabaseClient, nil, d.kubeClient, d.workflowUsecase)
			err := userUsecase.DeleteUser(context.Background(), testcase.adminID, testcase.userID)

			// assertion
			assert.Equal(t, testcase.expectErr, err)
			// r.AssertExpectations(t)
		})
	}
}
