package user

import (
	"context"
	"strings"

	"api-server/internal/dto"
	"api-server/internal/gateways/supabase"
	"api-server/internal/usecase"
	"api-server/pkg/oteltrace"

	"github.com/google/uuid"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
)

// ChangePassword implements the UserUsecase interface for changing a user's password.
// It verifies the current password and updates it in Supabase.
//
// Parameters:
//   - ctx: Context for the operation
//   - userID: UUID of the user changing their password
//   - userAccessToken: Access token for Supabase authentication
//   - input: ChangePasswordInput containing current and new password
//
// Returns:
//   - error: Any error that occurred during password change
func (i *impl) ChangePassword(ctx context.Context, userID uuid.UUID, userAccessToken string, input dto.ChangePasswordInput) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.user.ChangePassword")
	defer span.End()

	span.SetAttributes(attribute.String("user_id", userID.String()))

	err := i.repository.CheckCurrentPassword(ctx, userID, input.CurrentPassword)
	if err != nil {
		span.RecordError(err)
		if strings.Contains(err.Error(), "incorrect password") {
			span.SetStatus(codes.Error, "incorrect password")
			return usecase.ErrCurrentPasswordIsIncorrect
		}
		span.SetStatus(codes.Error, "failed to check current user password")
		return usecase.ErrFailedToChangePassword
	}

	err = i.supabaseClient.ChangePassword(ctx, userAccessToken, supabase.ChangePasswordRequest{
		NewPassword: input.NewPassword,
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to change user password")
		span.RecordError(err)
		return usecase.ErrFailedToChangePassword
	}

	span.AddEvent("successfully changed password")
	span.SetStatus(codes.Ok, "successfully changed password")

	return nil
}
