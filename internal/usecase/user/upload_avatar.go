package user

import (
	"context"
	"errors"

	"github.com/google/uuid"
	"go.opentelemetry.io/otel/codes"
	"gorm.io/gorm"

	"api-server/internal/dto"
	"api-server/internal/enums"
	"api-server/internal/usecase"
	"api-server/pkg/oteltrace"
)

// UploadAvatarUser implements the UserUsecase interface for uploading a user's avatar.
// It handles the upload of the avatar image to S3 and updates the user's avatar URL in the database.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: UploadUserAvatarInput containing the user ID and avatar file
//
// Returns:
//   - string: The URL of the uploaded avatar
//   - error: Any error that occurred during the upload
func (u *impl) UploadAvatarUser(ctx context.Context, input dto.UploadUserAvatarInput) (string, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.user.UploadAvatarUser")
	defer span.End()

	user, err := u.repository.FindUserByID(ctx, input.UserId)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			span.SetStatus(codes.Error, "user not found")
			span.RecordError(err)
			return "", usecase.ErrUserNotFound
		}

		span.SetStatus(codes.Error, "failed to find user by id")
		span.RecordError(err)
		return "", err
	}

	imageUrl, err := u.aws.UploadImage(ctx, *input.File, string(enums.S3BucketPrefix_Avatar))
	if err != nil {
		span.SetStatus(codes.Error, "failed to upload image to s3")
		span.RecordError(err)
		return "", err
	}

	user.Avatar = &imageUrl

	if err := u.repository.Save(ctx, user); err != nil {
		span.SetStatus(codes.Error, "failed to save user")
		span.RecordError(err)
		return "", err
	}

	span.AddEvent("user avatar uploaded successfully")
	span.SetStatus(codes.Ok, "user avatar uploaded successfully")
	return imageUrl, nil
}

// DeleteAvatarUser implements the UserUsecase interface for deleting a user's avatar.
// It removes the avatar image from S3 and clears the avatar URL from the user's record.
//
// Parameters:
//   - ctx: Context for the operation
//   - userId: UUID of the user whose avatar is being deleted
//
// Returns:
//   - error: Any error that occurred during the deletion
func (u *impl) DeleteAvatarUser(ctx context.Context, userId uuid.UUID) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.user.DeleteAvatarUser")
	defer span.End()

	user, err := u.repository.FindUserByID(ctx, userId)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			span.SetStatus(codes.Error, "user not found")
			span.RecordError(err)
			return usecase.ErrUserNotFound
		}

		span.SetStatus(codes.Error, "failed to find user by id")
		span.RecordError(err)
		return err
	}

	if user.Avatar != nil {
		if err = u.aws.DeleteImage(ctx, *user.Avatar); err != nil {
			span.SetStatus(codes.Error, "failed to delete s3 image")
			span.RecordError(err)
			return err
		}
	}

	user.Avatar = nil
	if err := u.repository.Save(ctx, user); err != nil {
		span.SetStatus(codes.Error, "failed to save user")
		span.RecordError(err)
		return err
	}

	span.AddEvent("user avatar deleted successfully")
	span.SetStatus(codes.Ok, "user avatar deleted successfully")
	return nil
}
