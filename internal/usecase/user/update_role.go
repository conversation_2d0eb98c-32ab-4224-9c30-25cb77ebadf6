package user

import (
	"context"
	"errors"

	"go.opentelemetry.io/otel/codes"
	"gorm.io/gorm"

	"api-server/internal/dto"
	"api-server/internal/usecase"
	"api-server/pkg/oteltrace"
)

// UpdateRoleUser implements the UserUsecase interface for updating a user's role.
// It updates the user's role in the database and handles error cases appropriately.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: UpdateRoleInput containing the user ID and new role
//
// Returns:
//   - error: Any error that occurred during the role update
func (i *impl) UpdateRoleUser(ctx context.Context, input dto.UpdateRoleInput) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.user.UpdateRoleUser")
	defer span.End()

	user, err := i.repository.FindUserByID(ctx, input.UserID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			span.SetStatus(codes.Error, "user not found")
			span.RecordError(err)
			return usecase.ErrUserNotFound
		}
		span.SetStatus(codes.Error, "failed to find user by id")
		span.RecordError(err)
		return err
	}

	user.Role = input.Role
	err = i.repository.Save(ctx, user)
	if err != nil {
		span.SetStatus(codes.Error, "failed to save user")
		span.RecordError(err)
		return err
	}

	span.AddEvent("user role updated successfully")
	span.SetStatus(codes.Ok, "user role updated successfully")
	return nil
}
