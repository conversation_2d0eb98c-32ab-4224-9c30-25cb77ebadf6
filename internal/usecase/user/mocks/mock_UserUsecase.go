// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	dto "api-server/internal/dto"
	enums "api-server/internal/enums"
	context "context"

	mock "github.com/stretchr/testify/mock"

	types "api-server/internal/types"

	uuid "github.com/google/uuid"
)

// MockUserUsecase is an autogenerated mock type for the UserUsecase type
type MockUserUsecase struct {
	mock.Mock
}

type MockUserUsecase_Expecter struct {
	mock *mock.Mock
}

func (_m *MockUserUsecase) EXPECT() *MockUserUsecase_Expecter {
	return &MockUserUsecase_Expecter{mock: &_m.Mock}
}

// AuthorizeAccessToken provides a mock function with given fields: ctx, accessToken
func (_m *MockUserUsecase) AuthorizeAccessToken(ctx context.Context, accessToken string) (*uuid.UUID, error) {
	ret := _m.Called(ctx, accessToken)

	if len(ret) == 0 {
		panic("no return value specified for AuthorizeAccessToken")
	}

	var r0 *uuid.UUID
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*uuid.UUID, error)); ok {
		return rf(ctx, accessToken)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *uuid.UUID); ok {
		r0 = rf(ctx, accessToken)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*uuid.UUID)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, accessToken)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockUserUsecase_AuthorizeAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AuthorizeAccessToken'
type MockUserUsecase_AuthorizeAccessToken_Call struct {
	*mock.Call
}

// AuthorizeAccessToken is a helper method to define mock.On call
//   - ctx context.Context
//   - accessToken string
func (_e *MockUserUsecase_Expecter) AuthorizeAccessToken(ctx interface{}, accessToken interface{}) *MockUserUsecase_AuthorizeAccessToken_Call {
	return &MockUserUsecase_AuthorizeAccessToken_Call{Call: _e.mock.On("AuthorizeAccessToken", ctx, accessToken)}
}

func (_c *MockUserUsecase_AuthorizeAccessToken_Call) Run(run func(ctx context.Context, accessToken string)) *MockUserUsecase_AuthorizeAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockUserUsecase_AuthorizeAccessToken_Call) Return(_a0 *uuid.UUID, _a1 error) *MockUserUsecase_AuthorizeAccessToken_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockUserUsecase_AuthorizeAccessToken_Call) RunAndReturn(run func(context.Context, string) (*uuid.UUID, error)) *MockUserUsecase_AuthorizeAccessToken_Call {
	_c.Call.Return(run)
	return _c
}

// AuthorizeECR provides a mock function with given fields: ctx, userID, ecrID, requiredPermissions
func (_m *MockUserUsecase) AuthorizeECR(ctx context.Context, userID uuid.UUID, ecrID uuid.UUID, requiredPermissions []enums.ECRPermission) (bool, error) {
	ret := _m.Called(ctx, userID, ecrID, requiredPermissions)

	if len(ret) == 0 {
		panic("no return value specified for AuthorizeECR")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID, uuid.UUID, []enums.ECRPermission) (bool, error)); ok {
		return rf(ctx, userID, ecrID, requiredPermissions)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID, uuid.UUID, []enums.ECRPermission) bool); ok {
		r0 = rf(ctx, userID, ecrID, requiredPermissions)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(context.Context, uuid.UUID, uuid.UUID, []enums.ECRPermission) error); ok {
		r1 = rf(ctx, userID, ecrID, requiredPermissions)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockUserUsecase_AuthorizeECR_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AuthorizeECR'
type MockUserUsecase_AuthorizeECR_Call struct {
	*mock.Call
}

// AuthorizeECR is a helper method to define mock.On call
//   - ctx context.Context
//   - userID uuid.UUID
//   - ecrID uuid.UUID
//   - requiredPermissions []enums.ECRPermission
func (_e *MockUserUsecase_Expecter) AuthorizeECR(ctx interface{}, userID interface{}, ecrID interface{}, requiredPermissions interface{}) *MockUserUsecase_AuthorizeECR_Call {
	return &MockUserUsecase_AuthorizeECR_Call{Call: _e.mock.On("AuthorizeECR", ctx, userID, ecrID, requiredPermissions)}
}

func (_c *MockUserUsecase_AuthorizeECR_Call) Run(run func(ctx context.Context, userID uuid.UUID, ecrID uuid.UUID, requiredPermissions []enums.ECRPermission)) *MockUserUsecase_AuthorizeECR_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID), args[2].(uuid.UUID), args[3].([]enums.ECRPermission))
	})
	return _c
}

func (_c *MockUserUsecase_AuthorizeECR_Call) Return(_a0 bool, _a1 error) *MockUserUsecase_AuthorizeECR_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockUserUsecase_AuthorizeECR_Call) RunAndReturn(run func(context.Context, uuid.UUID, uuid.UUID, []enums.ECRPermission) (bool, error)) *MockUserUsecase_AuthorizeECR_Call {
	_c.Call.Return(run)
	return _c
}

// AuthorizeOrg provides a mock function with given fields: ctx, userID, orgID, requiredPermissions
func (_m *MockUserUsecase) AuthorizeOrg(ctx context.Context, userID uuid.UUID, orgID uuid.UUID, requiredPermissions []enums.RepoPermission) (bool, error) {
	ret := _m.Called(ctx, userID, orgID, requiredPermissions)

	if len(ret) == 0 {
		panic("no return value specified for AuthorizeOrg")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID, uuid.UUID, []enums.RepoPermission) (bool, error)); ok {
		return rf(ctx, userID, orgID, requiredPermissions)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID, uuid.UUID, []enums.RepoPermission) bool); ok {
		r0 = rf(ctx, userID, orgID, requiredPermissions)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(context.Context, uuid.UUID, uuid.UUID, []enums.RepoPermission) error); ok {
		r1 = rf(ctx, userID, orgID, requiredPermissions)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockUserUsecase_AuthorizeOrg_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AuthorizeOrg'
type MockUserUsecase_AuthorizeOrg_Call struct {
	*mock.Call
}

// AuthorizeOrg is a helper method to define mock.On call
//   - ctx context.Context
//   - userID uuid.UUID
//   - orgID uuid.UUID
//   - requiredPermissions []enums.RepoPermission
func (_e *MockUserUsecase_Expecter) AuthorizeOrg(ctx interface{}, userID interface{}, orgID interface{}, requiredPermissions interface{}) *MockUserUsecase_AuthorizeOrg_Call {
	return &MockUserUsecase_AuthorizeOrg_Call{Call: _e.mock.On("AuthorizeOrg", ctx, userID, orgID, requiredPermissions)}
}

func (_c *MockUserUsecase_AuthorizeOrg_Call) Run(run func(ctx context.Context, userID uuid.UUID, orgID uuid.UUID, requiredPermissions []enums.RepoPermission)) *MockUserUsecase_AuthorizeOrg_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID), args[2].(uuid.UUID), args[3].([]enums.RepoPermission))
	})
	return _c
}

func (_c *MockUserUsecase_AuthorizeOrg_Call) Return(_a0 bool, _a1 error) *MockUserUsecase_AuthorizeOrg_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockUserUsecase_AuthorizeOrg_Call) RunAndReturn(run func(context.Context, uuid.UUID, uuid.UUID, []enums.RepoPermission) (bool, error)) *MockUserUsecase_AuthorizeOrg_Call {
	_c.Call.Return(run)
	return _c
}

// AuthorizePlatform provides a mock function with given fields: ctx, userID, requiredPermissions
func (_m *MockUserUsecase) AuthorizePlatform(ctx context.Context, userID uuid.UUID, requiredPermissions []enums.AppPermission) (bool, error) {
	ret := _m.Called(ctx, userID, requiredPermissions)

	if len(ret) == 0 {
		panic("no return value specified for AuthorizePlatform")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID, []enums.AppPermission) (bool, error)); ok {
		return rf(ctx, userID, requiredPermissions)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID, []enums.AppPermission) bool); ok {
		r0 = rf(ctx, userID, requiredPermissions)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(context.Context, uuid.UUID, []enums.AppPermission) error); ok {
		r1 = rf(ctx, userID, requiredPermissions)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockUserUsecase_AuthorizePlatform_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AuthorizePlatform'
type MockUserUsecase_AuthorizePlatform_Call struct {
	*mock.Call
}

// AuthorizePlatform is a helper method to define mock.On call
//   - ctx context.Context
//   - userID uuid.UUID
//   - requiredPermissions []enums.AppPermission
func (_e *MockUserUsecase_Expecter) AuthorizePlatform(ctx interface{}, userID interface{}, requiredPermissions interface{}) *MockUserUsecase_AuthorizePlatform_Call {
	return &MockUserUsecase_AuthorizePlatform_Call{Call: _e.mock.On("AuthorizePlatform", ctx, userID, requiredPermissions)}
}

func (_c *MockUserUsecase_AuthorizePlatform_Call) Run(run func(ctx context.Context, userID uuid.UUID, requiredPermissions []enums.AppPermission)) *MockUserUsecase_AuthorizePlatform_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID), args[2].([]enums.AppPermission))
	})
	return _c
}

func (_c *MockUserUsecase_AuthorizePlatform_Call) Return(_a0 bool, _a1 error) *MockUserUsecase_AuthorizePlatform_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockUserUsecase_AuthorizePlatform_Call) RunAndReturn(run func(context.Context, uuid.UUID, []enums.AppPermission) (bool, error)) *MockUserUsecase_AuthorizePlatform_Call {
	_c.Call.Return(run)
	return _c
}

// AuthorizeRepo provides a mock function with given fields: ctx, userID, repoID, requiredPermissions
func (_m *MockUserUsecase) AuthorizeRepo(ctx context.Context, userID uuid.UUID, repoID types.RepoID, requiredPermissions []enums.RepoPermission) (bool, error) {
	ret := _m.Called(ctx, userID, repoID, requiredPermissions)

	if len(ret) == 0 {
		panic("no return value specified for AuthorizeRepo")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID, types.RepoID, []enums.RepoPermission) (bool, error)); ok {
		return rf(ctx, userID, repoID, requiredPermissions)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID, types.RepoID, []enums.RepoPermission) bool); ok {
		r0 = rf(ctx, userID, repoID, requiredPermissions)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(context.Context, uuid.UUID, types.RepoID, []enums.RepoPermission) error); ok {
		r1 = rf(ctx, userID, repoID, requiredPermissions)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockUserUsecase_AuthorizeRepo_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AuthorizeRepo'
type MockUserUsecase_AuthorizeRepo_Call struct {
	*mock.Call
}

// AuthorizeRepo is a helper method to define mock.On call
//   - ctx context.Context
//   - userID uuid.UUID
//   - repoID types.RepoID
//   - requiredPermissions []enums.RepoPermission
func (_e *MockUserUsecase_Expecter) AuthorizeRepo(ctx interface{}, userID interface{}, repoID interface{}, requiredPermissions interface{}) *MockUserUsecase_AuthorizeRepo_Call {
	return &MockUserUsecase_AuthorizeRepo_Call{Call: _e.mock.On("AuthorizeRepo", ctx, userID, repoID, requiredPermissions)}
}

func (_c *MockUserUsecase_AuthorizeRepo_Call) Run(run func(ctx context.Context, userID uuid.UUID, repoID types.RepoID, requiredPermissions []enums.RepoPermission)) *MockUserUsecase_AuthorizeRepo_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID), args[2].(types.RepoID), args[3].([]enums.RepoPermission))
	})
	return _c
}

func (_c *MockUserUsecase_AuthorizeRepo_Call) Return(_a0 bool, _a1 error) *MockUserUsecase_AuthorizeRepo_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockUserUsecase_AuthorizeRepo_Call) RunAndReturn(run func(context.Context, uuid.UUID, types.RepoID, []enums.RepoPermission) (bool, error)) *MockUserUsecase_AuthorizeRepo_Call {
	_c.Call.Return(run)
	return _c
}

// ChangePassword provides a mock function with given fields: ctx, userID, userAccessToken, input
func (_m *MockUserUsecase) ChangePassword(ctx context.Context, userID uuid.UUID, userAccessToken string, input dto.ChangePasswordInput) error {
	ret := _m.Called(ctx, userID, userAccessToken, input)

	if len(ret) == 0 {
		panic("no return value specified for ChangePassword")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID, string, dto.ChangePasswordInput) error); ok {
		r0 = rf(ctx, userID, userAccessToken, input)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockUserUsecase_ChangePassword_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ChangePassword'
type MockUserUsecase_ChangePassword_Call struct {
	*mock.Call
}

// ChangePassword is a helper method to define mock.On call
//   - ctx context.Context
//   - userID uuid.UUID
//   - userAccessToken string
//   - input dto.ChangePasswordInput
func (_e *MockUserUsecase_Expecter) ChangePassword(ctx interface{}, userID interface{}, userAccessToken interface{}, input interface{}) *MockUserUsecase_ChangePassword_Call {
	return &MockUserUsecase_ChangePassword_Call{Call: _e.mock.On("ChangePassword", ctx, userID, userAccessToken, input)}
}

func (_c *MockUserUsecase_ChangePassword_Call) Run(run func(ctx context.Context, userID uuid.UUID, userAccessToken string, input dto.ChangePasswordInput)) *MockUserUsecase_ChangePassword_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID), args[2].(string), args[3].(dto.ChangePasswordInput))
	})
	return _c
}

func (_c *MockUserUsecase_ChangePassword_Call) Return(_a0 error) *MockUserUsecase_ChangePassword_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockUserUsecase_ChangePassword_Call) RunAndReturn(run func(context.Context, uuid.UUID, string, dto.ChangePasswordInput) error) *MockUserUsecase_ChangePassword_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteAvatarUser provides a mock function with given fields: ctx, userId
func (_m *MockUserUsecase) DeleteAvatarUser(ctx context.Context, userId uuid.UUID) error {
	ret := _m.Called(ctx, userId)

	if len(ret) == 0 {
		panic("no return value specified for DeleteAvatarUser")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) error); ok {
		r0 = rf(ctx, userId)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockUserUsecase_DeleteAvatarUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteAvatarUser'
type MockUserUsecase_DeleteAvatarUser_Call struct {
	*mock.Call
}

// DeleteAvatarUser is a helper method to define mock.On call
//   - ctx context.Context
//   - userId uuid.UUID
func (_e *MockUserUsecase_Expecter) DeleteAvatarUser(ctx interface{}, userId interface{}) *MockUserUsecase_DeleteAvatarUser_Call {
	return &MockUserUsecase_DeleteAvatarUser_Call{Call: _e.mock.On("DeleteAvatarUser", ctx, userId)}
}

func (_c *MockUserUsecase_DeleteAvatarUser_Call) Run(run func(ctx context.Context, userId uuid.UUID)) *MockUserUsecase_DeleteAvatarUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *MockUserUsecase_DeleteAvatarUser_Call) Return(_a0 error) *MockUserUsecase_DeleteAvatarUser_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockUserUsecase_DeleteAvatarUser_Call) RunAndReturn(run func(context.Context, uuid.UUID) error) *MockUserUsecase_DeleteAvatarUser_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteUser provides a mock function with given fields: ctx, adminID, userID
func (_m *MockUserUsecase) DeleteUser(ctx context.Context, adminID uuid.UUID, userID uuid.UUID) error {
	ret := _m.Called(ctx, adminID, userID)

	if len(ret) == 0 {
		panic("no return value specified for DeleteUser")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID, uuid.UUID) error); ok {
		r0 = rf(ctx, adminID, userID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockUserUsecase_DeleteUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteUser'
type MockUserUsecase_DeleteUser_Call struct {
	*mock.Call
}

// DeleteUser is a helper method to define mock.On call
//   - ctx context.Context
//   - adminID uuid.UUID
//   - userID uuid.UUID
func (_e *MockUserUsecase_Expecter) DeleteUser(ctx interface{}, adminID interface{}, userID interface{}) *MockUserUsecase_DeleteUser_Call {
	return &MockUserUsecase_DeleteUser_Call{Call: _e.mock.On("DeleteUser", ctx, adminID, userID)}
}

func (_c *MockUserUsecase_DeleteUser_Call) Run(run func(ctx context.Context, adminID uuid.UUID, userID uuid.UUID)) *MockUserUsecase_DeleteUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID), args[2].(uuid.UUID))
	})
	return _c
}

func (_c *MockUserUsecase_DeleteUser_Call) Return(_a0 error) *MockUserUsecase_DeleteUser_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockUserUsecase_DeleteUser_Call) RunAndReturn(run func(context.Context, uuid.UUID, uuid.UUID) error) *MockUserUsecase_DeleteUser_Call {
	_c.Call.Return(run)
	return _c
}

// GetCurrentUser provides a mock function with given fields: ctx, userId
func (_m *MockUserUsecase) GetCurrentUser(ctx context.Context, userId uuid.UUID) (dto.User, error) {
	ret := _m.Called(ctx, userId)

	if len(ret) == 0 {
		panic("no return value specified for GetCurrentUser")
	}

	var r0 dto.User
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) (dto.User, error)); ok {
		return rf(ctx, userId)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) dto.User); ok {
		r0 = rf(ctx, userId)
	} else {
		r0 = ret.Get(0).(dto.User)
	}

	if rf, ok := ret.Get(1).(func(context.Context, uuid.UUID) error); ok {
		r1 = rf(ctx, userId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockUserUsecase_GetCurrentUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetCurrentUser'
type MockUserUsecase_GetCurrentUser_Call struct {
	*mock.Call
}

// GetCurrentUser is a helper method to define mock.On call
//   - ctx context.Context
//   - userId uuid.UUID
func (_e *MockUserUsecase_Expecter) GetCurrentUser(ctx interface{}, userId interface{}) *MockUserUsecase_GetCurrentUser_Call {
	return &MockUserUsecase_GetCurrentUser_Call{Call: _e.mock.On("GetCurrentUser", ctx, userId)}
}

func (_c *MockUserUsecase_GetCurrentUser_Call) Run(run func(ctx context.Context, userId uuid.UUID)) *MockUserUsecase_GetCurrentUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *MockUserUsecase_GetCurrentUser_Call) Return(_a0 dto.User, _a1 error) *MockUserUsecase_GetCurrentUser_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockUserUsecase_GetCurrentUser_Call) RunAndReturn(run func(context.Context, uuid.UUID) (dto.User, error)) *MockUserUsecase_GetCurrentUser_Call {
	_c.Call.Return(run)
	return _c
}

// IsAdmin provides a mock function with given fields: ctx, userID
func (_m *MockUserUsecase) IsAdmin(ctx context.Context, userID uuid.UUID) (bool, error) {
	ret := _m.Called(ctx, userID)

	if len(ret) == 0 {
		panic("no return value specified for IsAdmin")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) (bool, error)); ok {
		return rf(ctx, userID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) bool); ok {
		r0 = rf(ctx, userID)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(context.Context, uuid.UUID) error); ok {
		r1 = rf(ctx, userID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockUserUsecase_IsAdmin_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'IsAdmin'
type MockUserUsecase_IsAdmin_Call struct {
	*mock.Call
}

// IsAdmin is a helper method to define mock.On call
//   - ctx context.Context
//   - userID uuid.UUID
func (_e *MockUserUsecase_Expecter) IsAdmin(ctx interface{}, userID interface{}) *MockUserUsecase_IsAdmin_Call {
	return &MockUserUsecase_IsAdmin_Call{Call: _e.mock.On("IsAdmin", ctx, userID)}
}

func (_c *MockUserUsecase_IsAdmin_Call) Run(run func(ctx context.Context, userID uuid.UUID)) *MockUserUsecase_IsAdmin_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *MockUserUsecase_IsAdmin_Call) Return(_a0 bool, _a1 error) *MockUserUsecase_IsAdmin_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockUserUsecase_IsAdmin_Call) RunAndReturn(run func(context.Context, uuid.UUID) (bool, error)) *MockUserUsecase_IsAdmin_Call {
	_c.Call.Return(run)
	return _c
}

// ListUsers provides a mock function with given fields: ctx, input
func (_m *MockUserUsecase) ListUsers(ctx context.Context, input dto.ListUsersInput) (dto.ListUsersOutput, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for ListUsers")
	}

	var r0 dto.ListUsersOutput
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.ListUsersInput) (dto.ListUsersOutput, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, dto.ListUsersInput) dto.ListUsersOutput); ok {
		r0 = rf(ctx, input)
	} else {
		r0 = ret.Get(0).(dto.ListUsersOutput)
	}

	if rf, ok := ret.Get(1).(func(context.Context, dto.ListUsersInput) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockUserUsecase_ListUsers_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListUsers'
type MockUserUsecase_ListUsers_Call struct {
	*mock.Call
}

// ListUsers is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.ListUsersInput
func (_e *MockUserUsecase_Expecter) ListUsers(ctx interface{}, input interface{}) *MockUserUsecase_ListUsers_Call {
	return &MockUserUsecase_ListUsers_Call{Call: _e.mock.On("ListUsers", ctx, input)}
}

func (_c *MockUserUsecase_ListUsers_Call) Run(run func(ctx context.Context, input dto.ListUsersInput)) *MockUserUsecase_ListUsers_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.ListUsersInput))
	})
	return _c
}

func (_c *MockUserUsecase_ListUsers_Call) Return(_a0 dto.ListUsersOutput, _a1 error) *MockUserUsecase_ListUsers_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockUserUsecase_ListUsers_Call) RunAndReturn(run func(context.Context, dto.ListUsersInput) (dto.ListUsersOutput, error)) *MockUserUsecase_ListUsers_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateRoleUser provides a mock function with given fields: ctx, input
func (_m *MockUserUsecase) UpdateRoleUser(ctx context.Context, input dto.UpdateRoleInput) error {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for UpdateRoleUser")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.UpdateRoleInput) error); ok {
		r0 = rf(ctx, input)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockUserUsecase_UpdateRoleUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateRoleUser'
type MockUserUsecase_UpdateRoleUser_Call struct {
	*mock.Call
}

// UpdateRoleUser is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.UpdateRoleInput
func (_e *MockUserUsecase_Expecter) UpdateRoleUser(ctx interface{}, input interface{}) *MockUserUsecase_UpdateRoleUser_Call {
	return &MockUserUsecase_UpdateRoleUser_Call{Call: _e.mock.On("UpdateRoleUser", ctx, input)}
}

func (_c *MockUserUsecase_UpdateRoleUser_Call) Run(run func(ctx context.Context, input dto.UpdateRoleInput)) *MockUserUsecase_UpdateRoleUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.UpdateRoleInput))
	})
	return _c
}

func (_c *MockUserUsecase_UpdateRoleUser_Call) Return(_a0 error) *MockUserUsecase_UpdateRoleUser_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockUserUsecase_UpdateRoleUser_Call) RunAndReturn(run func(context.Context, dto.UpdateRoleInput) error) *MockUserUsecase_UpdateRoleUser_Call {
	_c.Call.Return(run)
	return _c
}

// UploadAvatarUser provides a mock function with given fields: ctx, input
func (_m *MockUserUsecase) UploadAvatarUser(ctx context.Context, input dto.UploadUserAvatarInput) (string, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for UploadAvatarUser")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.UploadUserAvatarInput) (string, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, dto.UploadUserAvatarInput) string); ok {
		r0 = rf(ctx, input)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(context.Context, dto.UploadUserAvatarInput) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockUserUsecase_UploadAvatarUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UploadAvatarUser'
type MockUserUsecase_UploadAvatarUser_Call struct {
	*mock.Call
}

// UploadAvatarUser is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.UploadUserAvatarInput
func (_e *MockUserUsecase_Expecter) UploadAvatarUser(ctx interface{}, input interface{}) *MockUserUsecase_UploadAvatarUser_Call {
	return &MockUserUsecase_UploadAvatarUser_Call{Call: _e.mock.On("UploadAvatarUser", ctx, input)}
}

func (_c *MockUserUsecase_UploadAvatarUser_Call) Run(run func(ctx context.Context, input dto.UploadUserAvatarInput)) *MockUserUsecase_UploadAvatarUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.UploadUserAvatarInput))
	})
	return _c
}

func (_c *MockUserUsecase_UploadAvatarUser_Call) Return(_a0 string, _a1 error) *MockUserUsecase_UploadAvatarUser_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockUserUsecase_UploadAvatarUser_Call) RunAndReturn(run func(context.Context, dto.UploadUserAvatarInput) (string, error)) *MockUserUsecase_UploadAvatarUser_Call {
	_c.Call.Return(run)
	return _c
}

// UpsertUser provides a mock function with given fields: ctx, userID
func (_m *MockUserUsecase) UpsertUser(ctx context.Context, userID uuid.UUID) error {
	ret := _m.Called(ctx, userID)

	if len(ret) == 0 {
		panic("no return value specified for UpsertUser")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) error); ok {
		r0 = rf(ctx, userID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockUserUsecase_UpsertUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpsertUser'
type MockUserUsecase_UpsertUser_Call struct {
	*mock.Call
}

// UpsertUser is a helper method to define mock.On call
//   - ctx context.Context
//   - userID uuid.UUID
func (_e *MockUserUsecase_Expecter) UpsertUser(ctx interface{}, userID interface{}) *MockUserUsecase_UpsertUser_Call {
	return &MockUserUsecase_UpsertUser_Call{Call: _e.mock.On("UpsertUser", ctx, userID)}
}

func (_c *MockUserUsecase_UpsertUser_Call) Run(run func(ctx context.Context, userID uuid.UUID)) *MockUserUsecase_UpsertUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *MockUserUsecase_UpsertUser_Call) Return(_a0 error) *MockUserUsecase_UpsertUser_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockUserUsecase_UpsertUser_Call) RunAndReturn(run func(context.Context, uuid.UUID) error) *MockUserUsecase_UpsertUser_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockUserUsecase creates a new instance of MockUserUsecase. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockUserUsecase(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockUserUsecase {
	mock := &MockUserUsecase{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
