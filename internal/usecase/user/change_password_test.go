package user_test

import (
	"context"
	"errors"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"api-server/internal/dto"
	gitlab_mocks "api-server/internal/gateways/gitlab/mocks"
	"api-server/internal/gateways/supabase"
	supabase_mocks "api-server/internal/gateways/supabase/mocks"
	repository_mocks "api-server/internal/repositories/mocks"
	"api-server/internal/usecase"
	"api-server/internal/usecase/user"
)

func TestChangePassword(t *testing.T) {
	mockUserID := uuid.New()
	mockAccessToken := "mock-access-token"
	testcases := []struct {
		name      string
		input     dto.ChangePasswordInput
		mockFn    func(r *repository_mocks.MockRepository, s *supabase_mocks.MockSupabaseClient)
		expectErr error
	}{
		{
			name: "should return ErrCurrentPasswordIsIncorrect if current password is wrong",
			input: dto.ChangePasswordInput{
				CurrentPassword: "wrong-password",
				NewPassword:     "new-password",
			},
			mockFn: func(r *repository_mocks.MockRepository, s *supabase_mocks.MockSupabaseClient) {
				r.On("CheckCurrentPassword", mock.Anything, mockUserID, "wrong-password").Return(errors.New("incorrect password"))
			},
			expectErr: usecase.ErrCurrentPasswordIsIncorrect,
		},
		{
			name: "should return ErrFailedToChangePassword if CheckCurrentPassword fails with non-password error",
			input: dto.ChangePasswordInput{
				CurrentPassword: "current-password",
				NewPassword:     "new-password",
			},
			mockFn: func(r *repository_mocks.MockRepository, s *supabase_mocks.MockSupabaseClient) {
				r.On("CheckCurrentPassword", mock.Anything, mockUserID, "current-password").Return(errors.New("some other error"))
			},
			expectErr: usecase.ErrFailedToChangePassword,
		},
		{
			name: "should return ErrFailedToChangePassword if supabase ChangePassword fails",
			input: dto.ChangePasswordInput{
				CurrentPassword: "current-password",
				NewPassword:     "new-password",
			},
			mockFn: func(r *repository_mocks.MockRepository, s *supabase_mocks.MockSupabaseClient) {
				r.On("CheckCurrentPassword", mock.Anything, mockUserID, "current-password").Return(nil)
				s.On("ChangePassword", mock.Anything, mockAccessToken, supabase.ChangePasswordRequest{
					NewPassword: "new-password",
				}).Return(errors.New("supabase error"))
			},
			expectErr: usecase.ErrFailedToChangePassword,
		},
		{
			name: "should return nil if password change succeeds",
			input: dto.ChangePasswordInput{
				CurrentPassword: "current-password",
				NewPassword:     "new-password",
			},
			mockFn: func(r *repository_mocks.MockRepository, s *supabase_mocks.MockSupabaseClient) {
				r.On("CheckCurrentPassword", mock.Anything, mockUserID, "current-password").Return(nil)
				s.On("ChangePassword", mock.Anything, mockAccessToken, supabase.ChangePasswordRequest{
					NewPassword: "new-password",
				}).Return(nil)
			},
			expectErr: nil,
		},
	}

	for _, testcase := range testcases {
		t.Run(testcase.name, func(t *testing.T) {
			r := &repository_mocks.MockRepository{}
			s := &supabase_mocks.MockSupabaseClient{}
			g := &gitlab_mocks.MockGitlabClient{}
			testcase.mockFn(r, s)

			userUsecase := user.New(r, g, s, nil, nil, nil)
			err := userUsecase.ChangePassword(context.Background(), mockUserID, mockAccessToken, testcase.input)

			// assertion
			assert.Equal(t, testcase.expectErr, err)
		})
	}
}
