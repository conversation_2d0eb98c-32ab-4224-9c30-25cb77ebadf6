package user

import (
	"context"
	"errors"

	"api-server/internal/dto"
	"api-server/internal/entities"
	repository "api-server/internal/repositories"
	"api-server/internal/types"
	"api-server/internal/usecase"
	"api-server/pkg/oteltrace"

	"go.opentelemetry.io/otel/codes"
	"gorm.io/gorm"
)

// ListUsers implements the UserUsecase interface for retrieving a list of users.
// It fetches users with pagination, sorting, and filtering capabilities, including
// organization and repository membership filters.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: ListUsersInput containing pagination parameters and filters
//
// Returns:
//   - dto.ListUsersOutput: Output containing the list of users and pagination info
//   - error: Any error that occurred during retrieval
func (i *impl) ListUsers(ctx context.Context, input dto.ListUsersInput) (dto.ListUsersOutput, error) {
	c, span := oteltrace.Tracer.Start(ctx, "usecase.user.ListUsers")
	defer span.End()

	filter := repository.ListUsersInput{
		Paginate: repository.PaginateRequest{
			Page:    input.Paginate.Page,
			PerPage: input.Paginate.PerPage,
			OrderBy: input.Paginate.OrderBy,
			Sort:    input.Paginate.Sort,
		},
		Keyword: input.Keyword,
		Except: repository.ExceptFilter{
			NotInOrgId: input.Except.NotInOrgId,
		},
	}

	if input.Except.NotInRepo != nil {
		repoId, err := types.RepoID{}.FromString(*input.Except.NotInRepo)
		if err != nil {
			span.SetStatus(codes.Error, "failed to get repository ID")
			span.RecordError(err)
			return dto.ListUsersOutput{}, err
		}

		repo, err := i.repository.FindRepository(ctx, repository.RepositoryFilter{
			Type:      repoId.RepoType(),
			Namespace: repoId.Namespace(),
			Name:      repoId.RepoName(),
		})
		if err != nil {
			span.SetStatus(codes.Error, "failed to find repository")
			span.RecordError(err)
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return dto.ListUsersOutput{}, usecase.ErrRepositoryNotFound
			}
			return dto.ListUsersOutput{}, err
		}

		filter.Except.NotInRepId = &repo.ID
	}

	count, err := i.repository.CountUsers(c, filter)
	if err != nil {
		span.SetStatus(codes.Error, "failed to count users")
		span.RecordError(err)
		return dto.ListUsersOutput{}, err
	}

	users, err := i.repository.ListUsers(c, filter)
	if err != nil {
		span.SetStatus(codes.Error, "failed to list users")
		span.RecordError(err)
		return dto.ListUsersOutput{}, err
	}

	data := dto.FromManyEntities[entities.User, dto.User](users)

	// Pre-sign URL
	for j := 0; j < len(data); j++ {
		if data[j].Avatar != nil {
			repoImage, err := i.aws.GenPreSignUrl(c, *data[j].Avatar)
			if err != nil {
				span.SetStatus(codes.Error, "failed to generate presigned url for avatar")
				span.RecordError(err)
				return dto.ListUsersOutput{}, err
			}

			data[j].Avatar = &repoImage
		}
	}

	span.AddEvent("users listed successfully")
	span.SetStatus(codes.Ok, "users listed successfully")
	return dto.ListUsersOutput{
		Data: &data,
		Pagination: &dto.Pagination{
			Total:    int(count),
			PageNo:   input.Paginate.Page,
			PageSize: input.Paginate.PerPage,
		},
	}, nil
}
