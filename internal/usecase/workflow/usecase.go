package workflow

import (
	"context"
	"strconv"

	argoclient "github.com/argoproj/argo-workflows/v3/pkg/apiclient"
	workflowpkg "github.com/argoproj/argo-workflows/v3/pkg/apiclient/workflow"
	"github.com/argoproj/argo-workflows/v3/pkg/apis/workflow/v1alpha1"
	"go.opentelemetry.io/otel/codes"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"api-server/configs"
	repository "api-server/internal/repositories"
	"api-server/internal/types"
	"api-server/internal/usecase"
	"api-server/pkg/argo"
	"api-server/pkg/oteltrace"
)

const (
	CPU_DEPLOYMENT string = "./pkg/argo/custom-space-deployment-workflow.yaml"
	GPU_DEPLOYMENT string = "./pkg/argo/custom-gpu-space-deployment-workflow.yaml"
)

// WorkflowUsecase defines the interface for managing Argo workflows.
// It provides methods for creating, monitoring, and managing workflow deployments.
type WorkflowUsecase interface {
	// CreateSpaceDeploymentWorkflow creates a new workflow for space deployment.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - req: The request data for creating the workflow
	//
	// Returns:
	//   - *string: The name of the created workflow
	//   - error: Any error that occurred during creation
	CreateSpaceDeploymentWorkflow(ctx context.Context, req CreateSpaceDeploymentWorkflow) (*string, error)

	// GetWorkflow retrieves information about a specific workflow.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - workflowName: The name of the workflow
	//   - namespace: The namespace where the workflow is deployed
	//
	// Returns:
	//   - *v1alpha1.Workflow: The workflow information
	//   - error: Any error that occurred during retrieval
	GetWorkflow(ctx context.Context, workflowName, namespace string) (*v1alpha1.Workflow, error)
	// ListWorkflow(ctx context.Context, req dto.ListWorkflowRequest) (*dto.ListWorkflowResponse, error)
	// GetWorkflowLogs retrieves the logs for a specific workflow.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - workflowName: The name of the workflow
	//   - namespace: The namespace where the workflow is deployed
	//
	// Returns:
	//   - workflowpkg.WorkflowService_WorkflowLogsClient: The workflow logs client
	//   - error: Any error that occurred during retrieval
	GetWorkflowLogs(ctx context.Context, workflowName, namespace string) (workflowpkg.WorkflowService_WorkflowLogsClient, error)

	// TerminateWorkflow terminates a running workflow.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - workflowName: The name of the workflow to terminate
	//   - namespace: The namespace where the workflow is deployed
	//
	// Returns:
	//   - error: Any error that occurred during termination
	TerminateWorkflow(ctx context.Context, workflowName, namespace string) error
}

type impl struct {
	repo       repository.DeploymentRepository
	argoClient argoclient.Client
	config     *configs.ArgoWorkflowConfig
}

var _ WorkflowUsecase = (*impl)(nil)

// New creates a new instance of the workflow usecase implementation.
// It initializes the usecase with the Argo Workflow configuration,
// deployment repository, and Argo client.
//
// Parameters:
//   - config: Configuration for Argo Workflows.
//   - repo: Repository interface for deployment data access.
//   - argoClient: Client for interacting with the Argo Workflows API.
//
// Returns:
//   - *impl: New instance of the workflow usecase.
func New(config *configs.ArgoWorkflowConfig, repo repository.DeploymentRepository, argoClient argoclient.Client) *impl {
	return &impl{
		repo,
		argoClient,
		config,
	}
}

// createWorkflowsFromFile is a helper function to submit workflows from a YAML file.
// It uses a provided function to perform the actual creation, allowing for easier testing and DI.
//
// Parameters:
//   - ctx: Context for the operation.
//   - client: The Argo Workflow service client.
//   - namespace: The Kubernetes namespace where the workflow will be created.
//   - path: The file path to the workflow YAML definition.
//   - opts: Submission options for the workflow, including parameters.
//   - fn: The actual function that creates the workflow from the file.
//
// Returns:
//   - []string: A list of names of the created workflows.
//   - error: Any error that occurred during workflow creation.
func createWorkflowsFromFile(
	ctx context.Context,
	client workflowpkg.WorkflowServiceClient,
	namespace, path string,
	opts *v1alpha1.SubmitOpts,
	fn argo.CreateWorkflowFromFileFn,
) ([]string, error) {
	return fn(ctx, client, namespace, path, opts)
}

type Resource struct {
	CPU int
	Mem types.K8SHardwareMem
}

type CreateSpaceDeploymentWorkflow struct {
	SourceRepoUrl       string
	SourceRepoCommit    string
	BuildContextRepoUrl string
	ResourceName        string
	SpaceName           string
	SpaceDomain         string
	ImageTag            string
	Timestamp           int64
	ResourceRequest     Resource
	ResourceLimit       Resource
	IngressClassName    string
	NodeName            string

	GpuDeployment bool
}

func (i *impl) CreateSpaceDeploymentWorkflow(ctx context.Context, req CreateSpaceDeploymentWorkflow) (*string, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.workflow.CreateSpaceDeploymentWorkflow")
	defer span.End()

	workflowServiceClient := i.argoClient.NewWorkflowServiceClient()
	var workflowFilePath string
	if req.GpuDeployment {
		workflowFilePath = GPU_DEPLOYMENT
	} else {
		workflowFilePath = CPU_DEPLOYMENT
	}
	workflowParams := argo.WorkflowParams{
		"source-repo":          req.SourceRepoUrl,
		"source-repo-commit":   req.SourceRepoCommit,
		"build-context-repo":   req.BuildContextRepoUrl,
		"space-domain":         req.SpaceDomain,
		"resource-name":        req.ResourceName,
		"configmap-name":       req.ResourceName,
		"space-name":           req.SpaceName,
		"image-tag":            req.ImageTag,
		"image-repository-url": i.config.SpaceImageRegistry,
		"volvo-apiserver-url":  i.config.ApiServerURL,
		"cpu-request":          strconv.FormatInt(int64(req.ResourceRequest.CPU), 10),
		"mem-request":          string(req.ResourceRequest.Mem),
		"cpu-limit":            strconv.FormatInt(int64(req.ResourceLimit.CPU), 10),
		"mem-limit":            string(req.ResourceLimit.Mem),
		"timestamp":            strconv.FormatInt(req.Timestamp, 10),
		"ingress-class-name":   req.IngressClassName,
		"node-name":            req.NodeName,
	}
	workflowNames, err := createWorkflowsFromFile(
		ctx,
		workflowServiceClient,
		"argo", // namespace
		workflowFilePath,
		&v1alpha1.SubmitOpts{
			Parameters: workflowParams.ToKeyValue(),
		},
		argo.CreateWorkflowFromFileFunc,
	)
	if err != nil {
		span.SetStatus(codes.Error, err.Error())
		span.RecordError(err)
		return nil, usecase.ErrCreateWorkflow
	}

	return &workflowNames[0], nil
}

func (i *impl) GetWorkflowLogs(ctx context.Context, workflowName, namespace string) (workflowpkg.WorkflowService_WorkflowLogsClient, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.workflow.GetWorkflowLogs")
	defer span.End()

	workflowServiceClient := i.argoClient.NewWorkflowServiceClient()
	resp, err := workflowServiceClient.WorkflowLogs(ctx, &workflowpkg.WorkflowLogRequest{
		Name:      workflowName,
		Namespace: namespace,
		LogOptions: &v1.PodLogOptions{
			Follow:                       true,
			Container:                    "main",
			InsecureSkipTLSVerifyBackend: true,
		},
		Grep: "",
	})
	if err != nil {
		span.SetStatus(codes.Error, err.Error())
		span.RecordError(err)
		return nil, err
	}

	return resp, nil
}

// func (i *impl) ListWorkflow(ctx context.Context, req dto.ListWorkflowRequest) (*dto.ListWorkflowResponse, error) {
// 	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.workflow.ListWorkflow")
// 	defer span.End()
//
// 	pred := repository.ListWorkflowQuery{
// 		RepoID: req.RepoID,
// 	}
//
// 	total, err := i.repo.CountWorkflow(ctx, pred)
// 	if err != nil {
// 		span.SetStatus(codes.Error, err.Error())
// 		span.RecordError(err)
// 		return nil, usecase.ErrInternal
// 	}
//
// 	pagination := types.Pagination{
// 		PageNo:   req.Paginate.Page,
// 		PageSize: req.Paginate.PerPage,
// 	}
// 	orderBy := types.OrderBy{req.Paginate.OrderBy: req.Paginate.Sort}
// 	resp, err := i.repo.ListWorkflow(ctx, pagination, orderBy, pred)
// 	if err != nil {
// 		span.SetStatus(codes.Error, err.Error())
// 		span.RecordError(err)
// 		return nil, usecase.ErrInternal
// 	}
//
// 	data := dto.FromManyEntities[entities.Deployment, dto.Workflow](resp)
// 	result := dto.ListWorkflowResponse{
// 		Data: &data,
// 		Pagination: &dto.Pagination{
// 			Total:    int(total),
// 			PageNo:   pagination.PageNo,
// 			PageSize: pagination.PageSize,
// 		},
// 	}
//
// 	return &result, nil
// }

func (i *impl) TerminateWorkflow(ctx context.Context, workflowName, namespace string) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.workflow.TerminateWorkflow")
	defer span.End()

	workflowServiceClient := i.argoClient.NewWorkflowServiceClient()
	_, err := workflowServiceClient.TerminateWorkflow(ctx, &workflowpkg.WorkflowTerminateRequest{
		Name:      workflowName,
		Namespace: namespace,
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to terminate Argo workflow")
		span.RecordError(err)
		return err
	}

	return nil
}

func (i *impl) GetWorkflow(ctx context.Context, workflowName, namespace string) (*v1alpha1.Workflow, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.workflow.GetWorkflow")
	defer span.End()

	workflowServiceClient := i.argoClient.NewWorkflowServiceClient()
	resp, err := workflowServiceClient.GetWorkflow(ctx, &workflowpkg.WorkflowGetRequest{
		Name:       workflowName,
		Namespace:  namespace,
		GetOptions: &metav1.GetOptions{},
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to get Argo workflow")
		span.RecordError(err)
		return nil, err
	}

	return resp, err
}
