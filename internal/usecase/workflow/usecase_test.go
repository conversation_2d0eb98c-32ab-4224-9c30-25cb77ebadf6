package workflow_test

// import (
// 	"context"
// 	"errors"
// 	"testing"
//
// 	workflowpkg "github.com/argoproj/argo-workflows/v3/pkg/apiclient/workflow"
// 	wfv1 "github.com/argoproj/argo-workflows/v3/pkg/apis/workflow/v1alpha1"
// 	"github.com/google/uuid"
// 	"github.com/stretchr/testify/mock"
// 	"github.com/stretchr/testify/require"
//
// 	"api-server/internal/dto"
// 	"api-server/internal/entities"
// 	"api-server/internal/enums"
// 	repository_mocks "api-server/internal/repositories/mocks"
// 	"api-server/internal/usecase"
// 	"api-server/pkg/argo"
// 	argo_mocks "api-server/pkg/argo/mocks"
// 	"api-server/pkg/oteltrace"
// )
//
// func TestUpdateWorkflow(t *testing.T) {
// 	oteltrace.New()
//
// 	testcases := []struct {
// 		name      string
// 		mockFn    func(repo *repository_mocks.MockWorkflowRepository, argo *argo_mocks.MockClient)
// 		status    enums.ArgoWorkflowStatus
// 		expectErr error
// 	}{
// 		{
// 			name: "should pass when status = Running",
// 			mockFn: func(repo *repository_mocks.MockWorkflowRepository, argo *argo_mocks.MockClient) {
// 				repo.On("UpdateWorkflow", mock.Anything, mock.Anything).Return(&entities.Deployment{}, nil)
// 			},
// 			status:    enums.ArgoWorkflowStatus_Running,
// 			expectErr: nil,
// 		},
// 		{
// 			name: "should pass when status = Succeeded",
// 			mockFn: func(repo *repository_mocks.MockWorkflowRepository, argo *argo_mocks.MockClient) {
// 				repo.On("UpdateWorkflow", mock.Anything, mock.Anything).Return(&entities.Deployment{}, nil)
// 			},
// 			status:    enums.ArgoWorkflowStatus_Succeeded,
// 			expectErr: nil,
// 		},
// 		{
// 			name: "should pass when status = Failed",
// 			mockFn: func(repo *repository_mocks.MockWorkflowRepository, argo *argo_mocks.MockClient) {
// 				repo.On("UpdateWorkflow", mock.Anything, mock.Anything).Return(&entities.Deployment{}, nil)
// 			},
// 			status:    enums.ArgoWorkflowStatus_Failed,
// 			expectErr: nil,
// 		},
// 		{
// 			name: "should pass when status = Error",
// 			mockFn: func(repo *repository_mocks.MockWorkflowRepository, argo *argo_mocks.MockClient) {
// 				repo.On("UpdateWorkflow", mock.Anything, mock.Anything).Return(&entities.Deployment{}, nil)
// 			},
// 			status:    enums.ArgoWorkflowStatus_Error,
// 			expectErr: nil,
// 		},
// 		{
// 			name: "should return error if UpdateWorkflow return error",
// 			mockFn: func(repo *repository_mocks.MockWorkflowRepository, argo *argo_mocks.MockClient) {
// 				repo.On("UpdateWorkflow", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
// 			},
// 			status:    enums.ArgoWorkflowStatus_Running,
// 			expectErr: usecase.ErrInternal,
// 		},
// 		{
// 			name: "should pass if status = Pending",
// 			mockFn: func(repo *repository_mocks.MockWorkflowRepository, argo *argo_mocks.MockClient) {
// 			},
// 			status:    enums.ArgoWorkflowStatus_Pending,
// 			expectErr: nil,
// 		},
// 	}
//
// 	for _, testcase := range testcases {
// 		t.Run(testcase.name, func(t *testing.T) {
// 			repo := &repository_mocks.MockWorkflowRepository{}
// 			argo := &argo_mocks.MockClient{}
// 			testcase.mockFn(repo, argo)
//
// 			usecase := New(repo, argo)
// 			err := usecase.UpdateWorkflow(context.Background(), dto.UpdateWorkflowRequest{
// 				Name:     "name",
// 				Status:   testcase.status,
// 				Duration: 0.0,
// 			})
//
// 			// assertion
// 			require.Equal(t, testcase.expectErr, err)
// 		})
// 	}
// }
//
// func TestListWorkflow(t *testing.T) {
// 	oteltrace.New()
//
// 	mockRepoID, _ := uuid.Parse("efc031c8-584a-4e1a-8aec-58839e2f0cb8")
// 	testcases := []struct {
// 		name      string
// 		mockFn    func(repo *repository_mocks.MockWorkflowRepository, argo *argo_mocks.MockClient)
// 		expectErr error
// 	}{
// 		{
// 			name: "should return usecase.ErrInternal if CountWorkflow return error",
// 			mockFn: func(repo *repository_mocks.MockWorkflowRepository, argo *argo_mocks.MockClient) {
// 				repo.On("CountWorkflow", mock.Anything, mock.Anything).Return(int64(0), errors.New("error"))
// 			},
// 			expectErr: usecase.ErrInternal,
// 		},
// 		{
// 			name: "should return usecase.ErrInternal if ListWorkflow return error",
// 			mockFn: func(repo *repository_mocks.MockWorkflowRepository, argo *argo_mocks.MockClient) {
// 				repo.On("CountWorkflow", mock.Anything, mock.Anything).Return(int64(0), nil)
// 				repo.On("ListWorkflow", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil, errors.New("error"))
// 			},
// 			expectErr: usecase.ErrInternal,
// 		},
// 		{
// 			name: "should pass",
// 			mockFn: func(repo *repository_mocks.MockWorkflowRepository, argo *argo_mocks.MockClient) {
// 				repo.On("CountWorkflow", mock.Anything, mock.Anything).Return(int64(0), nil)
// 				repo.On("ListWorkflow", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]entities.Deployment{}, nil)
// 			},
// 			expectErr: nil,
// 		},
// 	}
//
// 	for _, testcase := range testcases {
// 		t.Run(testcase.name, func(t *testing.T) {
// 			repo := &repository_mocks.MockWorkflowRepository{}
// 			argo := &argo_mocks.MockClient{}
// 			testcase.mockFn(repo, argo)
//
// 			usecase := New(repo, argo)
// 			_, err := usecase.ListWorkflow(context.Background(), dto.ListWorkflowRequest{
// 				RepoID:   mockRepoID,
// 				Paginate: dto.PaginateRequest{},
// 			})
//
// 			// assertion
// 			require.Equal(t, testcase.expectErr, err)
// 		})
// 	}
// }
//
// func TestCreateWorkflow(t *testing.T) {
// 	oteltrace.New()
//
// 	mockRepoID, _ := uuid.Parse("efc031c8-584a-4e1a-8aec-58839e2f0cb8")
// 	testcases := []struct {
// 		name      string
// 		mockFn    func(repo *repository_mocks.MockWorkflowRepository, argoClient *argo_mocks.MockClient)
// 		expectErr error
// 	}{
// 		{
// 			name: "should return usecase.ErrCreateWorkflow if CreateWorkflowFromFileFunc return error",
// 			mockFn: func(repo *repository_mocks.MockWorkflowRepository, argoClient *argo_mocks.MockClient) {
// 				argoClient.On("NewWorkflowServiceClient").Return(&argo_mocks.MockWorkflowServiceClient{})
// 				argo.CreateWorkflowFromFileFunc = func(context.Context, workflowpkg.WorkflowServiceClient, string, string, *wfv1.SubmitOpts) ([]string, error) {
// 					return nil, errors.New("error")
// 				}
// 			},
// 			expectErr: usecase.ErrCreateWorkflow,
// 		},
// 		{
// 			name: "should return usecase.ErrInternal if CreateWorkflow return error",
// 			mockFn: func(repo *repository_mocks.MockWorkflowRepository, argoClient *argo_mocks.MockClient) {
// 				argoClient.On("NewWorkflowServiceClient").Return(&argo_mocks.MockWorkflowServiceClient{})
// 				argo.CreateWorkflowFromFileFunc = func(context.Context, workflowpkg.WorkflowServiceClient, string, string, *wfv1.SubmitOpts) ([]string, error) {
// 					return []string{"workflow"}, nil
// 				}
// 				repo.On("CreateWorkflow", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
// 			},
// 			expectErr: usecase.ErrInternal,
// 		},
// 		{
// 			name: "should pass",
// 			mockFn: func(repo *repository_mocks.MockWorkflowRepository, argoClient *argo_mocks.MockClient) {
// 				argoClient.On("NewWorkflowServiceClient").Return(&argo_mocks.MockWorkflowServiceClient{})
// 				argo.CreateWorkflowFromFileFunc = func(context.Context, workflowpkg.WorkflowServiceClient, string, string, *wfv1.SubmitOpts) ([]string, error) {
// 					return []string{"workflow"}, nil
// 				}
// 				repo.On("CreateWorkflow", mock.Anything, mock.Anything).Return(&entities.Deployment{}, nil)
// 			},
// 			expectErr: nil,
// 		},
// 	}
//
// 	for _, testcase := range testcases {
// 		t.Run(testcase.name, func(t *testing.T) {
// 			repo := &repository_mocks.MockWorkflowRepository{}
// 			argo := &argo_mocks.MockClient{}
// 			testcase.mockFn(repo, argo)
//
// 			usecase := New(repo, argo)
// 			_, err := usecase.CreateWorkflow(context.Background(), dto.CreateWorkflowRequest{
// 				RepoID:   mockRepoID,
// 				Revision: "main",
// 			})
//
// 			// assertion
// 			require.Equal(t, testcase.expectErr, err)
// 		})
// 	}
// }
