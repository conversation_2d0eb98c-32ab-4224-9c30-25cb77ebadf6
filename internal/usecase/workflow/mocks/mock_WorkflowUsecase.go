// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	context "context"

	apiclientworkflow "github.com/argoproj/argo-workflows/v3/pkg/apiclient/workflow"

	mock "github.com/stretchr/testify/mock"

	v1alpha1 "github.com/argoproj/argo-workflows/v3/pkg/apis/workflow/v1alpha1"

	workflow "api-server/internal/usecase/workflow"
)

// MockWorkflowUsecase is an autogenerated mock type for the WorkflowUsecase type
type MockWorkflowUsecase struct {
	mock.Mock
}

type MockWorkflowUsecase_Expecter struct {
	mock *mock.Mock
}

func (_m *MockWorkflowUsecase) EXPECT() *MockWorkflowUsecase_Expecter {
	return &MockWorkflowUsecase_Expecter{mock: &_m.Mock}
}

// CreateSpaceDeploymentWorkflow provides a mock function with given fields: ctx, req
func (_m *MockWorkflowUsecase) CreateSpaceDeploymentWorkflow(ctx context.Context, req workflow.CreateSpaceDeploymentWorkflow) (*string, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CreateSpaceDeploymentWorkflow")
	}

	var r0 *string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, workflow.CreateSpaceDeploymentWorkflow) (*string, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, workflow.CreateSpaceDeploymentWorkflow) *string); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*string)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, workflow.CreateSpaceDeploymentWorkflow) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockWorkflowUsecase_CreateSpaceDeploymentWorkflow_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateSpaceDeploymentWorkflow'
type MockWorkflowUsecase_CreateSpaceDeploymentWorkflow_Call struct {
	*mock.Call
}

// CreateSpaceDeploymentWorkflow is a helper method to define mock.On call
//   - ctx context.Context
//   - req workflow.CreateSpaceDeploymentWorkflow
func (_e *MockWorkflowUsecase_Expecter) CreateSpaceDeploymentWorkflow(ctx interface{}, req interface{}) *MockWorkflowUsecase_CreateSpaceDeploymentWorkflow_Call {
	return &MockWorkflowUsecase_CreateSpaceDeploymentWorkflow_Call{Call: _e.mock.On("CreateSpaceDeploymentWorkflow", ctx, req)}
}

func (_c *MockWorkflowUsecase_CreateSpaceDeploymentWorkflow_Call) Run(run func(ctx context.Context, req workflow.CreateSpaceDeploymentWorkflow)) *MockWorkflowUsecase_CreateSpaceDeploymentWorkflow_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(workflow.CreateSpaceDeploymentWorkflow))
	})
	return _c
}

func (_c *MockWorkflowUsecase_CreateSpaceDeploymentWorkflow_Call) Return(_a0 *string, _a1 error) *MockWorkflowUsecase_CreateSpaceDeploymentWorkflow_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockWorkflowUsecase_CreateSpaceDeploymentWorkflow_Call) RunAndReturn(run func(context.Context, workflow.CreateSpaceDeploymentWorkflow) (*string, error)) *MockWorkflowUsecase_CreateSpaceDeploymentWorkflow_Call {
	_c.Call.Return(run)
	return _c
}

// GetWorkflow provides a mock function with given fields: ctx, workflowName, namespace
func (_m *MockWorkflowUsecase) GetWorkflow(ctx context.Context, workflowName string, namespace string) (*v1alpha1.Workflow, error) {
	ret := _m.Called(ctx, workflowName, namespace)

	if len(ret) == 0 {
		panic("no return value specified for GetWorkflow")
	}

	var r0 *v1alpha1.Workflow
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) (*v1alpha1.Workflow, error)); ok {
		return rf(ctx, workflowName, namespace)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string) *v1alpha1.Workflow); ok {
		r0 = rf(ctx, workflowName, namespace)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*v1alpha1.Workflow)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = rf(ctx, workflowName, namespace)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockWorkflowUsecase_GetWorkflow_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetWorkflow'
type MockWorkflowUsecase_GetWorkflow_Call struct {
	*mock.Call
}

// GetWorkflow is a helper method to define mock.On call
//   - ctx context.Context
//   - workflowName string
//   - namespace string
func (_e *MockWorkflowUsecase_Expecter) GetWorkflow(ctx interface{}, workflowName interface{}, namespace interface{}) *MockWorkflowUsecase_GetWorkflow_Call {
	return &MockWorkflowUsecase_GetWorkflow_Call{Call: _e.mock.On("GetWorkflow", ctx, workflowName, namespace)}
}

func (_c *MockWorkflowUsecase_GetWorkflow_Call) Run(run func(ctx context.Context, workflowName string, namespace string)) *MockWorkflowUsecase_GetWorkflow_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string))
	})
	return _c
}

func (_c *MockWorkflowUsecase_GetWorkflow_Call) Return(_a0 *v1alpha1.Workflow, _a1 error) *MockWorkflowUsecase_GetWorkflow_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockWorkflowUsecase_GetWorkflow_Call) RunAndReturn(run func(context.Context, string, string) (*v1alpha1.Workflow, error)) *MockWorkflowUsecase_GetWorkflow_Call {
	_c.Call.Return(run)
	return _c
}

// GetWorkflowLogs provides a mock function with given fields: ctx, workflowName, namespace
func (_m *MockWorkflowUsecase) GetWorkflowLogs(ctx context.Context, workflowName string, namespace string) (apiclientworkflow.WorkflowService_WorkflowLogsClient, error) {
	ret := _m.Called(ctx, workflowName, namespace)

	if len(ret) == 0 {
		panic("no return value specified for GetWorkflowLogs")
	}

	var r0 apiclientworkflow.WorkflowService_WorkflowLogsClient
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) (apiclientworkflow.WorkflowService_WorkflowLogsClient, error)); ok {
		return rf(ctx, workflowName, namespace)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string) apiclientworkflow.WorkflowService_WorkflowLogsClient); ok {
		r0 = rf(ctx, workflowName, namespace)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(apiclientworkflow.WorkflowService_WorkflowLogsClient)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = rf(ctx, workflowName, namespace)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockWorkflowUsecase_GetWorkflowLogs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetWorkflowLogs'
type MockWorkflowUsecase_GetWorkflowLogs_Call struct {
	*mock.Call
}

// GetWorkflowLogs is a helper method to define mock.On call
//   - ctx context.Context
//   - workflowName string
//   - namespace string
func (_e *MockWorkflowUsecase_Expecter) GetWorkflowLogs(ctx interface{}, workflowName interface{}, namespace interface{}) *MockWorkflowUsecase_GetWorkflowLogs_Call {
	return &MockWorkflowUsecase_GetWorkflowLogs_Call{Call: _e.mock.On("GetWorkflowLogs", ctx, workflowName, namespace)}
}

func (_c *MockWorkflowUsecase_GetWorkflowLogs_Call) Run(run func(ctx context.Context, workflowName string, namespace string)) *MockWorkflowUsecase_GetWorkflowLogs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string))
	})
	return _c
}

func (_c *MockWorkflowUsecase_GetWorkflowLogs_Call) Return(_a0 apiclientworkflow.WorkflowService_WorkflowLogsClient, _a1 error) *MockWorkflowUsecase_GetWorkflowLogs_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockWorkflowUsecase_GetWorkflowLogs_Call) RunAndReturn(run func(context.Context, string, string) (apiclientworkflow.WorkflowService_WorkflowLogsClient, error)) *MockWorkflowUsecase_GetWorkflowLogs_Call {
	_c.Call.Return(run)
	return _c
}

// TerminateWorkflow provides a mock function with given fields: ctx, workflowName, namespace
func (_m *MockWorkflowUsecase) TerminateWorkflow(ctx context.Context, workflowName string, namespace string) error {
	ret := _m.Called(ctx, workflowName, namespace)

	if len(ret) == 0 {
		panic("no return value specified for TerminateWorkflow")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) error); ok {
		r0 = rf(ctx, workflowName, namespace)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockWorkflowUsecase_TerminateWorkflow_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'TerminateWorkflow'
type MockWorkflowUsecase_TerminateWorkflow_Call struct {
	*mock.Call
}

// TerminateWorkflow is a helper method to define mock.On call
//   - ctx context.Context
//   - workflowName string
//   - namespace string
func (_e *MockWorkflowUsecase_Expecter) TerminateWorkflow(ctx interface{}, workflowName interface{}, namespace interface{}) *MockWorkflowUsecase_TerminateWorkflow_Call {
	return &MockWorkflowUsecase_TerminateWorkflow_Call{Call: _e.mock.On("TerminateWorkflow", ctx, workflowName, namespace)}
}

func (_c *MockWorkflowUsecase_TerminateWorkflow_Call) Run(run func(ctx context.Context, workflowName string, namespace string)) *MockWorkflowUsecase_TerminateWorkflow_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string))
	})
	return _c
}

func (_c *MockWorkflowUsecase_TerminateWorkflow_Call) Return(_a0 error) *MockWorkflowUsecase_TerminateWorkflow_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockWorkflowUsecase_TerminateWorkflow_Call) RunAndReturn(run func(context.Context, string, string) error) *MockWorkflowUsecase_TerminateWorkflow_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockWorkflowUsecase creates a new instance of MockWorkflowUsecase. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockWorkflowUsecase(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockWorkflowUsecase {
	mock := &MockWorkflowUsecase{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
