package signup_request

import (
	"context"
	"errors"
	"time"

	"go.opentelemetry.io/otel/codes"
	"gorm.io/gorm"

	"api-server/internal/enums"
	"api-server/internal/gateways/gitlab"
	repository "api-server/internal/repositories"
	"api-server/internal/usecase"
	"api-server/pkg/oteltrace"
)

// RefreshAccessToken implements the SignupRequestUsecase interface for refreshing a user's GitLab access token.
// It creates a new personal access token, deletes the old one, and updates the user record.
//
// Parameters:
//   - ctx: Context for the operation
//   - userRefGitID: GitLab user ID to refresh token for
//
// Returns:
//   - error: Any error that occurred during token refresh
func (i *impl) RefreshAccessToken(ctx context.Context, userRefGitID int64) error {
	c, span := oteltrace.Tracer.Start(ctx, "usecase.signup_request.RefreshAccessToken")
	defer span.End()

	currUser, err := i.repository.FindUser(c, repository.FindUserFilter{
		RefGitUserID: &userRefGitID,
	})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			span.SetStatus(codes.Error, "user not found")
			span.RecordError(err)
			return usecase.ErrUserNotFound
		}
		span.SetStatus(codes.Error, "failed to find user")
		span.RecordError(err)
		return err
	}

	var gitlabAccessToken string
	if currUser.GitlabAccessToken != nil {
		gitlabAccessToken = *currUser.GitlabAccessToken
	}

	gitlabToken, err := i.gitLabClient.CreatePersonalAccessToken(c, gitlab.CreatePersonalAccessTokenRequest{
		Name:   "Admin_token",
		UserId: enums.AdminRefGitID,
		Scopes: []gitlab.TokenScope{
			gitlab.ScopeAPI,
			gitlab.ScopeReadUser,
			gitlab.ScopeReadAPI,
			gitlab.ScopeReadRepository,
			gitlab.ScopeWriteRepository,
			gitlab.ScopeSudo,
			gitlab.ScopeAdminMode,
			gitlab.ScopeAIFeatures,
			gitlab.ScopeK8sProxy,
			gitlab.ScopeReadServicePing,
		},
		AdminToken: gitlabAccessToken,
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to create personal access token")
		span.RecordError(err)
		return err
	}
	span.AddEvent("personal access token created successfully")
	span.SetStatus(codes.Ok, "personal access token created successfully")

	err = i.gitLabClient.DeletePersonalAccessToken(c, gitlabAccessToken)
	if err != nil {
		span.SetStatus(codes.Error, "failed to delete old personal access token")
		span.RecordError(err)
		return err
	}
	span.AddEvent("old personal access token deleted successfully")
	span.SetStatus(codes.Ok, "old personal access token deleted successfully")

	currUser.GitlabAccessToken = &gitlabToken.Token
	currUser.UpdatedAt = time.Now()
	err = i.repository.Save(ctx, currUser)
	if err != nil {
		span.SetStatus(codes.Error, "failed to save user with new access token")
		span.RecordError(err)
		return err
	}

	span.AddEvent("access token refreshed successfully")
	span.SetStatus(codes.Ok, "access token refreshed successfully")
	return nil
}
