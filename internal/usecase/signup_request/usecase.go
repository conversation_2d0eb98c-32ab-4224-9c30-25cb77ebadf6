package signup_request

import (
	"context"

	"api-server/configs"
	"api-server/internal/dto"
	"api-server/internal/gateways/gitlab"
	"api-server/internal/gateways/mail"
	supabase "api-server/internal/gateways/supabase"
	repository "api-server/internal/repositories"
)

// SignupRequestUsecase defines the interface for managing signup requests and user invitations.
// It provides methods for handling user signup, request processing, and email confirmations.
type SignupRequestUsecase interface {
	// Signup handles the initial signup request from a user.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - input: The signup input data containing user information
	//
	// Returns:
	//   - error: Any error that occurred during signup
	Signup(ctx context.Context, input dto.SignupInput) error

	// ProcessSignUpRequest processes an approval request for a signup.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - input: The approval request data
	//
	// Returns:
	//   - error: Any error that occurred during processing
	ProcessSignUpRequest(ctx context.Context, input dto.ApprovalRequest) error

	// InviteUser handles the invitation of a new user.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - input: The invitation input data
	//
	// Returns:
	//   - error: Any error that occurred during invitation
	InviteUser(ctx context.Context, input dto.InviteUserInput) error

	// GetAllSignUpRequests retrieves all pending signup requests.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//
	// Returns:
	//   - dto.GetAllSignUpRequestsOutput: The list of signup requests
	//   - error: Any error that occurred during retrieval
	GetAllSignUpRequests(ctx context.Context) (dto.GetAllSignUpRequestsOutput, error)

	// RefreshAccessToken refreshes the access token for a user.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - userRefGitID: The Git reference ID of the user
	//
	// Returns:
	//   - error: Any error that occurred during token refresh
	RefreshAccessToken(ctx context.Context, userRefGitID int64) error

	// SendConfirmEmail sends a confirmation email to a user.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - input: The input data for sending the confirmation email
	//
	// Returns:
	//   - error: Any error that occurred during email sending
	SendConfirmEmail(ctx context.Context, input dto.SendConfirmEmailInput) error
}

var _ SignupRequestUsecase = (*impl)(nil)

type impl struct {
	config           *configs.GlobalConfig
	repository       repository.Repository
	gitLabClient     gitlab.GitlabClient
	supabaseClient   supabase.SupabaseClient
	pwdEncryptSecret string
	mailClient       mail.MailClient
}

// New creates a new instance of the signup request usecase implementation.
// It initializes the usecase with the required dependencies including configuration,
// repository, GitLab client, Supabase client, password encryption secret, and mail client.
//
// Parameters:
//   - config: Global application configuration.
//   - repo: Repository interface for data access.
//   - gitlab: Client for GitLab API interactions.
//   - supabase: Client for Supabase interactions (e.g., authentication).
//   - pwdEncryptSecret: Secret key used for password encryption.
//   - mailClient: Client for sending emails.
//
// Returns:
//   - *impl: New instance of the signup request usecase.
func New(config *configs.GlobalConfig, repo repository.Repository, gitlab gitlab.GitlabClient, supabase supabase.SupabaseClient, pwdEncryptSecret string, mailClient mail.MailClient) *impl {
	return &impl{
		config:           config,
		repository:       repo,
		gitLabClient:     gitlab,
		supabaseClient:   supabase,
		pwdEncryptSecret: pwdEncryptSecret,
		mailClient:       mailClient,
	}
}
