package signup_request

import (
	"context"
	"errors"

	"gorm.io/gorm"

	"api-server/internal/dto"
	"api-server/internal/entities"
	"api-server/internal/usecase"
	"api-server/internal/utils"
	"api-server/pkg/oteltrace"

	"go.opentelemetry.io/otel/codes"
)

// Signup implements the SignupRequestUsecase interface for handling user signup requests.
// It processes new user signup requests, encrypts passwords, and stores the request for admin approval.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: Input data containing user signup information (email, password, name, username)
//
// Returns:
//   - error: Any error that occurred during signup processing
func (i *impl) Signup(ctx context.Context, input dto.SignupInput) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.signup_request.Signup")
	defer span.End()

	passwordEncrypt, err := utils.Encrypt(input.Password, i.pwdEncryptSecret)
	if err != nil {
		span.SetStatus(codes.Error, "failed to encrypt password")
		span.RecordError(err)
		return err
	}

	email := input.Email
	authUser, err := i.repository.FindAuthUser(ctx, dto.AuthUserFilter{
		Email: &email,
	})
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		span.SetStatus(codes.Error, "failed to find auth user")
		span.RecordError(err)
		return err
	}

	if authUser != nil {
		if authUser.EmailConfirmedAt != nil {
			span.SetStatus(codes.Error, usecase.ErrEmailHasBeenConfirmed.Error())
			span.RecordError(usecase.ErrEmailHasBeenConfirmed)
			return usecase.ErrEmailHasBeenConfirmed
		}

		span.SetStatus(codes.Error, usecase.ErrEmailWaitAdminApproval.Error())
		span.RecordError(usecase.ErrEmailWaitAdminApproval)
		return usecase.ErrEmailWaitAdminApproval
	}

	signupEntity := &entities.SignupRequest{
		Email:    input.Email,
		Password: passwordEncrypt,
		Name:     input.Name,
		Username: input.Username,
	}

	err = i.repository.UpsertSignupRequest(ctx, signupEntity)
	if err != nil {
		span.SetStatus(codes.Error, "failed to upsert signup request")
		span.RecordError(err)
		return err
	}

	span.AddEvent("signup request created successfully")
	span.SetStatus(codes.Ok, "signup request created successfully")
	return nil
}
