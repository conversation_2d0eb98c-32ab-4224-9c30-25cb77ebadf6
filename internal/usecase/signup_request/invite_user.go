package signup_request

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/google/uuid"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
	"gorm.io/gorm"

	"api-server/internal/dto"
	"api-server/internal/entities"
	"api-server/internal/enums"
	"api-server/internal/gateways/gitlab"
	"api-server/internal/gateways/supabase"
	repository "api-server/internal/repositories"
	"api-server/internal/usecase"
	"api-server/internal/utils"
	"api-server/pkg/oteltrace"
)

// InviteUser implements the SignupRequestUsecase interface for inviting a new user to the platform.
// It handles user creation in GitLab and Supabase, group creation, and access token generation.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: InviteUserInput containing user information (name, email, role)
//
// Returns:
//   - error: Any error that occurred during user invitation
func (i *impl) InviteUser(ctx context.Context, input dto.InviteUserInput) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.signup_request.InviteUser")
	defer span.End()

	// find user in public.users table
	user, err := i.repository.FindUser(ctx, repository.FindUserFilter{
		Email: &input.Email,
	})
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		span.SetStatus(codes.Error, "failed to find user")
		span.RecordError(err)
		return err
	}

	if user != nil {
		// if user exists then find user in auth.users table
		authUser, err := i.repository.FindAuthUser(ctx, dto.AuthUserFilter{
			Email: &input.Email,
		})
		if err != nil {
			span.SetStatus(codes.Error, "failed to find auth user")
			span.RecordError(err)
			return err
		}
		// check whether user email has been confirmed
		if authUser.EmailConfirmedAt != nil {
			span.SetStatus(codes.Error, usecase.ErrEmailHasBeenConfirmed.Error())
			span.RecordError(usecase.ErrEmailHasBeenConfirmed)
			return usecase.ErrEmailHasBeenConfirmed
		}

		if _, err = i.supabaseClient.InviteUser(ctx, supabase.InviteUserRequest{
			Email: input.Email,
		}); err != nil {
			span.SetStatus(codes.Error, "failed to upsert signup request")
			span.RecordError(err)
			return err
		}
	} else {
		// get root user
		adminRefGitID := enums.AdminRefGitID
		adminUser, err := i.repository.FindUser(ctx, repository.FindUserFilter{RefGitUserID: &adminRefGitID})
		if err != nil {
			span.SetStatus(codes.Error, "failed to find user")
			span.RecordError(err)
			return err
		}

		var gitlabAdminAccessToken string
		if adminUser.GitlabAccessToken != nil {
			gitlabAdminAccessToken = *adminUser.GitlabAccessToken
		}

		username, err := generateUniqueUsername(ctx, i, input.Email)
		if err != nil {
			span.AddEvent("failed to generate unique username", trace.WithAttributes(attribute.String("username", username)))
			span.SetStatus(codes.Error, "failed to generate unique username")
			span.RecordError(err)
			return err
		}

		gitlabUser, err := createGitlabUser(ctx, i, username, input, gitlabAdminAccessToken)
		if err != nil {
			span.AddEvent("failed to create gitlab user", trace.WithAttributes(attribute.String("username", username)))
			span.SetStatus(codes.Error, "failed to create gitlab user")
			span.RecordError(err)
			return err
		}

		gitGroups, err := createGitlabGroup(ctx, i, username, gitlabUser.Id, adminUser.ID, gitlabAdminAccessToken)
		if err != nil {
			span.SetStatus(codes.Error, "failed to create gitlab group")
			span.RecordError(err)
			return err
		}

		// Create Gitlab personal access token
		// it is ok to create multiple access token with the same name
		userGitlabAccessToken, err := i.gitLabClient.CreatePersonalAccessToken(ctx, gitlab.CreatePersonalAccessTokenRequest{
			UserId:     gitlabUser.Id,
			Name:       "volvo-user-auth", // hardcode, as there should only be one instance
			AdminToken: gitlabAdminAccessToken,
			Scopes:     []gitlab.TokenScope{gitlab.ScopeAPI},
		})
		if err != nil {
			span.SetStatus(codes.Error, "failed to create personal access token")
			span.RecordError(err)
			return err
		}

		err = i.repository.Transaction(ctx, func(ctx context.Context) error {
			var authUserId uuid.UUID
			// if user exists then find user in auth.users table
			authUser, err := i.repository.FindAuthUser(ctx, dto.AuthUserFilter{
				Email: &input.Email,
			})
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				span.SetStatus(codes.Error, "failed to find auth user")
				span.RecordError(err)
				return err
			}
			// check whether user email has been confirmed
			if authUser != nil && authUser.EmailConfirmedAt != nil {
				authUserId = authUser.ID
			} else {
				// invite user in Supabase
				supabaseUserResp, err := i.supabaseClient.InviteUser(ctx, supabase.InviteUserRequest{
					Email: input.Email,
				})
				if err != nil {
					span.SetStatus(codes.Error, "failed to invite user")
					span.RecordError(err)
					return err
				}

				authUserId = supabaseUserResp.ID
			}

			// create new user in public.users table
			expiredAt, err := utils.ConvertStrToTime(userGitlabAccessToken.ExpiredAt, time.DateOnly)
			if err != nil {
				span.SetStatus(codes.Error, "failed to convert time")
				span.RecordError(err)
				return err
			}
			newUser := entities.User{
				BaseModel: entities.BaseModel{
					ID: authUserId,
				},
				Name:                       input.Name,
				Username:                   username,
				Role:                       input.Role,
				RefGitUserID:               gitlabUser.Id,
				GitlabAccessToken:          &userGitlabAccessToken.Token,
				GitlabAccessTokenExpiresAt: &expiredAt,
			}
			if err := i.repository.Create(ctx, &newUser); err != nil {
				span.SetStatus(codes.Error, "failed to create new user")
				span.RecordError(err)
				return err
			}

			// create user Git group
			_, err = i.repository.CreateUserGitGroup(ctx, repository.CreateUserGitGroupInput{
				UserID:           newUser.ID,
				RefGitSpacesID:   gitGroups[fmt.Sprintf("%s/%s", enums.RepoType_Spaces, username)].UserGitGroupId,
				RefGitDatasetsID: gitGroups[fmt.Sprintf("%s/%s", enums.RepoType_Datasets, username)].UserGitGroupId,
				RefGitModelsID:   gitGroups[fmt.Sprintf("%s/%s", enums.RepoType_Models, username)].UserGitGroupId,
				// RefGitComposesID: gitGroups[fmt.Sprintf("%s/%s", enums.RepoType_Composes, username)].UserGitGroupId,
			})
			if err != nil {
				span.SetStatus(codes.Error, "failed to create user git group")
				span.RecordError(err)
				return err
			}

			return nil
		})
		if err != nil {
			fmt.Println(err)
			span.SetStatus(codes.Error, "failed to execute transaction")
			span.RecordError(err)
			return err
		}
	}

	span.AddEvent("user invitation process completed successfully")
	span.SetStatus(codes.Ok, "user invitation process completed successfully")
	return nil
}

// generateUniqueUsername generates a unique username for a new user based on their email.
// It ensures the generated username is not already taken by adding a numeric suffix if needed.
//
// Parameters:
//   - ctx: Context for the operation
//   - i: Implementation instance
//   - email: Email address to generate username from
//
// Returns:
//   - string: Generated unique username
//   - error: Any error that occurred during username generation
func generateUniqueUsername(ctx context.Context, i *impl, email string) (string, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.signup_request.generateUniqueUsername")
	defer span.End()

	baseUsername, err := gitlab.GenerateUsername(email)
	if err != nil {
		span.SetStatus(codes.Error, "failed to generate username")
		span.RecordError(err)
		return "", err
	}

	username := baseUsername
	suffix := 1
	for {
		count, err := i.repository.CountPrefixUsername(ctx, username)
		if err != nil {
			span.SetStatus(codes.Error, "failed to count username")
			span.RecordError(err)
			return "", err
		}

		if count == 0 {
			break // username is unique
		}

		username = fmt.Sprintf("%s%d", baseUsername, suffix)
		suffix++
	}

	span.AddEvent("generate unique username successfully")
	span.SetStatus(codes.Ok, "generate unique username successfully")
	return username, nil
}

// createGitlabUser creates or retrieves a GitLab user.
// It either finds an existing user by email or creates a new one with the provided information.
//
// Parameters:
//   - ctx: Context for the operation
//   - i: Implementation instance
//   - username: Username for the GitLab user
//   - input: InviteUserInput containing user information
//   - gitlabAdminAccessToken: Admin access token for GitLab API calls
//
// Returns:
//   - *gitlab.CreateUserResponse: Created or found GitLab user information
//   - error: Any error that occurred during user creation/retrieval
func createGitlabUser(ctx context.Context, i *impl, username string, input dto.InviteUserInput, gitlabAdminAccessToken string) (*gitlab.CreateUserResponse, error) {
	users, err := i.gitLabClient.GetUser(ctx, gitlab.GetUserRequest{
		Email:            input.Email,
		AdminAccessToken: gitlabAdminAccessToken,
	})
	if err != nil {
		return nil, err
	}

	var gitlabUser gitlab.CreateUserResponse
	if len(users) > 0 {
		gitlabUser = users[0]
	} else {
		gitlabUserResp, err := i.gitLabClient.CreateUser(ctx, gitlab.CreateUserRequest{
			Email:               input.Email,
			Name:                input.Name,
			AdminAccessToken:    gitlabAdminAccessToken,
			Username:            username,
			ForceRandomPassword: true,
			SkipConfirmation:    true,
		})
		if err != nil {
			return nil, err
		}

		gitlabUser = *gitlabUserResp
	}
	//if the user is in the process of being deleted
	if string(gitlabUser.State) == "blocked" {
		return nil, usecase.ErrInputUserDisable
	}

	return &gitlabUser, err
}

type Group struct {
	DefaultGitGroupId int64
	UserGitGroupId    int64
}

// createGitlabGroup creates GitLab groups for a user.
// It creates or retrieves groups for spaces, datasets, and models repositories.
//
// Parameters:
//   - ctx: Context for the operation
//   - i: Implementation instance
//   - username: Username to use for group paths
//   - gitlabUserID: GitLab user ID to associate with groups
//   - adminUserID: Admin user ID for reference
//   - gitlabAdminAccessToken: Admin access token for GitLab API calls
//
// Returns:
//   - map[string]Group: Map of repository types to their GitLab group information
//   - error: Any error that occurred during group creation
func createGitlabGroup(ctx context.Context, i *impl, username string, gitlabUserID int64, adminUserID uuid.UUID, gitlabAdminAccessToken string) (map[string]Group, error) {
	adminGitGroupInfo, err := i.repository.FindUserGitGroup(ctx, adminUserID)
	if err != nil {
		return nil, err
	}

	// map (git group path/git group id)
	gitGroups := map[string]Group{
		fmt.Sprintf("%s/%s", enums.RepoType_Spaces, username): {
			DefaultGitGroupId: adminGitGroupInfo.RefGitSpacesID,
		},
		fmt.Sprintf("%s/%s", enums.RepoType_Datasets, username): {
			DefaultGitGroupId: adminGitGroupInfo.RefGitDatasetsID,
		},
		fmt.Sprintf("%s/%s", enums.RepoType_Models, username): {
			DefaultGitGroupId: adminGitGroupInfo.RefGitModelsID,
		},
		// fmt.Sprintf("%s/%s", enums.RepoType_Composes, username): {
		// 	DefaultGitGroupId: adminGitGroupInfo.RefGitComposesID,
		// },
	}
	if adminGitGroupInfo.RefGitComposesID != nil {
		gitGroups[fmt.Sprintf("%s/%s", enums.RepoType_Composes, username)] = Group{
			DefaultGitGroupId: *adminGitGroupInfo.RefGitComposesID,
		}
	}

	for key := range gitGroups {
		orgs, err := i.gitLabClient.GetGroup(ctx, gitlab.GetGroupRequest{
			Token: gitlabAdminAccessToken,
			Path:  key,
		})
		if err != nil {
			return nil, err
		}

		if len(orgs) > 0 {
			gitGroups[key] = Group{
				UserGitGroupId: int64(orgs[0].Id),
			}
		} else {
			groupId, err := i.gitLabClient.CreateUserGroup(ctx, gitlabUserID, username, key, gitlabAdminAccessToken, gitGroups[key].DefaultGitGroupId)
			if err != nil {
				return nil, err
			}

			gitGroups[key] = Group{
				UserGitGroupId: groupId,
			}
		}
	}

	return gitGroups, nil
}
