package signup_request

import (
	"context"

	"api-server/internal/dto"
	supabase "api-server/internal/gateways/supabase"
	"api-server/internal/usecase"
	"api-server/pkg/oteltrace"

	"go.opentelemetry.io/otel/codes"
)

// SendConfirmEmail implements the SignupRequestUsecase interface for sending confirmation emails.
// It handles sending either signup confirmation or user invitation emails based on user status.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: Input data containing email address to send confirmation to
//
// Returns:
//   - error: Any error that occurred during email sending
func (i *impl) SendConfirmEmail(ctx context.Context, input dto.SendConfirmEmailInput) error {
	c, span := oteltrace.Tracer.Start(ctx, "usecase.signup_request.SendConfirmEmail")
	defer span.End()

	// if email exists then find user in auth.users table
	authUser, err := i.repository.FindAuthUser(ctx, dto.AuthUserFilter{
		Email: &input.Email,
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find auth user")
		span.RecordError(err)
		return err
	}
	// user exists and user's email has been confirmed
	if authUser != nil && authUser.EmailConfirmedAt != nil {
		span.SetStatus(codes.Error, usecase.ErrEmailHasBeenConfirmed.Error())
		span.RecordError(usecase.ErrEmailHasBeenConfirmed)
		return usecase.ErrEmailHasBeenConfirmed
	}

	if authUser != nil && authUser.EncryptedPassword != "" {
		//user not exist -> send email signup to the user
		_, err = i.supabaseClient.SignUp(ctx, supabase.SignupRequest{
			Email:    input.Email,
			Password: "123qweA@", //default password, just to send request success
		})
		if err != nil {
			span.SetStatus(codes.Error, "failed to resend user approval request email")
			span.RecordError(err)
			return err
		}
	} else {
		//user not exist -> send email invite to the user
		if _, err = i.supabaseClient.InviteUser(c, supabase.InviteUserRequest{
			Email: input.Email,
		}); err != nil {
			span.SetStatus(codes.Error, "failed to resend user invitation email")
			span.RecordError(err)
			return err
		}
	}

	span.SetStatus(codes.Ok, "send confirm email successfully")
	return nil
}
