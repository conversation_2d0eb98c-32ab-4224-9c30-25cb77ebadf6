// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	dto "api-server/internal/dto"
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// MockSignupRequestUsecase is an autogenerated mock type for the SignupRequestUsecase type
type MockSignupRequestUsecase struct {
	mock.Mock
}

type MockSignupRequestUsecase_Expecter struct {
	mock *mock.Mock
}

func (_m *MockSignupRequestUsecase) EXPECT() *MockSignupRequestUsecase_Expecter {
	return &MockSignupRequestUsecase_Expecter{mock: &_m.Mock}
}

// GetAllSignUpRequests provides a mock function with given fields: ctx
func (_m *MockSignupRequestUsecase) GetAllSignUpRequests(ctx context.Context) (dto.GetAllSignUpRequestsOutput, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetAllSignUpRequests")
	}

	var r0 dto.GetAllSignUpRequestsOutput
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) (dto.GetAllSignUpRequestsOutput, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) dto.GetAllSignUpRequestsOutput); ok {
		r0 = rf(ctx)
	} else {
		r0 = ret.Get(0).(dto.GetAllSignUpRequestsOutput)
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockSignupRequestUsecase_GetAllSignUpRequests_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllSignUpRequests'
type MockSignupRequestUsecase_GetAllSignUpRequests_Call struct {
	*mock.Call
}

// GetAllSignUpRequests is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockSignupRequestUsecase_Expecter) GetAllSignUpRequests(ctx interface{}) *MockSignupRequestUsecase_GetAllSignUpRequests_Call {
	return &MockSignupRequestUsecase_GetAllSignUpRequests_Call{Call: _e.mock.On("GetAllSignUpRequests", ctx)}
}

func (_c *MockSignupRequestUsecase_GetAllSignUpRequests_Call) Run(run func(ctx context.Context)) *MockSignupRequestUsecase_GetAllSignUpRequests_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockSignupRequestUsecase_GetAllSignUpRequests_Call) Return(_a0 dto.GetAllSignUpRequestsOutput, _a1 error) *MockSignupRequestUsecase_GetAllSignUpRequests_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockSignupRequestUsecase_GetAllSignUpRequests_Call) RunAndReturn(run func(context.Context) (dto.GetAllSignUpRequestsOutput, error)) *MockSignupRequestUsecase_GetAllSignUpRequests_Call {
	_c.Call.Return(run)
	return _c
}

// InviteUser provides a mock function with given fields: ctx, input
func (_m *MockSignupRequestUsecase) InviteUser(ctx context.Context, input dto.InviteUserInput) error {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for InviteUser")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.InviteUserInput) error); ok {
		r0 = rf(ctx, input)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockSignupRequestUsecase_InviteUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'InviteUser'
type MockSignupRequestUsecase_InviteUser_Call struct {
	*mock.Call
}

// InviteUser is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.InviteUserInput
func (_e *MockSignupRequestUsecase_Expecter) InviteUser(ctx interface{}, input interface{}) *MockSignupRequestUsecase_InviteUser_Call {
	return &MockSignupRequestUsecase_InviteUser_Call{Call: _e.mock.On("InviteUser", ctx, input)}
}

func (_c *MockSignupRequestUsecase_InviteUser_Call) Run(run func(ctx context.Context, input dto.InviteUserInput)) *MockSignupRequestUsecase_InviteUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.InviteUserInput))
	})
	return _c
}

func (_c *MockSignupRequestUsecase_InviteUser_Call) Return(_a0 error) *MockSignupRequestUsecase_InviteUser_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockSignupRequestUsecase_InviteUser_Call) RunAndReturn(run func(context.Context, dto.InviteUserInput) error) *MockSignupRequestUsecase_InviteUser_Call {
	_c.Call.Return(run)
	return _c
}

// ProcessSignUpRequest provides a mock function with given fields: ctx, input
func (_m *MockSignupRequestUsecase) ProcessSignUpRequest(ctx context.Context, input dto.ApprovalRequest) error {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for ProcessSignUpRequest")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.ApprovalRequest) error); ok {
		r0 = rf(ctx, input)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockSignupRequestUsecase_ProcessSignUpRequest_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ProcessSignUpRequest'
type MockSignupRequestUsecase_ProcessSignUpRequest_Call struct {
	*mock.Call
}

// ProcessSignUpRequest is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.ApprovalRequest
func (_e *MockSignupRequestUsecase_Expecter) ProcessSignUpRequest(ctx interface{}, input interface{}) *MockSignupRequestUsecase_ProcessSignUpRequest_Call {
	return &MockSignupRequestUsecase_ProcessSignUpRequest_Call{Call: _e.mock.On("ProcessSignUpRequest", ctx, input)}
}

func (_c *MockSignupRequestUsecase_ProcessSignUpRequest_Call) Run(run func(ctx context.Context, input dto.ApprovalRequest)) *MockSignupRequestUsecase_ProcessSignUpRequest_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.ApprovalRequest))
	})
	return _c
}

func (_c *MockSignupRequestUsecase_ProcessSignUpRequest_Call) Return(_a0 error) *MockSignupRequestUsecase_ProcessSignUpRequest_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockSignupRequestUsecase_ProcessSignUpRequest_Call) RunAndReturn(run func(context.Context, dto.ApprovalRequest) error) *MockSignupRequestUsecase_ProcessSignUpRequest_Call {
	_c.Call.Return(run)
	return _c
}

// RefreshAccessToken provides a mock function with given fields: ctx, userRefGitID
func (_m *MockSignupRequestUsecase) RefreshAccessToken(ctx context.Context, userRefGitID int64) error {
	ret := _m.Called(ctx, userRefGitID)

	if len(ret) == 0 {
		panic("no return value specified for RefreshAccessToken")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) error); ok {
		r0 = rf(ctx, userRefGitID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockSignupRequestUsecase_RefreshAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RefreshAccessToken'
type MockSignupRequestUsecase_RefreshAccessToken_Call struct {
	*mock.Call
}

// RefreshAccessToken is a helper method to define mock.On call
//   - ctx context.Context
//   - userRefGitID int64
func (_e *MockSignupRequestUsecase_Expecter) RefreshAccessToken(ctx interface{}, userRefGitID interface{}) *MockSignupRequestUsecase_RefreshAccessToken_Call {
	return &MockSignupRequestUsecase_RefreshAccessToken_Call{Call: _e.mock.On("RefreshAccessToken", ctx, userRefGitID)}
}

func (_c *MockSignupRequestUsecase_RefreshAccessToken_Call) Run(run func(ctx context.Context, userRefGitID int64)) *MockSignupRequestUsecase_RefreshAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *MockSignupRequestUsecase_RefreshAccessToken_Call) Return(_a0 error) *MockSignupRequestUsecase_RefreshAccessToken_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockSignupRequestUsecase_RefreshAccessToken_Call) RunAndReturn(run func(context.Context, int64) error) *MockSignupRequestUsecase_RefreshAccessToken_Call {
	_c.Call.Return(run)
	return _c
}

// SendConfirmEmail provides a mock function with given fields: ctx, input
func (_m *MockSignupRequestUsecase) SendConfirmEmail(ctx context.Context, input dto.SendConfirmEmailInput) error {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for SendConfirmEmail")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.SendConfirmEmailInput) error); ok {
		r0 = rf(ctx, input)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockSignupRequestUsecase_SendConfirmEmail_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SendConfirmEmail'
type MockSignupRequestUsecase_SendConfirmEmail_Call struct {
	*mock.Call
}

// SendConfirmEmail is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.SendConfirmEmailInput
func (_e *MockSignupRequestUsecase_Expecter) SendConfirmEmail(ctx interface{}, input interface{}) *MockSignupRequestUsecase_SendConfirmEmail_Call {
	return &MockSignupRequestUsecase_SendConfirmEmail_Call{Call: _e.mock.On("SendConfirmEmail", ctx, input)}
}

func (_c *MockSignupRequestUsecase_SendConfirmEmail_Call) Run(run func(ctx context.Context, input dto.SendConfirmEmailInput)) *MockSignupRequestUsecase_SendConfirmEmail_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.SendConfirmEmailInput))
	})
	return _c
}

func (_c *MockSignupRequestUsecase_SendConfirmEmail_Call) Return(_a0 error) *MockSignupRequestUsecase_SendConfirmEmail_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockSignupRequestUsecase_SendConfirmEmail_Call) RunAndReturn(run func(context.Context, dto.SendConfirmEmailInput) error) *MockSignupRequestUsecase_SendConfirmEmail_Call {
	_c.Call.Return(run)
	return _c
}

// Signup provides a mock function with given fields: ctx, input
func (_m *MockSignupRequestUsecase) Signup(ctx context.Context, input dto.SignupInput) error {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for Signup")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.SignupInput) error); ok {
		r0 = rf(ctx, input)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockSignupRequestUsecase_Signup_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Signup'
type MockSignupRequestUsecase_Signup_Call struct {
	*mock.Call
}

// Signup is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.SignupInput
func (_e *MockSignupRequestUsecase_Expecter) Signup(ctx interface{}, input interface{}) *MockSignupRequestUsecase_Signup_Call {
	return &MockSignupRequestUsecase_Signup_Call{Call: _e.mock.On("Signup", ctx, input)}
}

func (_c *MockSignupRequestUsecase_Signup_Call) Run(run func(ctx context.Context, input dto.SignupInput)) *MockSignupRequestUsecase_Signup_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.SignupInput))
	})
	return _c
}

func (_c *MockSignupRequestUsecase_Signup_Call) Return(_a0 error) *MockSignupRequestUsecase_Signup_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockSignupRequestUsecase_Signup_Call) RunAndReturn(run func(context.Context, dto.SignupInput) error) *MockSignupRequestUsecase_Signup_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockSignupRequestUsecase creates a new instance of MockSignupRequestUsecase. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockSignupRequestUsecase(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockSignupRequestUsecase {
	mock := &MockSignupRequestUsecase{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
