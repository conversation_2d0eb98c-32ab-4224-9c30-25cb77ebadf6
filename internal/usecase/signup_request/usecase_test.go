package signup_request_test

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gorm.io/gorm"

	"api-server/configs"
	"api-server/internal/dto"
	"api-server/internal/entities"
	"api-server/internal/enums"
	"api-server/internal/gateways/gitlab"
	gitlab_mocks "api-server/internal/gateways/gitlab/mocks"
	mail_mocks "api-server/internal/gateways/mail/mocks"
	supabase "api-server/internal/gateways/supabase"
	supabase_mocks "api-server/internal/gateways/supabase/mocks"
	repository_mocks "api-server/internal/repositories/mocks"
	"api-server/internal/usecase"
	"api-server/internal/usecase/signup_request"
	"api-server/internal/utils"
)

var (
	MockPasswordEncryptSecret  string = "N1PCdw3M2B1TfJhoaY2mL736p2vCUc47"
	MockGitLabAdminAccessToken string = "**************************"
)

func TestSignup(t *testing.T) {
	type dependencies struct {
		repo           *repository_mocks.MockRepository
		gitlabClient   *gitlab_mocks.MockGitlabClient
		supabaseClient *supabase_mocks.MockSupabaseClient
	}

	confirmedAt := time.Now()
	tests := []struct {
		name     string
		input    dto.SignupInput
		ctx      context.Context
		mockFn   func(d *dependencies)
		expError error
	}{
		{
			name: "should return error when FindAuthUser failed",
			input: dto.SignupInput{
				Email:    "",
				Password: "",
				Name:     "",
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindAuthUser", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error when FindAuthUser failed",
			input: dto.SignupInput{
				Email:    "",
				Password: "",
				Name:     "",
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindAuthUser", mock.Anything, mock.Anything).Return(&entities.AuthUser{EmailConfirmedAt: &confirmedAt}, nil)
			},
			expError: usecase.ErrEmailHasBeenConfirmed,
		},
		{
			name: "should return error when FindAuthUser failed",
			input: dto.SignupInput{
				Email:    "",
				Password: "",
				Name:     "",
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindAuthUser", mock.Anything, mock.Anything).Return(&entities.AuthUser{EmailConfirmedAt: nil}, nil)
			},
			expError: usecase.ErrEmailWaitAdminApproval,
		},
		{
			name: "should return error when UpsertSignupRequest failed",
			input: dto.SignupInput{
				Email:    "",
				Password: "",
				Name:     "",
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindAuthUser", mock.Anything, mock.Anything).Return(nil, nil)
				d.repo.On("UpsertSignupRequest", mock.Anything, mock.Anything).Return(errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return nil when everything is ok",
			input: dto.SignupInput{
				Email:    "",
				Password: "",
				Name:     "",
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindAuthUser", mock.Anything, mock.Anything).Return(nil, nil)
				d.repo.On("UpsertSignupRequest", mock.Anything, mock.Anything).Return(nil)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			dep := dependencies{
				repo:           &repository_mocks.MockRepository{},
				gitlabClient:   &gitlab_mocks.MockGitlabClient{},
				supabaseClient: &supabase_mocks.MockSupabaseClient{},
			}
			tt.mockFn(&dep)

			u := signup_request.New(nil, dep.repo, dep.gitlabClient, dep.supabaseClient, MockPasswordEncryptSecret, nil)
			err := u.Signup(context.Background(), tt.input)
			if err != nil && err.Error() != tt.expError.Error() {
				t.Errorf("expected error %v, got %v", tt.expError, err)
			}
		})
	}
}

func TestListSignUpRequests(t *testing.T) {
	type dependencies struct {
		repo *repository_mocks.MockRepository
	}
	tests := []struct {
		name     string
		mockFn   func(d *dependencies)
		expError error
	}{
		{
			name: "should return error when GetAllSignUpRequests returns error",
			mockFn: func(d *dependencies) {
				d.repo.On("ListSignUpRequests", mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should list all sign up requests successfully",
			mockFn: func(d *dependencies) {
				d.repo.On("ListSignUpRequests", mock.Anything).Return([]entities.SignupRequest{}, nil)
			},
			expError: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Arrange
			dep := dependencies{
				repo: &repository_mocks.MockRepository{},
			}
			tt.mockFn(&dep)

			// Act
			_, err := dep.repo.ListSignUpRequests(context.Background())
			assert.Equal(t, tt.expError, err)
		})
	}
}

func TestProcessSignUpRequest(t *testing.T) {
	uuidInput := uuid.New()
	expired := time.Now().Format("2006-01-02")
	isAccepted := true
	notAccepted := false
	type dependencies struct {
		config         *configs.GlobalConfig
		repo           *repository_mocks.MockRepository
		gitlabClient   *gitlab_mocks.MockGitlabClient
		supabaseClient *supabase_mocks.MockSupabaseClient
		mailClient     *mail_mocks.MockMailClient
	}

	tests := []struct {
		name     string
		ctx      context.Context
		input    dto.ApprovalRequest
		mockFn   func(d *dependencies)
		expError error
	}{
		{
			name: "should return error when GetSignUpRequestByID returns error",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.ApprovalRequest{
				IsAccepted: &isAccepted,
				ID:         uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("GetSignUpRequestByID", mock.Anything, uuidInput).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error when GetSignUpRequestByID returns error with gorm.ErrRecordNotFound",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.ApprovalRequest{
				IsAccepted: &isAccepted,
				ID:         uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("GetSignUpRequestByID", mock.Anything, uuidInput).Return(nil, gorm.ErrRecordNotFound)
			},
			expError: usecase.ErrSignUpRequestNotFound,
		},
		{
			name: "should return error when FindUserByID returns error with gorm.ErrRecordNotFound",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.ApprovalRequest{
				IsAccepted:    &isAccepted,
				ID:            uuidInput,
				CurrentUserID: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("GetSignUpRequestByID", mock.Anything, uuidInput).Return(&entities.SignupRequest{
					Password: "Zz3AJ+LA0AKR/Mqv+RvtMnUXZ8Rm5Bp/KTKsv7bCj2Xj9LPH",
					Email:    "<EMAIL>",
				}, nil)
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(nil, gorm.ErrRecordNotFound)
			},
			expError: usecase.ErrUserNotFound,
		},
		{
			name: "should return error when FindUserByID returns error",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.ApprovalRequest{
				IsAccepted:    &isAccepted,
				ID:            uuidInput,
				CurrentUserID: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("GetSignUpRequestByID", mock.Anything, uuidInput).Return(&entities.SignupRequest{
					Password: "Zz3AJ+LA0AKR/Mqv+RvtMnUXZ8Rm5Bp/KTKsv7bCj2Xj9LPH",
					Email:    "<EMAIL>",
				}, nil)
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return nil when input.IsAccepted is false",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.ApprovalRequest{
				IsAccepted:    &notAccepted,
				ID:            uuidInput,
				CurrentUserID: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("GetSignUpRequestByID", mock.Anything, uuidInput).Return(&entities.SignupRequest{
					Password: "Zz3AJ+LA0AKR/Mqv+RvtMnUXZ8Rm5Bp/KTKsv7bCj2Xj9LPH",
					Email:    "<EMAIL>",
				}, nil)
				d.repo.On("Transaction", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), uuidInput))
				}).Return(nil)
				// d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{}, nil)
				d.repo.On("DeleteById", mock.Anything, mock.Anything, uuidInput).Return(nil)
				d.mailClient.On("SendMail", mock.Anything, mock.Anything).Return(nil)
			},
		},
		{
			name: "should return error when mailClient.SendMail return error",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.ApprovalRequest{
				IsAccepted:    &notAccepted,
				ID:            uuidInput,
				CurrentUserID: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("GetSignUpRequestByID", mock.Anything, uuidInput).Return(&entities.SignupRequest{
					Password: "Zz3AJ+LA0AKR/Mqv+RvtMnUXZ8Rm5Bp/KTKsv7bCj2Xj9LPH",
					Email:    "<EMAIL>",
				}, nil)
				d.repo.On("Transaction", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), uuidInput))
				}).Return(usecase.ErrSendMail)
				d.repo.On("DeleteById", mock.Anything, mock.Anything, uuidInput).Return(nil)
				d.mailClient.On("SendMail", mock.Anything, mock.Anything).Return(errors.New("error"))
			},
			expError: usecase.ErrSendMail,
		},
		{
			name: "should return nil when repo.DeleteById return errors",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.ApprovalRequest{
				IsAccepted:    &notAccepted,
				ID:            uuidInput,
				CurrentUserID: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("GetSignUpRequestByID", mock.Anything, uuidInput).Return(&entities.SignupRequest{
					Password: "Zz3AJ+LA0AKR/Mqv+RvtMnUXZ8Rm5Bp/KTKsv7bCj2Xj9LPH",
					Email:    "<EMAIL>",
				}, nil)
				d.repo.On("Transaction", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), uuidInput))
				}).Return(errors.New("error"))
				d.repo.On("DeleteById", mock.Anything, mock.Anything, uuidInput).Return(errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error when repository.FindUser return error",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.ApprovalRequest{
				IsAccepted:    &isAccepted,
				ID:            uuidInput,
				CurrentUserID: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("GetSignUpRequestByID", mock.Anything, uuidInput).Return(&entities.SignupRequest{
					Password: "Zz3AJ+LA0AKR/Mqv+RvtMnUXZ8Rm5Bp/KTKsv7bCj2Xj9LPH",
					Email:    "<EMAIL>",
				}, nil)
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{}, nil)
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error when gitLabClient.GetUser return error",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.ApprovalRequest{
				IsAccepted:    &isAccepted,
				ID:            uuidInput,
				CurrentUserID: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("GetSignUpRequestByID", mock.Anything, uuidInput).Return(&entities.SignupRequest{
					Password: "Zz3AJ+LA0AKR/Mqv+RvtMnUXZ8Rm5Bp/KTKsv7bCj2Xj9LPH",
					Email:    "<EMAIL>",
				}, nil)
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{}, nil)
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.gitlabClient.On("GetUser", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error when gitLabClient.CreateUser return error",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.ApprovalRequest{
				IsAccepted:    &isAccepted,
				ID:            uuidInput,
				CurrentUserID: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("GetSignUpRequestByID", mock.Anything, uuidInput).Return(&entities.SignupRequest{
					Password: "Zz3AJ+LA0AKR/Mqv+RvtMnUXZ8Rm5Bp/KTKsv7bCj2Xj9LPH",
					Email:    "<EMAIL>",
				}, nil)
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{}, nil)
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.gitlabClient.On("GetUser", mock.Anything, mock.Anything).Return([]gitlab.CreateUserResponse{}, nil)
				d.gitlabClient.On("CreateUser", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error when repository.FindUserGitGroup return error",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.ApprovalRequest{
				IsAccepted:    &isAccepted,
				ID:            uuidInput,
				CurrentUserID: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("GetSignUpRequestByID", mock.Anything, uuidInput).Return(&entities.SignupRequest{
					Password: "Zz3AJ+LA0AKR/Mqv+RvtMnUXZ8Rm5Bp/KTKsv7bCj2Xj9LPH",
					Email:    "<EMAIL>",
				}, nil)
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{}, nil)
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.gitlabClient.On("GetUser", mock.Anything, mock.Anything).Return([]gitlab.CreateUserResponse{}, nil)
				d.gitlabClient.On("CreateUser", mock.Anything, mock.Anything).Return(&gitlab.CreateUserResponse{}, nil)
				d.repo.On("FindUserGitGroup", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error when repository.GetGroup return error",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.ApprovalRequest{
				IsAccepted:    &isAccepted,
				ID:            uuidInput,
				CurrentUserID: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("GetSignUpRequestByID", mock.Anything, uuidInput).Return(&entities.SignupRequest{
					Password: "Zz3AJ+LA0AKR/Mqv+RvtMnUXZ8Rm5Bp/KTKsv7bCj2Xj9LPH",
					Email:    "<EMAIL>",
				}, nil)
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{}, nil)
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.gitlabClient.On("GetUser", mock.Anything, mock.Anything).Return([]gitlab.CreateUserResponse{}, nil)
				d.gitlabClient.On("CreateUser", mock.Anything, mock.Anything).Return(&gitlab.CreateUserResponse{}, nil)
				d.repo.On("FindUserGitGroup", mock.Anything, mock.Anything).Return(&entities.DefaultGitGroup{}, nil)
				d.gitlabClient.On("GetGroup", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error when supabaseClient.SignUp return error",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.ApprovalRequest{
				IsAccepted:    &isAccepted,
				ID:            uuidInput,
				CurrentUserID: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("GetSignUpRequestByID", mock.Anything, uuidInput).Return(&entities.SignupRequest{
					Password: "Zz3AJ+LA0AKR/Mqv+RvtMnUXZ8Rm5Bp/KTKsv7bCj2Xj9LPH",
					Email:    "<EMAIL>",
				}, nil)
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{}, nil)
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.gitlabClient.On("GetUser", mock.Anything, mock.Anything).Return([]gitlab.CreateUserResponse{}, nil)
				d.gitlabClient.On("CreateUser", mock.Anything, mock.Anything).Return(&gitlab.CreateUserResponse{}, nil)
				d.repo.On("FindUserGitGroup", mock.Anything, mock.Anything).Return(&entities.DefaultGitGroup{}, nil)
				d.gitlabClient.On("GetGroup", mock.Anything, mock.Anything).Return([]gitlab.GitlabGroup{}, nil)
				d.gitlabClient.On("CreateUserGroup", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(int64(1), nil)
				d.gitlabClient.On("UpdateGroup", mock.Anything, mock.Anything).Return(nil)
				d.gitlabClient.On("AddUserToGroup", mock.Anything, mock.Anything).Return(&gitlab.AddUserToGroupResponse{}, nil)
				d.repo.On("FindAuthUser", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
				d.supabaseClient.On("SignUp", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error when gitlabClient.UpdateGroup return error",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.ApprovalRequest{
				IsAccepted:    &isAccepted,
				ID:            uuidInput,
				CurrentUserID: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("GetSignUpRequestByID", mock.Anything, uuidInput).Return(&entities.SignupRequest{
					Password: "Zz3AJ+LA0AKR/Mqv+RvtMnUXZ8Rm5Bp/KTKsv7bCj2Xj9LPH",
					Email:    "<EMAIL>",
				}, nil)
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{}, nil)
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.gitlabClient.On("GetUser", mock.Anything, mock.Anything).Return([]gitlab.CreateUserResponse{}, nil)
				d.gitlabClient.On("CreateUser", mock.Anything, mock.Anything).Return(&gitlab.CreateUserResponse{}, nil)
				d.repo.On("FindUserGitGroup", mock.Anything, mock.Anything).Return(&entities.DefaultGitGroup{}, nil)
				d.gitlabClient.On("GetGroup", mock.Anything, mock.Anything).Return([]gitlab.GitlabGroup{}, nil)
				d.gitlabClient.On("CreateUserGroup", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(int64(1), nil)
				d.gitlabClient.On("UpdateGroup", mock.Anything, mock.Anything).Return(errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error when gitlabClient.CreateUserGroup return error",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.ApprovalRequest{
				IsAccepted:    &isAccepted,
				ID:            uuidInput,
				CurrentUserID: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("GetSignUpRequestByID", mock.Anything, uuidInput).Return(&entities.SignupRequest{
					Password: "Zz3AJ+LA0AKR/Mqv+RvtMnUXZ8Rm5Bp/KTKsv7bCj2Xj9LPH",
					Email:    "<EMAIL>",
				}, nil)
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{}, nil)
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.gitlabClient.On("GetUser", mock.Anything, mock.Anything).Return([]gitlab.CreateUserResponse{}, nil)
				d.gitlabClient.On("CreateUser", mock.Anything, mock.Anything).Return(&gitlab.CreateUserResponse{}, nil)
				d.repo.On("FindUserGitGroup", mock.Anything, mock.Anything).Return(&entities.DefaultGitGroup{}, nil)
				d.gitlabClient.On("GetGroup", mock.Anything, mock.Anything).Return([]gitlab.GitlabGroup{}, nil)
				d.gitlabClient.On("CreateUserGroup", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should repo.Create return error",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.ApprovalRequest{
				IsAccepted:    &isAccepted,
				ID:            uuidInput,
				CurrentUserID: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("GetSignUpRequestByID", mock.Anything, uuidInput).Return(&entities.SignupRequest{
					Password: "Zz3AJ+LA0AKR/Mqv+RvtMnUXZ8Rm5Bp/KTKsv7bCj2Xj9LPH",
					Email:    "<EMAIL>",
				}, nil)
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{}, nil)
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.gitlabClient.On("GetUser", mock.Anything, mock.Anything).Return([]gitlab.CreateUserResponse{}, nil)
				d.gitlabClient.On("CreateUser", mock.Anything, mock.Anything).Return(&gitlab.CreateUserResponse{}, nil)
				d.repo.On("FindUserGitGroup", mock.Anything, mock.Anything).Return(&entities.DefaultGitGroup{}, nil)
				d.gitlabClient.On("GetGroup", mock.Anything, mock.Anything).Return([]gitlab.GitlabGroup{}, nil)
				d.gitlabClient.On("CreateUserGroup", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(int64(1), nil)
				d.gitlabClient.On("UpdateGroup", mock.Anything, mock.Anything).Return(nil)
				d.gitlabClient.On("AddUserToGroup", mock.Anything, mock.Anything).Return(&gitlab.AddUserToGroupResponse{}, nil)
				d.repo.On("FindAuthUser", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
				d.supabaseClient.On("SignUp", mock.Anything, mock.Anything).Return(&supabase.SignupResponse{}, nil)
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), uuidInput))
				}).Return(errors.New("error"))
				d.repo.On("Create", mock.Anything, mock.Anything).Return(errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return gitLabClient.CreatePersonalAccessToken error",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.ApprovalRequest{
				IsAccepted:    &isAccepted,
				ID:            uuidInput,
				CurrentUserID: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("GetSignUpRequestByID", mock.Anything, uuidInput).Return(&entities.SignupRequest{
					Password: "Zz3AJ+LA0AKR/Mqv+RvtMnUXZ8Rm5Bp/KTKsv7bCj2Xj9LPH",
					Email:    "<EMAIL>",
				}, nil)
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{}, nil)
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.gitlabClient.On("GetUser", mock.Anything, mock.Anything).Return([]gitlab.CreateUserResponse{}, nil)
				d.gitlabClient.On("CreateUser", mock.Anything, mock.Anything).Return(&gitlab.CreateUserResponse{}, nil)
				d.repo.On("FindUserGitGroup", mock.Anything, mock.Anything).Return(&entities.DefaultGitGroup{}, nil)
				d.gitlabClient.On("GetGroup", mock.Anything, mock.Anything).Return([]gitlab.GitlabGroup{}, nil)
				d.gitlabClient.On("CreateUserGroup", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(int64(1), nil)
				d.gitlabClient.On("UpdateGroup", mock.Anything, mock.Anything).Return(nil)
				d.gitlabClient.On("AddUserToGroup", mock.Anything, mock.Anything).Return(&gitlab.AddUserToGroupResponse{}, nil)
				d.repo.On("FindAuthUser", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
				d.supabaseClient.On("SignUp", mock.Anything, mock.Anything).Return(&supabase.SignupResponse{}, nil)
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), uuidInput))
				}).Return(errors.New("error"))
				d.repo.On("Create", mock.Anything, mock.Anything).Return(nil)
				d.gitlabClient.On("CreatePersonalAccessToken", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return repo.Save error",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.ApprovalRequest{
				IsAccepted:    &isAccepted,
				ID:            uuidInput,
				CurrentUserID: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("GetSignUpRequestByID", mock.Anything, uuidInput).Return(&entities.SignupRequest{
					Password: "Zz3AJ+LA0AKR/Mqv+RvtMnUXZ8Rm5Bp/KTKsv7bCj2Xj9LPH",
					Email:    "<EMAIL>",
				}, nil)
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{}, nil)
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.gitlabClient.On("GetUser", mock.Anything, mock.Anything).Return([]gitlab.CreateUserResponse{}, nil)
				d.gitlabClient.On("CreateUser", mock.Anything, mock.Anything).Return(&gitlab.CreateUserResponse{}, nil)
				d.repo.On("FindUserGitGroup", mock.Anything, mock.Anything).Return(&entities.DefaultGitGroup{}, nil)
				d.gitlabClient.On("GetGroup", mock.Anything, mock.Anything).Return([]gitlab.GitlabGroup{}, nil)
				d.gitlabClient.On("CreateUserGroup", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(int64(1), nil)
				d.gitlabClient.On("UpdateGroup", mock.Anything, mock.Anything).Return(nil)
				d.gitlabClient.On("AddUserToGroup", mock.Anything, mock.Anything).Return(&gitlab.AddUserToGroupResponse{}, nil)
				d.repo.On("FindAuthUser", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
				d.supabaseClient.On("SignUp", mock.Anything, mock.Anything).Return(&supabase.SignupResponse{}, nil)
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), uuidInput))
				}).Return(errors.New("error"))
				d.repo.On("Create", mock.Anything, mock.Anything).Return(nil)
				d.gitlabClient.On("CreatePersonalAccessToken", mock.Anything, mock.Anything).Return(&gitlab.CreatePersonalAccessTokenResponse{ExpiredAt: expired}, nil)
				d.repo.On("Save", mock.Anything, mock.Anything).Return(errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error when input user is disable because is deleting progress",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.ApprovalRequest{
				IsAccepted:    &isAccepted,
				ID:            uuidInput,
				CurrentUserID: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("GetSignUpRequestByID", mock.Anything, uuidInput).Return(&entities.SignupRequest{
					Password: "Zz3AJ+LA0AKR/Mqv+RvtMnUXZ8Rm5Bp/KTKsv7bCj2Xj9LPH",
					Email:    "<EMAIL>",
				}, nil)
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{}, nil)
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.gitlabClient.On("GetUser", mock.Anything, mock.Anything).Return([]gitlab.CreateUserResponse{
					{
						State: gitlab.UserBlocked,
					},
				}, nil)
			},
			expError: usecase.ErrInputUserDisable,
		},
		{
			name: "should return error when repo.Delete return errors",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.ApprovalRequest{
				IsAccepted:    &isAccepted,
				ID:            uuidInput,
				CurrentUserID: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("GetSignUpRequestByID", mock.Anything, uuidInput).Return(&entities.SignupRequest{
					Password: "Zz3AJ+LA0AKR/Mqv+RvtMnUXZ8Rm5Bp/KTKsv7bCj2Xj9LPH",
					Email:    "<EMAIL>",
				}, nil)
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{}, nil)
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.gitlabClient.On("GetUser", mock.Anything, mock.Anything).Return([]gitlab.CreateUserResponse{}, nil)
				d.gitlabClient.On("CreateUser", mock.Anything, mock.Anything).Return(&gitlab.CreateUserResponse{}, nil)
				d.repo.On("FindUserGitGroup", mock.Anything, mock.Anything).Return(&entities.DefaultGitGroup{}, nil)
				d.gitlabClient.On("GetGroup", mock.Anything, mock.Anything).Return([]gitlab.GitlabGroup{}, nil)
				d.gitlabClient.On("CreateUserGroup", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(int64(1), nil)
				d.gitlabClient.On("UpdateGroup", mock.Anything, mock.Anything).Return(nil)
				d.gitlabClient.On("AddUserToGroup", mock.Anything, mock.Anything).Return(&gitlab.AddUserToGroupResponse{}, nil)
				d.repo.On("FindAuthUser", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
				d.supabaseClient.On("SignUp", mock.Anything, mock.Anything).Return(&supabase.SignupResponse{}, nil)
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), uuidInput))
				}).Return(errors.New("error"))
				d.repo.On("Create", mock.Anything, mock.Anything).Return(nil)
				d.gitlabClient.On("CreatePersonalAccessToken", mock.Anything, mock.Anything).Return(&gitlab.CreatePersonalAccessTokenResponse{ExpiredAt: expired}, nil)
				d.repo.On("Save", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("Delete", mock.Anything, mock.Anything).Return(errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return success",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.ApprovalRequest{
				IsAccepted:    &isAccepted,
				ID:            uuidInput,
				CurrentUserID: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("GetSignUpRequestByID", mock.Anything, uuidInput).Return(&entities.SignupRequest{
					Password: "Zz3AJ+LA0AKR/Mqv+RvtMnUXZ8Rm5Bp/KTKsv7bCj2Xj9LPH",
					Email:    "<EMAIL>",
				}, nil)
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{}, nil)
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.gitlabClient.On("GetUser", mock.Anything, mock.Anything).Return([]gitlab.CreateUserResponse{}, nil)
				d.gitlabClient.On("CreateUser", mock.Anything, mock.Anything).Return(&gitlab.CreateUserResponse{}, nil)
				d.repo.On("FindUserGitGroup", mock.Anything, mock.Anything).Return(&entities.DefaultGitGroup{}, nil)
				d.gitlabClient.On("GetGroup", mock.Anything, mock.Anything).Return([]gitlab.GitlabGroup{}, nil)
				d.gitlabClient.On("CreateUserGroup", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(int64(1), nil)
				d.gitlabClient.On("UpdateGroup", mock.Anything, mock.Anything).Return(nil)
				d.gitlabClient.On("AddUserToGroup", mock.Anything, mock.Anything).Return(&gitlab.AddUserToGroupResponse{}, nil)
				d.repo.On("FindAuthUser", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
				d.supabaseClient.On("SignUp", mock.Anything, mock.Anything).Return(&supabase.SignupResponse{}, nil)
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), uuidInput))
				}).Return(errors.New("error"))
				d.repo.On("Create", mock.Anything, mock.Anything).Return(nil)
				d.gitlabClient.On("CreatePersonalAccessToken", mock.Anything, mock.Anything).Return(&gitlab.CreatePersonalAccessTokenResponse{ExpiredAt: expired}, nil)
				d.repo.On("Save", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("Delete", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("CreateUserGitGroup", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return success",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.ApprovalRequest{
				IsAccepted:    &isAccepted,
				ID:            uuidInput,
				CurrentUserID: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("GetSignUpRequestByID", mock.Anything, uuidInput).Return(&entities.SignupRequest{
					Password: "Zz3AJ+LA0AKR/Mqv+RvtMnUXZ8Rm5Bp/KTKsv7bCj2Xj9LPH",
					Email:    "<EMAIL>",
				}, nil)
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{}, nil)
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.gitlabClient.On("GetUser", mock.Anything, mock.Anything).Return([]gitlab.CreateUserResponse{}, nil)
				d.gitlabClient.On("CreateUser", mock.Anything, mock.Anything).Return(&gitlab.CreateUserResponse{}, nil)
				d.repo.On("FindUserGitGroup", mock.Anything, mock.Anything).Return(&entities.DefaultGitGroup{}, nil)
				d.gitlabClient.On("GetGroup", mock.Anything, mock.Anything).Return([]gitlab.GitlabGroup{}, nil)
				d.gitlabClient.On("CreateUserGroup", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(int64(1), nil)
				d.gitlabClient.On("UpdateGroup", mock.Anything, mock.Anything).Return(nil)
				d.gitlabClient.On("AddUserToGroup", mock.Anything, mock.Anything).Return(&gitlab.AddUserToGroupResponse{}, nil)
				d.repo.On("FindAuthUser", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
				d.supabaseClient.On("SignUp", mock.Anything, mock.Anything).Return(&supabase.SignupResponse{}, nil)
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), uuidInput))
				}).Return(nil)
				d.repo.On("Create", mock.Anything, mock.Anything).Return(nil)
				d.gitlabClient.On("CreatePersonalAccessToken", mock.Anything, mock.Anything).Return(&gitlab.CreatePersonalAccessTokenResponse{ExpiredAt: expired}, nil)
				d.repo.On("Save", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("Delete", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("CreateUserGitGroup", mock.Anything, mock.Anything).Return(&entities.UserGitGroup{}, nil)
			},
			expError: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Arrange
			dep := dependencies{
				config: &configs.GlobalConfig{Gitlab: &configs.GitlabConfig{
					VolvoApiServerHost: "http://localhost",
				}},
				repo:           &repository_mocks.MockRepository{},
				gitlabClient:   &gitlab_mocks.MockGitlabClient{},
				supabaseClient: &supabase_mocks.MockSupabaseClient{},
				mailClient:     &mail_mocks.MockMailClient{},
			}

			tt.mockFn(&dep)

			// Act
			u := signup_request.New(dep.config, dep.repo, dep.gitlabClient, dep.supabaseClient, MockPasswordEncryptSecret, dep.mailClient)
			err := u.ProcessSignUpRequest(tt.ctx, tt.input)
			assert.Equal(t, tt.expError, err)
		})
	}
}

func TestInviteUser(t *testing.T) {
	uuidInput := uuid.New()
	now := time.Now()
	expired := now.Format("2006-01-02")
	type dependencies struct {
		repo           *repository_mocks.MockRepository
		gitlabClient   *gitlab_mocks.MockGitlabClient
		supabaseClient *supabase_mocks.MockSupabaseClient
	}

	tests := []struct {
		name     string
		ctx      context.Context
		input    dto.InviteUserInput
		mockFn   func(d *dependencies)
		expError error
	}{
		{
			name: "should return error when repository.FindUser returns error",
			ctx:  context.Background(),
			input: dto.InviteUserInput{
				Email: "<EMAIL>",
				Name:  "sample",
				Role:  enums.UserRole_User,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(nil, usecase.ErrUserNotFound)
			},
			expError: usecase.ErrUserNotFound,
		},
		{
			name: "should return error usecase.ErrEmailHasBeenConfirmed when email has been confirm ",
			ctx:  context.Background(),
			input: dto.InviteUserInput{
				Email: "<EMAIL>",
				Name:  "sample",
				Role:  enums.UserRole_User,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{}, nil).Once()
				d.repo.On("FindAuthUser", mock.Anything, mock.Anything).Return(&entities.AuthUser{EmailConfirmedAt: &now}, nil)
			},
			expError: usecase.ErrEmailHasBeenConfirmed,
		},
		{
			name: "should return error when gitLabClient.GetUser return error",
			ctx:  context.Background(),
			input: dto.InviteUserInput{
				Email: "<EMAIL>",
				Name:  "sample",
				Role:  enums.UserRole_User,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(nil, nil).Once()
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{}, nil).Once()
				d.repo.On("CountPrefixUsername", mock.Anything, mock.Anything).Return(0, nil)
				d.gitlabClient.On("GetUser", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error when gitLabClient.CreateUser return error",
			ctx:  context.Background(),
			input: dto.InviteUserInput{
				Email: "<EMAIL>",
				Name:  "sample",
				Role:  enums.UserRole_User,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(nil, nil).Once()
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{}, nil).Once()
				d.repo.On("CountPrefixUsername", mock.Anything, mock.Anything).Return(0, nil)
				d.gitlabClient.On("GetUser", mock.Anything, mock.Anything).Return([]gitlab.CreateUserResponse{}, nil)
				d.gitlabClient.On("CreateUser", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error when repository.FindUserGitGroup return error",
			ctx:  context.Background(),
			input: dto.InviteUserInput{
				Email: "<EMAIL>",
				Name:  "sample",
				Role:  enums.UserRole_User,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(nil, nil).Once()
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{}, nil).Once()
				d.repo.On("CountPrefixUsername", mock.Anything, mock.Anything).Return(0, nil)
				d.gitlabClient.On("GetUser", mock.Anything, mock.Anything).Return([]gitlab.CreateUserResponse{}, nil)
				d.gitlabClient.On("CreateUser", mock.Anything, mock.Anything).Return(&gitlab.CreateUserResponse{}, nil)
				d.repo.On("FindUserGitGroup", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error when repository.GetGroup return error",
			ctx:  context.Background(),
			input: dto.InviteUserInput{
				Email: "<EMAIL>",
				Name:  "sample",
				Role:  enums.UserRole_User,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(nil, nil).Once()
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{}, nil).Once()
				d.repo.On("CountPrefixUsername", mock.Anything, mock.Anything).Return(0, nil)
				d.gitlabClient.On("GetUser", mock.Anything, mock.Anything).Return([]gitlab.CreateUserResponse{}, nil)
				d.gitlabClient.On("CreateUser", mock.Anything, mock.Anything).Return(&gitlab.CreateUserResponse{}, nil)
				d.repo.On("FindUserGitGroup", mock.Anything, mock.Anything).Return(&entities.DefaultGitGroup{}, nil)
				d.gitlabClient.On("GetGroup", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error when gitlabClient.AddUserToGroup returns error",
			ctx:  context.Background(),
			input: dto.InviteUserInput{
				Email: "<EMAIL>",
				Name:  "sample",
				Role:  enums.UserRole_User,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(nil, nil).Once()
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{}, nil).Once()
				d.repo.On("CountPrefixUsername", mock.Anything, mock.Anything).Return(0, nil)
				d.gitlabClient.On("GetUser", mock.Anything, mock.Anything).Return([]gitlab.CreateUserResponse{}, nil)
				d.gitlabClient.On("CreateUser", mock.Anything, mock.Anything).Return(&gitlab.CreateUserResponse{}, nil)
				d.repo.On("FindUserGitGroup", mock.Anything, mock.Anything).Return(&entities.DefaultGitGroup{}, nil)
				d.gitlabClient.On("GetGroup", mock.Anything, mock.Anything).Return([]gitlab.GitlabGroup{}, nil)
				d.gitlabClient.On("CreateGroup", mock.Anything, mock.Anything).Return(&gitlab.GitlabGroup{}, nil)
				d.gitlabClient.On("UpdateGroup", mock.Anything, mock.Anything).Return(nil)
				d.gitlabClient.On("AddUserToGroup", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error when supabase.InviteUser returns error",
			ctx:  context.Background(),
			input: dto.InviteUserInput{
				Email: "<EMAIL>",
				Name:  "sample",
				Role:  enums.UserRole_User,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(nil, nil).Once()
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{}, nil).Once()
				d.repo.On("CountPrefixUsername", mock.Anything, mock.Anything).Return(0, nil)
				d.gitlabClient.On("GetUser", mock.Anything, mock.Anything).Return([]gitlab.CreateUserResponse{}, nil)
				d.gitlabClient.On("CreateUser", mock.Anything, mock.Anything).Return(&gitlab.CreateUserResponse{}, nil)
				d.repo.On("FindUserGitGroup", mock.Anything, mock.Anything).Return(&entities.DefaultGitGroup{}, nil)
				d.gitlabClient.On("GetGroup", mock.Anything, mock.Anything).Return([]gitlab.GitlabGroup{}, nil)
				d.gitlabClient.On("CreateGroup", mock.Anything, mock.Anything).Return(&gitlab.GitlabGroup{}, nil)
				d.gitlabClient.On("UpdateGroup", mock.Anything, mock.Anything).Return(nil)
				d.gitlabClient.On("AddUserToGroup", mock.Anything, mock.Anything).Return(&gitlab.AddUserToGroupResponse{}, nil)
				d.gitlabClient.On("CreatePersonalAccessToken", mock.Anything, mock.Anything).Return(&gitlab.CreatePersonalAccessTokenResponse{ExpiredAt: expired}, nil)
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), uuidInput))
				}).Return(errors.New("error"))
				d.repo.On("FindAuthUser", mock.Anything, mock.Anything).Return(&entities.AuthUser{}, nil)
				d.supabaseClient.On("InviteUser", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error when input user is disable because is deleting progress",
			ctx:  context.Background(),
			input: dto.InviteUserInput{
				Email: "<EMAIL>",
				Name:  "sample",
				Role:  enums.UserRole_User,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(nil, nil).Once()
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{}, nil).Once()
				d.repo.On("CountPrefixUsername", mock.Anything, mock.Anything).Return(0, nil)
				d.gitlabClient.On("GetUser", mock.Anything, mock.Anything).Return([]gitlab.CreateUserResponse{
					{
						State: gitlab.UserBlocked,
					},
				}, nil)
			},
			expError: usecase.ErrInputUserDisable,
		},
		{
			name: "success",
			ctx:  context.Background(),
			input: dto.InviteUserInput{
				Email: "<EMAIL>",
				Name:  "sample",
				Role:  enums.UserRole_User,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(nil, nil).Once()
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{}, nil).Once()
				d.repo.On("CountPrefixUsername", mock.Anything, mock.Anything).Return(0, nil)
				d.gitlabClient.On("GetUser", mock.Anything, mock.Anything).Return([]gitlab.CreateUserResponse{}, nil)
				d.gitlabClient.On("CreateUser", mock.Anything, mock.Anything).Return(&gitlab.CreateUserResponse{}, nil)
				d.repo.On("FindUserGitGroup", mock.Anything, mock.Anything).Return(&entities.DefaultGitGroup{}, nil)
				d.gitlabClient.On("GetGroup", mock.Anything, mock.Anything).Return([]gitlab.GitlabGroup{}, nil)
				d.gitlabClient.On("CreateGroup", mock.Anything, mock.Anything).Return(&gitlab.GitlabGroup{}, nil)
				d.gitlabClient.On("UpdateGroup", mock.Anything, mock.Anything).Return(nil)
				d.gitlabClient.On("AddUserToGroup", mock.Anything, mock.Anything).Return(&gitlab.AddUserToGroupResponse{}, nil)
				d.gitlabClient.On("CreatePersonalAccessToken", mock.Anything, mock.Anything).Return(&gitlab.CreatePersonalAccessTokenResponse{ExpiredAt: expired}, nil)
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), uuidInput))
				}).Return(nil)
				d.repo.On("FindAuthUser", mock.Anything, mock.Anything).Return(&entities.AuthUser{}, nil)
				d.supabaseClient.On("InviteUser", mock.Anything, mock.Anything).Return(&supabase.InviteUserResponse{}, nil)
				d.repo.On("Create", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("CreateUserGitGroup", mock.Anything, mock.Anything).Return(&entities.UserGitGroup{}, nil)
			},
			expError: nil,
		},
		{
			name: "success when username is duplicate",
			ctx:  context.Background(),
			input: dto.InviteUserInput{
				Email: "<EMAIL>",
				Name:  "sample",
				Role:  enums.UserRole_User,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(nil, nil).Once()
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{}, nil).Once()
				d.repo.On("CountPrefixUsername", mock.Anything, mock.Anything).Return(1, nil).Once()
				d.repo.On("CountPrefixUsername", mock.Anything, mock.Anything).Return(0, nil).Once()
				d.gitlabClient.On("GetUser", mock.Anything, mock.Anything).Return([]gitlab.CreateUserResponse{}, nil)
				d.gitlabClient.On("CreateUser", mock.Anything, mock.Anything).Return(&gitlab.CreateUserResponse{}, nil)
				d.repo.On("FindUserGitGroup", mock.Anything, mock.Anything).Return(&entities.DefaultGitGroup{}, nil)
				d.gitlabClient.On("GetGroup", mock.Anything, mock.Anything).Return([]gitlab.GitlabGroup{}, nil)
				d.gitlabClient.On("CreateGroup", mock.Anything, mock.Anything).Return(&gitlab.GitlabGroup{}, nil)
				d.gitlabClient.On("UpdateGroup", mock.Anything, mock.Anything).Return(nil)
				d.gitlabClient.On("AddUserToGroup", mock.Anything, mock.Anything).Return(&gitlab.AddUserToGroupResponse{}, nil)
				d.gitlabClient.On("CreatePersonalAccessToken", mock.Anything, mock.Anything).Return(&gitlab.CreatePersonalAccessTokenResponse{ExpiredAt: expired}, nil)
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), uuidInput))
				}).Return(nil)
				d.repo.On("FindAuthUser", mock.Anything, mock.Anything).Return(&entities.AuthUser{}, nil)
				d.supabaseClient.On("InviteUser", mock.Anything, mock.Anything).Return(&supabase.InviteUserResponse{}, nil)
				d.repo.On("Create", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("CreateUserGitGroup", mock.Anything, mock.Anything).Return(&entities.UserGitGroup{}, nil)
			},
			expError: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Arrange
			dep := dependencies{
				repo:           &repository_mocks.MockRepository{},
				gitlabClient:   &gitlab_mocks.MockGitlabClient{},
				supabaseClient: &supabase_mocks.MockSupabaseClient{},
			}

			tt.mockFn(&dep)

			// Act
			u := signup_request.New(nil, dep.repo, dep.gitlabClient, dep.supabaseClient, MockPasswordEncryptSecret, nil)
			err := u.InviteUser(tt.ctx, tt.input)
			assert.Equal(t, tt.expError, err)
		})
	}

}

func TestSendConfirmEmail(t *testing.T) {
	type dependencies struct {
		repo           *repository_mocks.MockRepository
		supabaseClient *supabase_mocks.MockSupabaseClient
	}

	tests := []struct {
		name     string
		ctx      context.Context
		input    dto.SendConfirmEmailInput
		mockFn   func(d *dependencies)
		expError error
	}{

		{
			name: "should return error when repository.FindAuthUser returns error",
			ctx:  context.Background(),
			input: dto.SendConfirmEmailInput{
				Email: "<EMAIL>",
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindAuthUser", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error when supabaseClient.SignUp returns error",
			ctx:  context.Background(),
			input: dto.SendConfirmEmailInput{
				Email: "<EMAIL>",
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindAuthUser", mock.Anything, mock.Anything).Return(&entities.AuthUser{EncryptedPassword: "1"}, nil).Once()
				d.supabaseClient.On("SignUp", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return success when resend signup email",
			ctx:  context.Background(),
			input: dto.SendConfirmEmailInput{
				Email: "<EMAIL>",
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindAuthUser", mock.Anything, mock.Anything).Return(&entities.AuthUser{EncryptedPassword: "1"}, nil).Once()
				d.supabaseClient.On("SignUp", mock.Anything, mock.Anything).Return(&supabase.SignupResponse{}, nil)
			},
			expError: nil,
		},
		{
			name: "should return error when supabaseClient.InviteUser returns error",
			ctx:  context.Background(),
			input: dto.SendConfirmEmailInput{
				Email: "<EMAIL>",
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindAuthUser", mock.Anything, mock.Anything).Return(&entities.AuthUser{EncryptedPassword: ""}, nil).Once()
				d.supabaseClient.On("InviteUser", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return success when resend invite user email",
			ctx:  context.Background(),
			input: dto.SendConfirmEmailInput{
				Email: "<EMAIL>",
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindAuthUser", mock.Anything, mock.Anything).Return(&entities.AuthUser{EncryptedPassword: ""}, nil).Once()
				d.supabaseClient.On("InviteUser", mock.Anything, mock.Anything).Return(&supabase.InviteUserResponse{}, nil)
			},
			expError: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Arrange
			dep := dependencies{
				repo:           &repository_mocks.MockRepository{},
				supabaseClient: &supabase_mocks.MockSupabaseClient{},
			}

			tt.mockFn(&dep)

			// Act
			u := signup_request.New(nil, dep.repo, nil, dep.supabaseClient, MockPasswordEncryptSecret, nil)
			err := u.SendConfirmEmail(tt.ctx, tt.input)
			assert.Equal(t, tt.expError, err)
		})
	}

}

func TestGetAllSignUpRequests(t *testing.T) {
	type dependencies struct {
		repo           *repository_mocks.MockRepository
		supabaseClient *supabase_mocks.MockSupabaseClient
	}

	tests := []struct {
		name     string
		ctx      context.Context
		mockFn   func(d *dependencies)
		expError error
	}{

		{
			name: "should return error when repository.ListSignUpRequests returns error",
			ctx:  context.Background(),

			mockFn: func(d *dependencies) {
				d.repo.On("ListSignUpRequests", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return success",
			ctx:  context.Background(),
			mockFn: func(d *dependencies) {
				d.repo.On("ListSignUpRequests", mock.Anything, mock.Anything).Return([]entities.SignupRequest{}, nil)
			},
			expError: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Arrange
			dep := dependencies{
				repo: &repository_mocks.MockRepository{},
			}

			tt.mockFn(&dep)

			// Act
			u := signup_request.New(nil, dep.repo, nil, dep.supabaseClient, MockPasswordEncryptSecret, nil)
			_, err := u.GetAllSignUpRequests(tt.ctx)
			assert.Equal(t, tt.expError, err)
		})
	}

}

func TestRefreshAccessToken(t *testing.T) {
	type dependencies struct {
		repo           *repository_mocks.MockRepository
		supabaseClient *supabase_mocks.MockSupabaseClient
		gitlabClient   *gitlab_mocks.MockGitlabClient
	}

	userId := int64(1)
	tests := []struct {
		name     string
		ctx      context.Context
		mockFn   func(d *dependencies)
		expError error
	}{

		{
			name: "should return error when repository.FindUser returns error",
			ctx:  context.Background(),

			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error when repository.FindUser returns not found",
			ctx:  context.Background(),

			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expError: usecase.ErrUserNotFound,
		},
		{
			name: "should return error when gitlabClient.CreatePersonalAccessToken returns error",
			ctx:  context.Background(),

			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{GitlabAccessToken: utils.Ptr("token")}, nil)
				d.gitlabClient.On("CreatePersonalAccessToken", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error when gitlabClient.DeletePersonalAccessToken returns error",
			ctx:  context.Background(),

			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.gitlabClient.On("CreatePersonalAccessToken", mock.Anything, mock.Anything).Return(&gitlab.CreatePersonalAccessTokenResponse{}, nil)
				d.gitlabClient.On("DeletePersonalAccessToken", mock.Anything, mock.Anything).Return(errors.New("error"))

			},
			expError: errors.New("error"),
		},
		{
			name: "should return repository.Save returns error",
			ctx:  context.Background(),
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.gitlabClient.On("CreatePersonalAccessToken", mock.Anything, mock.Anything).Return(&gitlab.CreatePersonalAccessTokenResponse{}, nil)
				d.gitlabClient.On("DeletePersonalAccessToken", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("Save", mock.Anything, mock.Anything).Return(errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return success",
			ctx:  context.Background(),
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.gitlabClient.On("CreatePersonalAccessToken", mock.Anything, mock.Anything).Return(&gitlab.CreatePersonalAccessTokenResponse{}, nil)
				d.gitlabClient.On("DeletePersonalAccessToken", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("Save", mock.Anything, mock.Anything).Return(nil)
			},
			expError: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Arrange
			dep := dependencies{
				repo:           &repository_mocks.MockRepository{},
				supabaseClient: &supabase_mocks.MockSupabaseClient{},
				gitlabClient:   &gitlab_mocks.MockGitlabClient{},
			}

			tt.mockFn(&dep)

			// Act
			u := signup_request.New(nil, dep.repo, dep.gitlabClient, dep.supabaseClient, MockPasswordEncryptSecret, nil)
			err := u.RefreshAccessToken(tt.ctx, userId)
			assert.Equal(t, tt.expError, err)
		})
	}

}
