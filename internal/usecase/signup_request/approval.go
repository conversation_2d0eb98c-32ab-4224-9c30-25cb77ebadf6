package signup_request

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/google/uuid"
	"go.opentelemetry.io/otel/codes"
	"gorm.io/gorm"

	"api-server/internal/dto"
	"api-server/internal/entities"
	"api-server/internal/enums"
	"api-server/internal/gateways/gitlab"
	"api-server/internal/gateways/mail"
	supabase "api-server/internal/gateways/supabase"
	repository "api-server/internal/repositories"
	"api-server/internal/usecase"
	"api-server/internal/utils"
	"api-server/pkg/oteltrace"
)

// ProcessSignUpRequest implements the SignupRequestUsecase interface for processing a signup request.
// It handles both approval and rejection of signup requests, including user creation in GitLab and Supabase,
// group creation, and sending appropriate email notifications.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: ApprovalRequest containing the request ID, approval status, and current user ID
//
// Returns:
//   - error: Any error that occurred during request processing
func (i *impl) ProcessSignUpRequest(ctx context.Context, input dto.ApprovalRequest) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.signup_request.ProcessSignUpRequest")
	defer span.End()

	signupRequest, err := i.repository.GetSignUpRequestByID(ctx, input.ID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			span.SetStatus(codes.Error, "signup request not found")
			span.RecordError(err)
			return usecase.ErrSignUpRequestNotFound
		}
		span.SetStatus(codes.Error, "failed to get signup request by ID")
		span.RecordError(err)
		return err
	}

	if input.IsAccepted != nil && !(*input.IsAccepted) {
		if err := i.deleteSignUpRequest(ctx, *signupRequest, input.ID); err != nil {
			span.SetStatus(codes.Error, "failed to delete signup request")
			span.RecordError(err)
			return err
		}

		// sent rejected email
		if err := i.mailClient.SendMail(ctx, mail.SendEmailInput{
			TemplateName: "reject_user.html",
			Title:        "Email Rejection Notification",
			UserEmails:   []string{signupRequest.Email},
			Payload: map[string]interface{}{
				"Email":        signupRequest.Email,
				"PlatformName": i.config.PlatformName,
				"SupportEmail": i.config.SupportEmail,
			},
		}); err != nil {
			span.SetStatus(codes.Error, usecase.ErrSendMail.Error())
			span.RecordError(err)
			return usecase.ErrSendMail
		}

		span.AddEvent("signup request rejected and deleted successfully")
		span.SetStatus(codes.Ok, "signup request rejected and deleted successfully")
		return nil
	}

	password, err := utils.Decrypt(signupRequest.Password, i.pwdEncryptSecret)
	if err != nil {
		span.SetStatus(codes.Error, "failed to decrypt password")
		span.RecordError(err)
		return err
	}

	// user should be administrator role
	_, err = i.repository.FindUserByID(ctx, input.CurrentUserID)
	if err != nil {
		span.SetStatus(codes.Error, "failed to find user by ID")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return usecase.ErrUserNotFound
		}
		return err
	}
	//get root user
	adminRefGitID := enums.AdminRefGitID
	adminUser, err := i.repository.FindUser(ctx, repository.FindUserFilter{RefGitUserID: &adminRefGitID})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find admin user")
		span.RecordError(err)
		return err
	}

	gitlabAdminAccessToken := ""
	if adminUser.GitlabAccessToken != nil {
		gitlabAdminAccessToken = *adminUser.GitlabAccessToken
	}

	gitlabUser, err := createGitlabUser(ctx, i, signupRequest.Username, dto.InviteUserInput{
		Name:  signupRequest.Name,
		Email: signupRequest.Email,
	}, gitlabAdminAccessToken)
	if err != nil {
		return err
	}

	gitGroups, err := createGitlabGroup(ctx, i, signupRequest.Username, gitlabUser.Id, adminUser.ID, gitlabAdminAccessToken)
	if err != nil {
		span.SetStatus(codes.Error, "failed to create gitlab group")
		span.RecordError(err)
		return err
	}
	// Create user in Supabase
	var userId uuid.UUID
	// if supabase user exists then find user in auth.users table
	authUser, err := i.repository.FindAuthUser(ctx, dto.AuthUserFilter{
		Email: &signupRequest.Email,
	})
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}
	// supabase's user exist and email has been confirmed
	if authUser != nil && authUser.EmailConfirmedAt != nil {
		userId = authUser.ID
	} else if authUser != nil && authUser.EmailConfirmedAt == nil {
		// supabase's user exist and email has not been confirmed
		userResp, err := i.supabaseClient.InviteUser(ctx, supabase.InviteUserRequest{
			Email: signupRequest.Email,
		})
		if err != nil {
			span.SetStatus(codes.Error, "failed to generate username")
			span.RecordError(err)
			return err
		}

		userId = userResp.ID
	} else { //supabase user not exist
		userResp, err := i.supabaseClient.SignUp(ctx, supabase.SignupRequest{
			Email:    signupRequest.Email,
			Password: password,
		})
		if err != nil {
			span.SetStatus(codes.Error, "failed to sign up user in supabase")
			span.RecordError(err)
			return err
		}

		userId = userResp.ID
	}

	err = i.repository.Transaction(ctx, func(ctx context.Context) error {
		user := &entities.User{
			BaseModel: entities.BaseModel{
				ID: userId,
			},
			Name:         signupRequest.Name,
			Username:     signupRequest.Username,
			Role:         enums.UserRole_User,
			RefGitUserID: gitlabUser.Id,
		}

		// Create user in DB
		if err := i.repository.Create(ctx, user); err != nil {
			span.SetStatus(codes.Error, "failed to create user in db")
			span.RecordError(err)
			return err
		}

		// Create personal access token
		userAccessToken, err := i.gitLabClient.CreatePersonalAccessToken(ctx, gitlab.CreatePersonalAccessTokenRequest{
			UserId:     user.RefGitUserID,
			Name:       "volvo-user-auth", // hardcode, as there should only be one instance
			AdminToken: gitlabAdminAccessToken,
			Scopes:     []gitlab.TokenScope{gitlab.ScopeAPI},
		})
		if err != nil {
			span.SetStatus(codes.Error, "failed to create personal access token in gitlab")
			span.RecordError(err)
			return err
		}

		expiredAt, err := utils.ConvertStrToTime(userAccessToken.ExpiredAt, time.DateOnly)
		if err != nil {
			span.SetStatus(codes.Error, "failed to convert expire time")
			span.RecordError(err)
			return err
		}
		user.GitlabAccessToken = &userAccessToken.Token
		user.GitlabAccessTokenExpiresAt = &expiredAt

		if err := i.repository.Save(ctx, user); err != nil {
			span.SetStatus(codes.Error, "failed to save user in db")
			span.RecordError(err)
			return err
		}

		if err := i.repository.Delete(ctx, &signupRequest); err != nil {
			span.SetStatus(codes.Error, "failed to delete sign up request")
			span.RecordError(err)
			return err
		}

		createUserGroupInput := repository.CreateUserGitGroupInput{
			UserID:           userId,
			RefGitSpacesID:   gitGroups[fmt.Sprintf("%s/%s", enums.RepoType_Spaces, signupRequest.Username)].UserGitGroupId,
			RefGitDatasetsID: gitGroups[fmt.Sprintf("%s/%s", enums.RepoType_Datasets, signupRequest.Username)].UserGitGroupId,
			RefGitModelsID:   gitGroups[fmt.Sprintf("%s/%s", enums.RepoType_Models, signupRequest.Username)].UserGitGroupId,
			// RefGitComposesID: &gitGroups[fmt.Sprintf("%s/%s", enums.RepoType_Composes, signupRequest.Username)].UserGitGroupId,
		}
		if val, ok := gitGroups[fmt.Sprintf("%s/%s", enums.RepoType_Composes, signupRequest.Username)]; ok {
			createUserGroupInput.RefGitComposesID = &val.UserGitGroupId
		}
		_, err = i.repository.CreateUserGitGroup(ctx, createUserGroupInput)
		if err != nil {
			span.SetStatus(codes.Error, "failed to create user git group")
			span.RecordError(err)
			return err
		}
		return nil
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to process signup request")
		span.RecordError(err)
		return err
	}

	span.AddEvent("signup request processed and user created successfully")
	span.SetStatus(codes.Ok, "signup request processed and user created successfully")
	return nil
}

// deleteSignUpRequest deletes a signup request from the database.
// It handles the deletion within a transaction to ensure data consistency.
//
// Parameters:
//   - ctx: Context for the operation
//   - signupRequest: The signup request entity to delete
//   - id: UUID of the signup request to delete
//
// Returns:
//   - error: Any error that occurred during deletion
func (i *impl) deleteSignUpRequest(ctx context.Context, signupRequest entities.SignupRequest, id uuid.UUID) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.signup_request.deleteSignUpRequest")
	defer span.End()

	err := i.repository.Transaction(ctx, func(ctx context.Context) error {
		err := i.repository.DeleteById(ctx, signupRequest, id)
		if err != nil {
			span.SetStatus(codes.Error, "failed to delete signup request from db")
			span.RecordError(err)
			return err
		}
		return nil
	})

	if err != nil {
		return err
	}

	span.AddEvent("signup request deleted successfully")
	span.SetStatus(codes.Ok, "signup request deleted successfully")
	return nil
}
