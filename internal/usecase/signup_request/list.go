package signup_request

import (
	"context"

	"go.opentelemetry.io/otel/codes"

	"api-server/internal/dto"
	"api-server/internal/entities"
	"api-server/pkg/oteltrace"
)

// GetAllSignUpRequests implements the SignupRequestUsecase interface for retrieving all signup requests.
// It fetches all pending signup requests from the repository and converts them to DTO format.
//
// Parameters:
//   - ctx: Context for the operation
//
// Returns:
//   - dto.GetAllSignUpRequestsOutput: Output containing the list of signup requests
//   - error: Any error that occurred during retrieval
func (i *impl) GetAllSignUpRequests(ctx context.Context) (dto.GetAllSignUpRequestsOutput, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.signup_request.GetAllSignUpRequests")
	defer span.End()

	signUpRequests, err := i.repository.ListSignUpRequests(ctx)
	if err != nil {
		span.SetStatus(codes.Error, "failed to list signup requests")
		span.RecordError(err)
		return dto.GetAllSignUpRequestsOutput{}, err
	}

	data := dto.FromManyEntities[entities.SignupRequest, dto.SignUpRequest](signUpRequests)

	span.AddEvent("successfully list all signup requests")
	span.SetStatus(codes.Ok, "successfully list all signup requests")
	return dto.GetAllSignUpRequestsOutput{
		Data: &data,
	}, nil
}
