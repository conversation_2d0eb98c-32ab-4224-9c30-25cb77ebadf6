// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	dto "api-server/internal/dto"
	context "context"

	mock "github.com/stretchr/testify/mock"

	uuid "github.com/google/uuid"
)

// MockAccessTokenUsecase is an autogenerated mock type for the AccessTokenUsecase type
type MockAccessTokenUsecase struct {
	mock.Mock
}

type MockAccessTokenUsecase_Expecter struct {
	mock *mock.Mock
}

func (_m *MockAccessTokenUsecase) EXPECT() *MockAccessTokenUsecase_Expecter {
	return &MockAccessTokenUsecase_Expecter{mock: &_m.Mock}
}

// CreateAccessToken provides a mock function with given fields: ctx, req
func (_m *MockAccessTokenUsecase) CreateAccessToken(ctx context.Context, req dto.CreateAccessTokenRequest) (*dto.CreateAccessTokenResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CreateAccessToken")
	}

	var r0 *dto.CreateAccessTokenResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.CreateAccessTokenRequest) (*dto.CreateAccessTokenResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, dto.CreateAccessTokenRequest) *dto.CreateAccessTokenResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*dto.CreateAccessTokenResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, dto.CreateAccessTokenRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockAccessTokenUsecase_CreateAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateAccessToken'
type MockAccessTokenUsecase_CreateAccessToken_Call struct {
	*mock.Call
}

// CreateAccessToken is a helper method to define mock.On call
//   - ctx context.Context
//   - req dto.CreateAccessTokenRequest
func (_e *MockAccessTokenUsecase_Expecter) CreateAccessToken(ctx interface{}, req interface{}) *MockAccessTokenUsecase_CreateAccessToken_Call {
	return &MockAccessTokenUsecase_CreateAccessToken_Call{Call: _e.mock.On("CreateAccessToken", ctx, req)}
}

func (_c *MockAccessTokenUsecase_CreateAccessToken_Call) Run(run func(ctx context.Context, req dto.CreateAccessTokenRequest)) *MockAccessTokenUsecase_CreateAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.CreateAccessTokenRequest))
	})
	return _c
}

func (_c *MockAccessTokenUsecase_CreateAccessToken_Call) Return(_a0 *dto.CreateAccessTokenResponse, _a1 error) *MockAccessTokenUsecase_CreateAccessToken_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockAccessTokenUsecase_CreateAccessToken_Call) RunAndReturn(run func(context.Context, dto.CreateAccessTokenRequest) (*dto.CreateAccessTokenResponse, error)) *MockAccessTokenUsecase_CreateAccessToken_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteAccessToken provides a mock function with given fields: ctx, userID, accessTokenID
func (_m *MockAccessTokenUsecase) DeleteAccessToken(ctx context.Context, userID uuid.UUID, accessTokenID uuid.UUID) error {
	ret := _m.Called(ctx, userID, accessTokenID)

	if len(ret) == 0 {
		panic("no return value specified for DeleteAccessToken")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID, uuid.UUID) error); ok {
		r0 = rf(ctx, userID, accessTokenID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockAccessTokenUsecase_DeleteAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteAccessToken'
type MockAccessTokenUsecase_DeleteAccessToken_Call struct {
	*mock.Call
}

// DeleteAccessToken is a helper method to define mock.On call
//   - ctx context.Context
//   - userID uuid.UUID
//   - accessTokenID uuid.UUID
func (_e *MockAccessTokenUsecase_Expecter) DeleteAccessToken(ctx interface{}, userID interface{}, accessTokenID interface{}) *MockAccessTokenUsecase_DeleteAccessToken_Call {
	return &MockAccessTokenUsecase_DeleteAccessToken_Call{Call: _e.mock.On("DeleteAccessToken", ctx, userID, accessTokenID)}
}

func (_c *MockAccessTokenUsecase_DeleteAccessToken_Call) Run(run func(ctx context.Context, userID uuid.UUID, accessTokenID uuid.UUID)) *MockAccessTokenUsecase_DeleteAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID), args[2].(uuid.UUID))
	})
	return _c
}

func (_c *MockAccessTokenUsecase_DeleteAccessToken_Call) Return(_a0 error) *MockAccessTokenUsecase_DeleteAccessToken_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockAccessTokenUsecase_DeleteAccessToken_Call) RunAndReturn(run func(context.Context, uuid.UUID, uuid.UUID) error) *MockAccessTokenUsecase_DeleteAccessToken_Call {
	_c.Call.Return(run)
	return _c
}

// GetAccessToken provides a mock function with given fields: ctx, accessToken
func (_m *MockAccessTokenUsecase) GetAccessToken(ctx context.Context, accessToken string) (*dto.AccessToken, error) {
	ret := _m.Called(ctx, accessToken)

	if len(ret) == 0 {
		panic("no return value specified for GetAccessToken")
	}

	var r0 *dto.AccessToken
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*dto.AccessToken, error)); ok {
		return rf(ctx, accessToken)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *dto.AccessToken); ok {
		r0 = rf(ctx, accessToken)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*dto.AccessToken)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, accessToken)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockAccessTokenUsecase_GetAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAccessToken'
type MockAccessTokenUsecase_GetAccessToken_Call struct {
	*mock.Call
}

// GetAccessToken is a helper method to define mock.On call
//   - ctx context.Context
//   - accessToken string
func (_e *MockAccessTokenUsecase_Expecter) GetAccessToken(ctx interface{}, accessToken interface{}) *MockAccessTokenUsecase_GetAccessToken_Call {
	return &MockAccessTokenUsecase_GetAccessToken_Call{Call: _e.mock.On("GetAccessToken", ctx, accessToken)}
}

func (_c *MockAccessTokenUsecase_GetAccessToken_Call) Run(run func(ctx context.Context, accessToken string)) *MockAccessTokenUsecase_GetAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockAccessTokenUsecase_GetAccessToken_Call) Return(_a0 *dto.AccessToken, _a1 error) *MockAccessTokenUsecase_GetAccessToken_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockAccessTokenUsecase_GetAccessToken_Call) RunAndReturn(run func(context.Context, string) (*dto.AccessToken, error)) *MockAccessTokenUsecase_GetAccessToken_Call {
	_c.Call.Return(run)
	return _c
}

// ListAccessToken provides a mock function with given fields: ctx, req
func (_m *MockAccessTokenUsecase) ListAccessToken(ctx context.Context, req dto.ListAccessTokenRequest) (*dto.ListAccessTokenResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for ListAccessToken")
	}

	var r0 *dto.ListAccessTokenResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.ListAccessTokenRequest) (*dto.ListAccessTokenResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, dto.ListAccessTokenRequest) *dto.ListAccessTokenResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*dto.ListAccessTokenResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, dto.ListAccessTokenRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockAccessTokenUsecase_ListAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListAccessToken'
type MockAccessTokenUsecase_ListAccessToken_Call struct {
	*mock.Call
}

// ListAccessToken is a helper method to define mock.On call
//   - ctx context.Context
//   - req dto.ListAccessTokenRequest
func (_e *MockAccessTokenUsecase_Expecter) ListAccessToken(ctx interface{}, req interface{}) *MockAccessTokenUsecase_ListAccessToken_Call {
	return &MockAccessTokenUsecase_ListAccessToken_Call{Call: _e.mock.On("ListAccessToken", ctx, req)}
}

func (_c *MockAccessTokenUsecase_ListAccessToken_Call) Run(run func(ctx context.Context, req dto.ListAccessTokenRequest)) *MockAccessTokenUsecase_ListAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.ListAccessTokenRequest))
	})
	return _c
}

func (_c *MockAccessTokenUsecase_ListAccessToken_Call) Return(_a0 *dto.ListAccessTokenResponse, _a1 error) *MockAccessTokenUsecase_ListAccessToken_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockAccessTokenUsecase_ListAccessToken_Call) RunAndReturn(run func(context.Context, dto.ListAccessTokenRequest) (*dto.ListAccessTokenResponse, error)) *MockAccessTokenUsecase_ListAccessToken_Call {
	_c.Call.Return(run)
	return _c
}

// VerifyAccessToken provides a mock function with given fields: ctx, token
func (_m *MockAccessTokenUsecase) VerifyAccessToken(ctx context.Context, token string) (*dto.VerifyAccessTokenResponse, error) {
	ret := _m.Called(ctx, token)

	if len(ret) == 0 {
		panic("no return value specified for VerifyAccessToken")
	}

	var r0 *dto.VerifyAccessTokenResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*dto.VerifyAccessTokenResponse, error)); ok {
		return rf(ctx, token)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *dto.VerifyAccessTokenResponse); ok {
		r0 = rf(ctx, token)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*dto.VerifyAccessTokenResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, token)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockAccessTokenUsecase_VerifyAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'VerifyAccessToken'
type MockAccessTokenUsecase_VerifyAccessToken_Call struct {
	*mock.Call
}

// VerifyAccessToken is a helper method to define mock.On call
//   - ctx context.Context
//   - token string
func (_e *MockAccessTokenUsecase_Expecter) VerifyAccessToken(ctx interface{}, token interface{}) *MockAccessTokenUsecase_VerifyAccessToken_Call {
	return &MockAccessTokenUsecase_VerifyAccessToken_Call{Call: _e.mock.On("VerifyAccessToken", ctx, token)}
}

func (_c *MockAccessTokenUsecase_VerifyAccessToken_Call) Run(run func(ctx context.Context, token string)) *MockAccessTokenUsecase_VerifyAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockAccessTokenUsecase_VerifyAccessToken_Call) Return(_a0 *dto.VerifyAccessTokenResponse, _a1 error) *MockAccessTokenUsecase_VerifyAccessToken_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockAccessTokenUsecase_VerifyAccessToken_Call) RunAndReturn(run func(context.Context, string) (*dto.VerifyAccessTokenResponse, error)) *MockAccessTokenUsecase_VerifyAccessToken_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockAccessTokenUsecase creates a new instance of MockAccessTokenUsecase. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockAccessTokenUsecase(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockAccessTokenUsecase {
	mock := &MockAccessTokenUsecase{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
