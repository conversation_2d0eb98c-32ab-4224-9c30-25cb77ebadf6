package access_token

import (
	"context"
	"time"

	"github.com/google/uuid"
	gonanoid "github.com/matoous/go-nanoid/v2"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"

	"api-server/internal/dto"
	"api-server/internal/entities"
	repository "api-server/internal/repositories"
	"api-server/internal/types"
	"api-server/internal/usecase"
	"api-server/pkg/oteltrace"
	"api-server/pkg/otelzap"
)

const NANOID_KEY_LENGTH = 36

// AccessTokenUsecase defines the interface for managing user access tokens.
// It provides methods for creating, listing, verifying, and managing user access tokens.
type AccessTokenUsecase interface {
	// CreateAccessToken creates a new access token for a user.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - req: The request data for creating the token
	//
	// Returns:
	//   - *dto.CreateAccessTokenResponse: The created token information
	//   - error: Any error that occurred during creation
	CreateAccessToken(ctx context.Context, req dto.CreateAccessTokenRequest) (*dto.CreateAccessTokenResponse, error)

	// ListAccessToken retrieves a paginated list of access tokens for a user.
	// It includes total count and supports sorting and filtering.
	// The function includes OpenTelemetry tracing for monitoring the operation.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - req: Request containing pagination, sorting, and user ID parameters
	//
	// Returns:
	//   - *dto.ListAccessTokenResponse: Paginated list of access tokens
	//   - error: Any error that occurred during retrieval
	ListAccessToken(ctx context.Context, req dto.ListAccessTokenRequest) (*dto.ListAccessTokenResponse, error)

	// GetAccessToken retrieves information about a specific access token.
	// It verifies the token's existence and validity.
	// The function includes OpenTelemetry tracing for monitoring the operation.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - accessToken: The access token to retrieve
	//
	// Returns:
	//   - *dto.AccessToken: The token information
	//   - error: Any error that occurred during retrieval
	GetAccessToken(ctx context.Context, accessToken string) (*dto.AccessToken, error)

	// DeleteAccessToken deletes a user's access token.
	// It verifies the token ownership before deletion.
	// The function includes OpenTelemetry tracing for monitoring the operation.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - userID: The ID of the user
	//   - accessTokenID: The ID of the token to delete
	//
	// Returns:
	//   - error: Any error that occurred during deletion
	DeleteAccessToken(ctx context.Context, userID uuid.UUID, accessTokenID uuid.UUID) error

	// VerifyAccessToken verifies the validity of an access token.
	// It checks if the token exists, is not revoked, and has not expired.
	// The function includes OpenTelemetry tracing for monitoring the operation.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//   - token: The token to verify
	//
	// Returns:
	//   - *dto.VerifyAccessTokenResponse: The verification result
	//   - error: Any error that occurred during verification
	VerifyAccessToken(ctx context.Context, token string) (*dto.VerifyAccessTokenResponse, error)
}

type impl struct {
	repository repository.AccessTokenRepository
}

var _ AccessTokenUsecase = (*impl)(nil)

// New creates a new instance of the access token usecase implementation.
// It initializes the usecase with the required repository dependency.
//
// Parameters:
//   - repository: Repository interface for access token data access
//
// Returns:
//   - *impl: New instance of the access token usecase
func New(repository repository.AccessTokenRepository) *impl {
	return &impl{
		repository: repository,
	}
}

// ListAccessToken retrieves a paginated list of access tokens for a user.
// It includes total count and supports sorting and filtering.
// The function includes OpenTelemetry tracing for monitoring the operation.
//
// Parameters:
//   - ctx: Context for the operation
//   - req: Request containing pagination, sorting, and user ID parameters
//
// Returns:
//   - *dto.ListAccessTokenResponse: Paginated list of access tokens
//   - error: Any error that occurred during retrieval
func (i *impl) ListAccessToken(ctx context.Context, req dto.ListAccessTokenRequest) (*dto.ListAccessTokenResponse, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.access_token.ListAccessToken",
		trace.WithAttributes(
			attribute.String("action", "LIST_ACCESS_TOKENS"),
			attribute.String("user_id", req.UserID.String()),
		))
	defer span.End()

	otelzap.InfoWithContext(ctx, "listing access tokens",
		zap.String("action", "LIST_ACCESS_TOKENS"),
		zap.String("user_id", req.UserID.String()))

	pred := repository.ListAccessTokenQuery{
		UserID: req.UserID,
	}

	total, err := i.repository.CountAccessToken(ctx, pred)
	if err != nil {
		span.SetStatus(codes.Error, "repository count access token failed")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "repository count access token failed", err,
			zap.String("action", "LIST_ACCESS_TOKENS"),
			zap.String("user_id", req.UserID.String()),
			zap.String("status", "failed"))
		return nil, usecase.ErrInternal
	}

	pagination := types.Pagination{
		PageNo:   req.Paginate.Page,
		PageSize: req.Paginate.PerPage,
	}
	orderBy := types.OrderBy{req.Paginate.OrderBy: req.Paginate.Sort}
	resp, err := i.repository.ListAccessToken(ctx, pagination, orderBy, pred)
	if err != nil {
		span.SetStatus(codes.Error, "repository list access token failed")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "repository list access token failed", err,
			zap.String("action", "LIST_ACCESS_TOKENS"),
			zap.String("user_id", req.UserID.String()),
			zap.String("status", "failed"))
		return nil, usecase.ErrInternal
	}

	data := dto.FromManyEntities[entities.UserToken, dto.AccessToken](resp)
	result := dto.ListAccessTokenResponse{
		Data: &data,
		Pagination: &dto.Pagination{
			Total:    int(total),
			PageNo:   pagination.PageNo,
			PageSize: pagination.PageSize,
		},
	}

	span.AddEvent("access tokens listed successfully")
	span.SetStatus(codes.Ok, "access tokens listed successfully")
	span.SetAttributes(attribute.String("status", "success"))
	otelzap.InfoWithContext(ctx, "access tokens listed successfully",
		zap.String("action", "LIST_ACCESS_TOKENS"),
		zap.String("user_id", req.UserID.String()),
		zap.String("status", "success"))
	return &result, nil
}

// GetAccessToken retrieves information about a specific access token.
// It verifies the token's existence and validity.
// The function includes OpenTelemetry tracing for monitoring the operation.
//
// Parameters:
//   - ctx: Context for the operation
//   - accessToken: The access token to retrieve
//
// Returns:
//   - *dto.AccessToken: The token information
//   - error: Any error that occurred during retrieval
func (i *impl) GetAccessToken(ctx context.Context, accessToken string) (*dto.AccessToken, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.access_token.GetAccessToken",
		trace.WithAttributes(
			attribute.String("action", "GET_ACCESS_TOKEN"),
			attribute.String("access_token", accessToken),
		))
	defer span.End()

	otelzap.InfoWithContext(ctx, "getting access token",
		zap.String("action", "GET_ACCESS_TOKEN"),
		zap.String("access_token", accessToken))

	resp, err := i.repository.FindAccessToken(ctx, repository.FindAccessTokenQuery{
		AccessToken: accessToken,
		Revoked:     false,
	})
	if err != nil {
		span.SetStatus(codes.Error, "repository find access token failed")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "repository find access token failed", err,
			zap.String("action", "GET_ACCESS_TOKEN"),
			zap.String("access_token", accessToken),
			zap.String("status", "failed"))
		return nil, usecase.ErrInternal
	}

	var result dto.AccessToken
	result = result.FromEntity(*resp)

	span.AddEvent("access token retrieved successfully")
	span.SetStatus(codes.Ok, "access token retrieved successfully")
	span.SetAttributes(attribute.String("status", "success"))
	otelzap.InfoWithContext(ctx, "access token retrieved successfully",
		zap.String("action", "GET_ACCESS_TOKEN"),
		zap.String("access_token", accessToken),
		zap.String("status", "success"))
	return &result, nil
}

// CreateAccessToken creates a new access token for a user.
// It generates a unique token using nanoid and stores it in the repository.
// The function includes OpenTelemetry tracing for monitoring the operation.
//
// Parameters:
//   - ctx: Context for the operation
//   - req: Request containing token creation parameters
//
// Returns:
//   - *dto.CreateAccessTokenResponse: The created token information
//   - error: Any error that occurred during creation
func (i *impl) CreateAccessToken(ctx context.Context, req dto.CreateAccessTokenRequest) (*dto.CreateAccessTokenResponse, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.access_token.CreateAccessToken",
		trace.WithAttributes(
			attribute.String("action", "CREATE_ACCESS_TOKEN"),
			attribute.String("user_id", req.UserID.String()),
		))
	defer span.End()

	otelzap.InfoWithContext(ctx, "creating access token",
		zap.String("action", "CREATE_ACCESS_TOKEN"),
		zap.String("user_id", req.UserID.String()))

	accessToken, err := gonanoid.New(NANOID_KEY_LENGTH)
	if err != nil {
		span.SetStatus(codes.Error, "nanoid generate failed")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "nanoid generate failed", err,
			zap.String("action", "CREATE_ACCESS_TOKEN"),
			zap.String("user_id", req.UserID.String()),
			zap.String("status", "failed"))
		return nil, err
	}

	resp, err := i.repository.CreateAccessToken(ctx, repository.CreateAccessTokenInput{
		Name:        req.Name,
		AccessToken: accessToken,
		Scopes:      req.Scopes,
		UserID:      req.UserID,
		ExpiresAt:   req.ExpiresAt,
	})
	if err != nil {
		span.SetStatus(codes.Error, "repository create access token failed")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "repository create access token failed", err,
			zap.String("action", "CREATE_ACCESS_TOKEN"),
			zap.String("user_id", req.UserID.String()),
			zap.String("status", "failed"))
		return nil, usecase.ErrInternal
	}

	var data dto.AccessToken
	data = data.FromEntity(*resp)
	data.AccessToken = resp.AccessToken
	result := dto.CreateAccessTokenResponse{
		Data: &data,
	}

	span.AddEvent("access token created successfully")
	span.SetStatus(codes.Ok, "access token created successfully")
	span.SetAttributes(attribute.String("status", "success"))
	otelzap.InfoWithContext(ctx, "access token created successfully",
		zap.String("action", "CREATE_ACCESS_TOKEN"),
		zap.String("user_id", req.UserID.String()),
		zap.String("status", "success"))
	return &result, nil
}

// DeleteAccessToken deletes a user's access token.
// It verifies the token ownership before deletion.
// The function includes OpenTelemetry tracing for monitoring the operation.
//
// Parameters:
//   - ctx: Context for the operation
//   - userID: The ID of the user
//   - accessTokenID: The ID of the token to delete
//
// Returns:
//   - error: Any error that occurred during deletion
func (i *impl) DeleteAccessToken(ctx context.Context, userID uuid.UUID, accessTokenID uuid.UUID) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.access_token.DeleteAccessToken",
		trace.WithAttributes(
			attribute.String("action", "DELETE_ACCESS_TOKEN"),
			attribute.String("access_token_id", accessTokenID.String()),
			attribute.String("user_id", userID.String()),
		))
	defer span.End()

	otelzap.InfoWithContext(ctx, "deleting access token",
		zap.String("action", "DELETE_ACCESS_TOKEN"),
		zap.String("access_token_id", accessTokenID.String()),
		zap.String("user_id", userID.String()))

	err := i.repository.DeleteAccessToken(ctx, repository.DeleteAccessTokenInput{
		AccessTokenID: accessTokenID,
		UserID:        userID,
	})
	if err != nil {
		span.SetStatus(codes.Error, "repository delete access token failed")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "repository delete access token failed", err,
			zap.String("action", "DELETE_ACCESS_TOKEN"),
			zap.String("access_token_id", accessTokenID.String()),
			zap.String("user_id", userID.String()),
			zap.String("status", "failed"))
		return usecase.ErrInternal
	}

	span.AddEvent("access token deleted successfully")
	span.SetStatus(codes.Ok, "access token deleted successfully")
	span.SetAttributes(attribute.String("status", "success"))
	otelzap.InfoWithContext(ctx, "access token deleted successfully",
		zap.String("action", "DELETE_ACCESS_TOKEN"),
		zap.String("access_token_id", accessTokenID.String()),
		zap.String("user_id", userID.String()),
		zap.String("status", "success"))
	return nil
}

// VerifyAccessToken verifies the validity of an access token.
// It checks if the token exists, is not revoked, and has not expired.
// The function includes OpenTelemetry tracing for monitoring the operation.
//
// Parameters:
//   - ctx: Context for the operation
//   - token: The token to verify
//
// Returns:
//   - *dto.VerifyAccessTokenResponse: The verification result
//   - error: Any error that occurred during verification
func (i *impl) VerifyAccessToken(ctx context.Context, token string) (*dto.VerifyAccessTokenResponse, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.access_token.VerifyAccessToken",
		trace.WithAttributes(
			attribute.String("action", "VERIFY_ACCESS_TOKEN"),
			attribute.String("access_token", token),
		))
	defer span.End()

	otelzap.InfoWithContext(ctx, "verifying access token",
		zap.String("action", "VERIFY_ACCESS_TOKEN"),
		zap.String("access_token", token))

	accessToken, err := i.repository.FindAccessToken(ctx, repository.FindAccessTokenQuery{
		AccessToken: token,
		Revoked:     false,
	})
	if err != nil {
		span.AddEvent("invalid access token")
		span.SetStatus(codes.Ok, "invalid access token") // Still OK status as per original code, but event added
		span.SetAttributes(attribute.String("status", "invalid"))
		otelzap.DebugWithContext(ctx, "invalid access token",
			zap.String("action", "VERIFY_ACCESS_TOKEN"),
			zap.String("access_token", token),
			zap.String("status", "invalid"))
		return nil, usecase.ErrInvalidToken
	}

	// Check if token has expired
	if accessToken.ExpiresAt != nil && time.Now().After(*accessToken.ExpiresAt) {
		span.AddEvent("expired access token")
		span.SetStatus(codes.Ok, "expired access token") // Still OK status as per original code, but event added
		span.SetAttributes(attribute.String("status", "expired"))
		otelzap.DebugWithContext(ctx, "expired access token",
			zap.String("action", "VERIFY_ACCESS_TOKEN"),
			zap.String("access_token", token),
			zap.String("status", "expired"))
		return nil, usecase.ErrTokenExpired
	}

	span.AddEvent("access token verified successfully")
	span.SetStatus(codes.Ok, "access token verified successfully")
	span.SetAttributes(attribute.String("status", "success"))
	otelzap.InfoWithContext(ctx, "access token verified successfully",
		zap.String("action", "VERIFY_ACCESS_TOKEN"),
		zap.String("access_token", token),
		zap.String("status", "success"))
	return &dto.VerifyAccessTokenResponse{
		Valid: true,
	}, nil
}
