package access_token_test

import (
	"context"
	"errors"
	"testing"
	"time"

	"api-server/internal/dto"
	"api-server/internal/entities"
	repository "api-server/internal/repositories"
	repository_mocks "api-server/internal/repositories/mocks"
	"api-server/internal/usecase"
	"api-server/internal/usecase/access_token"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"gorm.io/gorm"
)

func TestCreateAccessToken(t *testing.T) {
	mockUserID := uuid.New()

	testcases := []struct {
		name      string
		mockFn    func(r *repository_mocks.MockAccessTokenRepository)
		expectErr error
	}{
		{
			name: "should return usecase.ErrInternal if CreateAccessToken return error",
			mockFn: func(r *repository_mocks.MockAccessTokenRepository) {
				r.On("CreateAccessToken", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectErr: usecase.ErrInternal,
		},
		{
			name: "should pass",
			mockFn: func(r *repository_mocks.MockAccessTokenRepository) {
				r.On("CreateAccessToken", mock.Anything, mock.Anything).Return(&entities.UserToken{
					Name:        "Name",
					AccessToken: "GitlabAccessToken",
					Revoked:     false,
					UserID:      mockUserID,
					ExpiresAt:   nil,
				}, nil)
			},
			expectErr: nil,
		},
	}

	for _, testcase := range testcases {
		t.Run(testcase.name, func(t *testing.T) {
			r := &repository_mocks.MockAccessTokenRepository{}
			testcase.mockFn(r)

			usecase := access_token.New(r)
			_, err := usecase.CreateAccessToken(context.Background(), dto.CreateAccessTokenRequest{
				Name:      mock.Anything,
				ExpiresAt: nil,
				UserID:    mockUserID,
			})

			// assertion
			require.Equal(t, testcase.expectErr, err)
		})
	}
}

func TestListAccessToken(t *testing.T) {
	mockUserID := uuid.New()

	testcases := []struct {
		name      string
		mockFn    func(r *repository_mocks.MockAccessTokenRepository)
		expectErr error
	}{
		{
			name: "should return usecase.ErrInternal if CountAccessToken return error",
			mockFn: func(r *repository_mocks.MockAccessTokenRepository) {
				r.On("CountAccessToken", mock.Anything, mock.Anything).Return(int64(0), errors.New("error"))
			},
			expectErr: usecase.ErrInternal,
		},
		{
			name: "should return usecase.ErrInternal if ListAccessToken return error",
			mockFn: func(r *repository_mocks.MockAccessTokenRepository) {
				r.On("CountAccessToken", mock.Anything, mock.Anything).Return(int64(0), nil)
				r.On("ListAccessToken", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectErr: usecase.ErrInternal,
		},
		{
			name: "should pass",
			mockFn: func(r *repository_mocks.MockAccessTokenRepository) {
				r.On("CountAccessToken", mock.Anything, mock.Anything).Return(int64(0), nil)
				r.On("ListAccessToken", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]entities.UserToken{}, nil)
			},
			expectErr: nil,
		},
	}

	for _, testcase := range testcases {
		t.Run(testcase.name, func(t *testing.T) {
			r := &repository_mocks.MockAccessTokenRepository{}
			testcase.mockFn(r)

			usecase := access_token.New(r)
			_, err := usecase.ListAccessToken(context.Background(), dto.ListAccessTokenRequest{
				UserID:   mockUserID,
				Paginate: dto.PaginateRequest{},
			})

			// assertion
			require.Equal(t, testcase.expectErr, err)
		})
	}
}

func TestDeleteAccessToken(t *testing.T) {
	mockUserID := uuid.New()
	mockAccessTokenID := uuid.New()

	testcases := []struct {
		name      string
		mockFn    func(r *repository_mocks.MockAccessTokenRepository)
		expectErr error
	}{
		{
			name: "should return usecase.ErrInternal if DeleteAccessToken return error",
			mockFn: func(r *repository_mocks.MockAccessTokenRepository) {
				r.On("DeleteAccessToken", mock.Anything, mock.Anything, mock.Anything).Return(errors.New("error"))
			},
			expectErr: usecase.ErrInternal,
		},
		{
			name: "should pass",
			mockFn: func(r *repository_mocks.MockAccessTokenRepository) {
				r.On("DeleteAccessToken", mock.Anything, mock.Anything, mock.Anything).Return(nil)
			},
			expectErr: nil,
		},
	}

	for _, testcase := range testcases {
		t.Run(testcase.name, func(t *testing.T) {
			r := &repository_mocks.MockAccessTokenRepository{}
			testcase.mockFn(r)

			usecase := access_token.New(r)
			err := usecase.DeleteAccessToken(context.Background(), mockUserID, mockAccessTokenID)

			// assertion
			require.Equal(t, testcase.expectErr, err)
		})
	}
}

func TestGetAccessToken(t *testing.T) {
	mockAccessToken := "test_token_string"
	mockUserID := uuid.New()

	testcases := []struct {
		name      string
		mockFn    func(r *repository_mocks.MockAccessTokenRepository)
		expectErr error
	}{
		{
			name: "should return usecase.ErrInternal if FindAccessToken returns an error",
			mockFn: func(r *repository_mocks.MockAccessTokenRepository) {
				r.On("FindAccessToken", mock.Anything, mock.Anything).Return(nil, errors.New("db error"))
			},
			expectErr: usecase.ErrInternal,
		},
		{
			name: "should pass and return access token data",
			mockFn: func(r *repository_mocks.MockAccessTokenRepository) {
				r.On("FindAccessToken", mock.Anything, mock.Anything).Return(&entities.UserToken{
					BaseModel:   entities.BaseModel{ID: uuid.New()},
					Name:        "TestToken",
					AccessToken: mockAccessToken,
					Revoked:     false,
					UserID:      mockUserID,
					ExpiresAt:   nil,
				}, nil)
			},
			expectErr: nil,
		},
	}

	for _, testcase := range testcases {
		t.Run(testcase.name, func(t *testing.T) {
			r := &repository_mocks.MockAccessTokenRepository{}
			testcase.mockFn(r)

			uc := access_token.New(r)
			_, err := uc.GetAccessToken(context.Background(), mockAccessToken)

			// assertion
			require.Equal(t, testcase.expectErr, err)
		})
	}
}

func TestVerifyAccessToken(t *testing.T) {
	mockTokenString := "valid_token_string"
	validExpiresAt := time.Now().Add(time.Hour * 24)
	expiredExpiresAt := time.Now().Add(-time.Hour * 24)

	testcases := []struct {
		name                  string
		mockFn                func(r *repository_mocks.MockAccessTokenRepository)
		expectedResponseValid bool
		expectErr             error
	}{
		{
			name: "should return valid true for a valid token",
			mockFn: func(r *repository_mocks.MockAccessTokenRepository) {
				r.On("FindAccessToken", mock.Anything, mock.MatchedBy(func(query repository.FindAccessTokenQuery) bool {
					return query.AccessToken == mockTokenString && !query.Revoked
				})).Return(&entities.UserToken{
					AccessToken: mockTokenString,
					UserID:      uuid.New(),
					Revoked:     false,
					ExpiresAt:   &validExpiresAt,
				}, nil)
			},
			expectedResponseValid: true,
			expectErr:             nil,
		},
		{
			name: "should return ErrRecordNotFound if FindAccessToken returns gorm.ErrRecordNotFound",
			mockFn: func(r *repository_mocks.MockAccessTokenRepository) {
				r.On("FindAccessToken", mock.Anything, mock.MatchedBy(func(query repository.FindAccessTokenQuery) bool {
					return query.AccessToken == mockTokenString && !query.Revoked
				})).Return(nil, gorm.ErrRecordNotFound)
			},
			expectedResponseValid: false, // DTO will have Valid: false by default if response is nil
			expectErr:             usecase.ErrInvalidToken,
		},
		{
			name: "should return usecase.ErrInternal if FindAccessToken returns a generic error",
			mockFn: func(r *repository_mocks.MockAccessTokenRepository) {
				r.On("FindAccessToken", mock.Anything, mock.MatchedBy(func(query repository.FindAccessTokenQuery) bool {
					return query.AccessToken == mockTokenString && !query.Revoked
				})).Return(nil, errors.New("random db error"))
			},
			expectedResponseValid: false,
			expectErr:             usecase.ErrInvalidToken,
		},
		{
			name: "should return ErrTokenExpired if token is expired",
			mockFn: func(r *repository_mocks.MockAccessTokenRepository) {
				r.On("FindAccessToken", mock.Anything, mock.MatchedBy(func(query repository.FindAccessTokenQuery) bool {
					return query.AccessToken == mockTokenString && !query.Revoked
				})).Return(&entities.UserToken{
					AccessToken: mockTokenString,
					UserID:      uuid.New(),
					Revoked:     false,
					ExpiresAt:   &expiredExpiresAt, // This token is expired
				}, nil)
			},
			expectedResponseValid: false,
			expectErr:             usecase.ErrTokenExpired,
		},
	}

	for _, tc := range testcases {
		t.Run(tc.name, func(t *testing.T) {
			r := &repository_mocks.MockAccessTokenRepository{}
			tc.mockFn(r)

			uc := access_token.New(r)
			resp, err := uc.VerifyAccessToken(context.Background(), mockTokenString)

			assert.Equal(t, tc.expectErr, err)
			if tc.expectErr == nil {
				assert.NotNil(t, resp)
				assert.Equal(t, tc.expectedResponseValid, resp.Valid)
			} else {
				assert.Nil(t, resp)
			}

			r.AssertExpectations(t)
		})
	}
}
