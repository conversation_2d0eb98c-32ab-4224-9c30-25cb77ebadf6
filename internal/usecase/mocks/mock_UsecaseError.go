// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import mock "github.com/stretchr/testify/mock"

// MockUsecaseError is an autogenerated mock type for the UsecaseError type
type MockUsecaseError struct {
	mock.Mock
}

type MockUsecaseError_Expecter struct {
	mock *mock.Mock
}

func (_m *MockUsecaseError) EXPECT() *MockUsecaseError_Expecter {
	return &MockUsecaseError_Expecter{mock: &_m.Mock}
}

// Error provides a mock function with no fields
func (_m *MockUsecaseError) Error() string {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Error")
	}

	var r0 string
	if rf, ok := ret.Get(0).(func() string); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(string)
	}

	return r0
}

// MockUsecaseError_Error_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Error'
type MockUsecaseError_Error_Call struct {
	*mock.Call
}

// Error is a helper method to define mock.On call
func (_e *MockUsecaseError_Expecter) Error() *MockUsecaseError_Error_Call {
	return &MockUsecaseError_Error_Call{Call: _e.mock.On("Error")}
}

func (_c *MockUsecaseError_Error_Call) Run(run func()) *MockUsecaseError_Error_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockUsecaseError_Error_Call) Return(_a0 string) *MockUsecaseError_Error_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockUsecaseError_Error_Call) RunAndReturn(run func() string) *MockUsecaseError_Error_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockUsecaseError creates a new instance of MockUsecaseError. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockUsecaseError(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockUsecaseError {
	mock := &MockUsecaseError{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
