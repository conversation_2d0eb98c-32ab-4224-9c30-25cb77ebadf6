package kompose

import (
	"context"
	"fmt"
	"os"
	"path/filepath"

	"github.com/kubernetes/kompose/pkg/app"
	"github.com/kubernetes/kompose/pkg/kobject"
	"k8s.io/apimachinery/pkg/runtime"
)

// ConvertDockerComposeInput represents the input for converting docker-compose to Kubernetes manifests
type ConvertDockerComposeInput struct {
	// DockerComposeContent is the content of the docker-compose.yaml file
	DockerComposeContent string `json:"docker_compose_content" validate:"required" example:"version: '3'\nservices:\n  web:\n    image: nginx"`

	// Namespace is the Kubernetes namespace for the generated resources (optional)
	Namespace string `json:"namespace,omitempty" validate:"omitempty,min=1,max=63" example:"default"`

	// Replicas is the number of replicas for deployments (default: 1)
	// Replicas int `json:"replicas,omitempty" validate:"omitempty,min=1" example:"1"`

	// GenerateNetworkPolicies specifies whether to generate network policies
	GenerateNetworkPolicies bool `json:"generate_network_policies,omitempty" example:"false"`

	// VolumeType specifies the type of volumes to generate
	VolumeType string `json:"volume_type,omitempty" validate:"omitempty,oneof=persistentVolumeClaim emptyDir hostPath configMap" example:"persistentVolumeClaim"`

	// PVCRequestSize specifies the size of PVC storage requests
	PVCRequestSize string `json:"pvc_request_size,omitempty" example:"1Gi"`
}

// KubernetesManifest represents a single Kubernetes manifest
type KubernetesManifest struct {
	// Kind is the Kubernetes resource kind (e.g., Deployment, Service, etc.)
	Kind string `json:"kind" example:"Deployment"`

	// Name is the name of the resource
	Name string `json:"name" example:"web"`

	// Content is the YAML content of the manifest
	Content string `json:"content" example:"apiVersion: apps/v1\nkind: Deployment\nmetadata:\n  name: web"`
}

// ConvertDockerComposeResponse represents the response for docker-compose conversion
type ConvertDockerComposeResponse struct {
	// Manifests contains all generated Kubernetes manifests
	Manifests []KubernetesManifest `json:"manifests"`

	// Summary provides a summary of what was converted
	Summary ConversionSummary `json:"summary"`
}

// ConversionSummary provides statistics about the conversion
type ConversionSummary struct {
	// TotalServices is the number of services converted
	TotalServices int `json:"total_services" example:"3"`

	// GeneratedResources is a count of each resource type generated
	GeneratedResources map[string]int `json:"generated_resources" example:"{\"Deployment\":2,\"Service\":2,\"PersistentVolumeClaim\":1}"`

	// Warnings contains any warnings generated during conversion
	Warnings []string `json:"warnings,omitempty"`
}

// ConvertDockerComposeToKubernetes converts docker-compose content to Kubernetes manifests
func (u *impl) ConvertDockerComposeToKubernetes(ctx context.Context, input ConvertDockerComposeInput) (*ConvertDockerComposeResponse, error) {
	// Create a temporary directory for the conversion
	tempDir, err := os.MkdirTemp("", "kompose-conversion-")
	if err != nil {
		return nil, fmt.Errorf("failed to create temporary directory: %w", err)
	}
	defer os.RemoveAll(tempDir)

	// Write the docker-compose content to a temporary file
	composeFile := filepath.Join(tempDir, "docker-compose.yaml")
	if err := os.WriteFile(composeFile, []byte(input.DockerComposeContent), 0644); err != nil {
		return nil, fmt.Errorf("failed to write docker-compose file: %w", err)
	}

	// outputFile:= "./test.yaml"
	outputFile := filepath.Join("./", "output.yaml")
	// Set up conversion options
	convertOpt := kobject.ConvertOptions{
		InputFiles:              []string{composeFile},
		Provider:                "kubernetes",
		GenerateYaml:            true,
		GenerateJSON:            false,
		ToStdout:                false,
		OutFile:                 outputFile,
		Replicas:                getReplicasOrDefault(1),
		Namespace:               input.Namespace,
		GenerateNetworkPolicies: input.GenerateNetworkPolicies,
		Volumes:                 getVolumeTypeOrDefault(input.VolumeType),
		PVCRequestSize:          input.PVCRequestSize,
		WithKomposeAnnotation:   true,
		YAMLIndent:              2,
	}

	// Validate the compose file
	if err := app.ValidateComposeFile(&convertOpt); err != nil {
		return nil, fmt.Errorf("invalid docker-compose file: %w", err)
	}

	// Perform the conversion
	objects, err := app.Convert(convertOpt)
	if err != nil {
		return nil, fmt.Errorf("failed to convert docker-compose to Kubernetes: %w", err)
	}

	// Process the converted objects
	manifests, summary, err := u.processConvertedObjects(objects)
	if err != nil {
		return nil, fmt.Errorf("failed to process converted objects: %w", err)
	}

	// parse using ParseManifestsFromFile()
	manifests, err = ParseManifestsFromFile(ctx, outputFile)
	if err != nil {
		return nil, fmt.Errorf("failed to parse manifests: %w", err)
	}

	return &ConvertDockerComposeResponse{
		Manifests: manifests,
		Summary:   summary,
	}, nil
}

// processConvertedObjects processes the runtime objects into our format
func (u *impl) processConvertedObjects(objects []runtime.Object) ([]KubernetesManifest, ConversionSummary, error) {
	var manifests []KubernetesManifest
	resourceCounts := make(map[string]int)
	var warnings []string

	for _, obj := range objects {
		// Convert the runtime object to YAML
		yamlContent, err := u.objectToYAML(obj)
		if err != nil {
			warnings = append(warnings, fmt.Sprintf("failed to convert object to YAML: %v", err))
			continue
		}

		// Extract metadata
		kind, name, err := u.extractObjectMetadata(obj)
		if err != nil {
			warnings = append(warnings, fmt.Sprintf("failed to extract object metadata: %v", err))
			continue
		}

		manifests = append(manifests, KubernetesManifest{
			Kind:    kind,
			Name:    name,
			Content: yamlContent,
		})

		// Count resources
		resourceCounts[kind]++
	}

	// Calculate total services (assuming services are the primary unit)
	totalServices := resourceCounts["Service"]
	if totalServices == 0 {
		// If no services, count deployments as services
		totalServices = resourceCounts["Deployment"]
	}

	summary := ConversionSummary{
		TotalServices:      totalServices,
		GeneratedResources: resourceCounts,
		Warnings:           warnings,
	}

	return manifests, summary, nil
}

// objectToYAML converts a runtime.Object to YAML string
func (u *impl) objectToYAML(obj runtime.Object) (string, error) {
	// For now, return a placeholder YAML representation
	// In a real implementation, we would use proper Kubernetes serialization
	gvk := obj.GetObjectKind().GroupVersionKind()

	placeholder := fmt.Sprintf(`apiVersion: %s/%s
kind: %s
metadata:
  name: placeholder
spec:
  # Converted from docker-compose
`, gvk.Group, gvk.Version, gvk.Kind)

	return placeholder, nil
}

// extractObjectMetadata extracts kind and name from a runtime.Object
func (u *impl) extractObjectMetadata(obj runtime.Object) (kind, name string, err error) {
	// Get the object's type information
	gvk := obj.GetObjectKind().GroupVersionKind()
	kind = gvk.Kind

	// For now, return a placeholder name
	// In a real implementation, we would extract the actual name from the object
	name = "placeholder-name"

	return kind, name, nil
}

// getReplicasOrDefault returns the replicas value or default of 1
func getReplicasOrDefault(replicas int) int {
	if replicas <= 0 {
		return 1
	}
	return replicas
}

// getVolumeTypeOrDefault returns the volume type or default of "persistentVolumeClaim"
func getVolumeTypeOrDefault(volumeType string) string {
	if volumeType == "" {
		return "persistentVolumeClaim"
	}
	return volumeType
}
