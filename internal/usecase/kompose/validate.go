package kompose

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"

	"github.com/kubernetes/kompose/pkg/app"
	"github.com/kubernetes/kompose/pkg/kobject"
	"go.opentelemetry.io/otel/codes"
	"gopkg.in/yaml.v3"

	"api-server/internal/utils"
	"api-server/pkg/oteltrace"
)

// ValidateDockerComposeInput represents the input for validating docker-compose content
type ValidateDockerComposeInput struct {
	// DockerComposeContent is the content of the docker-compose.yaml file
	DockerComposeContent string
}

// ValidateDockerComposeResponse represents the response from validating docker-compose content
type ValidateDockerComposeResponse struct {
	// Valid indicates if the docker-compose content is valid
	Valid bool `json:"valid"`
	// Errors contains validation errors if any
	Errors []string `json:"errors,omitempty"`
	// Warnings contains validation warnings if any
	Warnings []string `json:"warnings,omitempty"`
}

// ValidateDockerCompose validates docker-compose content format and structure
func ValidateDockerCompose(ctx context.Context, input ValidateDockerComposeInput) (*ValidateDockerComposeResponse, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.kompose.ValidateDockerCompose")
	defer span.End()

	response := &ValidateDockerComposeResponse{
		Valid:    true,
		Errors:   []string{},
		Warnings: []string{},
	}

	// Step 1: Basic YAML syntax validation
	var composeConfig map[string]interface{}
	if err := yaml.Unmarshal([]byte(input.DockerComposeContent), &composeConfig); err != nil {
		span.SetStatus(codes.Error, "invalid YAML syntax")
		span.RecordError(err)
		response.Valid = false
		response.Errors = append(response.Errors, fmt.Sprintf("Invalid YAML syntax: %v", err))
		return response, nil
	}

	// Step 2: Check for required 'services' section
	servicesInterface, ok := composeConfig["services"]
	if !ok {
		response.Valid = false
		response.Errors = append(response.Errors, "Missing required 'services' section")
		return response, nil
	}

	// Step 3: Validate services structure
	services, ok := servicesInterface.(map[string]interface{})
	if !ok {
		response.Valid = false
		response.Errors = append(response.Errors, "Invalid 'services' format - must be a mapping")
		return response, nil
	}

	if len(services) == 0 {
		response.Valid = false
		response.Errors = append(response.Errors, "No services defined in docker-compose file")
		return response, nil
	}

	// Step 4: Use kompose's validation for deeper validation
	// Create a temporary directory for the validation
	tempDir, err := os.MkdirTemp("", "kompose-validation-")
	if err != nil {
		span.SetStatus(codes.Error, "failed to create temporary directory")
		span.RecordError(err)
		return nil, fmt.Errorf("failed to create temporary directory: %w", err)
	}
	defer os.RemoveAll(tempDir)

	// Write the docker-compose content to a temporary file
	composeFile := filepath.Join(tempDir, "docker-compose.yaml")
	if err := os.WriteFile(composeFile, []byte(input.DockerComposeContent), 0644); err != nil {
		span.SetStatus(codes.Error, "failed to write docker-compose file")
		span.RecordError(err)
		return nil, fmt.Errorf("failed to write docker-compose file: %w", err)
	}

	// Set up validation options
	convertOpt := kobject.ConvertOptions{
		InputFiles: []string{composeFile},
		Provider:   "kubernetes",
	}

	// Validate the compose file using kompose's validation
	if err := app.ValidateComposeFile(&convertOpt); err != nil {
		span.SetStatus(codes.Error, "invalid docker-compose file")
		span.RecordError(err)
		response.Valid = false
		response.Errors = append(response.Errors, fmt.Sprintf("Docker Compose validation failed: %v", err))
		return response, nil
	}

	// Step 5: Additional validations for each service
	for serviceName, serviceInterface := range services {
		service, ok := serviceInterface.(map[string]interface{})
		if !ok {
			response.Errors = append(response.Errors,
				fmt.Sprintf("Service '%s' has invalid format", serviceName))
			response.Valid = false
			return response, nil
		}

		// Check for image definition
		if _, ok := service["image"]; !ok {
			if _, hasBuild := service["build"]; !hasBuild {
				response.Errors = append(response.Errors,
					fmt.Sprintf("Service '%s' has neither 'image' nor 'build' specified", serviceName))
				response.Valid = false
				return response, nil
			}
		}
	}

	return response, nil
}

// CheckKomposeLabelsInput represents the input for checking kompose labels
type CheckKomposeLabelsInput struct {
	// DockerComposeContent is the content of the docker-compose.yaml file
	DockerComposeContent string
}

// CheckKomposeLabels checks if any service labels start with "kompose" and returns an error if found
func CheckKomposeLabels(ctx context.Context, input CheckKomposeLabelsInput) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.kompose.CheckKomposeLabels")
	defer span.End()

	// Parse the docker-compose YAML content
	var composeConfig map[string]interface{}
	if err := yaml.Unmarshal([]byte(input.DockerComposeContent), &composeConfig); err != nil {
		span.SetStatus(codes.Error, "invalid YAML syntax")
		span.RecordError(err)
		return fmt.Errorf("invalid YAML syntax: %w", err)
	}

	// Check for required 'services' section
	servicesInterface, ok := composeConfig["services"]
	if !ok {
		return fmt.Errorf("missing required 'services' section")
	}

	// Validate services structure
	services, ok := servicesInterface.(map[string]interface{})
	if !ok {
		return fmt.Errorf("invalid 'services' format - must be a mapping")
	}

	// Check each service for kompose labels
	for serviceName, serviceInterface := range services {
		service, ok := serviceInterface.(map[string]interface{})
		if !ok {
			continue
		}

		// Check if service has labels
		labelsInterface, hasLabels := service["labels"]
		if !hasLabels {
			continue
		}

		// Handle different label formats
		switch labels := labelsInterface.(type) {
		case map[string]interface{}:
			// Labels as key-value map
			for labelKey := range labels {
				if strings.HasPrefix(labelKey, "kompose") {
					err := fmt.Errorf("service '%s' contains kompose label '%s' - kompose labels are not allowed", serviceName, labelKey)
					span.SetStatus(codes.Error, "kompose label found")
					span.RecordError(err)
					return err
				}
			}
		case []interface{}:
			// Labels as array of strings
			for _, labelInterface := range labels {
				if labelStr, ok := labelInterface.(string); ok {
					// Handle "key=value" format
					if strings.Contains(labelStr, "=") {
						parts := strings.SplitN(labelStr, "=", 2)
						if len(parts) > 0 && strings.HasPrefix(parts[0], "kompose") {
							err := fmt.Errorf("service '%s' contains kompose label '%s' - kompose labels are not allowed", serviceName, parts[0])
							span.SetStatus(codes.Error, "kompose label found")
							span.RecordError(err)
							return err
						}
					} else {
						// Handle plain key format
						if strings.HasPrefix(labelStr, "kompose") {
							err := fmt.Errorf("service '%s' contains kompose label '%s' - kompose labels are not allowed", serviceName, labelStr)
							span.SetStatus(codes.Error, "kompose label found")
							span.RecordError(err)
							return err
						}
					}
				}
			}
		}
	}

	return nil
}

// TransformVolvoLabelsInput represents the input for transforming volvo labels to kompose labels
type TransformVolvoLabelsInput struct {
	// DockerComposeContent is the content of the docker-compose.yaml file
	DockerComposeContent string
}

// TransformVolvoLabelsResponse represents the response from transforming volvo labels
type TransformVolvoLabelsResponse struct {
	// TransformedContent is the modified docker-compose content with kompose labels
	TransformedContent string
	// TransformationCount is the number of labels that were transformed
	TransformationCount int
	// TransformedLabels contains details about which labels were transformed
	TransformedLabels []TransformedLabel

	ProxyBodySizes map[string]*int
}

// TransformedLabel represents a single label transformation
type TransformedLabel struct {
	ServiceName   string   `json:"service_name"`
	OriginalLabel string   `json:"original_label"`
	TransformedTo []string `json:"transformed_to"`
}

// TransformVolvoLabels transforms volvo.* labels to their corresponding kompose.* labels
func TransformVolvoLabels(ctx context.Context, input TransformVolvoLabelsInput) (*TransformVolvoLabelsResponse, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.kompose.TransformVolvoLabels")
	defer span.End()

	// Parse the docker-compose YAML content
	var composeConfig map[string]interface{}
	if err := yaml.Unmarshal([]byte(input.DockerComposeContent), &composeConfig); err != nil {
		span.SetStatus(codes.Error, "invalid YAML syntax")
		span.RecordError(err)
		return nil, fmt.Errorf("invalid YAML syntax: %w", err)
	}

	// Check for required 'services' section
	servicesInterface, ok := composeConfig["services"]
	if !ok {
		return nil, fmt.Errorf("missing required 'services' section")
	}

	// Validate services structure
	services, ok := servicesInterface.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid 'services' format - must be a mapping")
	}

	response := &TransformVolvoLabelsResponse{
		TransformationCount: 0,
		TransformedLabels:   []TransformedLabel{},
	}

	// Define the label transformation mappings
	labelMappings := map[string][]string{
		"volvo.service.expose": {
			"kompose.service.expose: true",
			"kompose.service.expose.ingress-class-name: \"nginx\"",
		},
		"volvo.service.type": {
			"kompose.service.type: PRESERVE_VALUE",
		},
		"volvo.volume.size": {
			"kompose.volume.size: PRESERVE_VALUE",
		},
		"volvo.service.nodeport": {
			"kompose.service.type: \"NodePort\"",
		},
		"volvo.service.loadbalancer": {
			"kompose.service.type: \"LoadBalancer\"",
		},
		"volvo.ingress.proxy-body-size": {
			"nginx.ingress.kubernetes.io/proxy-body-size: PRESERVE_VALUE",
		},
	}

	// Transform labels in each service
	for serviceName, serviceInterface := range services {
		serviceName = utils.Slugify(serviceName)
		service, ok := serviceInterface.(map[string]interface{})
		if !ok {
			continue
		}

		// Check if service has labels
		labelsInterface, hasLabels := service["labels"]
		if !hasLabels {
			continue
		}

		// Handle different label formats
		switch labels := labelsInterface.(type) {
		case map[string]interface{}:
			// Labels as key-value map
			transformedLabels := make(map[string]interface{})

			// Copy existing labels first
			for k, v := range labels {
				transformedLabels[k] = v
			}

			// Transform volvo labels
			for labelKey, labelValue := range labels {
				if strings.HasPrefix(labelKey, "volvo.") {
					// Remove the original volvo label
					delete(transformedLabels, labelKey)

					// Add corresponding kompose labels
					if mappings, exists := labelMappings[labelKey]; exists {
						transformedLabel := TransformedLabel{
							ServiceName:   serviceName,
							OriginalLabel: fmt.Sprintf("%s: %v", labelKey, labelValue),
							TransformedTo: []string{},
						}

						for _, mapping := range mappings {
							if strings.Contains(mapping, ":") {
								// Handle "key: value" format
								parts := strings.SplitN(mapping, ":", 2)
								if len(parts) == 2 {
									key := strings.TrimSpace(parts[0])
									value := strings.TrimSpace(parts[1])

									// Handle special case where we need to preserve original value
									if value == "PRESERVE_VALUE" {
										// Special validation for proxy-body-size
										if labelKey == "volvo.ingress.proxy-body-size" {
											if err := validateProxyBodySize(fmt.Sprintf("%v", labelValue)); err != nil {
												span.SetStatus(codes.Error, "invalid proxy-body-size value")
												span.RecordError(err)
												return nil, fmt.Errorf("service '%s': %w", serviceName, err)
											}
											// Convert proxy-body-size to int with m unit
											intValue, err := convertProxyBodySizeToInt(fmt.Sprintf("%v", labelValue))
											if err != nil {
												span.SetStatus(codes.Error, "failed to convert proxy-body-size to int")
												span.RecordError(err)
												return nil, fmt.Errorf("service '%s': %w", serviceName, err)
											}
											response.ProxyBodySizes[serviceName] = &intValue
										}
										transformedLabels[key] = labelValue
										transformedLabel.TransformedTo = append(transformedLabel.TransformedTo, fmt.Sprintf("%s: %v", key, labelValue))
									} else {
										// Parse the value (remove quotes if present)
										if strings.HasPrefix(value, "\"") && strings.HasSuffix(value, "\"") {
											value = strings.Trim(value, "\"")
										}
										if value == "true" {
											transformedLabels[key] = true
										} else if value == "false" {
											transformedLabels[key] = false
										} else {
											transformedLabels[key] = value
										}
										transformedLabel.TransformedTo = append(transformedLabel.TransformedTo, mapping)
									}
								}
							}
						}

						response.TransformedLabels = append(response.TransformedLabels, transformedLabel)
						response.TransformationCount++
					}
				}
			}

			// Update the service labels
			service["labels"] = transformedLabels

		case []interface{}:
			// Labels as array of strings - this is more complex to handle
			// For now, we'll skip array format transformation
			// TODO: Implement array format transformation if needed
			continue
		}
	}

	// Marshal the modified config back to YAML
	modifiedContent, err := yaml.Marshal(composeConfig)
	if err != nil {
		span.SetStatus(codes.Error, "failed to marshal modified content")
		span.RecordError(err)
		return nil, fmt.Errorf("failed to marshal modified content: %w", err)
	}

	response.TransformedContent = string(modifiedContent)
	return response, nil
}

// validateProxyBodySize validates the format of proxy-body-size value
// Valid formats: "100m", "1g", "500k", "2G", "1024M", etc.
func validateProxyBodySize(value string) error {
	if value == "" {
		return fmt.Errorf("proxy-body-size value cannot be empty")
	}

	// Regular expression to match valid proxy-body-size formats
	// Supports: digits followed by optional unit (k, K, m, M, g, G)
	// Examples: "100m", "1g", "500k", "2G", "1024M", "0"
	validPattern := `^[0-9]+[kKmMgG]?$`
	matched, err := regexp.MatchString(validPattern, value)
	if err != nil {
		return fmt.Errorf("error validating proxy-body-size format: %w", err)
	}

	if !matched {
		return fmt.Errorf("invalid proxy-body-size format '%s'. Valid formats: '100m', '1g', '500k', '2G', '1024M', etc.", value)
	}

	return nil
}

// convert proxy-body-size to int with m unit
func convertProxyBodySizeToInt(value string) (int, error) {
	// Regular expression to extract digits and unit from the value
	// Example: "100m" -> digits = "100", unit = "m"
	extractPattern := `^([0-9]+)([kKmMgG]?)$`
	re := regexp.MustCompile(extractPattern)
	matches := re.FindStringSubmatch(value)
	if matches == nil {
		return 0, fmt.Errorf("failed to extract digits and unit from proxy-body-size value '%s'", value)
	}

	digits := matches[1]
	unit := matches[2]

	// Convert digits to int
	intValue, err := strconv.Atoi(digits)
	if err != nil {
		return 0, fmt.Errorf("failed to convert digits '%s' to int: %w", digits, err)
	}

	// Convert to int with m unit
	switch strings.ToLower(unit) {
	case "k":
		intValue *= 1024
	case "m":
		// no-op
	case "g":
		intValue *= 1024 * 1024
	case "":
		// no-op
	default:
		return 0, fmt.Errorf("unsupported unit '%s' in proxy-body-size value '%s'", unit, value)
	}

	return intValue, nil
}
