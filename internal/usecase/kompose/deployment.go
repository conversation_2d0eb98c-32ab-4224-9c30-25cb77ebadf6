package kompose

import (
	"context"
	"encoding/base64"
	"errors"
	"fmt"
	"strings"

	"github.com/google/uuid"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
	"gopkg.in/yaml.v3"
	"gorm.io/gorm"
	appsv1 "k8s.io/api/apps/v1"
	networkingv1 "k8s.io/api/networking/v1"
	kubeerrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/client-go/kubernetes"

	"api-server/internal/dto"
	"api-server/internal/entities"
	"api-server/internal/enums"
	"api-server/internal/gateways/gitlab"
	repository "api-server/internal/repositories"
	"api-server/internal/types"
	"api-server/internal/usecase"
	"api-server/internal/utils"
	"api-server/pkg/oteltrace"
)

func (u *impl) CreateComposeDeployment(ctx context.Context, userID uuid.UUID, repoID types.RepoID, input dto.StartDeploymentRequest) (*dto.StartDeploymentResponse, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.kompose.CreateComposeDeployment")
	defer span.End()

	repo, err := u.repo.FindRepository(ctx, repository.RepositoryFilter{
		Type:      repoID.RepoType(),
		Namespace: repoID.Namespace(),
		Name:      repoID.RepoName(),
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find repository")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, usecase.ErrRepositoryNotFound
		}
		return nil, err
	}

	dockerComposeContent, err := u.getDockerComposeFileContent(ctx, userID, repo, input.Revision)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get docker-compose.yaml file content")
		span.RecordError(err)
		return nil, err
	}

	// validate docker-compose
	validationResp, err := ValidateDockerCompose(ctx, ValidateDockerComposeInput{
		DockerComposeContent: dockerComposeContent,
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to validate docker-compose")
		span.RecordError(err)
		return nil, err
	}
	if !validationResp.Valid {
		span.SetStatus(codes.Error, "invalid docker-compose.yaml file")
		// wrap usecase.ErrInvalidInput error with validation errors
		return nil, fmt.Errorf("%w: %v", usecase.ErrInvalidInput, validationResp.Errors)
	}

	// check compose labels
	if err := CheckKomposeLabels(ctx, CheckKomposeLabelsInput{
		DockerComposeContent: dockerComposeContent,
	}); err != nil {
		span.SetStatus(codes.Error, "kompose labels are not allowed")
		span.RecordError(err)
		return nil, err
	}

	response, err := TransformVolvoLabels(context.Background(), TransformVolvoLabelsInput{
		DockerComposeContent: dockerComposeContent,
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to transform volvo labels")
		span.RecordError(err)
		return nil, err
	}
	dockerComposeContent = response.TransformedContent

	dockerComposeConfig, err := CollectDockerComposeConfig(ctx, dockerComposeContent)
	if err != nil {
		span.SetStatus(codes.Error, "failed to collect ports and volumes from docker-compose")
		span.RecordError(err)
		return nil, err
	}
	_ = dockerComposeConfig

	converted, err := u.ConvertDockerComposeToKubernetes(ctx, ConvertDockerComposeInput{
		DockerComposeContent: dockerComposeContent,
		Namespace:            ComposeResourceNamespace(repo.Name),
		// GenerateNetworkPolicies: true,
		// VolumeType:              "persistentVolumeClaim",
		// PVCRequestSize:          "1Gi",
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to convert docker-compose to Kubernetes")
		span.RecordError(err)
		return nil, err
	}
	_ = converted

	err = u.repo.Transaction(ctx, func(ctx context.Context) error {
		// Remove all existing Compose deployments for this repository
		err := u.removeExistingComposeDeployments(ctx, repo.ID)
		if err != nil {
			span.SetStatus(codes.Error, "failed to remove existing compose deployments")
			span.RecordError(err)
			return err
		}

		err = u.createComposeDeployment(ctx, userID, repo, CreateComposeDeploymentInput{
			Ports:          dockerComposeConfig.Ports,
			Volumes:        dockerComposeConfig.Volumes,
			ProxyBodySizes: dockerComposeConfig.ProxyBodySizes,
			Manifests:      converted.Manifests,
		})
		if err != nil {
			span.SetStatus(codes.Error, "failed to create compose deployment")
			span.RecordError(err)
			return err
		}

		// apply kubernetes manifests
		applyResponse, err := u.ApplyKubernetesManifests(ctx, ApplyKubernetesManifestsInput{
			Manifests:      converted.Manifests,
			Namespace:      ComposeResourceNamespace(repo.Name),
			DryRun:         false,
			Force:          false,
			ComposeRepo:    repo,
			ProxyBodySizes: dockerComposeConfig.ProxyBodySizes,
		})
		if err != nil {
			span.SetStatus(codes.Error, "failed to apply Kubernetes manifests")
			span.RecordError(err)
			return err
		}

		for _, appliedResource := range applyResponse.AppliedResources {
			span.AddEvent(fmt.Sprintf("%s/%s", appliedResource.Kind, appliedResource.Name), trace.WithAttributes(
				attribute.String("kind", appliedResource.Kind),
				attribute.String("name", appliedResource.Name),
				attribute.String("namespace", appliedResource.Namespace),
				attribute.String("action", appliedResource.Action),
				attribute.String("message", appliedResource.Message),
			))
			println(appliedResource.Kind, appliedResource.Name, appliedResource.Action, appliedResource.Message)
		}
		span.AddEvent("Applied resources", trace.WithAttributes(
			attribute.Int("total", applyResponse.Summary.TotalResources),
			attribute.Int("successful", applyResponse.Summary.SuccessfulResources),
			attribute.Int("failed", applyResponse.Summary.FailedResources),
		))
		for i, e := range applyResponse.Errors {
			span.AddEvent(fmt.Sprintf("Error %d", i+1), trace.WithAttributes(
				attribute.String("error", e),
			))
		}

		return nil
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to create transaction")
		span.RecordError(err)
		return nil, err
	}

	resp := &dto.StartDeploymentResponse{
		Message: "success",
	}

	return resp, nil
}

func (u *impl) getDockerComposeFileContent(
	ctx context.Context,
	userID uuid.UUID,
	repo *entities.Repository,
	revision *string,
) (string, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.kompose.createComposeDeploymentTx")
	defer span.End()

	var ref string
	if revision == nil {
		ref = "main"
	} else {
		ref = *revision
	}

	token, err := u.getUserGitlabAccessToken(ctx, userID)
	if err != nil {
		return "", err
	}

	resp, err := u.gitlab.GetFileFromRepo(ctx, gitlab.GetFileRequest{
		ProjectId: repo.RefGitRepoID,
		FilePath:  "docker-compose.yaml",
		Ref:       ref,
		Token:     token,
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to get file from repo in gitlab")
		span.RecordError(err)

		if errors.Is(err, gitlab.ErrNotFound) {
			return "", usecase.ErrDockerComposeFileNotFound
		}

		return "", err
	}

	// convert base64 string to string
	decoded, err := base64.StdEncoding.DecodeString(resp.Content)
	if err != nil {
		span.SetStatus(codes.Error, "failed to decode base64 content")
		span.RecordError(err)
		return "", err
	}

	return string(decoded), nil
}

func (i *impl) getUserGitlabAccessToken(ctx context.Context, userID uuid.UUID) (string, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.kompose.getUserGitlabAccessToken")
	defer span.End()

	currentUser, err := i.repo.FindUserByID(ctx, userID)
	if err != nil {
		span.SetStatus(codes.Error, "failed to find user")
		span.RecordError(err)
		return "", err
	}

	token := ""
	if currentUser.GitlabAccessToken != nil {
		token = *currentUser.GitlabAccessToken
	}
	if currentUser.Role == enums.UserRole_Admin {
		adminRefGitID := enums.AdminRefGitID
		adminUser, err := i.repo.FindUser(ctx, repository.FindUserFilter{RefGitUserID: &adminRefGitID})
		if err != nil {
			span.SetStatus(codes.Error, "failed to find admin user")
			span.RecordError(err)
			return "", err
		}
		if adminUser.GitlabAccessToken != nil {
			token = *adminUser.GitlabAccessToken
		}
	}

	return token, nil
}

type CreateComposeDeploymentInput struct {
	Ports          map[string][]string
	Volumes        map[string][]string
	ProxyBodySizes map[string]*int
	Manifests      []KubernetesManifest
}

func (u *impl) createComposeDeployment(
	ctx context.Context,
	userID uuid.UUID,
	composeRepo *entities.Repository,
	input CreateComposeDeploymentInput,
) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.kompose.createComposeDeployment")
	defer span.End()

	// TODO: remove all existing Compose deployments belongs to this repository ID

	manifestMap := make(map[string]*repository.CreateCustomImageDeploymentInput)

	for _, it := range input.Manifests {
		switch strings.ToLower(it.Kind) {
		case "deployment":
			// Parse the YAML content
			obj := &unstructured.Unstructured{}
			if err := yaml.Unmarshal([]byte(it.Content), obj); err != nil {
				return fmt.Errorf("failed to parse YAML: %w", err)
			}

			// convert to Deployment
			deployment := &appsv1.Deployment{}
			if err := runtime.DefaultUnstructuredConverter.FromUnstructured(obj.Object, deployment); err != nil {
				return fmt.Errorf("failed to convert to Deployment: %w", err)
			}

			manifestMap[it.Name] = &repository.CreateCustomImageDeploymentInput{
				DeploymentName: it.Name,
				ImageURI:       deployment.Spec.Template.Spec.Containers[0].Image,
				NodeName:       "cpu",
				Port:           8080,
				Env:            map[string]string{}, // TODO: implement this
				UserID:         &userID,
				CPU:            1,
				Mem:            512, // in Mebibytes
				// GPUModel:       utils.Ptr("model"), // TODO: implement this
				ProxyBodySize:  100,
				DeploymentType: utils.Ptr(types.CustomImageDeploymentType_Compose),
				ComposePorts:   input.Ports[it.Name],
				Volumes:        input.Volumes[it.Name],
				RestartPolicy:  utils.Ptr(string(deployment.Spec.Template.Spec.RestartPolicy)),
				Author:         &composeRepo.User.Username,
				RepoID:         utils.Ptr(composeRepo.ID),
				Namespace:      ComposeResourceNamespace(composeRepo.Name),
			}
			if input.ProxyBodySizes[it.Name] != nil {
				manifestMap[it.Name].ProxyBodySize = *input.ProxyBodySizes[it.Name]
			}
		default:
			continue
		}
	}

	// find ports from ingress
	for _, it := range input.Manifests {
		switch strings.ToLower(it.Kind) {
		case "ingress":
			// Parse the YAML content
			obj := &unstructured.Unstructured{}
			if err := yaml.Unmarshal([]byte(it.Content), obj); err != nil {
				return fmt.Errorf("failed to parse YAML: %w", err)
			}

			// convert to Ingress
			ingress := &networkingv1.Ingress{}
			if err := runtime.DefaultUnstructuredConverter.FromUnstructured(obj.Object, ingress); err != nil {
				return fmt.Errorf("failed to convert to Ingress: %w", err)
			}

			// find matching deployment
			for _, rule := range ingress.Spec.Rules {
				for _, path := range rule.HTTP.Paths {
					if path.Backend.Service != nil {
						if deployment, ok := manifestMap[path.Backend.Service.Name]; ok {
							deployment.Port = path.Backend.Service.Port.Number
						}
					}
				}
			}
		default:
			continue
		}
	}

	// save all deployments to database
	for _, it := range manifestMap {
		// create deployment
		if it != nil {
			a, err := u.repo.CreateECRDeployment(ctx, *it)
			if err != nil {
				span.SetStatus(codes.Error, "failed to create deployment")
				span.RecordError(err)
				return err
			}
			_ = a
		}
	}

	return nil
}

type DockerComposeConfig struct {
	Ports          map[string][]string
	Volumes        map[string][]string
	ProxyBodySizes map[string]*int
}

// CollectDockerComposeConfig extracts all ports and volumes from docker-compose services
func CollectDockerComposeConfig(ctx context.Context, dockerComposeContent string) (*DockerComposeConfig, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.kompose.CollectDockerComposeConfig")
	defer span.End()

	// Parse the docker-compose YAML content
	var composeConfig map[string]any
	if err := yaml.Unmarshal([]byte(dockerComposeContent), &composeConfig); err != nil {
		span.SetStatus(codes.Error, "failed to parse docker-compose content")
		span.RecordError(err)
		return nil, fmt.Errorf("failed to parse docker-compose content: %w", err)
	}

	// Get services section
	servicesInterface, ok := composeConfig["services"]
	if !ok {
		return nil, fmt.Errorf("no services found in docker-compose file")
	}

	services, ok := servicesInterface.(map[string]any)
	if !ok {
		return nil, fmt.Errorf("invalid services format in docker-compose file")
	}

	// Extract ports and volumes from each service
	servicePorts := make(map[string][]string)
	serviceVolumes := make(map[string][]string)
	proxyBodySizes := make(map[string]*int)

	for serviceName, serviceInterface := range services {
		serviceName = utils.Slugify(serviceName)
		service, ok := serviceInterface.(map[string]any)
		if !ok {
			continue
		}

		// Extract ports
		var ports []string
		if portsInterface, exists := service["ports"]; exists {
			switch p := portsInterface.(type) {
			case []any:
				for _, portInterface := range p {
					switch port := portInterface.(type) {
					case string:
						ports = append(ports, port)
					case map[string]any:
						// Handle long syntax port definitions
						if published, ok := port["published"].(string); ok {
							if target, ok := port["target"].(string); ok {
								portStr := fmt.Sprintf("%s:%s", published, target)
								if protocol, ok := port["protocol"].(string); ok {
									portStr += "/" + protocol
								}
								ports = append(ports, portStr)
							}
						} else if published, ok := port["published"].(int); ok {
							if target, ok := port["target"].(int); ok {
								portStr := fmt.Sprintf("%d:%d", published, target)
								if protocol, ok := port["protocol"].(string); ok {
									portStr += "/" + protocol
								}
								ports = append(ports, portStr)
							}
						}
					}
				}
			}
		}
		servicePorts[serviceName] = ports

		// Extract volumes
		var volumes []string
		if volumesInterface, exists := service["volumes"]; exists {
			switch v := volumesInterface.(type) {
			case []any:
				for _, volumeInterface := range v {
					switch volume := volumeInterface.(type) {
					case string:
						volumes = append(volumes, volume)
					case map[string]any:
						// Handle long syntax volume definitions
						if source, ok := volume["source"].(string); ok {
							if target, ok := volume["target"].(string); ok {
								volumeStr := fmt.Sprintf("%s:%s", source, target)
								volumes = append(volumes, volumeStr)
							}
						}
					}
				}
			}
		}
		serviceVolumes[serviceName] = volumes

		// TODO: Extract proxy-body-size from each service
		proxyBodySizes[serviceName] = nil
	}

	// Log collected information
	for service, ports := range servicePorts {
		span.AddEvent("Service ports", trace.WithAttributes(
			attribute.String("service", service),
			attribute.StringSlice("ports", ports),
		))
	}

	for service, volumes := range serviceVolumes {
		span.AddEvent("Service volumes", trace.WithAttributes(
			attribute.String("service", service),
			attribute.StringSlice("volumes", volumes),
		))
	}

	resp := &DockerComposeConfig{
		Ports:          servicePorts,
		Volumes:        serviceVolumes,
		ProxyBodySizes: proxyBodySizes,
	}

	return resp, nil
}

func ComposeDeploymentName(repoName, serviceName string) string {
	return utils.Slugify(fmt.Sprintf("%s-%s", repoName, serviceName))
}

func ComposeResourceName(deploymentName string) string {
	return fmt.Sprintf("compose-%s", deploymentName)
}

func ComposeResourceNamespace(repoName string) string {
	return utils.Slugify(fmt.Sprintf("compose-%s", repoName))
}

func getDeploymentURL(deploymentName, domain string) string {
	return fmt.Sprintf("%s.%s", deploymentName, domain)
}

func (u *impl) StopComposeDeployment(ctx context.Context, repoID types.RepoID) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.kompose.StopComposeDeployment")
	defer span.End()

	repo, err := u.repo.FindRepository(ctx, repository.RepositoryFilter{
		Type:      repoID.RepoType(),
		Namespace: repoID.Namespace(),
		Name:      repoID.RepoName(),
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find repository")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return usecase.ErrRepositoryNotFound
		}
		return err
	}

	// delete manifests
	_, err = u.DeleteKubernetesManifests(ctx, DeleteKubernetesManifestsInput{
		Namespace:   ComposeResourceNamespace(repo.Name),
		DryRun:      false,
		DeleteAll:   true,
		ComposeRepo: repo,
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to delete Kubernetes manifests")
		span.RecordError(err)
		return err
	}

	return nil
}

func QueryDeploymentStatus(ctx context.Context, k8sClient kubernetes.Interface, serviceName, repoName string) (*dto.DeploymentStatus, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.kompose.queryDeploymentStatus")
	defer span.End()

	namespace := ComposeResourceNamespace(repoName)

	var status dto.DeploymentStatus
	existingDeployment, err := k8sClient.AppsV1().Deployments(namespace).Get(ctx, serviceName, metav1.GetOptions{})
	if err != nil {
		if kubeerrors.IsNotFound(err) {
			status.Status = "Not Running"
			return &status, nil
		}

		span.SetStatus(codes.Error, "failed to find k8s deployment")
		span.RecordError(err)
		return nil, fmt.Errorf("failed to find k8s deployment: %w", err)
	}
	_ = existingDeployment

	podLabelSelector := fmt.Sprintf("app=%s", ComposeResourceName(ComposeDeploymentName(repoName, serviceName)))
	span.SetAttributes(attribute.String("pod_label_selector", podLabelSelector))

	podList, err := k8sClient.CoreV1().
		Pods(namespace).
		List(ctx, metav1.ListOptions{
			LabelSelector: podLabelSelector,
		})
	if err != nil {
		span.SetStatus(codes.Error, "failed to list pods")
		span.RecordError(err)
		return nil, err
	}

	// if there is no pod or container, just return Scheduling while waiting for pod to be scheduled by k8s
	if len(podList.Items) == 0 {
		status.Status = "Scheduling"
	} else {
		pod := podList.Items[0]
		containerStatuses := pod.Status.ContainerStatuses
		if len(containerStatuses) == 0 {
			status.Status = "Scheduling"
		} else {
			containerState := containerStatuses[0].State
			dto.MapContainerStatus(&containerState, &status)
		}
	}

	return &status, nil
}
