package kompose

var dockerComposeContent = `
 services:
   weba-c_b:
     ports:
       - "8000:80"
     environment:
       - ENV=production
     labels:
       kompose.service.expose: true
       kompose.service.expose.ingress-class-name: "nginx"

   postgres-a:
     image: postgres:16
     environment:
       POSTGRES_USER: myuser
       POSTGRES_PASSWORD: mypassword
       POSTGRES_DB: mydb
     ports:
       - "5432:5432"
       - "5431:5432"
     volumes:
       - pgdata:/var/lib/postgresql/data
     labels:
       kompose.service.type: "ClusterIP"
       kompose.volume.size: "1Gi"

   redisa-b_c:
     image: redis:alpine
     ports:
       - "6379:6379"
       - "6666:6379"

 volumes:
   pgdata: {}
 `
