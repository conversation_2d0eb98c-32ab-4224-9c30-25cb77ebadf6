# Kompose Usecase

This package provides functionality to convert Docker Compose files to Kubernetes manifests and apply them to a Kubernetes cluster using the Kompose library.

## Features

1. **Convert Docker Compose to Kubernetes**: Convert docker-compose.yaml content to Kubernetes manifests
2. **Apply to Cluster**: Apply the generated Kubernetes manifests directly to a Kubernetes cluster

## Usage

### 1. Convert Docker Compose to Kubernetes Manifests

```go
package main

import (
    "context"
    "fmt"
    "log"

    "api-server/configs"
    "api-server/internal/dto"
    "api-server/internal/usecase/kompose"
    "api-server/internal/repositories"
)

func main() {
    // Initialize dependencies
    config := &configs.GlobalConfig{}
    repo := repositories.New(/* your repo dependencies */)

    // Create kompose usecase (without k8s client for conversion only)
    komposeUC := kompose.New(config, repo, nil)

    // Docker compose content
    dockerComposeContent := `version: '3'
services:
  web:
    image: nginx:latest
    ports:
      - "80:80"
    environment:
      - ENV=production
  redis:
    image: redis:alpine
    ports:
      - "6379:6379"`

    // Convert to Kubernetes manifests
    input := dto.ConvertDockerComposeInput{
        DockerComposeContent: dockerComposeContent,
        Namespace:           "default",
        Replicas:            2,
        VolumeType:          "persistentVolumeClaim",
    }

    response, err := komposeUC.ConvertDockerComposeToKubernetes(context.Background(), input)
    if err != nil {
        log.Fatalf("Failed to convert: %v", err)
    }

    fmt.Printf("Converted %d services into %d manifests\n",
        response.Summary.TotalServices, len(response.Manifests))

    for _, manifest := range response.Manifests {
        fmt.Printf("Generated %s: %s\n", manifest.Kind, manifest.Name)
        fmt.Println("---")
        fmt.Println(manifest.Content)
        fmt.Println("---")
    }
}
```

### 2. Apply Kubernetes Manifests to Cluster

```go
package main

import (
    "context"
    "fmt"
    "log"

    "api-server/configs"
    "api-server/internal/dto"
    "api-server/internal/usecase/kompose"
    "api-server/internal/repositories"

    "k8s.io/client-go/kubernetes"
    "k8s.io/client-go/tools/clientcmd"
)

func main() {
    // Initialize Kubernetes client
    config, err := clientcmd.BuildConfigFromFlags("", "/path/to/kubeconfig")
    if err != nil {
        log.Fatalf("Failed to build k8s config: %v", err)
    }

    k8sClient, err := kubernetes.NewForConfig(config)
    if err != nil {
        log.Fatalf("Failed to create k8s client: %v", err)
    }

    // Initialize dependencies
    appConfig := &configs.GlobalConfig{}
    repo := repositories.New(/* your repo dependencies */)

    // Create kompose usecase with k8s client
    komposeUC := kompose.New(appConfig, repo, k8sClient)

    // First convert docker-compose to manifests
    convertInput := dto.ConvertDockerComposeInput{
        DockerComposeContent: dockerComposeContent,
        Namespace:           "default",
        Replicas:            1,
    }

    convertResponse, err := komposeUC.ConvertDockerComposeToKubernetes(context.Background(), convertInput)
    if err != nil {
        log.Fatalf("Failed to convert: %v", err)
    }

    // Apply manifests to cluster
    applyInput := dto.ApplyKubernetesManifestsInput{
        Manifests: convertResponse.Manifests,
        Namespace: "default",
        DryRun:    false, // Set to true for validation only
        Force:     false,
    }

    applyResponse, err := komposeUC.ApplyKubernetesManifests(context.Background(), applyInput)
    if err != nil {
        log.Fatalf("Failed to apply manifests: %v", err)
    }

    fmt.Printf("Applied %d/%d resources successfully\n",
        applyResponse.Summary.SuccessfulResources,
        applyResponse.Summary.TotalResources)

    for _, resource := range applyResponse.AppliedResources {
        fmt.Printf("%s: %s/%s - %s\n",
            resource.Action, resource.Kind, resource.Name, resource.Message)
    }

    if len(applyResponse.Errors) > 0 {
        fmt.Println("Errors:")
        for _, err := range applyResponse.Errors {
            fmt.Printf("  - %s\n", err)
        }
    }
}
```

## Supported Kubernetes Resources

The apply functionality currently supports the following Kubernetes resource types:

- **Deployment**: Creates or updates application deployments
- **Service**: Creates or updates services for network access
- **PersistentVolumeClaim**: Creates PVCs for persistent storage
- **ConfigMap**: Creates or updates configuration data
- **Secret**: Creates or updates sensitive data
- **Generic Resources**: Basic support for other resource types

## Configuration Options

### ConvertDockerComposeInput

- `DockerComposeContent`: The docker-compose.yaml file content (required)
- `Namespace`: Target Kubernetes namespace (optional)
- `Replicas`: Number of replicas for deployments (default: 1)
- `GenerateNetworkPolicies`: Whether to generate network policies (default: false)
- `VolumeType`: Type of volumes to generate (`persistentVolumeClaim`, `emptyDir`, `hostPath`, `configMap`)
- `PVCRequestSize`: Size for PVC storage requests (e.g., "1Gi")

### ApplyKubernetesManifestsInput

- `Manifests`: Array of Kubernetes manifests to apply (required)
- `Namespace`: Override namespace for all resources (optional)
- `DryRun`: Perform validation only without applying (default: false)
- `Force`: Force apply by deleting and recreating if necessary (default: false)

## Error Handling

The package provides comprehensive error handling:

- Conversion errors are returned with detailed messages
- Apply operations collect errors per resource and continue processing
- Dry run mode allows validation without making changes
- Summary statistics help track success/failure rates

## Dependencies

- `github.com/kubernetes/kompose`: Core conversion functionality
- `k8s.io/client-go`: Kubernetes client library
- `k8s.io/api`: Kubernetes API types
- `k8s.io/apimachinery`: Kubernetes API machinery

## Notes

- The conversion uses Kompose's default settings with some customization options
- Apply operations are performed sequentially for each resource
- Existing resources are updated when possible, created when they don't exist
- PersistentVolumeClaims are generally immutable after creation
- Network policies and other advanced features depend on cluster capabilities
