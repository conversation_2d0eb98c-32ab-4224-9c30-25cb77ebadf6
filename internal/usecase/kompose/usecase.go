package kompose

import (
	"context"

	"api-server/configs"
	"api-server/internal/dto"
	"api-server/internal/gateways/gitlab"
	repository "api-server/internal/repositories"
	"api-server/internal/types"

	"github.com/google/uuid"
	"k8s.io/client-go/kubernetes"
)

// KomposeUsecase defines operations for converting docker-compose files to Kubernetes manifests
type KomposeUsecase interface {
	// ConvertDockerComposeToKubernetes converts a docker-compose.yaml file content to Kubernetes manifests
	ConvertDockerComposeToKubernetes(ctx context.Context, input ConvertDockerComposeInput) (*ConvertDockerComposeResponse, error)

	// ApplyKubernetesManifests applies the generated Kubernetes manifests to a cluster
	CreateComposeDeployment(ctx context.Context, userID uuid.UUID, repoID types.RepoID, input dto.StartDeploymentRequest) (*dto.StartDeploymentResponse, error)

	// ApplyKubernetesManifests applies the generated Kubernetes manifests to a cluster
	ApplyKubernetesManifests(ctx context.Context, input ApplyKubernetesManifestsInput) (*ApplyKubernetesManifestsResponse, error)

	// DeleteKubernetesManifests deletes the Kubernetes resources that were previously applied
	DeleteKubernetesManifests(ctx context.Context, input DeleteKubernetesManifestsInput) (*DeleteKubernetesManifestsResponse, error)

	StopComposeDeployment(ctx context.Context, repoID types.RepoID) error
}

type impl struct {
	config    *configs.GlobalConfig
	repo      repository.Repository
	k8sClient kubernetes.Interface
	gitlab    gitlab.GitlabClient
}

var _ KomposeUsecase = (*impl)(nil)

// New creates a new instance of KomposeUsecase
func New(
	config *configs.GlobalConfig,
	repository repository.Repository,
	k8sClient kubernetes.Interface,
	gitlab gitlab.GitlabClient,
) KomposeUsecase {
	return &impl{
		config:    config,
		repo:      repository,
		k8sClient: k8sClient,
		gitlab:    gitlab,
	}
}
