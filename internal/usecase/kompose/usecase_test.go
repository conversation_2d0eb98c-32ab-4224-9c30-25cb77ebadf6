package kompose_test

// import (
// 	"context"
// 	"testing"
//
// 	"github.com/stretchr/testify/assert"
//
// 	"api-server/configs"
// 	"api-server/internal/dto"
// 	repoMocks "api-server/internal/repositories/mocks"
// 	"api-server/internal/usecase/kompose"
// )
//
// func TestConvertDockerComposeToKubernetes(t *testing.T) {
// 	type dependencies struct {
// 		repo *repoMocks.MockRepository
// 	}
//
// 	tests := []struct {
// 		name           string
// 		input          dto.ConvertDockerComposeInput
// 		setupMocks     func(*dependencies)
// 		expectedError  string
// 		validateResult func(*testing.T, *dto.ConvertDockerComposeResponse)
// 	}{
// 		{
// 			name: "successful conversion with simple docker-compose",
// 			input: dto.ConvertDockerComposeInput{
// 				DockerComposeContent: `version: '3'
// services:
//   web:
//     image: nginx:latest
//     ports:
//       - "80:80"
//     environment:
//       - ENV=production
//   redis:
//     image: redis:alpine
//     ports:
//       - "6379:6379"`,
// 				Namespace:  "default",
// 				Replicas:   2,
// 				VolumeType: "persistentVolumeClaim",
// 			},
// 			setupMocks: func(d *dependencies) {
// 				// No repository calls needed for this conversion
// 			},
// 			expectedError: "",
// 			validateResult: func(t *testing.T, result *dto.ConvertDockerComposeResponse) {
// 				assert.NotNil(t, result)
// 				assert.NotEmpty(t, result.Manifests)
// 				assert.True(t, result.Summary.TotalServices > 0)
// 				assert.NotEmpty(t, result.Summary.GeneratedResources)
//
// 				// Check that we have some expected resource types
// 				hasDeployment := false
// 				hasService := false
// 				for _, manifest := range result.Manifests {
// 					if manifest.Kind == "Deployment" {
// 						hasDeployment = true
// 						assert.Contains(t, manifest.Content, "nginx")
// 						assert.Contains(t, manifest.Content, "replicas: 2")
// 					}
// 					if manifest.Kind == "Service" {
// 						hasService = true
// 						assert.Contains(t, manifest.Content, "port: 80")
// 					}
// 				}
// 				assert.True(t, hasDeployment, "Should have at least one Deployment")
// 				assert.True(t, hasService, "Should have at least one Service")
// 			},
// 		},
// 		{
// 			name: "successful conversion with volumes",
// 			input: dto.ConvertDockerComposeInput{
// 				DockerComposeContent: `version: '3'
// services:
//   app:
//     image: myapp:latest
//     ports:
//       - "8080:8080"
//     volumes:
//       - app-data:/data
// volumes:
//   app-data:`,
// 				VolumeType:     "persistentVolumeClaim",
// 				PVCRequestSize: "5Gi",
// 			},
// 			setupMocks: func(d *dependencies) {
// 				// No repository calls needed
// 			},
// 			expectedError: "",
// 			validateResult: func(t *testing.T, result *dto.ConvertDockerComposeResponse) {
// 				assert.NotNil(t, result)
// 				assert.NotEmpty(t, result.Manifests)
//
// 				// Check for PVC
// 				hasPVC := false
// 				for _, manifest := range result.Manifests {
// 					if manifest.Kind == "PersistentVolumeClaim" {
// 						hasPVC = true
// 						assert.Contains(t, manifest.Content, "5Gi")
// 					}
// 				}
// 				assert.True(t, hasPVC, "Should have PersistentVolumeClaim when volumes are defined")
// 			},
// 		},
// 		{
// 			name: "invalid docker-compose content",
// 			input: dto.ConvertDockerComposeInput{
// 				DockerComposeContent: `invalid yaml content
// this is not valid yaml: [`,
// 			},
// 			setupMocks: func(d *dependencies) {
// 				// No repository calls needed
// 			},
// 			expectedError: "invalid docker-compose file",
// 		},
// 		{
// 			name: "empty docker-compose content",
// 			input: dto.ConvertDockerComposeInput{
// 				DockerComposeContent: "",
// 			},
// 			setupMocks: func(d *dependencies) {
// 				// No repository calls needed
// 			},
// 			expectedError: "invalid docker-compose file",
// 		},
// 		{
// 			name: "docker-compose with network policies",
// 			input: dto.ConvertDockerComposeInput{
// 				DockerComposeContent: `version: '3'
// services:
//   frontend:
//     image: nginx:latest
//     ports:
//       - "80:80"
//   backend:
//     image: node:alpine
//     ports:
//       - "3000:3000"`,
// 				GenerateNetworkPolicies: true,
// 			},
// 			setupMocks: func(d *dependencies) {
// 				// No repository calls needed
// 			},
// 			expectedError: "",
// 			validateResult: func(t *testing.T, result *dto.ConvertDockerComposeResponse) {
// 				assert.NotNil(t, result)
// 				assert.NotEmpty(t, result.Manifests)
//
// 				// When network policies are enabled, we should have more resources
// 				assert.True(t, len(result.Manifests) > 2, "Should have multiple manifests including network policies")
// 			},
// 		},
// 		{
// 			name: "docker-compose with custom namespace",
// 			input: dto.ConvertDockerComposeInput{
// 				DockerComposeContent: `version: '3'
// services:
//   app:
//     image: myapp:latest
//     ports:
//       - "8080:8080"`,
// 				Namespace: "custom-namespace",
// 			},
// 			setupMocks: func(d *dependencies) {
// 				// No repository calls needed
// 			},
// 			expectedError: "",
// 			validateResult: func(t *testing.T, result *dto.ConvertDockerComposeResponse) {
// 				assert.NotNil(t, result)
// 				assert.NotEmpty(t, result.Manifests)
//
// 				// Check that namespace is set in manifests
// 				for _, manifest := range result.Manifests {
// 					assert.Contains(t, manifest.Content, "namespace: custom-namespace")
// 				}
// 			},
// 		},
// 	}
//
// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			// Setup mocks
// 			mockRepo := repoMocks.NewMockRepository(t)
// 			dep := dependencies{
// 				repo: mockRepo,
// 			}
//
// 			tt.setupMocks(&dep)
//
// 			// Create usecase instance
// 			config := &configs.GlobalConfig{}
// 			usecase := kompose.New(config, dep.repo)
//
// 			// Execute test
// 			result, err := usecase.ConvertDockerComposeToKubernetes(context.Background(), tt.input)
//
// 			// Assertions
// 			if tt.expectedError != "" {
// 				assert.Error(t, err)
// 				assert.Contains(t, err.Error(), tt.expectedError)
// 				assert.Nil(t, result)
// 			} else {
// 				assert.NoError(t, err)
// 				assert.NotNil(t, result)
// 				if tt.validateResult != nil {
// 					tt.validateResult(t, result)
// 				}
// 			}
//
// 			// Verify all expectations
// 			mockRepo.AssertExpectations(t)
// 		})
// 	}
// }
//
// func TestConvertDockerComposeToKubernetes_EdgeCases(t *testing.T) {
// 	config := &configs.GlobalConfig{}
// 	mockRepo := repoMocks.NewMockRepository(t)
// 	usecase := kompose.New(config, mockRepo)
//
// 	t.Run("default values are applied correctly", func(t *testing.T) {
// 		input := dto.ConvertDockerComposeInput{
// 			DockerComposeContent: `version: '3'
// services:
//   app:
//     image: nginx:latest
//     ports:
//       - "80:80"`,
// 			// No replicas, namespace, or volume type specified
// 		}
//
// 		result, err := usecase.ConvertDockerComposeToKubernetes(context.Background(), input)
//
// 		assert.NoError(t, err)
// 		assert.NotNil(t, result)
//
// 		// Check that default values are applied
// 		for _, manifest := range result.Manifests {
// 			if manifest.Kind == "Deployment" {
// 				// Default replicas should be 1
// 				assert.Contains(t, manifest.Content, "replicas: 1")
// 			}
// 		}
// 	})
//
// 	t.Run("complex docker-compose with multiple services", func(t *testing.T) {
// 		input := dto.ConvertDockerComposeInput{
// 			DockerComposeContent: `version: '3.8'
// services:
//   web:
//     image: nginx:latest
//     ports:
//       - "80:80"
//     environment:
//       - NGINX_HOST=localhost
//       - NGINX_PORT=80
//     depends_on:
//       - api
//   api:
//     image: node:alpine
//     ports:
//       - "3000:3000"
//     environment:
//       - NODE_ENV=production
//       - DB_HOST=db
//     depends_on:
//       - db
//   db:
//     image: postgres:13
//     environment:
//       - POSTGRES_DB=myapp
//       - POSTGRES_USER=user
//       - POSTGRES_PASSWORD=password
//     volumes:
//       - db-data:/var/lib/postgresql/data
// volumes:
//   db-data:`,
// 			Replicas:   3,
// 			VolumeType: "persistentVolumeClaim",
// 		}
//
// 		result, err := usecase.ConvertDockerComposeToKubernetes(context.Background(), input)
//
// 		assert.NoError(t, err)
// 		assert.NotNil(t, result)
// 		assert.True(t, result.Summary.TotalServices >= 3, "Should have at least 3 services")
// 		assert.True(t, len(result.Manifests) >= 6, "Should have multiple manifests for deployments and services")
//
// 		// Verify we have the expected resource types
// 		resourceTypes := make(map[string]int)
// 		for _, manifest := range result.Manifests {
// 			resourceTypes[manifest.Kind]++
// 		}
//
// 		assert.True(t, resourceTypes["Deployment"] >= 3, "Should have at least 3 deployments")
// 		assert.True(t, resourceTypes["Service"] >= 3, "Should have at least 3 services")
// 		assert.True(t, resourceTypes["PersistentVolumeClaim"] >= 1, "Should have at least 1 PVC")
// 	})
// }
