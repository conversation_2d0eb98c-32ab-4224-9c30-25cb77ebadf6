package kompose

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestCheckKomposeLabels(t *testing.T) {
	tests := []struct {
		name          string
		content       string
		expectError   bool
		errorContains string
	}{
		{
			name: "valid docker-compose without kompose labels",
			content: `
services:
  web:
    image: nginx:latest
    ports:
      - "8080:80"
    labels:
      app: web
      version: "1.0"
`,
			expectError: false,
		},
		{
			name: "docker-compose with kompose labels in map format",
			content: `
services:
  web:
    image: nginx:latest
    ports:
      - "8080:80"
    labels:
      kompose.service.expose: true
      app: web
`,
			expectError:   true,
			errorContains: "kompose.service.expose",
		},
		{
			name: "docker-compose with kompose labels in array format",
			content: `
services:
  web:
    image: nginx:latest
    ports:
      - "8080:80"
    labels:
      - "kompose.service.type=ClusterIP"
      - "app=web"
`,
			expectError:   true,
			errorContains: "kompose.service.type",
		},
		{
			name: "docker-compose with kompose labels in plain array format",
			content: `
services:
  web:
    image: nginx:latest
    ports:
      - "8080:80"
    labels:
      - "kompose.volume.size"
      - "app"
`,
			expectError:   true,
			errorContains: "kompose.volume.size",
		},
		{
			name: "multiple services with kompose labels",
			content: `
services:
  web:
    image: nginx:latest
    labels:
      app: web
  db:
    image: postgres:13
    labels:
      kompose.service.expose.ingress-class-name: "nginx"
`,
			expectError:   true,
			errorContains: "kompose.service.expose.ingress-class-name",
		},
		{
			name: "service without labels",
			content: `
services:
  web:
    image: nginx:latest
    ports:
      - "8080:80"
`,
			expectError: false,
		},
		{
			name: "invalid YAML",
			content: `
services:
  web:
    image: nginx:latest
    labels: [
`,
			expectError:   true,
			errorContains: "invalid YAML syntax",
		},
		{
			name: "missing services section",
			content: `
version: "3.8"
volumes:
  data: {}
`,
			expectError:   true,
			errorContains: "missing required 'services' section",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := CheckKomposeLabels(context.Background(), CheckKomposeLabelsInput{
				DockerComposeContent: tt.content,
			})

			if tt.expectError {
				assert.Error(t, err)
				if tt.errorContains != "" {
					assert.Contains(t, err.Error(), tt.errorContains)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}
