package usecase

import "errors"

type UsecaseError error

var (
	ErrRecordNotFound                  UsecaseError = errors.New("Record not found")
	ErrInternal                                     = errors.New("Internal error")
	ErrSignUpRequestNotFound                        = errors.New("Sign up request not found")
	ErrUserNotFound                                 = errors.New("User not found")
	ErrRepositoryNotFound                           = errors.New("Repository not found")
	ErrOrganizationNotFound                         = errors.New("Organization not found")
	ErrDuplicatedOrganization                       = errors.New("Duplicated organization name or full name")
	ErrInvalidTime                                  = errors.New("Invalid time")
	ErrTimeIsPast                                   = errors.New("Time is past")
	ErrSendMail                                     = errors.New("Failed to send mail")
	ErrRepositoryExist                              = errors.New("Repository already exists")
	ErrEmailExist                                   = errors.New("Email already exists")
	ErrTokenExpired                                 = errors.New("Access token has expired")
	ErrInvalidToken                                 = errors.New("Invalid access token")
	ErrCreateWorkflow                               = errors.New("Create workflow error")
	ErrDeploymentRunning                            = errors.New("A deployment is running")
	ErrTerminateDeploymentNotRunning                = errors.New("Cannot stop deployment. The deployment is not currently running")
	ErrNoDeployment                                 = errors.New("No deployment")
	ErrNoPod                                        = errors.New("No pod")
	ErrNoContainer                                  = errors.New("No container")
	ErrUserHasBeenInvited                           = errors.New("User has been invited")
	ErrMemberAlreadyExistsInRepo                    = errors.New("User already exists in repo")
	ErrNoPermission                                 = errors.New("You do not have permission")
	ErrInviteOrgOwnerAsDeveloper                    = errors.New("Cannot invite organization owner as repository developer role")
	ErrNotSpaceRepo                                 = errors.New("Repository type is not space repository")
	ErrNotComposeRepo                               = errors.New("Repository type is not compose repository")
	ErrNotSpaceOrComposeRepo                        = errors.New("Repository type is not a space repository or compose repository")
	ErrNoHardware                                   = errors.New("No hardware is assigned to this repository")
	ErrHardwareNotFound                             = errors.New("Hardware not found")
	ErrDeploymentNotSucceeded                       = errors.New("Last deployment has not succeeded")
	ErrEmailHasBeenConfirmed                        = errors.New("Email has been confirmed")
	ErrEmailWaitAdminApproval                       = errors.New("Email is waiting admin approval")
	ErrCannotInviteUserAsOwner                      = errors.New("You cannot invite another user as an owner to this repository")
	ErrMemberNotFound                               = errors.New("Member not found")
	ErrCannotDeleteRootUser                         = errors.New("Cannot delete the root user")
	ErrMemberAlreadyExistsInOrg                     = errors.New("User already exists in organization")
	ErrCreateSpaceIngressResource                   = errors.New("Failed to create space Ingress resource")
	ErrGetSpaceIngressResource                      = errors.New("Failed to get space Ingress resource")
	ErrApplySpaceServiceResource                    = errors.New("Failed to apply space Service resource")
	ErrFailedToChangePassword                       = errors.New("Failed to change password")
	ErrCurrentPasswordIsIncorrect                   = errors.New("Current password is incorrect")
	ErrInputUserDisable                             = errors.New("The input user is currently disabled because they are marked for deletion")
	ErrCannotFindLastCommit                         = errors.New("Cannot find last commit")
	ErrDuplicatedSSHKey                             = errors.New("Duplicated SSH key")
	ErrCannotRemoveOwnerFromRepository              = errors.New("Cannot remove owner from repository")
	ErrFailedToTerminateArgoWorkflow                = errors.New("Fail to terminate workflow")
	ErrNodeNotExist                                 = errors.New("Node does not exist")
	ErrBranchNotFound                               = errors.New("Branch not found")
	ErrECRRepositoryNotFound                        = errors.New("ECR repository not found")
	ErrECRImageNotFound                             = errors.New("ECR image not found")
	ErrInvalidECRImageURI                           = errors.New("Invalid ECR image URI format")
	ErrGPUNodeNameRequired                          = errors.New("Node name is required for GPU deployments")
	ErrNoImageIdentifierProvided                    = errors.New("No image identifiers provided")
	ErrDeploymentNameAlreadyExists                  = errors.New("Deployment name already exists")
	ErrDockerComposeFileNotFound                    = errors.New("Missing docker-compose.yaml file")
)

// Common errors
var (
	ErrInvalidInput       = errors.New("invalid input")
	ErrNotFound           = errors.New("not found")
	ErrAlreadyExists      = errors.New("already exists")
	ErrUnauthorized       = errors.New("unauthorized")
	ErrForbidden          = errors.New("forbidden")
	ErrConflict           = errors.New("conflict")
	ErrPreconditionFailed = errors.New("precondition failed")
	ErrTooManyRequests    = errors.New("too many requests")
	ErrServiceUnavailable = errors.New("service unavailable")
)
