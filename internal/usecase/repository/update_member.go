package repository

import (
	"context"
	"errors"
	"time"

	"go.opentelemetry.io/otel/codes"
	"gorm.io/gorm"

	"api-server/internal/dto"
	"api-server/internal/enums"
	"api-server/internal/gateways/gitlab"
	repository "api-server/internal/repositories"
	"api-server/internal/usecase"
	"api-server/internal/utils"
	"api-server/pkg/oteltrace"
)

// UpdateMember implements the RepositoryUsecase interface for updating repository member information.
// It handles the process of updating a member's role and permissions in the repository.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: Input data containing repository ID, member ID, and updated role
//
// Returns:
//   - error: Any error that occurred during member update
func (i *impl) UpdateMember(ctx context.Context, input dto.UpdateMemberRepositoryInput) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.UpdateMember")
	defer span.End()

	if input.ExpireAt != nil && input.ExpireAt.Before(time.Now()) {
		span.SetStatus(codes.Error, usecase.ErrTimeIsPast.Error())
		span.RecordError(usecase.ErrTimeIsPast)
		return usecase.ErrTimeIsPast
	}

	currentUser, err := i.repo.FindUserByID(ctx, input.CurrentUserID)
	if err != nil {
		span.SetStatus(codes.Error, "failed to find user by id")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return usecase.ErrUserNotFound
		}
		return err
	}

	member, err := i.repo.FindUserByID(ctx, input.MemberId)
	if err != nil {
		span.SetStatus(codes.Error, "failed to find user by id")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return usecase.ErrUserNotFound
		}
		return err
	}

	repo, err := i.repo.FindRepository(ctx, repository.RepositoryFilter{
		Type:      input.RepoID.RepoType(),
		Namespace: input.RepoID.Namespace(),
		Name:      input.RepoID.RepoName(),
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find repository")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return usecase.ErrRepositoryNotFound
		}
		return err
	}
	// check whether repo belongs to the user or the organization
	isOwnedByUser := true
	if repo.OrgID != nil {
		isOwnedByUser = false
	}

	if isOwnedByUser && input.Role == enums.RepoRole_Owner {
		span.SetStatus(codes.Error, usecase.ErrNoPermission.Error())
		span.RecordError(usecase.ErrNoPermission)
		return usecase.ErrNoPermission
	}
	//get user permission accesstoken
	accessToken, err := getAccessToken(ctx, currentUser, *i, repo, span)
	if err != nil {
		return err
	}

	repoMember, err := i.repo.FindRepositoryMember(ctx, repository.FilterRepoMember{
		RepoID: &repo.ID,
		UserID: &member.ID,
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find repository member")
		span.RecordError(err)
		return err
	}

	role := gitlab.OrganizationAccessLevel_Developer
	if input.Role == enums.RepoRole_Owner {
		role = gitlab.OrganizationAccessLevel_Owner
	}

	gitlabMember, err := i.gitlab.GetMemberOfProject(ctx, gitlab.GetMemberOfProjectRequest{
		RepoId: repo.RefGitRepoID,
		UserId: member.RefGitUserID,
		Token:  accessToken,
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to get member in gitlab project")
		span.RecordError(err)
	}

	if gitlabMember != nil {
		_, err = i.gitlab.EditUserInProject(ctx, gitlab.EditUserInProjectRequest{
			RepoId:      repo.RefGitRepoID,
			UserId:      member.RefGitUserID,
			AccessLevel: role,
			Token:       accessToken,
			ExpiresAt:   utils.ConvertTimeToString(input.ExpireAt),
		})
		if err != nil {
			span.SetStatus(codes.Error, "failed to edit user in gitlab repository")
			span.RecordError(err)
			return err
		}
	}

	repoMember.Role = input.Role
	repoMember.ExpiresAt = input.ExpireAt
	err = i.repo.Save(ctx, repoMember)
	if err != nil {
		span.SetStatus(codes.Error, "failed to save repository member")
		span.RecordError(err)
		return err
	}

	span.AddEvent("update user in repository successfully")
	span.SetStatus(codes.Ok, "update member in repository successfully")
	return nil
}
