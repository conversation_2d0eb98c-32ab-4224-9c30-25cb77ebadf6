package repository

import (
	"context"
	"errors"
	"net/http"

	"api-server/internal/dto"
	"api-server/internal/enums"
	"api-server/internal/gateways/gitlab"
	repository "api-server/internal/repositories"
	"api-server/internal/usecase"
	"api-server/pkg/oteltrace"

	"go.opentelemetry.io/otel/codes"
	"gorm.io/gorm"
)

// ArchiveRepo implements the RepositoryUsecase interface for downloading a repository archive.
// It handles the process of retrieving a repository archive in the specified format (zip/tar).
//
// Parameters:
//   - ctx: Context for the operation
//   - input: Input data containing repository ID, archive type, and reference (branch/tag)
//
// Returns:
//   - *http.Response: HTTP response containing the archive data
//   - error: Any error that occurred during archive retrieval
func (u *impl) ArchiveRepo(ctx context.Context, input dto.ArchiveRepositoryInput) (*http.Response, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.ArchiveRepo")
	defer span.End()

	repo, err := u.repo.FindRepository(ctx, repository.RepositoryFilter{
		Type:      input.RepoID.RepoType(),
		Namespace: input.RepoID.Namespace(),
		Name:      input.RepoID.RepoName(),
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find repository")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, usecase.ErrRepositoryNotFound
		}
		return nil, err
	}
	//root user info
	adminRefGitID := enums.AdminRefGitID
	adminUser, err := u.repo.FindUser(ctx, repository.FindUserFilter{RefGitUserID: &adminRefGitID})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find admin user")
		span.RecordError(err)
		return nil, err
	}

	token := ""
	if adminUser.GitlabAccessToken != nil {
		token = *adminUser.GitlabAccessToken
	}

	ouput, err := u.gitlab.DownloadProject(ctx, gitlab.DownloadProjectRequest{
		Id:    repo.RefGitRepoID,
		Type:  input.Type,
		Token: token,
		Sha:   input.Ref,
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to download project from gitlab")
		span.RecordError(err)
		return nil, err
	}

	span.AddEvent("download repository successfully")
	span.SetStatus(codes.Ok, "download repository successfully")
	return ouput, nil
}
