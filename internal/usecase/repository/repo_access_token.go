package repository

import (
	"context"
	"errors"
	"time"

	"github.com/google/uuid"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"api-server/internal/dto"
	"api-server/internal/entities"
	"api-server/internal/gateways/gitlab"
	repository "api-server/internal/repositories"
	"api-server/internal/types"
	"api-server/internal/usecase"
	"api-server/pkg/oteltrace"
	"api-server/pkg/otelzap"
)

// CreateRepoAccessToken implements the RepositoryUsecase interface for creating a repository access token.
// It handles the process of creating a new access token for repository operations with specified scopes and expiration.
//
// Parameters:
//   - ctx: Context for the operation
//   - req: Input data containing repository ID, user ID, token name, scopes, and expiration time
//
// Returns:
//   - *dto.CreateRepoAccessTokenResponse: Created access token information
//   - error: Any error that occurred during token creation
func (i *impl) CreateRepoAccessToken(ctx context.Context, req dto.CreateRepoAccessTokenRequest) (*dto.CreateRepoAccessTokenResponse, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.CreateRepoAccessToken",
		trace.WithAttributes(
			attribute.String("action", "CREATE_REPO_ACCESS_TOKEN"),
			attribute.String("repo_id", req.RepoID.String()),
			attribute.String("user_id", req.UserID.String()),
		))
	defer span.End()

	otelzap.InfoWithContext(ctx, "creating repository access token",
		zap.String("action", "CREATE_REPO_ACCESS_TOKEN"),
		zap.String("repo_id", req.RepoID.String()),
		zap.String("user_id", req.UserID.String()))

	var result dto.CreateRepoAccessTokenResponse
	err := i.repo.Transaction(ctx, func(ctx context.Context) error {
		userInfo, err := i.repo.FindUserByID(ctx, req.UserID)
		if err != nil {
			span.SetStatus(codes.Error, "find user by id failed")
			span.RecordError(err)
			span.SetAttributes(attribute.String("status", "failed"))
			otelzap.ErrorWithContext(ctx, "find user by id failed", err,
				zap.String("action", "CREATE_REPO_ACCESS_TOKEN"),
				zap.String("repo_id", req.RepoID.String()),
				zap.String("user_id", req.UserID.String()),
				zap.String("status", "failed"))
			return err
		}

		repoInfo, err := i.repo.FindRepository(ctx, repository.RepositoryFilter{
			Type:      req.RepoID.RepoType(),
			Namespace: req.RepoID.Namespace(),
			Name:      req.RepoID.RepoName(),
		})
		if err != nil {
			span.SetStatus(codes.Error, "failed to find repository")
			span.RecordError(err)
			span.SetAttributes(attribute.String("status", "failed"))
			otelzap.ErrorWithContext(ctx, "failed to find repository", err,
				zap.String("action", "CREATE_REPO_ACCESS_TOKEN"),
				zap.String("repo_id", req.RepoID.String()),
				zap.String("user_id", req.UserID.String()),
				zap.String("status", "failed"))
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return usecase.ErrRepositoryNotFound
			}
			return err
		}

		_, err = i.repo.FindRepositoryMember(ctx, repository.FilterRepoMember{
			UserID: &userInfo.ID,
			RepoID: &repoInfo.ID,
		})
		if err != nil {
			span.SetStatus(codes.Error, "failed to find repository members")
			span.RecordError(err)
			span.SetAttributes(attribute.String("status", "failed"))
			otelzap.ErrorWithContext(ctx, "failed to find repository members", err,
				zap.String("action", "CREATE_REPO_ACCESS_TOKEN"),
				zap.String("repo_id", req.RepoID.String()),
				zap.String("user_id", req.UserID.String()),
				zap.String("status", "failed"))
			return err
		}

		userToken := ""
		if userInfo.GitlabAccessToken != nil {
			userToken = *userInfo.GitlabAccessToken
		}

		res, err := i.gitlab.CreateRepoPersonalAccessToken(ctx, gitlab.CreateProjectAccessTokenRequest{
			Name:      req.Name,
			RepoID:    repoInfo.RefGitRepoID,
			Scopes:    req.Scopes,
			UserToken: userToken,
			ExpiresAt: req.ExpiresAt.Format(time.RFC3339),
		})
		if err != nil {
			span.SetStatus(codes.Error, "failed to create personal access token")
			span.RecordError(err)
			span.SetAttributes(attribute.String("status", "failed"))
			otelzap.ErrorWithContext(ctx, "failed to create personal access token", err,
				zap.String("action", "CREATE_REPO_ACCESS_TOKEN"),
				zap.String("repo_id", req.RepoID.String()),
				zap.String("user_id", req.UserID.String()),
				zap.String("status", "failed"))
			return err
		}

		token, err := i.repo.CreateRepoAccessToken(ctx, repository.CreateRepoAccessTokenInput{
			Name:        req.Name,
			RepoID:      repoInfo.ID,
			AccessToken: res.Token,
			ExpiresAt:   req.ExpiresAt,
			Scopes:      req.Scopes,
			RefGitID:    res.Id,
		})
		if err != nil {
			span.SetStatus(codes.Error, "failed to create repository access token")
			span.RecordError(err)
			span.SetAttributes(attribute.String("status", "failed"))
			otelzap.ErrorWithContext(ctx, "failed to create repository access token", err,
				zap.String("action", "CREATE_REPO_ACCESS_TOKEN"),
				zap.String("repo_id", req.RepoID.String()),
				zap.String("user_id", req.UserID.String()),
				zap.String("status", "failed"))
			return usecase.ErrInternal
		}

		var data dto.RepoAccessToken
		data = data.FromEntity(*token)
		data.AccessToken = token.AccessToken
		result = dto.CreateRepoAccessTokenResponse{
			Data: &data,
		}

		return nil
	})

	if err != nil {
		span.SetStatus(codes.Error, "create repository access token transaction failed")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "create repository access token transaction failed", err,
			zap.String("action", "CREATE_REPO_ACCESS_TOKEN"),
			zap.String("repo_id", req.RepoID.String()),
			zap.String("user_id", req.UserID.String()),
			zap.String("status", "failed"))
		return nil, err
	}

	span.AddEvent("repository access token created successfully")
	span.SetStatus(codes.Ok, "repository access token created successfully")
	span.SetAttributes(attribute.String("status", "success"))
	otelzap.InfoWithContext(ctx, "repository access token created successfully",
		zap.String("action", "CREATE_REPO_ACCESS_TOKEN"),
		zap.String("repo_id", req.RepoID.String()),
		zap.String("user_id", req.UserID.String()),
		zap.String("status", "success"))
	return &result, nil
}

// DeleteRepoAccessToken implements the RepositoryUsecase interface for removing a repository access token.
// It handles the process of deleting an existing access token and its associated GitLab project access token.
//
// Parameters:
//   - ctx: Context for the operation
//   - userID: ID of the user performing the deletion
//   - repoID: ID of the repository
//   - accessTokenID: ID of the access token to be deleted
//
// Returns:
//   - error: Any error that occurred during token deletion
func (i *impl) DeleteRepoAccessToken(ctx context.Context, userID uuid.UUID, repoID types.RepoID, accessTokenID uuid.UUID) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.DeleteRepoAccessToken",
		trace.WithAttributes(
			attribute.String("action", "DELETE_REPO_ACCESS_TOKEN"),
			attribute.String("access_token_id", accessTokenID.String()),
			attribute.String("repo_id", repoID.String()),
			attribute.String("user_id", userID.String()),
		))
	defer span.End()

	otelzap.InfoWithContext(ctx, "deleting repository access token",
		zap.String("action", "DELETE_REPO_ACCESS_TOKEN"),
		zap.String("access_token_id", accessTokenID.String()),
		zap.String("repo_id", repoID.String()),
		zap.String("user_id", userID.String()))

	err := i.repo.Transaction(ctx, func(ctx context.Context) error {
		userInfo, err := i.repo.FindUserByID(ctx, userID)
		if err != nil {
			span.SetStatus(codes.Error, "find user by id failed")
			span.RecordError(err)
			span.SetAttributes(attribute.String("status", "failed"))
			otelzap.ErrorWithContext(ctx, "find user by id failed", err,
				zap.String("action", "DELETE_REPO_ACCESS_TOKEN"),
				zap.String("access_token_id", accessTokenID.String()),
				zap.String("repo_id", repoID.String()),
				zap.String("user_id", userID.String()),
				zap.String("status", "failed"))
			return err
		}

		repoInfo, err := i.repo.FindRepository(ctx, repository.RepositoryFilter{
			Type:      repoID.RepoType(),
			Namespace: repoID.Namespace(),
			Name:      repoID.RepoName(),
		})
		if err != nil {
			span.SetStatus(codes.Error, "failed to find repository")
			span.RecordError(err)
			span.SetAttributes(attribute.String("status", "failed"))
			otelzap.ErrorWithContext(ctx, "failed to find repository", err,
				zap.String("action", "DELETE_REPO_ACCESS_TOKEN"),
				zap.String("access_token_id", accessTokenID.String()),
				zap.String("repo_id", repoID.String()),
				zap.String("user_id", userID.String()),
				zap.String("status", "failed"))
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return usecase.ErrRepositoryNotFound
			}
			return err
		}

		_, err = i.repo.FindRepositoryMember(ctx, repository.FilterRepoMember{
			UserID: &userInfo.ID,
			RepoID: &repoInfo.ID,
		})
		if err != nil {
			span.SetStatus(codes.Error, "failed to find repository members")
			span.RecordError(err)
			span.SetAttributes(attribute.String("status", "failed"))
			otelzap.ErrorWithContext(ctx, "failed to find repository members", err,
				zap.String("action", "DELETE_REPO_ACCESS_TOKEN"),
				zap.String("access_token_id", accessTokenID.String()),
				zap.String("repo_id", repoID.String()),
				zap.String("user_id", userID.String()),
				zap.String("status", "failed"))
			return err
		}

		accessToken, err := i.repo.FindRepoAccessToken(ctx, repository.FindRepoAccessTokenQuery{
			ID: accessTokenID,
		})
		if err != nil {
			span.SetStatus(codes.Error, "failed to find repository access token")
			span.RecordError(err)
			span.SetAttributes(attribute.String("status", "failed"))
			otelzap.ErrorWithContext(ctx, "failed to find repository access token", err,
				zap.String("action", "DELETE_REPO_ACCESS_TOKEN"),
				zap.String("access_token_id", accessTokenID.String()),
				zap.String("repo_id", repoID.String()),
				zap.String("user_id", userID.String()),
				zap.String("status", "failed"))
			return err
		}

		userToken := ""
		if userInfo.GitlabAccessToken != nil {
			userToken = *userInfo.GitlabAccessToken
		}

		err = i.gitlab.DeleteRepoPersonalAccessToken(ctx, gitlab.DeleteProjectAccessTokenRequest{
			RepoID:    repoInfo.RefGitRepoID,
			UserToken: userToken,
			ID:        accessToken.RefGitID,
		})
		if err != nil {
			span.SetStatus(codes.Error, "failed to delete repository personal access token")
			span.RecordError(err)
			span.SetAttributes(attribute.String("status", "failed"))
			otelzap.ErrorWithContext(ctx, "failed to delete repository personal access token", err,
				zap.String("action", "DELETE_REPO_ACCESS_TOKEN"),
				zap.String("access_token_id", accessTokenID.String()),
				zap.String("repo_id", repoID.String()),
				zap.String("user_id", userID.String()),
				zap.String("status", "failed"))
			return err
		}

		err = i.repo.DeleteRepoAccessToken(ctx, repository.DeleteRepoAccessTokenInput{
			AccessTokenID: accessTokenID,
			RepoID:        repoInfo.ID,
		})
		if err != nil {
			span.SetStatus(codes.Error, "failed to delete repository access token")
			span.RecordError(err)
			span.SetAttributes(attribute.String("status", "failed"))
			otelzap.ErrorWithContext(ctx, "failed to delete repository access token", err,
				zap.String("action", "DELETE_REPO_ACCESS_TOKEN"),
				zap.String("access_token_id", accessTokenID.String()),
				zap.String("repo_id", repoID.String()),
				zap.String("user_id", userID.String()),
				zap.String("status", "failed"))
			return usecase.ErrInternal
		}

		span.SetStatus(codes.Ok, "repository access token deleted successfully")
		return nil
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to delete repository access token transaction")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "failed to delete repository access token transaction", err,
			zap.String("action", "DELETE_REPO_ACCESS_TOKEN"),
			zap.String("access_token_id", accessTokenID.String()),
			zap.String("repo_id", repoID.String()),
			zap.String("user_id", userID.String()),
			zap.String("status", "failed"))
		return err
	}

	span.AddEvent("repository access token deleted successfully")
	span.SetStatus(codes.Ok, "repository access token deleted successfully")
	span.SetAttributes(attribute.String("status", "success"))
	otelzap.InfoWithContext(ctx, "repository access token deleted successfully",
		zap.String("action", "DELETE_REPO_ACCESS_TOKEN"),
		zap.String("access_token_id", accessTokenID.String()),
		zap.String("repo_id", repoID.String()),
		zap.String("user_id", userID.String()),
		zap.String("status", "success"))
	return nil
}

// ListRepoAccessToken implements the RepositoryUsecase interface for retrieving repository access tokens.
// It fetches a list of access tokens associated with a repository with pagination support.
//
// Parameters:
//   - ctx: Context for the operation
//   - req: Input data containing repository ID and pagination parameters
//
// Returns:
//   - *dto.ListRepoAccessTokenResponse: List of access token information with pagination
//   - error: Any error that occurred during retrieval
func (i *impl) ListRepoAccessToken(ctx context.Context, req dto.ListRepoAccessTokenRequest) (*dto.ListRepoAccessTokenResponse, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.ListRepoAccessToken",
		trace.WithAttributes(
			attribute.String("action", "LIST_REPO_ACCESS_TOKENS"),
			attribute.String("repo_id", req.RepoID.String()),
		))
	defer span.End()

	otelzap.InfoWithContext(ctx, "listing repository access tokens",
		zap.String("action", "LIST_REPO_ACCESS_TOKENS"),
		zap.String("repo_id", req.RepoID.String()))

	repo, err := i.repo.FindRepository(ctx, repository.RepositoryFilter{
		Type:      req.RepoID.RepoType(),
		Namespace: req.RepoID.Namespace(),
		Name:      req.RepoID.RepoName(),
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find repository")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "failed to find repository", err,
			zap.String("action", "LIST_REPO_ACCESS_TOKENS"),
			zap.String("repo_id", req.RepoID.String()),
			zap.String("status", "failed"))
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, usecase.ErrRepositoryNotFound
		}
		return nil, err
	}

	pred := repository.ListRepoAccessTokenQuery{
		RepoID: repo.ID,
	}

	total, err := i.repo.CountRepoAccessToken(ctx, pred)
	if err != nil {
		span.SetStatus(codes.Error, "failed to counter repository access token")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "failed to counter repository access token", err,
			zap.String("action", "LIST_REPO_ACCESS_TOKENS"),
			zap.String("repo_id", req.RepoID.String()),
			zap.String("status", "failed"))
		return nil, usecase.ErrInternal
	}

	pagination := types.Pagination{
		PageNo:   req.Paginate.Page,
		PageSize: req.Paginate.PerPage,
	}

	orderBy := types.OrderBy{req.Paginate.OrderBy: req.Paginate.Sort}
	resp, err := i.repo.ListRepoAccessToken(ctx, pagination, orderBy, pred)
	if err != nil {
		span.SetStatus(codes.Error, "failed to list repository access token")
		span.RecordError(err)
		span.SetAttributes(attribute.String("status", "failed"))
		otelzap.ErrorWithContext(ctx, "failed to list repository access token", err,
			zap.String("action", "LIST_REPO_ACCESS_TOKENS"),
			zap.String("repo_id", req.RepoID.String()),
			zap.String("status", "failed"))
		return nil, usecase.ErrInternal
	}

	data := dto.FromManyEntities[entities.RepoAccessToken, dto.RepoAccessToken](resp)
	result := dto.ListRepoAccessTokenResponse{
		Data: &data,
		Pagination: &dto.Pagination{
			Total:    int(total),
			PageNo:   pagination.PageNo,
			PageSize: pagination.PageSize,
		},
	}

	span.AddEvent("repository access tokens listed successfully")
	span.SetStatus(codes.Ok, "repository access tokens listed successfully")
	span.SetAttributes(attribute.String("status", "success"))
	otelzap.InfoWithContext(ctx, "repository access tokens listed successfully",
		zap.String("action", "LIST_REPO_ACCESS_TOKENS"),
		zap.String("repo_id", req.RepoID.String()),
		zap.String("status", "success"))
	return &result, nil
}
