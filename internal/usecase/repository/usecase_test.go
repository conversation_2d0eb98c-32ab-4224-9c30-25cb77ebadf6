package repository_test

import (
	"bytes"
	"context"
	"errors"
	"io"
	"mime/multipart"
	"net/http"
	"strings"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"gorm.io/gorm"
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/kubernetes/fake"
	kubetesting "k8s.io/client-go/testing"

	"api-server/configs"
	"api-server/internal/dto"
	"api-server/internal/entities"
	"api-server/internal/enums"
	aws_mocks "api-server/internal/gateways/aws/mocks"
	"api-server/internal/gateways/gitlab"
	gitlab_mocks "api-server/internal/gateways/gitlab/mocks"
	repository_mocks "api-server/internal/repositories/mocks"
	"api-server/internal/types"
	"api-server/internal/usecase"
	"api-server/internal/usecase/repository"
	workflow_mocks "api-server/internal/usecase/workflow/mocks"
	"api-server/internal/utils"
)

func TestCreateRepository(t *testing.T) {
	uuidInput := uuid.New()
	uuidString := uuid.New()
	_ = uuidString
	type dependencies struct {
		config     *configs.GlobalConfig
		repo       *repository_mocks.MockRepository
		gitlab     *gitlab_mocks.MockGitlabClient
		workflow   *workflow_mocks.MockWorkflowUsecase
		kubeClient kubernetes.Interface
	}

	tests := []struct {
		name     string
		ctx      context.Context
		mockFn   func(d *dependencies)
		input    dto.RepositoryCreateInput
		expError error
	}{
		{
			name: "should return error if FindUserByID fail",
			ctx:  context.TODO(),
			input: dto.RepositoryCreateInput{
				CurrentUserId: uuidInput,
				Name:          "test",
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error if FindUserByID not found",
			ctx:  context.TODO(),
			input: dto.RepositoryCreateInput{
				CurrentUserId: uuidInput,
				Name:          "test",
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(nil, gorm.ErrRecordNotFound)
			},
			expError: usecase.ErrUserNotFound,
		},
		{
			name: "should return error if FindRepository fail",
			ctx:  context.TODO(),
			input: dto.RepositoryCreateInput{
				CurrentUserId: uuidInput,
				Name:          "test",
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error if FindOrganization fail",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.RepositoryCreateInput{
				CurrentUserId: uuidInput,
				Name:          "test",
				Owner: dto.RepositoryOwner{
					Type: enums.RepoOwnerType_Org,
					ID:   uuidString,
				},
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindOrganization", mock.Anything, mock.Anything, mock.Anything).Return(nil, errors.New("error"))
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil)
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, nil)
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error if FindOrganization fail",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.RepositoryCreateInput{
				CurrentUserId: uuidInput,
				Name:          "test",
				Owner: dto.RepositoryOwner{
					Type: enums.RepoOwnerType_Org,
					ID:   uuidString,
				},
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindOrganization", mock.Anything, mock.Anything, mock.Anything).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything, mock.Anything).Return(nil, errors.New("error"))
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, nil)
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error if error usecase.ErrNoPermission",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.RepositoryCreateInput{
				CurrentUserId: uuidInput,
				Name:          "test",
				Owner: dto.RepositoryOwner{
					Type: enums.RepoOwnerType_Org,
					ID:   uuidString,
				},
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindOrganization", mock.Anything, mock.Anything, mock.Anything).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Developer}, nil)
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, nil)
				d.repo.On("********************", mock.Anything, uuidString).Return(nil, errors.New("error"))
			},
			expError: usecase.ErrNoPermission,
		},
		{
			name: "should return error if FindRepository fail",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.RepositoryCreateInput{
				CurrentUserId: uuidInput,
				Name:          "test",
				Owner: dto.RepositoryOwner{
					Type: enums.RepoOwnerType_Org,
					ID:   uuidString,
				},
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindOrganization", mock.Anything, mock.Anything, mock.Anything).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil)
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error if repository already exists",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.RepositoryCreateInput{
				CurrentUserId: uuidInput,
				Name:          "test",
				Owner: dto.RepositoryOwner{
					Type: enums.RepoOwnerType_Org,
					ID:   uuidString,
				},
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindOrganization", mock.Anything, mock.Anything, mock.Anything).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil)
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{}, nil)
			},
			expError: usecase.ErrRepositoryExist,
		},
		{
			name: "should return error if ******************** fail",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.RepositoryCreateInput{
				CurrentUserId: uuidInput,
				Name:          "test",
				Owner: dto.RepositoryOwner{
					Type: enums.RepoOwnerType_Org,
					ID:   uuidString,
				},
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindOrganization", mock.Anything, mock.Anything, mock.Anything).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil)
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, nil)
				d.repo.On("********************", mock.Anything, uuidString).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error if ******************** not found",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.RepositoryCreateInput{
				CurrentUserId: uuidInput,
				Name:          "test",
				Owner: dto.RepositoryOwner{
					Type: enums.RepoOwnerType_Org,
					ID:   uuidString,
				},
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindOrganization", mock.Anything, mock.Anything, mock.Anything).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil)
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, nil)
				d.repo.On("********************", mock.Anything, uuidString).Return(nil, gorm.ErrRecordNotFound)
			},
			expError: usecase.ErrOrganizationNotFound,
		},
		{
			name: "should return error if ******************** not found",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.RepositoryCreateInput{
				CurrentUserId: uuidInput,
				Name:          "test",
				Owner: dto.RepositoryOwner{
					Type: enums.RepoOwnerType_Org,
					ID:   uuidString,
				},
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindOrganization", mock.Anything, mock.Anything, mock.Anything).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil)
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, nil)
				d.repo.On("********************", mock.Anything, uuidString).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgGitGroup", mock.Anything, uuidString).Return(nil, errors.New("error"))

			},
			expError: errors.New("error"),
		},
		{
			name: "should return error if FindUserGitGroup fail",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.RepositoryCreateInput{
				CurrentUserId: uuidInput,
				Name:          "test",
				Owner: dto.RepositoryOwner{
					Type: enums.RepoOwnerType_User,
					ID:   uuidInput,
				},
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindOrganization", mock.Anything, mock.Anything, mock.Anything).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil)
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, nil)
				d.repo.On("FindUserGitGroup", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error if Create fail",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.RepositoryCreateInput{
				CurrentUserId: uuidInput,
				Name:          "test",
				Owner: dto.RepositoryOwner{
					Type: enums.RepoOwnerType_User,
					ID:   uuidInput,
				},
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindOrganization", mock.Anything, mock.Anything, mock.Anything).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil)
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, nil)
				d.repo.On("FindUserGitGroup", mock.Anything, mock.Anything).Return(nil, nil)
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(context.Background())
				}).Return(errors.New("error"))
				d.repo.On("Create", mock.Anything, mock.Anything).Return(errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error if CreateProject fail",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.RepositoryCreateInput{
				CurrentUserId: uuidInput,
				Name:          "test",
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindOrganization", mock.Anything, mock.Anything, mock.Anything).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil)
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, nil)
				d.repo.On("FindUserGitGroup", mock.Anything, mock.Anything).Return(nil, nil)
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(context.Background())
				}).Return(errors.New("error"))
				d.repo.On("Create", mock.Anything, mock.Anything).Return(nil)
				d.gitlab.On("CreateProject", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error if Save fail",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.RepositoryCreateInput{
				CurrentUserId: uuidInput,
				Name:          "test",
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindOrganization", mock.Anything, mock.Anything, mock.Anything).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil)
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, nil)
				d.repo.On("FindUserGitGroup", mock.Anything, mock.Anything).Return(nil, nil)
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(context.Background())
				}).Return(errors.New("error"))
				d.repo.On("Create", mock.Anything, mock.Anything).Return(nil)
				d.gitlab.On("CreateProject", mock.Anything, mock.Anything).Return(&gitlab.CreateProjectResponse{Id: 1}, nil)
				d.repo.On("Save", mock.Anything, mock.Anything).Return(errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should success without add user to project",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.RepositoryCreateInput{
				CurrentUserId: uuidInput,
				Name:          "test",
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindOrganization", mock.Anything, mock.Anything, mock.Anything).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil)
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, nil)
				d.repo.On("FindUserGitGroup", mock.Anything, mock.Anything).Return(nil, nil)
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(context.Background())
				}).Return(nil)
				d.repo.On("Create", mock.Anything, mock.Anything).Return(nil)
				d.gitlab.On("CreateProject", mock.Anything, mock.Anything).Return(&gitlab.CreateProjectResponse{Id: 1}, nil)
				d.repo.On("Save", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("CreateRepositoryMember", mock.Anything, mock.Anything).Return(nil)
				d.gitlab.On("GetMemberOfProject", mock.Anything, mock.Anything).Return(&gitlab.*********************ponse{}, nil)
				d.gitlab.On("CreateWebhook", mock.Anything, mock.Anything).Return(nil)
			},
			expError: nil,
		},
		{
			name: "should return error",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.RepositoryCreateInput{
				CurrentUserId: uuidInput,
				Name:          "test",
				Type:          enums.RepoType_Spaces,
				Hardware: dto.RepositoryHardwareInput{
					NodeName: "cpu",
					Name:     "cpu",
				},
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindOrganization", mock.Anything, mock.Anything, mock.Anything).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil)
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, nil)
				d.repo.On("FindUserGitGroup", mock.Anything, mock.Anything).Return(nil, nil)
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(context.Background())
				}).Return(errors.New("error"))
				d.repo.On("Create", mock.Anything, mock.Anything).Return(nil)
				// fakeClientSet := fake.NewSimpleClientset()
				// fakeClientSet.PrependReactor("get", "nodes", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
				// 	return true, nil, nil
				// })
				// d.kubeClient = fakeClientSet
				d.repo.On("CreateHardware", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should success",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.RepositoryCreateInput{
				CurrentUserId: uuidInput,
				Name:          "test",
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindOrganization", mock.Anything, mock.Anything, mock.Anything).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil)
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, nil)
				d.repo.On("FindUserGitGroup", mock.Anything, mock.Anything).Return(nil, nil)
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(context.Background())
				}).Return(nil)
				d.repo.On("Create", mock.Anything, mock.Anything).Return(nil)
				d.gitlab.On("CreateProject", mock.Anything, mock.Anything).Return(&gitlab.CreateProjectResponse{Id: 1}, nil)
				d.repo.On("Save", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("CreateRepositoryMember", mock.Anything, mock.Anything).Return(nil)
				d.gitlab.On("GetMemberOfProject", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
				d.gitlab.On("AddUserToProject", mock.Anything, mock.Anything).Return(&gitlab.AddUserToProjectResponse{}, nil)
				d.gitlab.On("CreateWebhook", mock.Anything, mock.Anything).Return(nil)
			},
			expError: nil,
		},
		{
			name: "should return error if FindTemplate fail",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.RepositoryCreateInput{
				CurrentUserId: uuidInput,
				Name:          "test",
				SDKID:         &uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindOrganization", mock.Anything, mock.Anything, mock.Anything).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil)
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{GitlabAccessToken: utils.Ptr("token")}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, nil)
				d.repo.On("FindUserGitGroup", mock.Anything, mock.Anything).Return(nil, nil)
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(context.Background())
				}).Return(errors.New("error"))
				d.repo.On("Create", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("FindTemplate", mock.Anything, mock.Anything).Return(nil, errors.New("error"))

			},
			expError: errors.New("error"),
		},
		{
			name: "should return error if CreateProjectFromTemplate fail",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.RepositoryCreateInput{
				CurrentUserId: uuidInput,
				Name:          "test",
				SDKID:         &uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindOrganization", mock.Anything, mock.Anything, mock.Anything).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil)
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{GitlabAccessToken: utils.Ptr("token")}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, nil)
				d.repo.On("FindUserGitGroup", mock.Anything, mock.Anything).Return(nil, nil)
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(context.Background())
				}).Return(errors.New("error"))
				d.repo.On("Create", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("FindTemplate", mock.Anything, mock.Anything).Return(&entities.RepoTemplate{GitRefId: 1}, nil)
				d.gitlab.On("CreateProjectFromTemplate", mock.Anything, mock.Anything).Return(nil, errors.New("error"))

			},
			expError: errors.New("error"),
		},
		{
			name: "should success when create with template",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.RepositoryCreateInput{
				CurrentUserId: uuidInput,
				Name:          "test",
				SDKID:         &uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindOrganization", mock.Anything, mock.Anything, mock.Anything).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil)
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{GitlabAccessToken: utils.Ptr("token")}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, nil)
				d.repo.On("FindUserGitGroup", mock.Anything, mock.Anything).Return(nil, nil)
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(context.Background())
				}).Return(nil)
				d.repo.On("Create", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("FindTemplate", mock.Anything, mock.Anything).Return(&entities.RepoTemplate{GitRefId: 1}, nil)
				d.gitlab.On("CreateProjectFromTemplate", mock.Anything, mock.Anything).Return(&gitlab.CreateProjectResponse{Id: 1}, nil)
				d.repo.On("Save", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("CreateRepositoryMember", mock.Anything, mock.Anything).Return(nil)
				d.gitlab.On("GetMemberOfProject", mock.Anything, mock.Anything).Return(&gitlab.*********************ponse{}, nil)
				d.gitlab.On("AddUserToProject", mock.Anything, mock.Anything).Return(&gitlab.AddUserToProjectResponse{}, nil)
				d.gitlab.On("CreateWebhook", mock.Anything, mock.Anything).Return(nil)
			},
			expError: nil,
		},
		{
			name: "should success when create with org",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.RepositoryCreateInput{
				CurrentUserId: uuidInput,
				Name:          "test",
				Owner: dto.RepositoryOwner{
					Type: enums.RepoOwnerType_Org,
					ID:   uuidString,
				},
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindOrganization", mock.Anything, mock.Anything, mock.Anything).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil)
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, nil)
				d.repo.On("********************", mock.Anything, uuidString).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgGitGroup", mock.Anything, uuidString).Return(&entities.DefaultGitGroup{}, nil)
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(context.Background())
				}).Return(nil)
				d.repo.On("Create", mock.Anything, mock.Anything).Return(nil)
				d.gitlab.On("CreateProject", mock.Anything, mock.Anything).Return(&gitlab.CreateProjectResponse{Id: 1}, nil)
				d.repo.On("FindTemplate", mock.Anything, mock.Anything).Return(&entities.RepoTemplate{GitRefId: 1}, nil)
				d.gitlab.On("CreateProjectFromTemplate", mock.Anything, mock.Anything).Return(&gitlab.CreateProjectResponse{Id: 1}, nil)
				d.repo.On("Save", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("ListMembers", mock.Anything, mock.Anything).Return([]entities.OrgMemberInfo{
					{
						Name:   "test",
						Email:  "<EMAIL>",
						Avatar: utils.Ptr("avatar"),
						OrgMember: entities.OrgMember{
							ExpiresAt: utils.Ptr(time.Now().Add(time.Hour * 24)),
							User: &entities.User{
								BaseModel: entities.BaseModel{
									ID: uuidInput,
								},
								Email: "<EMAIL>",
							},
						},
					},
				}, nil)
				d.repo.On("CreateRepositoryMember", mock.Anything, mock.Anything).Return(nil)
				d.gitlab.On("GetMemberOfProject", mock.Anything, mock.Anything).Return(&gitlab.*********************ponse{}, nil)
				d.gitlab.On("AddUserToProject", mock.Anything, mock.Anything).Return(&gitlab.AddUserToProjectResponse{}, nil)
				d.gitlab.On("CreateWebhook", mock.Anything, mock.Anything).Return(nil)

			},
			expError: nil,
		},
		{
			name: "should error when ListMembers failed",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.RepositoryCreateInput{
				CurrentUserId: uuidInput,
				Name:          "test",
				Owner: dto.RepositoryOwner{
					Type: enums.RepoOwnerType_Org,
					ID:   uuidString,
				},
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindOrganization", mock.Anything, mock.Anything, mock.Anything).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil)
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, nil)
				d.repo.On("********************", mock.Anything, uuidString).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgGitGroup", mock.Anything, uuidString).Return(&entities.DefaultGitGroup{}, nil)
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(context.Background())
				}).Return(nil)
				d.repo.On("Create", mock.Anything, mock.Anything).Return(nil)
				d.gitlab.On("CreateProject", mock.Anything, mock.Anything).Return(&gitlab.CreateProjectResponse{Id: 1}, nil)
				d.repo.On("FindTemplate", mock.Anything, mock.Anything).Return(&entities.RepoTemplate{GitRefId: 1}, nil)
				d.gitlab.On("CreateProjectFromTemplate", mock.Anything, mock.Anything).Return(&gitlab.CreateProjectResponse{Id: 1}, nil)
				d.repo.On("Save", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("ListMembers", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: nil,
		},
		{
			name: "should return success if GetMemberOfProject fail",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.RepositoryCreateInput{
				CurrentUserId: uuidInput,
				Name:          "test",
				Owner: dto.RepositoryOwner{
					Type: enums.RepoOwnerType_Org,
					ID:   uuidString,
				},
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindOrganization", mock.Anything, mock.Anything, mock.Anything).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil)
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, nil)
				d.repo.On("********************", mock.Anything, uuidString).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgGitGroup", mock.Anything, uuidString).Return(&entities.DefaultGitGroup{}, nil)
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(context.Background())
				}).Return(nil)
				d.repo.On("Create", mock.Anything, mock.Anything).Return(nil)
				d.gitlab.On("CreateProject", mock.Anything, mock.Anything).Return(&gitlab.CreateProjectResponse{Id: 1}, nil)
				d.repo.On("FindTemplate", mock.Anything, mock.Anything).Return(&entities.RepoTemplate{GitRefId: 1}, nil)
				d.gitlab.On("CreateProjectFromTemplate", mock.Anything, mock.Anything).Return(&gitlab.CreateProjectResponse{Id: 1}, nil)
				d.repo.On("Save", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("ListMembers", mock.Anything, mock.Anything).Return([]entities.OrgMemberInfo{
					{
						Name:   "test",
						Email:  "<EMAIL>",
						Avatar: utils.Ptr("avatar"),
						OrgMember: entities.OrgMember{
							ExpiresAt: utils.Ptr(time.Now().Add(time.Hour * 24)),
							User: &entities.User{
								BaseModel: entities.BaseModel{
									ID: uuidInput,
								},
								Email: "<EMAIL>",
							},
						},
					},
				}, nil)
				d.repo.On("CreateRepositoryMember", mock.Anything, mock.Anything).Return(nil)
				d.gitlab.On("GetMemberOfProject", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
				d.gitlab.On("AddUserToProject", mock.Anything, mock.Anything).Return(&gitlab.AddUserToProjectResponse{}, nil)
				d.gitlab.On("CreateWebhook", mock.Anything, mock.Anything).Return(nil)

			},
			expError: nil,
		},
		{
			name: "should return error if CreateWebhook fail",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.RepositoryCreateInput{
				CurrentUserId: uuidInput,
				Name:          "test",
				Owner: dto.RepositoryOwner{
					Type: enums.RepoOwnerType_Org,
					ID:   uuidString,
				},
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindOrganization", mock.Anything, mock.Anything, mock.Anything).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil)
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, nil)
				d.repo.On("********************", mock.Anything, uuidString).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgGitGroup", mock.Anything, uuidString).Return(&entities.DefaultGitGroup{}, nil)
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(context.Background())
				}).Return(errors.New("error"))
				d.repo.On("Create", mock.Anything, mock.Anything).Return(nil)
				d.gitlab.On("CreateProject", mock.Anything, mock.Anything).Return(&gitlab.CreateProjectResponse{Id: 1}, nil)
				d.repo.On("FindTemplate", mock.Anything, mock.Anything).Return(&entities.RepoTemplate{GitRefId: 1}, nil)
				d.gitlab.On("CreateProjectFromTemplate", mock.Anything, mock.Anything).Return(&gitlab.CreateProjectResponse{Id: 1}, nil)
				d.repo.On("Save", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("ListMembers", mock.Anything, mock.Anything).Return([]entities.OrgMemberInfo{
					{
						Name:   "test",
						Email:  "<EMAIL>",
						Avatar: utils.Ptr("avatar"),
						OrgMember: entities.OrgMember{
							ExpiresAt: utils.Ptr(time.Now().Add(time.Hour * 24)),
							User: &entities.User{
								BaseModel: entities.BaseModel{
									ID: uuidInput,
								},
								Email: "<EMAIL>",
							},
						},
					},
				}, nil)
				d.repo.On("CreateRepositoryMember", mock.Anything, mock.Anything).Return(nil)
				d.gitlab.On("GetMemberOfProject", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
				d.gitlab.On("AddUserToProject", mock.Anything, mock.Anything).Return(&gitlab.AddUserToProjectResponse{}, nil)
				d.gitlab.On("CreateWebhook", mock.Anything, mock.Anything).Return(errors.New("error"))

			},
			expError: errors.New("error"),
		},
		{
			name: "should return error if AddUserToProject fail",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.RepositoryCreateInput{
				CurrentUserId: uuidInput,
				Name:          "test",
				Owner: dto.RepositoryOwner{
					Type: enums.RepoOwnerType_Org,
					ID:   uuidString,
				},
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindOrganization", mock.Anything, mock.Anything, mock.Anything).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil)
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, nil)
				d.repo.On("********************", mock.Anything, uuidString).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgGitGroup", mock.Anything, uuidString).Return(&entities.DefaultGitGroup{}, nil)
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(context.Background())
				}).Return(errors.New("error"))
				d.repo.On("Create", mock.Anything, mock.Anything).Return(nil)
				d.gitlab.On("CreateProject", mock.Anything, mock.Anything).Return(&gitlab.CreateProjectResponse{Id: 1}, nil)
				d.repo.On("FindTemplate", mock.Anything, mock.Anything).Return(&entities.RepoTemplate{GitRefId: 1}, nil)
				d.gitlab.On("CreateProjectFromTemplate", mock.Anything, mock.Anything).Return(&gitlab.CreateProjectResponse{Id: 1}, nil)
				d.repo.On("Save", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("ListMembers", mock.Anything, mock.Anything).Return([]entities.OrgMemberInfo{
					{
						Name:   "test",
						Email:  "<EMAIL>",
						Avatar: utils.Ptr("avatar"),
						OrgMember: entities.OrgMember{
							ExpiresAt: utils.Ptr(time.Now().Add(time.Hour * 24)),
							User: &entities.User{
								BaseModel: entities.BaseModel{
									ID: uuidInput,
								},
								Email: "<EMAIL>",
							},
						},
					},
				}, nil)
				d.repo.On("CreateRepositoryMember", mock.Anything, mock.Anything).Return(nil)
				d.gitlab.On("GetMemberOfProject", mock.Anything, mock.Anything).Return(&gitlab.*********************ponse{}, nil)
				d.gitlab.On("AddUserToProject", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
				d.gitlab.On("CreateWebhook", mock.Anything, mock.Anything).Return(nil)

			},
			expError: errors.New("error"),
		},
		{
			name: "should success when AddUserToProject failed",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.RepositoryCreateInput{
				CurrentUserId: uuidInput,
				Name:          "test",
				Owner: dto.RepositoryOwner{
					Type: enums.RepoOwnerType_Org,
					ID:   uuidString,
				},
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindOrganization", mock.Anything, mock.Anything, mock.Anything).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil)
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, nil)
				d.repo.On("********************", mock.Anything, uuidString).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgGitGroup", mock.Anything, uuidString).Return(&entities.DefaultGitGroup{}, nil)
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(context.Background())
				}).Return(errors.New("error"))
				d.repo.On("Create", mock.Anything, mock.Anything).Return(nil)
				d.gitlab.On("CreateProject", mock.Anything, mock.Anything).Return(&gitlab.CreateProjectResponse{Id: 1}, nil)
				d.repo.On("FindTemplate", mock.Anything, mock.Anything).Return(&entities.RepoTemplate{GitRefId: 1}, nil)
				d.gitlab.On("CreateProjectFromTemplate", mock.Anything, mock.Anything).Return(&gitlab.CreateProjectResponse{Id: 1}, nil)
				d.repo.On("Save", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("ListMembers", mock.Anything, mock.Anything).Return([]entities.OrgMemberInfo{
					{
						Name:   "test",
						Email:  "<EMAIL>",
						Avatar: utils.Ptr("avatar"),
						OrgMember: entities.OrgMember{
							ExpiresAt: utils.Ptr(time.Now().Add(time.Hour * 24)),
							User: &entities.User{
								BaseModel: entities.BaseModel{
									ID: uuidInput,
								},
								Email: "<EMAIL>",
							},
						},
					},
				}, nil)
				d.repo.On("CreateRepositoryMember", mock.Anything, mock.Anything).Return(nil)
				d.gitlab.On("GetMemberOfProject", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
				d.gitlab.On("AddUserToProject", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should success when Save failed",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.RepositoryCreateInput{
				CurrentUserId: uuidInput,
				Name:          "test",
				Owner: dto.RepositoryOwner{
					Type: enums.RepoOwnerType_Org,
					ID:   uuidString,
				},
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindOrganization", mock.Anything, mock.Anything, mock.Anything).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil)
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, nil)
				d.repo.On("********************", mock.Anything, uuidString).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgGitGroup", mock.Anything, uuidString).Return(&entities.DefaultGitGroup{}, nil)
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(context.Background())
				}).Return(errors.New("error"))
				d.repo.On("Create", mock.Anything, mock.Anything).Return(nil)
				d.gitlab.On("CreateProject", mock.Anything, mock.Anything).Return(&gitlab.CreateProjectResponse{Id: 1}, nil)
				d.repo.On("FindTemplate", mock.Anything, mock.Anything).Return(&entities.RepoTemplate{GitRefId: 1}, nil)
				d.gitlab.On("CreateProjectFromTemplate", mock.Anything, mock.Anything).Return(&gitlab.CreateProjectResponse{Id: 1}, nil)
				d.repo.On("Save", mock.Anything, mock.Anything).Return(nil).Once()
				d.repo.On("ListMembers", mock.Anything, mock.Anything).Return([]entities.OrgMemberInfo{
					{
						Name:   "test",
						Email:  "<EMAIL>",
						Avatar: utils.Ptr("avatar"),
						OrgMember: entities.OrgMember{
							ExpiresAt: utils.Ptr(time.Now().Add(time.Hour * 24)),
							User: &entities.User{
								BaseModel: entities.BaseModel{
									ID: uuidInput,
								},
								Email: "<EMAIL>",
							},
						},
					},
				}, nil)
				d.repo.On("CreateRepositoryMember", mock.Anything, mock.Anything).Return(nil)
				d.gitlab.On("GetMemberOfProject", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
				d.gitlab.On("AddUserToProject", mock.Anything, mock.Anything).Return(&gitlab.AddUserToProjectResponse{}, nil)
				d.repo.On("Save", mock.Anything, mock.Anything).Return(errors.New("error")).Once()
			},
			expError: errors.New("error"),
		},
		{
			name: "should error if CreateRepositoryMember fail",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.RepositoryCreateInput{
				CurrentUserId: uuidInput,
				Name:          "test",
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindOrganization", mock.Anything, mock.Anything, mock.Anything).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil)
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, nil)
				d.repo.On("FindUserGitGroup", mock.Anything, mock.Anything).Return(nil, nil)
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(context.Background())
				}).Return(errors.New("error"))
				d.repo.On("Create", mock.Anything, mock.Anything).Return(nil)
				d.gitlab.On("CreateProject", mock.Anything, mock.Anything).Return(&gitlab.CreateProjectResponse{Id: 1}, nil)
				d.repo.On("Save", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("CreateRepositoryMember", mock.Anything, mock.Anything).Return(errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return success if AddUserToProject fail",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.RepositoryCreateInput{
				CurrentUserId: uuidInput,
				Name:          "test",
				Owner: dto.RepositoryOwner{
					Type: enums.RepoOwnerType_User,
					ID:   uuidString,
				},
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindOrganization", mock.Anything, mock.Anything, mock.Anything).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil)
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, nil)
				d.repo.On("FindUserGitGroup", mock.Anything, mock.Anything).Return(nil, nil)
				d.repo.On("********************", mock.Anything, uuidString).Return(&entities.Organization{}, nil)
				d.repo.On("FindOrgGitGroup", mock.Anything, uuidString).Return(&entities.DefaultGitGroup{}, nil)
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(context.Background())
				}).Return(errors.New("error"))
				d.repo.On("Create", mock.Anything, mock.Anything).Return(nil)
				d.gitlab.On("CreateProject", mock.Anything, mock.Anything).Return(&gitlab.CreateProjectResponse{Id: 1}, nil)
				d.repo.On("FindTemplate", mock.Anything, mock.Anything).Return(&entities.RepoTemplate{GitRefId: 1}, nil)
				d.gitlab.On("CreateProjectFromTemplate", mock.Anything, mock.Anything).Return(&gitlab.CreateProjectResponse{Id: 1}, nil)
				d.repo.On("Save", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("ListMembers", mock.Anything, mock.Anything).Return([]entities.OrgMemberInfo{
					{
						Name:   "test",
						Email:  "<EMAIL>",
						Avatar: utils.Ptr("avatar"),
						OrgMember: entities.OrgMember{
							ExpiresAt: utils.Ptr(time.Now().Add(time.Hour * 24)),
							User: &entities.User{
								BaseModel: entities.BaseModel{
									ID: uuidInput,
								},
								Email: "<EMAIL>",
							},
						},
					},
				}, nil)
				d.repo.On("CreateRepositoryMember", mock.Anything, mock.Anything).Return(nil)
				d.gitlab.On("GetMemberOfProject", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
				d.gitlab.On("AddUserToProject", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				config: &configs.GlobalConfig{Gitlab: &configs.GitlabConfig{
					VolvoApiServerHost: "http://localhost",
				}},
				repo:     &repository_mocks.MockRepository{},
				gitlab:   &gitlab_mocks.MockGitlabClient{},
				workflow: &workflow_mocks.MockWorkflowUsecase{},
			}
			tt.mockFn(d)

			u := repository.New(d.config, d.repo, d.gitlab, d.workflow, d.kubeClient, nil)
			_, err := u.CreateRepository(tt.ctx, tt.input)
			require.Equal(t, tt.expError, err)
		})
	}
}

func TestGetRepoInfo(t *testing.T) {
	repoType := enums.RepoType_Models
	namespace := "namespace"
	repoName := "repo-name"
	repoID := *types.NewRepoID(repoType, namespace, repoName)

	email := "<EMAIL>"
	avatar := "http://avatar"
	uuidInput := uuid.New()
	type dependencies struct {
		config   *configs.GlobalConfig
		repo     *repository_mocks.MockRepository
		gitlab   *gitlab_mocks.MockGitlabClient
		workflow *workflow_mocks.MockWorkflowUsecase
		aws      *aws_mocks.MockAWSClient
	}

	mockUserID := uuid.New()
	tests := []struct {
		name     string
		ctx      context.Context
		mockFn   func(d *dependencies)
		input    dto.GetRepositoryInput
		exOutput *dto.GetRepositoryOutput
		expError error
	}{
		{
			name: "should return error if FindUser fail",
			ctx:  context.TODO(),
			input: dto.GetRepositoryInput{
				CurrentUserId: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error if FindUser not found",
			ctx:  context.TODO(),
			input: dto.GetRepositoryInput{
				CurrentUserId: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expError: usecase.ErrUserNotFound,
		},
		{
			name: "should return error if FindRepository fail",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.GetRepositoryInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},

			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error if FindRepository fail",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.GetRepositoryInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},

			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{BaseModel: entities.BaseModel{ID: mockUserID}}, nil).Once()
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expError: usecase.ErrRepositoryNotFound,
		},
		{
			name: "should return error if GetProjectURLs fail",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.GetRepositoryInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},

			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{GitlabAccessToken: utils.Ptr("token")}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					RefGitRepoID: 1,
				}, nil)
				d.gitlab.On("GetProjectURLs", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should ListRepoTagsByQueryModel return error",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.GetRepositoryInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{BaseModel: entities.BaseModel{ID: mockUserID}}, nil).Once()
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					RefGitRepoID: 1,
					UserID:       &uuidInput,
					OrgID:        nil,
				}, nil)
				d.gitlab.On("GetProjectURLs", mock.Anything, mock.Anything).Return(&gitlab.GetProjectURLsResponse{
					SSHUrl: "ssh://gitlab.com",
				}, nil)
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{
					BaseModel: entities.BaseModel{
						ID: uuidInput,
					},
					Email: email,
				}, nil)
				d.repo.On("ListRepoTagsByQueryModel", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			exOutput: nil,
			expError: errors.New("error"),
		},
		{
			name: "should return error if FindUserByID fail",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.GetRepositoryInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					RefGitRepoID: 1,
					UserID:       &uuidInput,
					OrgID:        nil,
				}, nil)
				d.gitlab.On("GetProjectURLs", mock.Anything, mock.Anything).Return(&gitlab.GetProjectURLsResponse{
					SSHUrl: "ssh://gitlab.com",
				}, nil)
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			exOutput: nil,
			expError: errors.New("error"),
		},
		{
			name: "should return error if get avatar fail",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.GetRepositoryInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					RefGitRepoID: 1,
					UserID:       &uuidInput,
					OrgID:        nil,
					User: &entities.User{
						BaseModel: entities.BaseModel{
							ID: uuidInput,
						},
						Email: email,
					},
					Avatar: &avatar,
				}, nil)
				d.gitlab.On("GetProjectURLs", mock.Anything, mock.Anything).Return(&gitlab.GetProjectURLsResponse{
					SSHUrl: "ssh://gitlab.com",
				}, nil)
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{
					BaseModel: entities.BaseModel{
						ID: uuidInput,
					},
					Email: email,
				}, nil)
				d.aws.On("GenPreSignUrl", mock.Anything, mock.Anything).Return("", errors.New("error"))
			},
			exOutput: nil,
			expError: errors.New("error"),
		},
		{
			name: "should success ",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.GetRepositoryInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					RefGitRepoID: 1,
					UserID:       &uuidInput,
					OrgID:        nil,
					User: &entities.User{
						BaseModel: entities.BaseModel{
							ID: uuidInput,
						},
						Email: email,
					},
					Hardware: &entities.Hardware{
						BaseModel: entities.BaseModel{
							ID: uuidInput,
						},
						CPU: 2,
					},
					Deployment: &entities.Deployment{
						BaseModel: entities.BaseModel{
							ID: uuidInput,
						},
					},
					Avatar: &avatar,
				}, nil)
				d.gitlab.On("GetProjectURLs", mock.Anything, mock.Anything).Return(&gitlab.GetProjectURLsResponse{
					SSHUrl: "ssh://gitlab.com",
				}, nil)
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{
					BaseModel: entities.BaseModel{
						ID: uuidInput,
					},
					Email: email,
				}, nil)
				d.aws.On("GenPreSignUrl", mock.Anything, mock.Anything).Return(avatar, nil)
				d.repo.On("ListRepoTagsByQueryModel", mock.Anything, mock.Anything).Return([]entities.Tag{}, nil)
			},
			exOutput: &dto.GetRepositoryOutput{
				SSHURL: "ssh://gitlab.com",
				Owner: dto.RepositoryOwner{
					ID:    uuidInput,
					Type:  enums.RepoOwnerType_User,
					Email: &email,
				},
				Hardware: &dto.Hardware{
					ID:  uuidInput,
					CPU: 2,
					Mem: types.HardwareMem{
						Amount: 0,
						Unit:   "MiB",
					},
				},
				UserID: uuidInput,
				Deployment: &dto.Deployment{
					ID:  uuidInput,
					URL: "https://.test-space",
					User: &dto.User{
						Name:     "user has been removed",
						Username: "user has been removed",
					},
				},
				Avatar: &avatar,
				Tags:   []dto.Tag{},
			},
		},
		{
			name: "should error if ******************** fail",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.GetRepositoryInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					RefGitRepoID: 1,
					OrgID:        &uuidInput,
				}, nil)
				d.gitlab.On("GetProjectURLs", mock.Anything, mock.Anything).Return(&gitlab.GetProjectURLsResponse{
					SSHUrl: "ssh://gitlab.com",
				}, nil)
				d.repo.On("********************", mock.Anything, mock.Anything).Return(nil, errors.New("error"))

			},
			exOutput: nil,
			expError: errors.New("error"),
		},
		{
			name: "should success",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.GetRepositoryInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					RefGitRepoID: 1,
					OrgID:        &uuidInput,
				}, nil)
				d.repo.On("********************", mock.Anything, mock.Anything).Return(&entities.Organization{
					BaseModel: entities.BaseModel{
						ID: uuidInput,
					},
					Name:   "name",
					Type:   enums.OrganizationType_Company,
					Avatar: &avatar,
				}, nil)
				d.gitlab.On("GetProjectURLs", mock.Anything, mock.Anything).Return(&gitlab.GetProjectURLsResponse{
					SSHUrl: "ssh://gitlab.com",
				}, nil)
				d.aws.On("GenPreSignUrl", mock.Anything, mock.Anything).Return(avatar, errors.New("error"))
				d.repo.On("ListRepoTagsByQueryModel", mock.Anything, mock.Anything).Return([]entities.Tag{}, nil)
			},
			exOutput: &dto.GetRepositoryOutput{
				SSHURL: "ssh://gitlab.com",
				Owner: dto.RepositoryOwner{
					ID:     uuidInput,
					Type:   enums.RepoOwnerType_Org,
					Name:   "name",
					Avatar: &avatar,
				},
				Tags: []dto.Tag{},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				config: &configs.GlobalConfig{
					Space: &configs.SpaceConfig{
						SpaceDomain: "test-space",
					},
				},
				repo:     &repository_mocks.MockRepository{},
				gitlab:   &gitlab_mocks.MockGitlabClient{},
				workflow: &workflow_mocks.MockWorkflowUsecase{},
				aws:      &aws_mocks.MockAWSClient{},
			}
			tt.mockFn(d)

			u := repository.New(d.config, d.repo, d.gitlab, d.workflow, nil, d.aws)
			output, err := u.GetRepoInfo(tt.ctx, tt.input)
			require.Equal(t, tt.exOutput, output)
			require.Equal(t, tt.expError, err)
		})
	}
}

func TestGetRawFileFromRepository(t *testing.T) {
	repoType := enums.RepoType_Models
	namespace := "namespace"
	repoName := "repo-name"
	repoID := *types.NewRepoID(repoType, namespace, repoName)

	uuidInput := uuid.New()
	type dependencies struct {
		config   *configs.GlobalConfig
		repo     *repository_mocks.MockRepository
		gitlab   *gitlab_mocks.MockGitlabClient
		workflow *workflow_mocks.MockWorkflowUsecase
	}

	mockUserID := uuid.New()
	tests := []struct {
		name     string
		ctx      context.Context
		mockFn   func(d *dependencies)
		input    dto.GetFileFromRepositoryInput
		exOutput *http.Response
		expError error
	}{
		{
			name: "should return error if FindUser fail",
			ctx:  context.TODO(),
			input: dto.GetFileFromRepositoryInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error if FindUser not found",
			ctx:  context.TODO(),
			input: dto.GetFileFromRepositoryInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expError: usecase.ErrUserNotFound,
		},
		{
			name: "should return error if FindRepository fail",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.GetFileFromRepositoryInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},

			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{BaseModel: entities.BaseModel{ID: mockUserID}}, nil).Once()
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error if FindRepository fail",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.GetFileFromRepositoryInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{BaseModel: entities.BaseModel{ID: mockUserID}}, nil).Once()
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expError: usecase.ErrRepositoryNotFound,
		},
		{
			name: "should return error if GetRawFileFromRepo fail",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.GetFileFromRepositoryInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{GitlabAccessToken: utils.Ptr("token")}, nil)
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{BaseModel: entities.BaseModel{ID: mockUserID}}, nil).Once()
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					RefGitRepoID: 1,
				}, nil)
				d.gitlab.On("GetRawFileFromRepo", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should success",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.GetFileFromRepositoryInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{GitlabAccessToken: utils.Ptr("token")}, nil)
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{BaseModel: entities.BaseModel{ID: mockUserID}}, nil).Once()
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					RefGitRepoID: 1,
					UserID:       &uuidInput,
					OrgID:        nil,
				}, nil)
				d.gitlab.On("GetRawFileFromRepo", mock.Anything, mock.Anything).Return(&http.Response{
					StatusCode: 200,
					Body:       io.NopCloser(bytes.NewBufferString("")),
				}, nil)
			},
			exOutput: &http.Response{
				StatusCode: 200,
				Body:       io.NopCloser(bytes.NewBufferString("")),
			},
			expError: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				config:   &configs.GlobalConfig{},
				repo:     &repository_mocks.MockRepository{},
				gitlab:   &gitlab_mocks.MockGitlabClient{},
				workflow: &workflow_mocks.MockWorkflowUsecase{},
			}
			tt.mockFn(d)

			u := repository.New(d.config, d.repo, d.gitlab, d.workflow, nil, nil)
			output, err := u.GetRawFileFromRepository(tt.ctx, tt.input)
			require.Equal(t, tt.exOutput, output)
			require.Equal(t, tt.expError, err)
		})
	}
}

func TestGetHeaderRawFileFromRepository(t *testing.T) {
	repoType := enums.RepoType_Models
	namespace := "namespace"
	repoName := "repo-name"
	repoID := *types.NewRepoID(repoType, namespace, repoName)

	uuidInput := uuid.New()
	type dependencies struct {
		config   *configs.GlobalConfig
		repo     *repository_mocks.MockRepository
		gitlab   *gitlab_mocks.MockGitlabClient
		workflow *workflow_mocks.MockWorkflowUsecase
	}

	mockUserID := uuid.New()
	tests := []struct {
		name     string
		ctx      context.Context
		mockFn   func(d *dependencies)
		input    dto.GetFileFromRepositoryInput
		exOutput *http.Response
		expError error
	}{
		{
			name: "should return error if FindUser fail",
			ctx:  context.TODO(),
			input: dto.GetFileFromRepositoryInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error if FindUser not found",
			ctx:  context.TODO(),
			input: dto.GetFileFromRepositoryInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expError: usecase.ErrUserNotFound,
		},
		{
			name: "should return error if FindRepository fail",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.GetFileFromRepositoryInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},

			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{BaseModel: entities.BaseModel{ID: mockUserID}}, nil).Once()
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error if FindRepository fail",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.GetFileFromRepositoryInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{BaseModel: entities.BaseModel{ID: mockUserID}}, nil).Once()
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expError: usecase.ErrRepositoryNotFound,
		},
		{
			name: "should return error if GetHeaderRawFileFromRepo fail",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.GetFileFromRepositoryInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{GitlabAccessToken: utils.Ptr("token")}, nil)
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{BaseModel: entities.BaseModel{ID: mockUserID}}, nil).Once()
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					RefGitRepoID: 1,
				}, nil)
				d.gitlab.On("GetHeaderRawFileFromRepo", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should success",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.GetFileFromRepositoryInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{GitlabAccessToken: utils.Ptr("token")}, nil)
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{BaseModel: entities.BaseModel{ID: mockUserID}}, nil).Once()
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					RefGitRepoID: 1,
					UserID:       &uuidInput,
					OrgID:        nil,
				}, nil)
				d.gitlab.On("GetHeaderRawFileFromRepo", mock.Anything, mock.Anything).Return(&http.Response{
					StatusCode: 200,
					Body:       io.NopCloser(bytes.NewBufferString("")),
				}, nil)
			},
			exOutput: &http.Response{
				StatusCode: 200,
				Body:       io.NopCloser(bytes.NewBufferString("")),
			},
			expError: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				config:   &configs.GlobalConfig{},
				repo:     &repository_mocks.MockRepository{},
				gitlab:   &gitlab_mocks.MockGitlabClient{},
				workflow: &workflow_mocks.MockWorkflowUsecase{},
			}
			tt.mockFn(d)

			u := repository.New(d.config, d.repo, d.gitlab, d.workflow, nil, nil)
			output, err := u.GetHeaderRawFileFromRepository(tt.ctx, tt.input)
			require.Equal(t, tt.exOutput, output)
			require.Equal(t, tt.expError, err)
		})
	}
}

func TestListRepoTags(t *testing.T) {
	type dependencies struct {
		config   *configs.GlobalConfig
		repo     *repository_mocks.MockRepository
		gitlab   *gitlab_mocks.MockGitlabClient
		workflow *workflow_mocks.MockWorkflowUsecase
	}

	tests := []struct {
		name     string
		ctx      context.Context
		mockFn   func(d *dependencies)
		input    dto.ListRepoTagsInput
		exOutput *dto.ListRepoTagsOutput
		expError error
	}{
		{
			name: "should return error if ListRepoTags fail",
			ctx:  context.TODO(),
			input: dto.ListRepoTagsInput{
				Keyword: "abc",
			},
			mockFn: func(d *dependencies) {
				d.repo.On("ListRepoTags", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should success",
			ctx:  context.TODO(),
			input: dto.ListRepoTagsInput{
				Keyword: "abc",
			},
			mockFn: func(d *dependencies) {
				d.repo.On("ListRepoTags", mock.Anything, mock.Anything).Return([]entities.Tag{
					{
						Name: "tag1",
					},
					{
						Name: "tag2",
					},
				}, nil)
			},
			expError: nil,
			exOutput: &dto.ListRepoTagsOutput{
				Data: &[]dto.Tag{
					{
						Name: "tag1",
					},
					{
						Name: "tag2",
					},
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				config:   &configs.GlobalConfig{},
				repo:     &repository_mocks.MockRepository{},
				gitlab:   &gitlab_mocks.MockGitlabClient{},
				workflow: &workflow_mocks.MockWorkflowUsecase{},
			}
			tt.mockFn(d)

			u := repository.New(d.config, d.repo, d.gitlab, d.workflow, nil, nil)
			output, err := u.ListRepoTags(tt.ctx, tt.input)
			require.Equal(t, tt.exOutput, output)
			require.Equal(t, tt.expError, err)
		})
	}
}

func TestUpdateRepository(t *testing.T) {
	repoType := enums.RepoType_Models
	namespace := "namespace"
	repoName := "repo-name"
	repoID := *types.NewRepoID(repoType, namespace, repoName)

	uuidInput := uuid.New()
	type dependencies struct {
		config   *configs.GlobalConfig
		repo     *repository_mocks.MockRepository
		gitlab   *gitlab_mocks.MockGitlabClient
		workflow *workflow_mocks.MockWorkflowUsecase
	}

	tests := []struct {
		name     string
		ctx      context.Context
		mockFn   func(d *dependencies)
		input    dto.UpdateRepositoryInput
		expError error
	}{
		{
			name: "should return error if FindUserByID fail",
			ctx:  context.TODO(),
			input: dto.UpdateRepositoryInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error if FindUserByID not found",
			ctx:  context.TODO(),
			input: dto.UpdateRepositoryInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expError: usecase.ErrUserNotFound,
		},
		{
			name: "should return error if FindRepository fail",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.UpdateRepositoryInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},

			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error if FindRepository fail",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.UpdateRepositoryInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expError: usecase.ErrRepositoryNotFound,
		},
		{
			name: "should return error if FindRepositoryMember fail",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.UpdateRepositoryInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{GitlabAccessToken: utils.Ptr("token")}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					RefGitRepoID: 1,
				}, nil)
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error if UpdateRepository fail",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.UpdateRepositoryInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{GitlabAccessToken: utils.Ptr("token")}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					RefGitRepoID: 1,
				}, nil)
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything).Return(&entities.RepoMember{Role: enums.RepoRole_Owner}, nil)
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(context.Background())
				}).Return(errors.New("error"))
				d.repo.On("UpdateRepository", mock.Anything, mock.Anything).Return(errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error if UpdateHardware fail",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.UpdateRepositoryInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
				Hardware: &dto.RepositoryHardwareInput{
					NodeName: "cpu",
					Name:     "cpu",
				},
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{GitlabAccessToken: utils.Ptr("token")}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					RefGitRepoID: 1,
					Hardware:     &entities.Hardware{},
				}, nil)
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything).Return(&entities.RepoMember{Role: enums.RepoRole_Owner}, nil)
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(context.Background())
				}).Return(errors.New("error"))
				d.repo.On("UpdateHardware", mock.Anything, mock.Anything).Return(errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should success",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.UpdateRepositoryInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{GitlabAccessToken: utils.Ptr("token")}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					RefGitRepoID: 1,
					UserID:       &uuidInput,
					OrgID:        nil,
				}, nil)
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything).Return(&entities.RepoMember{Role: enums.RepoRole_Owner}, nil)
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(context.Background())
				}).Return(nil)
				d.repo.On("UpdateRepository", mock.Anything, mock.Anything).Return(nil)
			},
			expError: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				config:   &configs.GlobalConfig{},
				repo:     &repository_mocks.MockRepository{},
				gitlab:   &gitlab_mocks.MockGitlabClient{},
				workflow: &workflow_mocks.MockWorkflowUsecase{},
			}
			tt.mockFn(d)

			u := repository.New(d.config, d.repo, d.gitlab, d.workflow, nil, nil)
			err := u.UpdateRepository(tt.ctx, tt.input)
			require.Equal(t, tt.expError, err)
		})
	}
}

func TestDeleteAvatarRepo(t *testing.T) {
	repoType := enums.RepoType_Models
	namespace := "namespace"
	repoName := "repo-name"
	repoID := *types.NewRepoID(repoType, namespace, repoName)

	uuidInput := uuid.New()
	type dependencies struct {
		config   *configs.GlobalConfig
		repo     *repository_mocks.MockRepository
		gitlab   *gitlab_mocks.MockGitlabClient
		workflow *workflow_mocks.MockWorkflowUsecase
		aws      *aws_mocks.MockAWSClient
	}

	avatar := "avatar"
	tests := []struct {
		name     string
		ctx      context.Context
		mockFn   func(d *dependencies)
		input    dto.DeleteRepositoryAvatarInput
		expError error
	}{
		{
			name: "should return error if FindUserByID fail",
			ctx:  context.TODO(),
			input: dto.DeleteRepositoryAvatarInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error if FindUserByID not found",
			ctx:  context.TODO(),
			input: dto.DeleteRepositoryAvatarInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expError: usecase.ErrUserNotFound,
		},
		{
			name: "should return error if FindRepository fail",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.DeleteRepositoryAvatarInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},

			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error if FindRepository fail",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.DeleteRepositoryAvatarInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expError: usecase.ErrRepositoryNotFound,
		},
		{
			name: "should return error if FindRepositoryMember fail",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.DeleteRepositoryAvatarInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{GitlabAccessToken: utils.Ptr("token")}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					RefGitRepoID: 1,
				}, nil)
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error if Save fail",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.DeleteRepositoryAvatarInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{GitlabAccessToken: utils.Ptr("token")}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					RefGitRepoID: 1,
					UserID:       &uuidInput,
					OrgID:        nil,
					Avatar:       &avatar,
				}, nil)
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything).Return(&entities.RepoMember{Role: enums.RepoRole_Owner}, nil)
				d.aws.On("DeleteImage", mock.Anything, mock.Anything).Return(errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error if Save fail",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.DeleteRepositoryAvatarInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{GitlabAccessToken: utils.Ptr("token")}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					RefGitRepoID: 1,
					UserID:       &uuidInput,
					OrgID:        nil,
				}, nil)
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything).Return(&entities.RepoMember{Role: enums.RepoRole_Owner}, nil)
				d.repo.On("Save", mock.Anything, mock.Anything).Return(errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should success",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.DeleteRepositoryAvatarInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{GitlabAccessToken: utils.Ptr("token")}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					RefGitRepoID: 1,
					UserID:       &uuidInput,
					OrgID:        nil,
				}, nil)
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything).Return(&entities.RepoMember{Role: enums.RepoRole_Owner}, nil)
				d.repo.On("Save", mock.Anything, mock.Anything).Return(nil)
			},
			expError: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				config:   &configs.GlobalConfig{},
				repo:     &repository_mocks.MockRepository{},
				gitlab:   &gitlab_mocks.MockGitlabClient{},
				workflow: &workflow_mocks.MockWorkflowUsecase{},
				aws:      &aws_mocks.MockAWSClient{},
			}
			tt.mockFn(d)

			u := repository.New(d.config, d.repo, d.gitlab, d.workflow, nil, d.aws)
			err := u.DeleteAvatarRepo(tt.ctx, tt.input)
			require.Equal(t, tt.expError, err)
		})
	}
}

func TestGetAllRepositories(t *testing.T) {
	uuidInput := uuid.New()
	type dependencies struct {
		repo      *repository_mocks.MockRepository
		aws       *aws_mocks.MockAWSClient
		clientset *fake.Clientset
	}

	avatar := "avatar"
	tests := []struct {
		name     string
		ctx      context.Context
		mockFn   func(d *dependencies)
		input    dto.GetRepositoriesInput
		expError error
	}{
		{
			name: "should return error if CountRepositories fail",
			ctx:  context.TODO(),
			input: dto.GetRepositoriesInput{
				Keyword: "abc",
			},
			mockFn: func(d *dependencies) {
				d.repo.On("CountRepositories", mock.Anything, mock.Anything).Return(int64(0), errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error if GetAllRepositories fail",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.GetRepositoriesInput{
				Keyword: "abc",
			},
			mockFn: func(d *dependencies) {
				d.repo.On("CountRepositories", mock.Anything, mock.Anything).Return(int64(1), nil)
				d.repo.On("ListRepositories", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should error if GenPreSignUrl fail",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.GetRepositoriesInput{
				Keyword: "abc",
			},
			mockFn: func(d *dependencies) {
				d.repo.On("CountRepositories", mock.Anything, mock.Anything).Return(int64(1), nil)
				d.repo.On("ListRepositories", mock.Anything, mock.Anything).Return([]entities.Repository{
					{
						BaseModel: entities.BaseModel{
							ID: uuidInput,
						},
						Type: enums.RepoType_Models,
						User: &entities.User{
							Username: "username",
							Avatar:   &avatar,
						},
						Name:   "name",
						Avatar: &avatar,
						UserID: &uuidInput,
						OrgID:  nil,
					},
				}, nil)
				d.aws.On("GenPreSignUrl", mock.Anything, mock.Anything).Return("", errors.New("error")).Once()
				d.repo.On("ListRepoTagsByQueryModel", mock.Anything, mock.Anything).Return([]entities.Tag{}, nil)
			},
			expError: errors.New("error"),
		},
		{
			name: "should error if GenPreSignUrl fail",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.GetRepositoriesInput{
				Keyword: "abc",
			},
			mockFn: func(d *dependencies) {
				d.repo.On("CountRepositories", mock.Anything, mock.Anything).Return(int64(1), nil)
				d.repo.On("ListRepositories", mock.Anything, mock.Anything).Return([]entities.Repository{
					{
						BaseModel: entities.BaseModel{
							ID: uuidInput,
						},
						Type: enums.RepoType_Models,
						User: &entities.User{
							Username: "username",
							Avatar:   &avatar,
						},
						Name:   "name",
						Avatar: &avatar,
						UserID: &uuidInput,
						OrgID:  nil,
					},
				}, nil)
				d.aws.On("GenPreSignUrl", mock.Anything, mock.Anything).Return("", nil).Once()
				d.aws.On("GenPreSignUrl", mock.Anything, mock.Anything).Return(avatar, errors.New("error")).Once()
				d.repo.On("ListRepoTagsByQueryModel", mock.Anything, mock.Anything).Return([]entities.Tag{}, nil)
			},
			expError: errors.New("error"),
		},
		{
			name: "should error if ListRepoTagsByQueryModel fail",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.GetRepositoriesInput{
				Keyword: "abc",
			},
			mockFn: func(d *dependencies) {
				d.repo.On("CountRepositories", mock.Anything, mock.Anything).Return(int64(1), nil)
				d.repo.On("ListRepositories", mock.Anything, mock.Anything).Return([]entities.Repository{
					{
						BaseModel: entities.BaseModel{
							ID: uuidInput,
						},
						Type: enums.RepoType_Models,
						User: &entities.User{
							Username: "username",
							Avatar:   &avatar,
						},
						Name:   "name",
						Avatar: &avatar,
						UserID: &uuidInput,
						OrgID:  nil,
					},
				}, nil)
				d.aws.On("GenPreSignUrl", mock.Anything, mock.Anything).Return(avatar, nil)
				d.repo.On("ListRepoTagsByQueryModel", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should success",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.GetRepositoriesInput{
				Keyword:        "abc",
				RepositoryType: enums.RepoType_Spaces,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("CountRepositories", mock.Anything, mock.Anything).Return(int64(1), nil)
				d.repo.On("ListRepositories", mock.Anything, mock.Anything).Return([]entities.Repository{
					{
						BaseModel: entities.BaseModel{
							ID: uuidInput,
						},
						Type: enums.RepoType_Spaces,
						User: &entities.User{
							Username: "username",
							Avatar:   &avatar,
						},
						Name:   "name",
						Avatar: &avatar,
						UserID: &uuidInput,
						OrgID:  nil,
					},
				}, nil)
				d.aws.On("GenPreSignUrl", mock.Anything, mock.Anything).Return(avatar, nil)
				d.repo.On("ListRepoTagsByQueryModel", mock.Anything, mock.Anything).Return([]entities.Tag{}, nil)
			},
			expError: nil,
		},
		{
			name: "should success",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.GetRepositoriesInput{
				Keyword: "abc",
			},
			mockFn: func(d *dependencies) {
				d.repo.On("CountRepositories", mock.Anything, mock.Anything).Return(int64(1), nil)
				d.repo.On("ListRepositories", mock.Anything, mock.Anything).Return([]entities.Repository{
					{
						BaseModel: entities.BaseModel{
							ID: uuidInput,
						},
						Type: enums.RepoType_Models,
						User: &entities.User{
							Username: "username",
							Avatar:   &avatar,
						},
						Name:   "name",
						Avatar: &avatar,
						UserID: &uuidInput,
						OrgID:  nil,
					},
				}, nil)
				d.aws.On("GenPreSignUrl", mock.Anything, mock.Anything).Return(avatar, nil)
				d.repo.On("ListRepoTagsByQueryModel", mock.Anything, mock.Anything).Return([]entities.Tag{}, nil)
			},
			expError: nil,
		},
		{
			name: "should success",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.GetRepositoriesInput{
				Keyword:        "abc",
				RepositoryType: enums.RepoType_Spaces,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("CountRepositories", mock.Anything, mock.Anything).Return(int64(1), nil)
				d.repo.On("ListRepositories", mock.Anything, mock.Anything).Return([]entities.Repository{
					{
						BaseModel: entities.BaseModel{
							ID: uuidInput,
						},
						Type: enums.RepoType_Spaces,
						User: &entities.User{
							Username: "username",
							Avatar:   &avatar,
						},
						Name:   "name",
						Avatar: &avatar,
						UserID: &uuidInput,
						OrgID:  nil,
						Deployment: &entities.Deployment{
							Name: "deployment-name",
						},
					},
				}, nil)
				d.aws.On("GenPreSignUrl", mock.Anything, mock.Anything).Return(avatar, nil)
				d.repo.On("ListRepoTagsByQueryModel", mock.Anything, mock.Anything).Return([]entities.Tag{}, nil)
			},
			expError: nil,
		},
		{
			name: "should return error getDeploymentStatus fail",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.GetRepositoriesInput{
				Keyword:        "abc",
				RepositoryType: enums.RepoType_Spaces,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("CountRepositories", mock.Anything, mock.Anything).Return(int64(1), nil)
				d.repo.On("ListRepositories", mock.Anything, mock.Anything).Return([]entities.Repository{
					{
						BaseModel: entities.BaseModel{
							ID: uuidInput,
						},
						Type: enums.RepoType_Spaces,
						User: &entities.User{
							Username: "username",
							Avatar:   &avatar,
						},
						Name:   "name",
						Avatar: &avatar,
						UserID: &uuidInput,
						OrgID:  nil,
						Deployment: &entities.Deployment{
							Name:   "deployment-name",
							Status: enums.ArgoWorkflowStatus_Succeeded,
						},
					},
				}, nil)
				k8sPodList := &v1.PodList{Items: []v1.Pod{}}
				d.clientset.PrependReactor("list", "pods", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, k8sPodList, errors.New("error")
				})
			},
			expError: errors.New("error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			fakeClient := fake.NewSimpleClientset()
			d := &dependencies{
				repo:      &repository_mocks.MockRepository{},
				aws:       &aws_mocks.MockAWSClient{},
				clientset: fakeClient,
			}
			tt.mockFn(d)

			u := repository.New(nil, d.repo, nil, nil, d.clientset, d.aws)
			_, err := u.GetAllRepositories(tt.ctx, tt.input)
			require.Equal(t, tt.expError, err)
		})
	}
}

func TestGetFileFromRepository(t *testing.T) {
	type dependencies struct {
		repo         *repository_mocks.MockRepository
		gitlabClient *gitlab_mocks.MockGitlabClient
	}

	tests := []struct {
		name     string
		ctx      context.Context
		mockFn   func(d *dependencies)
		input    dto.GetFileFromRepositoryInput
		expError error
	}{
		{
			name: "should return error if FindUser fail",
			ctx:  context.TODO(),
			input: dto.GetFileFromRepositoryInput{
				CurrentUserId: uuid.New(),
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error if FindUser not found",
			ctx:  context.TODO(),
			input: dto.GetFileFromRepositoryInput{
				CurrentUserId: uuid.New(),
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expError: usecase.ErrUserNotFound,
		},
		{
			name: "should return error if FindRepository fail",
			ctx:  context.TODO(),
			input: dto.GetFileFromRepositoryInput{
				CurrentUserId: uuid.New(),
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error if FindRepository not found",
			ctx:  context.TODO(),
			input: dto.GetFileFromRepositoryInput{
				CurrentUserId: uuid.New(),
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expError: usecase.ErrRepositoryNotFound,
		},
		{
			name: "should return error if GetHeaderRawFileFromRepo fail",
			ctx:  context.TODO(),
			input: dto.GetFileFromRepositoryInput{
				CurrentUserId: uuid.New(),
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{}, nil)
				d.gitlabClient.On("GetHeaderRawFileFromRepo", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error if GetFileFromRepo fail",
			ctx:  context.TODO(),
			input: dto.GetFileFromRepositoryInput{
				CurrentUserId: uuid.New(),
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{}, nil)
				d.gitlabClient.On("GetHeaderRawFileFromRepo", mock.Anything, mock.Anything).Return(&http.Response{
					StatusCode: 200,
					Body:       io.NopCloser(bytes.NewBufferString("")),
					Header:     http.Header{},
				}, nil)
				d.gitlabClient.On("GetFileFromRepo", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error if GetSingleProjectCommit fail",
			ctx:  context.TODO(),
			input: dto.GetFileFromRepositoryInput{
				CurrentUserId: uuid.New(),
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{}, nil)
				d.gitlabClient.On("GetHeaderRawFileFromRepo", mock.Anything, mock.Anything).Return(&http.Response{
					StatusCode: 200,
					Body:       io.NopCloser(bytes.NewBufferString("")),
					Header:     http.Header{},
				}, nil)
				d.gitlabClient.On("GetFileFromRepo", mock.Anything, mock.Anything).Return(&gitlab.GetFileResponse{}, nil)
				d.gitlabClient.On("GetSingleProjectCommit", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should success",
			ctx:  context.TODO(),
			input: dto.GetFileFromRepositoryInput{
				CurrentUserId: uuid.New(),
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{}, nil)
				d.gitlabClient.On("GetHeaderRawFileFromRepo", mock.Anything, mock.Anything).Return(&http.Response{
					StatusCode: 200,
					Body:       io.NopCloser(bytes.NewBufferString("")),
					Header:     http.Header{},
				}, nil)
				d.gitlabClient.On("GetFileFromRepo", mock.Anything, mock.Anything).Return(&gitlab.GetFileResponse{}, nil)
				d.gitlabClient.On("GetSingleProjectCommit", mock.Anything, mock.Anything).Return(nil, nil)
			},
			expError: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				repo:         &repository_mocks.MockRepository{},
				gitlabClient: &gitlab_mocks.MockGitlabClient{},
			}
			tt.mockFn(d)

			u := repository.New(nil, d.repo, d.gitlabClient, nil, nil, nil)
			_, err := u.GetFileFromRepository(tt.ctx, tt.input)
			require.Equal(t, tt.expError, err)
		})
	}
}

func TestUploadAvatarRepo(t *testing.T) {
	type dependencies struct {
		repo         *repository_mocks.MockRepository
		gitlabClient *gitlab_mocks.MockGitlabClient
		awsClient    *aws_mocks.MockAWSClient
	}

	createMockFileHeader := func(fieldName, fileName, fileContent string) *multipart.FileHeader {
		var buffer bytes.Buffer
		writer := multipart.NewWriter(&buffer)

		part, _ := writer.CreateFormFile(fieldName, fileName)
		io.Copy(part, strings.NewReader(fileContent))

		writer.Close()

		reader := multipart.NewReader(&buffer, writer.Boundary())
		form, _ := reader.ReadForm(1024)

		return form.File[fieldName][0]
	}

	mockFileHeader := createMockFileHeader("avatar", "mockfile.txt", "This is a mock file content")

	tests := []struct {
		name     string
		ctx      context.Context
		mockFn   func(d *dependencies)
		input    dto.UploadRepositoryAvatarInput
		expError error
	}{
		{
			name: "should return error if FindUserByID fail",
			ctx:  context.TODO(),
			input: dto.UploadRepositoryAvatarInput{
				CurrentUserId: uuid.New(),
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(nil, errors.New("errors"))
			},
			expError: errors.New("errors"),
		},
		{
			name: "should return error if FindUserByID not found",
			ctx:  context.TODO(),
			input: dto.UploadRepositoryAvatarInput{
				CurrentUserId: uuid.New(),
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expError: usecase.ErrUserNotFound,
		},
		{
			name: "should return error if FindRepository fail",
			ctx:  context.TODO(),
			input: dto.UploadRepositoryAvatarInput{
				CurrentUserId: uuid.New(),
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{Role: enums.UserRole_User}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, errors.New("errors"))
			},
			expError: errors.New("errors"),
		},
		{
			name: "should return error if FindRepository not found",
			ctx:  context.TODO(),
			input: dto.UploadRepositoryAvatarInput{
				CurrentUserId: uuid.New(),
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{Role: enums.UserRole_User}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expError: usecase.ErrRepositoryNotFound,
		},
		{
			name: "should return error if user have no permission return no permission",
			ctx:  context.TODO(),
			input: dto.UploadRepositoryAvatarInput{
				CurrentUserId: uuid.New(),
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{Role: enums.UserRole_User}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{}, nil)
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything).Return(&entities.RepoMember{Role: enums.RepoRole_Developer}, nil)
			},
			expError: usecase.ErrNoPermission,
		},
		{
			name: "should return error if UploadImage fail",
			ctx:  context.TODO(),
			input: dto.UploadRepositoryAvatarInput{
				CurrentUserId: uuid.New(),
				File:          mockFileHeader,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{Role: enums.UserRole_User}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{}, nil)
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything).Return(&entities.RepoMember{Role: enums.RepoRole_Owner}, nil)
				d.awsClient.On("UploadImage", mock.Anything, mock.Anything, mock.Anything).Return("", errors.New("errors"))
			},
			expError: errors.New("errors"),
		},
		{
			name: "should return error if Save fail",
			ctx:  context.TODO(),
			input: dto.UploadRepositoryAvatarInput{
				CurrentUserId: uuid.New(),
				File:          mockFileHeader,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{Role: enums.UserRole_User}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{}, nil)
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything).Return(&entities.RepoMember{Role: enums.RepoRole_Owner}, nil)
				d.awsClient.On("UploadImage", mock.Anything, mock.Anything, mock.Anything).Return("http://imageURL", nil)
				d.repo.On("Save", mock.Anything, mock.Anything).Return(errors.New("errors"))
			},
			expError: errors.New("errors"),
		},
		{
			name: "should success",
			ctx:  context.TODO(),
			input: dto.UploadRepositoryAvatarInput{
				CurrentUserId: uuid.New(),
				File:          mockFileHeader,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{Role: enums.UserRole_User}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{}, nil)
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything).Return(&entities.RepoMember{Role: enums.RepoRole_Owner}, nil)
				d.awsClient.On("UploadImage", mock.Anything, mock.Anything, mock.Anything).Return("http://imageURL", nil)
				d.repo.On("Save", mock.Anything, mock.Anything).Return(nil)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				repo:         &repository_mocks.MockRepository{},
				gitlabClient: &gitlab_mocks.MockGitlabClient{},
				awsClient:    &aws_mocks.MockAWSClient{},
			}
			tt.mockFn(d)

			u := repository.New(nil, d.repo, d.gitlabClient, nil, nil, d.awsClient)
			_, err := u.UploadAvatarRepo(tt.ctx, tt.input)
			require.Equal(t, tt.expError, err)
		})
	}
}

func TestListRepoBranches(t *testing.T) {
	repoType := enums.RepoType_Models
	namespace := "namespace"
	repoName := "repo-name"
	repoID := *types.NewRepoID(repoType, namespace, repoName)

	uuidInput := uuid.New()
	type dependencies struct {
		repo   *repository_mocks.MockRepository
		gitlab *gitlab_mocks.MockGitlabClient
	}

	tests := []struct {
		name     string
		ctx      context.Context
		mockFn   func(d *dependencies)
		input    dto.GetRepositoryBranchesInput
		exOutput []dto.RepositoryBranchInfo
		expError error
	}{
		{
			name: "should return error if FindUser fail",
			ctx:  context.TODO(),
			input: dto.GetRepositoryBranchesInput{
				CurrentUserId: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error if FindUser not found",
			ctx:  context.TODO(),
			input: dto.GetRepositoryBranchesInput{
				CurrentUserId: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expError: usecase.ErrUserNotFound,
		},
		{
			name: "should return error if FindRepository fail",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.GetRepositoryBranchesInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},

			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error if FindRepository not found",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.GetRepositoryBranchesInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},

			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expError: usecase.ErrRepositoryNotFound,
		},
		{
			name: "should return error if GetProjectBranches fail",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.GetRepositoryBranchesInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},

			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					RefGitRepoID: 1,
				}, nil)
				d.gitlab.On("GetProjectBranches", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should success",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.GetRepositoryBranchesInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{GitlabAccessToken: utils.Ptr("token")}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					RefGitRepoID: 1,
				}, nil)
				d.gitlab.On("GetProjectBranches", mock.Anything, mock.Anything).Return([]gitlab.RepositoryBranchInfo{
					{RepositoryBranch: gitlab.RepositoryBranch{
						Name: "main",
					}},
				}, nil)
			},
			exOutput: []dto.RepositoryBranchInfo{
				{RepositoryBranch: dto.RepositoryBranch{
					Name: "main",
				}},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				repo:   &repository_mocks.MockRepository{},
				gitlab: &gitlab_mocks.MockGitlabClient{},
			}
			tt.mockFn(d)

			u := repository.New(nil, d.repo, d.gitlab, nil, nil, nil)
			output, err := u.ListRepoBranches(tt.ctx, tt.input)
			require.Equal(t, tt.exOutput, output)
			require.Equal(t, tt.expError, err)
		})
	}
}

func TestListRepoFiles(t *testing.T) {
	repoType := enums.RepoType_Models
	namespace := "namespace"
	repoName := "repo-name"
	repoID := *types.NewRepoID(repoType, namespace, repoName)

	uuidInput := uuid.New()
	type dependencies struct {
		repo   *repository_mocks.MockRepository
		gitlab *gitlab_mocks.MockGitlabClient
	}

	tests := []struct {
		name     string
		ctx      context.Context
		mockFn   func(d *dependencies)
		input    dto.GetRepositoryFilesInput
		exOutput []dto.RepositoryFile
		expError error
	}{
		{
			name: "should return error if FindUser fail",
			ctx:  context.TODO(),
			input: dto.GetRepositoryFilesInput{
				CurrentUserId: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error if FindRepository fail",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.GetRepositoryFilesInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},

			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error if GetProjectFiles fail",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.GetRepositoryFilesInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					RefGitRepoID: 1,
				}, nil)
				d.gitlab.On("GetProjectFiles", mock.Anything, mock.Anything).Return(nil, nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should success",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.GetRepositoryFilesInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					RefGitRepoID: 1,
				}, nil)
				d.gitlab.On("GetProjectFiles", mock.Anything, mock.Anything).Return([]gitlab.RepositoryFile{
					{
						Name: "main.go",
						Type: "blob",
					},
				}, nil, nil)
			},
			exOutput: []dto.RepositoryFile{
				{
					Name: "main.go",
					Type: "blob",
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				repo:   &repository_mocks.MockRepository{},
				gitlab: &gitlab_mocks.MockGitlabClient{},
			}
			tt.mockFn(d)

			u := repository.New(nil, d.repo, d.gitlab, nil, nil, nil)
			output, _, err := u.ListRepoFiles(tt.ctx, tt.input)
			require.Equal(t, tt.exOutput, output)
			require.Equal(t, tt.expError, err)
		})
	}
}

func TestListRepoCommits(t *testing.T) {
	repoType := enums.RepoType_Models
	namespace := "namespace"
	repoName := "repo-name"
	repoID := *types.NewRepoID(repoType, namespace, repoName)

	uuidInput := uuid.New()
	type dependencies struct {
		repo      *repository_mocks.MockRepository
		gitlab    *gitlab_mocks.MockGitlabClient
		awsClient *aws_mocks.MockAWSClient
	}

	mockUserID := uuid.New()
	avatar := "https://gitlab.com/uploads/-/system/user/avatar/1/avatar.png"
	tests := []struct {
		name     string
		ctx      context.Context
		mockFn   func(d *dependencies)
		input    dto.GetRepositoryCommitsInput
		exOutput *dto.GetRepositoryCommitsOutput
		expError error
	}{
		{
			name: "should error if FindUser fail",
			ctx:  context.TODO(),
			input: dto.GetRepositoryCommitsInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
			exOutput: nil,
		},
		{
			name: "should error if FindUser not found",
			ctx:  context.TODO(),
			input: dto.GetRepositoryCommitsInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expError: usecase.ErrUserNotFound,
			exOutput: nil,
		},
		{
			name: "should error if FindRepository fail",
			ctx:  context.TODO(),
			input: dto.GetRepositoryCommitsInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{GitlabAccessToken: utils.Ptr("token")}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expError: usecase.ErrRepositoryNotFound,
			exOutput: nil,
		},
		{
			name: "should error if FindRepository not found",
			ctx:  context.TODO(),
			input: dto.GetRepositoryCommitsInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{GitlabAccessToken: utils.Ptr("token")}, nil)
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{BaseModel: entities.BaseModel{ID: mockUserID}}, nil).Once()
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
			exOutput: nil,
		},
		{
			name: "should return error if GetProjectCommits fail",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.GetRepositoryCommitsInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},

			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{GitlabAccessToken: utils.Ptr("token")}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					RefGitRepoID: 1,
				}, nil)
				d.gitlab.On("GetProjectCommits", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error if FindDeployment fail",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.GetRepositoryCommitsInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					RefGitRepoID: 1,
				}, nil)
				d.gitlab.On("GetProjectCommits", mock.Anything, mock.Anything).Return(&gitlab.HTTPResponse[[]gitlab.RepositoryCommit]{
					Data: &[]gitlab.RepositoryCommit{
						{
							ID:          "1",
							ShortID:     "1234",
							AuthorEmail: "<EMAIL>",
						},
					},
					Pagination: &gitlab.Pagination{
						Page: 0, PerPage: 0,
					},
				}, nil)
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{
					Email: "<EMAIL>",
				}, nil)
				d.repo.On("FindDeployment", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			exOutput: nil,
			expError: errors.New("error"),
		},

		{
			name: "should success",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.GetRepositoryCommitsInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{BaseModel: entities.BaseModel{ID: mockUserID}}, nil).Once()
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					RefGitRepoID: 1,
				}, nil)
				d.gitlab.On("GetProjectCommits", mock.Anything, mock.Anything).Return(&gitlab.HTTPResponse[[]gitlab.RepositoryCommit]{
					Data: &[]gitlab.RepositoryCommit{
						{
							ID:          "1",
							ShortID:     "1234",
							AuthorEmail: "<EMAIL>",
						},
					},
					Pagination: &gitlab.Pagination{
						Page: 0, PerPage: 0,
					},
				}, nil)
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{
					Email:  "<EMAIL>",
					Avatar: &avatar,
				}, nil)
				d.awsClient.On("GenPreSignUrl", mock.Anything, mock.Anything).Return(avatar, errors.New("error"))
				d.repo.On("FindDeployment", mock.Anything, mock.Anything).Return(&entities.Deployment{
					Commit: "1",
				}, nil)
				d.gitlab.On("GetMergeRequests", mock.Anything, mock.Anything).Return([]gitlab.MergeRequest{
					{SHA: "1", ID: 10},
				}, nil)
			},
			exOutput: &dto.GetRepositoryCommitsOutput{
				Data: &[]dto.RepositoryCommit{
					{
						ID:          "1",
						ShortID:     "1234",
						AuthorEmail: "<EMAIL>",
						User: dto.User{
							ID: uuid.Nil.String(),
						},
						MergeRequest: &dto.MergeRequest{
							CommitId: "1",
							ID:       10,
						},
						Status: utils.Ptr("Current"),
					},
				},
				Pagination: &dto.Pagination{Total: 0, PageNo: 0, PageSize: 0},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				repo:      &repository_mocks.MockRepository{},
				gitlab:    &gitlab_mocks.MockGitlabClient{},
				awsClient: &aws_mocks.MockAWSClient{},
			}
			tt.mockFn(d)

			u := repository.New(nil, d.repo, d.gitlab, nil, nil, d.awsClient)
			output, err := u.ListRepoCommits(tt.ctx, tt.input)
			require.Equal(t, tt.exOutput, output)
			require.Equal(t, tt.expError, err)
		})
	}
}

func TestGetLastRepoCommit(t *testing.T) {
	repoType := enums.RepoType_Models
	namespace := "namespace"
	repoName := "repo-name"
	repoID := *types.NewRepoID(repoType, namespace, repoName)

	uuidInput := uuid.New()
	type dependencies struct {
		repo      *repository_mocks.MockRepository
		gitlab    *gitlab_mocks.MockGitlabClient
		awsClient *aws_mocks.MockAWSClient
	}

	tests := []struct {
		name     string
		ctx      context.Context
		mockFn   func(d *dependencies)
		input    dto.GetRepositoryCommitsInput
		exOutput *dto.RepositoryCommit
		expError error
	}{
		{
			name: "should error if FindUser fail",
			ctx:  context.TODO(),
			input: dto.GetRepositoryCommitsInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
			exOutput: nil,
		},
		{
			name: "should error if FindUser not found",
			ctx:  context.TODO(),
			input: dto.GetRepositoryCommitsInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expError: usecase.ErrUserNotFound,
			exOutput: nil,
		},
		{
			name: "should error if FindRepository fail",
			ctx:  context.TODO(),
			input: dto.GetRepositoryCommitsInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{GitlabAccessToken: utils.Ptr("token")}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expError: usecase.ErrRepositoryNotFound,
			exOutput: nil,
		},
		{
			name: "should error if FindRepository not found",
			ctx:  context.TODO(),
			input: dto.GetRepositoryCommitsInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{GitlabAccessToken: utils.Ptr("token")}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
			exOutput: nil,
		},
		{
			name: "should error if GetProjectCommits fail",
			ctx:  context.TODO(),
			input: dto.GetRepositoryCommitsInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{GitlabAccessToken: utils.Ptr("token")}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{}, nil)
				d.gitlab.On("GetProjectCommits", mock.Anything, mock.Anything).Return(nil, errors.New("error"))

			},
			expError: errors.New("error"),
			exOutput: nil,
		},
		{
			name: "should success",
			ctx:  context.TODO(),
			input: dto.GetRepositoryCommitsInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{GitlabAccessToken: utils.Ptr("token")}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{}, nil)
				d.gitlab.On("GetProjectCommits", mock.Anything, mock.Anything).Return(&gitlab.HTTPResponse[[]gitlab.RepositoryCommit]{
					Data: &[]gitlab.RepositoryCommit{
						{
							ID: "1",
						},
					},
				}, nil)

			},
			expError: nil,
			exOutput: &dto.RepositoryCommit{
				ID: "1",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				repo:      &repository_mocks.MockRepository{},
				gitlab:    &gitlab_mocks.MockGitlabClient{},
				awsClient: &aws_mocks.MockAWSClient{},
			}
			tt.mockFn(d)

			u := repository.New(nil, d.repo, d.gitlab, nil, nil, d.awsClient)
			output, err := u.GetLastRepoCommit(tt.ctx, tt.input)
			require.Equal(t, tt.exOutput, output)
			require.Equal(t, tt.expError, err)
		})
	}
}

func TestListRepoTemplates(t *testing.T) {

	type dependencies struct {
		repo      *repository_mocks.MockRepository
		gitlab    *gitlab_mocks.MockGitlabClient
		awsClient *aws_mocks.MockAWSClient
	}

	tests := []struct {
		name     string
		ctx      context.Context
		mockFn   func(d *dependencies)
		exOutput []dto.RepoTemplate
		expError error
	}{
		{
			name: "should error if ListTemplates fail",
			ctx:  context.TODO(),

			mockFn: func(d *dependencies) {
				d.repo.On("ListTemplates", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
			exOutput: nil,
		},
		{
			name: "should success",
			ctx:  context.TODO(),
			mockFn: func(d *dependencies) {
				d.repo.On("ListTemplates", mock.Anything, mock.Anything).Return([]entities.RepoTemplate{}, nil)
			},
			expError: nil,
			exOutput: []dto.RepoTemplate{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				repo:      &repository_mocks.MockRepository{},
				gitlab:    &gitlab_mocks.MockGitlabClient{},
				awsClient: &aws_mocks.MockAWSClient{},
			}
			tt.mockFn(d)

			u := repository.New(nil, d.repo, d.gitlab, nil, nil, d.awsClient)
			output, err := u.ListRepoTemplates(tt.ctx)
			require.Equal(t, tt.exOutput, output)
			require.Equal(t, tt.expError, err)
		})
	}
}

func TestInviteRepoMember(t *testing.T) {
	userId := uuid.New()
	type dependencies struct {
		repo         *repository_mocks.MockRepository
		gitlabClient *gitlab_mocks.MockGitlabClient
	}

	repoType := enums.RepoType_Models
	namespace := "namespace"
	repoName := "repo-name"
	repoID := *types.NewRepoID(repoType, namespace, repoName)

	orgId := uuid.New()
	expireTime := time.Date(2026, 1, 2, 15, 4, 5, 0, time.UTC)
	pastTime := time.Date(2024, 1, 2, 15, 4, 5, 0, time.UTC)
	tests := []struct {
		name      string
		ctx       context.Context
		input     dto.InviteRepoMemberInput
		mockFn    func(d *dependencies)
		expectErr error
	}{
		{
			name: "should return error when repository.FindUserByID return error",
			ctx:  context.Background(),
			input: dto.InviteRepoMemberInput{
				RepoID:   repoID,
				UserId:   userId,
				Role:     "developer",
				ExpireAt: &expireTime,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return error time is past",
			ctx:  context.Background(),
			input: dto.InviteRepoMemberInput{
				RepoID:   repoID,
				UserId:   userId,
				Role:     "developer",
				ExpireAt: &pastTime,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{Role: enums.UserRole_User}, nil)

			},
			expectErr: usecase.ErrTimeIsPast,
		},
		{
			name: "should return error when repository is owned by organization and owner updates other owner's role to developer return no permission",
			ctx:  context.Background(),
			input: dto.InviteRepoMemberInput{
				RepoID:   repoID,
				UserId:   userId,
				Role:     "developer",
				ExpireAt: &expireTime,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{Role: enums.UserRole_User}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{OrgID: &orgId}, nil)
				d.repo.On("FindOrgMember", mock.Anything, mock.Anything).Return(&entities.OrgMember{Role: enums.OrgRole_Owner}, nil)

			},
			expectErr: usecase.ErrInviteOrgOwnerAsDeveloper,
		},
		{
			name: "should return error when repository is owned by user and user invite other user as owner",
			ctx:  context.Background(),
			input: dto.InviteRepoMemberInput{
				RepoID:   repoID,
				UserId:   userId,
				Role:     "owner",
				ExpireAt: &expireTime,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{Role: enums.UserRole_User}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{}, nil)
			},
			expectErr: usecase.ErrCannotInviteUserAsOwner,
		},
		{
			name: "should return error when input role = owner",
			ctx:  context.Background(),
			input: dto.InviteRepoMemberInput{
				RepoID:   repoID,
				UserId:   userId,
				Role:     "owner",
				ExpireAt: &expireTime,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{}, nil)
			},
			expectErr: usecase.ErrCannotInviteUserAsOwner,
		},
		{
			name: "should return error when repository.FindUserByID return gorm.ErrRecordNotFound",
			ctx:  context.Background(),
			input: dto.InviteRepoMemberInput{
				RepoID:   repoID,
				UserId:   userId,
				Role:     "developer",
				ExpireAt: &expireTime,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expectErr: usecase.ErrUserNotFound,
		},
		{
			name: "should return error when repository.FindRepository return error",
			ctx:  context.Background(),
			input: dto.InviteRepoMemberInput{
				RepoID:   repoID,
				UserId:   userId,
				Role:     "developer",
				ExpireAt: &expireTime,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return error when repository.FindRepository return gorm.ErrRecordNotFound",
			ctx:  context.Background(),
			input: dto.InviteRepoMemberInput{
				RepoID:   repoID,
				UserId:   userId,
				Role:     "developer",
				ExpireAt: &expireTime,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expectErr: usecase.ErrRepositoryNotFound,
		},
		{
			name: "should return error when repository.Create return error",
			ctx:  context.Background(),
			input: dto.InviteRepoMemberInput{
				RepoID:   repoID,
				UserId:   userId,
				Role:     "developer",
				ExpireAt: &expireTime,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{}, nil)
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything).Return(&entities.RepoMember{Role: enums.RepoRole_Owner}, nil)
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(context.Background())
				}).Return(errors.New("error"))
				d.gitlabClient.On("GetMemberOfProject", mock.Anything, mock.Anything).Return(&gitlab.*********************ponse{}, nil)
				d.gitlabClient.On("AddUserToProject", mock.Anything, mock.Anything).Return(&gitlab.AddUserToProjectResponse{}, nil)
				d.repo.On("Create", mock.Anything, mock.Anything).Return(errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return error when AddUserToProject return error",
			ctx:  context.Background(),
			input: dto.InviteRepoMemberInput{
				RepoID:   repoID,
				UserId:   userId,
				Role:     "developer",
				ExpireAt: &expireTime,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{}, nil)
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything).Return(&entities.RepoMember{Role: enums.RepoRole_Owner}, nil)
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(context.Background())
				}).Return(errors.New("error"))
				d.repo.On("Create", mock.Anything, mock.Anything).Return(nil)
				d.gitlabClient.On("GetMemberOfProject", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
				d.gitlabClient.On("AddUserToProject", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "success without add user to project",
			ctx:  context.Background(),
			input: dto.InviteRepoMemberInput{
				RepoID:   repoID,
				UserId:   userId,
				Role:     "developer",
				ExpireAt: &expireTime,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{}, nil)
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything).Return(&entities.RepoMember{Role: enums.RepoRole_Owner}, nil)
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(context.Background())
				}).Return(nil)
				d.gitlabClient.On("GetMemberOfProject", mock.Anything, mock.Anything).Return(&gitlab.*********************ponse{}, nil)
				d.repo.On("Create", mock.Anything, mock.Anything).Return(nil)
			},
			expectErr: nil,
		},
		{
			name: "success",
			ctx:  context.Background(),
			input: dto.InviteRepoMemberInput{
				RepoID:   repoID,
				UserId:   userId,
				Role:     "developer",
				ExpireAt: &expireTime,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{}, nil)
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything).Return(&entities.RepoMember{Role: enums.RepoRole_Owner}, nil)
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(context.Background())
				}).Return(nil)
				d.gitlabClient.On("GetMemberOfProject", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
				d.gitlabClient.On("AddUserToProject", mock.Anything, mock.Anything).Return(&gitlab.AddUserToProjectResponse{}, nil)
				d.repo.On("Create", mock.Anything, mock.Anything).Return(nil)
			},
			expectErr: nil,
		},
		{
			name: "success without expire time",
			ctx:  context.Background(),
			input: dto.InviteRepoMemberInput{
				RepoID: repoID,
				UserId: userId,
				Role:   "developer",
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{}, nil)
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything).Return(&entities.RepoMember{Role: enums.RepoRole_Owner}, nil)
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(context.Background())
				}).Return(nil)
				d.gitlabClient.On("GetMemberOfProject", mock.Anything, mock.Anything).Return(&gitlab.*********************ponse{}, nil)
				d.gitlabClient.On("AddUserToProject", mock.Anything, mock.Anything).Return(&gitlab.AddUserToProjectResponse{}, nil)
				d.repo.On("Create", mock.Anything, mock.Anything).Return(nil)
			},
			expectErr: nil,
		},
		{
			name: "error FindUserByID when user is not found",
			ctx:  context.Background(),
			input: dto.InviteRepoMemberInput{
				RepoID:   repoID,
				UserId:   userId,
				Role:     "developer",
				ExpireAt: &expireTime,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{}, nil).Once()
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{}, nil).Once()
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything).Return(&entities.RepoMember{Role: enums.RepoRole_Owner}, nil)
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(nil, errors.New("error")).Once()
			},
			expectErr: errors.New("error"),
		},
		{
			name: "error FindUserByID when user is not found",
			ctx:  context.Background(),
			input: dto.InviteRepoMemberInput{
				RepoID:   repoID,
				UserId:   userId,
				Role:     "developer",
				ExpireAt: &expireTime,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{}, nil).Once()
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{}, nil).Once()
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything).Return(&entities.RepoMember{Role: enums.RepoRole_Owner}, nil)
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound).Once()
			},
			expectErr: usecase.ErrUserNotFound,
		},
		{
			name: "error FindRepositoryMember fail",
			ctx:  context.Background(),
			input: dto.InviteRepoMemberInput{
				RepoID:   repoID,
				UserId:   userId,
				Role:     "developer",
				ExpireAt: &expireTime,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{}, nil)
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything).Return(&entities.RepoMember{Role: enums.RepoRole_Owner}, nil).Once()
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything).Return(nil, errors.New("error")).Once()
			},
			expectErr: errors.New("error"),
		},
		{
			name: "error ErrMemberAlreadyExistsInRepository",
			ctx:  context.Background(),
			input: dto.InviteRepoMemberInput{
				RepoID:   repoID,
				UserId:   userId,
				Role:     "developer",
				ExpireAt: &expireTime,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{}, nil)
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything).Return(&entities.RepoMember{Role: enums.RepoRole_Owner}, nil).Once()
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything).Return(&entities.RepoMember{
					Role:   enums.RepoRole_Owner,
					UserID: userId,
				}, nil).Once()
			},
			expectErr: usecase.ErrMemberAlreadyExistsInRepo,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				repo:         &repository_mocks.MockRepository{},
				gitlabClient: &gitlab_mocks.MockGitlabClient{},
			}
			tt.mockFn(d)

			u := repository.New(nil, d.repo, d.gitlabClient, nil, nil, nil)
			err := u.InviteRepoMember(tt.ctx, tt.input)
			require.Equal(t, tt.expectErr, err)
		})
	}
}

func TestInviteMultipleRepoMembers(t *testing.T) {
	userId := uuid.New()
	type dependencies struct {
		repo         *repository_mocks.MockRepository
		gitlabClient *gitlab_mocks.MockGitlabClient
	}

	repoType := enums.RepoType_Models
	namespace := "namespace"
	repoName := "repo-name"
	repoID := *types.NewRepoID(repoType, namespace, repoName)

	expireTime := time.Date(2026, 1, 2, 15, 4, 5, 0, time.UTC)
	tests := []struct {
		name      string
		ctx       context.Context
		input     dto.InviteRepoMembersInput
		mockFn    func(d *dependencies)
		expectErr error
	}{
		{
			name: "should return error when repository.FindUserByID return error",
			ctx:  context.Background(),
			input: dto.InviteRepoMembersInput{
				RepoID: repoID,
				Members: []dto.RepoMemberInviteInput{
					{
						UserId:   userId,
						Role:     "developer",
						ExpireAt: &expireTime,
					},
				},
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return error when repository.FindUserByID return not found",
			ctx:  context.Background(),
			input: dto.InviteRepoMembersInput{
				RepoID: repoID,
				Members: []dto.RepoMemberInviteInput{
					{
						UserId:   userId,
						Role:     "developer",
						ExpireAt: &expireTime,
					},
				},
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expectErr: usecase.ErrUserNotFound,
		},
		{
			name: "should return error when repository.FindRepository return error",
			ctx:  context.Background(),
			input: dto.InviteRepoMembersInput{
				RepoID: repoID,
				Members: []dto.RepoMemberInviteInput{
					{
						UserId:   userId,
						Role:     "developer",
						ExpireAt: &expireTime,
					},
				},
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return error when repository.FindRepository return gorm.ErrRecordNotFound",
			ctx:  context.Background(),
			input: dto.InviteRepoMembersInput{
				RepoID: repoID,
				Members: []dto.RepoMemberInviteInput{
					{
						UserId:   userId,
						Role:     "developer",
						ExpireAt: &expireTime,
					},
				},
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expectErr: usecase.ErrRepositoryNotFound,
		},
		{
			name: "success",
			ctx:  context.Background(),
			input: dto.InviteRepoMembersInput{
				RepoID: repoID,
				Members: []dto.RepoMemberInviteInput{
					{
						UserId:   userId,
						Role:     "developer",
						ExpireAt: &expireTime,
					},
				},
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{}, nil)
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything).Return(&entities.RepoMember{Role: enums.RepoRole_Owner}, nil)
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(context.Background())
				}).Return(nil)
				d.gitlabClient.On("GetMemberOfProject", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
				d.gitlabClient.On("AddUserToProject", mock.Anything, mock.Anything).Return(&gitlab.AddUserToProjectResponse{}, nil)
				d.repo.On("Create", mock.Anything, mock.Anything).Return(nil)
			},
			expectErr: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				repo:         &repository_mocks.MockRepository{},
				gitlabClient: &gitlab_mocks.MockGitlabClient{},
			}
			tt.mockFn(d)

			u := repository.New(nil, d.repo, d.gitlabClient, nil, nil, nil)
			err := u.InviteMultipleRepoMembers(tt.ctx, tt.input)
			require.Equal(t, tt.expectErr, err)
		})
	}
}

func TestListMemberOfRepository(t *testing.T) {
	repoType := enums.RepoType_Models
	namespace := "namespace"
	repoName := "repo-name"
	repoID := *types.NewRepoID(repoType, namespace, repoName)
	repoUUID := uuid.New()

	type dependencies struct {
		repo *repository_mocks.MockRepository
	}

	userId := uuid.New()
	tests := []struct {
		name      string
		ctx       context.Context
		input     dto.ListRepositoryMembersInput
		mockFn    func(d *dependencies)
		expectErr error
		output    *dto.ListRepositoryMembersOutput
	}{
		{
			name: "should return error when repository.CountRepoMembers returns error",
			ctx:  context.Background(),
			input: dto.ListRepositoryMembersInput{
				RepoID:        repoID,
				CurrentUserId: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					BaseModel: entities.BaseModel{
						ID: repoUUID,
					},
				}, nil)
				d.repo.On("CountRepoMembers", mock.Anything, mock.Anything).Return(0, errors.New("error"))
			},
			expectErr: errors.New("error"),
			output:    nil,
		},
		{
			name: "should return error when repository.ListRepoMembers returns error",
			ctx:  context.Background(),
			input: dto.ListRepositoryMembersInput{
				RepoID:        repoID,
				CurrentUserId: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					BaseModel: entities.BaseModel{
						ID: repoUUID,
					},
				}, nil)
				d.repo.On("CountRepoMembers", mock.Anything, mock.Anything).Return(1, nil)
				d.repo.On("ListRepoMembers", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectErr: errors.New("error"),
			output:    nil,
		},
		{
			name: "success",
			ctx:  context.Background(),
			input: dto.ListRepositoryMembersInput{
				RepoID:        repoID,
				CurrentUserId: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					BaseModel: entities.BaseModel{
						ID: repoUUID,
					},
				}, nil)
				d.repo.On("CountRepoMembers", mock.Anything, mock.Anything).Return(1, nil)
				d.repo.On("ListRepoMembers", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]entities.RepoMemberInfo{
					{
						Name: "ad",
						RepoMember: entities.RepoMember{
							BaseModel: entities.BaseModel{
								ID: userId,
							},
						},
					},
				}, nil)
			},
			expectErr: nil,
			output: &dto.ListRepositoryMembersOutput{
				Data: &[]entities.RepoMemberInfo{
					{
						Name: "ad",
						RepoMember: entities.RepoMember{
							BaseModel: entities.BaseModel{
								ID: userId,
							},
						},
					},
				},
				Pagination: &dto.Pagination{
					Total: 1,
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				repo: &repository_mocks.MockRepository{},
			}
			tt.mockFn(d)

			u := repository.New(nil, d.repo, nil, nil, nil, nil)
			output, err := u.ListRepoMembers(tt.ctx, tt.input)
			require.Equal(t, tt.expectErr, err)
			require.Equal(t, tt.output, output)
		})
	}
}

func TestListContributorsOfRepository(t *testing.T) {
	repoType := enums.RepoType_Models
	namespace := "namespace"
	repoName := "repo-name"
	repoID := *types.NewRepoID(repoType, namespace, repoName)

	type dependencies struct {
		repo         *repository_mocks.MockRepository
		awsClient    *aws_mocks.MockAWSClient
		gitlabClient *gitlab_mocks.MockGitlabClient
	}

	avatar := "http://avatar"
	userId := uuid.New()
	tests := []struct {
		name      string
		ctx       context.Context
		input     dto.GetRepositoryContributorsInput
		mockFn    func(d *dependencies)
		expectErr error
		output    []dto.RepositoryContributor
	}{
		{
			name: "should return error when repository.FindUser returns error",
			ctx:  context.Background(),
			input: dto.GetRepositoryContributorsInput{
				RepoID:        repoID,
				CurrentUserId: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(nil, errors.New("error")).Once()
			},
			expectErr: errors.New("error"),
			output:    nil,
		},
		{
			name: "should return error when repository.FindUser returns usecase.ErrRecordNotFound",
			ctx:  context.Background(),
			input: dto.GetRepositoryContributorsInput{
				RepoID:        repoID,
				CurrentUserId: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound).Once()
			},
			expectErr: usecase.ErrUserNotFound,
			output:    nil,
		},
		{
			name: "should return error when repository.FindRepository returns error",
			ctx:  context.Background(),
			input: dto.GetRepositoryContributorsInput{
				RepoID:        repoID,
				CurrentUserId: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{}, nil).Once()
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, errors.New("error")).Once()
			},
			expectErr: errors.New("error"),
			output:    nil,
		},
		{
			name: "should return error when repository.FindRepository returns usecase.ErrRecordNotFound",
			ctx:  context.Background(),
			input: dto.GetRepositoryContributorsInput{
				RepoID:        repoID,
				CurrentUserId: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{}, nil).Once()
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound).Once()
			},
			expectErr: usecase.ErrRepositoryNotFound,
			output:    nil,
		},
		{
			name: "should return error when gitlab.GetProjectContributors returns error",
			ctx:  context.Background(),
			input: dto.GetRepositoryContributorsInput{
				RepoID:        repoID,
				CurrentUserId: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{}, nil).Once()
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{}, nil).Once()
				d.gitlabClient.On("GetProjectContributors", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectErr: errors.New("error"),
			output:    nil,
		},
		{
			name: "should return success when find contributor avatar returns error",
			ctx:  context.Background(),
			input: dto.GetRepositoryContributorsInput{
				RepoID:        repoID,
				CurrentUserId: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{}, nil).Once()
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{}, nil).Once()
				d.gitlabClient.On("GetProjectContributors", mock.Anything, mock.Anything).Return([]gitlab.RepositoryContributor{
					{
						Email: "<EMAIL>",
					},
				}, nil)
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(nil, errors.New("error")).Once()
			},
			expectErr: nil,
			output: []dto.RepositoryContributor{
				{
					Email: "<EMAIL>",
				},
			},
		},
		{
			name: "should return error when get contributor avatar returns error",
			ctx:  context.Background(),
			input: dto.GetRepositoryContributorsInput{
				RepoID:        repoID,
				CurrentUserId: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{GitlabAccessToken: utils.Ptr("token")}, nil).Once()
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{}, nil).Once()
				d.gitlabClient.On("GetProjectContributors", mock.Anything, mock.Anything).Return([]gitlab.RepositoryContributor{
					{
						Email:  "<EMAIL>",
						Avatar: &avatar,
					},
				}, nil)
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{Avatar: &avatar}, nil).Once()
				d.awsClient.On("GenPreSignUrl", mock.Anything, mock.Anything).Return(avatar, errors.New("error"))
			},
			expectErr: errors.New("error"),
			output:    nil,
		},
		{
			name: "success",
			ctx:  context.Background(),
			input: dto.GetRepositoryContributorsInput{
				RepoID:        repoID,
				CurrentUserId: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{GitlabAccessToken: utils.Ptr("token")}, nil).Once()
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{}, nil).Once()
				d.gitlabClient.On("GetProjectContributors", mock.Anything, mock.Anything).Return([]gitlab.RepositoryContributor{
					{
						Email:  "<EMAIL>",
						Avatar: &avatar,
					},
				}, nil)
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{Avatar: &avatar}, nil).Once()
				d.awsClient.On("GenPreSignUrl", mock.Anything, mock.Anything).Return(avatar, nil)
			},
			expectErr: nil,
			output: []dto.RepositoryContributor{
				{
					Email:  "<EMAIL>",
					Avatar: &avatar,
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				repo:         &repository_mocks.MockRepository{},
				awsClient:    &aws_mocks.MockAWSClient{},
				gitlabClient: &gitlab_mocks.MockGitlabClient{},
			}
			tt.mockFn(d)

			u := repository.New(nil, d.repo, d.gitlabClient, nil, nil, d.awsClient)
			output, err := u.ListRepoContributors(tt.ctx, tt.input)
			require.Equal(t, tt.expectErr, err)
			require.Equal(t, tt.output, output)
		})
	}
}

func TestRemoveMemberInRepository(t *testing.T) {
	repoType := enums.RepoType_Models
	namespace := "namespace"
	repoName := "repo-name"
	repoID := *types.NewRepoID(repoType, namespace, repoName)

	userId := uuid.New()

	type dependencies struct {
		repo         *repository_mocks.MockRepository
		gitlabClient *gitlab_mocks.MockGitlabClient
	}

	tests := []struct {
		name      string
		ctx       context.Context
		input     dto.RemoveMemberRepositoryInput
		mockFn    func(d *dependencies)
		expectErr error
	}{
		{
			name: "should return error when repository.FindUserByID return error",
			ctx:  context.Background(),
			input: dto.RemoveMemberRepositoryInput{
				RepoID:   repoID,
				MemberId: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return error when repository.FindUserByID return gorm.ErrRecordNotFound",
			ctx:  context.Background(),
			input: dto.RemoveMemberRepositoryInput{
				RepoID:   repoID,
				MemberId: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expectErr: usecase.ErrUserNotFound,
		},
		{
			name: "should return error when repository.FindUserByID return gorm.ErrRecordNotFound",
			ctx:  context.Background(),
			input: dto.RemoveMemberRepositoryInput{
				RepoID:   repoID,
				MemberId: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{}, nil).Once()
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(nil, errors.New("error")).Once()
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return error when repository.FindUserByID return gorm.ErrRecordNotFound",
			ctx:  context.Background(),
			input: dto.RemoveMemberRepositoryInput{
				RepoID:   repoID,
				MemberId: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{}, nil).Once()
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound).Once()
			},
			expectErr: usecase.ErrUserNotFound,
		},

		{
			name: "should return error when repository.FindRepository return error",
			ctx:  context.Background(),
			input: dto.RemoveMemberRepositoryInput{
				RepoID:   repoID,
				MemberId: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return error when repository.FindRepository return gorm.ErrRecordNotFound",
			ctx:  context.Background(),
			input: dto.RemoveMemberRepositoryInput{
				RepoID:   repoID,
				MemberId: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expectErr: usecase.ErrRepositoryNotFound,
		},
		{
			name: "should return error when user have no permission return no permission",
			ctx:  context.Background(),
			input: dto.RemoveMemberRepositoryInput{
				RepoID:   repoID,
				MemberId: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{Role: enums.UserRole_User}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{}, nil)
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything).Return(&entities.RepoMember{Role: enums.RepoRole_Developer}, nil)
			},
			expectErr: usecase.ErrNoPermission,
		},
		{
			name: "should return error",
			ctx:  context.Background(),
			input: dto.RemoveMemberRepositoryInput{
				RepoID:   repoID,
				MemberId: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{Role: enums.UserRole_User}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{}, nil)
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return error when member is owner",
			ctx:  context.Background(),
			input: dto.RemoveMemberRepositoryInput{
				RepoID:   repoID,
				MemberId: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{Role: enums.UserRole_Admin}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{}, nil)
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything).Return(&entities.RepoMember{Role: enums.RepoRole_Owner}, nil).Once()
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{Role: enums.UserRole_Admin}, nil)
			},
			expectErr: errors.New("Cannot remove owner from repository"),
		},
		{
			name: "should return error",
			ctx:  context.Background(),
			input: dto.RemoveMemberRepositoryInput{
				RepoID:   repoID,
				MemberId: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{Role: enums.UserRole_User}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{}, nil)
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything).Return(&entities.RepoMember{Role: enums.RepoRole_Owner}, nil).Once()
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything).Return(&entities.RepoMember{Role: enums.RepoRole_Developer}, nil).Once()
				d.gitlabClient.On("GetMemberOfProject", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
				d.gitlabClient.On("RemoveUserFromProject", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("Delete", mock.Anything, mock.Anything).Return(errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return error when DeleteOrgMemberByID return error",
			ctx:  context.Background(),
			input: dto.RemoveMemberRepositoryInput{
				RepoID:   repoID,
				MemberId: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{Role: enums.UserRole_User}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{}, nil)
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything).Return(&entities.RepoMember{Role: enums.RepoRole_Owner}, nil).Once()
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything).Return(&entities.RepoMember{Role: enums.RepoRole_Developer}, nil).Once()
				d.gitlabClient.On("GetMemberOfProject", mock.Anything, mock.Anything).Return(&gitlab.*********************ponse{}, nil)
				d.gitlabClient.On("RemoveUserFromProject", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("Delete", mock.Anything, mock.Anything).Return(errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return error",
			ctx:  context.Background(),
			input: dto.RemoveMemberRepositoryInput{
				RepoID:   repoID,
				MemberId: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{Role: enums.UserRole_User}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{}, nil)
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything).Return(&entities.RepoMember{Role: enums.RepoRole_Owner}, nil).Once()
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything).Return(nil, errors.New("error")).Once()
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return error",
			ctx:  context.Background(),
			input: dto.RemoveMemberRepositoryInput{
				RepoID:   repoID,
				MemberId: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{Role: enums.UserRole_User}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{}, nil)
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything).Return(&entities.RepoMember{Role: enums.RepoRole_Owner}, nil).Once()
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything).Return(&entities.RepoMember{Role: enums.RepoRole_Developer}, nil).Once()
				d.gitlabClient.On("GetMemberOfProject", mock.Anything, mock.Anything).Return(&gitlab.*********************ponse{}, nil)
				d.gitlabClient.On("RemoveUserFromProject", mock.Anything, mock.Anything).Return(errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "success",
			ctx:  context.Background(),
			input: dto.RemoveMemberRepositoryInput{
				RepoID:   repoID,
				MemberId: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{Role: enums.UserRole_User}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{}, nil)
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything).Return(&entities.RepoMember{Role: enums.RepoRole_Owner}, nil).Once()
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything).Return(&entities.RepoMember{Role: enums.RepoRole_Developer}, nil).Once()
				d.gitlabClient.On("GetMemberOfProject", mock.Anything, mock.Anything).Return(&gitlab.*********************ponse{}, nil)
				d.gitlabClient.On("RemoveUserFromProject", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("Delete", mock.Anything, mock.Anything).Return(nil)
			},
			expectErr: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				repo:         &repository_mocks.MockRepository{},
				gitlabClient: &gitlab_mocks.MockGitlabClient{},
			}
			tt.mockFn(d)

			u := repository.New(nil, d.repo, d.gitlabClient, nil, nil, nil)
			err := u.RemoveMember(tt.ctx, tt.input)
			require.Equal(t, tt.expectErr, err)
		})
	}
}

func TestUpdateMemberInRepository(t *testing.T) {
	repoType := enums.RepoType_Models
	namespace := "namespace"
	repoName := "repo-name"
	repoID := *types.NewRepoID(repoType, namespace, repoName)

	userId := uuid.New()
	now, _ := time.Parse(time.RFC3339, "2025-12-30T15:04:05Z")
	past, _ := time.Parse(time.RFC3339, "2024-12-30T15:04:05Z")

	type dependencies struct {
		repo         *repository_mocks.MockRepository
		gitlabClient *gitlab_mocks.MockGitlabClient
	}

	tests := []struct {
		name      string
		ctx       context.Context
		input     dto.UpdateMemberRepositoryInput
		mockFn    func(d *dependencies)
		expectErr error
	}{
		{
			name: "should return error when repository.FindUserByID return error",
			ctx:  context.Background(),
			input: dto.UpdateMemberRepositoryInput{
				RepoID:   repoID,
				MemberId: userId,
				ExpireAt: &now,
				Role:     enums.RepoRole_Developer,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return error when time is past",
			ctx:  context.Background(),
			input: dto.UpdateMemberRepositoryInput{
				RepoID:   repoID,
				MemberId: userId,
				ExpireAt: &past,
				Role:     enums.RepoRole_Developer,
			},
			mockFn: func(d *dependencies) {
			},
			expectErr: usecase.ErrTimeIsPast,
		},
		{
			name: "should return error when repository.FindUserByID return gorm.ErrRecordNotFound",
			ctx:  context.Background(),
			input: dto.UpdateMemberRepositoryInput{
				RepoID:   repoID,
				MemberId: userId,
				ExpireAt: &now,
				Role:     enums.RepoRole_Developer,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expectErr: usecase.ErrUserNotFound,
		},
		{
			name: "should return error when repository.FindRepository return error",
			ctx:  context.Background(),
			input: dto.UpdateMemberRepositoryInput{
				RepoID:   repoID,
				MemberId: userId,
				ExpireAt: &now,
				Role:     enums.RepoRole_Developer,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return error when repository.FindRepository return gorm.ErrRecordNotFound",
			ctx:  context.Background(),
			input: dto.UpdateMemberRepositoryInput{
				RepoID:   repoID,
				MemberId: userId,
				ExpireAt: &now,
				Role:     enums.RepoRole_Developer,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expectErr: usecase.ErrRepositoryNotFound,
		},
		{
			name: "should return error when FindUserByID return error",
			ctx:  context.Background(),
			input: dto.UpdateMemberRepositoryInput{
				RepoID:   repoID,
				MemberId: userId,
				ExpireAt: &now,
				Role:     enums.RepoRole_Developer,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{Role: enums.UserRole_User}, nil).Once()
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(nil, errors.New("error")).Once()
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return error when FindUserByID return gorm.ErrRecordNotFound",
			ctx:  context.Background(),
			input: dto.UpdateMemberRepositoryInput{
				RepoID:   repoID,
				MemberId: userId,
				ExpireAt: &now,
				Role:     enums.RepoRole_Developer,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{Role: enums.UserRole_User}, nil).Once()
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound).Once()
			},
			expectErr: usecase.ErrUserNotFound,
		},
		{
			name: "should return error when user have no permission return no permission",
			ctx:  context.Background(),
			input: dto.UpdateMemberRepositoryInput{
				RepoID:   repoID,
				MemberId: userId,
				ExpireAt: &now,
				Role:     enums.RepoRole_Developer,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{Role: enums.UserRole_User}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{}, nil)
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything).Return(&entities.RepoMember{Role: enums.RepoRole_Developer}, nil)
			},
			expectErr: usecase.ErrNoPermission,
		},
		{
			name: "should return error when FindRepositoryMember return error",
			ctx:  context.Background(),
			input: dto.UpdateMemberRepositoryInput{
				RepoID:   repoID,
				MemberId: userId,
				ExpireAt: &now,
				Role:     enums.RepoRole_Developer,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{}, nil)
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return error when Save return error",
			ctx:  context.Background(),
			input: dto.UpdateMemberRepositoryInput{
				RepoID:   repoID,
				MemberId: userId,
				ExpireAt: &now,
				Role:     enums.RepoRole_Developer,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{}, nil)
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything).Return(&entities.RepoMember{Role: enums.RepoRole_Owner}, nil)
				d.gitlabClient.On("GetMemberOfProject", mock.Anything, mock.Anything).Return(&gitlab.*********************ponse{}, nil)
				d.gitlabClient.On("EditUserInProject", mock.Anything, mock.Anything).Return(&gitlab.EditUserInProjectResponse{}, nil)
				d.repo.On("Save", mock.Anything, mock.Anything).Return(errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "success when user not found in gitlab",
			ctx:  context.Background(),
			input: dto.UpdateMemberRepositoryInput{
				RepoID:   repoID,
				MemberId: userId,
				ExpireAt: &now,
				Role:     enums.RepoRole_Developer,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{}, nil)
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything).Return(&entities.RepoMember{Role: enums.RepoRole_Owner}, nil)
				d.gitlabClient.On("GetMemberOfProject", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
				d.repo.On("Save", mock.Anything, mock.Anything).Return(nil)
			},
			expectErr: nil,
		},
		{
			name: "success",
			ctx:  context.Background(),
			input: dto.UpdateMemberRepositoryInput{
				RepoID:   repoID,
				MemberId: userId,
				ExpireAt: &now,
				Role:     enums.RepoRole_Developer,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{}, nil)
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything).Return(&entities.RepoMember{Role: enums.RepoRole_Owner}, nil)
				d.gitlabClient.On("GetMemberOfProject", mock.Anything, mock.Anything).Return(&gitlab.*********************ponse{}, nil)
				d.gitlabClient.On("EditUserInProject", mock.Anything, mock.Anything).Return(&gitlab.EditUserInProjectResponse{}, nil)
				d.repo.On("Save", mock.Anything, mock.Anything).Return(nil)
			},
			expectErr: nil,
		},
		{
			name: "success without expire time",
			ctx:  context.Background(),
			input: dto.UpdateMemberRepositoryInput{
				RepoID:   repoID,
				MemberId: userId,
				Role:     enums.RepoRole_Developer,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{}, nil)
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything).Return(&entities.RepoMember{Role: enums.RepoRole_Owner}, nil)
				d.gitlabClient.On("GetMemberOfProject", mock.Anything, mock.Anything).Return(&gitlab.*********************ponse{}, nil)
				d.gitlabClient.On("EditUserInProject", mock.Anything, mock.Anything).Return(&gitlab.EditUserInProjectResponse{}, nil)
				d.repo.On("Save", mock.Anything, mock.Anything).Return(nil)
			},
			expectErr: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				repo:         &repository_mocks.MockRepository{},
				gitlabClient: &gitlab_mocks.MockGitlabClient{},
			}
			tt.mockFn(d)

			u := repository.New(nil, d.repo, d.gitlabClient, nil, nil, nil)
			err := u.UpdateMember(tt.ctx, tt.input)
			require.Equal(t, tt.expectErr, err)
		})
	}
}

func TestGetMemberInRepository(t *testing.T) {
	userId := uuid.New()
	repoType := enums.RepoType_Models
	namespace := "namespace"
	repoName := "repo-name"
	repoID := *types.NewRepoID(repoType, namespace, repoName)

	type dependencies struct {
		repo         *repository_mocks.MockRepository
		gitlabClient *gitlab_mocks.MockGitlabClient
	}

	tests := []struct {
		name      string
		ctx       context.Context
		input     dto.GetMemberRepositoryInput
		mockFn    func(d *dependencies)
		expectErr error
		output    *entities.RepoMember
	}{
		{
			name: "should return error when repository.FindRepository return error",
			ctx:  context.Background(),
			input: dto.GetMemberRepositoryInput{
				RepoID:   repoID,
				MemberId: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectErr: errors.New("error"),
			output:    nil,
		},
		{
			name: "should return error when repository.FindRepository return gorm.ErrRecordNotFound",
			ctx:  context.Background(),
			input: dto.GetMemberRepositoryInput{
				RepoID:   repoID,
				MemberId: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expectErr: usecase.ErrRepositoryNotFound,
			output:    nil,
		},
		{
			name: "should return error when repository.FindUserByID return error",
			ctx:  context.Background(),
			input: dto.GetMemberRepositoryInput{
				RepoID:   repoID,
				MemberId: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{}, nil)
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectErr: errors.New("error"),
			output:    nil,
		},
		{
			name: "should return blank value when repository.FindUserByID return gorm.ErrRecordNotFound",
			ctx:  context.Background(),
			input: dto.GetMemberRepositoryInput{
				RepoID:   repoID,
				MemberId: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{}, nil)
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expectErr: nil,
			output:    &entities.RepoMember{},
		},
		{
			name: "should return blank value when member not exist in repository",
			ctx:  context.Background(),
			input: dto.GetMemberRepositoryInput{
				RepoID:   repoID,
				MemberId: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{}, nil)
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything).Return(&entities.RepoMember{}, nil)
			},
			expectErr: nil,
			output:    &entities.RepoMember{},
		},
		{
			name: "should return error when repository.FindRepositoryMember return unkown error",
			ctx:  context.Background(),
			input: dto.GetMemberRepositoryInput{
				RepoID:   repoID,
				MemberId: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{}, nil)
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectErr: errors.New("error"),
			output:    nil,
		},
		{
			name: "return error",
			ctx:  context.Background(),
			input: dto.GetMemberRepositoryInput{
				RepoID:   repoID,
				MemberId: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{}, nil)
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expectErr: nil,
			output:    &entities.RepoMember{},
		},
		{
			name: "success",
			ctx:  context.Background(),
			input: dto.GetMemberRepositoryInput{
				RepoID:   repoID,
				MemberId: userId,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{}, nil)
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything).Return(&entities.RepoMember{Role: enums.RepoRole_Owner}, nil)
			},
			expectErr: nil,
			output:    &entities.RepoMember{Role: enums.RepoRole_Owner},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				repo:         &repository_mocks.MockRepository{},
				gitlabClient: &gitlab_mocks.MockGitlabClient{},
			}
			tt.mockFn(d)

			u := repository.New(nil, d.repo, d.gitlabClient, nil, nil, nil)
			output, err := u.GetMember(tt.ctx, tt.input)
			require.Equal(t, tt.expectErr, err)
			require.Equal(t, tt.output, output)
		})
	}
}

func TestArchiveRepository(t *testing.T) {
	type dependencies struct {
		repo         *repository_mocks.MockRepository
		gitlabClient *gitlab_mocks.MockGitlabClient
	}

	tests := []struct {
		name     string
		ctx      context.Context
		mockFn   func(d *dependencies)
		input    dto.ArchiveRepositoryInput
		expError error
	}{
		{
			name: "should return error if FindRepository fail",
			ctx:  context.TODO(),
			input: dto.ArchiveRepositoryInput{
				Ref:           "main",
				Type:          "tar",
				CurrentUserId: uuid.New(),
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error if FindRepository not found",
			ctx:  context.TODO(),
			input: dto.ArchiveRepositoryInput{
				Ref:           "main",
				Type:          "tar",
				CurrentUserId: uuid.New(),
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expError: usecase.ErrRepositoryNotFound,
		},
		{
			name: "should return error if DownloadProject fail",
			ctx:  context.TODO(),
			input: dto.ArchiveRepositoryInput{
				Ref:           "main",
				Type:          "tar",
				CurrentUserId: uuid.New(),
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{}, nil)
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.gitlabClient.On("DownloadProject", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error if FindUser fail",
			ctx:  context.TODO(),
			input: dto.ArchiveRepositoryInput{
				Ref:           "main",
				Type:          "tar",
				CurrentUserId: uuid.New(),
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{}, nil)
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should success",
			ctx:  context.TODO(),
			input: dto.ArchiveRepositoryInput{
				Ref:           "main",
				Type:          "tar",
				CurrentUserId: uuid.New(),
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{}, nil)
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{GitlabAccessToken: utils.Ptr("token")}, nil)
				d.gitlabClient.On("DownloadProject", mock.Anything, mock.Anything).Return(nil, nil)
			},
			expError: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				repo:         &repository_mocks.MockRepository{},
				gitlabClient: &gitlab_mocks.MockGitlabClient{},
			}
			tt.mockFn(d)

			u := repository.New(nil, d.repo, d.gitlabClient, nil, nil, nil)
			_, err := u.ArchiveRepo(tt.ctx, tt.input)
			require.Equal(t, tt.expError, err)
		})
	}
}

func TestCreateRepositoryTag(t *testing.T) {
	type dependencies struct {
		repo *repository_mocks.MockRepository
	}

	tests := []struct {
		name     string
		ctx      context.Context
		mockFn   func(d *dependencies)
		input    dto.CreateRepoTagInput
		expError error
	}{
		{
			name: "should return error if FindUserByID fail",
			ctx:  context.TODO(),
			input: dto.CreateRepoTagInput{
				Type:    "Tasks",
				SubType: "",
				Name:    "sample-task",
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error if FindUserByID not found",
			ctx:  context.TODO(),
			input: dto.CreateRepoTagInput{
				Type:    "Tasks",
				SubType: "",
				Name:    "sample-task",
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expError: usecase.ErrUserNotFound,
		},
		{
			name: "should return error if Create tag fail",
			ctx:  context.TODO(),
			input: dto.CreateRepoTagInput{
				Type:    "Tasks",
				SubType: "",
				Name:    "sample-task",
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("Create", mock.Anything, mock.Anything).Return(errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return success",
			ctx:  context.TODO(),
			input: dto.CreateRepoTagInput{
				Type:    "Tasks",
				SubType: "",
				Name:    "sample-task",
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("Create", mock.Anything, mock.Anything).Return(nil)
			},
			expError: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				repo: &repository_mocks.MockRepository{},
			}
			tt.mockFn(d)

			u := repository.New(nil, d.repo, nil, nil, nil, nil)
			err := u.CreateRepoTag(tt.ctx, tt.input)
			require.Equal(t, tt.expError, err)
		})
	}
}

func TestGetRepositoryTag(t *testing.T) {
	type dependencies struct {
		repo         *repository_mocks.MockRepository
		gitlabClient *gitlab_mocks.MockGitlabClient
	}

	id := uuid.New()
	tests := []struct {
		name     string
		ctx      context.Context
		mockFn   func(d *dependencies)
		input    dto.GetRepoTagInput
		expError error
	}{
		{
			name: "should return error if FindUserByID fail",
			ctx:  context.TODO(),
			input: dto.GetRepoTagInput{
				Id:            id,
				CurrentUserID: id,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error if FindUserByID not found",
			ctx:  context.TODO(),
			input: dto.GetRepoTagInput{
				Id:            id,
				CurrentUserID: id,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expError: usecase.ErrUserNotFound,
		},
		{
			name: "should return error if Get tag fail",
			ctx:  context.TODO(),
			input: dto.GetRepoTagInput{
				Id:            id,
				CurrentUserID: id,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindRepoTag", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return success",
			ctx:  context.TODO(),
			input: dto.GetRepoTagInput{
				Id:            id,
				CurrentUserID: id,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{}, nil)
				d.repo.On("FindRepoTag", mock.Anything, mock.Anything).Return(&entities.Tag{}, nil)
			},
			expError: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				repo:         &repository_mocks.MockRepository{},
				gitlabClient: &gitlab_mocks.MockGitlabClient{},
			}
			tt.mockFn(d)

			u := repository.New(nil, d.repo, d.gitlabClient, nil, nil, nil)
			_, err := u.GetRepoTag(tt.ctx, tt.input)
			require.Equal(t, tt.expError, err)
		})
	}
}

func TestDeleteRepositoryTag(t *testing.T) {
	type dependencies struct {
		repo         *repository_mocks.MockRepository
		gitlabClient *gitlab_mocks.MockGitlabClient
	}

	id := uuid.New()
	tests := []struct {
		name     string
		ctx      context.Context
		mockFn   func(d *dependencies)
		input    dto.DeleteRepoTagInput
		expError error
	}{
		{
			name: "should return error if FindUserByID fail",
			ctx:  context.TODO(),
			input: dto.DeleteRepoTagInput{
				Id:            id,
				CurrentUserID: id,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error if FindUserByID not found",
			ctx:  context.TODO(),
			input: dto.DeleteRepoTagInput{
				Id:            id,
				CurrentUserID: id,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expError: usecase.ErrUserNotFound,
		},
		{
			name: "should return error if Find tag fail",
			ctx:  context.TODO(),
			input: dto.DeleteRepoTagInput{
				Id:            id,
				CurrentUserID: id,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{Role: enums.UserRole_Admin}, nil)
				d.repo.On("FindRepoTag", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error if Find tag return not found",
			ctx:  context.TODO(),
			input: dto.DeleteRepoTagInput{
				Id:            id,
				CurrentUserID: id,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{Role: enums.UserRole_Admin}, nil)
				d.repo.On("FindRepoTag", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expError: gorm.ErrRecordNotFound,
		},
		{
			name: "should return error if delete tag error",
			ctx:  context.TODO(),
			input: dto.DeleteRepoTagInput{
				Id:            id,
				CurrentUserID: id,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{Role: enums.UserRole_Admin}, nil)
				d.repo.On("FindRepoTag", mock.Anything, mock.Anything).Return(&entities.Tag{}, nil)
				d.repo.On("DeleteById", mock.Anything, mock.Anything, mock.Anything).Return(errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return success",
			ctx:  context.TODO(),
			input: dto.DeleteRepoTagInput{
				Id:            id,
				CurrentUserID: id,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{Role: enums.UserRole_Admin}, nil)
				d.repo.On("FindRepoTag", mock.Anything, mock.Anything).Return(&entities.Tag{}, nil)
				d.repo.On("DeleteById", mock.Anything, mock.Anything, mock.Anything).Return(nil)
			},
			expError: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				repo:         &repository_mocks.MockRepository{},
				gitlabClient: &gitlab_mocks.MockGitlabClient{},
			}
			tt.mockFn(d)

			u := repository.New(nil, d.repo, d.gitlabClient, nil, nil, nil)
			err := u.DeleteRepoTag(tt.ctx, tt.input)
			require.Equal(t, tt.expError, err)
		})
	}
}

func TestCreateRepoCommit(t *testing.T) {
	repoType := enums.RepoType_Models
	namespace := "namespace"
	repoName := "repo-name"
	repoID := *types.NewRepoID(repoType, namespace, repoName)

	type dependencies struct {
		config *configs.GlobalConfig
		repo   *repository_mocks.MockRepository
		gitlab *gitlab_mocks.MockGitlabClient
	}

	tests := []struct {
		name     string
		ctx      context.Context
		mockFn   func(d *dependencies)
		input    dto.CreateRepositoryCommitInput
		exOutput *dto.RepositoryCommit
		expError error
	}{

		{
			name: "should error if FindUser fail",
			ctx:  context.TODO(),
			input: dto.CreateRepositoryCommitInput{
				RepoID: repoID,
				RepoCommitInput: dto.RepoCommitInput{
					Branch:        "main",
					CommitMessage: "test",
					Actions:       []dto.GitLabAction{},
				},
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
			exOutput: nil,
		},
		{
			name: "should error if FindUser fail",
			ctx:  context.TODO(),
			input: dto.CreateRepositoryCommitInput{
				RepoID: repoID,
				RepoCommitInput: dto.RepoCommitInput{
					Branch:        "main",
					CommitMessage: "test",
					Actions:       []dto.GitLabAction{},
				},
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expError: usecase.ErrUserNotFound,
			exOutput: nil,
		},
		{
			name: "should error if FindRepository fail",
			ctx:  context.TODO(),
			input: dto.CreateRepositoryCommitInput{
				RepoID: repoID,
				RepoCommitInput: dto.RepoCommitInput{
					Branch:        "main",
					CommitMessage: "test",
					Actions:       []dto.GitLabAction{},
				},
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{GitlabAccessToken: utils.Ptr("token")}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expError: usecase.ErrRepositoryNotFound,
			exOutput: nil,
		},
		{
			name: "should error if FindRepository fail",
			ctx:  context.TODO(),
			input: dto.CreateRepositoryCommitInput{
				RepoID: repoID,
				RepoCommitInput: dto.RepoCommitInput{
					Branch:        "main",
					CommitMessage: "test",
					Actions:       []dto.GitLabAction{},
				},
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{GitlabAccessToken: utils.Ptr("token")}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
			exOutput: nil,
		},
		{
			name: "should error if CreateProjectCommit fail",
			ctx:  context.TODO(),
			input: dto.CreateRepositoryCommitInput{
				RepoID: repoID,
				RepoCommitInput: dto.RepoCommitInput{
					Branch:        "main",
					CommitMessage: "test",
					Actions:       []dto.GitLabAction{},
				},
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{GitlabAccessToken: utils.Ptr("token")}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{}, nil)
				d.gitlab.On("CreateProjectCommit", mock.Anything, mock.Anything).Return(nil, errors.New("error"))

			},
			expError: errors.New("error"),
			exOutput: nil,
		},
		{
			name: "should success",
			ctx:  context.TODO(),
			input: dto.CreateRepositoryCommitInput{
				RepoID: repoID,
				RepoCommitInput: dto.RepoCommitInput{
					Branch:        "main",
					CommitMessage: "test",
					Actions: []dto.GitLabAction{
						{
							Action:   "create",
							FilePath: "test.txt",
						},
					},
				},
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{GitlabAccessToken: utils.Ptr("token")}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{}, nil)
				d.gitlab.On("CreateProjectCommit", mock.Anything, mock.Anything).Return(&gitlab.RepositoryCommit{
					ID: "1",
				}, nil)

			},
			expError: nil,
			exOutput: &dto.RepositoryCommit{
				ID: "1",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				config: &configs.GlobalConfig{},
				repo:   &repository_mocks.MockRepository{},
				gitlab: &gitlab_mocks.MockGitlabClient{},
			}
			tt.mockFn(d)

			u := repository.New(d.config, d.repo, d.gitlab, nil, nil, nil)
			output, err := u.CreateRepoCommit(tt.ctx, tt.input)
			require.Equal(t, tt.exOutput, output)
			require.Equal(t, tt.expError, err)
		})
	}
}

func TestGetSingleRepoBranch(t *testing.T) {
	repoType := enums.RepoType_Models
	namespace := "namespace"
	repoName := "repo-name"
	repoID := *types.NewRepoID(repoType, namespace, repoName)

	type dependencies struct {
		config *configs.GlobalConfig
		repo   *repository_mocks.MockRepository
		gitlab *gitlab_mocks.MockGitlabClient
	}

	mockUserID := uuid.New()
	tests := []struct {
		name     string
		ctx      context.Context
		mockFn   func(d *dependencies)
		input    dto.GetSingleRepositoryBranchInput
		exOutput *dto.RepositoryBranchInfo
		expError error
	}{

		{
			name: "should error if FindUser fail",
			ctx:  context.TODO(),
			input: dto.GetSingleRepositoryBranchInput{
				RepoID: repoID,
				Branch: "main",
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
			exOutput: nil,
		},
		{
			name: "should error if FindUser fail",
			ctx:  context.TODO(),
			input: dto.GetSingleRepositoryBranchInput{
				RepoID: repoID,
				Branch: "main",
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expError: usecase.ErrUserNotFound,
			exOutput: nil,
		},
		{
			name: "should error if FindRepository fail",
			ctx:  context.TODO(),
			input: dto.GetSingleRepositoryBranchInput{
				RepoID: repoID,
				Branch: "main",
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{GitlabAccessToken: utils.Ptr("token")}, nil)
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{BaseModel: entities.BaseModel{ID: mockUserID}}, nil).Once()
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expError: usecase.ErrRepositoryNotFound,
			exOutput: nil,
		},
		{
			name: "should error if FindRepository fail",
			ctx:  context.TODO(),
			input: dto.GetSingleRepositoryBranchInput{
				RepoID: repoID,
				Branch: "main",
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{GitlabAccessToken: utils.Ptr("token")}, nil)
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{BaseModel: entities.BaseModel{ID: mockUserID}}, nil).Once()
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
			exOutput: nil,
		},
		{
			name: "should error if GetSingleProjectBrancheInfo fail",
			ctx:  context.TODO(),
			input: dto.GetSingleRepositoryBranchInput{
				RepoID: repoID,
				Branch: "main",
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{GitlabAccessToken: utils.Ptr("token")}, nil)
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{BaseModel: entities.BaseModel{ID: mockUserID}}, nil).Once()
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{}, nil)
				d.gitlab.On("GetSingleProjectBrancheInfo", mock.Anything, mock.Anything).Return(nil, errors.New("error"))

			},
			expError: errors.New("error"),
			exOutput: nil,
		},
		{
			name: "should success",
			ctx:  context.TODO(),
			input: dto.GetSingleRepositoryBranchInput{
				RepoID: repoID,
				Branch: "main",
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{GitlabAccessToken: utils.Ptr("token")}, nil)
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{BaseModel: entities.BaseModel{ID: mockUserID}}, nil).Once()
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{}, nil)
				d.gitlab.On("GetSingleProjectBrancheInfo", mock.Anything, mock.Anything).Return(&gitlab.RepositoryBranchInfo{}, nil)

			},
			expError: nil,
			exOutput: &dto.RepositoryBranchInfo{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				config: &configs.GlobalConfig{},
				repo:   &repository_mocks.MockRepository{},
				gitlab: &gitlab_mocks.MockGitlabClient{},
			}
			tt.mockFn(d)

			u := repository.New(d.config, d.repo, d.gitlab, nil, nil, nil)
			output, err := u.GetSingleRepoBranch(tt.ctx, tt.input)
			require.Equal(t, tt.exOutput, output)
			require.Equal(t, tt.expError, err)
		})
	}
}
