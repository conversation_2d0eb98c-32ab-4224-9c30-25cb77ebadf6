package repository

import (
	"bufio"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/url"
	"strconv"
	"strings"
	"time"

	pond "github.com/alitto/pond/v2"
	"github.com/argoproj/argo-workflows/v3/pkg/apis/workflow/v1alpha1"
	"github.com/google/uuid"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	corev1 "k8s.io/api/core/v1"
	networkingv1 "k8s.io/api/networking/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	kubeerrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/intstr"
	applycorev1 "k8s.io/client-go/applyconfigurations/core/v1"
	applymetav1 "k8s.io/client-go/applyconfigurations/meta/v1"
	"k8s.io/client-go/kubernetes"

	"api-server/internal/dto"
	"api-server/internal/entities"
	"api-server/internal/enums"
	"api-server/internal/gateways/gitlab"
	repository "api-server/internal/repositories"
	"api-server/internal/types"
	"api-server/internal/usecase"
	"api-server/internal/usecase/workflow"
	"api-server/internal/utils"
	"api-server/pkg/oteltrace"
)

// TODO: make this a env variable
const (
	spaceNamespace = "space"
	argoNamespace  = "argo"
)

// StartDeployment implements the RepositoryUsecase interface for starting a new deployment.
// It handles the process of deploying a space repository to Kubernetes, including creating necessary resources
// and starting the Argo workflow for deployment.
//
// Parameters:
//   - ctx: Context for the operation
//   - userID: ID of the user initiating the deployment
//   - repoID: ID of the repository to deploy
//   - input: Input data containing deployment configuration
//
// Returns:
//   - *dto.StartDeploymentResponse: Response containing deployment information
//   - error: Any error that occurred during deployment
func (i *impl) StartDeployment(
	ctx context.Context,
	userID uuid.UUID,
	repoID types.RepoID,
	input dto.StartDeploymentRequest,
) (*dto.StartDeploymentResponse, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.StartDeployment")
	defer span.End()

	if *repoID.RepoType() != enums.RepoType_Spaces {
		span.SetStatus(codes.Error, usecase.ErrNotSpaceRepo.Error())
		span.RecordError(usecase.ErrNotSpaceRepo)
		return nil, usecase.ErrNotSpaceRepo
	}

	var result dto.StartDeploymentResponse
	var resourceName string
	var repo *entities.Repository
	var gitlabRepoMetadata *GitlabRepoMetadata
	revision := "main"
	if input.Revision != nil {
		revision = *input.Revision
	}

	repo, err := i.repo.FindRepository(ctx, repository.RepositoryFilter{
		Type:      repoID.RepoType(),
		Namespace: repoID.Namespace(),
		Name:      repoID.RepoName(),
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find repository")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, usecase.ErrRepositoryNotFound
		}
		return nil, err
	}
	// check hardware
	if repo.Hardware == nil {
		span.SetStatus(codes.Error, usecase.ErrNoHardware.Error())
		span.RecordError(usecase.ErrNoHardware)
		return nil, usecase.ErrNoHardware
	}
	// check node exist
	if !strings.Contains(strings.ToLower(repo.Hardware.Name), "cpu") {
		_, err = i.kubeClient.CoreV1().Nodes().Get(ctx, repo.Hardware.Name, metav1.GetOptions{})
		if err != nil {
			span.AddEvent("failed to get node", trace.WithAttributes(attribute.String("node_name", repo.Hardware.Name)))
			span.SetStatus(codes.Error, "failed to get node")
			span.RecordError(err)
			if apierrors.IsNotFound(err) {
				return nil, usecase.ErrNodeNotExist
			}
			return nil, err
		}
	}

	resourceName = spaceResourceName(repo.ID.String())

	gitlabRepoMetadata, err = i.getGitlabRepoMetadata(ctx, userID, repo.RefGitRepoID, revision)
	if err != nil {
		return nil, err
	}

	// create new placeholder deployment record in database if not exists
	if repo.Deployment == nil {
		deployment, err := i.repo.CreateDeployment(ctx, repository.CreateDeploymentInput{
			Name:         gitlabRepoMetadata.SpaceName,
			UserID:       userID,
			WorkflowName: "",
			RepoID:       repo.ID,
			Revision:     revision,
			Status:       enums.ArgoWorkflowStatus_NotRunning,
			Commit:       gitlabRepoMetadata.LatestCommit,
			// HardwareID:   repo.Hardware.ID,
		})
		if err != nil {
			span.SetStatus(codes.Error, "failed to create deployment")
			span.RecordError(err)
			return nil, err
		}

		repo.Deployment = deployment
	}

	txErr := i.repo.Transaction(ctx, func(ctx context.Context) error {
		return i.StartDeploymentTx(ctx, repo, resourceName, gitlabRepoMetadata, userID, revision, &result)
	})
	if txErr != nil {
		span.SetStatus(codes.Error, "failed to start deployment")
		span.RecordError(err)
		return nil, txErr
	}

	span.AddEvent("start deployment successfully")
	span.SetStatus(codes.Ok, "start deployment successfully")
	return &result, nil
}

// StartDeploymentTx handles the transactional part of starting a deployment.
// It creates and configures all necessary Kubernetes resources and starts the Argo workflow.
//
// Parameters:
//   - ctx: Context for the operation
//   - repo: Repository entity to deploy
//   - resourceName: Name for Kubernetes resources
//   - gitlabRepoMetadata: GitLab repository metadata
//   - userID: ID of the user initiating the deployment
//   - revision: Git revision to deploy
//   - result: Pointer to store deployment response
//
// Returns:
//   - error: Any error that occurred during deployment transaction
func (i *impl) StartDeploymentTx(
	ctx context.Context,
	repo *entities.Repository,
	resourceName string,
	gitlabRepoMetadata *GitlabRepoMetadata,
	userID uuid.UUID,
	revision string,
	result *dto.StartDeploymentResponse,
) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.StartDeploymentTx")
	defer span.End()

	deployment, err := i.repo.FindDeployment(ctx, repo.ID, clause.Locking{
		Strength: clause.LockingStrengthUpdate,
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find deployment")
		span.RecordError(err)
		return err
	}

	// clean up running k8s deployment to make sure
	err = deleteResources(ctx, i.kubeClient, spaceNamespace, resourceName)
	if err != nil {
		span.SetStatus(codes.Error, "failed to delete resources")
		span.RecordError(err)
		return err
	}

	err = i.checkOngoingDeploymentStatus(ctx, deployment)
	if err != nil {
		span.SetStatus(codes.Error, "failed to check ongoing deployment status")
		span.RecordError(err)
		return err
	}

	// create ConfigMap for env
	err = i.upsertConfigMap(ctx, repo.ID, resourceName)
	if err != nil {
		span.SetStatus(codes.Error, "failed to upsert configmap")
		span.RecordError(err)
		return err
	}
	// apply Service resource
	err = i.applyKubernetesServiceResource(ctx, resourceName)
	if err != nil {
		span.SetStatus(codes.Error, "failed to apply Kubernetes Service resource")
		span.RecordError(err)
		return err
	}
	// apply Ingress resource
	err = i.createKubernetesIngressResource(ctx, gitlabRepoMetadata.SpaceName, i.config.Space.SpaceDomain, resourceName)
	if err != nil {
		span.SetStatus(codes.Error, "failed to create Kubernetes Ingress resource")
		span.RecordError(err)
		return err
	}

	gpuDeployment := !strings.Contains(strings.ToLower(repo.Hardware.Name), "cpu")
	workflowName, err := i.workflow.CreateSpaceDeploymentWorkflow(ctx, workflow.CreateSpaceDeploymentWorkflow{
		SourceRepoUrl:       gitlabRepoMetadata.SourceRepoUrl,
		SourceRepoCommit:    gitlabRepoMetadata.LatestCommit,
		BuildContextRepoUrl: i.config.Space.SpaceBuildContext,
		SpaceDomain:         i.config.Space.SpaceDomain,
		IngressClassName:    i.config.Space.SpaceIngressClassName,
		ResourceName:        resourceName,
		SpaceName:           gitlabRepoMetadata.SpaceName,
		ImageTag:            gitlabRepoMetadata.ImageTag,
		Timestamp:           time.Now().Unix(),
		GpuDeployment:       gpuDeployment,
		ResourceRequest: workflow.Resource{
			CPU: repo.Hardware.CPU,
			Mem: types.HardwareMem{}.FromMiB(uint(repo.Hardware.Mem)).ToK8SHardwareMem(), // Mem request only apply for CPU deployment
		},
		ResourceLimit: workflow.Resource{
			// CPU: 1,
			Mem: types.HardwareMem{}.FromMiB(uint(repo.Hardware.Mem)).ToK8SHardwareMem(), // Mem limit only apply for CPU deployment
		},
		NodeName: repo.Hardware.Name,
	},
	)
	if err != nil {
		span.SetStatus(codes.Error, "failed to create space deployment workflow")
		span.RecordError(err)
		return err
	}

	resp, err := i.repo.UpsertDeployment(ctx, repository.CreateDeploymentInput{
		Name:         gitlabRepoMetadata.SpaceName,
		UserID:       userID,
		WorkflowName: *workflowName,
		RepoID:       repo.ID,
		Revision:     revision,
		Status:       enums.ArgoWorkflowStatus_Pending,
		Commit:       gitlabRepoMetadata.LatestCommit,
		// HardwareID:   repo.Hardware.ID,
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to upsert deployment")
		span.RecordError(err)
		return err
	}

	var data dto.Deployment
	data = data.FromEntity(*resp)
	result.Data = &data

	return nil
}

// getUserGitlabAccessToken retrieves the GitLab access token for a user.
// For admin users, it returns the admin GitLab token instead of the user's token.
//
// Parameters:
//   - ctx: Context for the operation
//   - userID: ID of the user to get token for
//
// Returns:
//   - string: GitLab access token
//   - error: Any error that occurred during token retrieval
func (i *impl) getUserGitlabAccessToken(ctx context.Context, userID uuid.UUID) (string, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.getUserGitlabAccessToken")
	defer span.End()

	currentUser, err := i.repo.FindUserByID(ctx, userID)
	if err != nil {
		span.SetStatus(codes.Error, "failed to find user")
		span.RecordError(err)
		return "", err
	}

	token := ""
	if currentUser.GitlabAccessToken != nil {
		token = *currentUser.GitlabAccessToken
	}
	if currentUser.Role == enums.UserRole_Admin {
		adminRefGitID := enums.AdminRefGitID
		adminUser, err := i.repo.FindUser(ctx, repository.FindUserFilter{RefGitUserID: &adminRefGitID})
		if err != nil {
			span.SetStatus(codes.Error, "failed to find admin user")
			span.RecordError(err)
			return "", err
		}
		if adminUser.GitlabAccessToken != nil {
			token = *adminUser.GitlabAccessToken
		}
	}

	return token, nil
}

type GitlabRepoMetadata struct {
	SourceRepoUrl string
	SpaceName     string
	ImageTag      string
	LatestCommit  string
}

// getGitlabRepoMetadata retrieves metadata about a GitLab repository.
// It fetches repository URLs, latest commit, and generates deployment-specific information.
//
// Parameters:
//   - ctx: Context for the operation
//   - userID: ID of the user requesting metadata
//   - refGitRepoID: GitLab repository ID
//   - revision: Git revision to get metadata for
//
// Returns:
//   - *GitlabRepoMetadata: Repository metadata including URLs and commit information
//   - error: Any error that occurred during metadata retrieval
func (i *impl) getGitlabRepoMetadata(ctx context.Context, userID uuid.UUID, refGitRepoID int64, revision string) (*GitlabRepoMetadata, error) {
	token, err := i.getUserGitlabAccessToken(ctx, userID)
	if err != nil {
		return nil, err
	}

	repoHttpURL, gitlabURL, err := i.getRepoAndGitlabURL(ctx, refGitRepoID, token)
	if err != nil {
		return nil, err
	}

	latestCommit, err := i.getLatestCommit(ctx, refGitRepoID, token, revision)
	if err != nil {
		return nil, err
	}

	sourceRepoUrl := fmt.Sprintf("http://oauth2:%s@%s%s", token, gitlabURL.Host, repoHttpURL.Path)
	spaceName := utils.Slugify(strings.TrimSuffix(repoHttpURL.Path, ".git"))
	imageTag := fmt.Sprintf("%s-%s", spaceName, latestCommit)

	return &GitlabRepoMetadata{
		SourceRepoUrl: sourceRepoUrl,
		SpaceName:     spaceName,
		ImageTag:      imageTag,
		LatestCommit:  latestCommit,
	}, nil
}

// checkOngoingDeploymentStatus verifies if there is an ongoing deployment.
// It checks both the deployment status in the database and the Argo workflow status.
//
// Parameters:
//   - ctx: Context for the operation
//   - deployment: Deployment entity to check
//
// Returns:
//   - error: ErrDeploymentRunning if there is an ongoing deployment, ErrNoDeployment if no deployment exists,
//     or any other error that occurred during status check
func (i *impl) checkOngoingDeploymentStatus(ctx context.Context, deployment *entities.Deployment) error {
	if deployment != nil {
		switch deployment.Status {
		case enums.ArgoWorkflowStatus_Pending, enums.ArgoWorkflowStatus_Running:
			return usecase.ErrDeploymentRunning
		default:
		}

		// check Argo Workflow if workflowName is not empty
		if deployment.WorkflowName != "" {
			workflow, err := i.workflow.GetWorkflow(ctx, deployment.WorkflowName, argoNamespace)
			if err != nil {
				return err
			}
			switch workflow.Status.Phase {
			case v1alpha1.WorkflowUnknown, v1alpha1.WorkflowPending, v1alpha1.WorkflowRunning:
				return usecase.ErrDeploymentRunning
			default: // v1alpha1.WorkflowSucceeded, v1alpha1.WorkflowFailed, v1alpha1.WorkflowError
			}
		}

		return nil
	} else {
		return usecase.ErrNoDeployment
	}
}

// getLatestCommit retrieves the latest commit hash for a given branch in a GitLab repository.
//
// Parameters:
//   - ctx: Context for the operation
//   - refGitRepoID: GitLab repository ID
//   - token: GitLab access token
//   - branch: Branch name to get latest commit from
//
// Returns:
//   - string: Latest commit hash
//   - error: Any error that occurred during commit retrieval
func (i *impl) getLatestCommit(
	ctx context.Context,
	refGitRepoID int64,
	token string,
	branch string,
) (string, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.getLatestCommit")
	defer span.End()

	req := gitlab.GetSingleProjectBrancheRequest{
		Id:     refGitRepoID,
		Token:  token,
		Branch: branch,
	}
	resp, err := i.gitlab.GetSingleProjectBrancheInfo(ctx, req)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get repository branch info")
		span.RecordError(err)
		return "", err
	}

	return resp.Commit.ID, nil
}

// upsertConfigMap creates or updates a Kubernetes ConfigMap for environment variables.
// It retrieves environment variables from the repository and stores them in a ConfigMap.
//
// Parameters:
//   - ctx: Context for the operation
//   - repoID: Repository ID to get environment variables from
//   - resourceName: Name for the ConfigMap resource
//
// Returns:
//   - error: Any error that occurred during ConfigMap creation or update
func (i *impl) upsertConfigMap(
	ctx context.Context,
	repoID uuid.UUID,
	resourceName string,
) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.upsertConfigMap")
	defer span.End()

	env, err := i.repo.GetEnv(ctx, repoID)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		span.SetStatus(codes.Error, "failed to get repository environment variables")
		span.RecordError(err)
		return usecase.ErrInternal
	}

	var envMap map[string]string
	if env != nil {
		if err := json.Unmarshal(env.Env, &envMap); err != nil {
			span.SetStatus(codes.Error, "failed to unmarshal environment variables")
			span.RecordError(err)
			return err
		}
	}

	span.AddEvent("get configmap", trace.WithAttributes(attribute.String("configmap_name", resourceName)))
	cm, err := i.kubeClient.CoreV1().ConfigMaps(spaceNamespace).Get(ctx, resourceName, metav1.GetOptions{})
	if err == nil {
		span.AddEvent("configmap already existed, start to update current configmap", trace.WithAttributes(attribute.String("configmap_name", resourceName)))
		cm.Data = envMap
		_, err = i.kubeClient.CoreV1().ConfigMaps(spaceNamespace).Update(ctx, cm, metav1.UpdateOptions{})
		if err != nil {
			span.SetStatus(codes.Error, "failed to update configmap")
			span.RecordError(err)
			return err
		}
	} else {
		if strings.HasSuffix(err.Error(), "not found") {
			span.AddEvent("configmap not found, start to create a new configmap", trace.WithAttributes(attribute.String("configmap_name", resourceName)))
			cm := &corev1.ConfigMap{
				ObjectMeta: metav1.ObjectMeta{
					Name:      resourceName,
					Namespace: spaceNamespace,
				},
				Data: envMap,
			}
			_, err = i.kubeClient.CoreV1().ConfigMaps(spaceNamespace).Create(ctx, cm, metav1.CreateOptions{})
			if err != nil {
				span.SetStatus(codes.Error, "failed to create configmap")
				span.RecordError(err)
				return err
			}
		}
		span.SetStatus(codes.Error, "failed to get configmap")
		span.RecordError(err)
		return err
	}

	return nil
}

func (i *impl) createKubernetesIngressResource(
	ctx context.Context,
	spaceName, spaceDomain, resourceName string,
) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.createKubernetesIngressResource")
	defer span.End()

	ingressClient := i.kubeClient.NetworkingV1().Ingresses(spaceNamespace)
	_, err := ingressClient.Get(ctx, resourceName, metav1.GetOptions{})
	if err != nil {
		if !kubeerrors.IsNotFound(err) {
			span.SetStatus(codes.Error, "failed to get Ingress resource")
			span.RecordError(err)
			return usecase.ErrGetSpaceIngressResource
		}

		span.AddEvent("Ingress for this space does not exist, create a new Ingress")
		ingress := &networkingv1.Ingress{
			ObjectMeta: metav1.ObjectMeta{
				Name:      resourceName,
				Namespace: spaceNamespace,
				// Annotations: map[string]string{
				// 	"kubernetes.io/ingress.class": i.config.Space.SpaceIngressClassName,
				// },
			},
			Spec: networkingv1.IngressSpec{
				IngressClassName: &i.config.Space.SpaceIngressClassName,
				Rules: []networkingv1.IngressRule{
					{
						Host: fmt.Sprintf("%s.%s", spaceName, spaceDomain),
						IngressRuleValue: networkingv1.IngressRuleValue{
							HTTP: &networkingv1.HTTPIngressRuleValue{
								Paths: []networkingv1.HTTPIngressPath{
									{
										Path:     "/",
										PathType: func() *networkingv1.PathType { pt := networkingv1.PathTypePrefix; return &pt }(),
										Backend: networkingv1.IngressBackend{
											Service: &networkingv1.IngressServiceBackend{
												Name: resourceName,
												Port: networkingv1.ServiceBackendPort{
													Number: 80,
												},
											},
										},
									},
								},
							},
						},
					},
				},
			},
		}
		_, err = ingressClient.Create(ctx, ingress, metav1.CreateOptions{})
		if err != nil {
			span.SetStatus(codes.Error, "failed to create Kubernetes Ingress resource")
			span.RecordError(err)
			return usecase.ErrCreateSpaceIngressResource
		}
	} else {
		span.AddEvent("Ingress for this space already existed")
	}

	return nil
}

// applyKubernetesServiceResource creates or updates a Kubernetes Service resource for the space deployment.
// It configures the service to route traffic to the deployed space pods.
//
// Parameters:
//   - ctx: Context for the operation
//   - resourceName: Name for the Service resource
//
// Returns:
//   - error: Any error that occurred during Service resource creation or update
func (i *impl) applyKubernetesServiceResource(
	ctx context.Context,
	resourceName string,
) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.applyKubernetesServiceResource")
	defer span.End()

	serviceApplyConf := &applycorev1.ServiceApplyConfiguration{
		TypeMetaApplyConfiguration: applymetav1.TypeMetaApplyConfiguration{
			Kind:       utils.Ptr("Service"),
			APIVersion: utils.Ptr("v1"),
		},
		ObjectMetaApplyConfiguration: &applymetav1.ObjectMetaApplyConfiguration{
			Name:      utils.Ptr(resourceName),
			Namespace: utils.Ptr(spaceNamespace),
		},
		Spec: &applycorev1.ServiceSpecApplyConfiguration{
			Selector: map[string]string{"app": resourceName},
			Ports: []applycorev1.ServicePortApplyConfiguration{
				{
					Port: utils.Ptr(int32(80)),
					TargetPort: &intstr.IntOrString{
						Type:   intstr.Int,
						IntVal: 7860,
					},
				},
			},
		},
	}

	_, err := i.kubeClient.CoreV1().Services(spaceNamespace).Apply(ctx, serviceApplyConf, metav1.ApplyOptions{FieldManager: "volvo-api-server", Force: true})
	if err != nil {
		span.SetStatus(codes.Error, "failed to apply Service resource for space deployment")
		span.RecordError(err)
		return usecase.ErrApplySpaceServiceResource
	}

	return nil
}

// get repo HTTP URL and Gitlab URL for Argo Workflow to clone when building container image
// getRepoAndGitlabURL retrieves and parses the repository HTTP URL and GitLab host URL.
// These URLs are used by Argo Workflow for cloning repositories during container image building.
//
// Parameters:
//   - ctx: Context for the operation
//   - refGitRepoID: GitLab repository ID
//   - token: GitLab access token
//
// Returns:
//   - *url.URL: Parsed repository HTTP URL
//   - *url.URL: Parsed GitLab host URL
//   - error: Any error that occurred during URL retrieval or parsing
func (i *impl) getRepoAndGitlabURL(
	ctx context.Context,
	refGitRepoID int64,
	token string,
) (*url.URL, *url.URL, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.getRepoAndGitlabURL")
	defer span.End()

	req := gitlab.GetProjectURLsRequest{
		Id:    refGitRepoID,
		Token: token,
	}
	projectURL, err := i.gitlab.GetProjectURLs(ctx, req)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get Gitlab project URL")
		span.RecordError(err)
		return nil, nil, err
	}

	repoHttpURL, err := url.Parse(projectURL.HTTPUrl)
	if err != nil {
		span.SetAttributes(attribute.String("project_url", projectURL.HTTPUrl))
		span.SetStatus(codes.Error, "failed to parse project URL")
		span.RecordError(err)
		return nil, nil, err
	}
	gitlabUrl, err := url.Parse(i.config.Gitlab.Host)
	if err != nil {
		span.SetAttributes(attribute.String("gitlab_host", i.config.Gitlab.Host))
		span.SetStatus(codes.Error, "failed to parse Gitlab host")
		span.RecordError(err)
		return nil, nil, err
	}

	return repoHttpURL, gitlabUrl, nil
}

// StopDeployment implements the RepositoryUsecase interface for stopping a deployment.
// It terminates the Argo workflow and cleans up all associated Kubernetes resources.
//
// Parameters:
//   - ctx: Context for the operation
//   - req: Request containing repository ID and stop parameters
//
// Returns:
//   - error: Any error that occurred during deployment termination
func (i *impl) StopDeployment(ctx context.Context, req dto.StopDeploymentRequest) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.StopDeployment")
	defer span.End()

	if *req.RepoID.RepoType() != enums.RepoType_Spaces {
		span.SetStatus(codes.Error, usecase.ErrNotSpaceRepo.Error())
		span.RecordError(usecase.ErrNotSpaceRepo)
		return usecase.ErrNotSpaceRepo
	}

	repo, err := i.repo.FindRepository(ctx, repository.RepositoryFilter{
		Type:      req.RepoID.RepoType(),
		Namespace: req.RepoID.Namespace(),
		Name:      req.RepoID.RepoName(),
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find repository")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return usecase.ErrRepositoryNotFound
		}
		return err
	}
	if repo.Deployment == nil {
		span.SetStatus(codes.Error, "repository have no deployment")
		span.RecordError(err)
		return usecase.ErrNoDeployment
	}

	switch repo.Deployment.Status {
	case enums.ArgoWorkflowStatus_Error,
		enums.ArgoWorkflowStatus_Failed,
		enums.ArgoWorkflowStatus_NotRunning,
		enums.ArgoWorkflowStatus_Terminated:
		return usecase.ErrTerminateDeploymentNotRunning
	default:
	}

	err = i.workflow.TerminateWorkflow(ctx, repo.Deployment.WorkflowName, argoNamespace)
	if err != nil {
		if strings.Contains(err.Error(), "cannot shutdown a completed workflow") {
			err = nil
		} else {
			span.SetStatus(codes.Error, "failed to terminate workflow")
			span.RecordError(err)
			return err
		}
	}

	resourceName := spaceResourceName(repo.ID.String())

	err = deleteResources(ctx, i.kubeClient, spaceNamespace, resourceName)
	if err != nil {
		span.SetStatus(codes.Error, "failed to delete resources")
		span.RecordError(err)
		return err
	}

	_, err = i.repo.UpdateDeployment(ctx, repository.UpdateDeploymentInput{
		WorkflowName: repo.Deployment.WorkflowName,
		Status:       enums.ArgoWorkflowStatus_Terminated,
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to update deployment")
		span.RecordError(err)
		return usecase.ErrInternal
	}

	span.AddEvent("stop deployment successfully")
	span.SetStatus(codes.Ok, "stop deployment successfully")
	return nil
}

// Kubernetes resource name
func spaceResourceName(name string) string {
	return fmt.Sprintf("space-%s", name)
}

// deleteResources deletes Kubernetes resources associated with a space deployment.
// It removes Deployments, Services, and Ingresses for the given resource name.
// Errors during deletion of individual resources are logged but do not stop the process
// if the error indicates the resource was not found.
//
// Parameters:
//   - ctx: Context for the operation
//   - clientset: Kubernetes client interface
//   - namespace: Kubernetes namespace where resources are located
//   - resourceName: Name of the resources to delete
//
// Returns:
//   - error: Any error that occurred during resource deletion, other than "not found" errors
func deleteResources(
	ctx context.Context,
	clientset kubernetes.Interface,
	namespace, resourceName string,
) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.deleteResources")
	defer span.End()

	span.SetAttributes(attribute.String("namespace", namespace), attribute.String("resource_name", resourceName))

	err := clientset.AppsV1().
		Deployments(namespace).
		Delete(ctx, resourceName, metav1.DeleteOptions{})
	if err != nil {
		// skip if not found resource error else return error
		if !kubeerrors.IsNotFound(err) {
			span.SetStatus(codes.Error, "failed to delete resource deployments")
			span.RecordError(err)
			return err
		}
	}

	err = clientset.CoreV1().Services(namespace).Delete(ctx, resourceName, metav1.DeleteOptions{})
	if err != nil {
		// skip if not found resource error else return error
		if !kubeerrors.IsNotFound(err) {
			span.SetStatus(codes.Error, "failed to delete resource services")
			span.RecordError(err)
			return err
		}
	}

	err = clientset.NetworkingV1().
		Ingresses(namespace).
		Delete(ctx, resourceName, metav1.DeleteOptions{})
	if err != nil {
		// skip if not found resource error else return error
		if !kubeerrors.IsNotFound(err) {
			span.SetStatus(codes.Error, "failed to delete resource ingress")
			span.RecordError(err)
			return err
		}
	}

	return nil
}

// UpdateDeploymentStatus updates the status of a deployment in the database.
// It handles different Argo workflow statuses and updates the deployment record accordingly.
//
// Parameters:
//   - ctx: Context for the operation
//   - req: Request containing deployment name and new status information
//
// Returns:
//   - error: Any error that occurred during the database update
func (i *impl) UpdateDeploymentStatus(
	ctx context.Context,
	req dto.UpdateDeploymentStatusRequest,
) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.UpdateDeploymentStatus")
	defer span.End()

	var err error

	switch req.Status {
	case enums.ArgoWorkflowStatus_Running,
		enums.ArgoWorkflowStatus_Succeeded,
		enums.ArgoWorkflowStatus_Failed,
		enums.ArgoWorkflowStatus_Terminated,
		enums.ArgoWorkflowStatus_Error:
		_, err = i.repo.UpdateDeployment(ctx, repository.UpdateDeploymentInput{
			WorkflowName: req.Name,
			Status:       req.Status,
			Duration:     req.Duration,
		})
		if err != nil {
			span.SetStatus(codes.Error, "failed to update deployment")
			span.RecordError(err)
			err = usecase.ErrInternal
		}
	default:
		err = nil
	}

	span.AddEvent("update deployment status successfully")
	span.SetStatus(codes.Ok, "update deployment status successfully")
	return err
}

// GetDeploymentStatus retrieves the current status of a space deployment.
// It fetches repository and deployment details and maps the Argo workflow status
// to a user-friendly deployment status, including Kubernetes pod status if applicable.
//
// Parameters:
//   - ctx: Context for the operation
//   - req: Request containing the repository ID
//
// Returns:
//   - *dto.GetDeploymentStatusResponse: Deployment status information
//   - error: Any error that occurred during status retrieval
func (i *impl) GetDeploymentStatus(
	ctx context.Context,
	req dto.GetDeploymentStatusRequest,
) (*dto.GetDeploymentStatusResponse, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.GetDeploymentStatus")
	defer span.End()

	if *req.RepoID.RepoType() != enums.RepoType_Spaces {
		span.SetStatus(codes.Error, usecase.ErrNotSpaceRepo.Error())
		span.RecordError(usecase.ErrNotSpaceRepo)
		return nil, usecase.ErrNotSpaceRepo
	}

	repo, err := i.repo.FindRepository(ctx, repository.RepositoryFilter{
		Type:      req.RepoID.RepoType(),
		Namespace: req.RepoID.Namespace(),
		Name:      req.RepoID.RepoName(),
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find repository")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, usecase.ErrRepositoryNotFound
		}
		return nil, err
	}

	if repo.Deployment == nil {
		status := dto.DeploymentStatus{
			Status: string(enums.ArgoWorkflowStatus_NotRunning),
		}
		return &dto.GetDeploymentStatusResponse{
			Data: &status,
		}, nil
	}

	status, err := MapDeploymentStatus(ctx, i.kubeClient, repo.Deployment.Status, repo.ID.String())
	if err != nil {
		span.SetStatus(codes.Error, "failed to get deployment status")
		span.RecordError(err)
		return nil, err
	}

	return &dto.GetDeploymentStatusResponse{
		Data: status,
	}, nil
}

// k8s container status will be return directly (Waiting (with message and reason), Running, Terminated (with message and reason))
// Argo workflow status will be map before retuning (possible value: Building, Failed, Terminated, Not Running, Scheduling)
// nolint: cyclop
func MapDeploymentStatus(ctx context.Context, cli kubernetes.Interface, buildStatus enums.ArgoWorkflowStatus, repoID string) (*dto.DeploymentStatus, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.mapDeploymentStatus")
	defer span.End()

	var status dto.DeploymentStatus
	switch buildStatus {
	case enums.ArgoWorkflowStatus_Succeeded: // if workflow builds successfully, get container status
		resourceName := spaceResourceName(repoID)
		podLabelSelector := fmt.Sprintf("app=%s", resourceName)
		span.SetAttributes(attribute.String("pod_label_selector", podLabelSelector))

		podList, err := cli.CoreV1().
			Pods(spaceNamespace).
			List(ctx, metav1.ListOptions{
				LabelSelector: podLabelSelector,
			})
		if err != nil {
			span.SetStatus(codes.Error, "failed to list pods")
			span.RecordError(err)
			return nil, err
		}

		// if there is no pod or container, just return Scheduling while waiting for pod to be scheduled by k8s
		if len(podList.Items) == 0 {
			status.Status = "Scheduling"
			return &status, nil
		}

		// find first pod with Running status if there is no Running pod then return first pod
		var pod corev1.Pod
		pod = podList.Items[0] // default to first pod
		for _, it := range podList.Items {
			if it.Status.Phase == corev1.PodRunning {
				pod = it
				break
			}
		}

		containerStatuses := pod.Status.ContainerStatuses
		if len(containerStatuses) == 0 {
			status.Status = "Scheduling"
			return &status, nil
		}

		containerState := containerStatuses[0].State
		dto.MapContainerStatus(&containerState, &status)
	case enums.ArgoWorkflowStatus_Error, enums.ArgoWorkflowStatus_Failed:
		status.Status = "Failed"
	case enums.ArgoWorkflowStatus_Terminated:
		status.Status = string(enums.ArgoWorkflowStatus_Terminated)
	case enums.ArgoWorkflowStatus_Pending, enums.ArgoWorkflowStatus_Running:
		status.Status = "Building"
	default:
		status.Status = string(enums.ArgoWorkflowStatus_NotRunning)
	}

	return &status, nil
}

// GetPodLogs retrieves and streams logs from a running pod associated with a space deployment.
// It polls for the pod to be ready and then streams logs back through a channel.
//
// Parameters:
//   - ctx: Context for the operation, also used for stopping the log stream
//   - req: Request containing the repository ID
//
// Returns:
//   - chan dto.GetDeploymentLogsResponse: A channel that streams log lines
//   - error: Any error encountered before log streaming starts (e.g., repository not found, no deployment)
func (i *impl) GetPodLogs(ctx context.Context, req dto.GetDeploymentLogsRequest) (chan dto.GetDeploymentLogsResponse, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.GetPodLogs")
	defer span.End()

	repo, err := i.repo.FindRepository(ctx, repository.RepositoryFilter{
		Type:      req.RepoID.RepoType(),
		Namespace: req.RepoID.Namespace(),
		Name:      req.RepoID.RepoName(),
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find repository")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, usecase.ErrRepositoryNotFound
		}
		return nil, err
	}

	if repo.Deployment == nil {
		span.SetStatus(codes.Error, usecase.ErrNoDeployment.Error())
		span.RecordError(usecase.ErrNoDeployment)
		return nil, usecase.ErrNoDeployment
	}

	resourceName := spaceResourceName(repo.ID.String())
	dataChan := make(chan dto.GetDeploymentLogsResponse)

	var pod corev1.Pod
podPolling:
	for {
		select {
		case <-ctx.Done():
			break podPolling
		default:
			podList, err := i.kubeClient.CoreV1().
				Pods(spaceNamespace).
				List(ctx, metav1.ListOptions{
					LabelSelector: fmt.Sprintf("app=%s", resourceName),
				})
			if err != nil {
				close(dataChan)
				span.SetAttributes(attribute.String("pod_label_selector", fmt.Sprintf("app=%s", resourceName)))
				span.SetStatus(codes.Error, "failed to list pods")
				span.RecordError(err)
				return nil, err
			}

			if len(podList.Items) == 0 {
				utils.Sleep(ctx, 5*time.Second)
				continue podPolling
			}

			pod = podList.Items[0]
			stopChan := make(chan struct{})
			go StreamPodLogsRetry(ctx, i.kubeClient, spaceNamespace, pod.Name, dataChan, stopChan)

			break podPolling
		}
	}

	return dataChan, nil
}

// streamPodLogsRetry continuously tries to stream logs from a pod.
// It calls streamPodLogs and retries if it indicates a retry is needed (e.g., temporary error).
// The log streaming stops if the context is done or the stopChan is closed.
//
// Parameters:
//   - ctx: Context for the operation
//   - cli: Kubernetes client interface
//   - namespace: Namespace of the pod
//   - podName: Name of the pod to stream logs from
//   - dataChan: Channel to send log lines to
//   - stopChan: Channel to signal stopping the log stream
func StreamPodLogsRetry(ctx context.Context, cli kubernetes.Interface, namespace, podName string, dataChan chan dto.GetDeploymentLogsResponse, stopChan <-chan struct{}) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.streamPodLogsRetry")
	defer span.End()

	for {
		select {
		case <-ctx.Done():
			close(dataChan)
			return
		case <-stopChan: // TODO: handle sending message to stopChan
			close(dataChan)
			return
		default:
			retry, _ := streamPodLogs(ctx, cli, namespace, podName, dataChan, stopChan)
			if !retry {
				close(dataChan)
				return
			}
		}
	}
}

// streamPodLogs streams logs from a specific pod to a channel.
// It opens a log stream from the Kubernetes API and sends each log line to dataChan.
// The streaming stops if the context is done, stopCh is closed, or the log stream ends.
//
// Parameters:
//   - ctx: Context for the operation
//   - cli: Kubernetes client interface
//   - namespace: Namespace of the pod
//   - podName: Name of the pod to stream logs from
//   - dataChan: Channel to send log lines to
//   - stopCh: Channel to signal stopping the log stream
//
// Returns:
//   - bool: True if streaming should be retried (e.g., on temporary error or stream end), false otherwise.
//   - error: Any error encountered while trying to establish the log stream.
func streamPodLogs(ctx context.Context, cli kubernetes.Interface, namespace, podName string, dataChan chan dto.GetDeploymentLogsResponse, stopCh <-chan struct{}) (bool, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.streamPodLogs")
	defer span.End()

	stream, err := cli.CoreV1().Pods(namespace).
		GetLogs(podName, &corev1.PodLogOptions{
			Follow: true,
		}).
		Stream(ctx)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get logs from pod")
		span.RecordError(err)
		return true, err
	}
	defer func() { _ = stream.Close() }()

	sc := bufio.NewScanner(stream)
	for {
		select {
		case <-stopCh:
			return false, nil
		case <-ctx.Done():
			return false, nil
		default:
			if !sc.Scan() {
				return true, nil
			}

			dataChan <- dto.GetDeploymentLogsResponse{
				PodName: podName,
				Content: sc.Text(),
			}
		}
	}
}

// GetDeploymentBuildLogs retrieves and streams build logs from an Argo workflow associated with a space deployment.
// It fetches the workflow logs and streams them back through a channel.
//
// Parameters:
//   - ctx: Context for the operation, also used for stopping the log stream
//   - req: Request containing the repository ID
//
// Returns:
//   - chan dto.GetDeploymentLogsResponse: A channel that streams log lines from the build workflow
//   - error: Any error encountered before log streaming starts (e.g., repository not found, no deployment, workflow logs error)
func (i *impl) GetDeploymentBuildLogs(
	ctx context.Context,
	req dto.GetDeploymentLogsRequest,
) (chan dto.GetDeploymentLogsResponse, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.GetDeploymentLogs")
	defer span.End()

	repo, err := i.repo.FindRepository(ctx, repository.RepositoryFilter{
		Type:      req.RepoID.RepoType(),
		Namespace: req.RepoID.Namespace(),
		Name:      req.RepoID.RepoName(),
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find repository")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, usecase.ErrRepositoryNotFound
		}
		return nil, err
	}

	if repo.Deployment == nil {
		span.SetStatus(codes.Error, usecase.ErrNoDeployment.Error())
		span.RecordError(usecase.ErrNoDeployment)
		return nil, usecase.ErrNoDeployment
	}

	// TODO: make argo namespace an env var
	stream, err := i.workflow.GetWorkflowLogs(ctx, repo.Deployment.WorkflowName, argoNamespace)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get workflow logs")
		span.RecordError(err)
		return nil, err
	}

	dataChan := make(chan dto.GetDeploymentLogsResponse)

	go func() {
		for {
			select {
			case <-ctx.Done():
				close(dataChan)
				return
			default:
				log, err := stream.Recv()
				if err != nil {
					if errors.Is(err, io.EOF) {

						utils.Sleep(ctx, 3*time.Second)
						continue
					}
				}

				dataChan <- dto.GetDeploymentLogsResponse{
					PodName: log.GetPodName(),
					Content: log.GetContent(),
				}
			}
		}
	}()

	return dataChan, nil
}

// ListDeployment retrieves a paginated list of all deployments.
// It counts all deployments and then fetches a specific page with an optional order.
// For each deployment, it also fetches the current pod status.
//
// Parameters:
//   - ctx: Context for the operation
//   - req: Request containing pagination and ordering parameters
//
// Returns:
//   - *dto.ListDeploymentResponse: A paginated list of deployments with their statuses
//   - error: Any error encountered during retrieval
func (i *impl) ListDeployment(ctx context.Context, req dto.ListDeploymentRequest) (*dto.ListDeploymentResponse, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.ListDeployment")
	defer span.End()

	input := repository.ListDeploymentInput{}

	count, err := i.repo.CountDeployment(ctx, input)
	if err != nil {
		span.SetStatus(codes.Error, "failed to count deployment")
		span.RecordError(err)
		return nil, err
	}

	pagination := types.Pagination{
		PageNo:   req.Paginate.Page,
		PageSize: req.Paginate.PerPage,
	}
	orderBy := types.OrderBy{req.Paginate.OrderBy: req.Paginate.Sort}
	deployments, err := i.repo.ListDeployment(ctx, pagination, orderBy, input)
	if err != nil {
		span.SetStatus(codes.Error, "failed to list deployment")
		span.RecordError(err)
		return nil, err
	}

	data := dto.FromManyEntities[entities.Deployment, dto.Deployment](deployments)
	if err := getDeploymentPodStatus(ctx, i.kubeClient, deployments, data); err != nil {
		span.SetStatus(codes.Error, "failed to get deployment pod status")
		span.RecordError(err)
		return nil, err
	}

	span.AddEvent("list deployment successfully")
	span.SetStatus(codes.Ok, "list deployment successfully")
	return &dto.ListDeploymentResponse{
		Data: &data,
		Pagination: &dto.Pagination{
			Total:    int(count),
			PageNo:   req.Paginate.Page,
			PageSize: req.Paginate.PerPage,
		},
	}, nil
}

// getDeploymentPodStatus fetches and updates the status for a list of deployments concurrently.
// It uses a worker pool to call mapDeploymentStatus for each deployment.
//
// Parameters:
//   - ctx: Context for the operation
//   - cli: Kubernetes client interface
//   - deployments: A slice of deployment entities to get status for
//   - output: A slice of DTO deployments to update with status information (must match order of `deployments`)
//
// Returns:
//   - error: Any error encountered during concurrent status fetching
func getDeploymentPodStatus(ctx context.Context, cli kubernetes.Interface, deployments []entities.Deployment, output []dto.Deployment) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.getDeploymentPodStatus")
	defer span.End()

	pool := pond.NewPool(8)
	group := pool.NewGroupContext(ctx)

	for i, deploy := range deployments {
		group.SubmitErr(func() error {
			repoID := deploy.Repo.ID.String()
			status, err := MapDeploymentStatus(ctx, cli, deploy.Status, repoID)
			if err != nil {
				return err
			}

			output[i].Status = *status

			return nil
		})
	}

	err := group.Wait()
	if err != nil {
		span.SetStatus(codes.Error, "failed to get deployment status")
		span.RecordError(err)
		return err
	}

	return nil
}

// RestartDeployment restarts a running space deployment.
// It first ensures the repository is a space, finds the repository and its deployment,
// checks if the deployment status is Succeeded, re-applies the ConfigMap, and then triggers a restart.
//
// Parameters:
//   - ctx: Context for the operation
//   - req: Request containing the repository ID
//
// Returns:
//   - error: Any error encountered during the restart process
func (i *impl) RestartDeployment(ctx context.Context, req dto.RestartDeploymentRequest) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.RestartDeployment")
	defer span.End()

	if *req.RepoID.RepoType() != enums.RepoType_Spaces {
		span.SetStatus(codes.Error, usecase.ErrNotSpaceRepo.Error())
		span.RecordError(usecase.ErrNotSpaceRepo)
		return usecase.ErrNotSpaceRepo
	}

	repo, err := i.repo.FindRepository(ctx, repository.RepositoryFilter{
		Type:      req.RepoID.RepoType(),
		Namespace: req.RepoID.Namespace(),
		Name:      req.RepoID.RepoName(),
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find repository")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return usecase.ErrRepositoryNotFound
		}
		return err
	}
	if repo.Deployment == nil {
		span.SetStatus(codes.Error, usecase.ErrNoDeployment.Error())
		span.RecordError(usecase.ErrNoDeployment)
		return usecase.ErrNoDeployment
	}

	if repo.Deployment.Status != enums.ArgoWorkflowStatus_Succeeded {
		span.SetStatus(codes.Error, usecase.ErrDeploymentNotSucceeded.Error())
		span.RecordError(usecase.ErrDeploymentNotSucceeded)
		return usecase.ErrDeploymentNotSucceeded
	}

	resourceName := spaceResourceName(repo.ID.String())

	// create ConfigMap for env
	err = i.upsertConfigMap(ctx, repo.ID, resourceName)
	if err != nil {
		span.SetStatus(codes.Error, "failed to upsert configmap")
		span.RecordError(err)
		return err
	}

	isGpuDeployment := strings.ToLower(repo.Hardware.Name) != "cpu"
	if isGpuDeployment {
		// if GPU deployment, need to delete running pods to free GPU resources
		err = deleteRunningPods(ctx, i.kubeClient, spaceNamespace, resourceName)
		if err != nil {
			span.SetStatus(codes.Error, "failed to delete running pod")
			span.RecordError(err)
			return err
		}
	} else {
		err = restartSpaceDeployment(ctx, i.kubeClient, spaceNamespace, resourceName)
		if err != nil {
			span.SetStatus(codes.Error, "failed to restart deployment")
			span.RecordError(err)
			return err
		}
	}

	span.AddEvent("restart deployment successfully")
	span.SetStatus(codes.Ok, "restart deployment successfully")
	return nil
}

// restartSpaceDeployment triggers a restart of a Kubernetes deployment by updating its template annotations.
// It fetches the current Kubernetes Deployment, updates the 'timestamp' annotation in its pod template,
// and then applies the update to trigger a rolling restart.
//
// Parameters:
//   - ctx: Context for the operation
//   - clientset: Kubernetes client interface
//   - namespace: Kubernetes namespace of the deployment
//   - resourceName: Name of the Kubernetes Deployment to restart
//
// Returns:
//   - error: Any error encountered while getting or updating the Kubernetes Deployment
func restartSpaceDeployment(
	ctx context.Context,
	clientset kubernetes.Interface,
	namespace, resourceName string,
) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.restartSpaceDeployment")
	defer span.End()

	// Get the current deployment
	deployment, err := clientset.AppsV1().
		Deployments(namespace).
		Get(ctx, resourceName, metav1.GetOptions{})
	if err != nil {
		return err
	}

	// Update the annotation to trigger a restart
	if deployment.Spec.Template.ObjectMeta.Annotations == nil {
		deployment.Spec.Template.ObjectMeta.Annotations = make(map[string]string)
	}

	// set new value for timestamp annotation in these Deployment template `../../../pkg/argo/space-deployment-workflow.yaml` or `../../../pkg/argo/gpu-space-deployment-workflow.yaml`
	deployment.Spec.Template.ObjectMeta.Annotations["timestamp"] = strconv.FormatInt(
		time.Now().Unix(),
		10,
	)

	// Patch the deployment with the updated annotations
	_, err = clientset.AppsV1().
		Deployments(namespace).
		Update(ctx, deployment, metav1.UpdateOptions{})
	if err != nil {
		return err
	}

	return nil
}

// deleteRunningPod deletes all running pods for a given Kubernetes deployment.
//
// Parameters:
//   - ctx: Context for the operation
//   - clientset: Kubernetes client interface
//   - namespace: Kubernetes namespace of the deployment
//   - resourceName: Name of the Kubernetes Deployment to restart
//
// Returns:
//   - error: Any error encountered while getting or updating the Kubernetes Deployment
func deleteRunningPods(
	ctx context.Context,
	clientset kubernetes.Interface,
	namespace, resourceName string,
) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.deleteRunningPod")
	defer span.End()

	// Get all pods for this deployment and remove running ones to free GPU resources
	podLabelSelector := fmt.Sprintf("app=%s", resourceName)
	span.SetAttributes(attribute.String("pod_label_selector", podLabelSelector))

	podList, err := clientset.CoreV1().
		Pods(namespace).
		List(ctx, metav1.ListOptions{
			LabelSelector: podLabelSelector,
		})
	if err != nil {
		span.SetStatus(codes.Error, "failed to list pods for deployment")
		span.RecordError(err)
		return err
	}

	// Delete running pods to free GPU resources for new pods
	for _, pod := range podList.Items {
		if pod.Status.Phase == corev1.PodRunning {
			span.AddEvent("deleting running pod to free GPU resources",
				trace.WithAttributes(attribute.String("pod_name", pod.Name)))

			err = clientset.CoreV1().
				Pods(namespace).
				Delete(ctx, pod.Name, metav1.DeleteOptions{})
			if err != nil {
				// Log the error but don't fail the entire operation
				span.AddEvent("failed to delete running pod",
					trace.WithAttributes(
						attribute.String("pod_name", pod.Name),
						attribute.String("error", err.Error()),
					))
				// Continue with other pods instead of returning error
			} else {
				span.AddEvent("successfully deleted running pod",
					trace.WithAttributes(attribute.String("pod_name", pod.Name)))
			}
		}
	}

	return nil
}

// TerminateDeployment terminates a running or pending space deployment.
// It ensures the repository is a space, finds the repository and its deployment,
// checks if the deployment is in a state that can be terminated (Pending or Running),
// terminates the Argo workflow, and updates the deployment status in the database to Terminated.
//
// Parameters:
//   - ctx: Context for the operation
//   - userID: ID of the user initiating the termination (currently unused in the core logic but available)
//   - repoID: ID of the repository whose deployment is to be terminated
//
// Returns:
//   - error: Any error encountered during the termination process
func (i *impl) TerminateDeployment(ctx context.Context, userID uuid.UUID, repoID types.RepoID) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.TerminateDeployment")
	defer span.End()

	if *repoID.RepoType() != enums.RepoType_Spaces {
		span.SetStatus(codes.Error, usecase.ErrNotSpaceRepo.Error())
		span.RecordError(usecase.ErrNotSpaceRepo)
		return usecase.ErrNotSpaceRepo
	}

	repo, err := i.repo.FindRepository(ctx, repository.RepositoryFilter{
		Type:      repoID.RepoType(),
		Namespace: repoID.Namespace(),
		Name:      repoID.RepoName(),
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find repository")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return usecase.ErrRepositoryNotFound
		}
		return err
	}
	// check hardware
	if repo.Hardware != nil {
		span.SetStatus(codes.Error, usecase.ErrNoHardware.Error())
		span.RecordError(usecase.ErrNoHardware)
		return usecase.ErrNoHardware
	}

	// check ongoing deployment for this repoID
	if repo.Deployment != nil {
		if repo.Deployment.Status != enums.ArgoWorkflowStatus_Pending && repo.Deployment.Status != enums.ArgoWorkflowStatus_Running {
			span.SetStatus(codes.Error, usecase.ErrNoDeployment.Error())
			span.RecordError(usecase.ErrNoDeployment)
			return usecase.ErrNoDeployment
		}
	}

	err = i.workflow.TerminateWorkflow(ctx, repo.Deployment.WorkflowName, argoNamespace)
	if err != nil {
		span.SetStatus(codes.Error, "failed to terminate workflow")
		span.RecordError(err)
		return err
	}

	_, err = i.repo.UpdateDeployment(ctx, repository.UpdateDeploymentInput{
		WorkflowName: repo.Deployment.WorkflowName,
		Status:       enums.ArgoWorkflowStatus_Terminated,
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to update deployment")
		span.RecordError(err)
		return usecase.ErrInternal
	}

	span.AddEvent("terminate deployment successfully")
	span.SetStatus(codes.Ok, "terminate deployment successfully")
	return nil
}
