package repository

import (
	"context"
	"errors"

	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
	"gorm.io/gorm"

	"api-server/internal/dto"
	"api-server/internal/enums"
	"api-server/internal/gateways/gitlab"
	repository "api-server/internal/repositories"
	"api-server/internal/usecase"
	"api-server/pkg/oteltrace"
)

// ListContributors implements the RepositoryUsecase interface for retrieving repository contributors.
// It fetches a list of users who have contributed to the repository through commits.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: Input data containing repository ID
//
// Returns:
//   - []dto.User: List of contributor information
//   - error: Any error that occurred during retrieval
func (u *impl) ListRepoContributors(ctx context.Context, input dto.GetRepositoryContributorsInput) ([]dto.RepositoryContributor, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.ListRepoContributors")
	defer span.End()

	adminRefGitID := enums.AdminRefGitID
	adminUser, err := u.repo.FindUser(ctx, repository.FindUserFilter{RefGitUserID: &adminRefGitID})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find admin user")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, usecase.ErrUserNotFound
		}
		return nil, err
	}

	repo, err := u.repo.FindRepository(ctx, repository.RepositoryFilter{
		Type:      input.RepoID.RepoType(),
		Namespace: input.RepoID.Namespace(),
		Name:      input.RepoID.RepoName(),
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find repository")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, usecase.ErrRepositoryNotFound
		}
		return nil, err
	}

	token := ""
	if adminUser.GitlabAccessToken != nil {
		token = *adminUser.GitlabAccessToken
	}

	req := gitlab.GetProjectContributorsRequest{
		Id:    repo.RefGitRepoID,
		Ref:   input.Ref,
		Token: token,
	}

	gitlabResp, err := u.gitlab.GetProjectContributors(ctx, req)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get project contributors from gitlab")
		span.RecordError(err)
		return nil, err
	}

	resp := make([]dto.RepositoryContributor, len(gitlabResp))
	for i, item := range gitlabResp {
		resp[i] = *resp[i].FromGitlab(item)
	}

	// get contribbutors avatar
	for i := 0; i < len(resp); i++ {
		cn, err := u.repo.FindUser(ctx, repository.FindUserFilter{
			Email: &resp[i].Email,
		})
		if err != nil {
			span.AddEvent("failed to get contributor infor", trace.WithAttributes(attribute.String("email not exist", resp[i].Email)))
			span.RecordError(err)
			continue
		}

		if cn.Avatar != nil {
			span.AddEvent("start generating repository avatar")
			repoImage, err := u.aws.GenPreSignUrl(ctx, *cn.Avatar)
			if err != nil {
				span.SetStatus(codes.Error, "failed to generate presigned url for avatar")
				span.RecordError(err)
				return nil, err
			}

			resp[i].Avatar = &repoImage
		}
	}

	span.AddEvent("list repository contributors successfully")
	span.SetStatus(codes.Ok, "list repository contributors successfully")
	return resp, nil
}
