package repository

import (
	"context"
	"errors"
	"sort"

	"go.opentelemetry.io/otel/codes"
	"gorm.io/gorm"

	"api-server/internal/dto"
	"api-server/internal/enums"
	repository "api-server/internal/repositories"
	"api-server/internal/types"
	"api-server/internal/usecase"
	"api-server/pkg/oteltrace"
)

// GetEnv implements the RepositoryUsecase interface for retrieving repository environment variables.
// It fetches all environment variables associated with a space repository.
//
// Parameters:
//   - ctx: Context for the operation
//   - repoID: ID of the repository to get environment variables for
//
// Returns:
//   - *dto.GetRepositoryEnvResponse: Environment variables information
//   - error: Any error that occurred during retrieval
func (i *impl) GetEnv(
	ctx context.Context,
	repoID types.RepoID,
) (*dto.GetRepositoryEnvResponse, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.GetEnv")
	defer span.End()

	if *repoID.RepoType() != enums.RepoType_Spaces && *repoID.RepoType() != enums.RepoType_Composes {
		span.SetStatus(codes.Error, usecase.ErrNotSpaceOrComposeRepo.Error())
		span.RecordError(usecase.ErrNotSpaceOrComposeRepo)
		return nil, usecase.ErrNotSpaceOrComposeRepo
	}

	repo, err := i.repo.FindRepository(ctx, repository.RepositoryFilter{
		Type:      repoID.RepoType(),
		Namespace: repoID.Namespace(),
		Name:      repoID.RepoName(),
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find repository")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, usecase.ErrRepositoryNotFound
		}
		return nil, err
	}

	resp, err := i.repo.GetEnv(ctx, repo.ID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			span.AddEvent("repository env not found, returning default empty env")
			span.SetStatus(codes.Ok, "repository env retrieved successfully")
			return &dto.GetRepositoryEnvResponse{
				Data: &dto.RepoEnv{
					RepoID: repo.ID,
					Envs:   []dto.EnvKV{},
				},
			}, nil
		}
		span.SetStatus(codes.Error, "failed to get environment variables")
		span.RecordError(err)
		return nil, usecase.ErrInternal
	}

	var env dto.RepoEnv
	env = env.FromEntity(*resp)
	// Sort by Key alphabetically
	sort.Slice(env.Envs, func(i, j int) bool {
		return env.Envs[i].Key < env.Envs[j].Key
	})

	span.SetStatus(codes.Ok, "get repository environment variables successfully")
	return &dto.GetRepositoryEnvResponse{
		Data: &env,
	}, nil
}

// CreateEnv implements the RepositoryUsecase interface for creating repository environment variables.
// It handles the process of creating or deleting environment variables for a space repository.
//
// Parameters:
//   - ctx: Context for the operation
//   - repoID: ID of the repository to create environment variables for
//   - req: Input data containing environment variable key and value
//
// Returns:
//   - error: Any error that occurred during creation
func (i *impl) CreateEnv(
	ctx context.Context,
	repoID types.RepoID,
	req dto.CreateRepositoryEnvRequest,
) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.CreateEnv")
	defer span.End()

	if *repoID.RepoType() != enums.RepoType_Spaces && *repoID.RepoType() != enums.RepoType_Composes {
		span.SetStatus(codes.Error, usecase.ErrNotSpaceOrComposeRepo.Error())
		span.RecordError(usecase.ErrNotSpaceOrComposeRepo)
		return usecase.ErrNotSpaceOrComposeRepo
	}

	repo, err := i.repo.FindRepository(ctx, repository.RepositoryFilter{
		Type:      repoID.RepoType(),
		Namespace: repoID.Namespace(),
		Name:      repoID.RepoName(),
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find repository")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return usecase.ErrRepositoryNotFound
		}
		return err
	}

	err = i.repo.Transaction(ctx, func(ctx context.Context) error {
		if req.Value != nil {
			return i.repo.CreateEnv(ctx, repository.CreateEnvInput{
				RepoID: repo.ID,
				Key:    req.Key,
				Value:  *req.Value,
			})
		} else {
			return i.repo.DeleteEnv(ctx, repository.CreateEnvInput{
				RepoID: repo.ID,
				Key:    req.Key,
			})
		}
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to create repo env transaction")
		span.RecordError(err)
		return usecase.ErrInternal
	}

	span.AddEvent("create env successfully")
	span.SetStatus(codes.Ok, "create env successfully")
	return nil
}

// BulkCreateEnv implements the RepositoryUsecase interface for creating multiple repository environment variables.
// It handles the process of creating or deleting multiple environment variables for a space repository in a single operation.
//
// Parameters:
//   - ctx: Context for the operation
//   - repoID: ID of the repository to create environment variables for
//   - req: Input data containing multiple environment variable key-value pairs
//
// Returns:
//   - error: Any error that occurred during creation
func (i *impl) BulkCreateEnv(
	ctx context.Context,
	repoID types.RepoID,
	req dto.BulkCreateRepositoryEnvRequest,
) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.BulkCreateEnv")
	defer span.End()

	if *repoID.RepoType() != enums.RepoType_Spaces && *repoID.RepoType() != enums.RepoType_Composes {
		span.SetStatus(codes.Error, usecase.ErrNotSpaceRepo.Error())
		span.RecordError(usecase.ErrNotSpaceRepo)
		return usecase.ErrNotSpaceRepo
	}

	repo, err := i.repo.FindRepository(ctx, repository.RepositoryFilter{
		Type:      repoID.RepoType(),
		Namespace: repoID.Namespace(),
		Name:      repoID.RepoName(),
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find repository")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return usecase.ErrRepositoryNotFound
		}
		return usecase.ErrInternal
	}

	// Convert DTO to map for repository layer
	envMap := make(map[string]string)
	for _, env := range req.Envs {
		if env.Value != nil {
			envMap[env.Key] = *env.Value
		} else {
			// Empty string indicates deletion (similar to single CreateEnv logic)
			envMap[env.Key] = ""
		}
	}

	err = i.repo.Transaction(ctx, func(ctx context.Context) error {
		return i.repo.BulkCreateEnv(ctx, repository.BulkCreateEnvInput{
			RepoID: repo.ID,
			Envs:   envMap,
		})
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to bulk create repo env transaction")
		span.RecordError(err)
		return usecase.ErrInternal
	}

	span.AddEvent("bulk create env successfully")
	span.SetStatus(codes.Ok, "bulk create env successfully")
	return nil
}

// UpdateEnv implements the RepositoryUsecase interface for updating repository environment variables.
// It handles the process of updating existing environment variables in a space repository.
//
// Parameters:
//   - ctx: Context for the operation
//   - repoID: ID of the repository to update environment variables for
//   - req: Input data containing old key, new key, and value
//
// Returns:
//   - error: Any error that occurred during update
func (i *impl) UpdateEnv(
	ctx context.Context,
	repoID types.RepoID,
	req dto.UpdateRepositoryEnvRequest,
) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.UpdateEnv")
	defer span.End()

	if *repoID.RepoType() != enums.RepoType_Spaces && *repoID.RepoType() != enums.RepoType_Composes {
		span.SetStatus(codes.Error, usecase.ErrNotSpaceOrComposeRepo.Error())
		span.RecordError(usecase.ErrNotSpaceOrComposeRepo)
		return usecase.ErrNotSpaceOrComposeRepo
	}

	repo, err := i.repo.FindRepository(ctx, repository.RepositoryFilter{
		Type:      repoID.RepoType(),
		Namespace: repoID.Namespace(),
		Name:      repoID.RepoName(),
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find repository")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return usecase.ErrRepositoryNotFound
		}
		return err
	}

	err = i.repo.Transaction(ctx, func(ctx context.Context) error {
		return i.repo.UpdateEnv(ctx, repository.UpdateEnvInput{
			RepoID: repo.ID,
			OldKey: req.OldKey,
			Key:    req.Key,
			Value:  *req.Value,
		})

	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to execute transaction")
		span.RecordError(err)
		return usecase.ErrInternal
	}

	span.AddEvent("update env successfully")
	span.SetStatus(codes.Ok, "update env successfully")
	return nil
}

// DeleteEnv implements the RepositoryUsecase interface for removing repository environment variables.
// It handles the process of deleting environment variables from a space repository.
//
// Parameters:
//   - ctx: Context for the operation
//   - repoID: ID of the repository to delete environment variables from
//   - req: Input data containing the key of the environment variable to delete
//
// Returns:
//   - error: Any error that occurred during deletion
func (i *impl) DeleteEnv(ctx context.Context, repoID types.RepoID, req dto.DeleteRepositoryEnvRequest) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.DeleteEnv")
	defer span.End()

	if *repoID.RepoType() != enums.RepoType_Spaces && *repoID.RepoType() != enums.RepoType_Composes {
		span.SetStatus(codes.Error, usecase.ErrNotSpaceOrComposeRepo.Error())
		span.RecordError(usecase.ErrNotSpaceOrComposeRepo)
		return usecase.ErrNotSpaceOrComposeRepo
	}

	repo, err := i.repo.FindRepository(ctx, repository.RepositoryFilter{
		Type:      repoID.RepoType(),
		Namespace: repoID.Namespace(),
		Name:      repoID.RepoName(),
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find repository")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return usecase.ErrRepositoryNotFound
		}
		return err
	}

	if err = i.repo.Transaction(ctx, func(ctx context.Context) error {
		return i.repo.DeleteEnv(ctx, repository.CreateEnvInput{
			RepoID: repo.ID,
			Key:    req.Key,
		})
	}); err != nil {
		span.SetStatus(codes.Error, err.Error())
		span.RecordError(err)
		return usecase.ErrInternal
	}

	span.AddEvent("delete env successfully")
	span.SetStatus(codes.Ok, "delete env successfully")
	return nil
}
