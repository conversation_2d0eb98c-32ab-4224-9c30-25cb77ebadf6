package repository

import (
	"context"
	"errors"
	"time"

	"github.com/google/uuid"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
	"gorm.io/gorm"

	"api-server/internal/dto"
	"api-server/internal/entities"
	"api-server/internal/enums"
	"api-server/internal/gateways/gitlab"
	repository "api-server/internal/repositories"
	"api-server/internal/usecase"
	"api-server/internal/utils"
	"api-server/pkg/oteltrace"
)

// InviteRepoMember implements the RepositoryUsecase interface for inviting a member to a repository.
// It handles the process of inviting a user to a repository with specified role and expiration time.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: Input data containing repository ID, user ID, role, and expiration time
//
// Returns:
//   - error: Any error that occurred during invitation
func (u *impl) InviteRepoMember(ctx context.Context, input dto.InviteRepoMemberInput) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.InviteRepoMember")
	defer span.End()

	currentUser, err := u.repo.FindUserByID(ctx, input.CurrentUserID)
	if err != nil {
		span.SetStatus(codes.Error, "failed to find user by id")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return usecase.ErrUserNotFound
		}
		return err
	}

	if input.ExpireAt != nil && input.ExpireAt.Before(time.Now()) {
		err := errors.New("expire time is in the past")
		span.SetStatus(codes.Error, err.Error())
		span.RecordError(err)
		return usecase.ErrTimeIsPast
	}

	// query repo by id
	repo, err := u.repo.FindRepository(ctx, repository.RepositoryFilter{
		Type:      input.RepoID.RepoType(),
		Namespace: input.RepoID.Namespace(),
		Name:      input.RepoID.RepoName(),
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find repository")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return usecase.ErrRepositoryNotFound
		}
		return err
	}

	err = checkRepoInvite(ctx, span, u, repo, input.UserId, input.Role)
	if err != nil {
		return err
	}
	//get user permission accesstoken
	accessToken, err := getAccessToken(ctx, currentUser, *u, repo, span)
	if err != nil {
		return err
	}

	role := gitlab.OrganizationAccessLevel_Developer
	if input.Role == enums.RepoRole_Owner {
		role = gitlab.OrganizationAccessLevel_Owner
	}

	// find user being invited
	invitedUser, err := u.repo.FindUserByID(ctx, input.UserId)
	if err != nil {
		span.SetStatus(codes.Error, "failed to find user")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return usecase.ErrUserNotFound
		}
		return err
	}

	userRepoMember, err := u.repo.FindRepositoryMember(ctx, repository.FilterRepoMember{
		RepoID: &repo.ID,
		UserID: &invitedUser.ID,
	})
	if err != nil && err != gorm.ErrRecordNotFound {
		span.SetStatus(codes.Error, "failed to find repository member")
		span.RecordError(err)
		return err
	}

	// if user already is a member of this repo, return error
	if userRepoMember != nil && userRepoMember.UserID == input.UserId {
		span.SetStatus(codes.Error, usecase.ErrMemberAlreadyExistsInRepo.Error())
		span.RecordError(usecase.ErrMemberAlreadyExistsInRepo)
		return usecase.ErrMemberAlreadyExistsInRepo
	}

	err = u.repo.Transaction(ctx, func(ctx context.Context) error {
		// create repo member
		if err := u.repo.Create(ctx, &entities.RepoMember{
			User:      invitedUser,
			Role:      input.Role,
			ExpiresAt: input.ExpireAt,
			UserID:    invitedUser.ID,
			RepoID:    repo.ID,
		}); err != nil {
			span.SetStatus(codes.Error, "failed to create repository member")
			span.RecordError(err)
			return err
		}

		// add new member to project
		gitlabMember, err := u.gitlab.GetMemberOfProject(ctx, gitlab.GetMemberOfProjectRequest{
			RepoId: repo.RefGitRepoID,
			UserId: invitedUser.RefGitUserID,
			Token:  accessToken,
		})
		if err != nil {
			span.SetStatus(codes.Error, "failed to get member in gitlab project")
			span.RecordError(err)
		}

		if gitlabMember == nil {
			_, err = u.gitlab.AddUserToProject(ctx, gitlab.AddUserToProjectRequest{
				RepoId:      repo.RefGitRepoID,
				UserId:      invitedUser.RefGitUserID,
				AccessLevel: role,
				Token:       accessToken,
				ExpiresAt:   utils.ConvertTimeToString(input.ExpireAt),
			})
			if err != nil {
				span.SetStatus(codes.Error, "failed to add gitlab user to repository")
				span.RecordError(err)
				return err
			}
		}

		span.AddEvent("repository member updated successfully")
		span.SetStatus(codes.Ok, "repository member updated successfully")
		return nil
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to execute transaction")
		span.RecordError(err)
		return err
	}

	span.AddEvent("invite user to repository successfully")
	span.SetStatus(codes.Ok, "invite user to repository successfully")
	return nil
}

// getAccessToken retrieves the appropriate GitLab access token for repository operations.
// It determines whether to use the current user's token or the admin user's token based on permissions.
//
// Parameters:
//   - ctx: Context for the operation
//   - currentUser: The user requesting access
//   - u: Repository usecase implementation
//   - repo: The repository to access
//   - span: OpenTelemetry span for tracing
//
// Returns:
//   - string: The GitLab access token to use
//   - error: Any error that occurred during token retrieval
func getAccessToken(ctx context.Context, currentUser *entities.User, u impl, repo *entities.Repository, span trace.Span) (string, error) {
	var accessToken string
	// check current user permission
	if currentUser.Role != enums.UserRole_Admin {
		err := checkRepoMemberPermission(ctx, u, repo, currentUser, span)
		if err != nil {
			span.SetStatus(codes.Error, "no permission")
			span.RecordError(err)
			return "", err
		}

		if currentUser.GitlabAccessToken != nil {
			accessToken = *currentUser.GitlabAccessToken
		}
	} else {
		adminRefGitID := enums.AdminRefGitID
		adminUser, err := u.repo.FindUser(ctx, repository.FindUserFilter{RefGitUserID: &adminRefGitID})
		if err != nil {
			span.SetStatus(codes.Error, "failed to find admin user")
			span.RecordError(err)
			return "", err
		}
		if adminUser.GitlabAccessToken != nil {
			accessToken = *adminUser.GitlabAccessToken
		}
	}
	return accessToken, nil
}

// checkRepoMemberPermission verifies if the current user has owner permissions in the repository.
// It checks if the user is a member of the repository and has the owner role.
//
// Parameters:
//   - ctx: Context for the operation
//   - u: Repository usecase implementation
//   - repo: The repository to check permissions for
//   - currentUser: The user whose permissions are being checked
//   - span: OpenTelemetry span for tracing
//
// Returns:
//   - error: Any error that occurred during permission check, including ErrNoPermission if user lacks owner role
func checkRepoMemberPermission(ctx context.Context, u impl, repo *entities.Repository, currentUser *entities.User, span trace.Span) error {
	currentUserRepo, err := u.repo.FindRepositoryMember(ctx, repository.FilterRepoMember{
		RepoID: &repo.ID,
		UserID: &currentUser.ID,
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find repository member")
		span.RecordError(err)
		return err
	}

	if currentUserRepo.Role != enums.RepoRole_Owner {
		err := errors.New("current user does not have owner role in repository")
		span.SetStatus(codes.Error, err.Error())
		span.RecordError(err)
		return usecase.ErrNoPermission
	}
	return nil
}

// InviteMultipleRepoMembers implements the RepositoryUsecase interface for inviting multiple members to a repository.
// It handles the process of inviting multiple users to a repository with specified roles and expiration times.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: Input data containing repository ID and list of members to invite
//
// Returns:
//   - error: Any error that occurred during invitation process
func (u *impl) InviteMultipleRepoMembers(ctx context.Context, input dto.InviteRepoMembersInput) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.InviteMultipleRepoMembers")
	defer span.End()

	currentUser, err := u.repo.FindUserByID(ctx, input.CurrentUserID)
	if err != nil {
		span.SetStatus(codes.Error, "failed to find current user")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return usecase.ErrUserNotFound
		}
		return err
	}

	// query repo by id - Moved outside the loop for efficiency
	repo, err := u.repo.FindRepository(ctx, repository.RepositoryFilter{
		Type:      input.RepoID.RepoType(),
		Namespace: input.RepoID.Namespace(),
		Name:      input.RepoID.RepoName(),
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find repository")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return usecase.ErrRepositoryNotFound
		}
		return err
	}

	//get user permission accesstoken
	accessToken, err := getAccessToken(ctx, currentUser, *u, repo, span)
	if err != nil {
		return err
	}

	for _, memberInput := range input.Members {
		if memberInput.ExpireAt != nil && memberInput.ExpireAt.Before(time.Now()) {
			span.SetStatus(codes.Error, "expired time in the past for a member")
			span.RecordError(usecase.ErrTimeIsPast)
			return usecase.ErrTimeIsPast // Consider if you want to return immediately or collect errors for all members
		}

		err = checkRepoInvite(ctx, span, u, repo, memberInput.UserId, memberInput.Role)
		if err != nil {
			return err
		}

		user, err := u.repo.FindUserByID(ctx, memberInput.UserId)
		if err != nil {
			span.SetStatus(codes.Error, "failed to find user to invite")
			span.RecordError(err)
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return usecase.ErrUserNotFound
			}
			return err
		}

		err = checkMemberExistInRepo(ctx, u, span, repo.ID, memberInput.UserId)
		if err != nil {
			return err
		}

		err = u.repo.Transaction(ctx, func(ctx context.Context) error {
			// create repo member
			err = u.repo.Create(ctx, &entities.RepoMember{
				Role:      memberInput.Role,
				ExpiresAt: memberInput.ExpireAt,
				UserID:    user.ID,
				RepoID:    repo.ID,
			})
			if err != nil {
				span.SetStatus(codes.Error, "failed to create repository member")
				span.RecordError(err)
				return err
			}

			// map Gitlab access level
			var gitlabAccessLevel int
			switch memberInput.Role {
			case enums.RepoRole_Owner:
				gitlabAccessLevel = gitlab.OrganizationAccessLevel_Owner
			case enums.RepoRole_Developer:
				gitlabAccessLevel = gitlab.OrganizationAccessLevel_Developer
			}
			// add new member to project
			gitlabMember, err := u.gitlab.GetMemberOfProject(ctx, gitlab.GetMemberOfProjectRequest{
				RepoId: repo.RefGitRepoID,
				UserId: user.RefGitUserID,
				Token:  accessToken,
			})
			if err != nil {
				span.SetStatus(codes.Error, "failed to get member in gitlab project")
				span.RecordError(err)
			}

			if gitlabMember == nil {
				_, err = u.gitlab.AddUserToProject(ctx, gitlab.AddUserToProjectRequest{
					RepoId:      repo.RefGitRepoID,
					UserId:      user.RefGitUserID,
					AccessLevel: gitlabAccessLevel,
					Token:       accessToken,
					ExpiresAt:   utils.ConvertTimeToString(memberInput.ExpireAt),
				})
				if err != nil {
					span.SetStatus(codes.Error, "failed to add gitlab user to repository")
					span.RecordError(err)
					return err
				}
			}

			return nil
		})
		if err != nil {
			span.SetStatus(codes.Error, "failed to execute transaction")
			span.RecordError(err)
			return err
		}

		span.AddEvent(
			"invite user to repository successfully",
			trace.WithAttributes(
				attribute.String("user_id", memberInput.UserId.String()),
				attribute.String("role", memberInput.Role.String()),
			),
		)
	}

	span.AddEvent("invite users to repository successfully")
	span.SetStatus(codes.Ok, "invite users to repository successfully")
	return nil
}

// checkMemberExistInRepo verifies if a user is already a member of the repository.
// It checks if the user exists in the repository's member list.
//
// Parameters:
//   - ctx: Context for the operation
//   - u: Repository usecase implementation
//   - span: OpenTelemetry span for tracing
//   - repoID: ID of the repository to check
//   - memberUserID: ID of the user to check membership for
//
// Returns:
//   - error: Any error that occurred during check, including ErrMemberAlreadyExistsInRepo if user is already a member
func checkMemberExistInRepo(ctx context.Context, u *impl, span trace.Span, repoID, memberUserID uuid.UUID) error {
	userRepo, err := u.repo.FindRepositoryMember(ctx, repository.FilterRepoMember{
		RepoID: &repoID,
		UserID: &memberUserID,
	})
	if err != nil && err != gorm.ErrRecordNotFound {
		span.SetStatus(codes.Error, "failed to find repository member")
		span.RecordError(err)
		return err
	}

	// check if member already exists in project
	if userRepo != nil && userRepo.UserID == memberUserID {
		span.SetStatus(codes.Error, usecase.ErrMemberAlreadyExistsInRepo.Error())
		span.RecordError(usecase.ErrMemberAlreadyExistsInRepo)
		return usecase.ErrMemberAlreadyExistsInRepo
	}

	return nil
}

// checkRepoInvite verifies if it is possible to invite a new user to the repository.
// It checks various conditions based on repository ownership and user roles.
//
// Parameters:
//   - ctx: Context for the operation
//   - span: OpenTelemetry span for tracing
//   - u: Repository usecase implementation
//   - repo: The repository to check
//   - userID: ID of the user to be invited
//   - userRole: Role to be assigned to the user
//
// Returns:
//   - error: Any error that occurred during check, including role-specific errors
func checkRepoInvite(ctx context.Context, span trace.Span, u *impl, repo *entities.Repository, userID uuid.UUID, userRole enums.RepoRole) error {
	// check whether repo belongs to the user or the organization
	isOwnedByUser := true
	if repo.OrgID != nil {
		isOwnedByUser = false
	}

	if isOwnedByUser {
		if userRole == enums.RepoRole_Owner {
			span.SetStatus(codes.Error, "cannot invite user as owner to personal repository")
			span.RecordError(usecase.ErrCannotInviteUserAsOwner)
			return usecase.ErrCannotInviteUserAsOwner
		}
	} else {
		// check the valid role for the user being invited to the repository under the organization
		orgMember, err := u.repo.FindOrgMember(ctx, repository.FilterOrgMember{
			UserID: &userID,
			OrgID:  repo.OrgID,
		})
		if err != nil && err != gorm.ErrRecordNotFound {
			span.SetStatus(codes.Error, "failed to find organization member")
			span.RecordError(err)
			return err
		}

		// cannot invite organization owner as repository developer
		if orgMember != nil && orgMember.Role == enums.OrgRole_Owner && userRole == enums.RepoRole_Developer {
			span.SetStatus(codes.Error, usecase.ErrInviteOrgOwnerAsDeveloper.Error())
			span.RecordError(usecase.ErrInviteOrgOwnerAsDeveloper)
			return usecase.ErrInviteOrgOwnerAsDeveloper
		}
	}

	return nil
}
