package repository

import (
	"context"
	"errors"

	"go.opentelemetry.io/otel/codes"
	"gorm.io/gorm"

	"api-server/internal/dto"
	"api-server/internal/enums"
	"api-server/internal/gateways/gitlab"
	repository "api-server/internal/repositories"
	"api-server/internal/usecase"
	"api-server/pkg/oteltrace"
)

// GetFiles implements the RepositoryUsecase interface for retrieving repository files.
// It fetches a list of files and directories in a specific repository path.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: Input data containing repository ID and path
//
// Returns:
//   - []dto.RepositoryFile: List of file and directory information
//   - *string: Pointer to the next page URL for pagination
//   - error: Any error that occurred during retrieval
func (u *impl) ListRepoFiles(ctx context.Context, input dto.GetRepositoryFilesInput) ([]dto.RepositoryFile, *string, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.ListRepoFiles")
	defer span.End()

	adminRefGitID := enums.AdminRefGitID
	adminUser, err := u.repo.FindUser(ctx, repository.FindUserFilter{RefGitUserID: &adminRefGitID})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find admin user")
		span.RecordError(err)
		return nil, nil, err
	}

	repo, err := u.repo.FindRepository(ctx, repository.RepositoryFilter{
		Type:      input.RepoID.RepoType(),
		Namespace: input.RepoID.Namespace(),
		Name:      input.RepoID.RepoName(),
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find repository")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil, usecase.ErrRepositoryNotFound
		}
		return nil, nil, err
	}

	token := ""
	if adminUser.GitlabAccessToken != nil {
		token = *adminUser.GitlabAccessToken
	}

	req := gitlab.GetProjectFilesRequest{
		Id:        repo.RefGitRepoID,
		Ref:       input.Ref,
		Path:      input.Path,
		Recursive: input.Recursive,
		Token:     token,
		PerPage:   input.PerPage,
		PageToken: input.PageToken,
		All:       input.All,
	}
	gitlabResp, nextPage, err := u.gitlab.GetProjectFiles(ctx, req)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get project files from gitlab")
		span.RecordError(err)
		return nil, nil, err
	}

	resp := make([]dto.RepositoryFile, len(gitlabResp))
	for i, item := range gitlabResp {
		resp[i] = *resp[i].FromGitlab(item)
	}

	span.AddEvent("list repository files successfully")
	span.SetStatus(codes.Ok, "list repository files successfully")
	return resp, nextPage, nil
}
