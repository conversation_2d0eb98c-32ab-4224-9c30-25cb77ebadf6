package repository

import (
	"context"
	"errors"
	"net/http"

	"go.opentelemetry.io/otel/codes"
	"gorm.io/gorm"

	"api-server/internal/dto"
	"api-server/internal/enums"
	"api-server/internal/gateways/gitlab"
	repository "api-server/internal/repositories"
	"api-server/internal/usecase"
	"api-server/pkg/oteltrace"
)

// GetRawFileFromRepository implements the RepositoryUsecase interface for retrieving raw file content.
// It fetches the raw content of a file from a repository without any processing.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: Input data containing repository ID and file path
//
// Returns:
//   - *http.Response: HTTP response containing the raw file content
//   - error: Any error that occurred during retrieval
func (u *impl) GetRawFileFromRepository(ctx context.Context, input dto.GetFileFromRepositoryInput) (*http.Response, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.GetRawFileFromRepository")
	defer span.End()

	adminRefGitID := enums.AdminRefGitID
	adminUser, err := u.repo.FindUser(ctx, repository.FindUserFilter{RefGitUserID: &adminRefGitID})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find admin user")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, usecase.ErrUserNotFound
		}
		return nil, err
	}

	repo, err := u.repo.FindRepository(ctx, repository.RepositoryFilter{
		Type:      input.RepoID.RepoType(),
		Namespace: input.RepoID.Namespace(),
		Name:      input.RepoID.RepoName(),
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find repository")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, usecase.ErrRepositoryNotFound
		}
		return nil, err
	}

	token := ""
	if adminUser.GitlabAccessToken != nil {
		token = *adminUser.GitlabAccessToken
	}

	rawFile, err := u.gitlab.GetRawFileFromRepo(ctx, gitlab.GetFileRequest{
		ProjectId: repo.RefGitRepoID,
		FilePath:  input.Path,
		Ref:       input.Ref,
		Token:     token,
		LFS:       input.LFS,
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to get raw file from repo in gitlab")
		span.RecordError(err)
		return nil, err
	}

	span.AddEvent("get raw file successfully")
	span.SetStatus(codes.Ok, "get raw file successfully")
	return rawFile, nil
}

// GetHeaderRawFileFromRepository implements the RepositoryUsecase interface for retrieving raw file headers.
// It fetches the HTTP headers of a file from a repository without downloading the content.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: Input data containing repository ID and file path
//
// Returns:
//   - *http.Response: HTTP response containing the file headers
//   - error: Any error that occurred during retrieval
func (u *impl) GetHeaderRawFileFromRepository(ctx context.Context, input dto.GetFileFromRepositoryInput) (*http.Response, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.GetHeaderRawFileFromRepository")
	defer span.End()

	adminRefGitID := enums.AdminRefGitID
	adminUser, err := u.repo.FindUser(ctx, repository.FindUserFilter{RefGitUserID: &adminRefGitID})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find admin user")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, usecase.ErrUserNotFound
		}
		return nil, err
	}

	repo, err := u.repo.FindRepository(ctx, repository.RepositoryFilter{
		Type:      input.RepoID.RepoType(),
		Namespace: input.RepoID.Namespace(),
		Name:      input.RepoID.RepoName(),
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find repository")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, usecase.ErrRepositoryNotFound
		}
		return nil, err
	}

	token := ""
	if adminUser.GitlabAccessToken != nil {
		token = *adminUser.GitlabAccessToken
	}

	rawFile, err := u.gitlab.GetHeaderRawFileFromRepo(ctx, gitlab.GetFileRequest{
		ProjectId: repo.RefGitRepoID,
		FilePath:  input.Path,
		Ref:       input.Ref,
		Token:     token,
		LFS:       input.LFS,
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to get header raw file from repo in gitlab")
		span.RecordError(err)
		return nil, err
	}

	span.AddEvent("get header raw file successfully")
	span.SetStatus(codes.Ok, "get header raw file successfully")
	return rawFile, nil
}
