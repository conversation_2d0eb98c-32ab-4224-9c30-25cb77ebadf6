package repository

import (
	"api-server/internal/dto"
	"api-server/internal/entities"
	repository "api-server/internal/repositories"
	"api-server/internal/usecase"
	"api-server/pkg/oteltrace"
	"context"
	"errors"
	"fmt"
	"reflect"
	"strings"

	"go.opentelemetry.io/otel/codes"
	"gorm.io/gorm"
)

// CreateTag implements the RepositoryUsecase interface for creating repository tags.
// It handles the process of creating a new tag in the repository with specified name and reference.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: Input data containing repository ID, tag name, and reference
//
// Returns:
//   - *dto.Tag: Created tag information
//   - error: Any error that occurred during creation
func (u *impl) CreateRepoTag(ctx context.Context, input dto.CreateRepoTagInput) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.CreateRepoTag")
	defer span.End()

	_, err := u.repo.FindUserByID(ctx, input.CurrentUserID)
	if err != nil {
		span.SetStatus(codes.Error, "failed to find user")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return usecase.ErrUserNotFound
		}
		return err
	}

	tag := &entities.Tag{
		Type:      input.Type,
		SubType:   input.SubType,
		Name:      input.Name,
		IconUrl:   input.IconUrl,
		RepoTypes: input.RepoTypes,
	}

	//identify type
	if err := identifyTag(tag); err != nil {
		span.SetStatus(codes.Error, "failed to identify tag")
		span.RecordError(err)
		return err
	}

	if err := u.repo.Create(ctx, &tag); err != nil {
		span.SetStatus(codes.Error, "failed to create tag")
		span.RecordError(err)
		return err
	}

	span.AddEvent("create tag successfully")
	span.SetStatus(codes.Ok, "create tag successfully")
	return nil
}

// identifyTag determines the query string and value for a tag based on its type and name.
// It uses reflection on dto.TagsQuery to map field names to JSON tags for constructing
// the tag's query parameters. Special handling is provided for "Main" tags.
//
// Parameters:
//   - tag: The tag entity to identify. Its Query and Value fields will be populated.
//
// Returns:
//   - error: An error if the tag type is invalid or cannot be identified.
func identifyTag(tag *entities.Tag) error {
	mapType := make(map[string]string)
	t := reflect.TypeOf(dto.TagsQuery{})
	for i := range t.NumField() {
		field := t.Field(i)
		jsonTag := field.Tag.Get("json")
		mapType[field.Name] = strings.Split(jsonTag, ",")[0]
	}

	for key, value := range mapType {
		if strings.Contains(strings.ToLower(key), strings.ToLower(tag.Type)) ||
			strings.Contains(strings.ToLower(tag.Type), strings.ToLower(key)) {
			tag.Value = fmt.Sprintf("%s:%s", value, strings.ToLower(tag.Name))
			tag.Query = value
		}
	}
	//Main tag
	if tag.Type == "Main" && tag.SubType != "" && tag.Value == "" && tag.Query == "" {
		tag.Value = fmt.Sprintf("%s:%s", strings.ToLower(tag.SubType), strings.ToLower(tag.Name))
		tag.Query = "tags"
	}

	if tag.Value == "" || tag.Query == "" {
		return errors.New("invalid tag")
	}

	return nil
}

// DeleteTag implements the RepositoryUsecase interface for removing repository tags.
// It handles the process of deleting an existing tag from the repository.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: Input data containing repository ID and tag name
//
// Returns:
//   - error: Any error that occurred during deletion
func (u *impl) DeleteRepoTag(ctx context.Context, input dto.DeleteRepoTagInput) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.DeleteRepoTag")
	defer span.End()

	_, err := u.repo.FindUserByID(ctx, input.CurrentUserID)
	if err != nil {
		span.SetStatus(codes.Error, "failed to find user")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return usecase.ErrUserNotFound
		}
		return err
	}

	tag, err := u.repo.FindRepoTag(ctx, repository.FilterTagInput{
		Id: input.Id,
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find tag")
		span.RecordError(err)
		return err
	}

	if err := u.repo.DeleteById(ctx, entities.Tag{}, tag.ID); err != nil {
		span.SetStatus(codes.Error, "failed to delete tag")
		span.RecordError(err)
		return err
	}

	span.AddEvent("delete tag successfully")
	span.SetStatus(codes.Ok, "delete tag successfully")
	return nil
}

// GetTag implements the RepositoryUsecase interface for retrieving repository tag information.
// It fetches detailed information about a specific tag in the repository.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: Input data containing repository ID and tag name
//
// Returns:
//   - *dto.Tag: Tag information
//   - error: Any error that occurred during retrieval
func (u *impl) GetRepoTag(ctx context.Context, input dto.GetRepoTagInput) (*entities.Tag, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.GetRepoTag")
	defer span.End()

	_, err := u.repo.FindUserByID(ctx, input.CurrentUserID)
	if err != nil {
		span.SetStatus(codes.Error, "failed to find user")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, usecase.ErrUserNotFound
		}
		return nil, err
	}

	tag, err := u.repo.FindRepoTag(ctx, repository.FilterTagInput{
		Id: input.Id,
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find repo tags")
		span.RecordError(err)
		return nil, err
	}

	return tag, nil
}

// UpdateRepoTag implements the RepositoryUsecase interface for updating repository tags.
// It handles the process of modifying an existing tag in the repository.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: Input data containing tag ID and new tag details
//
// Returns:
//   - error: Any error that occurred during update
func (u *impl) UpdateRepoTag(ctx context.Context, input dto.UpdateRepoTagInput) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.UpdateRepoTag")
	defer span.End()

	_, err := u.repo.FindUserByID(ctx, input.CurrentUserID)
	if err != nil {
		span.SetStatus(codes.Error, "failed to find user")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return usecase.ErrUserNotFound
		}
		return err
	}

	tag, err := u.repo.FindRepoTag(ctx, repository.FilterTagInput{
		Id: input.Id,
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find tag")
		span.RecordError(err)
		return err
	}

	tag.Type = input.Type
	tag.SubType = input.SubType
	tag.Name = input.Name
	tag.IconUrl = input.IconUrl
	tag.RepoTypes = input.RepoTypes

	if err := identifyTag(tag); err != nil {
		span.SetStatus(codes.Error, "failed to identify tags")
		span.RecordError(err)
		return err
	}

	if err := u.repo.Save(ctx, tag); err != nil {
		span.SetStatus(codes.Error, "failed to update tag")
		span.RecordError(err)
		return err
	}

	span.AddEvent("update tag successfully")
	span.SetStatus(codes.Ok, "update tag successfully")
	return nil
}
