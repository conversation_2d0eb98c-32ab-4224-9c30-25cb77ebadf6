package repository_test

import (
	"context"
	"errors"
	"testing"

	"github.com/argoproj/argo-workflows/v3/pkg/apis/workflow/v1alpha1"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	kubeerrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/kubernetes/fake"
	kubetesting "k8s.io/client-go/testing"

	"api-server/configs"
	"api-server/internal/dto"
	"api-server/internal/entities"
	"api-server/internal/enums"
	"api-server/internal/gateways/gitlab"
	gitlab_mocks "api-server/internal/gateways/gitlab/mocks"
	repository_mocks "api-server/internal/repositories/mocks"
	"api-server/internal/types"
	"api-server/internal/usecase"
	"api-server/internal/usecase/repository"
	workflow_mocks "api-server/internal/usecase/workflow/mocks"
	"api-server/internal/utils"
)

func TestStartDeployment(t *testing.T) {
	revision := "main"
	userID := uuid.New()
	hardwareID := uuid.New()

	customErr := errors.New("error")

	type dependencies struct {
		config     *configs.GlobalConfig
		repo       *repository_mocks.MockRepository
		gitlab     *gitlab_mocks.MockGitlabClient
		workflow   *workflow_mocks.MockWorkflowUsecase
		kubeClient kubernetes.Interface
	}

	tests := []struct {
		name             string
		input            dto.StartDeploymentRequest
		repoID           *types.RepoID
		mockFn           func(d *dependencies)
		expectedResponse string
		expectedErr      error
	}{
		{
			name:   "repository not found",
			repoID: types.NewRepoID(enums.RepoType_Spaces, "namespace", "name"),
			input: dto.StartDeploymentRequest{
				Revision: &revision,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, usecase.ErrRepositoryNotFound)
			},
			expectedErr: usecase.ErrRepositoryNotFound,
		},
		{
			name:   "FindRepository return error",
			repoID: types.NewRepoID(enums.RepoType_Spaces, "namespace", "name"),
			input: dto.StartDeploymentRequest{
				Revision: &revision,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, customErr)
			},
			expectedErr: customErr,
		},
		{
			name:   "not space repo",
			repoID: types.NewRepoID(enums.RepoType_Models, "namespace", "name"),
			input: dto.StartDeploymentRequest{
				Revision: &revision,
			},
			mockFn:      func(d *dependencies) {},
			expectedErr: usecase.ErrNotSpaceRepo,
		},
		{
			name:   "no hardware",
			repoID: types.NewRepoID(enums.RepoType_Spaces, "namespace", "name"),
			input: dto.StartDeploymentRequest{
				Revision: &revision,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					Hardware: nil,
				}, nil)
			},
			expectedErr: usecase.ErrNoHardware,
		},
		{
			name:   "getGitlabRepoMetadata should return error if repo.FindUserByID fail",
			repoID: types.NewRepoID(enums.RepoType_Spaces, "namespace", "name"),
			input: dto.StartDeploymentRequest{
				Revision: &revision,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					RefGitRepoID: 1,
					Hardware: &entities.Hardware{
						BaseModel: entities.BaseModel{
							ID: hardwareID,
						},
						Name: "cpu",
						Mem:  1024,
					},
				}, nil)
				fakeClientSet := fake.NewSimpleClientset()
				fakeClientSet.PrependReactor("get", "nodes", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, nil, nil
				})
				d.kubeClient = fakeClientSet
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(nil, customErr).Once()
			},
			expectedErr: customErr,
		},
		{
			name:   "getRepoAndGitlabURL should return error if gitlab.GetProjectURLs fail",
			repoID: types.NewRepoID(enums.RepoType_Spaces, "namespace", "name"),
			input: dto.StartDeploymentRequest{
				Revision: &revision,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					RefGitRepoID: 1,
					Hardware: &entities.Hardware{
						BaseModel: entities.BaseModel{
							ID: hardwareID,
						},
						Name: "cpu",
						Mem:  1024,
					},
				}, nil)
				fakeClientSet := fake.NewSimpleClientset()
				fakeClientSet.PrependReactor("get", "nodes", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, nil, nil
				})
				d.kubeClient = fakeClientSet
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{
					GitlabAccessToken: utils.Ptr("token"),
				}, nil).Once()
				d.gitlab.On("GetProjectURLs", mock.Anything, mock.Anything).Return(nil, customErr).Once()
			},
			expectedErr: customErr,
		},
		{
			name:   "getLatestCommit should return error if gitlab.GetSingleProjectBrancheInfo fail",
			repoID: types.NewRepoID(enums.RepoType_Spaces, "namespace", "name"),
			input: dto.StartDeploymentRequest{
				Revision: &revision,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					RefGitRepoID: 1,
					Hardware: &entities.Hardware{
						BaseModel: entities.BaseModel{
							ID: hardwareID,
						},
						Name: "cpu",
						Mem:  1024,
					},
				}, nil)
				fakeClientSet := fake.NewSimpleClientset()
				fakeClientSet.PrependReactor("get", "nodes", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, nil, nil
				})
				d.kubeClient = fakeClientSet
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{
					GitlabAccessToken: utils.Ptr("token"),
				}, nil).Once()
				d.gitlab.On("GetProjectURLs", mock.Anything, mock.Anything).Return(&gitlab.GetProjectURLsResponse{
					HTTPUrl: "https://example.com",
				}, nil).Once()
				d.config.Gitlab = &configs.GitlabConfig{
					Host: "https://gitlab.com",
				}
				d.gitlab.On("GetSingleProjectBrancheInfo", mock.Anything, mock.Anything).Return(nil, customErr).Once()
			},
			expectedErr: customErr,
		},
		{
			name:   "should return error if repo.Deployment == nil and repo.CreateDeployment fail",
			repoID: types.NewRepoID(enums.RepoType_Spaces, "namespace", "name"),
			input: dto.StartDeploymentRequest{
				Revision: &revision,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					RefGitRepoID: 1,
					Hardware: &entities.Hardware{
						BaseModel: entities.BaseModel{
							ID: hardwareID,
						},
						Name: "cpu",
						Mem:  1024,
					},
				}, nil)
				fakeClientSet := fake.NewSimpleClientset()
				fakeClientSet.PrependReactor("get", "nodes", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, nil, nil
				})
				d.kubeClient = fakeClientSet
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{
					GitlabAccessToken: utils.Ptr("token"),
				}, nil).Once()
				d.gitlab.On("GetProjectURLs", mock.Anything, mock.Anything).Return(&gitlab.GetProjectURLsResponse{
					HTTPUrl: "https://example.com",
				}, nil).Once()
				d.config.Gitlab = &configs.GitlabConfig{
					Host: "https://gitlab.com",
				}
				d.gitlab.On("GetSingleProjectBrancheInfo", mock.Anything, mock.Anything).Return(&gitlab.RepositoryBranchInfo{
					Commit: gitlab.RepositoryCommit{
						ID: "id",
					},
				}, nil).Once()
				d.repo.On("CreateDeployment", mock.Anything, mock.Anything).Return(nil, customErr).Once()
			},
			expectedErr: customErr,
		},
		{
			name:   "transaction should fail if transaction fail",
			repoID: types.NewRepoID(enums.RepoType_Spaces, "namespace", "name"),
			input: dto.StartDeploymentRequest{
				Revision: &revision,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					RefGitRepoID: 1,
					Hardware: &entities.Hardware{
						BaseModel: entities.BaseModel{
							ID: hardwareID,
						},
						Name: "cpu",
						Mem:  1024,
					},
				}, nil)
				fakeClientSet := fake.NewSimpleClientset()
				fakeClientSet.PrependReactor("get", "nodes", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, nil, nil
				})
				d.kubeClient = fakeClientSet
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{
					GitlabAccessToken: utils.Ptr("token"),
				}, nil).Once()
				d.gitlab.On("GetProjectURLs", mock.Anything, mock.Anything).Return(&gitlab.GetProjectURLsResponse{
					HTTPUrl: "https://example.com",
				}, nil).Once()
				d.config.Gitlab = &configs.GitlabConfig{
					Host: "https://gitlab.com",
				}
				d.gitlab.On("GetSingleProjectBrancheInfo", mock.Anything, mock.Anything).Return(&gitlab.RepositoryBranchInfo{
					Commit: gitlab.RepositoryCommit{
						ID: "id",
					},
				}, nil).Once()
				d.repo.On("CreateDeployment", mock.Anything, mock.Anything).Return(&entities.Deployment{}, nil).Once()
				d.repo.On("Transaction", mock.Anything, mock.Anything).Return(customErr)
			},
			expectedErr: customErr,
		},
		{
			name:   "should pass",
			repoID: types.NewRepoID(enums.RepoType_Spaces, "namespace", "name"),
			input: dto.StartDeploymentRequest{
				Revision: &revision,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					RefGitRepoID: 1,
					Hardware: &entities.Hardware{
						BaseModel: entities.BaseModel{
							ID: hardwareID,
						},
						Name: "cpu",
						Mem:  1024,
					},
				}, nil)
				fakeClientSet := fake.NewSimpleClientset()
				fakeClientSet.PrependReactor("get", "nodes", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, nil, nil
				})
				d.kubeClient = fakeClientSet
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{
					GitlabAccessToken: utils.Ptr("token"),
				}, nil).Once()
				d.gitlab.On("GetProjectURLs", mock.Anything, mock.Anything).Return(&gitlab.GetProjectURLsResponse{
					HTTPUrl: "https://example.com",
				}, nil).Once()
				d.config.Gitlab = &configs.GitlabConfig{
					Host: "https://gitlab.com",
				}
				d.gitlab.On("GetSingleProjectBrancheInfo", mock.Anything, mock.Anything).Return(&gitlab.RepositoryBranchInfo{
					Commit: gitlab.RepositoryCommit{
						ID: "id",
					},
				}, nil).Once()
				d.repo.On("CreateDeployment", mock.Anything, mock.Anything).Return(&entities.Deployment{}, nil).Once()
				d.repo.On("Transaction", mock.Anything, mock.Anything).Return(nil)
			},
			expectedErr: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				config:   &configs.GlobalConfig{},
				repo:     &repository_mocks.MockRepository{},
				gitlab:   &gitlab_mocks.MockGitlabClient{},
				workflow: &workflow_mocks.MockWorkflowUsecase{},
			}
			tt.mockFn(d)

			u := repository.New(d.config, d.repo, d.gitlab, d.workflow, d.kubeClient, nil)
			resp, err := u.StartDeployment(context.Background(), userID, *tt.repoID, tt.input)

			if tt.expectedErr != nil {
				assert.Error(t, err)
				// assert.True(t, errors.Is(err, tt.expectedErr))
				assert.Equal(t, err, tt.expectedErr)
				assert.Nil(t, resp)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, resp)
			}
		})
	}
}

func TestStartDeploymentTx(t *testing.T) {
	// userID := uuid.New()
	repoID := uuid.New()
	hardwareID := uuid.New()
	// resourceName := "test-resource"
	// revision := "main"

	type dependencies struct {
		config     *configs.GlobalConfig
		repo       *repository_mocks.MockRepository
		gitlab     *gitlab_mocks.MockGitlabClient
		workflow   *workflow_mocks.MockWorkflowUsecase
		kubeClient kubernetes.Interface
	}

	type input struct {
		repo               *entities.Repository
		resourceName       string
		gitlabRepoMetadata *repository.GitlabRepoMetadata
		userID             uuid.UUID
		revision           string
		result             *dto.StartDeploymentResponse
	}

	tests := []struct {
		name        string
		input       input
		mockFn      func(d *dependencies)
		expectedErr error
	}{
		{
			name: "should return error if find deployment fails",
			input: input{
				repo: &entities.Repository{
					BaseModel: entities.BaseModel{
						ID: repoID,
					},
				},
				// gitlabRepoMetadata: &repository.GitlabRepoMetadata{
				// 	SourceRepoUrl: "https://example.com",
				// 	SpaceName:     "test-space",
				// 	ImageTag:      "latest",
				// 	LatestCommit:  "commit-id",
				// },
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindDeployment", mock.Anything, mock.Anything, mock.Anything).Return(nil, errors.New("find deployment error"))
			},
			expectedErr: errors.New("find deployment error"),
		},
		{
			name: "should return error if deployment is running",
			input: input{
				repo: &entities.Repository{
					BaseModel: entities.BaseModel{
						ID: repoID,
					},
				},
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindDeployment", mock.Anything, mock.Anything, mock.Anything).Return(&entities.Deployment{
					Status: enums.ArgoWorkflowStatus_Pending,
				}, nil)

				fakeClientSet := fake.NewSimpleClientset()
				fakeClientSet.PrependReactor("delete", "deployments", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, nil, nil
				})
				fakeClientSet.PrependReactor("delete", "services", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, nil, nil
				})
				d.kubeClient = fakeClientSet
			},
			expectedErr: usecase.ErrDeploymentRunning,
		},
		{
			name: "should return error if Argo workflow is running",
			input: input{
				repo: &entities.Repository{
					BaseModel: entities.BaseModel{
						ID: repoID,
					},
				},
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindDeployment", mock.Anything, mock.Anything, mock.Anything).Return(&entities.Deployment{
					Status:       enums.ArgoWorkflowStatus_Failed,
					WorkflowName: "workflow",
				}, nil)
				d.workflow.On("GetWorkflow", mock.Anything, mock.Anything, mock.Anything).Return(&v1alpha1.Workflow{
					Status: v1alpha1.WorkflowStatus{
						Phase: v1alpha1.WorkflowPending,
					},
				}, nil)

				fakeClientSet := fake.NewSimpleClientset()
				fakeClientSet.PrependReactor("delete", "deployments", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, nil, nil
				})
				fakeClientSet.PrependReactor("delete", "services", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, nil, nil
				})
				d.kubeClient = fakeClientSet
			},
			expectedErr: usecase.ErrDeploymentRunning,
		},
		{
			name: "should return error if cannot find deployment",
			input: input{
				repo: &entities.Repository{
					BaseModel: entities.BaseModel{
						ID: repoID,
					},
				},
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindDeployment", mock.Anything, mock.Anything, mock.Anything).Return(nil, nil)

				fakeClientSet := fake.NewSimpleClientset()
				fakeClientSet.PrependReactor("delete", "deployments", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, nil, nil
				})
				fakeClientSet.PrependReactor("delete", "services", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, nil, nil
				})
				d.kubeClient = fakeClientSet
			},
			expectedErr: usecase.ErrNoDeployment,
		},
		{
			name: "should return error if GetEnv fail",
			input: input{
				repo: &entities.Repository{
					BaseModel: entities.BaseModel{
						ID: repoID,
					},
				},
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindDeployment", mock.Anything, mock.Anything, mock.Anything).Return(&entities.Deployment{
					Status: enums.ArgoWorkflowStatus_NotRunning,
				}, nil)
				// d.workflow.On("GetWorkflow", mock.Anything, mock.Anything, mock.Anything).Return(&v1alpha1.Workflow{
				// 	Status: v1alpha1.WorkflowStatus{
				// 		Phase: v1alpha1.WorkflowPending,
				// 	},
				// }, nil)
				d.repo.On("GetEnv", mock.Anything, mock.Anything, mock.Anything).Return(nil, errors.New("error"))

				fakeClientSet := fake.NewSimpleClientset()
				fakeClientSet.PrependReactor("delete", "deployments", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, nil, nil
				})
				fakeClientSet.PrependReactor("delete", "services", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, nil, nil
				})
				d.kubeClient = fakeClientSet
			},
			expectedErr: usecase.ErrInternal,
		},
		{
			name: "should return error if get k8s ConfigMaps fail",
			input: input{
				repo: &entities.Repository{
					BaseModel: entities.BaseModel{
						ID: repoID,
					},
				},
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindDeployment", mock.Anything, mock.Anything, mock.Anything).Return(&entities.Deployment{
					Status: enums.ArgoWorkflowStatus_NotRunning,
				}, nil)
				d.repo.On("GetEnv", mock.Anything, mock.Anything, mock.Anything).Return(nil, nil)

				fakeClientSet := fake.NewSimpleClientset()
				fakeClientSet.PrependReactor("get", "configmaps", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, nil, errors.New("get configmap error")
				})
				d.kubeClient = fakeClientSet
			},
			expectedErr: errors.New("get configmap error"),
		},
		{
			name: "should return error if cannot ConfigMaps not found and create k8s ConfigMaps fail",
			input: input{
				repo: &entities.Repository{
					BaseModel: entities.BaseModel{
						ID: repoID,
					},
				},
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindDeployment", mock.Anything, mock.Anything, mock.Anything).Return(&entities.Deployment{
					Status: enums.ArgoWorkflowStatus_NotRunning,
				}, nil)
				d.repo.On("GetEnv", mock.Anything, mock.Anything, mock.Anything).Return(nil, nil)

				fakeClientSet := fake.NewSimpleClientset()
				fakeClientSet.PrependReactor("get", "configmaps", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, nil, errors.New("not found")
				})
				fakeClientSet.PrependReactor("create", "configmaps", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, nil, errors.New("create configmap error")
				})
				d.kubeClient = fakeClientSet
			},
			expectedErr: errors.New("create configmap error"),
		},
		{
			name: "should return error if update k8s ConfigMaps fail",
			input: input{
				repo: &entities.Repository{
					BaseModel: entities.BaseModel{
						ID: repoID,
					},
				},
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindDeployment", mock.Anything, mock.Anything, mock.Anything).Return(&entities.Deployment{
					Status: enums.ArgoWorkflowStatus_NotRunning,
				}, nil)
				d.repo.On("GetEnv", mock.Anything, mock.Anything, mock.Anything).Return(nil, nil)

				fakeClientSet := fake.NewSimpleClientset()
				fakeClientSet.PrependReactor("get", "configmaps", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, &corev1.ConfigMap{}, nil
				})
				fakeClientSet.PrependReactor("update", "configmaps", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, nil, errors.New("update configmap error")
				})
				d.kubeClient = fakeClientSet
			},
			expectedErr: errors.New("update configmap error"),
		},
		{
			name: "should return error if apply k8s service fail",
			input: input{
				repo: &entities.Repository{
					BaseModel: entities.BaseModel{
						ID: repoID,
					},
				},
				resourceName: "aaa",
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindDeployment", mock.Anything, mock.Anything, mock.Anything).Return(&entities.Deployment{
					Status: enums.ArgoWorkflowStatus_NotRunning,
				}, nil)
				d.repo.On("GetEnv", mock.Anything, mock.Anything, mock.Anything).Return(nil, nil)

				fakeClientSet := fake.NewSimpleClientset(&corev1.Service{
					ObjectMeta: metav1.ObjectMeta{
						Name: "aaa",
					},
				})
				fakeClientSet.PrependReactor("get", "configmaps", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, &corev1.ConfigMap{}, nil
				})
				fakeClientSet.PrependReactor("update", "configmaps", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, nil, nil
				})
				fakeClientSet.PrependReactor("patch", "services", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, nil, errors.New("apply service error")
				})
				d.kubeClient = fakeClientSet
			},
			expectedErr: usecase.ErrApplySpaceServiceResource,
		},
		{
			name: "should return error if get k8s ingress fail",
			input: input{
				repo: &entities.Repository{
					BaseModel: entities.BaseModel{
						ID: repoID,
					},
				},
				resourceName: "aaa",
				gitlabRepoMetadata: &repository.GitlabRepoMetadata{
					SourceRepoUrl: "https://example.com",
					SpaceName:     "test-space",
					ImageTag:      "latest",
					LatestCommit:  "commit-id",
				},
			},
			mockFn: func(d *dependencies) {
				d.config.Space = &configs.SpaceConfig{
					SpaceDomain: "space.volvo.com",
				}
				d.repo.On("FindDeployment", mock.Anything, mock.Anything, mock.Anything).Return(&entities.Deployment{
					Status: enums.ArgoWorkflowStatus_NotRunning,
				}, nil)
				d.repo.On("GetEnv", mock.Anything, mock.Anything, mock.Anything).Return(nil, nil)

				fakeClientSet := fake.NewSimpleClientset(&corev1.Service{
					ObjectMeta: metav1.ObjectMeta{
						Name: "aaa",
					},
				})
				fakeClientSet.PrependReactor("get", "configmaps", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, &corev1.ConfigMap{}, nil
				})
				fakeClientSet.PrependReactor("update", "configmaps", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, nil, nil
				})
				fakeClientSet.PrependReactor("patch", "services", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, nil, nil
				})
				fakeClientSet.PrependReactor("get", "ingresses", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, nil, errors.New("get ingress error")
				})
				d.kubeClient = fakeClientSet
			},
			expectedErr: usecase.ErrGetSpaceIngressResource,
		},
		{
			name: "should return error if get k8s ingress not found and create new ingress fail",
			input: input{
				repo: &entities.Repository{
					BaseModel: entities.BaseModel{
						ID: repoID,
					},
				},
				resourceName: "aaa",
				gitlabRepoMetadata: &repository.GitlabRepoMetadata{
					SourceRepoUrl: "https://example.com",
					SpaceName:     "test-space",
					ImageTag:      "latest",
					LatestCommit:  "commit-id",
				},
			},
			mockFn: func(d *dependencies) {
				d.config.Space = &configs.SpaceConfig{
					SpaceDomain: "space.volvo.com",
				}
				d.repo.On("FindDeployment", mock.Anything, mock.Anything, mock.Anything).Return(&entities.Deployment{
					Status: enums.ArgoWorkflowStatus_NotRunning,
				}, nil)
				d.repo.On("GetEnv", mock.Anything, mock.Anything, mock.Anything).Return(nil, nil)

				fakeClientSet := fake.NewSimpleClientset(&corev1.Service{
					ObjectMeta: metav1.ObjectMeta{
						Name: "aaa",
					},
				})
				fakeClientSet.PrependReactor("get", "configmaps", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, &corev1.ConfigMap{}, nil
				})
				fakeClientSet.PrependReactor("update", "configmaps", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, nil, nil
				})
				fakeClientSet.PrependReactor("patch", "services", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, nil, nil
				})
				fakeClientSet.PrependReactor("get", "ingresses", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, nil, kubeerrors.NewNotFound(schema.GroupResource{
						Group:    "",
						Resource: "",
					}, "")
				})
				fakeClientSet.PrependReactor("create", "ingresses", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, nil, errors.New("create ingress fail")
				})
				d.kubeClient = fakeClientSet
			},
			expectedErr: usecase.ErrCreateSpaceIngressResource,
		},
		{
			name: "should return error if workflow.CreateSpaceDeploymentWorkflow fail",
			input: input{
				repo: &entities.Repository{
					BaseModel: entities.BaseModel{
						ID: repoID,
					},
					Hardware: &entities.Hardware{
						GPUMem: nil,
					},
				},
				resourceName: "aaa",
				gitlabRepoMetadata: &repository.GitlabRepoMetadata{
					SourceRepoUrl: "https://example.com",
					SpaceName:     "test-space",
					ImageTag:      "latest",
					LatestCommit:  "commit-id",
				},
			},
			mockFn: func(d *dependencies) {
				d.config.Space = &configs.SpaceConfig{
					SpaceDomain: "space.volvo.com",
				}
				d.repo.On("FindDeployment", mock.Anything, mock.Anything, mock.Anything).Return(&entities.Deployment{
					Status: enums.ArgoWorkflowStatus_NotRunning,
				}, nil)
				d.repo.On("GetEnv", mock.Anything, mock.Anything, mock.Anything).Return(nil, nil)

				fakeClientSet := fake.NewSimpleClientset(&corev1.Service{
					ObjectMeta: metav1.ObjectMeta{
						Name: "aaa",
					},
				})
				fakeClientSet.PrependReactor("get", "configmaps", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, &corev1.ConfigMap{}, nil
				})
				fakeClientSet.PrependReactor("update", "configmaps", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, nil, nil
				})
				fakeClientSet.PrependReactor("patch", "services", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, nil, nil
				})
				fakeClientSet.PrependReactor("get", "ingresses", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, nil, kubeerrors.NewNotFound(schema.GroupResource{
						Group:    "",
						Resource: "",
					}, "")
				})
				fakeClientSet.PrependReactor("create", "ingresses", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, nil, nil
				})
				d.kubeClient = fakeClientSet

				d.workflow.On("CreateSpaceDeploymentWorkflow", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectedErr: errors.New("error"),
		},
		{
			name: "should return error if repo.UpsertDeployment fail",
			input: input{
				repo: &entities.Repository{
					BaseModel: entities.BaseModel{
						ID: repoID,
					},
					Hardware: &entities.Hardware{
						BaseModel: entities.BaseModel{
							ID: hardwareID,
						},
						GPUMem: nil,
					},
				},
				resourceName: "aaa",
				gitlabRepoMetadata: &repository.GitlabRepoMetadata{
					SourceRepoUrl: "https://example.com",
					SpaceName:     "test-space",
					ImageTag:      "latest",
					LatestCommit:  "commit-id",
				},
			},
			mockFn: func(d *dependencies) {
				d.config.Space = &configs.SpaceConfig{
					SpaceDomain: "space.volvo.com",
				}
				d.repo.On("FindDeployment", mock.Anything, mock.Anything, mock.Anything).Return(&entities.Deployment{
					Status: enums.ArgoWorkflowStatus_NotRunning,
				}, nil)
				d.repo.On("GetEnv", mock.Anything, mock.Anything, mock.Anything).Return(nil, nil)

				fakeClientSet := fake.NewSimpleClientset(&corev1.Service{
					ObjectMeta: metav1.ObjectMeta{
						Name: "aaa",
					},
				})
				fakeClientSet.PrependReactor("get", "configmaps", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, &corev1.ConfigMap{}, nil
				})
				fakeClientSet.PrependReactor("update", "configmaps", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, nil, nil
				})
				fakeClientSet.PrependReactor("patch", "services", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, nil, nil
				})
				fakeClientSet.PrependReactor("get", "ingresses", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, nil, kubeerrors.NewNotFound(schema.GroupResource{
						Group:    "",
						Resource: "",
					}, "")
				})
				fakeClientSet.PrependReactor("create", "ingresses", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, nil, nil
				})
				d.kubeClient = fakeClientSet

				d.workflow.On("CreateSpaceDeploymentWorkflow", mock.Anything, mock.Anything).Return(utils.Ptr("workflow_name"), nil)
				d.repo.On("UpsertDeployment", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectedErr: errors.New("error"),
		},
		{
			name: "should pass",
			input: input{
				repo: &entities.Repository{
					BaseModel: entities.BaseModel{
						ID: repoID,
					},
					Hardware: &entities.Hardware{
						BaseModel: entities.BaseModel{
							ID: hardwareID,
						},
						GPUMem: nil,
					},
				},
				resourceName: "aaa",
				gitlabRepoMetadata: &repository.GitlabRepoMetadata{
					SourceRepoUrl: "https://example.com",
					SpaceName:     "test-space",
					ImageTag:      "latest",
					LatestCommit:  "commit-id",
				},
				result: &dto.StartDeploymentResponse{},
			},
			mockFn: func(d *dependencies) {
				d.config.Space = &configs.SpaceConfig{
					SpaceDomain: "space.volvo.com",
				}
				d.repo.On("FindDeployment", mock.Anything, mock.Anything, mock.Anything).Return(&entities.Deployment{
					Status: enums.ArgoWorkflowStatus_NotRunning,
				}, nil)
				d.repo.On("GetEnv", mock.Anything, mock.Anything, mock.Anything).Return(nil, nil)

				fakeClientSet := fake.NewSimpleClientset(&corev1.Service{
					ObjectMeta: metav1.ObjectMeta{
						Name: "aaa",
					},
				})
				fakeClientSet.PrependReactor("get", "configmaps", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, &corev1.ConfigMap{}, nil
				})
				fakeClientSet.PrependReactor("update", "configmaps", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, nil, nil
				})
				fakeClientSet.PrependReactor("patch", "services", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, nil, nil
				})
				fakeClientSet.PrependReactor("get", "ingresses", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, nil, kubeerrors.NewNotFound(schema.GroupResource{
						Group:    "",
						Resource: "",
					}, "")
				})
				fakeClientSet.PrependReactor("create", "ingresses", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, nil, nil
				})
				d.kubeClient = fakeClientSet

				d.workflow.On("CreateSpaceDeploymentWorkflow", mock.Anything, mock.Anything).Return(utils.Ptr("workflow_name"), nil)
				d.repo.On("UpsertDeployment", mock.Anything, mock.Anything).Return(&entities.Deployment{}, nil)
			},
			expectedErr: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			d := &dependencies{
				config:   &configs.GlobalConfig{},
				repo:     &repository_mocks.MockRepository{},
				gitlab:   &gitlab_mocks.MockGitlabClient{},
				workflow: &workflow_mocks.MockWorkflowUsecase{},
			}
			tt.mockFn(d)

			u := repository.New(d.config, d.repo, d.gitlab, d.workflow, d.kubeClient, nil)
			err := u.StartDeploymentTx(context.Background(), tt.input.repo, tt.input.resourceName, tt.input.gitlabRepoMetadata, tt.input.userID, tt.input.revision, tt.input.result)

			if tt.expectedErr != nil {
				assert.EqualError(t, err, tt.expectedErr.Error())
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestStopDeployment(t *testing.T) {
	type dependencies struct {
		config     *configs.GlobalConfig
		repo       *repository_mocks.MockRepository
		gitlab     *gitlab_mocks.MockGitlabClient
		workflow   *workflow_mocks.MockWorkflowUsecase
		kubeClient kubernetes.Interface
	}

	tests := []struct {
		name             string
		input            dto.StopDeploymentRequest
		mockFn           func(d *dependencies)
		expectedResponse string
		expectedErr      error
	}{
		{
			name: "should return usecase.ErrNotSpaceRepo",
			input: dto.StopDeploymentRequest{
				RepoID: types.NewRepoID(enums.RepoType_Datasets, "namespace", "name"),
			},
			mockFn:      func(d *dependencies) {},
			expectedErr: usecase.ErrNotSpaceRepo,
		},
		{
			name: "repository not found",
			input: dto.StopDeploymentRequest{
				RepoID: types.NewRepoID(enums.RepoType_Spaces, "namespace", "name"),
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, usecase.ErrRepositoryNotFound)
			},
			expectedErr: usecase.ErrRepositoryNotFound,
		},
		{
			name: "FindRepository return error",
			input: dto.StopDeploymentRequest{
				RepoID: types.NewRepoID(enums.RepoType_Spaces, "namespace", "name"),
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectedErr: errors.New("error"),
		},
		{
			name: "should return no deployment",
			input: dto.StopDeploymentRequest{
				RepoID: types.NewRepoID(enums.RepoType_Spaces, "namespace", "name"),
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					Deployment: nil,
				}, nil)
			},
			expectedErr: usecase.ErrNoDeployment,
		},
		{
			name: "should return usecase.ErrTerminateDeploymentNotRunning",
			input: dto.StopDeploymentRequest{
				RepoID: types.NewRepoID(enums.RepoType_Spaces, "namespace", "name"),
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					Deployment: &entities.Deployment{
						Status:       enums.ArgoWorkflowStatus_Terminated,
						WorkflowName: "workflow_name",
					},
				}, nil)
			},
			expectedErr: usecase.ErrTerminateDeploymentNotRunning,
		},
		{
			name: "should return error if TerminateWorkflow fail",
			input: dto.StopDeploymentRequest{
				RepoID: types.NewRepoID(enums.RepoType_Spaces, "namespace", "name"),
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					Deployment: &entities.Deployment{
						Status:       enums.ArgoWorkflowStatus_Running,
						WorkflowName: "workflow_name",
					},
				}, nil).Once()
				d.workflow.On("TerminateWorkflow", mock.Anything, "workflow_name", mock.Anything).Return(errors.New("error")).Once()
			},
			expectedErr: errors.New("error"),
		},
		{
			name: "should return error if delete k8s deployments in deleteResources fail",
			input: dto.StopDeploymentRequest{
				RepoID: types.NewRepoID(enums.RepoType_Spaces, "namespace", "name"),
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					Deployment: &entities.Deployment{
						Status:       enums.ArgoWorkflowStatus_Running,
						WorkflowName: "workflow_name",
					},
				}, nil).Once()
				d.workflow.On("TerminateWorkflow", mock.Anything, "workflow_name", mock.Anything).Return(errors.New("cannot shutdown a completed workflow")).Once()

				fakeClientSet := fake.NewSimpleClientset()
				fakeClientSet.PrependReactor("delete", "deployments", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, nil, errors.New("delete deployments")
				})
				d.kubeClient = fakeClientSet
			},
			expectedErr: errors.New("delete deployments"),
		},
		{
			name: "should return error if delete k8s services in deleteResources fail",
			input: dto.StopDeploymentRequest{
				RepoID: types.NewRepoID(enums.RepoType_Spaces, "namespace", "name"),
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					Deployment: &entities.Deployment{
						Status:       enums.ArgoWorkflowStatus_Running,
						WorkflowName: "workflow_name",
					},
				}, nil).Once()
				d.workflow.On("TerminateWorkflow", mock.Anything, "workflow_name", mock.Anything).Return(errors.New("cannot shutdown a completed workflow")).Once()

				fakeClientSet := fake.NewSimpleClientset()
				fakeClientSet.PrependReactor("delete", "deployments", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, nil, nil
				})
				fakeClientSet.PrependReactor("delete", "services", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, nil, errors.New("delete services")
				})
				// fakeClientSet.PrependReactor("delete", "ingresses", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
				// 	return true, nil, nil
				// })
				d.kubeClient = fakeClientSet
			},
			expectedErr: errors.New("delete services"),
		},
		// {
		// 	name: "should return error if delete k8s ingresses in deleteResources fail",
		// 	input: dto.StopDeploymentRequest{
		// 		RepoID: types.NewRepoID(enums.RepoType_Spaces, "namespace", "name"),
		// 	},
		// 	mockFn: func(d *dependencies) {
		// 		d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
		// 			Deployment: &entities.Deployment{
		// 				Status:       enums.ArgoWorkflowStatus_Running,
		// 				WorkflowName: "workflow_name",
		// 			},
		// 		}, nil).Once()
		// 		d.workflow.On("TerminateWorkflow", mock.Anything, "workflow_name", mock.Anything).Return(errors.New("cannot shutdown a completed workflow")).Once()
		//
		// 		fakeClientSet := fake.NewSimpleClientset()
		// 		fakeClientSet.PrependReactor("delete", "deployments", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
		// 			return true, nil, nil
		// 		})
		// 		fakeClientSet.PrependReactor("delete", "services", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
		// 			return true, nil, nil
		// 		})
		// 		fakeClientSet.PrependReactor("delete", "ingresses", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
		// 			return true, nil, errors.New("delete ingresses")
		// 		})
		// 		d.kubeClient = fakeClientSet
		// 	},
		// 	expectedErr: errors.New("delete services"),
		// },
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				config:   &configs.GlobalConfig{},
				repo:     &repository_mocks.MockRepository{},
				gitlab:   &gitlab_mocks.MockGitlabClient{},
				workflow: &workflow_mocks.MockWorkflowUsecase{},
			}
			tt.mockFn(d)

			u := repository.New(d.config, d.repo, d.gitlab, d.workflow, d.kubeClient, nil)
			err := u.StopDeployment(context.Background(), tt.input)

			if tt.expectedErr != nil {
				assert.Error(t, err)
				assert.Equal(t, err, tt.expectedErr)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestRestartDeployment(t *testing.T) {
	type dependencies struct {
		config     *configs.GlobalConfig
		repo       *repository_mocks.MockRepository
		gitlab     *gitlab_mocks.MockGitlabClient
		workflow   *workflow_mocks.MockWorkflowUsecase
		kubeClient kubernetes.Interface
	}

	tests := []struct {
		name        string
		input       dto.RestartDeploymentRequest
		mockFn      func(d *dependencies)
		expectedErr error
	}{
		{
			name: "should return usecase.ErrNotSpaceRepo",
			input: dto.RestartDeploymentRequest{
				RepoID: *types.NewRepoID(enums.RepoType_Datasets, "namespace", "name"),
			},
			mockFn:      func(d *dependencies) {},
			expectedErr: usecase.ErrNotSpaceRepo,
		},
		{
			name: "repository not found",
			input: dto.RestartDeploymentRequest{
				RepoID: *types.NewRepoID(enums.RepoType_Spaces, "namespace", "name"),
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, usecase.ErrRepositoryNotFound)
			},
			expectedErr: usecase.ErrRepositoryNotFound,
		},
		{
			name: "FindRepository return error",
			input: dto.RestartDeploymentRequest{
				RepoID: *types.NewRepoID(enums.RepoType_Spaces, "namespace", "name"),
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectedErr: errors.New("error"),
		},
		{
			name: "should return no deployment",
			input: dto.RestartDeploymentRequest{
				RepoID: *types.NewRepoID(enums.RepoType_Spaces, "namespace", "name"),
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					Deployment: nil,
				}, nil)
			},
			expectedErr: usecase.ErrNoDeployment,
		},
		{
			name: "should return usecase.ErrDeploymentNotSucceeded",
			input: dto.RestartDeploymentRequest{
				RepoID: *types.NewRepoID(enums.RepoType_Spaces, "namespace", "name"),
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					Deployment: &entities.Deployment{
						Status: enums.ArgoWorkflowStatus_Running,
					},
				}, nil)
			},
			expectedErr: usecase.ErrDeploymentNotSucceeded,
		},
		{
			name: "should return error if upsertConfigMap fail",
			input: dto.RestartDeploymentRequest{
				RepoID: *types.NewRepoID(enums.RepoType_Spaces, "namespace", "name"),
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					BaseModel: entities.BaseModel{
						ID: uuid.New(),
					},
					Deployment: &entities.Deployment{
						Status: enums.ArgoWorkflowStatus_Succeeded,
					},
				}, nil)
				d.repo.On("GetEnv", mock.Anything, mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectedErr: usecase.ErrInternal,
		},
		{
			name: "should return error if restartSpaceDeployment fail",
			input: dto.RestartDeploymentRequest{
				RepoID: *types.NewRepoID(enums.RepoType_Spaces, "namespace", "name"),
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					BaseModel: entities.BaseModel{
						ID: uuid.New(),
					},
					Deployment: &entities.Deployment{
						Status: enums.ArgoWorkflowStatus_Succeeded,
					},
					Hardware: &entities.Hardware{
						Name: "cpu",
					},
				}, nil)
				d.repo.On("GetEnv", mock.Anything, mock.Anything, mock.Anything).Return(nil, nil)

				fakeClientSet := fake.NewSimpleClientset()
				fakeClientSet.PrependReactor("get", "deployments", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, nil, errors.New("get deployment error")
				})
				d.kubeClient = fakeClientSet
			},
			expectedErr: errors.New("get deployment error"),
		},
		{
			name: "should return error if deleteRunningPods fail",
			input: dto.RestartDeploymentRequest{
				RepoID: *types.NewRepoID(enums.RepoType_Spaces, "namespace", "name"),
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					BaseModel: entities.BaseModel{
						ID: uuid.New(),
					},
					Deployment: &entities.Deployment{
						Status: enums.ArgoWorkflowStatus_Succeeded,
					},
					Hardware: &entities.Hardware{
						Name: "gpu",
					},
				}, nil)
				d.repo.On("GetEnv", mock.Anything, mock.Anything, mock.Anything).Return(nil, nil)

				fakeClientSet := fake.NewSimpleClientset()
				fakeClientSet.PrependReactor("get", "deployments", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, nil, errors.New("get deployment error")
				})

				k8sPodList := &corev1.PodList{Items: []corev1.Pod{}}
				fakeClientSet.PrependReactor("list", "pods", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, k8sPodList, errors.New("list pods error")
				})
				d.kubeClient = fakeClientSet
			},
			expectedErr: errors.New("list pods error"),
		},
		{
			name: "should pass",
			input: dto.RestartDeploymentRequest{
				RepoID: *types.NewRepoID(enums.RepoType_Spaces, "namespace", "name"),
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					BaseModel: entities.BaseModel{
						ID: uuid.New(),
					},
					Deployment: &entities.Deployment{
						Status: enums.ArgoWorkflowStatus_Succeeded,
					},
					Hardware: &entities.Hardware{
						Name: "cpu",
					},
				}, nil)
				d.repo.On("GetEnv", mock.Anything, mock.Anything, mock.Anything).Return(nil, nil)

				fakeClientSet := fake.NewSimpleClientset(&appsv1.Deployment{
					ObjectMeta: metav1.ObjectMeta{
						Name: "test-deployment",
					},
					Spec: appsv1.DeploymentSpec{
						Template: corev1.PodTemplateSpec{
							ObjectMeta: metav1.ObjectMeta{
								Annotations: make(map[string]string),
							},
						},
					},
				})
				fakeClientSet.PrependReactor("get", "deployments", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, &appsv1.Deployment{
						ObjectMeta: metav1.ObjectMeta{
							Name: "test-deployment",
						},
						Spec: appsv1.DeploymentSpec{
							Template: corev1.PodTemplateSpec{
								ObjectMeta: metav1.ObjectMeta{
									Annotations: make(map[string]string),
								},
							},
						},
					}, nil
				})
				fakeClientSet.PrependReactor("update", "deployments", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, nil, nil
				})
				d.kubeClient = fakeClientSet
			},
			expectedErr: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				config:   &configs.GlobalConfig{},
				repo:     &repository_mocks.MockRepository{},
				gitlab:   &gitlab_mocks.MockGitlabClient{},
				workflow: &workflow_mocks.MockWorkflowUsecase{},
			}
			tt.mockFn(d)

			u := repository.New(d.config, d.repo, d.gitlab, d.workflow, d.kubeClient, nil)
			err := u.RestartDeployment(context.Background(), tt.input)

			if tt.expectedErr != nil {
				assert.Error(t, err)
				assert.Equal(t, err, tt.expectedErr)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestListDeployment(t *testing.T) {
	repoUUID, _ := uuid.Parse("c0df7222-3748-40d8-a601-fb7c684f6d17")
	// mockUserUUID, _ := uuid.Parse("ad044f6b-c37f-4068-9662-9e34d3b6fb1b")

	type dependencies struct {
		config     *configs.GlobalConfig
		repo       *repository_mocks.MockRepository
		gitlab     *gitlab_mocks.MockGitlabClient
		workflow   *workflow_mocks.MockWorkflowUsecase
		kubeClient kubernetes.Interface
	}

	tests := []struct {
		name             string
		input            dto.ListDeploymentRequest
		mockFn           func(d *dependencies)
		expectedResponse *dto.ListDeploymentResponse
		expectedErr      error
	}{
		{
			name: "should return error when CountDeployment fail",
			input: dto.ListDeploymentRequest{
				RepoID: repoUUID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("CountDeployment", mock.Anything, mock.Anything).Return(int64(0), errors.New("error"))
			},
			expectedErr: errors.New("error"),
		},
		{
			name: "should return error when ListDeployment fail",
			input: dto.ListDeploymentRequest{
				RepoID: repoUUID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("CountDeployment", mock.Anything, mock.Anything).Return(int64(10), nil)
				d.repo.On("ListDeployment", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectedErr: errors.New("error"),
		},
		{
			name: "should pass",
			input: dto.ListDeploymentRequest{
				RepoID: repoUUID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("CountDeployment", mock.Anything, mock.Anything).Return(int64(10), nil)
				deploymentResp := []entities.Deployment{
					{
						Repo: &entities.Repository{
							Name: "repo_name",
							User: &entities.User{
								Username: "username",
							},
							Type: enums.RepoType_Spaces,
							Hardware: &entities.Hardware{
								CPU:      1,
								Mem:      1,
								GPUModel: nil,
								GPUMem:   nil,
							},
						},
						User:   &entities.User{},
						Status: enums.ArgoWorkflowStatus_NotRunning,
					},
					{
						Repo: &entities.Repository{
							Name: "repo_name",
							Org: &entities.Organization{
								PathName: "org_name",
							},
							Type: enums.RepoType_Spaces,
							Hardware: &entities.Hardware{
								CPU:      1,
								Mem:      1,
								GPUModel: utils.Ptr("gpu_model"),
								GPUMem:   utils.Ptr(1),
							},
						},
						User:   &entities.User{},
						Status: enums.ArgoWorkflowStatus_NotRunning,
					},
				}
				d.repo.On("ListDeployment", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(deploymentResp, nil)
			},
			expectedResponse: &dto.ListDeploymentResponse{
				Data: &[]dto.Deployment{
					{
						RepoID: *types.NewRepoID(enums.RepoType_Spaces, "username", "repo_name"),
						Hardware: dto.Hardware{
							Mem: types.HardwareMem{
								Amount: 1,
								Unit:   enums.MemoryUnit_MiB,
							},
							CPU:      1,
							GPUModel: "",
							GPUMem:   nil,
						},
						User: &dto.User{
							ID: uuid.Nil.String(),
						},
						Status: dto.DeploymentStatus{
							Status: "Not Running",
						},
					},
					{
						RepoID: *types.NewRepoID(enums.RepoType_Spaces, "org_name", "repo_name"),
						Hardware: dto.Hardware{
							Mem: types.HardwareMem{
								Amount: 1,
								Unit:   enums.MemoryUnit_MiB,
							},
							CPU:      1,
							GPUModel: "gpu_model",
							GPUMem: &types.HardwareMem{
								Amount: 1,
								Unit:   enums.MemoryUnit_MiB,
							},
							Name: "gpu_model",
						},
						User: &dto.User{
							ID: uuid.Nil.String(),
						},
						Status: dto.DeploymentStatus{
							Status: "Not Running",
						},
					},
				},
				Pagination: &dto.Pagination{
					Total:    10,
					PageNo:   0,
					PageSize: 0,
				},
			},
			expectedErr: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				config:   &configs.GlobalConfig{},
				repo:     &repository_mocks.MockRepository{},
				gitlab:   &gitlab_mocks.MockGitlabClient{},
				workflow: &workflow_mocks.MockWorkflowUsecase{},
			}
			tt.mockFn(d)

			u := repository.New(d.config, d.repo, d.gitlab, d.workflow, d.kubeClient, nil)
			resp, err := u.ListDeployment(context.Background(), tt.input)

			if tt.expectedErr != nil {
				assert.Error(t, err)
				assert.Equal(t, err, tt.expectedErr)
			} else {
				assert.Equal(t, tt.expectedResponse, resp)
				assert.NoError(t, err)
			}
		})
	}
}
