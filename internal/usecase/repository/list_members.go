package repository

import (
	"context"
	"errors"

	"go.opentelemetry.io/otel/codes"
	"gorm.io/gorm"

	"api-server/internal/dto"
	repository "api-server/internal/repositories"
	"api-server/internal/types"
	"api-server/internal/usecase"
	"api-server/pkg/oteltrace"
)

// ListRepoMembers implements the RepositoryUsecase interface for retrieving repository members.
// It fetches a list of users who are members of the repository with their roles and permissions.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: Input data containing repository ID and query parameters
//
// Returns:
//   - *dto.ListRepositoryMembersOutput: List of member information with pagination
//   - error: Any error that occurred during retrieval
func (u *impl) ListRepoMembers(ctx context.Context, input dto.ListRepositoryMembersInput) (*dto.ListRepositoryMembersOutput, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.ListRepoMembers")
	defer span.End()

	repo, err := u.repo.FindRepository(ctx, repository.RepositoryFilter{
		Type:      input.RepoID.RepoType(),
		Namespace: input.RepoID.Namespace(),
		Name:      input.RepoID.RepoName(),
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find repository")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, usecase.ErrRepositoryNotFound
		}
		return nil, err
	}

	query := repository.ListRepositoryMembersQuery{
		ID:            repo.ID,
		Keyword:       input.Keyword,
		CurrentUserId: input.CurrentUserId,
	}
	memberNum, err := u.repo.CountRepoMembers(ctx, query)
	if err != nil {
		span.SetStatus(codes.Error, "failed to count repository members")
		span.RecordError(err)
		return nil, err
	}

	pagination := types.Pagination{
		PageNo:   input.Paginate.Page,
		PageSize: input.Paginate.PerPage,
	}
	orderBy := types.OrderBy{input.Paginate.OrderBy: input.Paginate.Sort}
	members, err := u.repo.ListRepoMembers(ctx, pagination, orderBy, query)
	if err != nil {
		span.SetStatus(codes.Error, "failed to list repository members")
		span.RecordError(err)
		return nil, err
	}

	span.AddEvent("list repository members successfully")
	span.SetStatus(codes.Ok, "list repository members successfully")
	return &dto.ListRepositoryMembersOutput{
		Data: &members,
		Pagination: &dto.Pagination{
			Total:    memberNum,
			PageNo:   input.Paginate.Page,
			PageSize: input.Paginate.PerPage,
		},
	}, nil
}
