package repository_test

import (
	"context"
	"errors"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/kubernetes/fake"
	kubetesting "k8s.io/client-go/testing"

	"api-server/configs"
	"api-server/internal/dto"
	"api-server/internal/entities"
	"api-server/internal/enums"
	gitlab_mocks "api-server/internal/gateways/gitlab/mocks"
	repository_mocks "api-server/internal/repositories/mocks"
	"api-server/internal/types"
	"api-server/internal/usecase"
	"api-server/internal/usecase/repository"
	workflow_mocks "api-server/internal/usecase/workflow/mocks"
	"api-server/internal/utils"
)

func TestDeleteRepository(t *testing.T) {
	repoType := enums.RepoType_Models
	namespace := "namespace"
	repoName := "repo-name"
	repoID := *types.NewRepoID(repoType, namespace, repoName)

	uuidInput := uuid.New()
	type dependencies struct {
		config     *configs.GlobalConfig
		repo       *repository_mocks.MockRepository
		gitlab     *gitlab_mocks.MockGitlabClient
		workflow   *workflow_mocks.MockWorkflowUsecase
		kubeClient kubernetes.Interface
	}

	tests := []struct {
		name     string
		ctx      context.Context
		input    dto.DeleteRepositoryInput
		mockFn   func(d *dependencies)
		expError error
	}{
		{
			name: "should return error if FindUserByID fail",
			ctx:  context.TODO(),
			input: dto.DeleteRepositoryInput{
				CurrentUserId: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error if FindRepository fail",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.DeleteRepositoryInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},

			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{Role: enums.UserRole_User}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error if user is not owner return no permission",
			ctx:  context.TODO(),
			input: dto.DeleteRepositoryInput{
				CurrentUserId: uuidInput,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{Role: enums.UserRole_User}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{}, nil)
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything).Return(&entities.RepoMember{Role: enums.RepoRole_Developer}, nil)
			},
			expError: usecase.ErrNoPermission,
		},
		{
			name: "should return error if DeleteRepositoryMemberByRepositoryID fail",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.DeleteRepositoryInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{Role: enums.UserRole_User}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{}, nil)
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything).Return(&entities.RepoMember{Role: enums.RepoRole_Owner}, nil)
				d.repo.On("DeleteRepositoryMemberByRepositoryID", mock.Anything, mock.Anything).Return(errors.New("error"))
				d.repo.On("DeleteDeploymentsByRepoID", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("Transaction", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), uuidInput))
				}).Return(errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error if DeleteDeploymentsByRepoID fail",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.DeleteRepositoryInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{Role: enums.UserRole_User}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{}, nil)
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything).Return(&entities.RepoMember{Role: enums.RepoRole_Owner}, nil)
				d.repo.On("DeleteRepositoryMemberByRepositoryID", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("DeleteDeploymentsByRepoID", mock.Anything, mock.Anything).Return(errors.New("error"))
				d.repo.On("Transaction", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), uuidInput))
				}).Return(errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error if DeleteById fail",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.DeleteRepositoryInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{Role: enums.UserRole_User}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					BaseModel: entities.BaseModel{
						ID: uuidInput,
					},
				}, nil)
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything).Return(&entities.RepoMember{Role: enums.RepoRole_Owner}, nil)
				d.repo.On("DeleteRepositoryMemberByRepositoryID", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("DeleteDeploymentsByRepoID", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("DeleteById", mock.Anything, mock.Anything, uuidInput).Return(errors.New("error"))
				d.repo.On("Transaction", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), uuidInput))
				}).Return(errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error if DeleteProject fail",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.DeleteRepositoryInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{Role: enums.UserRole_User}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					BaseModel: entities.BaseModel{
						ID: uuidInput,
					},
				}, nil)
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything).Return(&entities.RepoMember{Role: enums.RepoRole_Owner}, nil)
				d.repo.On("DeleteRepositoryMemberByRepositoryID", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("DeleteDeploymentsByRepoID", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("DeleteById", mock.Anything, mock.Anything, uuidInput).Return(nil)
				d.repo.On("Transaction", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), uuidInput))
				}).Return(errors.New("error"))
				d.gitlab.On("DeleteProject", mock.Anything, mock.Anything).Return(errors.New("error"))
			},
			expError: errors.New("error"),
		},
		{
			name: "should return error if terminate workflow fails",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.DeleteRepositoryInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{Role: enums.UserRole_User}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					BaseModel: entities.BaseModel{
						ID: uuidInput,
					},
					Deployment: &entities.Deployment{},
				}, nil)
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything).Return(&entities.RepoMember{Role: enums.RepoRole_Owner}, nil)
				d.workflow.On("TerminateWorkflow", mock.Anything, mock.Anything, mock.Anything).Return(usecase.ErrFailedToTerminateArgoWorkflow)

				fakeClientSet := fake.NewSimpleClientset()
				fakeClientSet.PrependReactor("delete", "deployments", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, nil, errors.New("simulated delete failure")
				})
				fakeClientSet.PrependReactor("delete", "services", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, nil, errors.New("simulated delete failure")
				})
				fakeClientSet.PrependReactor("delete", "ingresses", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, nil, errors.New("simulated delete failure")
				})
				d.kubeClient = fakeClientSet

				d.repo.On("Transaction", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), uuidInput))
				}).Return(usecase.ErrFailedToTerminateArgoWorkflow)
			},
			expError: usecase.ErrFailedToTerminateArgoWorkflow,
		},
		{
			name: "should return error if Kubernetes resource deletion fails",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.DeleteRepositoryInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{Role: enums.UserRole_User}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					BaseModel: entities.BaseModel{
						ID: uuidInput,
					},
					Deployment: &entities.Deployment{},
				}, nil)
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything).Return(&entities.RepoMember{Role: enums.RepoRole_Owner}, nil)
				d.workflow.On("TerminateWorkflow", mock.Anything, mock.Anything, mock.Anything).Return(nil)
				fakeClientSet := fake.NewSimpleClientset()
				fakeClientSet.PrependReactor("delete", "deployments", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, nil, errors.New("simulated delete failure")
				})
				fakeClientSet.PrependReactor("delete", "services", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, nil, errors.New("simulated delete failure")
				})
				fakeClientSet.PrependReactor("delete", "ingresses", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, nil, errors.New("simulated delete failure")
				})
				d.kubeClient = fakeClientSet

				d.repo.On("Transaction", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), uuidInput))
				}).Return(errors.New("failed to delete Kubernetes resources"))
			},
			expError: errors.New("failed to delete Kubernetes resources"),
		},
		{
			name: "should success with Kubernetes resource cleanup",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.DeleteRepositoryInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{Role: enums.UserRole_User}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					BaseModel: entities.BaseModel{
						ID: uuidInput,
					},
					Deployment: &entities.Deployment{},
				}, nil)
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything).Return(&entities.RepoMember{Role: enums.RepoRole_Owner}, nil)
				d.workflow.On("TerminateWorkflow", mock.Anything, mock.Anything, mock.Anything).Return(nil)
				fakeClientSet := fake.NewSimpleClientset()
				fakeClientSet.PrependReactor("delete", "deployments", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, nil, nil
				})
				fakeClientSet.PrependReactor("delete", "services", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, nil, nil
				})
				fakeClientSet.PrependReactor("delete", "ingresses", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, nil, nil
				})
				d.kubeClient = fakeClientSet

				d.repo.On("DeleteRepositoryMemberByRepositoryID", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("DeleteDeploymentsByRepoID", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("DeleteById", mock.Anything, mock.Anything, uuidInput).Return(nil)
				d.repo.On("Transaction", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), uuidInput))
				}).Return(nil)
				d.gitlab.On("DeleteProject", mock.Anything, mock.Anything).Return(nil)
			},
			expError: nil,
		},
		{
			name: "should success without Kubernetes resource cleanup (no deployment)",
			ctx:  utils.WithUserId(context.Background(), uuidInput),
			input: dto.DeleteRepositoryInput{
				CurrentUserId: uuidInput,
				RepoID:        repoID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindUserByID", mock.Anything, uuidInput).Return(&entities.User{Role: enums.UserRole_User}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					BaseModel: entities.BaseModel{
						ID: uuidInput,
					},
					Deployment: nil,
				}, nil)
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything).Return(&entities.RepoMember{Role: enums.RepoRole_Owner}, nil)
				d.repo.On("DeleteRepositoryMemberByRepositoryID", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("DeleteDeploymentsByRepoID", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("DeleteById", mock.Anything, mock.Anything, uuidInput).Return(nil)
				d.repo.On("Transaction", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), uuidInput))
				}).Return(nil)
				d.gitlab.On("DeleteProject", mock.Anything, mock.Anything).Return(nil)
			},
			expError: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				config:   &configs.GlobalConfig{},
				repo:     &repository_mocks.MockRepository{},
				gitlab:   &gitlab_mocks.MockGitlabClient{},
				workflow: &workflow_mocks.MockWorkflowUsecase{},
			}
			tt.mockFn(d)

			u := repository.New(d.config, d.repo, d.gitlab, d.workflow, d.kubeClient, nil)
			err := u.DeleteRepository(tt.ctx, tt.input)
			require.Equal(t, tt.expError, err)
		})
	}
}
