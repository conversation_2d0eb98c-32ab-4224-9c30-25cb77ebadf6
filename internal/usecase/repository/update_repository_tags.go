package repository

import (
	"api-server/internal/dto"
	"api-server/internal/enums"
	"api-server/internal/gateways/gitlab"
	repository "api-server/internal/repositories"
	"api-server/pkg/oteltrace"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"strings"

	"go.opentelemetry.io/otel/codes"
	"gopkg.in/yaml.v2"
)

type ModelMetadata struct {
	Language       []string `yaml:"language" json:"language,omitempty"`
	License        string   `yaml:"license" json:"license,omitempty"`
	LicenseName    string   `yaml:"license_name" json:"license_name,omitempty"`
	LicenseLink    string   `yaml:"license_link" json:"license_link,omitempty"`
	Libraries      []string `yaml:"libraries" json:"libraries,omitempty"`
	TaskCategories []string `yaml:"task_categories" json:"task_categories,omitempty"`
	LibraryName    string   `yaml:"library_name" json:"library_name,omitempty"`
	Tags           []string `yaml:"tags" json:"tags,omitempty"`
	Datasets       []string `yaml:"datasets" json:"datasets,omitempty"`
	Metrics        []string `yaml:"metrics" json:"metrics,omitempty"`
	BaseModel      string   `yaml:"base_model" json:"base_model,omitempty"`
	ModelIndex     []struct {
		Name    string `yaml:"name" json:"name,omitempty"`
		Results []struct {
			Task struct {
				Type string `yaml:"type" json:"type,omitempty"`
				Name string `yaml:"name" json:"name,omitempty"`
			} `yaml:"task" json:"task,omitempty"`
			Dataset struct {
				Type     string            `yaml:"type" json:"type,omitempty"`
				Name     string            `yaml:"name" json:"name,omitempty"`
				Config   string            `yaml:"config" json:"config,omitempty"`
				Split    string            `yaml:"split" json:"split,omitempty"`
				Revision string            `yaml:"revision" json:"revision,omitempty"`
				Args     map[string]string `yaml:"args" json:"args,omitempty"`
			} `yaml:"dataset" json:"dataset,omitempty"`
			Metrics []struct {
				Type   string            `yaml:"type" json:"type,omitempty"`
				Value  string            `yaml:"value" json:"value,omitempty"`
				Name   string            `yaml:"name" json:"name,omitempty"`
				Config string            `yaml:"config" json:"config,omitempty"`
				Args   map[string]string `yaml:"args" json:"args,omitempty"`
			} `yaml:"metrics" json:"metrics,omitempty"`
			Source struct {
				Name string `yaml:"name" json:"name,omitempty"`
				URL  string `yaml:"url" json:"url,omitempty"`
			} `yaml:"source" json:"source,omitempty"`
		} `yaml:"results" json:"results,omitempty"`
	} `yaml:"model-index" json:"model_index,omitempty"`
}

type DatasetMetadata struct {
	Language            []string `yaml:"language" json:"language,omitempty"`
	License             string   `yaml:"license" json:"license,omitempty"`
	LicenseName         string   `yaml:"license_name" json:"license_name,omitempty"`
	LicenseLink         string   `yaml:"license_link" json:"license_link,omitempty"`
	LicenseDetails      string   `yaml:"license_details" json:"license_details,omitempty"`
	Libraries           []string `yaml:"libraries" json:"libraries,omitempty"`
	Tags                []string `yaml:"tags" json:"tags,omitempty"`
	AnnotationsCreators []string `yaml:"annotations_creators" json:"annotations_creators,omitempty"`
	LanguageCreators    []string `yaml:"language_creators" json:"language_creators,omitempty"`
	LanguageDetails     []string `yaml:"language_details" json:"language_details,omitempty"`
	PrettyName          string   `yaml:"pretty_name" json:"pretty_name,omitempty"`
	SizeCategories      []string `yaml:"size_categories" json:"size_categories,omitempty"`
	SourceDatasets      []string `yaml:"source_datasets" json:"source_datasets,omitempty"`
	TaskCategories      []string `yaml:"task_categories" json:"task_categories,omitempty"`
	TaskIDs             []string `yaml:"task_ids" json:"task_ids,omitempty"`
	PaperswithcodeID    string   `yaml:"paperswithcode_id" json:"paperswithcode_id,omitempty"`
	Configs             []struct {
		ConfigName string `yaml:"config_name" json:"config_name,omitempty"`
		DataFiles  []struct {
			Split string `yaml:"split" json:"split,omitempty"`
			Path  string `yaml:"path" json:"path,omitempty"`
		} `yaml:"data_files" json:"data_files,omitempty"`
	} `yaml:"configs" json:"configs,omitempty"`
	DatasetInfo struct {
		Features []struct {
			Name  string `yaml:"name" json:"name,omitempty"`
			DType string `yaml:"dtype" json:"dtype,omitempty"`
		} `yaml:"features" json:"features,omitempty"`
		ConfigName string `yaml:"config_name" json:"config_name,omitempty"`
		Splits     []struct {
			Name        string `yaml:"name" json:"name,omitempty"`
			NumBytes    string `yaml:"num_bytes" json:"num_bytes,omitempty"`
			NumExamples string `yaml:"num_examples" json:"num_examples,omitempty"`
		} `yaml:"splits" json:"splits,omitempty"`
		DownloadSize string `yaml:"download_size" json:"download_size,omitempty"`
		DatasetSize  string `yaml:"dataset_size" json:"dataset_size,omitempty"`
	} `yaml:"dataset_info" json:"dataset_info,omitempty"`
	ExtraGatedFields []map[string]string `yaml:"extra_gated_fields" json:"extra_gated_fields,omitempty"`
	ExtraGatedPrompt string              `yaml:"extra_gated_prompt" json:"extra_gated_prompt,omitempty"`
	TrainEvalIndex   []struct {
		Config string `yaml:"config" json:"config,omitempty"`
		Task   string `yaml:"task" json:"task,omitempty"`
		TaskID string `yaml:"task_id" json:"task_id,omitempty"`
		Splits struct {
			TrainSplit string `yaml:"train_split" json:"train_split,omitempty"`
			EvalSplit  string `yaml:"eval_split" json:"eval_split,omitempty"`
		} `yaml:"splits" json:"splits,omitempty"`
		ColMapping map[string]string `yaml:"col_mapping" json:"col_mapping,omitempty"`
		Metrics    []struct {
			Type string `yaml:"type" json:"type,omitempty"`
			Name string `yaml:"name" json:"name,omitempty"`
		} `yaml:"metrics" json:"metrics,omitempty"`
	} `yaml:"train-eval-index" json:"train_eval_index,omitempty"`
}

const README_FILE = "README.md"

// UpdateTagsInRepository implements the RepositoryUsecase interface for updating repository tags.
// It handles the process of updating repository metadata and tags based on README.md changes.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: Input data containing GitLab push event information
//
// Returns:
//   - error: Any error that occurred during update
func (u *impl) UpdateTagsInRepository(ctx context.Context, input dto.GitLabPushEvent) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.UpdateTagsInRepository")
	defer span.End()

	// Fetch admin user and access token
	token, err := u.getAdminAccessToken(ctx)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get admin access token")
		span.RecordError(err)
		return err
	}

	for _, commit := range input.Commits {
		if u.isReadmeCreatedOrUpdated(commit) {
			err := u.handleReadmeUpdate(ctx, input, token)
			if err != nil {
				span.SetStatus(codes.Error, "failed to handle readme update")
				span.RecordError(err)
				return err
			}
		} else if containsFile(commit.Removed, README_FILE) {
			repoType := enums.RepoType_Models
			if strings.HasPrefix(input.Project.PathWithNamespace, "datasets/") {
				repoType = enums.RepoType_Datasets
			}

			err := u.handleReadmeRemoval(ctx, input.Project.Name, input.Project.Namespace, repoType)
			if err != nil {
				span.SetStatus(codes.Error, "failed to readme removal")
				span.RecordError(err)
				return err
			}
		}
	}

	span.AddEvent("update tags in repository successfully")
	span.SetStatus(codes.Ok, "update tags in repository successfully")
	return nil
}

// getAdminAccessToken retrieves the GitLab access token for the admin user.
// It is used for performing GitLab operations that require admin privileges.
//
// Parameters:
//   - ctx: Context for the operation
//
// Returns:
//   - string: Admin user's GitLab access token
//   - error: Any error that occurred during retrieval
func (u *impl) getAdminAccessToken(ctx context.Context) (string, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.getAdminAccessToken")
	defer span.End()

	adminRefGitID := enums.AdminRefGitID
	adminUser, err := u.repo.FindUser(ctx, repository.FindUserFilter{RefGitUserID: &adminRefGitID})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find user")
		span.RecordError(err)
		return "", err
	}

	if adminUser.GitlabAccessToken != nil {
		span.SetStatus(codes.Ok, "get admin access token successfully")
		return *adminUser.GitlabAccessToken, nil
	}

	span.AddEvent("return empty access token")
	return "", nil
}

// isReadmeCreatedOrUpdated checks if the README.md file was added or modified in a commit.
//
// Parameters:
//   - commit: The GitLab push event commit details.
//
// Returns:
//   - bool: True if README.md was added or modified, false otherwise.
func (u *impl) isReadmeCreatedOrUpdated(commit dto.GitlabPustEventCommit) bool {

	return containsFile(commit.Added, README_FILE) || containsFile(commit.Modified, README_FILE)
}

// handleReadmeUpdate processes updates to the repository's README.md file.
// It extracts metadata from the README and updates the repository accordingly.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: Input data containing GitLab push event information
//   - token: GitLab access token for API calls
//
// Returns:
//   - error: Any error that occurred during processing
func (u *impl) handleReadmeUpdate(ctx context.Context, input dto.GitLabPushEvent, token string) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.handleReadmeUpdate")
	defer span.End()

	yamlPart, err := u.fetchReadmeContent(ctx, input.ProjectID, input.Project.DefaultBranch, token)
	if err != nil {
		span.SetStatus(codes.Error, "fetch readme content failed")
		span.RecordError(err)
		return err
	}

	if strings.HasPrefix(input.Project.PathWithNamespace, "datasets/") {
		return u.processDatasetMetadata(ctx, input.Project.Name, input.Project.Namespace, yamlPart)
	}

	return u.processModelMetadata(ctx, input.Project.Name, input.Project.Namespace, yamlPart)
}

// handleReadmeRemoval processes the removal of a repository's README.md file.
// It clears the repository metadata when the README is removed.
//
// Parameters:
//   - ctx: Context for the operation
//   - repoName: Name of the repository
//   - repoNamespace: Namespace of the repository
//   - repoType: Type of the repository
//
// Returns:
//   - error: Any error that occurred during processing
func (u *impl) handleReadmeRemoval(ctx context.Context, repoName, repoNamespace string, repoType enums.RepoType) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.handleReadmeRemoval")
	defer span.End()

	repo, err := u.repo.FindRepository(ctx, repository.RepositoryFilter{
		Name:      &repoName,
		Type:      &repoType,
		Namespace: &repoNamespace,
	})
	if err != nil {
		span.SetStatus(codes.Error, "find repository failed")
		span.RecordError(err)
		return err
	}

	repo.Metadata = nil
	return u.repo.Save(ctx, repo)
}

// processDatasetMetadata processes dataset metadata from the README file.
// It parses and updates repository metadata for dataset repositories.
//
// Parameters:
//   - ctx: Context for the operation
//   - repoName: Name of the repository
//   - repoNamespace: Namespace of the repository
//   - yamlPart: YAML content from the README file
//
// Returns:
//   - error: Any error that occurred during processing
func (u *impl) processDatasetMetadata(ctx context.Context, repoName, repoNamespace, yamlPart string) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.processDatasetMetadata")
	defer span.End()

	var datasetMetadata DatasetMetadata
	if err := yaml.Unmarshal([]byte(yamlPart), &datasetMetadata); err != nil {
		span.SetStatus(codes.Error, "unmarshal dataset metadata failed")
		span.RecordError(err)
		return fmt.Errorf("failed to parse dataset metadata: %w", err)
	}

	return u.updateRepositoryMetadata(ctx, repoName, repoNamespace, enums.RepoType_Datasets, datasetMetadata)
}

// processModelMetadata processes model metadata from the README file.
// It parses and updates repository metadata for model repositories.
//
// Parameters:
//   - ctx: Context for the operation
//   - repoName: Name of the repository
//   - repoNamespace: Namespace of the repository
//   - yamlPart: YAML content from the README file
//
// Returns:
//   - error: Any error that occurred during processing
func (u *impl) processModelMetadata(ctx context.Context, repoName, repoNamespace, yamlPart string) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.processModelMetadata")
	defer span.End()

	var modelMetadata ModelMetadata
	if err := yaml.Unmarshal([]byte(yamlPart), &modelMetadata); err != nil {
		span.SetStatus(codes.Error, "unmarshal model metadata failed")
		span.RecordError(err)
		return fmt.Errorf("failed to parse model metadata: %w", err)
	}

	return u.updateRepositoryMetadata(ctx, repoName, repoNamespace, enums.RepoType_Models, modelMetadata)
}

// containsFile checks if a target string exists in a slice of strings.
//
// Parameters:
//   - files: A slice of strings to search within.
//   - target: The string to search for.
//
// Returns:
//   - bool: True if the target is found, false otherwise.
func containsFile(files []string, target string) bool {
	for _, file := range files {
		if file == target {
			return true
		}
	}

	return false
}

// fetchReadmeContent retrieves the content of a repository's README.md file.
// It extracts the YAML front matter from the README content.
//
// Parameters:
//   - ctx: Context for the operation
//   - projectID: GitLab project ID
//   - branch: Branch name to fetch from
//   - token: GitLab access token for API calls
//
// Returns:
//   - string: YAML content from the README file
//   - error: Any error that occurred during retrieval
func (u *impl) fetchReadmeContent(ctx context.Context, projectID int64, branch, token string) (string, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.fetchReadmeContent")
	defer span.End()

	resp, err := u.gitlab.GetRawFileFromRepo(ctx, gitlab.GetFileRequest{
		ProjectId: projectID,
		FilePath:  README_FILE,
		Ref:       branch,
		Token:     token,
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to fetch readme content")
		span.RecordError(err)
		return "", err
	}

	defer resp.Body.Close()

	content, err := io.ReadAll(resp.Body)
	if err != nil {
		span.SetStatus(codes.Error, "failed to read content")
		span.RecordError(err)
		return "", fmt.Errorf("failed to read content: %w", err)
	}

	parts := strings.SplitN(string(content), "---", 3)
	if len(parts) < 3 {
		err = fmt.Errorf("no valid YAML front matter found")
		span.SetStatus(codes.Error, err.Error())
		span.RecordError(err)
		return "", err
	}

	span.SetStatus(codes.Ok, "success")
	return parts[1], nil
}

// updateRepositoryMetadata updates the metadata of a repository.
// It stores the provided metadata in the repository record.
//
// Parameters:
//   - ctx: Context for the operation
//   - repoName: Name of the repository
//   - repoNamespace: Namespace of the repository
//   - repoType: Type of the repository
//   - metadata: Metadata to be stored
//
// Returns:
//   - error: Any error that occurred during update
func (u *impl) updateRepositoryMetadata(ctx context.Context, repoName, repoNamespace string, repoType enums.RepoType, metadata interface{}) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.updateRepositoryMetadata")
	defer span.End()

	repo, err := u.repo.FindRepository(ctx, repository.RepositoryFilter{
		Name:      &repoName,
		Type:      &repoType,
		Namespace: &repoNamespace,
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find repository")
		span.RecordError(err)
		return err
	}

	data, err := json.Marshal(metadata)
	if err != nil {
		span.SetStatus(codes.Error, "failed to marshal metadata")
		span.RecordError(err)
		return fmt.Errorf("failed to marshal metadata: %w", err)
	}

	repo.Metadata = data

	span.SetStatus(codes.Ok, "success")
	return u.repo.Save(ctx, repo)
}
