package repository

import (
	"context"
	"errors"
	"net/http"
	"strconv"

	"go.opentelemetry.io/otel/codes"
	"gorm.io/gorm"

	"api-server/internal/dto"
	"api-server/internal/enums"
	"api-server/internal/gateways/gitlab"
	repository "api-server/internal/repositories"
	usecase "api-server/internal/usecase"
	"api-server/pkg/oteltrace"
)

// GetFileFromRepository implements the RepositoryUsecase interface for retrieving file content.
// It fetches the content of a specific file in the repository.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: Input data containing repository ID and file path
//
// Returns:
//   - *dto.GetFileFromRepositoryOutput: File content information
//   - error: Any error that occurred during retrieval
func (u *impl) GetFileFromRepository(ctx context.Context, input dto.GetFileFromRepositoryInput) (*dto.GetFileFromRepositoryOutput, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.GetFileFromRepository")
	defer span.End()

	adminRefGitID := enums.AdminRefGitID
	adminUser, err := u.repo.FindUser(ctx, repository.FindUserFilter{RefGitUserID: &adminRefGitID})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find admin user")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, usecase.ErrUserNotFound
		}
		return nil, err
	}

	repo, err := u.repo.FindRepository(ctx, repository.RepositoryFilter{
		Type:      input.RepoID.RepoType(),
		Namespace: input.RepoID.Namespace(),
		Name:      input.RepoID.RepoName(),
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find repository")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, usecase.ErrRepositoryNotFound
		}
		return nil, err
	}

	token := ""
	if adminUser.GitlabAccessToken != nil {
		token = *adminUser.GitlabAccessToken
	}

	var getFile *gitlab.GetFileResponse

	// get header first
	httpResp, err := u.gitlab.GetHeaderRawFileFromRepo(ctx, gitlab.GetFileRequest{
		ProjectId: repo.RefGitRepoID,
		FilePath:  input.Path,
		Ref:       input.Ref,
		Token:     token,
		LFS:       input.LFS,
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to get header raw file from repo in gitlab")
		span.RecordError(err)
		return nil, err
	}
	getFile = getHeaderFromResponse(httpResp)

	notLFS := input.LFS == nil || !*input.LFS
	limitSize := input.FileContentLimitSize != nil && getFile.Size > *input.FileContentLimitSize
	if limitSize && notLFS {
		// skip get content if file size is larger than limit size and not LFS
	} else {
		resp, err := u.gitlab.GetFileFromRepo(ctx, gitlab.GetFileRequest{
			ProjectId: repo.RefGitRepoID,
			FilePath:  input.Path,
			Ref:       input.Ref,
			Token:     token,
		})
		if err != nil {
			span.SetStatus(codes.Error, "failed to get file from repo in gitlab")
			span.RecordError(err)
			return nil, err
		}
		getFile = resp
	}

	rs := &dto.GetFileFromRepositoryOutput{
		Content:       getFile.Content,
		FileName:      getFile.FileName,
		FilePath:      getFile.FilePath,
		Size:          getFile.Size,
		Encoding:      getFile.Encoding,
		ContentSHA256: getFile.ContentSHA256,
		Ref:           getFile.Ref,
		BlobId:        getFile.BlobId,
		CommitId:      getFile.CommitId,
		LastCommitId:  getFile.LastCommitId,
	}

	commit, err := u.gitlab.GetSingleProjectCommit(ctx, gitlab.GetSingleProjectCommitRequest{
		Id:    repo.RefGitRepoID,
		Sha:   getFile.LastCommitId,
		Token: token,
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to get single project commit from gitlab")
		span.RecordError(err)
		return nil, err
	}

	if commit != nil {
		var lastCommit dto.RepositoryCommit
		rs.LastCommit = *lastCommit.FromGitlab(*commit)
	}

	span.AddEvent("get file content successfully")
	span.SetStatus(codes.Ok, "get file content successfully")
	return rs, nil
}

func getHeaderFromResponse(httpResp *http.Response) *gitlab.GetFileResponse {
	var size int64
	sizeString := httpResp.Header.Get("x-gitlab-size")
	if sizeString != "" {
		size, _ = strconv.ParseInt(sizeString, 10, 64)
	}

	return &gitlab.GetFileResponse{
		Content:       "",
		FileName:      httpResp.Header.Get("x-gitlab-file-name"),
		FilePath:      httpResp.Header.Get("x-gitlab-file-path"),
		Size:          size,
		Encoding:      httpResp.Header.Get("x-gitlab-encoding"),
		ContentSHA256: httpResp.Header.Get("x-gitlab-content-sha256"),
		Ref:           httpResp.Header.Get("x-gitlab-ref"),
		BlobId:        httpResp.Header.Get("x-gitlab-blob-id"),
		CommitId:      httpResp.Header.Get("x-gitlab-commit-id"),
		LastCommitId:  httpResp.Header.Get("x-gitlab-last-commit-id"),
	}
}
