package repository

import (
	"context"
	"errors"
	"strings"

	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
	"gorm.io/gorm"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"api-server/internal/dto"
	"api-server/internal/entities"
	"api-server/internal/enums"
	repository "api-server/internal/repositories"
	"api-server/internal/usecase"
	"api-server/internal/utils"
	"api-server/pkg/oteltrace"
)

// UpdateRepository implements the RepositoryUsecase interface for updating repository information.
// It handles updating repository details including name, description, and visibility settings.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: Input data containing repository update details including ID and new values
//
// Returns:
//   - error: Any error that occurred during update
func (u *impl) UpdateRepository(ctx context.Context, input dto.UpdateRepositoryInput) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.UpdateRepository")
	defer span.End()

	currentUser, err := u.repo.FindUserByID(ctx, input.CurrentUserId)
	if err != nil {
		span.SetStatus(codes.Error, "failed to find user")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return usecase.ErrUserNotFound
		}
		return err
	}

	repo, err := u.repo.FindRepository(ctx, repository.RepositoryFilter{
		Type:      input.RepoID.RepoType(),
		Namespace: input.RepoID.Namespace(),
		Name:      input.RepoID.RepoName(),
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find repository")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return usecase.ErrRepositoryNotFound
		}
		return err
	}

	if currentUser.Role != enums.UserRole_Admin {
		err := checkRepoMemberPermission(ctx, *u, repo, currentUser, span)
		if err != nil {
			span.SetStatus(codes.Error, "no permission")
			span.RecordError(err)
			return err
		}
	}
	//check hardware
	updateData := entities.Repository{
		BaseModel: entities.BaseModel{
			ID: repo.ID,
		},
	}
	err = u.repo.Transaction(ctx, func(ctx context.Context) error {
		if repo.Hardware != nil && input.Hardware != nil {
			repo.Hardware.GPUModel = nil
			// check hardware exist
			if strings.ToLower(input.Hardware.NodeName) != "cpu" {
				node, err := u.kubeClient.CoreV1().Nodes().Get(ctx, input.Hardware.NodeName, metav1.GetOptions{})
				if err != nil {
					span.AddEvent("failed to get node", trace.WithAttributes(attribute.String("node_name", input.Hardware.NodeName)))
					span.SetStatus(codes.Error, "failed to get node")
					span.RecordError(err)
					if apierrors.IsNotFound(err) {
						return usecase.ErrNodeNotExist
					}
					return err
				}
				repo.Hardware.GPUModel = utils.Ptr(utils.TrimNodeName(node.Labels[usecase.GPULabelKey]))
			}

			repo.Hardware.Name = input.Hardware.NodeName
			if input.Hardware.NumCpu != nil {
				repo.Hardware.CPU = int(*input.Hardware.NumCpu)
			}
			if input.Hardware.Mem != nil {
				repo.Hardware.Mem = int(input.Hardware.Mem.ToMiB())
			}

			if err := u.repo.UpdateHardware(ctx, repo.Hardware); err != nil {
				span.SetStatus(codes.Error, "failed to update hardware")
				span.RecordError(err)
				return err
			}
		}

		err = u.repo.UpdateRepository(ctx, updateData)
		if err != nil {
			span.SetStatus(codes.Error, "failed to update repository")
			span.RecordError(err)
			return err
		}

		return nil
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to execute transaction")
		span.RecordError(err)
		return err
	}

	span.AddEvent("update repository successfully")
	span.SetStatus(codes.Ok, "update repository successfully")
	return nil
}
