package repository

import (
	"context"
	"errors"

	"go.opentelemetry.io/otel/codes"
	"gorm.io/gorm"

	"api-server/internal/dto"
	"api-server/internal/entities"
	repository "api-server/internal/repositories"
	"api-server/internal/usecase"
	"api-server/pkg/oteltrace"
	"api-server/pkg/otelzap"
)

// GetMember implements the RepositoryUsecase interface for retrieving repository member information.
// It fetches detailed information about a specific member in the repository.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: Input data containing repository ID and member ID
//
// Returns:
//   - *dto.RepositoryMember: Member information
//   - error: Any error that occurred during retrieval
func (i *impl) GetMember(ctx context.Context, input dto.GetMemberRepositoryInput) (*entities.RepoMember, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.GetMember")
	defer span.End()

	repo, err := i.repo.FindRepository(ctx, repository.RepositoryFilter{
		Type:      input.RepoID.RepoType(),
		Namespace: input.RepoID.Namespace(),
		Name:      input.RepoID.RepoName(),
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find repository")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, usecase.ErrRepositoryNotFound
		}
		return nil, err
	}

	member, err := i.repo.FindUserByID(ctx, input.MemberId)
	if err != nil {
		span.SetStatus(codes.Error, "failed to find user by id")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &entities.RepoMember{}, nil // return blank value if user is not exist
		}
		return nil, err
	}

	repoMember, err := i.repo.FindRepositoryMember(ctx, repository.FilterRepoMember{
		RepoID: &repo.ID,
		UserID: &member.ID,
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find repository member")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			span.SetStatus(codes.Error, err.Error())
			span.RecordError(err)
			otelzap.ErrorWithContext(ctx, "repo member not found", err)
			return &entities.RepoMember{}, nil // return blank value if user is not in repository
		}

		span.SetStatus(codes.Error, err.Error())
		span.RecordError(err)
		otelzap.ErrorWithContext(ctx, "failed to find repo member", err)
		return nil, err
	}

	span.AddEvent("get repository member successfully")
	span.SetStatus(codes.Ok, "get repository member successfully")
	return repoMember, nil
}
