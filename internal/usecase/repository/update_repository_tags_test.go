package repository_test

import (
	"api-server/internal/entities"
	gitlab_mocks "api-server/internal/gateways/gitlab/mocks"
	repository_mocks "api-server/internal/repositories/mocks"
	"api-server/internal/usecase/repository"
	"api-server/internal/utils"
	"context"
	"errors"
	"io"
	"net/http"
	"strings"
	"testing"

	"api-server/internal/dto"

	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
)

type mockRepository struct {
	mock.Mock
}

func TestUpdateTagsInRepository(t *testing.T) {
	type dependencies struct {
		repo   *repository_mocks.MockRepository
		gitlab *gitlab_mocks.MockGitlabClient
	}

	tests := []struct {
		name          string
		input         dto.GitLabPushEvent
		mockSetup     func(*repository_mocks.MockRepository, *gitlab_mocks.MockGitlabClient)
		expectedError error
	}{
		{
			name: "successful README update",
			input: dto.GitLabPushEvent{
				Project: dto.GitlabPustEventProject{
					Name:              "test-project",
					Namespace:         "test-namespace",
					PathWithNamespace: "test-namespace/test-project",
				},
				Commits: []dto.GitlabPustEventCommit{
					{
						Added: []string{"README.md"},
					},
				},
			},
			mockSetup: func(m *repository_mocks.MockRepository, g *gitlab_mocks.MockGitlabClient) {
				m.On("getAdminAccessToken", mock.Anything).Return("test-token", nil)
				m.On("FindUser", mock.Anything, mock.Anything).Return(&entities.User{GitlabAccessToken: utils.Ptr("test-token")}, nil)
				m.On("isReadmeCreatedOrUpdated", mock.Anything).Return(true)
				g.On("GetRawFileFromRepo", mock.Anything, mock.Anything).Return(&http.Response{
					Body: io.NopCloser(strings.NewReader(`---
name: Test Model
version: 1.0.0
description: A test model for demonstration purposes
tags:
  - machine-learning
  - classification
  - python
framework: tensorflow
license: MIT
author: Test Author
metrics:
  accuracy: 0.95
  f1-score: 0.94
dependencies:
  - tensorflow>=2.8.0
  - numpy>=1.19.0
  - scikit-learn>=0.24.0
---
`)),
				}, errors.New("error"))
			},
			expectedError: errors.New("error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Run(tt.name, func(t *testing.T) {
				d := &dependencies{
					repo:   &repository_mocks.MockRepository{},
					gitlab: &gitlab_mocks.MockGitlabClient{},
				}
				tt.mockSetup(d.repo, d.gitlab)
				u := repository.New(nil, d.repo, d.gitlab, nil, nil, nil)
				err := u.UpdateTagsInRepository(context.Background(), tt.input)
				require.Equal(t, tt.expectedError, err)
			})
		})
	}
}
