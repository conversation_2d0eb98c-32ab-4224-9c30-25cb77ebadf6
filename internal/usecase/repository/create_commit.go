package repository

import (
	"context"
	"errors"

	"go.opentelemetry.io/otel/codes"
	"gorm.io/gorm"

	"api-server/internal/dto"
	"api-server/internal/enums"
	"api-server/internal/gateways/gitlab"
	repository "api-server/internal/repositories"
	"api-server/internal/usecase"
	"api-server/pkg/oteltrace"
)

// CreateRepoCommit implements the RepositoryUsecase interface for creating a new commit.
// It handles the process of creating a commit with specified changes in the repository.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: Input data containing repository ID, commit message, and changes
//
// Returns:
//   - *dto.RepositoryCommit: Created commit information
//   - error: Any error that occurred during commit creation
func (u *impl) CreateRepoCommit(ctx context.Context, input dto.CreateRepositoryCommitInput) (*dto.RepositoryCommit, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.CreateRepoCommit")
	defer span.End()

	adminRefGitID := enums.AdminRefGitID
	adminUser, err := u.repo.FindUser(ctx, repository.FindUserFilter{RefGitUserID: &adminRefGitID})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find admin user")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, usecase.ErrUserNotFound
		}
		return nil, err
	}

	repo, err := u.repo.FindRepository(ctx, repository.RepositoryFilter{
		Type:      input.RepoID.RepoType(),
		Namespace: input.RepoID.Namespace(),
		Name:      input.RepoID.RepoName(),
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find repository")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, usecase.ErrRepositoryNotFound
		}
		return nil, err
	}

	token := ""
	if adminUser.GitlabAccessToken != nil {
		token = *adminUser.GitlabAccessToken
	}

	req := gitlab.CreateProjectCommitRequest{
		Id:    repo.RefGitRepoID,
		Token: token,
		CreateCommitInput: gitlab.CreateCommitInput{
			Branch:        input.RepoCommitInput.Branch,
			CommitMessage: input.RepoCommitInput.CommitMessage,
			StartBranch:   input.RepoCommitInput.StartBranch,
			StartSha:      input.RepoCommitInput.StartSha,
			StartProject:  input.RepoCommitInput.StartProject,
			AuthorEmail:   input.RepoCommitInput.AuthorEmail,
			AuthorName:    input.RepoCommitInput.AuthorName,
			Stats:         input.RepoCommitInput.Stats,
			Force:         input.RepoCommitInput.Force,
		},
	}

	if len(input.RepoCommitInput.Actions) > 0 {
		for _, val := range input.RepoCommitInput.Actions {
			req.CreateCommitInput.Actions = append(req.CreateCommitInput.Actions, gitlab.GitLabAction{
				Action:          gitlab.ActionType(val.Action),
				FilePath:        val.FilePath,
				PreviousPath:    val.PreviousPath,
				Content:         val.Content,
				Encoding:        val.Encoding,
				ExecuteFileMode: val.ExecuteFileMode,
			})
		}
	}

	gitlabResp, err := u.gitlab.CreateProjectCommit(ctx, req)
	if err != nil {
		span.SetStatus(codes.Error, "create project commit failed")
		span.RecordError(err)
		return nil, err
	}

	var resp dto.RepositoryCommit
	resp = *resp.FromGitlab(*gitlabResp)

	span.AddEvent("repository commit created successfully")
	span.SetStatus(codes.Ok, "repository commit created successfully")
	return &resp, nil
}
