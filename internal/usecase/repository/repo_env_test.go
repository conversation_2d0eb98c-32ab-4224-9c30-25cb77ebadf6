package repository_test

import (
	"context"
	"errors"
	"testing"

	"api-server/internal/dto"
	"api-server/internal/entities"
	"api-server/internal/enums"
	repository_mocks "api-server/internal/repositories/mocks"
	"api-server/internal/types"
	"api-server/internal/usecase"
	"api-server/internal/usecase/repository"
	"api-server/internal/utils"

	"github.com/google/uuid"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

func TestCreateEnv(t *testing.T) {
	mockUserID, _ := uuid.Parse("d2de43e6-fde6-4deb-a1d0-2d5c2c2e1038")
	repoUUID := uuid.New()

	type dependencies struct {
		repo *repository_mocks.MockRepository
	}

	testcases := []struct {
		name      string
		ctx       context.Context
		repoID    types.RepoID
		input     dto.CreateRepositoryEnvRequest
		mockFn    func(d *dependencies)
		expectErr error
	}{
		{
			name:      "should return usecase.ErrNotSpaceRepo err",
			ctx:       utils.WithUserId(context.Background(), mockUserID),
			repoID:    *types.NewRepoID(enums.RepoType_Models, "namespace", "repo-name"),
			input:     dto.CreateRepositoryEnvRequest{},
			mockFn:    func(d *dependencies) {},
			expectErr: usecase.ErrNotSpaceRepo,
		},
		{
			name:   "should return usecase.ErrRepositoryNotFound if cannot found repo",
			ctx:    utils.WithUserId(context.Background(), mockUserID),
			repoID: *types.NewRepoID(enums.RepoType_Spaces, "namespace", "repo-name"),
			input: dto.CreateRepositoryEnvRequest{
				Key:   "aaa",
				Value: new(string),
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name:   "should return usecase.ErrRepositoryNotFound if cannot found repo",
			ctx:    utils.WithUserId(context.Background(), mockUserID),
			repoID: *types.NewRepoID(enums.RepoType_Spaces, "namespace", "repo-name"),
			input: dto.CreateRepositoryEnvRequest{
				Key:   "aaa",
				Value: new(string),
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expectErr: usecase.ErrRepositoryNotFound,
		},
		{
			name:   "should return err when updating env",
			ctx:    utils.WithUserId(context.Background(), mockUserID),
			repoID: *types.NewRepoID(enums.RepoType_Spaces, "namespace", "repo-name"),
			input: dto.CreateRepositoryEnvRequest{
				Key:   "aaa",
				Value: new(string),
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					BaseModel: entities.BaseModel{ID: repoUUID},
				}, nil)
				d.repo.On("Transaction", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), mockUserID))
				}).Return(usecase.ErrInternal)
				d.repo.On("CreateEnv", mock.Anything, mock.Anything).Return(usecase.ErrInternal)
			},
			expectErr: usecase.ErrInternal,
		},
		{
			name:   "should pass updating env",
			ctx:    utils.WithUserId(context.Background(), mockUserID),
			repoID: *types.NewRepoID(enums.RepoType_Spaces, "namespace", "repo-name"),
			input: dto.CreateRepositoryEnvRequest{
				Key:   "aaa",
				Value: new(string),
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					BaseModel: entities.BaseModel{ID: repoUUID},
				}, nil)
				d.repo.On("Transaction", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), mockUserID))
				}).Return(nil)
				d.repo.On("CreateEnv", mock.Anything, mock.Anything).Return(nil)
			},
			expectErr: nil,
		},
		{
			name:   "should return err when deleting env",
			ctx:    utils.WithUserId(context.Background(), mockUserID),
			repoID: *types.NewRepoID(enums.RepoType_Spaces, "namespace", "repo-name"),
			input: dto.CreateRepositoryEnvRequest{
				Key:   "aaa",
				Value: nil,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					BaseModel: entities.BaseModel{ID: repoUUID},
				}, nil)
				d.repo.On("Transaction", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), mockUserID))
				}).Return(usecase.ErrInternal)
				d.repo.On("DeleteEnv", mock.Anything, mock.Anything).Return(usecase.ErrInternal)
			},
			expectErr: usecase.ErrInternal,
		},
		{
			name:   "should pass deleting env",
			ctx:    utils.WithUserId(context.Background(), mockUserID),
			repoID: *types.NewRepoID(enums.RepoType_Spaces, "namespace", "repo-name"),
			input: dto.CreateRepositoryEnvRequest{
				Key:   "aaa",
				Value: nil,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					BaseModel: entities.BaseModel{ID: repoUUID},
				}, nil)
				d.repo.On("Transaction", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), mockUserID))
				}).Return(nil)
				d.repo.On("DeleteEnv", mock.Anything, mock.Anything).Return(nil)
			},
			expectErr: nil,
		},
	}

	for _, testcase := range testcases {
		t.Run(testcase.name, func(t *testing.T) {
			r := &dependencies{
				repo: &repository_mocks.MockRepository{},
			}
			testcase.mockFn(r)

			usecase := repository.New(nil, r.repo, nil, nil, nil, nil)
			err := usecase.CreateEnv(context.Background(), testcase.repoID, testcase.input)

			// assertion
			require.Equal(t, testcase.expectErr, err)
		})
	}
}

func TestBulkCreateEnv(t *testing.T) {
	mockUserID, _ := uuid.Parse("d2de43e6-fde6-4deb-a1d0-2d5c2c2e1038")
	repoUUID := uuid.New()

	type dependencies struct {
		repo *repository_mocks.MockRepository
	}

	testcases := []struct {
		name      string
		ctx       context.Context
		repoID    types.RepoID
		input     dto.BulkCreateRepositoryEnvRequest
		mockFn    func(d *dependencies)
		expectErr error
	}{
		{
			name:      "should return usecase.ErrNotSpaceRepo err",
			ctx:       utils.WithUserId(context.Background(), mockUserID),
			repoID:    *types.NewRepoID(enums.RepoType_Models, "namespace", "repo-name"),
			input:     dto.BulkCreateRepositoryEnvRequest{},
			mockFn:    func(d *dependencies) {},
			expectErr: usecase.ErrNotSpaceRepo,
		},
		{
			name:   "should return usecase.ErrRepositoryNotFound if cannot found repo",
			ctx:    utils.WithUserId(context.Background(), mockUserID),
			repoID: *types.NewRepoID(enums.RepoType_Spaces, "namespace", "repo-name"),
			input: dto.BulkCreateRepositoryEnvRequest{
				Envs: []dto.CreateRepositoryEnvRequest{
					{Key: "KEY1", Value: stringPtr("value1")},
					{Key: "KEY2", Value: stringPtr("value2")},
				},
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expectErr: usecase.ErrRepositoryNotFound,
		},
		{
			name:   "should return usecase.ErrInternal if find repo failed",
			ctx:    utils.WithUserId(context.Background(), mockUserID),
			repoID: *types.NewRepoID(enums.RepoType_Spaces, "namespace", "repo-name"),
			input: dto.BulkCreateRepositoryEnvRequest{
				Envs: []dto.CreateRepositoryEnvRequest{
					{Key: "KEY1", Value: stringPtr("value1")},
				},
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, errors.New("db error"))
			},
			expectErr: usecase.ErrInternal,
		},
		{
			name:   "should return usecase.ErrInternal if transaction failed",
			ctx:    utils.WithUserId(context.Background(), mockUserID),
			repoID: *types.NewRepoID(enums.RepoType_Spaces, "namespace", "repo-name"),
			input: dto.BulkCreateRepositoryEnvRequest{
				Envs: []dto.CreateRepositoryEnvRequest{
					{Key: "KEY1", Value: stringPtr("value1")},
					{Key: "KEY2", Value: stringPtr("value2")},
				},
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					BaseModel: entities.BaseModel{ID: repoUUID},
				}, nil)
				d.repo.On("Transaction", mock.Anything, mock.Anything).Return(errors.New("transaction error"))
			},
			expectErr: usecase.ErrInternal,
		},
		{
			name:   "should pass bulk creating env",
			ctx:    utils.WithUserId(context.Background(), mockUserID),
			repoID: *types.NewRepoID(enums.RepoType_Spaces, "namespace", "repo-name"),
			input: dto.BulkCreateRepositoryEnvRequest{
				Envs: []dto.CreateRepositoryEnvRequest{
					{Key: "KEY1", Value: stringPtr("value1")},
					{Key: "KEY2", Value: stringPtr("value2")},
					{Key: "KEY3", Value: nil}, // This should delete KEY3
				},
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					BaseModel: entities.BaseModel{ID: repoUUID},
				}, nil)
				d.repo.On("Transaction", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), mockUserID))
				}).Return(nil)
				d.repo.On("BulkCreateEnv", mock.Anything, mock.Anything).Return(nil)
			},
			expectErr: nil,
		},
	}

	for _, testcase := range testcases {
		t.Run(testcase.name, func(t *testing.T) {
			r := &dependencies{
				repo: &repository_mocks.MockRepository{},
			}
			testcase.mockFn(r)

			usecase := repository.New(nil, r.repo, nil, nil, nil, nil)
			err := usecase.BulkCreateEnv(context.Background(), testcase.repoID, testcase.input)

			// assertion
			require.Equal(t, testcase.expectErr, err)
		})
	}
}

// Helper function to create string pointer
func stringPtr(s string) *string {
	return &s
}

func TestGetEnv(t *testing.T) {
	mockUserID, _ := uuid.Parse("d2de43e6-fde6-4deb-a1d0-2d5c2c2e1038")
	repoUUID := uuid.New()

	type dependencies struct {
		repo *repository_mocks.MockRepository
	}

	testcases := []struct {
		name      string
		ctx       context.Context
		repoID    types.RepoID
		input     dto.UpdateRepositoryEnvRequest
		mockFn    func(d *dependencies)
		expectErr error
	}{
		{
			name:      "should return usecase.ErrNotSpaceRepo err",
			ctx:       utils.WithUserId(context.Background(), mockUserID),
			repoID:    *types.NewRepoID(enums.RepoType_Models, "namespace", "repo-name"),
			input:     dto.UpdateRepositoryEnvRequest{},
			mockFn:    func(d *dependencies) {},
			expectErr: usecase.ErrNotSpaceRepo,
		},
		{
			name:   "should return usecase.ErrRepositoryNotFound if cannot found repo",
			ctx:    utils.WithUserId(context.Background(), mockUserID),
			repoID: *types.NewRepoID(enums.RepoType_Spaces, "namespace", "repo-name"),
			input: dto.UpdateRepositoryEnvRequest{
				OldKey: "bbb",
				Key:    "aaa",
				Value:  new(string),
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name:   "should return usecase.ErrRepositoryNotFound if cannot found repo",
			ctx:    utils.WithUserId(context.Background(), mockUserID),
			repoID: *types.NewRepoID(enums.RepoType_Spaces, "namespace", "repo-name"),
			input: dto.UpdateRepositoryEnvRequest{
				OldKey: "bbb",
				Key:    "aaa",
				Value:  new(string),
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expectErr: usecase.ErrRepositoryNotFound,
		},
		{
			name:   "should return err when GetEnv env",
			ctx:    utils.WithUserId(context.Background(), mockUserID),
			repoID: *types.NewRepoID(enums.RepoType_Spaces, "namespace", "repo-name"),
			input: dto.UpdateRepositoryEnvRequest{
				OldKey: "bbb",
				Key:    "aaa",
				Value:  new(string),
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					BaseModel: entities.BaseModel{ID: repoUUID},
				}, nil)
				d.repo.On("GetEnv", mock.Anything, mock.Anything).Return(nil, usecase.ErrInternal)
			},
			expectErr: usecase.ErrInternal,
		},
		{
			name:   "should error",
			ctx:    utils.WithUserId(context.Background(), mockUserID),
			repoID: *types.NewRepoID(enums.RepoType_Spaces, "namespace", "repo-name"),
			input: dto.UpdateRepositoryEnvRequest{
				Key:    "aaa",
				OldKey: "bbb",
				Value:  new(string),
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					BaseModel: entities.BaseModel{ID: repoUUID},
				}, nil)
				d.repo.On("GetEnv", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expectErr: nil,
		},
		{
			name:   "should pass get env",
			ctx:    utils.WithUserId(context.Background(), mockUserID),
			repoID: *types.NewRepoID(enums.RepoType_Spaces, "namespace", "repo-name"),
			input: dto.UpdateRepositoryEnvRequest{
				Key:    "aaa",
				OldKey: "bbb",
				Value:  new(string),
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					BaseModel: entities.BaseModel{ID: repoUUID},
				}, nil)
				d.repo.On("GetEnv", mock.Anything, mock.Anything).Return(&entities.RepoEnv{
					RepoID: repoUUID,
					Env: datatypes.JSON(`[
						{"key": "aaa", "value": "bbb"},
						{"key": "ccc", "value": "ddd"}
					]`),
				}, nil)
			},
			expectErr: nil,
		},
	}

	for _, testcase := range testcases {
		t.Run(testcase.name, func(t *testing.T) {
			r := &dependencies{
				repo: &repository_mocks.MockRepository{},
			}
			testcase.mockFn(r)

			usecase := repository.New(nil, r.repo, nil, nil, nil, nil)
			_, err := usecase.GetEnv(context.Background(), testcase.repoID)

			// assertion
			require.Equal(t, testcase.expectErr, err)
		})
	}
}

func TestUpdateEnv(t *testing.T) {
	mockUserID, _ := uuid.Parse("d2de43e6-fde6-4deb-a1d0-2d5c2c2e1038")
	repoUUID := uuid.New()

	type dependencies struct {
		repo *repository_mocks.MockRepository
	}

	testcases := []struct {
		name      string
		ctx       context.Context
		repoID    types.RepoID
		input     dto.UpdateRepositoryEnvRequest
		mockFn    func(d *dependencies)
		expectErr error
	}{
		{
			name:      "should return usecase.ErrNotSpaceRepo err",
			ctx:       utils.WithUserId(context.Background(), mockUserID),
			repoID:    *types.NewRepoID(enums.RepoType_Models, "namespace", "repo-name"),
			input:     dto.UpdateRepositoryEnvRequest{},
			mockFn:    func(d *dependencies) {},
			expectErr: usecase.ErrNotSpaceRepo,
		},
		{
			name:   "should return usecase.ErrRepositoryNotFound if cannot found repo",
			ctx:    utils.WithUserId(context.Background(), mockUserID),
			repoID: *types.NewRepoID(enums.RepoType_Spaces, "namespace", "repo-name"),
			input: dto.UpdateRepositoryEnvRequest{
				OldKey: "bbb",
				Key:    "aaa",
				Value:  new(string),
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, usecase.ErrRepositoryNotFound)
			},
			expectErr: usecase.ErrRepositoryNotFound,
		},
		{
			name:   "should return usecase.ErrRepositoryNotFound if cannot found repo",
			ctx:    utils.WithUserId(context.Background(), mockUserID),
			repoID: *types.NewRepoID(enums.RepoType_Spaces, "namespace", "repo-name"),
			input: dto.UpdateRepositoryEnvRequest{
				OldKey: "bbb",
				Key:    "aaa",
				Value:  new(string),
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expectErr: usecase.ErrRepositoryNotFound,
		},
		{
			name:   "should return err when updating env",
			ctx:    utils.WithUserId(context.Background(), mockUserID),
			repoID: *types.NewRepoID(enums.RepoType_Spaces, "namespace", "repo-name"),
			input: dto.UpdateRepositoryEnvRequest{
				OldKey: "bbb",
				Key:    "aaa",
				Value:  new(string),
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					BaseModel: entities.BaseModel{ID: repoUUID},
				}, nil)
				d.repo.On("Transaction", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), mockUserID))
				}).Return(usecase.ErrInternal)
				d.repo.On("UpdateEnv", mock.Anything, mock.Anything).Return(usecase.ErrInternal)
			},
			expectErr: usecase.ErrInternal,
		},
		{
			name:   "should pass updating env",
			ctx:    utils.WithUserId(context.Background(), mockUserID),
			repoID: *types.NewRepoID(enums.RepoType_Spaces, "namespace", "repo-name"),
			input: dto.UpdateRepositoryEnvRequest{
				Key:    "aaa",
				OldKey: "bbb",
				Value:  new(string),
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					BaseModel: entities.BaseModel{ID: repoUUID},
				}, nil)
				d.repo.On("Transaction", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), mockUserID))
				}).Return(nil)
				d.repo.On("UpdateEnv", mock.Anything, mock.Anything).Return(nil)
			},
			expectErr: nil,
		},
	}

	for _, testcase := range testcases {
		t.Run(testcase.name, func(t *testing.T) {
			r := &dependencies{
				repo: &repository_mocks.MockRepository{},
			}
			testcase.mockFn(r)

			usecase := repository.New(nil, r.repo, nil, nil, nil, nil)
			err := usecase.UpdateEnv(context.Background(), testcase.repoID, testcase.input)

			// assertion
			require.Equal(t, testcase.expectErr, err)
		})
	}
}

func TestDeleteEnv(t *testing.T) {
	mockUserID, _ := uuid.Parse("d2de43e6-fde6-4deb-a1d0-2d5c2c2e1038")
	repoUUID := uuid.New()

	type dependencies struct {
		repo *repository_mocks.MockRepository
	}

	testcases := []struct {
		name      string
		ctx       context.Context
		repoID    types.RepoID
		input     dto.DeleteRepositoryEnvRequest
		mockFn    func(d *dependencies)
		expectErr error
	}{
		{
			name:      "should return usecase.ErrNotSpaceRepo err",
			ctx:       utils.WithUserId(context.Background(), mockUserID),
			repoID:    *types.NewRepoID(enums.RepoType_Models, "namespace", "repo-name"),
			input:     dto.DeleteRepositoryEnvRequest{},
			mockFn:    func(d *dependencies) {},
			expectErr: usecase.ErrNotSpaceRepo,
		},
		{
			name:   "should return usecase.ErrRepositoryNotFound if cannot found repo",
			ctx:    utils.WithUserId(context.Background(), mockUserID),
			repoID: *types.NewRepoID(enums.RepoType_Spaces, "namespace", "repo-name"),
			input: dto.DeleteRepositoryEnvRequest{
				Key: "aaa",
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, usecase.ErrRepositoryNotFound)
			},
			expectErr: usecase.ErrRepositoryNotFound,
		},
		{
			name:   "should return usecase.ErrRepositoryNotFound if cannot found repo",
			ctx:    utils.WithUserId(context.Background(), mockUserID),
			repoID: *types.NewRepoID(enums.RepoType_Spaces, "namespace", "repo-name"),
			input: dto.DeleteRepositoryEnvRequest{
				Key: "aaa",
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expectErr: usecase.ErrRepositoryNotFound,
		},
		{
			name:   "should return err when delete env",
			ctx:    utils.WithUserId(context.Background(), mockUserID),
			repoID: *types.NewRepoID(enums.RepoType_Spaces, "namespace", "repo-name"),
			input: dto.DeleteRepositoryEnvRequest{
				Key: "aaa",
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					BaseModel: entities.BaseModel{ID: repoUUID},
				}, nil)
				d.repo.On("Transaction", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), mockUserID))
				}).Return(usecase.ErrInternal)
				d.repo.On("DeleteEnv", mock.Anything, mock.Anything).Return(usecase.ErrInternal)
			},
			expectErr: usecase.ErrInternal,
		},
		{
			name:   "should pass updating env",
			ctx:    utils.WithUserId(context.Background(), mockUserID),
			repoID: *types.NewRepoID(enums.RepoType_Spaces, "namespace", "repo-name"),
			input: dto.DeleteRepositoryEnvRequest{
				Key: "aaa",
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					BaseModel: entities.BaseModel{ID: repoUUID},
				}, nil)
				d.repo.On("Transaction", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), mockUserID))
				}).Return(nil)
				d.repo.On("DeleteEnv", mock.Anything, mock.Anything).Return(nil)
			},
			expectErr: nil,
		},
	}

	for _, testcase := range testcases {
		t.Run(testcase.name, func(t *testing.T) {
			r := &dependencies{
				repo: &repository_mocks.MockRepository{},
			}
			testcase.mockFn(r)

			usecase := repository.New(nil, r.repo, nil, nil, nil, nil)
			err := usecase.DeleteEnv(context.Background(), testcase.repoID, testcase.input)

			// assertion
			require.Equal(t, testcase.expectErr, err)
		})
	}
}
