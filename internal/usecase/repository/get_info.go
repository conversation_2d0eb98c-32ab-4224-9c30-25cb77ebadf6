package repository

import (
	"context"
	"errors"

	"go.opentelemetry.io/otel/codes"
	"gorm.io/gorm"

	"api-server/internal/dto"
	"api-server/internal/entities"
	"api-server/internal/enums"
	"api-server/internal/gateways/gitlab"
	repository "api-server/internal/repositories"
	"api-server/internal/types"
	"api-server/internal/usecase"
	"api-server/pkg/oteltrace"
)

// GetRepositoryInfo implements the RepositoryUsecase interface for retrieving repository details.
// It fetches comprehensive information about a specific repository including its members,
// GitLab project details, and access URLs.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: Input data containing repository ID
//
// Returns:
//   - *dto.GetRepositoryInfoOutput: Repository information including details and URLs
//   - error: Any error that occurred during retrieval
func (u *impl) GetRepoInfo(
	ctx context.Context,
	input dto.GetRepositoryInput,
) (*dto.GetRepositoryOutput, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.GetRepoInfo")
	defer span.End()

	adminRefGitID := enums.AdminRefGitID
	adminUser, err := u.repo.FindUser(ctx, repository.FindUserFilter{RefGitUserID: &adminRefGitID})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find admin user")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, usecase.ErrUserNotFound
		}
		return nil, err
	}

	repo, err := u.repo.FindRepository(ctx, repository.RepositoryFilter{
		Type:      input.RepoID.RepoType(),
		Namespace: input.RepoID.Namespace(),
		Name:      input.RepoID.RepoName(),
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find repository")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, usecase.ErrRepositoryNotFound
		}
		return nil, err
	}

	token := ""
	if adminUser.GitlabAccessToken != nil {
		token = *adminUser.GitlabAccessToken
	}

	output, err := prepareOutput(ctx, u, repo, token)
	if err != nil {
		span.SetStatus(codes.Error, "prepare output failed")
		span.RecordError(err)
		return nil, err
	}

	owner, namespace, err := getOwnerAndNamespace(ctx, u, repo)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get owner and namespace")
		span.RecordError(err)
		return nil, err
	}
	output.Owner = *owner

	// tags
	tags, err := u.repo.ListRepoTagsByQueryModel(ctx, output.Metadata)
	if err != nil {
		span.SetStatus(codes.Error, "failed to list repository's tags")
		span.RecordError(err)
		return nil, err
	}

	output.Tags = dto.FromManyEntities[entities.Tag, dto.Tag](tags)
	output.RepoID = *types.NewRepoID(repo.Type, *namespace, repo.Name)

	span.AddEvent("get repository info successfully")
	span.SetStatus(codes.Ok, "get repository info successfully")
	return output, nil
}

// getOwnerAndNamespace retrieves the owner and namespace information for a repository.
//
// Parameters:
//   - ctx: Context for the operation
//   - u: Repository usecase implementation
//   - repo: Repository entity
//
// Returns:
//   - *dto.RepositoryOwner: Owner information
//   - *string: Namespace string
//   - error: Any error that occurred during retrieval
func getOwnerAndNamespace(ctx context.Context, u *impl, repo *entities.Repository) (*dto.RepositoryOwner, *string, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.getOwnerAndNamespace")
	defer span.End()

	var owner *dto.RepositoryOwner
	var namespace string
	if repo.OrgID != nil {
		organization, err := u.repo.FindOrganizationByID(ctx, *repo.OrgID)
		if err != nil {
			span.SetStatus(codes.Error, "failed to find organization")
			span.RecordError(err)
			return nil, nil, err
		}

		owner = &dto.RepositoryOwner{
			ID:     *repo.OrgID,
			Name:   organization.Name,
			Path:   organization.PathName,
			Type:   enums.RepoOwnerType_Org,
			Avatar: organization.Avatar,
		}
		namespace = organization.PathName
	} else if repo.UserID != nil {
		repoUser, err := u.repo.FindUserByID(ctx, *repo.UserID)
		if err != nil {
			span.SetStatus(codes.Error, "failed to find user by id")
			span.RecordError(err)
			return nil, nil, err
		}

		owner = &dto.RepositoryOwner{
			ID:     *repo.UserID,
			Name:   repoUser.Name,
			Path:   repoUser.Username,
			Type:   enums.RepoOwnerType_User,
			Email:  &repoUser.Email,
			Avatar: repoUser.Avatar,
		}
		namespace = repoUser.Username
	}

	if owner.Avatar != nil {
		repoImage, err := u.aws.GenPreSignUrl(ctx, *owner.Avatar)
		if err != nil {
			span.SetStatus(codes.Error, "failed to generate presigned url for avatar")
			span.RecordError(err)
			return owner, &namespace, nil
		}
		owner.Avatar = &repoImage
	}

	span.SetStatus(codes.Ok, "getOwnerAndNamespace successfully")
	return owner, &namespace, nil
}

// prepareOutput prepares the repository information output including GitLab URLs,
// hardware details, and deployment information.
//
// Parameters:
//   - ctx: Context for the operation
//   - u: Repository usecase implementation
//   - repo: Repository entity
//   - token: GitLab access token
//
// Returns:
//   - *dto.GetRepositoryOutput: Prepared repository information
//   - error: Any error that occurred during preparation
func prepareOutput(ctx context.Context, u *impl, repo *entities.Repository, token string) (*dto.GetRepositoryOutput, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.prepareOutput")
	defer span.End()

	gitlabResp, err := u.gitlab.GetProjectURLs(ctx, gitlab.GetProjectURLsRequest{
		Id:    repo.RefGitRepoID,
		Token: token,
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to get project URL")
		span.RecordError(err)
		return nil, err
	}

	output := &dto.GetRepositoryOutput{
		DefaultBranch: gitlabResp.DefaultBranch,
		Name:          repo.Name,
		Avatar:        repo.Avatar,
		Visibility:    repo.Visibility,
		Type:          repo.Type,
		SSHURL:        gitlabResp.SSHUrl,
		HTTPURL:       gitlabResp.HTTPUrl,
		Metadata:      repo.Metadata,
	}
	if repo.User != nil {
		output.UserID = *repo.UserID
	}
	if repo.Hardware != nil {
		var hardware dto.Hardware
		hardware = hardware.FromEntity(*repo.Hardware)
		output.Hardware = &hardware
	}
	if repo.Deployment != nil {
		var deployment dto.Deployment
		deployment = deployment.FromEntity(*repo.Deployment)
		deployment.WithDomain(u.config.Space.SpaceDomain)
		output.Deployment = &deployment
	}

	//Pre-sign URL
	if output.Avatar != nil {
		repoImage, err := u.aws.GenPreSignUrl(ctx, *output.Avatar)
		if err != nil {
			span.SetStatus(codes.Error, "failed to generate pre-sign URL")
			span.RecordError(err)
			return nil, err
		}

		output.Avatar = &repoImage
	}

	span.SetStatus(codes.Ok, "prepareOutput successfully")
	return output, nil
}
