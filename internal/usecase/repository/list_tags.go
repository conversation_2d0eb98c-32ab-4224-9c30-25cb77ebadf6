package repository

import (
	"context"
	"sort"
	"sync"

	"api-server/internal/dto"
	"api-server/internal/entities"
	"api-server/pkg/oteltrace"

	"go.opentelemetry.io/otel/codes"
)

// Desired order
var DesiredOrder = []string{"Main", "Tasks", "Libraries", "Datasets", "Languages", "Licenses", "Other"}

var (
	orderMap     map[string]int
	orderMapOnce sync.Once
)

// ListRepoTags implements the RepositoryUsecase interface for retrieving repository tags.
// It fetches a list of tags associated with a repository.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: Input data containing repository ID and query parameters
//
// Returns:
//   - *dto.ListRepoTagsOutput: List of tag information
//   - error: Any error that occurred during retrieval
func (u *impl) ListRepoTags(ctx context.Context, input dto.ListRepoTagsInput) (*dto.ListRepoTagsOutput, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.ListRepoTags")
	defer span.End()

	tags, err := u.repo.ListRepoTags(ctx, input)
	if err != nil {
		span.SetStatus(codes.Error, "failed to list repository tags")
		span.RecordError(err)
		return nil, err
	}

	data := dto.FromManyEntities[entities.Tag, dto.Tag](tags)
	//sort tags
	sortTagsByOrder(data)

	span.AddEvent("list repository tags successfully")
	span.SetStatus(codes.Ok, "list repository tags successfully")
	return &dto.ListRepoTagsOutput{
		Data: &data,
	}, nil
}

// getOrderMap returns a map of tag types to their desired order index
func getOrderMap() map[string]int {
	orderMapOnce.Do(func() {
		orderMap = make(map[string]int)
		for i, name := range DesiredOrder {
			orderMap[name] = i
		}
	})
	return orderMap
}

// sortTagsByOrder sorts the tags according to the desired order
func sortTagsByOrder(data []dto.Tag) {
	orderMap := getOrderMap()

	sort.Slice(data, func(i, j int) bool {
		iData := data[i].Type
		jData := data[j].Type
		iIndex, iOk := orderMap[iData]
		jIndex, jOk := orderMap[jData]

		if !iOk {
			iIndex = len(DesiredOrder)
		}
		if !jOk {
			jIndex = len(DesiredOrder)
		}

		return iIndex < jIndex
	})
}
