package repository

import (
	"context"

	"api-server/internal/dto"
	"api-server/internal/entities"
	"api-server/pkg/oteltrace"

	"go.opentelemetry.io/otel/codes"
)

// ListRepoTemplates implements the RepositoryUsecase interface for retrieving repository templates.
// It fetches a list of available repository templates that can be used for creating new repositories.
//
// Parameters:
//   - ctx: Context for the operation
//
// Returns:
//   - []dto.RepoTemplate: List of repository template information
//   - error: Any error that occurred during retrieval
func (u *impl) ListRepoTemplates(ctx context.Context) ([]dto.RepoTemplate, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.ListRepoTemplates")
	defer span.End()

	templates, err := u.repo.ListTemplates(ctx)
	if err != nil {
		span.SetStatus(codes.Error, "failed to list repository templates")
		span.RecordError(err)
		return nil, err
	}

	span.AddEvent("list repository templates successfully")
	span.SetStatus(codes.Ok, "list repository templates successfully")
	return dto.FromManyEntities[entities.RepoTemplate, dto.RepoTemplate](templates), nil
}
