package repository

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
	"gorm.io/gorm"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"api-server/internal/dto"
	"api-server/internal/entities"
	"api-server/internal/enums"
	"api-server/internal/gateways/gitlab"
	repository "api-server/internal/repositories"
	"api-server/internal/types"
	"api-server/internal/usecase"
	"api-server/internal/utils"
	"api-server/pkg/oteltrace"
)

// CreateRepository implements the RepositoryUsecase interface for creating a new repository.
// It handles the creation process including GitLab project setup, member initialization,
// and repository configuration.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: Input data containing repository details including name, description, and visibility
//
// Returns:
//   - *dto.CreateRepositoryOutput: Created repository information including ID and URLs
//   - error: Any error that occurred during creation
func (u *impl) CreateRepository(ctx context.Context, input dto.RepositoryCreateInput) (*dto.RepositoryCreateResponse, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.CreateRepository")
	defer span.End()

	currentUser, err := u.repo.FindUserByID(ctx, input.CurrentUserId)
	if err != nil {
		span.SetStatus(codes.Error, "failed to find user by id")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, usecase.ErrUserNotFound
		}
		return nil, err
	}

	err = utils.ValidatePath(input.Name)
	if err != nil {
		span.SetStatus(codes.Error, "invalid repository name")
		span.RecordError(err)
		return nil, err
	}

	findRepositoryFilter := repository.RepositoryFilter{
		Name:      (*string)(&input.Name),
		Type:      (&input.Type),
		Namespace: &currentUser.Username,
		UserId:    &input.CurrentUserId,
	}
	if input.Owner.Type == enums.RepoOwnerType_Org {
		org, err := u.repo.FindOrganization(ctx, repository.FilterOrganization{
			OrgId: &input.Owner.ID,
		})
		if err != nil {
			span.SetStatus(codes.Error, "failed to find organization")
			span.RecordError(err)
			return nil, err
		}

		orgMember, err := u.repo.FindOrgMember(ctx, repository.FilterOrgMember{
			OrgID:  &input.Owner.ID,
			UserID: &input.CurrentUserId,
		})
		if err != nil {
			span.SetStatus(codes.Error, "failed to find organization member")
			span.RecordError(err)
			return nil, err
		}

		if orgMember.Role != enums.OrgRole_Owner {
			span.SetStatus(codes.Error, "user do not hvae permission")
			span.RecordError(err)
			return nil, usecase.ErrNoPermission
		}

		findRepositoryFilter.OrgId = &input.Owner.ID
		findRepositoryFilter.Namespace = &org.PathName
	}
	repo, err := u.repo.FindRepository(ctx, findRepositoryFilter)
	if err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			span.SetStatus(codes.Error, "failed to find repository")
			span.RecordError(err)
			return nil, err
		}
	}

	if repo != nil {
		err := errors.New("repository already exists")
		span.SetStatus(codes.Error, err.Error())
		span.RecordError(err)
		return nil, usecase.ErrRepositoryExist
	}

	var namespace string
	var gitGroupInfo *entities.DefaultGitGroup
	var org *entities.Organization
	if input.Owner.Type == enums.RepoOwnerType_Org {
		org, err = u.repo.FindOrganizationByID(ctx, input.Owner.ID)
		if err != nil {
			span.SetStatus(codes.Error, "failed to find organization by id")
			span.RecordError(err)
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, usecase.ErrOrganizationNotFound
			}
			return nil, err
		}

		namespace = org.PathName
		gitGroupInfo, err = u.repo.FindOrgGitGroup(ctx, input.Owner.ID)
		if err != nil {
			span.SetStatus(codes.Error, "failed to find organization Git group")
			span.RecordError(err)
			return nil, err
		}
	} else {
		namespace = currentUser.Username
		gitGroupInfo, err = u.repo.FindUserGitGroup(ctx, currentUser.ID)
		if err != nil {
			span.SetStatus(codes.Error, "failed to find user Git group")
			span.RecordError(err)
			return nil, err
		}
	}

	repoUUID := uuid.New()
	repository := &entities.Repository{
		BaseModel: entities.BaseModel{
			ID: repoUUID,
		},
		Name:       input.Name,
		Type:       input.Type,
		Visibility: input.Visibility,
		Org:        org,
		User:       currentUser,
	}
	err = u.repo.Transaction(ctx, func(ctx context.Context) error {
		// create repository
		err = u.repo.Create(ctx, repository)
		if err != nil {
			span.SetStatus(codes.Error, "failed to create repository entity")
			span.RecordError(err)
			return err
		}
		// assign hardware to repositrory if repository type is Space
		if input.Type == enums.RepoType_Spaces {
			// check hardware exist
			hardwareInput := &entities.Hardware{
				Name:   input.Hardware.NodeName,
				RepoID: &repoUUID,
				CPU:    1,
				Mem:    512, // in Mebibytes
			}
			if strings.ToLower(input.Hardware.NodeName) != "cpu" {
				node, err := u.kubeClient.CoreV1().Nodes().Get(ctx, input.Hardware.NodeName, metav1.GetOptions{})
				if err != nil {
					span.AddEvent("failed to get node", trace.WithAttributes(attribute.String("node_name", input.Hardware.NodeName)))
					span.SetStatus(codes.Error, "failed to get node")
					span.RecordError(err)
					if apierrors.IsNotFound(err) {
						return usecase.ErrNodeNotExist
					}
					return err
				}
				hardwareInput.GPUModel = utils.Ptr(utils.TrimNodeName(node.Labels[usecase.GPULabelKey]))
			}
			// create hardware if repository type is Space
			_, err := u.repo.CreateHardware(ctx, hardwareInput)
			if err != nil {
				span.SetStatus(codes.Error, "failed to create hardware")
				span.RecordError(err)
				return err
			}
		}

		token := ""
		if currentUser.GitlabAccessToken != nil {
			token = *currentUser.GitlabAccessToken
		}

		reqProject := gitlab.CreateProjectRequest{
			Name:       repository.Name,
			Path:       input.Name,
			Visibility: repository.Visibility.String(),
			Token:      token,
		}

		if gitGroupInfo != nil {
			switch input.Type {
			case enums.RepoType_Spaces:
				reqProject.NamespaceId = &gitGroupInfo.RefGitSpacesID
			case enums.RepoType_Datasets:
				reqProject.NamespaceId = &gitGroupInfo.RefGitDatasetsID
			case enums.RepoType_Models:
				reqProject.NamespaceId = &gitGroupInfo.RefGitModelsID
			case enums.RepoType_Composes:
				if gitGroupInfo.RefGitComposesID == nil {
					composeGroupID, err := u.createNewComposeGitGroup(ctx, currentUser.RefGitUserID, currentUser.ID, currentUser.Username)
					if err != nil {
						return err
					}
					gitGroupInfo.RefGitComposesID = composeGroupID
				}
				reqProject.NamespaceId = gitGroupInfo.RefGitComposesID
			}
		}

		var projectGitlabResp *gitlab.CreateProjectResponse
		if input.SDKID != nil {
			template, err := u.repo.FindTemplate(ctx, *input.SDKID)
			if err != nil {
				span.SetStatus(codes.Error, "failed to find template")
				span.RecordError(err)
				return err
			}

			reqProject.SourceProjectId = &template.GitRefId
			projectGitlabResp, err = u.gitlab.CreateProjectFromTemplate(ctx, reqProject)
			if err != nil {
				span.SetStatus(codes.Error, "failed to create project from template in gitlab")
				span.RecordError(err)
				return err
			}
		} else {
			projectGitlabResp, err = u.gitlab.CreateProject(ctx, reqProject)
			if err != nil {
				span.SetStatus(codes.Error, "failed to create project in gitlab")
				span.RecordError(err)
				return err
			}
		}

		repository.RefGitRepoID = projectGitlabResp.Id
		err = u.repo.Save(ctx, repository)
		if err != nil {
			span.SetStatus(codes.Error, "failed to save repository entity")
			span.RecordError(err)
			return err
		}
		//Sync repo member inherit from the organization
		now := time.Now()
		if input.Owner.Type == enums.RepoOwnerType_Org {
			orgMembers, err := u.repo.ListMembers(ctx, dto.ListOrgMembersInput{
				OrgId: input.Owner.ID,
			})
			if err != nil {
				span.SetStatus(codes.Error, "failed to list organization members")
				span.RecordError(err)
				return err
			}

			repoMembers := make([]entities.RepoMember, 0, len(orgMembers))
			for _, val := range orgMembers {
				//skip if user is not active
				if val.ExpiresAt != nil && val.ExpiresAt.Before(now) {
					continue
				}

				var repoRole enums.RepoRole
				switch val.Role {
				case enums.OrgRole_Owner:
					repoRole = enums.RepoRole_Owner
				case enums.OrgRole_Developer:
					repoRole = enums.RepoRole_Developer
				}
				repoMembers = append(repoMembers, entities.RepoMember{
					BaseModel: entities.BaseModel{
						ID:        uuid.New(),
						CreatedAt: now,
						UpdatedAt: now,
					},
					UserID:    val.UserID,
					RepoID:    repository.BaseModel.ID,
					Role:      repoRole,
					ExpiresAt: val.ExpiresAt,
				})
				//add user to project gitlab
				accessLevel := gitlab.OrganizationAccessLevel_Owner
				if val.Role == enums.OrgRole_Developer {
					accessLevel = gitlab.OrganizationAccessLevel_Developer
				}

				if val.User != nil {
					gitlabMember, err := u.gitlab.GetMemberOfProject(ctx, gitlab.GetMemberOfProjectRequest{
						RepoId: repository.RefGitRepoID,
						UserId: val.User.RefGitUserID,
						Token:  token,
					})
					if err != nil {
						span.SetStatus(codes.Error, "failed to get member in gitlab project")
						span.RecordError(err)
					}

					if gitlabMember == nil {
						_, err = u.gitlab.AddUserToProject(ctx, gitlab.AddUserToProjectRequest{
							RepoId:      repository.RefGitRepoID,
							UserId:      val.User.RefGitUserID,
							AccessLevel: accessLevel,
							Token:       token,
							ExpiresAt:   utils.ConvertTimeToString(val.ExpiresAt),
						})
						if err != nil {
							span.SetStatus(codes.Error, "failed to add user to project in gitlab")
							span.RecordError(err)
							return err
						}
					}
				}
			}

			if len(repoMembers) > 0 {
				if err := u.repo.Save(ctx, repoMembers); err != nil {
					span.SetStatus(codes.Error, "failed to save repository members")
					span.RecordError(err)
					return err
				}
			}
		} else {
			err = u.repo.CreateRepositoryMember(ctx, &entities.RepoMember{
				UserID: currentUser.ID,
				RepoID: repository.BaseModel.ID,
				Role:   enums.RepoRole_Owner,
			})
			if err != nil {
				span.SetStatus(codes.Error, "failed to create repository member")
				span.RecordError(err)
				return err
			}

			//add user to project
			gitlabMember, err := u.gitlab.GetMemberOfProject(ctx, gitlab.GetMemberOfProjectRequest{
				RepoId: repository.RefGitRepoID,
				UserId: currentUser.RefGitUserID,
				Token:  token,
			})
			if err != nil {
				span.SetStatus(codes.Error, "failed to get member in gitlab project")
				span.RecordError(err)
			}

			if gitlabMember == nil {
				_, err = u.gitlab.AddUserToProject(ctx, gitlab.AddUserToProjectRequest{
					RepoId:      repository.RefGitRepoID,
					UserId:      currentUser.RefGitUserID,
					AccessLevel: gitlab.OrganizationAccessLevel_Owner,
					Token:       token,
				})
				if err != nil {
					span.SetStatus(codes.Error, "failed to add user to project in gitlab")
					span.RecordError(err)
					return err
				}
			}
		}

		//create webhook
		err = u.gitlab.CreateWebhook(ctx, gitlab.CreateProjectWebhookRequest{
			Id:                     repository.RefGitRepoID,
			Token:                  token,
			Name:                   "Push Event Webhook",
			Url:                    fmt.Sprintf("%s%s", u.config.Gitlab.VolvoApiServerHost, "/api/v1/repositories/gitlab/webhook/push_event"),
			PushEvent:              true,
			PushEventsBranchFilter: "*main",
		})
		if err != nil {
			span.SetStatus(codes.Error, "failed to create repository webhook")
			span.RecordError(err)
			return err
		}

		return nil
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to execute transaction")
		span.RecordError(err)
		return nil, err
	}

	span.AddEvent("repository created successfully")
	span.SetStatus(codes.Ok, "repository created successfully")

	return &dto.RepositoryCreateResponse{
		ID:     repoUUID,
		RepoID: *types.NewRepoID(input.Type, namespace, input.Name),
	}, nil
}

// create new user group
func (u *impl) createNewComposeGitGroup(
	ctx context.Context,
	gitlabUserID int64,
	userID uuid.UUID,
	username string,
) (*int64, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.createNewComposeGitGroup")
	defer span.End()

	adminRefGitID := enums.AdminRefGitID
	adminUser, err := u.repo.FindUser(ctx, repository.FindUserFilter{RefGitUserID: &adminRefGitID})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find user")
		span.RecordError(err)
		return nil, err
	}

	var gitlabAdminAccessToken string
	if adminUser.GitlabAccessToken != nil {
		gitlabAdminAccessToken = *adminUser.GitlabAccessToken
	}

	adminGitGroupInfo, err := u.repo.FindUserGitGroup(ctx, adminUser.ID)
	if err != nil {
		span.SetStatus(codes.Error, "failed to find user group")
		span.RecordError(err)
		return nil, err
	}
	if adminGitGroupInfo.RefGitComposesID == nil {
		err := errors.New("admin Compose group not found")
		span.SetStatus(codes.Error, "failed to find user group")
		span.RecordError(err)
		return nil, err
	}

	path := fmt.Sprintf("%s/%s", enums.RepoType_Composes, username)
	groupId, err := u.gitlab.CreateUserGroup(ctx, gitlabUserID, username, path, gitlabAdminAccessToken, *adminGitGroupInfo.RefGitComposesID)
	if err != nil {
		span.SetStatus(codes.Error, "failed to create user group")
		span.RecordError(err)
		return nil, err
	}

	_, err = u.repo.UpdateUserGitGroup(ctx, repository.UpdateUserGitGroupInput{
		UserID:           userID,
		RefGitComposesID: &groupId,
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to create user git group")
		span.RecordError(err)
		return nil, err
	}

	return &groupId, nil
}
