package repository

import (
	"context"
	"errors"

	"go.opentelemetry.io/otel/codes"
	"golang.org/x/sync/errgroup"
	"gorm.io/gorm"

	"api-server/internal/dto"
	"api-server/internal/entities"
	"api-server/internal/enums"
	repository "api-server/internal/repositories"
	"api-server/internal/types"
	"api-server/internal/usecase"
	"api-server/internal/usecase/kompose"
	"api-server/internal/utils"
	"api-server/pkg/oteltrace"
)

func (i *impl) GetListComposeServices(
	ctx context.Context,
	req dto.GetComposeServicesRequest,
) (*dto.GetComposeServicesResponse, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.GetListComposeServices")
	defer span.End()

	if *req.RepoID.RepoType() != enums.RepoType_Composes {
		span.SetStatus(codes.Error, usecase.ErrNotComposeRepo.Error())
		span.RecordError(usecase.ErrNotComposeRepo)
		return nil, usecase.ErrNotComposeRepo
	}

	repo, err := i.repo.FindRepository(ctx, repository.RepositoryFilter{
		Type:      req.RepoID.RepoType(),
		Namespace: req.RepoID.Namespace(),
		Name:      req.RepoID.RepoName(),
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find repository")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, usecase.ErrRepositoryNotFound
		}
		return nil, err
	}

	pagination := types.Pagination{
		PageNo:   req.Page,
		PageSize: req.PerPage,
	}
	order := types.OrderBy{
		req.OrderBy: req.Sort,
	}
	deployments, total, err := i.repo.ListECRDeployment(ctx, pagination, order, repository.ListECRDeploymentInput{
		RepoID:         &repo.ID,
		DeploymentType: utils.Ptr(types.CustomImageDeploymentType_Compose),
		Search:         req.Search,
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to list deployments")
		span.RecordError(err)
		return nil, err
	}

	data := dto.FromManyEntities[entities.CustomImageDeployment, dto.ComposeService](deployments)

	g, gCtx := errgroup.WithContext(ctx)
	g.SetLimit(8)
	for idx, d := range deployments {
		select {
		case <-gCtx.Done():
			break
		default:
			g.Go(func() error {
				data[idx].URL = utils.GetDeploymentURL(
					kompose.ComposeResourceName(kompose.ComposeDeploymentName(repo.Name, d.DeploymentName)),
					i.config.Space.SpaceDomain,
				)

				status, err := kompose.QueryDeploymentStatus(ctx, i.kubeClient, d.DeploymentName, repo.Name)
				if err != nil {
					span.SetStatus(codes.Error, "failed to query deployment status")
					span.RecordError(err)
					return err
				}
				data[idx].Status = status.Status
				return nil
			})
		}
	}

	if err := g.Wait(); err != nil {
		span.SetStatus(codes.Error, "failed to get deployment status")
		span.RecordError(err)
		return nil, err
	}

	return &dto.GetComposeServicesResponse{
		Data: &data,
		Pagination: &dto.Pagination{
			Total:    int(total),
			PageNo:   req.Page,
			PageSize: req.PerPage,
		},
	}, nil
}

// GetComposeRepoDeploymentStatus retrieves the current status of a compose repository deployment.
// It will fetch all deployment from all services in docker-compose and return the status of the deployment.
//
// Parameters:
//   - ctx: Context for the operation
//   - req: Request containing the repository ID
//
// Returns:
//   - *dto.GetDeploymentStatusResponse: Deployment status information
//   - error: Any error that occurred during status retrieval
func (i *impl) GetComposeRepoDeploymentStatus(
	ctx context.Context,
	req dto.GetDeploymentStatusRequest,
) (*dto.GetDeploymentStatusResponse, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.GetComposeRepoDeploymentStatus")
	defer span.End()

	if *req.RepoID.RepoType() != enums.RepoType_Composes {
		span.SetStatus(codes.Error, usecase.ErrNotComposeRepo.Error())
		span.RecordError(usecase.ErrNotComposeRepo)
		return nil, usecase.ErrNotComposeRepo
	}

	repo, err := i.repo.FindRepository(ctx, repository.RepositoryFilter{
		Type:      req.RepoID.RepoType(),
		Namespace: req.RepoID.Namespace(),
		Name:      req.RepoID.RepoName(),
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find repository")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, usecase.ErrRepositoryNotFound
		}
		return nil, err
	}

	// Get all deployments for this repository
	deployments, _, err := i.repo.ListECRDeployment(ctx, types.Pagination{
		PageNo:   1,
		PageSize: 100,
	}, types.OrderBy{
		enums.OrderByColumn_CreatedAt: enums.OrderByDirection_Desc,
	}, repository.ListECRDeploymentInput{
		RepoID:         &repo.ID,
		DeploymentType: utils.Ptr(types.CustomImageDeploymentType_Compose),
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to list deployments")
		span.RecordError(err)
		return nil, err
	}

	// If no deployments, return NotRunning
	if len(deployments) == 0 {
		status := &dto.DeploymentStatus{
			Status: "Not Running",
		}
		return &dto.GetDeploymentStatusResponse{
			Data: status,
		}, nil
	}

	// Collect statuses from all deployments
	statuses := make([]string, len(deployments))
	g, gCtx := errgroup.WithContext(ctx)
	g.SetLimit(8)
	for idx, d := range deployments {
		select {
		case <-gCtx.Done():
			break
		default:
			g.Go(func() error {
				status, err := kompose.QueryDeploymentStatus(ctx, i.kubeClient, d.DeploymentName, repo.Name)
				if err != nil {
					span.SetStatus(codes.Error, "failed to query deployment status")
					span.RecordError(err)
					return err
				}
				statuses[idx] = status.Status
				return nil
			})
		}
	}

	if err := g.Wait(); err != nil {
		span.SetStatus(codes.Error, "failed to get deployment status")
		span.RecordError(err)
		return nil, err
	}

	// Aggregate all statuses
	aggregatedStatus := AggregateDeploymentStatuses(statuses)

	status := &dto.DeploymentStatus{
		Status: aggregatedStatus,
	}

	return &dto.GetDeploymentStatusResponse{
		Data: status,
	}, nil
}

// AggregateDeploymentStatuses aggregates multiple deployment statuses into a single status.
// Returns "Running" if all statuses are "Running", "Failed" if any status is "Failed",
// and the first non-Running status otherwise.
//
// Parameters:
//   - statuses: A slice of status strings to aggregate
//
// Returns:
//   - string: The aggregated status
func AggregateDeploymentStatuses(statuses []string) string {
	if len(statuses) == 0 {
		return "Not Running"
	}

	// Return the first non-Running status as fallback
	for _, status := range statuses {
		if status != "Running" {
			return status
		}
	}

	return "Running" // Should never reach here if there's at least one non-Running status
}
