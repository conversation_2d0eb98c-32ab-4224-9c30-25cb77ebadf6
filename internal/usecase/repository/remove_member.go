package repository

import (
	"context"
	"errors"

	"go.opentelemetry.io/otel/codes"
	"gorm.io/gorm"

	"api-server/internal/dto"
	"api-server/internal/enums"
	"api-server/internal/gateways/gitlab"
	repository "api-server/internal/repositories"
	"api-server/internal/usecase"
	"api-server/pkg/oteltrace"
)

// RemoveMember implements the RepositoryUsecase interface for removing a member from a repository.
// It handles the process of removing a user's membership and associated permissions.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: Input data containing repository ID and member ID
//
// Returns:
//   - error: Any error that occurred during member removal
func (i *impl) RemoveMember(ctx context.Context, input dto.RemoveMemberRepositoryInput) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.RemoveMember")
	defer span.End()

	currentUser, err := i.repo.FindUserByID(ctx, input.CurrentUserID)
	if err != nil {
		span.SetStatus(codes.Error, "failed to find user by id")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return usecase.ErrUserNotFound
		}
		return err
	}

	member, err := i.repo.FindUserByID(ctx, input.MemberId)
	if err != nil {
		span.SetStatus(codes.Error, "failed to find user by id")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return usecase.ErrUserNotFound
		}
		return err
	}

	repo, err := i.repo.FindRepository(ctx, repository.RepositoryFilter{
		Type:      input.RepoID.RepoType(),
		Namespace: input.RepoID.Namespace(),
		Name:      input.RepoID.RepoName(),
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find repository")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return usecase.ErrRepositoryNotFound
		}
		return err
	}

	//get user permission accesstoken
	accessToken, err := getAccessToken(ctx, currentUser, *i, repo, span)
	if err != nil {
		return err
	}

	repoMember, err := i.repo.FindRepositoryMember(ctx, repository.FilterRepoMember{
		RepoID: &repo.ID,
		UserID: &member.ID,
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find repository member")
		span.RecordError(err)
		return err
	}

	if repoMember.Role == enums.RepoRole_Owner {
		span.SetStatus(codes.Error, "cannot remove owner from repository")
		span.RecordError(err)
		return usecase.ErrCannotRemoveOwnerFromRepository
	}

	// remove member in project
	gitlabMember, err := i.gitlab.GetMemberOfProject(ctx, gitlab.GetMemberOfProjectRequest{
		RepoId: repo.RefGitRepoID,
		UserId: member.RefGitUserID,
		Token:  accessToken,
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to get member in gitlab project")
		span.RecordError(err)
	}

	if gitlabMember != nil {
		if err = i.gitlab.RemoveUserFromProject(ctx, gitlab.RemoveUserFromProjectRequest{
			RepoId: repo.RefGitRepoID,
			UserId: member.RefGitUserID,
			Token:  accessToken,
		}); err != nil {
			span.SetStatus(codes.Error, "failed to remove user from project in gitlab")
			span.RecordError(err)
			return err
		}
	}

	err = i.repo.Delete(ctx, repoMember)
	if err != nil {
		span.SetStatus(codes.Error, "failed to delete repository member")
		span.RecordError(err)
		return err
	}

	span.AddEvent("remove member from repository successfully")
	span.SetStatus(codes.Ok, "remove member from repository successfully")
	return nil
}
