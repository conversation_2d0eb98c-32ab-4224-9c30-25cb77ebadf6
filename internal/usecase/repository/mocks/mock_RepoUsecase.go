// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	dto "api-server/internal/dto"
	entities "api-server/internal/entities"
	context "context"

	http "net/http"

	mock "github.com/stretchr/testify/mock"

	types "api-server/internal/types"

	uuid "github.com/google/uuid"
)

// MockRepoUsecase is an autogenerated mock type for the RepoUsecase type
type MockRepoUsecase struct {
	mock.Mock
}

type MockRepoUsecase_Expecter struct {
	mock *mock.Mock
}

func (_m *MockRepoUsecase) EXPECT() *MockRepoUsecase_Expecter {
	return &MockRepoUsecase_Expecter{mock: &_m.Mock}
}

// ArchiveRepo provides a mock function with given fields: ctx, input
func (_m *MockRepoUsecase) ArchiveRepo(ctx context.Context, input dto.ArchiveRepositoryInput) (*http.Response, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for ArchiveRepo")
	}

	var r0 *http.Response
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.ArchiveRepositoryInput) (*http.Response, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, dto.ArchiveRepositoryInput) *http.Response); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*http.Response)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, dto.ArchiveRepositoryInput) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepoUsecase_ArchiveRepo_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ArchiveRepo'
type MockRepoUsecase_ArchiveRepo_Call struct {
	*mock.Call
}

// ArchiveRepo is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.ArchiveRepositoryInput
func (_e *MockRepoUsecase_Expecter) ArchiveRepo(ctx interface{}, input interface{}) *MockRepoUsecase_ArchiveRepo_Call {
	return &MockRepoUsecase_ArchiveRepo_Call{Call: _e.mock.On("ArchiveRepo", ctx, input)}
}

func (_c *MockRepoUsecase_ArchiveRepo_Call) Run(run func(ctx context.Context, input dto.ArchiveRepositoryInput)) *MockRepoUsecase_ArchiveRepo_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.ArchiveRepositoryInput))
	})
	return _c
}

func (_c *MockRepoUsecase_ArchiveRepo_Call) Return(_a0 *http.Response, _a1 error) *MockRepoUsecase_ArchiveRepo_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepoUsecase_ArchiveRepo_Call) RunAndReturn(run func(context.Context, dto.ArchiveRepositoryInput) (*http.Response, error)) *MockRepoUsecase_ArchiveRepo_Call {
	_c.Call.Return(run)
	return _c
}

// BulkCreateEnv provides a mock function with given fields: ctx, repoID, req
func (_m *MockRepoUsecase) BulkCreateEnv(ctx context.Context, repoID types.RepoID, req dto.BulkCreateRepositoryEnvRequest) error {
	ret := _m.Called(ctx, repoID, req)

	if len(ret) == 0 {
		panic("no return value specified for BulkCreateEnv")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, types.RepoID, dto.BulkCreateRepositoryEnvRequest) error); ok {
		r0 = rf(ctx, repoID, req)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepoUsecase_BulkCreateEnv_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'BulkCreateEnv'
type MockRepoUsecase_BulkCreateEnv_Call struct {
	*mock.Call
}

// BulkCreateEnv is a helper method to define mock.On call
//   - ctx context.Context
//   - repoID types.RepoID
//   - req dto.BulkCreateRepositoryEnvRequest
func (_e *MockRepoUsecase_Expecter) BulkCreateEnv(ctx interface{}, repoID interface{}, req interface{}) *MockRepoUsecase_BulkCreateEnv_Call {
	return &MockRepoUsecase_BulkCreateEnv_Call{Call: _e.mock.On("BulkCreateEnv", ctx, repoID, req)}
}

func (_c *MockRepoUsecase_BulkCreateEnv_Call) Run(run func(ctx context.Context, repoID types.RepoID, req dto.BulkCreateRepositoryEnvRequest)) *MockRepoUsecase_BulkCreateEnv_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(types.RepoID), args[2].(dto.BulkCreateRepositoryEnvRequest))
	})
	return _c
}

func (_c *MockRepoUsecase_BulkCreateEnv_Call) Return(_a0 error) *MockRepoUsecase_BulkCreateEnv_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepoUsecase_BulkCreateEnv_Call) RunAndReturn(run func(context.Context, types.RepoID, dto.BulkCreateRepositoryEnvRequest) error) *MockRepoUsecase_BulkCreateEnv_Call {
	_c.Call.Return(run)
	return _c
}

// CreateEnv provides a mock function with given fields: ctx, repoID, req
func (_m *MockRepoUsecase) CreateEnv(ctx context.Context, repoID types.RepoID, req dto.CreateRepositoryEnvRequest) error {
	ret := _m.Called(ctx, repoID, req)

	if len(ret) == 0 {
		panic("no return value specified for CreateEnv")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, types.RepoID, dto.CreateRepositoryEnvRequest) error); ok {
		r0 = rf(ctx, repoID, req)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepoUsecase_CreateEnv_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateEnv'
type MockRepoUsecase_CreateEnv_Call struct {
	*mock.Call
}

// CreateEnv is a helper method to define mock.On call
//   - ctx context.Context
//   - repoID types.RepoID
//   - req dto.CreateRepositoryEnvRequest
func (_e *MockRepoUsecase_Expecter) CreateEnv(ctx interface{}, repoID interface{}, req interface{}) *MockRepoUsecase_CreateEnv_Call {
	return &MockRepoUsecase_CreateEnv_Call{Call: _e.mock.On("CreateEnv", ctx, repoID, req)}
}

func (_c *MockRepoUsecase_CreateEnv_Call) Run(run func(ctx context.Context, repoID types.RepoID, req dto.CreateRepositoryEnvRequest)) *MockRepoUsecase_CreateEnv_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(types.RepoID), args[2].(dto.CreateRepositoryEnvRequest))
	})
	return _c
}

func (_c *MockRepoUsecase_CreateEnv_Call) Return(_a0 error) *MockRepoUsecase_CreateEnv_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepoUsecase_CreateEnv_Call) RunAndReturn(run func(context.Context, types.RepoID, dto.CreateRepositoryEnvRequest) error) *MockRepoUsecase_CreateEnv_Call {
	_c.Call.Return(run)
	return _c
}

// CreateRepoAccessToken provides a mock function with given fields: ctx, req
func (_m *MockRepoUsecase) CreateRepoAccessToken(ctx context.Context, req dto.CreateRepoAccessTokenRequest) (*dto.CreateRepoAccessTokenResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CreateRepoAccessToken")
	}

	var r0 *dto.CreateRepoAccessTokenResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.CreateRepoAccessTokenRequest) (*dto.CreateRepoAccessTokenResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, dto.CreateRepoAccessTokenRequest) *dto.CreateRepoAccessTokenResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*dto.CreateRepoAccessTokenResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, dto.CreateRepoAccessTokenRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepoUsecase_CreateRepoAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateRepoAccessToken'
type MockRepoUsecase_CreateRepoAccessToken_Call struct {
	*mock.Call
}

// CreateRepoAccessToken is a helper method to define mock.On call
//   - ctx context.Context
//   - req dto.CreateRepoAccessTokenRequest
func (_e *MockRepoUsecase_Expecter) CreateRepoAccessToken(ctx interface{}, req interface{}) *MockRepoUsecase_CreateRepoAccessToken_Call {
	return &MockRepoUsecase_CreateRepoAccessToken_Call{Call: _e.mock.On("CreateRepoAccessToken", ctx, req)}
}

func (_c *MockRepoUsecase_CreateRepoAccessToken_Call) Run(run func(ctx context.Context, req dto.CreateRepoAccessTokenRequest)) *MockRepoUsecase_CreateRepoAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.CreateRepoAccessTokenRequest))
	})
	return _c
}

func (_c *MockRepoUsecase_CreateRepoAccessToken_Call) Return(_a0 *dto.CreateRepoAccessTokenResponse, _a1 error) *MockRepoUsecase_CreateRepoAccessToken_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepoUsecase_CreateRepoAccessToken_Call) RunAndReturn(run func(context.Context, dto.CreateRepoAccessTokenRequest) (*dto.CreateRepoAccessTokenResponse, error)) *MockRepoUsecase_CreateRepoAccessToken_Call {
	_c.Call.Return(run)
	return _c
}

// CreateRepoCommit provides a mock function with given fields: ctx, input
func (_m *MockRepoUsecase) CreateRepoCommit(ctx context.Context, input dto.CreateRepositoryCommitInput) (*dto.RepositoryCommit, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for CreateRepoCommit")
	}

	var r0 *dto.RepositoryCommit
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.CreateRepositoryCommitInput) (*dto.RepositoryCommit, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, dto.CreateRepositoryCommitInput) *dto.RepositoryCommit); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*dto.RepositoryCommit)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, dto.CreateRepositoryCommitInput) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepoUsecase_CreateRepoCommit_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateRepoCommit'
type MockRepoUsecase_CreateRepoCommit_Call struct {
	*mock.Call
}

// CreateRepoCommit is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.CreateRepositoryCommitInput
func (_e *MockRepoUsecase_Expecter) CreateRepoCommit(ctx interface{}, input interface{}) *MockRepoUsecase_CreateRepoCommit_Call {
	return &MockRepoUsecase_CreateRepoCommit_Call{Call: _e.mock.On("CreateRepoCommit", ctx, input)}
}

func (_c *MockRepoUsecase_CreateRepoCommit_Call) Run(run func(ctx context.Context, input dto.CreateRepositoryCommitInput)) *MockRepoUsecase_CreateRepoCommit_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.CreateRepositoryCommitInput))
	})
	return _c
}

func (_c *MockRepoUsecase_CreateRepoCommit_Call) Return(_a0 *dto.RepositoryCommit, _a1 error) *MockRepoUsecase_CreateRepoCommit_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepoUsecase_CreateRepoCommit_Call) RunAndReturn(run func(context.Context, dto.CreateRepositoryCommitInput) (*dto.RepositoryCommit, error)) *MockRepoUsecase_CreateRepoCommit_Call {
	_c.Call.Return(run)
	return _c
}

// CreateRepoTag provides a mock function with given fields: ctx, input
func (_m *MockRepoUsecase) CreateRepoTag(ctx context.Context, input dto.CreateRepoTagInput) error {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for CreateRepoTag")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.CreateRepoTagInput) error); ok {
		r0 = rf(ctx, input)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepoUsecase_CreateRepoTag_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateRepoTag'
type MockRepoUsecase_CreateRepoTag_Call struct {
	*mock.Call
}

// CreateRepoTag is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.CreateRepoTagInput
func (_e *MockRepoUsecase_Expecter) CreateRepoTag(ctx interface{}, input interface{}) *MockRepoUsecase_CreateRepoTag_Call {
	return &MockRepoUsecase_CreateRepoTag_Call{Call: _e.mock.On("CreateRepoTag", ctx, input)}
}

func (_c *MockRepoUsecase_CreateRepoTag_Call) Run(run func(ctx context.Context, input dto.CreateRepoTagInput)) *MockRepoUsecase_CreateRepoTag_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.CreateRepoTagInput))
	})
	return _c
}

func (_c *MockRepoUsecase_CreateRepoTag_Call) Return(_a0 error) *MockRepoUsecase_CreateRepoTag_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepoUsecase_CreateRepoTag_Call) RunAndReturn(run func(context.Context, dto.CreateRepoTagInput) error) *MockRepoUsecase_CreateRepoTag_Call {
	_c.Call.Return(run)
	return _c
}

// CreateRepository provides a mock function with given fields: ctx, input
func (_m *MockRepoUsecase) CreateRepository(ctx context.Context, input dto.RepositoryCreateInput) (*dto.RepositoryCreateResponse, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for CreateRepository")
	}

	var r0 *dto.RepositoryCreateResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.RepositoryCreateInput) (*dto.RepositoryCreateResponse, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, dto.RepositoryCreateInput) *dto.RepositoryCreateResponse); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*dto.RepositoryCreateResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, dto.RepositoryCreateInput) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepoUsecase_CreateRepository_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateRepository'
type MockRepoUsecase_CreateRepository_Call struct {
	*mock.Call
}

// CreateRepository is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.RepositoryCreateInput
func (_e *MockRepoUsecase_Expecter) CreateRepository(ctx interface{}, input interface{}) *MockRepoUsecase_CreateRepository_Call {
	return &MockRepoUsecase_CreateRepository_Call{Call: _e.mock.On("CreateRepository", ctx, input)}
}

func (_c *MockRepoUsecase_CreateRepository_Call) Run(run func(ctx context.Context, input dto.RepositoryCreateInput)) *MockRepoUsecase_CreateRepository_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.RepositoryCreateInput))
	})
	return _c
}

func (_c *MockRepoUsecase_CreateRepository_Call) Return(_a0 *dto.RepositoryCreateResponse, _a1 error) *MockRepoUsecase_CreateRepository_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepoUsecase_CreateRepository_Call) RunAndReturn(run func(context.Context, dto.RepositoryCreateInput) (*dto.RepositoryCreateResponse, error)) *MockRepoUsecase_CreateRepository_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteAvatarRepo provides a mock function with given fields: ctx, input
func (_m *MockRepoUsecase) DeleteAvatarRepo(ctx context.Context, input dto.DeleteRepositoryAvatarInput) error {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for DeleteAvatarRepo")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.DeleteRepositoryAvatarInput) error); ok {
		r0 = rf(ctx, input)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepoUsecase_DeleteAvatarRepo_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteAvatarRepo'
type MockRepoUsecase_DeleteAvatarRepo_Call struct {
	*mock.Call
}

// DeleteAvatarRepo is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.DeleteRepositoryAvatarInput
func (_e *MockRepoUsecase_Expecter) DeleteAvatarRepo(ctx interface{}, input interface{}) *MockRepoUsecase_DeleteAvatarRepo_Call {
	return &MockRepoUsecase_DeleteAvatarRepo_Call{Call: _e.mock.On("DeleteAvatarRepo", ctx, input)}
}

func (_c *MockRepoUsecase_DeleteAvatarRepo_Call) Run(run func(ctx context.Context, input dto.DeleteRepositoryAvatarInput)) *MockRepoUsecase_DeleteAvatarRepo_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.DeleteRepositoryAvatarInput))
	})
	return _c
}

func (_c *MockRepoUsecase_DeleteAvatarRepo_Call) Return(_a0 error) *MockRepoUsecase_DeleteAvatarRepo_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepoUsecase_DeleteAvatarRepo_Call) RunAndReturn(run func(context.Context, dto.DeleteRepositoryAvatarInput) error) *MockRepoUsecase_DeleteAvatarRepo_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteEnv provides a mock function with given fields: ctx, repoID, req
func (_m *MockRepoUsecase) DeleteEnv(ctx context.Context, repoID types.RepoID, req dto.DeleteRepositoryEnvRequest) error {
	ret := _m.Called(ctx, repoID, req)

	if len(ret) == 0 {
		panic("no return value specified for DeleteEnv")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, types.RepoID, dto.DeleteRepositoryEnvRequest) error); ok {
		r0 = rf(ctx, repoID, req)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepoUsecase_DeleteEnv_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteEnv'
type MockRepoUsecase_DeleteEnv_Call struct {
	*mock.Call
}

// DeleteEnv is a helper method to define mock.On call
//   - ctx context.Context
//   - repoID types.RepoID
//   - req dto.DeleteRepositoryEnvRequest
func (_e *MockRepoUsecase_Expecter) DeleteEnv(ctx interface{}, repoID interface{}, req interface{}) *MockRepoUsecase_DeleteEnv_Call {
	return &MockRepoUsecase_DeleteEnv_Call{Call: _e.mock.On("DeleteEnv", ctx, repoID, req)}
}

func (_c *MockRepoUsecase_DeleteEnv_Call) Run(run func(ctx context.Context, repoID types.RepoID, req dto.DeleteRepositoryEnvRequest)) *MockRepoUsecase_DeleteEnv_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(types.RepoID), args[2].(dto.DeleteRepositoryEnvRequest))
	})
	return _c
}

func (_c *MockRepoUsecase_DeleteEnv_Call) Return(_a0 error) *MockRepoUsecase_DeleteEnv_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepoUsecase_DeleteEnv_Call) RunAndReturn(run func(context.Context, types.RepoID, dto.DeleteRepositoryEnvRequest) error) *MockRepoUsecase_DeleteEnv_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteRepoAccessToken provides a mock function with given fields: ctx, userID, repoID, accessTokenID
func (_m *MockRepoUsecase) DeleteRepoAccessToken(ctx context.Context, userID uuid.UUID, repoID types.RepoID, accessTokenID uuid.UUID) error {
	ret := _m.Called(ctx, userID, repoID, accessTokenID)

	if len(ret) == 0 {
		panic("no return value specified for DeleteRepoAccessToken")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID, types.RepoID, uuid.UUID) error); ok {
		r0 = rf(ctx, userID, repoID, accessTokenID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepoUsecase_DeleteRepoAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteRepoAccessToken'
type MockRepoUsecase_DeleteRepoAccessToken_Call struct {
	*mock.Call
}

// DeleteRepoAccessToken is a helper method to define mock.On call
//   - ctx context.Context
//   - userID uuid.UUID
//   - repoID types.RepoID
//   - accessTokenID uuid.UUID
func (_e *MockRepoUsecase_Expecter) DeleteRepoAccessToken(ctx interface{}, userID interface{}, repoID interface{}, accessTokenID interface{}) *MockRepoUsecase_DeleteRepoAccessToken_Call {
	return &MockRepoUsecase_DeleteRepoAccessToken_Call{Call: _e.mock.On("DeleteRepoAccessToken", ctx, userID, repoID, accessTokenID)}
}

func (_c *MockRepoUsecase_DeleteRepoAccessToken_Call) Run(run func(ctx context.Context, userID uuid.UUID, repoID types.RepoID, accessTokenID uuid.UUID)) *MockRepoUsecase_DeleteRepoAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID), args[2].(types.RepoID), args[3].(uuid.UUID))
	})
	return _c
}

func (_c *MockRepoUsecase_DeleteRepoAccessToken_Call) Return(_a0 error) *MockRepoUsecase_DeleteRepoAccessToken_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepoUsecase_DeleteRepoAccessToken_Call) RunAndReturn(run func(context.Context, uuid.UUID, types.RepoID, uuid.UUID) error) *MockRepoUsecase_DeleteRepoAccessToken_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteRepoTag provides a mock function with given fields: ctx, input
func (_m *MockRepoUsecase) DeleteRepoTag(ctx context.Context, input dto.DeleteRepoTagInput) error {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for DeleteRepoTag")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.DeleteRepoTagInput) error); ok {
		r0 = rf(ctx, input)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepoUsecase_DeleteRepoTag_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteRepoTag'
type MockRepoUsecase_DeleteRepoTag_Call struct {
	*mock.Call
}

// DeleteRepoTag is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.DeleteRepoTagInput
func (_e *MockRepoUsecase_Expecter) DeleteRepoTag(ctx interface{}, input interface{}) *MockRepoUsecase_DeleteRepoTag_Call {
	return &MockRepoUsecase_DeleteRepoTag_Call{Call: _e.mock.On("DeleteRepoTag", ctx, input)}
}

func (_c *MockRepoUsecase_DeleteRepoTag_Call) Run(run func(ctx context.Context, input dto.DeleteRepoTagInput)) *MockRepoUsecase_DeleteRepoTag_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.DeleteRepoTagInput))
	})
	return _c
}

func (_c *MockRepoUsecase_DeleteRepoTag_Call) Return(_a0 error) *MockRepoUsecase_DeleteRepoTag_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepoUsecase_DeleteRepoTag_Call) RunAndReturn(run func(context.Context, dto.DeleteRepoTagInput) error) *MockRepoUsecase_DeleteRepoTag_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteRepository provides a mock function with given fields: ctx, input
func (_m *MockRepoUsecase) DeleteRepository(ctx context.Context, input dto.DeleteRepositoryInput) error {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for DeleteRepository")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.DeleteRepositoryInput) error); ok {
		r0 = rf(ctx, input)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepoUsecase_DeleteRepository_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteRepository'
type MockRepoUsecase_DeleteRepository_Call struct {
	*mock.Call
}

// DeleteRepository is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.DeleteRepositoryInput
func (_e *MockRepoUsecase_Expecter) DeleteRepository(ctx interface{}, input interface{}) *MockRepoUsecase_DeleteRepository_Call {
	return &MockRepoUsecase_DeleteRepository_Call{Call: _e.mock.On("DeleteRepository", ctx, input)}
}

func (_c *MockRepoUsecase_DeleteRepository_Call) Run(run func(ctx context.Context, input dto.DeleteRepositoryInput)) *MockRepoUsecase_DeleteRepository_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.DeleteRepositoryInput))
	})
	return _c
}

func (_c *MockRepoUsecase_DeleteRepository_Call) Return(_a0 error) *MockRepoUsecase_DeleteRepository_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepoUsecase_DeleteRepository_Call) RunAndReturn(run func(context.Context, dto.DeleteRepositoryInput) error) *MockRepoUsecase_DeleteRepository_Call {
	_c.Call.Return(run)
	return _c
}

// GetAllRepositories provides a mock function with given fields: ctx, input
func (_m *MockRepoUsecase) GetAllRepositories(ctx context.Context, input dto.GetRepositoriesInput) (*dto.GetRepositoriesOutput, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for GetAllRepositories")
	}

	var r0 *dto.GetRepositoriesOutput
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.GetRepositoriesInput) (*dto.GetRepositoriesOutput, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, dto.GetRepositoriesInput) *dto.GetRepositoriesOutput); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*dto.GetRepositoriesOutput)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, dto.GetRepositoriesInput) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepoUsecase_GetAllRepositories_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllRepositories'
type MockRepoUsecase_GetAllRepositories_Call struct {
	*mock.Call
}

// GetAllRepositories is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.GetRepositoriesInput
func (_e *MockRepoUsecase_Expecter) GetAllRepositories(ctx interface{}, input interface{}) *MockRepoUsecase_GetAllRepositories_Call {
	return &MockRepoUsecase_GetAllRepositories_Call{Call: _e.mock.On("GetAllRepositories", ctx, input)}
}

func (_c *MockRepoUsecase_GetAllRepositories_Call) Run(run func(ctx context.Context, input dto.GetRepositoriesInput)) *MockRepoUsecase_GetAllRepositories_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.GetRepositoriesInput))
	})
	return _c
}

func (_c *MockRepoUsecase_GetAllRepositories_Call) Return(_a0 *dto.GetRepositoriesOutput, _a1 error) *MockRepoUsecase_GetAllRepositories_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepoUsecase_GetAllRepositories_Call) RunAndReturn(run func(context.Context, dto.GetRepositoriesInput) (*dto.GetRepositoriesOutput, error)) *MockRepoUsecase_GetAllRepositories_Call {
	_c.Call.Return(run)
	return _c
}

// GetComposeRepoDeploymentStatus provides a mock function with given fields: ctx, req
func (_m *MockRepoUsecase) GetComposeRepoDeploymentStatus(ctx context.Context, req dto.GetDeploymentStatusRequest) (*dto.GetDeploymentStatusResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetComposeRepoDeploymentStatus")
	}

	var r0 *dto.GetDeploymentStatusResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.GetDeploymentStatusRequest) (*dto.GetDeploymentStatusResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, dto.GetDeploymentStatusRequest) *dto.GetDeploymentStatusResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*dto.GetDeploymentStatusResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, dto.GetDeploymentStatusRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepoUsecase_GetComposeRepoDeploymentStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetComposeRepoDeploymentStatus'
type MockRepoUsecase_GetComposeRepoDeploymentStatus_Call struct {
	*mock.Call
}

// GetComposeRepoDeploymentStatus is a helper method to define mock.On call
//   - ctx context.Context
//   - req dto.GetDeploymentStatusRequest
func (_e *MockRepoUsecase_Expecter) GetComposeRepoDeploymentStatus(ctx interface{}, req interface{}) *MockRepoUsecase_GetComposeRepoDeploymentStatus_Call {
	return &MockRepoUsecase_GetComposeRepoDeploymentStatus_Call{Call: _e.mock.On("GetComposeRepoDeploymentStatus", ctx, req)}
}

func (_c *MockRepoUsecase_GetComposeRepoDeploymentStatus_Call) Run(run func(ctx context.Context, req dto.GetDeploymentStatusRequest)) *MockRepoUsecase_GetComposeRepoDeploymentStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.GetDeploymentStatusRequest))
	})
	return _c
}

func (_c *MockRepoUsecase_GetComposeRepoDeploymentStatus_Call) Return(_a0 *dto.GetDeploymentStatusResponse, _a1 error) *MockRepoUsecase_GetComposeRepoDeploymentStatus_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepoUsecase_GetComposeRepoDeploymentStatus_Call) RunAndReturn(run func(context.Context, dto.GetDeploymentStatusRequest) (*dto.GetDeploymentStatusResponse, error)) *MockRepoUsecase_GetComposeRepoDeploymentStatus_Call {
	_c.Call.Return(run)
	return _c
}

// GetDeploymentBuildLogs provides a mock function with given fields: ctx, req
func (_m *MockRepoUsecase) GetDeploymentBuildLogs(ctx context.Context, req dto.GetDeploymentLogsRequest) (chan dto.GetDeploymentLogsResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetDeploymentBuildLogs")
	}

	var r0 chan dto.GetDeploymentLogsResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.GetDeploymentLogsRequest) (chan dto.GetDeploymentLogsResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, dto.GetDeploymentLogsRequest) chan dto.GetDeploymentLogsResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(chan dto.GetDeploymentLogsResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, dto.GetDeploymentLogsRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepoUsecase_GetDeploymentBuildLogs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDeploymentBuildLogs'
type MockRepoUsecase_GetDeploymentBuildLogs_Call struct {
	*mock.Call
}

// GetDeploymentBuildLogs is a helper method to define mock.On call
//   - ctx context.Context
//   - req dto.GetDeploymentLogsRequest
func (_e *MockRepoUsecase_Expecter) GetDeploymentBuildLogs(ctx interface{}, req interface{}) *MockRepoUsecase_GetDeploymentBuildLogs_Call {
	return &MockRepoUsecase_GetDeploymentBuildLogs_Call{Call: _e.mock.On("GetDeploymentBuildLogs", ctx, req)}
}

func (_c *MockRepoUsecase_GetDeploymentBuildLogs_Call) Run(run func(ctx context.Context, req dto.GetDeploymentLogsRequest)) *MockRepoUsecase_GetDeploymentBuildLogs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.GetDeploymentLogsRequest))
	})
	return _c
}

func (_c *MockRepoUsecase_GetDeploymentBuildLogs_Call) Return(_a0 chan dto.GetDeploymentLogsResponse, _a1 error) *MockRepoUsecase_GetDeploymentBuildLogs_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepoUsecase_GetDeploymentBuildLogs_Call) RunAndReturn(run func(context.Context, dto.GetDeploymentLogsRequest) (chan dto.GetDeploymentLogsResponse, error)) *MockRepoUsecase_GetDeploymentBuildLogs_Call {
	_c.Call.Return(run)
	return _c
}

// GetDeploymentStatus provides a mock function with given fields: ctx, req
func (_m *MockRepoUsecase) GetDeploymentStatus(ctx context.Context, req dto.GetDeploymentStatusRequest) (*dto.GetDeploymentStatusResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetDeploymentStatus")
	}

	var r0 *dto.GetDeploymentStatusResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.GetDeploymentStatusRequest) (*dto.GetDeploymentStatusResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, dto.GetDeploymentStatusRequest) *dto.GetDeploymentStatusResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*dto.GetDeploymentStatusResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, dto.GetDeploymentStatusRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepoUsecase_GetDeploymentStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDeploymentStatus'
type MockRepoUsecase_GetDeploymentStatus_Call struct {
	*mock.Call
}

// GetDeploymentStatus is a helper method to define mock.On call
//   - ctx context.Context
//   - req dto.GetDeploymentStatusRequest
func (_e *MockRepoUsecase_Expecter) GetDeploymentStatus(ctx interface{}, req interface{}) *MockRepoUsecase_GetDeploymentStatus_Call {
	return &MockRepoUsecase_GetDeploymentStatus_Call{Call: _e.mock.On("GetDeploymentStatus", ctx, req)}
}

func (_c *MockRepoUsecase_GetDeploymentStatus_Call) Run(run func(ctx context.Context, req dto.GetDeploymentStatusRequest)) *MockRepoUsecase_GetDeploymentStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.GetDeploymentStatusRequest))
	})
	return _c
}

func (_c *MockRepoUsecase_GetDeploymentStatus_Call) Return(_a0 *dto.GetDeploymentStatusResponse, _a1 error) *MockRepoUsecase_GetDeploymentStatus_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepoUsecase_GetDeploymentStatus_Call) RunAndReturn(run func(context.Context, dto.GetDeploymentStatusRequest) (*dto.GetDeploymentStatusResponse, error)) *MockRepoUsecase_GetDeploymentStatus_Call {
	_c.Call.Return(run)
	return _c
}

// GetEnv provides a mock function with given fields: ctx, repoID
func (_m *MockRepoUsecase) GetEnv(ctx context.Context, repoID types.RepoID) (*dto.GetRepositoryEnvResponse, error) {
	ret := _m.Called(ctx, repoID)

	if len(ret) == 0 {
		panic("no return value specified for GetEnv")
	}

	var r0 *dto.GetRepositoryEnvResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, types.RepoID) (*dto.GetRepositoryEnvResponse, error)); ok {
		return rf(ctx, repoID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, types.RepoID) *dto.GetRepositoryEnvResponse); ok {
		r0 = rf(ctx, repoID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*dto.GetRepositoryEnvResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, types.RepoID) error); ok {
		r1 = rf(ctx, repoID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepoUsecase_GetEnv_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetEnv'
type MockRepoUsecase_GetEnv_Call struct {
	*mock.Call
}

// GetEnv is a helper method to define mock.On call
//   - ctx context.Context
//   - repoID types.RepoID
func (_e *MockRepoUsecase_Expecter) GetEnv(ctx interface{}, repoID interface{}) *MockRepoUsecase_GetEnv_Call {
	return &MockRepoUsecase_GetEnv_Call{Call: _e.mock.On("GetEnv", ctx, repoID)}
}

func (_c *MockRepoUsecase_GetEnv_Call) Run(run func(ctx context.Context, repoID types.RepoID)) *MockRepoUsecase_GetEnv_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(types.RepoID))
	})
	return _c
}

func (_c *MockRepoUsecase_GetEnv_Call) Return(_a0 *dto.GetRepositoryEnvResponse, _a1 error) *MockRepoUsecase_GetEnv_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepoUsecase_GetEnv_Call) RunAndReturn(run func(context.Context, types.RepoID) (*dto.GetRepositoryEnvResponse, error)) *MockRepoUsecase_GetEnv_Call {
	_c.Call.Return(run)
	return _c
}

// GetFileFromRepository provides a mock function with given fields: ctx, input
func (_m *MockRepoUsecase) GetFileFromRepository(ctx context.Context, input dto.GetFileFromRepositoryInput) (*dto.GetFileFromRepositoryOutput, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for GetFileFromRepository")
	}

	var r0 *dto.GetFileFromRepositoryOutput
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.GetFileFromRepositoryInput) (*dto.GetFileFromRepositoryOutput, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, dto.GetFileFromRepositoryInput) *dto.GetFileFromRepositoryOutput); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*dto.GetFileFromRepositoryOutput)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, dto.GetFileFromRepositoryInput) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepoUsecase_GetFileFromRepository_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFileFromRepository'
type MockRepoUsecase_GetFileFromRepository_Call struct {
	*mock.Call
}

// GetFileFromRepository is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.GetFileFromRepositoryInput
func (_e *MockRepoUsecase_Expecter) GetFileFromRepository(ctx interface{}, input interface{}) *MockRepoUsecase_GetFileFromRepository_Call {
	return &MockRepoUsecase_GetFileFromRepository_Call{Call: _e.mock.On("GetFileFromRepository", ctx, input)}
}

func (_c *MockRepoUsecase_GetFileFromRepository_Call) Run(run func(ctx context.Context, input dto.GetFileFromRepositoryInput)) *MockRepoUsecase_GetFileFromRepository_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.GetFileFromRepositoryInput))
	})
	return _c
}

func (_c *MockRepoUsecase_GetFileFromRepository_Call) Return(_a0 *dto.GetFileFromRepositoryOutput, _a1 error) *MockRepoUsecase_GetFileFromRepository_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepoUsecase_GetFileFromRepository_Call) RunAndReturn(run func(context.Context, dto.GetFileFromRepositoryInput) (*dto.GetFileFromRepositoryOutput, error)) *MockRepoUsecase_GetFileFromRepository_Call {
	_c.Call.Return(run)
	return _c
}

// GetHeaderRawFileFromRepository provides a mock function with given fields: ctx, input
func (_m *MockRepoUsecase) GetHeaderRawFileFromRepository(ctx context.Context, input dto.GetFileFromRepositoryInput) (*http.Response, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for GetHeaderRawFileFromRepository")
	}

	var r0 *http.Response
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.GetFileFromRepositoryInput) (*http.Response, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, dto.GetFileFromRepositoryInput) *http.Response); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*http.Response)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, dto.GetFileFromRepositoryInput) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepoUsecase_GetHeaderRawFileFromRepository_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetHeaderRawFileFromRepository'
type MockRepoUsecase_GetHeaderRawFileFromRepository_Call struct {
	*mock.Call
}

// GetHeaderRawFileFromRepository is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.GetFileFromRepositoryInput
func (_e *MockRepoUsecase_Expecter) GetHeaderRawFileFromRepository(ctx interface{}, input interface{}) *MockRepoUsecase_GetHeaderRawFileFromRepository_Call {
	return &MockRepoUsecase_GetHeaderRawFileFromRepository_Call{Call: _e.mock.On("GetHeaderRawFileFromRepository", ctx, input)}
}

func (_c *MockRepoUsecase_GetHeaderRawFileFromRepository_Call) Run(run func(ctx context.Context, input dto.GetFileFromRepositoryInput)) *MockRepoUsecase_GetHeaderRawFileFromRepository_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.GetFileFromRepositoryInput))
	})
	return _c
}

func (_c *MockRepoUsecase_GetHeaderRawFileFromRepository_Call) Return(_a0 *http.Response, _a1 error) *MockRepoUsecase_GetHeaderRawFileFromRepository_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepoUsecase_GetHeaderRawFileFromRepository_Call) RunAndReturn(run func(context.Context, dto.GetFileFromRepositoryInput) (*http.Response, error)) *MockRepoUsecase_GetHeaderRawFileFromRepository_Call {
	_c.Call.Return(run)
	return _c
}

// GetLastRepoCommit provides a mock function with given fields: ctx, input
func (_m *MockRepoUsecase) GetLastRepoCommit(ctx context.Context, input dto.GetRepositoryCommitsInput) (*dto.RepositoryCommit, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for GetLastRepoCommit")
	}

	var r0 *dto.RepositoryCommit
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.GetRepositoryCommitsInput) (*dto.RepositoryCommit, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, dto.GetRepositoryCommitsInput) *dto.RepositoryCommit); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*dto.RepositoryCommit)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, dto.GetRepositoryCommitsInput) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepoUsecase_GetLastRepoCommit_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetLastRepoCommit'
type MockRepoUsecase_GetLastRepoCommit_Call struct {
	*mock.Call
}

// GetLastRepoCommit is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.GetRepositoryCommitsInput
func (_e *MockRepoUsecase_Expecter) GetLastRepoCommit(ctx interface{}, input interface{}) *MockRepoUsecase_GetLastRepoCommit_Call {
	return &MockRepoUsecase_GetLastRepoCommit_Call{Call: _e.mock.On("GetLastRepoCommit", ctx, input)}
}

func (_c *MockRepoUsecase_GetLastRepoCommit_Call) Run(run func(ctx context.Context, input dto.GetRepositoryCommitsInput)) *MockRepoUsecase_GetLastRepoCommit_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.GetRepositoryCommitsInput))
	})
	return _c
}

func (_c *MockRepoUsecase_GetLastRepoCommit_Call) Return(_a0 *dto.RepositoryCommit, _a1 error) *MockRepoUsecase_GetLastRepoCommit_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepoUsecase_GetLastRepoCommit_Call) RunAndReturn(run func(context.Context, dto.GetRepositoryCommitsInput) (*dto.RepositoryCommit, error)) *MockRepoUsecase_GetLastRepoCommit_Call {
	_c.Call.Return(run)
	return _c
}

// GetListComposeServices provides a mock function with given fields: ctx, req
func (_m *MockRepoUsecase) GetListComposeServices(ctx context.Context, req dto.GetComposeServicesRequest) (*dto.GetComposeServicesResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetListComposeServices")
	}

	var r0 *dto.GetComposeServicesResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.GetComposeServicesRequest) (*dto.GetComposeServicesResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, dto.GetComposeServicesRequest) *dto.GetComposeServicesResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*dto.GetComposeServicesResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, dto.GetComposeServicesRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepoUsecase_GetListComposeServices_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetListComposeServices'
type MockRepoUsecase_GetListComposeServices_Call struct {
	*mock.Call
}

// GetListComposeServices is a helper method to define mock.On call
//   - ctx context.Context
//   - req dto.GetComposeServicesRequest
func (_e *MockRepoUsecase_Expecter) GetListComposeServices(ctx interface{}, req interface{}) *MockRepoUsecase_GetListComposeServices_Call {
	return &MockRepoUsecase_GetListComposeServices_Call{Call: _e.mock.On("GetListComposeServices", ctx, req)}
}

func (_c *MockRepoUsecase_GetListComposeServices_Call) Run(run func(ctx context.Context, req dto.GetComposeServicesRequest)) *MockRepoUsecase_GetListComposeServices_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.GetComposeServicesRequest))
	})
	return _c
}

func (_c *MockRepoUsecase_GetListComposeServices_Call) Return(_a0 *dto.GetComposeServicesResponse, _a1 error) *MockRepoUsecase_GetListComposeServices_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepoUsecase_GetListComposeServices_Call) RunAndReturn(run func(context.Context, dto.GetComposeServicesRequest) (*dto.GetComposeServicesResponse, error)) *MockRepoUsecase_GetListComposeServices_Call {
	_c.Call.Return(run)
	return _c
}

// GetMember provides a mock function with given fields: ctx, input
func (_m *MockRepoUsecase) GetMember(ctx context.Context, input dto.GetMemberRepositoryInput) (*entities.RepoMember, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for GetMember")
	}

	var r0 *entities.RepoMember
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.GetMemberRepositoryInput) (*entities.RepoMember, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, dto.GetMemberRepositoryInput) *entities.RepoMember); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.RepoMember)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, dto.GetMemberRepositoryInput) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepoUsecase_GetMember_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetMember'
type MockRepoUsecase_GetMember_Call struct {
	*mock.Call
}

// GetMember is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.GetMemberRepositoryInput
func (_e *MockRepoUsecase_Expecter) GetMember(ctx interface{}, input interface{}) *MockRepoUsecase_GetMember_Call {
	return &MockRepoUsecase_GetMember_Call{Call: _e.mock.On("GetMember", ctx, input)}
}

func (_c *MockRepoUsecase_GetMember_Call) Run(run func(ctx context.Context, input dto.GetMemberRepositoryInput)) *MockRepoUsecase_GetMember_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.GetMemberRepositoryInput))
	})
	return _c
}

func (_c *MockRepoUsecase_GetMember_Call) Return(_a0 *entities.RepoMember, _a1 error) *MockRepoUsecase_GetMember_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepoUsecase_GetMember_Call) RunAndReturn(run func(context.Context, dto.GetMemberRepositoryInput) (*entities.RepoMember, error)) *MockRepoUsecase_GetMember_Call {
	_c.Call.Return(run)
	return _c
}

// GetPodLogs provides a mock function with given fields: ctx, req
func (_m *MockRepoUsecase) GetPodLogs(ctx context.Context, req dto.GetDeploymentLogsRequest) (chan dto.GetDeploymentLogsResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetPodLogs")
	}

	var r0 chan dto.GetDeploymentLogsResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.GetDeploymentLogsRequest) (chan dto.GetDeploymentLogsResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, dto.GetDeploymentLogsRequest) chan dto.GetDeploymentLogsResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(chan dto.GetDeploymentLogsResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, dto.GetDeploymentLogsRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepoUsecase_GetPodLogs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetPodLogs'
type MockRepoUsecase_GetPodLogs_Call struct {
	*mock.Call
}

// GetPodLogs is a helper method to define mock.On call
//   - ctx context.Context
//   - req dto.GetDeploymentLogsRequest
func (_e *MockRepoUsecase_Expecter) GetPodLogs(ctx interface{}, req interface{}) *MockRepoUsecase_GetPodLogs_Call {
	return &MockRepoUsecase_GetPodLogs_Call{Call: _e.mock.On("GetPodLogs", ctx, req)}
}

func (_c *MockRepoUsecase_GetPodLogs_Call) Run(run func(ctx context.Context, req dto.GetDeploymentLogsRequest)) *MockRepoUsecase_GetPodLogs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.GetDeploymentLogsRequest))
	})
	return _c
}

func (_c *MockRepoUsecase_GetPodLogs_Call) Return(_a0 chan dto.GetDeploymentLogsResponse, _a1 error) *MockRepoUsecase_GetPodLogs_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepoUsecase_GetPodLogs_Call) RunAndReturn(run func(context.Context, dto.GetDeploymentLogsRequest) (chan dto.GetDeploymentLogsResponse, error)) *MockRepoUsecase_GetPodLogs_Call {
	_c.Call.Return(run)
	return _c
}

// GetRawFileFromRepository provides a mock function with given fields: ctx, input
func (_m *MockRepoUsecase) GetRawFileFromRepository(ctx context.Context, input dto.GetFileFromRepositoryInput) (*http.Response, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for GetRawFileFromRepository")
	}

	var r0 *http.Response
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.GetFileFromRepositoryInput) (*http.Response, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, dto.GetFileFromRepositoryInput) *http.Response); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*http.Response)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, dto.GetFileFromRepositoryInput) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepoUsecase_GetRawFileFromRepository_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetRawFileFromRepository'
type MockRepoUsecase_GetRawFileFromRepository_Call struct {
	*mock.Call
}

// GetRawFileFromRepository is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.GetFileFromRepositoryInput
func (_e *MockRepoUsecase_Expecter) GetRawFileFromRepository(ctx interface{}, input interface{}) *MockRepoUsecase_GetRawFileFromRepository_Call {
	return &MockRepoUsecase_GetRawFileFromRepository_Call{Call: _e.mock.On("GetRawFileFromRepository", ctx, input)}
}

func (_c *MockRepoUsecase_GetRawFileFromRepository_Call) Run(run func(ctx context.Context, input dto.GetFileFromRepositoryInput)) *MockRepoUsecase_GetRawFileFromRepository_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.GetFileFromRepositoryInput))
	})
	return _c
}

func (_c *MockRepoUsecase_GetRawFileFromRepository_Call) Return(_a0 *http.Response, _a1 error) *MockRepoUsecase_GetRawFileFromRepository_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepoUsecase_GetRawFileFromRepository_Call) RunAndReturn(run func(context.Context, dto.GetFileFromRepositoryInput) (*http.Response, error)) *MockRepoUsecase_GetRawFileFromRepository_Call {
	_c.Call.Return(run)
	return _c
}

// GetRepoInfo provides a mock function with given fields: ctx, input
func (_m *MockRepoUsecase) GetRepoInfo(ctx context.Context, input dto.GetRepositoryInput) (*dto.GetRepositoryOutput, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for GetRepoInfo")
	}

	var r0 *dto.GetRepositoryOutput
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.GetRepositoryInput) (*dto.GetRepositoryOutput, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, dto.GetRepositoryInput) *dto.GetRepositoryOutput); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*dto.GetRepositoryOutput)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, dto.GetRepositoryInput) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepoUsecase_GetRepoInfo_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetRepoInfo'
type MockRepoUsecase_GetRepoInfo_Call struct {
	*mock.Call
}

// GetRepoInfo is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.GetRepositoryInput
func (_e *MockRepoUsecase_Expecter) GetRepoInfo(ctx interface{}, input interface{}) *MockRepoUsecase_GetRepoInfo_Call {
	return &MockRepoUsecase_GetRepoInfo_Call{Call: _e.mock.On("GetRepoInfo", ctx, input)}
}

func (_c *MockRepoUsecase_GetRepoInfo_Call) Run(run func(ctx context.Context, input dto.GetRepositoryInput)) *MockRepoUsecase_GetRepoInfo_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.GetRepositoryInput))
	})
	return _c
}

func (_c *MockRepoUsecase_GetRepoInfo_Call) Return(_a0 *dto.GetRepositoryOutput, _a1 error) *MockRepoUsecase_GetRepoInfo_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepoUsecase_GetRepoInfo_Call) RunAndReturn(run func(context.Context, dto.GetRepositoryInput) (*dto.GetRepositoryOutput, error)) *MockRepoUsecase_GetRepoInfo_Call {
	_c.Call.Return(run)
	return _c
}

// GetRepoTag provides a mock function with given fields: ctx, input
func (_m *MockRepoUsecase) GetRepoTag(ctx context.Context, input dto.GetRepoTagInput) (*entities.Tag, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for GetRepoTag")
	}

	var r0 *entities.Tag
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.GetRepoTagInput) (*entities.Tag, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, dto.GetRepoTagInput) *entities.Tag); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.Tag)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, dto.GetRepoTagInput) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepoUsecase_GetRepoTag_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetRepoTag'
type MockRepoUsecase_GetRepoTag_Call struct {
	*mock.Call
}

// GetRepoTag is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.GetRepoTagInput
func (_e *MockRepoUsecase_Expecter) GetRepoTag(ctx interface{}, input interface{}) *MockRepoUsecase_GetRepoTag_Call {
	return &MockRepoUsecase_GetRepoTag_Call{Call: _e.mock.On("GetRepoTag", ctx, input)}
}

func (_c *MockRepoUsecase_GetRepoTag_Call) Run(run func(ctx context.Context, input dto.GetRepoTagInput)) *MockRepoUsecase_GetRepoTag_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.GetRepoTagInput))
	})
	return _c
}

func (_c *MockRepoUsecase_GetRepoTag_Call) Return(_a0 *entities.Tag, _a1 error) *MockRepoUsecase_GetRepoTag_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepoUsecase_GetRepoTag_Call) RunAndReturn(run func(context.Context, dto.GetRepoTagInput) (*entities.Tag, error)) *MockRepoUsecase_GetRepoTag_Call {
	_c.Call.Return(run)
	return _c
}

// GetSingleRepoBranch provides a mock function with given fields: ctx, input
func (_m *MockRepoUsecase) GetSingleRepoBranch(ctx context.Context, input dto.GetSingleRepositoryBranchInput) (*dto.RepositoryBranchInfo, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for GetSingleRepoBranch")
	}

	var r0 *dto.RepositoryBranchInfo
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.GetSingleRepositoryBranchInput) (*dto.RepositoryBranchInfo, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, dto.GetSingleRepositoryBranchInput) *dto.RepositoryBranchInfo); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*dto.RepositoryBranchInfo)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, dto.GetSingleRepositoryBranchInput) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepoUsecase_GetSingleRepoBranch_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetSingleRepoBranch'
type MockRepoUsecase_GetSingleRepoBranch_Call struct {
	*mock.Call
}

// GetSingleRepoBranch is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.GetSingleRepositoryBranchInput
func (_e *MockRepoUsecase_Expecter) GetSingleRepoBranch(ctx interface{}, input interface{}) *MockRepoUsecase_GetSingleRepoBranch_Call {
	return &MockRepoUsecase_GetSingleRepoBranch_Call{Call: _e.mock.On("GetSingleRepoBranch", ctx, input)}
}

func (_c *MockRepoUsecase_GetSingleRepoBranch_Call) Run(run func(ctx context.Context, input dto.GetSingleRepositoryBranchInput)) *MockRepoUsecase_GetSingleRepoBranch_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.GetSingleRepositoryBranchInput))
	})
	return _c
}

func (_c *MockRepoUsecase_GetSingleRepoBranch_Call) Return(_a0 *dto.RepositoryBranchInfo, _a1 error) *MockRepoUsecase_GetSingleRepoBranch_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepoUsecase_GetSingleRepoBranch_Call) RunAndReturn(run func(context.Context, dto.GetSingleRepositoryBranchInput) (*dto.RepositoryBranchInfo, error)) *MockRepoUsecase_GetSingleRepoBranch_Call {
	_c.Call.Return(run)
	return _c
}

// InviteMultipleRepoMembers provides a mock function with given fields: ctx, input
func (_m *MockRepoUsecase) InviteMultipleRepoMembers(ctx context.Context, input dto.InviteRepoMembersInput) error {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for InviteMultipleRepoMembers")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.InviteRepoMembersInput) error); ok {
		r0 = rf(ctx, input)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepoUsecase_InviteMultipleRepoMembers_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'InviteMultipleRepoMembers'
type MockRepoUsecase_InviteMultipleRepoMembers_Call struct {
	*mock.Call
}

// InviteMultipleRepoMembers is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.InviteRepoMembersInput
func (_e *MockRepoUsecase_Expecter) InviteMultipleRepoMembers(ctx interface{}, input interface{}) *MockRepoUsecase_InviteMultipleRepoMembers_Call {
	return &MockRepoUsecase_InviteMultipleRepoMembers_Call{Call: _e.mock.On("InviteMultipleRepoMembers", ctx, input)}
}

func (_c *MockRepoUsecase_InviteMultipleRepoMembers_Call) Run(run func(ctx context.Context, input dto.InviteRepoMembersInput)) *MockRepoUsecase_InviteMultipleRepoMembers_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.InviteRepoMembersInput))
	})
	return _c
}

func (_c *MockRepoUsecase_InviteMultipleRepoMembers_Call) Return(_a0 error) *MockRepoUsecase_InviteMultipleRepoMembers_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepoUsecase_InviteMultipleRepoMembers_Call) RunAndReturn(run func(context.Context, dto.InviteRepoMembersInput) error) *MockRepoUsecase_InviteMultipleRepoMembers_Call {
	_c.Call.Return(run)
	return _c
}

// InviteRepoMember provides a mock function with given fields: ctx, input
func (_m *MockRepoUsecase) InviteRepoMember(ctx context.Context, input dto.InviteRepoMemberInput) error {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for InviteRepoMember")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.InviteRepoMemberInput) error); ok {
		r0 = rf(ctx, input)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepoUsecase_InviteRepoMember_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'InviteRepoMember'
type MockRepoUsecase_InviteRepoMember_Call struct {
	*mock.Call
}

// InviteRepoMember is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.InviteRepoMemberInput
func (_e *MockRepoUsecase_Expecter) InviteRepoMember(ctx interface{}, input interface{}) *MockRepoUsecase_InviteRepoMember_Call {
	return &MockRepoUsecase_InviteRepoMember_Call{Call: _e.mock.On("InviteRepoMember", ctx, input)}
}

func (_c *MockRepoUsecase_InviteRepoMember_Call) Run(run func(ctx context.Context, input dto.InviteRepoMemberInput)) *MockRepoUsecase_InviteRepoMember_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.InviteRepoMemberInput))
	})
	return _c
}

func (_c *MockRepoUsecase_InviteRepoMember_Call) Return(_a0 error) *MockRepoUsecase_InviteRepoMember_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepoUsecase_InviteRepoMember_Call) RunAndReturn(run func(context.Context, dto.InviteRepoMemberInput) error) *MockRepoUsecase_InviteRepoMember_Call {
	_c.Call.Return(run)
	return _c
}

// ListDeployment provides a mock function with given fields: ctx, input
func (_m *MockRepoUsecase) ListDeployment(ctx context.Context, input dto.ListDeploymentRequest) (*dto.ListDeploymentResponse, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for ListDeployment")
	}

	var r0 *dto.ListDeploymentResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.ListDeploymentRequest) (*dto.ListDeploymentResponse, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, dto.ListDeploymentRequest) *dto.ListDeploymentResponse); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*dto.ListDeploymentResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, dto.ListDeploymentRequest) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepoUsecase_ListDeployment_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListDeployment'
type MockRepoUsecase_ListDeployment_Call struct {
	*mock.Call
}

// ListDeployment is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.ListDeploymentRequest
func (_e *MockRepoUsecase_Expecter) ListDeployment(ctx interface{}, input interface{}) *MockRepoUsecase_ListDeployment_Call {
	return &MockRepoUsecase_ListDeployment_Call{Call: _e.mock.On("ListDeployment", ctx, input)}
}

func (_c *MockRepoUsecase_ListDeployment_Call) Run(run func(ctx context.Context, input dto.ListDeploymentRequest)) *MockRepoUsecase_ListDeployment_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.ListDeploymentRequest))
	})
	return _c
}

func (_c *MockRepoUsecase_ListDeployment_Call) Return(_a0 *dto.ListDeploymentResponse, _a1 error) *MockRepoUsecase_ListDeployment_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepoUsecase_ListDeployment_Call) RunAndReturn(run func(context.Context, dto.ListDeploymentRequest) (*dto.ListDeploymentResponse, error)) *MockRepoUsecase_ListDeployment_Call {
	_c.Call.Return(run)
	return _c
}

// ListRepoAccessToken provides a mock function with given fields: ctx, req
func (_m *MockRepoUsecase) ListRepoAccessToken(ctx context.Context, req dto.ListRepoAccessTokenRequest) (*dto.ListRepoAccessTokenResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for ListRepoAccessToken")
	}

	var r0 *dto.ListRepoAccessTokenResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.ListRepoAccessTokenRequest) (*dto.ListRepoAccessTokenResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, dto.ListRepoAccessTokenRequest) *dto.ListRepoAccessTokenResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*dto.ListRepoAccessTokenResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, dto.ListRepoAccessTokenRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepoUsecase_ListRepoAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListRepoAccessToken'
type MockRepoUsecase_ListRepoAccessToken_Call struct {
	*mock.Call
}

// ListRepoAccessToken is a helper method to define mock.On call
//   - ctx context.Context
//   - req dto.ListRepoAccessTokenRequest
func (_e *MockRepoUsecase_Expecter) ListRepoAccessToken(ctx interface{}, req interface{}) *MockRepoUsecase_ListRepoAccessToken_Call {
	return &MockRepoUsecase_ListRepoAccessToken_Call{Call: _e.mock.On("ListRepoAccessToken", ctx, req)}
}

func (_c *MockRepoUsecase_ListRepoAccessToken_Call) Run(run func(ctx context.Context, req dto.ListRepoAccessTokenRequest)) *MockRepoUsecase_ListRepoAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.ListRepoAccessTokenRequest))
	})
	return _c
}

func (_c *MockRepoUsecase_ListRepoAccessToken_Call) Return(_a0 *dto.ListRepoAccessTokenResponse, _a1 error) *MockRepoUsecase_ListRepoAccessToken_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepoUsecase_ListRepoAccessToken_Call) RunAndReturn(run func(context.Context, dto.ListRepoAccessTokenRequest) (*dto.ListRepoAccessTokenResponse, error)) *MockRepoUsecase_ListRepoAccessToken_Call {
	_c.Call.Return(run)
	return _c
}

// ListRepoBranches provides a mock function with given fields: ctx, input
func (_m *MockRepoUsecase) ListRepoBranches(ctx context.Context, input dto.GetRepositoryBranchesInput) ([]dto.RepositoryBranchInfo, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for ListRepoBranches")
	}

	var r0 []dto.RepositoryBranchInfo
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.GetRepositoryBranchesInput) ([]dto.RepositoryBranchInfo, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, dto.GetRepositoryBranchesInput) []dto.RepositoryBranchInfo); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]dto.RepositoryBranchInfo)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, dto.GetRepositoryBranchesInput) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepoUsecase_ListRepoBranches_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListRepoBranches'
type MockRepoUsecase_ListRepoBranches_Call struct {
	*mock.Call
}

// ListRepoBranches is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.GetRepositoryBranchesInput
func (_e *MockRepoUsecase_Expecter) ListRepoBranches(ctx interface{}, input interface{}) *MockRepoUsecase_ListRepoBranches_Call {
	return &MockRepoUsecase_ListRepoBranches_Call{Call: _e.mock.On("ListRepoBranches", ctx, input)}
}

func (_c *MockRepoUsecase_ListRepoBranches_Call) Run(run func(ctx context.Context, input dto.GetRepositoryBranchesInput)) *MockRepoUsecase_ListRepoBranches_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.GetRepositoryBranchesInput))
	})
	return _c
}

func (_c *MockRepoUsecase_ListRepoBranches_Call) Return(_a0 []dto.RepositoryBranchInfo, _a1 error) *MockRepoUsecase_ListRepoBranches_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepoUsecase_ListRepoBranches_Call) RunAndReturn(run func(context.Context, dto.GetRepositoryBranchesInput) ([]dto.RepositoryBranchInfo, error)) *MockRepoUsecase_ListRepoBranches_Call {
	_c.Call.Return(run)
	return _c
}

// ListRepoCommits provides a mock function with given fields: ctx, input
func (_m *MockRepoUsecase) ListRepoCommits(ctx context.Context, input dto.GetRepositoryCommitsInput) (*dto.GetRepositoryCommitsOutput, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for ListRepoCommits")
	}

	var r0 *dto.GetRepositoryCommitsOutput
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.GetRepositoryCommitsInput) (*dto.GetRepositoryCommitsOutput, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, dto.GetRepositoryCommitsInput) *dto.GetRepositoryCommitsOutput); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*dto.GetRepositoryCommitsOutput)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, dto.GetRepositoryCommitsInput) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepoUsecase_ListRepoCommits_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListRepoCommits'
type MockRepoUsecase_ListRepoCommits_Call struct {
	*mock.Call
}

// ListRepoCommits is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.GetRepositoryCommitsInput
func (_e *MockRepoUsecase_Expecter) ListRepoCommits(ctx interface{}, input interface{}) *MockRepoUsecase_ListRepoCommits_Call {
	return &MockRepoUsecase_ListRepoCommits_Call{Call: _e.mock.On("ListRepoCommits", ctx, input)}
}

func (_c *MockRepoUsecase_ListRepoCommits_Call) Run(run func(ctx context.Context, input dto.GetRepositoryCommitsInput)) *MockRepoUsecase_ListRepoCommits_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.GetRepositoryCommitsInput))
	})
	return _c
}

func (_c *MockRepoUsecase_ListRepoCommits_Call) Return(_a0 *dto.GetRepositoryCommitsOutput, _a1 error) *MockRepoUsecase_ListRepoCommits_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepoUsecase_ListRepoCommits_Call) RunAndReturn(run func(context.Context, dto.GetRepositoryCommitsInput) (*dto.GetRepositoryCommitsOutput, error)) *MockRepoUsecase_ListRepoCommits_Call {
	_c.Call.Return(run)
	return _c
}

// ListRepoContributors provides a mock function with given fields: ctx, input
func (_m *MockRepoUsecase) ListRepoContributors(ctx context.Context, input dto.GetRepositoryContributorsInput) ([]dto.RepositoryContributor, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for ListRepoContributors")
	}

	var r0 []dto.RepositoryContributor
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.GetRepositoryContributorsInput) ([]dto.RepositoryContributor, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, dto.GetRepositoryContributorsInput) []dto.RepositoryContributor); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]dto.RepositoryContributor)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, dto.GetRepositoryContributorsInput) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepoUsecase_ListRepoContributors_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListRepoContributors'
type MockRepoUsecase_ListRepoContributors_Call struct {
	*mock.Call
}

// ListRepoContributors is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.GetRepositoryContributorsInput
func (_e *MockRepoUsecase_Expecter) ListRepoContributors(ctx interface{}, input interface{}) *MockRepoUsecase_ListRepoContributors_Call {
	return &MockRepoUsecase_ListRepoContributors_Call{Call: _e.mock.On("ListRepoContributors", ctx, input)}
}

func (_c *MockRepoUsecase_ListRepoContributors_Call) Run(run func(ctx context.Context, input dto.GetRepositoryContributorsInput)) *MockRepoUsecase_ListRepoContributors_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.GetRepositoryContributorsInput))
	})
	return _c
}

func (_c *MockRepoUsecase_ListRepoContributors_Call) Return(_a0 []dto.RepositoryContributor, _a1 error) *MockRepoUsecase_ListRepoContributors_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepoUsecase_ListRepoContributors_Call) RunAndReturn(run func(context.Context, dto.GetRepositoryContributorsInput) ([]dto.RepositoryContributor, error)) *MockRepoUsecase_ListRepoContributors_Call {
	_c.Call.Return(run)
	return _c
}

// ListRepoFiles provides a mock function with given fields: ctx, input
func (_m *MockRepoUsecase) ListRepoFiles(ctx context.Context, input dto.GetRepositoryFilesInput) ([]dto.RepositoryFile, *string, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for ListRepoFiles")
	}

	var r0 []dto.RepositoryFile
	var r1 *string
	var r2 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.GetRepositoryFilesInput) ([]dto.RepositoryFile, *string, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, dto.GetRepositoryFilesInput) []dto.RepositoryFile); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]dto.RepositoryFile)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, dto.GetRepositoryFilesInput) *string); ok {
		r1 = rf(ctx, input)
	} else {
		if ret.Get(1) != nil {
			r1 = ret.Get(1).(*string)
		}
	}

	if rf, ok := ret.Get(2).(func(context.Context, dto.GetRepositoryFilesInput) error); ok {
		r2 = rf(ctx, input)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// MockRepoUsecase_ListRepoFiles_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListRepoFiles'
type MockRepoUsecase_ListRepoFiles_Call struct {
	*mock.Call
}

// ListRepoFiles is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.GetRepositoryFilesInput
func (_e *MockRepoUsecase_Expecter) ListRepoFiles(ctx interface{}, input interface{}) *MockRepoUsecase_ListRepoFiles_Call {
	return &MockRepoUsecase_ListRepoFiles_Call{Call: _e.mock.On("ListRepoFiles", ctx, input)}
}

func (_c *MockRepoUsecase_ListRepoFiles_Call) Run(run func(ctx context.Context, input dto.GetRepositoryFilesInput)) *MockRepoUsecase_ListRepoFiles_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.GetRepositoryFilesInput))
	})
	return _c
}

func (_c *MockRepoUsecase_ListRepoFiles_Call) Return(_a0 []dto.RepositoryFile, _a1 *string, _a2 error) *MockRepoUsecase_ListRepoFiles_Call {
	_c.Call.Return(_a0, _a1, _a2)
	return _c
}

func (_c *MockRepoUsecase_ListRepoFiles_Call) RunAndReturn(run func(context.Context, dto.GetRepositoryFilesInput) ([]dto.RepositoryFile, *string, error)) *MockRepoUsecase_ListRepoFiles_Call {
	_c.Call.Return(run)
	return _c
}

// ListRepoMembers provides a mock function with given fields: ctx, input
func (_m *MockRepoUsecase) ListRepoMembers(ctx context.Context, input dto.ListRepositoryMembersInput) (*dto.ListRepositoryMembersOutput, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for ListRepoMembers")
	}

	var r0 *dto.ListRepositoryMembersOutput
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.ListRepositoryMembersInput) (*dto.ListRepositoryMembersOutput, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, dto.ListRepositoryMembersInput) *dto.ListRepositoryMembersOutput); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*dto.ListRepositoryMembersOutput)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, dto.ListRepositoryMembersInput) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepoUsecase_ListRepoMembers_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListRepoMembers'
type MockRepoUsecase_ListRepoMembers_Call struct {
	*mock.Call
}

// ListRepoMembers is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.ListRepositoryMembersInput
func (_e *MockRepoUsecase_Expecter) ListRepoMembers(ctx interface{}, input interface{}) *MockRepoUsecase_ListRepoMembers_Call {
	return &MockRepoUsecase_ListRepoMembers_Call{Call: _e.mock.On("ListRepoMembers", ctx, input)}
}

func (_c *MockRepoUsecase_ListRepoMembers_Call) Run(run func(ctx context.Context, input dto.ListRepositoryMembersInput)) *MockRepoUsecase_ListRepoMembers_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.ListRepositoryMembersInput))
	})
	return _c
}

func (_c *MockRepoUsecase_ListRepoMembers_Call) Return(_a0 *dto.ListRepositoryMembersOutput, _a1 error) *MockRepoUsecase_ListRepoMembers_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepoUsecase_ListRepoMembers_Call) RunAndReturn(run func(context.Context, dto.ListRepositoryMembersInput) (*dto.ListRepositoryMembersOutput, error)) *MockRepoUsecase_ListRepoMembers_Call {
	_c.Call.Return(run)
	return _c
}

// ListRepoTags provides a mock function with given fields: ctx, input
func (_m *MockRepoUsecase) ListRepoTags(ctx context.Context, input dto.ListRepoTagsInput) (*dto.ListRepoTagsOutput, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for ListRepoTags")
	}

	var r0 *dto.ListRepoTagsOutput
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.ListRepoTagsInput) (*dto.ListRepoTagsOutput, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, dto.ListRepoTagsInput) *dto.ListRepoTagsOutput); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*dto.ListRepoTagsOutput)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, dto.ListRepoTagsInput) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepoUsecase_ListRepoTags_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListRepoTags'
type MockRepoUsecase_ListRepoTags_Call struct {
	*mock.Call
}

// ListRepoTags is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.ListRepoTagsInput
func (_e *MockRepoUsecase_Expecter) ListRepoTags(ctx interface{}, input interface{}) *MockRepoUsecase_ListRepoTags_Call {
	return &MockRepoUsecase_ListRepoTags_Call{Call: _e.mock.On("ListRepoTags", ctx, input)}
}

func (_c *MockRepoUsecase_ListRepoTags_Call) Run(run func(ctx context.Context, input dto.ListRepoTagsInput)) *MockRepoUsecase_ListRepoTags_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.ListRepoTagsInput))
	})
	return _c
}

func (_c *MockRepoUsecase_ListRepoTags_Call) Return(_a0 *dto.ListRepoTagsOutput, _a1 error) *MockRepoUsecase_ListRepoTags_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepoUsecase_ListRepoTags_Call) RunAndReturn(run func(context.Context, dto.ListRepoTagsInput) (*dto.ListRepoTagsOutput, error)) *MockRepoUsecase_ListRepoTags_Call {
	_c.Call.Return(run)
	return _c
}

// ListRepoTemplates provides a mock function with given fields: ctx
func (_m *MockRepoUsecase) ListRepoTemplates(ctx context.Context) ([]dto.RepoTemplate, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for ListRepoTemplates")
	}

	var r0 []dto.RepoTemplate
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) ([]dto.RepoTemplate, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) []dto.RepoTemplate); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]dto.RepoTemplate)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepoUsecase_ListRepoTemplates_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListRepoTemplates'
type MockRepoUsecase_ListRepoTemplates_Call struct {
	*mock.Call
}

// ListRepoTemplates is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockRepoUsecase_Expecter) ListRepoTemplates(ctx interface{}) *MockRepoUsecase_ListRepoTemplates_Call {
	return &MockRepoUsecase_ListRepoTemplates_Call{Call: _e.mock.On("ListRepoTemplates", ctx)}
}

func (_c *MockRepoUsecase_ListRepoTemplates_Call) Run(run func(ctx context.Context)) *MockRepoUsecase_ListRepoTemplates_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockRepoUsecase_ListRepoTemplates_Call) Return(_a0 []dto.RepoTemplate, _a1 error) *MockRepoUsecase_ListRepoTemplates_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepoUsecase_ListRepoTemplates_Call) RunAndReturn(run func(context.Context) ([]dto.RepoTemplate, error)) *MockRepoUsecase_ListRepoTemplates_Call {
	_c.Call.Return(run)
	return _c
}

// RemoveMember provides a mock function with given fields: ctx, input
func (_m *MockRepoUsecase) RemoveMember(ctx context.Context, input dto.RemoveMemberRepositoryInput) error {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for RemoveMember")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.RemoveMemberRepositoryInput) error); ok {
		r0 = rf(ctx, input)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepoUsecase_RemoveMember_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RemoveMember'
type MockRepoUsecase_RemoveMember_Call struct {
	*mock.Call
}

// RemoveMember is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.RemoveMemberRepositoryInput
func (_e *MockRepoUsecase_Expecter) RemoveMember(ctx interface{}, input interface{}) *MockRepoUsecase_RemoveMember_Call {
	return &MockRepoUsecase_RemoveMember_Call{Call: _e.mock.On("RemoveMember", ctx, input)}
}

func (_c *MockRepoUsecase_RemoveMember_Call) Run(run func(ctx context.Context, input dto.RemoveMemberRepositoryInput)) *MockRepoUsecase_RemoveMember_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.RemoveMemberRepositoryInput))
	})
	return _c
}

func (_c *MockRepoUsecase_RemoveMember_Call) Return(_a0 error) *MockRepoUsecase_RemoveMember_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepoUsecase_RemoveMember_Call) RunAndReturn(run func(context.Context, dto.RemoveMemberRepositoryInput) error) *MockRepoUsecase_RemoveMember_Call {
	_c.Call.Return(run)
	return _c
}

// RestartDeployment provides a mock function with given fields: ctx, req
func (_m *MockRepoUsecase) RestartDeployment(ctx context.Context, req dto.RestartDeploymentRequest) error {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for RestartDeployment")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.RestartDeploymentRequest) error); ok {
		r0 = rf(ctx, req)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepoUsecase_RestartDeployment_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RestartDeployment'
type MockRepoUsecase_RestartDeployment_Call struct {
	*mock.Call
}

// RestartDeployment is a helper method to define mock.On call
//   - ctx context.Context
//   - req dto.RestartDeploymentRequest
func (_e *MockRepoUsecase_Expecter) RestartDeployment(ctx interface{}, req interface{}) *MockRepoUsecase_RestartDeployment_Call {
	return &MockRepoUsecase_RestartDeployment_Call{Call: _e.mock.On("RestartDeployment", ctx, req)}
}

func (_c *MockRepoUsecase_RestartDeployment_Call) Run(run func(ctx context.Context, req dto.RestartDeploymentRequest)) *MockRepoUsecase_RestartDeployment_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.RestartDeploymentRequest))
	})
	return _c
}

func (_c *MockRepoUsecase_RestartDeployment_Call) Return(_a0 error) *MockRepoUsecase_RestartDeployment_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepoUsecase_RestartDeployment_Call) RunAndReturn(run func(context.Context, dto.RestartDeploymentRequest) error) *MockRepoUsecase_RestartDeployment_Call {
	_c.Call.Return(run)
	return _c
}

// StartDeployment provides a mock function with given fields: ctx, userID, repoID, input
func (_m *MockRepoUsecase) StartDeployment(ctx context.Context, userID uuid.UUID, repoID types.RepoID, input dto.StartDeploymentRequest) (*dto.StartDeploymentResponse, error) {
	ret := _m.Called(ctx, userID, repoID, input)

	if len(ret) == 0 {
		panic("no return value specified for StartDeployment")
	}

	var r0 *dto.StartDeploymentResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID, types.RepoID, dto.StartDeploymentRequest) (*dto.StartDeploymentResponse, error)); ok {
		return rf(ctx, userID, repoID, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID, types.RepoID, dto.StartDeploymentRequest) *dto.StartDeploymentResponse); ok {
		r0 = rf(ctx, userID, repoID, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*dto.StartDeploymentResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uuid.UUID, types.RepoID, dto.StartDeploymentRequest) error); ok {
		r1 = rf(ctx, userID, repoID, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepoUsecase_StartDeployment_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'StartDeployment'
type MockRepoUsecase_StartDeployment_Call struct {
	*mock.Call
}

// StartDeployment is a helper method to define mock.On call
//   - ctx context.Context
//   - userID uuid.UUID
//   - repoID types.RepoID
//   - input dto.StartDeploymentRequest
func (_e *MockRepoUsecase_Expecter) StartDeployment(ctx interface{}, userID interface{}, repoID interface{}, input interface{}) *MockRepoUsecase_StartDeployment_Call {
	return &MockRepoUsecase_StartDeployment_Call{Call: _e.mock.On("StartDeployment", ctx, userID, repoID, input)}
}

func (_c *MockRepoUsecase_StartDeployment_Call) Run(run func(ctx context.Context, userID uuid.UUID, repoID types.RepoID, input dto.StartDeploymentRequest)) *MockRepoUsecase_StartDeployment_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID), args[2].(types.RepoID), args[3].(dto.StartDeploymentRequest))
	})
	return _c
}

func (_c *MockRepoUsecase_StartDeployment_Call) Return(_a0 *dto.StartDeploymentResponse, _a1 error) *MockRepoUsecase_StartDeployment_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepoUsecase_StartDeployment_Call) RunAndReturn(run func(context.Context, uuid.UUID, types.RepoID, dto.StartDeploymentRequest) (*dto.StartDeploymentResponse, error)) *MockRepoUsecase_StartDeployment_Call {
	_c.Call.Return(run)
	return _c
}

// StopDeployment provides a mock function with given fields: ctx, input
func (_m *MockRepoUsecase) StopDeployment(ctx context.Context, input dto.StopDeploymentRequest) error {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for StopDeployment")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.StopDeploymentRequest) error); ok {
		r0 = rf(ctx, input)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepoUsecase_StopDeployment_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'StopDeployment'
type MockRepoUsecase_StopDeployment_Call struct {
	*mock.Call
}

// StopDeployment is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.StopDeploymentRequest
func (_e *MockRepoUsecase_Expecter) StopDeployment(ctx interface{}, input interface{}) *MockRepoUsecase_StopDeployment_Call {
	return &MockRepoUsecase_StopDeployment_Call{Call: _e.mock.On("StopDeployment", ctx, input)}
}

func (_c *MockRepoUsecase_StopDeployment_Call) Run(run func(ctx context.Context, input dto.StopDeploymentRequest)) *MockRepoUsecase_StopDeployment_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.StopDeploymentRequest))
	})
	return _c
}

func (_c *MockRepoUsecase_StopDeployment_Call) Return(_a0 error) *MockRepoUsecase_StopDeployment_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepoUsecase_StopDeployment_Call) RunAndReturn(run func(context.Context, dto.StopDeploymentRequest) error) *MockRepoUsecase_StopDeployment_Call {
	_c.Call.Return(run)
	return _c
}

// TerminateDeployment provides a mock function with given fields: ctx, userID, repoID
func (_m *MockRepoUsecase) TerminateDeployment(ctx context.Context, userID uuid.UUID, repoID types.RepoID) error {
	ret := _m.Called(ctx, userID, repoID)

	if len(ret) == 0 {
		panic("no return value specified for TerminateDeployment")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID, types.RepoID) error); ok {
		r0 = rf(ctx, userID, repoID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepoUsecase_TerminateDeployment_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'TerminateDeployment'
type MockRepoUsecase_TerminateDeployment_Call struct {
	*mock.Call
}

// TerminateDeployment is a helper method to define mock.On call
//   - ctx context.Context
//   - userID uuid.UUID
//   - repoID types.RepoID
func (_e *MockRepoUsecase_Expecter) TerminateDeployment(ctx interface{}, userID interface{}, repoID interface{}) *MockRepoUsecase_TerminateDeployment_Call {
	return &MockRepoUsecase_TerminateDeployment_Call{Call: _e.mock.On("TerminateDeployment", ctx, userID, repoID)}
}

func (_c *MockRepoUsecase_TerminateDeployment_Call) Run(run func(ctx context.Context, userID uuid.UUID, repoID types.RepoID)) *MockRepoUsecase_TerminateDeployment_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID), args[2].(types.RepoID))
	})
	return _c
}

func (_c *MockRepoUsecase_TerminateDeployment_Call) Return(_a0 error) *MockRepoUsecase_TerminateDeployment_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepoUsecase_TerminateDeployment_Call) RunAndReturn(run func(context.Context, uuid.UUID, types.RepoID) error) *MockRepoUsecase_TerminateDeployment_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateDeploymentStatus provides a mock function with given fields: ctx, req
func (_m *MockRepoUsecase) UpdateDeploymentStatus(ctx context.Context, req dto.UpdateDeploymentStatusRequest) error {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for UpdateDeploymentStatus")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.UpdateDeploymentStatusRequest) error); ok {
		r0 = rf(ctx, req)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepoUsecase_UpdateDeploymentStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateDeploymentStatus'
type MockRepoUsecase_UpdateDeploymentStatus_Call struct {
	*mock.Call
}

// UpdateDeploymentStatus is a helper method to define mock.On call
//   - ctx context.Context
//   - req dto.UpdateDeploymentStatusRequest
func (_e *MockRepoUsecase_Expecter) UpdateDeploymentStatus(ctx interface{}, req interface{}) *MockRepoUsecase_UpdateDeploymentStatus_Call {
	return &MockRepoUsecase_UpdateDeploymentStatus_Call{Call: _e.mock.On("UpdateDeploymentStatus", ctx, req)}
}

func (_c *MockRepoUsecase_UpdateDeploymentStatus_Call) Run(run func(ctx context.Context, req dto.UpdateDeploymentStatusRequest)) *MockRepoUsecase_UpdateDeploymentStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.UpdateDeploymentStatusRequest))
	})
	return _c
}

func (_c *MockRepoUsecase_UpdateDeploymentStatus_Call) Return(_a0 error) *MockRepoUsecase_UpdateDeploymentStatus_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepoUsecase_UpdateDeploymentStatus_Call) RunAndReturn(run func(context.Context, dto.UpdateDeploymentStatusRequest) error) *MockRepoUsecase_UpdateDeploymentStatus_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateEnv provides a mock function with given fields: ctx, repoID, req
func (_m *MockRepoUsecase) UpdateEnv(ctx context.Context, repoID types.RepoID, req dto.UpdateRepositoryEnvRequest) error {
	ret := _m.Called(ctx, repoID, req)

	if len(ret) == 0 {
		panic("no return value specified for UpdateEnv")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, types.RepoID, dto.UpdateRepositoryEnvRequest) error); ok {
		r0 = rf(ctx, repoID, req)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepoUsecase_UpdateEnv_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateEnv'
type MockRepoUsecase_UpdateEnv_Call struct {
	*mock.Call
}

// UpdateEnv is a helper method to define mock.On call
//   - ctx context.Context
//   - repoID types.RepoID
//   - req dto.UpdateRepositoryEnvRequest
func (_e *MockRepoUsecase_Expecter) UpdateEnv(ctx interface{}, repoID interface{}, req interface{}) *MockRepoUsecase_UpdateEnv_Call {
	return &MockRepoUsecase_UpdateEnv_Call{Call: _e.mock.On("UpdateEnv", ctx, repoID, req)}
}

func (_c *MockRepoUsecase_UpdateEnv_Call) Run(run func(ctx context.Context, repoID types.RepoID, req dto.UpdateRepositoryEnvRequest)) *MockRepoUsecase_UpdateEnv_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(types.RepoID), args[2].(dto.UpdateRepositoryEnvRequest))
	})
	return _c
}

func (_c *MockRepoUsecase_UpdateEnv_Call) Return(_a0 error) *MockRepoUsecase_UpdateEnv_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepoUsecase_UpdateEnv_Call) RunAndReturn(run func(context.Context, types.RepoID, dto.UpdateRepositoryEnvRequest) error) *MockRepoUsecase_UpdateEnv_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateMember provides a mock function with given fields: ctx, input
func (_m *MockRepoUsecase) UpdateMember(ctx context.Context, input dto.UpdateMemberRepositoryInput) error {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for UpdateMember")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.UpdateMemberRepositoryInput) error); ok {
		r0 = rf(ctx, input)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepoUsecase_UpdateMember_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateMember'
type MockRepoUsecase_UpdateMember_Call struct {
	*mock.Call
}

// UpdateMember is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.UpdateMemberRepositoryInput
func (_e *MockRepoUsecase_Expecter) UpdateMember(ctx interface{}, input interface{}) *MockRepoUsecase_UpdateMember_Call {
	return &MockRepoUsecase_UpdateMember_Call{Call: _e.mock.On("UpdateMember", ctx, input)}
}

func (_c *MockRepoUsecase_UpdateMember_Call) Run(run func(ctx context.Context, input dto.UpdateMemberRepositoryInput)) *MockRepoUsecase_UpdateMember_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.UpdateMemberRepositoryInput))
	})
	return _c
}

func (_c *MockRepoUsecase_UpdateMember_Call) Return(_a0 error) *MockRepoUsecase_UpdateMember_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepoUsecase_UpdateMember_Call) RunAndReturn(run func(context.Context, dto.UpdateMemberRepositoryInput) error) *MockRepoUsecase_UpdateMember_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateRepoTag provides a mock function with given fields: ctx, input
func (_m *MockRepoUsecase) UpdateRepoTag(ctx context.Context, input dto.UpdateRepoTagInput) error {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for UpdateRepoTag")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.UpdateRepoTagInput) error); ok {
		r0 = rf(ctx, input)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepoUsecase_UpdateRepoTag_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateRepoTag'
type MockRepoUsecase_UpdateRepoTag_Call struct {
	*mock.Call
}

// UpdateRepoTag is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.UpdateRepoTagInput
func (_e *MockRepoUsecase_Expecter) UpdateRepoTag(ctx interface{}, input interface{}) *MockRepoUsecase_UpdateRepoTag_Call {
	return &MockRepoUsecase_UpdateRepoTag_Call{Call: _e.mock.On("UpdateRepoTag", ctx, input)}
}

func (_c *MockRepoUsecase_UpdateRepoTag_Call) Run(run func(ctx context.Context, input dto.UpdateRepoTagInput)) *MockRepoUsecase_UpdateRepoTag_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.UpdateRepoTagInput))
	})
	return _c
}

func (_c *MockRepoUsecase_UpdateRepoTag_Call) Return(_a0 error) *MockRepoUsecase_UpdateRepoTag_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepoUsecase_UpdateRepoTag_Call) RunAndReturn(run func(context.Context, dto.UpdateRepoTagInput) error) *MockRepoUsecase_UpdateRepoTag_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateRepository provides a mock function with given fields: ctx, input
func (_m *MockRepoUsecase) UpdateRepository(ctx context.Context, input dto.UpdateRepositoryInput) error {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for UpdateRepository")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.UpdateRepositoryInput) error); ok {
		r0 = rf(ctx, input)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepoUsecase_UpdateRepository_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateRepository'
type MockRepoUsecase_UpdateRepository_Call struct {
	*mock.Call
}

// UpdateRepository is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.UpdateRepositoryInput
func (_e *MockRepoUsecase_Expecter) UpdateRepository(ctx interface{}, input interface{}) *MockRepoUsecase_UpdateRepository_Call {
	return &MockRepoUsecase_UpdateRepository_Call{Call: _e.mock.On("UpdateRepository", ctx, input)}
}

func (_c *MockRepoUsecase_UpdateRepository_Call) Run(run func(ctx context.Context, input dto.UpdateRepositoryInput)) *MockRepoUsecase_UpdateRepository_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.UpdateRepositoryInput))
	})
	return _c
}

func (_c *MockRepoUsecase_UpdateRepository_Call) Return(_a0 error) *MockRepoUsecase_UpdateRepository_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepoUsecase_UpdateRepository_Call) RunAndReturn(run func(context.Context, dto.UpdateRepositoryInput) error) *MockRepoUsecase_UpdateRepository_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateTagsInRepository provides a mock function with given fields: ctx, input
func (_m *MockRepoUsecase) UpdateTagsInRepository(ctx context.Context, input dto.GitLabPushEvent) error {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for UpdateTagsInRepository")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.GitLabPushEvent) error); ok {
		r0 = rf(ctx, input)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRepoUsecase_UpdateTagsInRepository_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateTagsInRepository'
type MockRepoUsecase_UpdateTagsInRepository_Call struct {
	*mock.Call
}

// UpdateTagsInRepository is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.GitLabPushEvent
func (_e *MockRepoUsecase_Expecter) UpdateTagsInRepository(ctx interface{}, input interface{}) *MockRepoUsecase_UpdateTagsInRepository_Call {
	return &MockRepoUsecase_UpdateTagsInRepository_Call{Call: _e.mock.On("UpdateTagsInRepository", ctx, input)}
}

func (_c *MockRepoUsecase_UpdateTagsInRepository_Call) Run(run func(ctx context.Context, input dto.GitLabPushEvent)) *MockRepoUsecase_UpdateTagsInRepository_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.GitLabPushEvent))
	})
	return _c
}

func (_c *MockRepoUsecase_UpdateTagsInRepository_Call) Return(_a0 error) *MockRepoUsecase_UpdateTagsInRepository_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRepoUsecase_UpdateTagsInRepository_Call) RunAndReturn(run func(context.Context, dto.GitLabPushEvent) error) *MockRepoUsecase_UpdateTagsInRepository_Call {
	_c.Call.Return(run)
	return _c
}

// UploadAvatarRepo provides a mock function with given fields: ctx, input
func (_m *MockRepoUsecase) UploadAvatarRepo(ctx context.Context, input dto.UploadRepositoryAvatarInput) (string, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for UploadAvatarRepo")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.UploadRepositoryAvatarInput) (string, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, dto.UploadRepositoryAvatarInput) string); ok {
		r0 = rf(ctx, input)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(context.Context, dto.UploadRepositoryAvatarInput) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepoUsecase_UploadAvatarRepo_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UploadAvatarRepo'
type MockRepoUsecase_UploadAvatarRepo_Call struct {
	*mock.Call
}

// UploadAvatarRepo is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.UploadRepositoryAvatarInput
func (_e *MockRepoUsecase_Expecter) UploadAvatarRepo(ctx interface{}, input interface{}) *MockRepoUsecase_UploadAvatarRepo_Call {
	return &MockRepoUsecase_UploadAvatarRepo_Call{Call: _e.mock.On("UploadAvatarRepo", ctx, input)}
}

func (_c *MockRepoUsecase_UploadAvatarRepo_Call) Run(run func(ctx context.Context, input dto.UploadRepositoryAvatarInput)) *MockRepoUsecase_UploadAvatarRepo_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.UploadRepositoryAvatarInput))
	})
	return _c
}

func (_c *MockRepoUsecase_UploadAvatarRepo_Call) Return(_a0 string, _a1 error) *MockRepoUsecase_UploadAvatarRepo_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepoUsecase_UploadAvatarRepo_Call) RunAndReturn(run func(context.Context, dto.UploadRepositoryAvatarInput) (string, error)) *MockRepoUsecase_UploadAvatarRepo_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockRepoUsecase creates a new instance of MockRepoUsecase. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockRepoUsecase(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockRepoUsecase {
	mock := &MockRepoUsecase{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
