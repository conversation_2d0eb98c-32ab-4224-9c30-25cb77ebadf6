package repository

import (
	"context"
	"errors"

	"go.opentelemetry.io/otel/codes"
	"gorm.io/gorm"

	"api-server/internal/dto"
	"api-server/internal/enums"
	"api-server/internal/gateways/gitlab"
	repository "api-server/internal/repositories"
	"api-server/internal/usecase"
	"api-server/pkg/oteltrace"
)

// GetRepositoryBranches implements the RepositoryUsecase interface for retrieving repository branches.
// It fetches a list of branches for a specific repository with their details.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: Input data containing repository ID and branch retrieval parameters
//
// Returns:
//   - *dto.GetRepositoryBranchesOutput: List of branch information
//   - error: Any error that occurred during retrieval
func (u *impl) ListRepoBranches(ctx context.Context, input dto.GetRepositoryBranchesInput) ([]dto.RepositoryBranchInfo, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.ListRepoBranches")
	defer span.End()

	adminRefGitID := enums.AdminRefGitID
	adminUser, err := u.repo.FindUser(ctx, repository.FindUserFilter{RefGitUserID: &adminRefGitID})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find admin user")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, usecase.ErrUserNotFound
		}
		return nil, err
	}

	repo, err := u.repo.FindRepository(ctx, repository.RepositoryFilter{
		Type:      input.RepoID.RepoType(),
		Namespace: input.RepoID.Namespace(),
		Name:      input.RepoID.RepoName(),
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find repository")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, usecase.ErrRepositoryNotFound
		}
		return nil, err
	}

	token := ""
	if adminUser.GitlabAccessToken != nil {
		token = *adminUser.GitlabAccessToken
	}

	req := gitlab.GetProjectBranchesRequest{
		Id:    repo.RefGitRepoID,
		Token: token,
	}
	gitlabResp, err := u.gitlab.GetProjectBranches(ctx, req)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get project branches from gitlab")
		span.RecordError(err)
		return nil, err
	}

	resp := make([]dto.RepositoryBranchInfo, len(gitlabResp))
	for i, item := range gitlabResp {
		resp[i] = *resp[i].FromGitlab(item)
	}

	span.AddEvent("list repository branches successfully")
	span.SetStatus(codes.Ok, "list repository branches successfully")
	return resp, nil
}

// GetSingleRepoBranch implements the RepositoryUsecase interface for retrieving a specific branch.
// It fetches detailed information about a single branch in a repository.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: Input data containing repository ID and branch name
//
// Returns:
//   - *dto.RepositoryBranchInfo: Detailed branch information
//   - error: Any error that occurred during retrieval
func (u *impl) GetSingleRepoBranch(ctx context.Context, input dto.GetSingleRepositoryBranchInput) (*dto.RepositoryBranchInfo, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.GetSingleRepoBranch")
	defer span.End()

	adminRefGitID := enums.AdminRefGitID
	adminUser, err := u.repo.FindUser(ctx, repository.FindUserFilter{RefGitUserID: &adminRefGitID})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find admin user")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, usecase.ErrUserNotFound
		}
		return nil, err
	}

	repo, err := u.repo.FindRepository(ctx, repository.RepositoryFilter{
		Type:      input.RepoID.RepoType(),
		Namespace: input.RepoID.Namespace(),
		Name:      input.RepoID.RepoName(),
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find repository")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, usecase.ErrRepositoryNotFound
		}
		return nil, err
	}

	token := ""
	if adminUser.GitlabAccessToken != nil {
		token = *adminUser.GitlabAccessToken
	}

	req := gitlab.GetSingleProjectBrancheRequest{
		Id:     repo.RefGitRepoID,
		Token:  token,
		Branch: input.Branch,
	}

	gitlabResp, err := u.gitlab.GetSingleProjectBrancheInfo(ctx, req)
	if err != nil {
		if errors.Is(err, gitlab.ErrBranchNotFound) {
			span.SetStatus(codes.Error, "branch not found in gitlab")
			return nil, usecase.ErrBranchNotFound
		}
		span.SetStatus(codes.Error, "failed to get single project branch info from gitlab")
		span.RecordError(err)
		return nil, err
	}

	var resp dto.RepositoryBranchInfo
	resp = *resp.FromGitlab(*gitlabResp)

	span.AddEvent("get single repository branch successfully")
	span.SetStatus(codes.Ok, "get single repository branch successfully")
	return &resp, nil
}
