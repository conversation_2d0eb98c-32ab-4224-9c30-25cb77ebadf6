package repository

import (
	"context"
	"errors"

	"go.opentelemetry.io/otel/codes"
	"gorm.io/gorm"

	"api-server/internal/dto"
	"api-server/internal/enums"
	repository "api-server/internal/repositories"
	"api-server/internal/usecase"
	"api-server/pkg/oteltrace"
)

// UploadAvatar implements the RepositoryUsecase interface for uploading a repository avatar.
// It handles the process of uploading and storing an avatar image for the repository.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: Input data containing repository ID and avatar file
//
// Returns:
//   - *dto.UploadAvatarOutput: Upload result information
//   - error: Any error that occurred during upload
func (u *impl) UploadAvatarRepo(ctx context.Context, input dto.UploadRepositoryAvatarInput) (string, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.UploadAvatarRepo")
	defer span.End()

	currentUser, err := u.repo.FindUserByID(ctx, input.CurrentUserId)
	if err != nil {
		span.SetStatus(codes.Error, "failed to find user by id")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return "", usecase.ErrUserNotFound
		}
		return "", err
	}

	repo, err := u.repo.FindRepository(ctx, repository.RepositoryFilter{
		Type:      input.RepoID.RepoType(),
		Namespace: input.RepoID.Namespace(),
		Name:      input.RepoID.RepoName(),
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find repository")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return "", usecase.ErrRepositoryNotFound
		}
		return "", err
	}

	if currentUser.Role != enums.UserRole_Admin {
		err := checkRepoMemberPermission(ctx, *u, repo, currentUser, span)
		if err != nil {
			span.SetStatus(codes.Error, "no permission")
			span.RecordError(err)
			return "", err
		}
	}

	imageUrl, err := u.aws.UploadImage(ctx, *input.File, string(enums.S3BucketPrefix_Avatar))
	if err != nil {
		span.SetStatus(codes.Error, "failed to upload image to s3")
		span.RecordError(err)
		return "", err
	}

	repo.Avatar = &imageUrl

	if err := u.repo.Save(ctx, repo); err != nil {
		span.SetStatus(codes.Error, "failed to save repository")
		span.RecordError(err)
		return "", err
	}

	span.AddEvent("update repository avatar successfully")
	span.SetStatus(codes.Ok, "update repository avatar successfully")
	return imageUrl, nil
}

// DeleteAvatarRepo implements the RepositoryUsecase interface for deleting a repository avatar.
// It handles the process of removing the avatar image from storage and updating the repository record.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: Input data containing repository ID
//
// Returns:
//   - error: Any error that occurred during deletion
func (u *impl) DeleteAvatarRepo(ctx context.Context, input dto.DeleteRepositoryAvatarInput) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.DeleteAvatarRepo")
	defer span.End()

	currentUser, err := u.repo.FindUserByID(ctx, input.CurrentUserId)
	if err != nil {
		span.SetStatus(codes.Error, "failed to find user by id")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return usecase.ErrUserNotFound
		}
		return err
	}

	repo, err := u.repo.FindRepository(ctx, repository.RepositoryFilter{
		Type:      input.RepoID.RepoType(),
		Namespace: input.RepoID.Namespace(),
		Name:      input.RepoID.RepoName(),
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find repository")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return usecase.ErrRepositoryNotFound
		}
		return err
	}

	if currentUser.Role != enums.UserRole_Admin {
		err := checkRepoMemberPermission(ctx, *u, repo, currentUser, span)
		if err != nil {
			span.SetStatus(codes.Error, "no permission")
			span.RecordError(err)
			return err
		}
	}

	if repo.Avatar != nil {
		if err = u.aws.DeleteImage(ctx, *repo.Avatar); err != nil {
			span.SetStatus(codes.Error, "failed to delete s3 image")
			span.RecordError(err)
			return err
		}
	}

	repo.Avatar = nil
	err = u.repo.Save(ctx, repo)
	if err != nil {
		span.SetStatus(codes.Error, "failed to save repository")
		span.RecordError(err)
		return err
	}

	span.SetStatus(codes.Ok, "repository avatar deleted successfully")
	return nil
}
