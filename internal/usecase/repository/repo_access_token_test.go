package repository_test

import (
	"context"
	"errors"
	"testing"
	"time"

	"api-server/internal/dto"
	"api-server/internal/entities"
	"api-server/internal/enums"
	"api-server/internal/gateways/gitlab"
	gitlab_mocks "api-server/internal/gateways/gitlab/mocks"
	repository_mocks "api-server/internal/repositories/mocks"
	"api-server/internal/types"
	"api-server/internal/usecase"
	"api-server/internal/usecase/repository"
	"api-server/internal/utils"

	"github.com/google/uuid"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"gorm.io/gorm"
)

func TestCreateRepoAccessToken(t *testing.T) {
	mockUserID, _ := uuid.Parse("d2de43e6-fde6-4deb-a1d0-2d5c2c2e1038")

	repoType := enums.RepoType_Models
	namespace := "namespace"
	repoName := "repo-name"
	repoID := *types.NewRepoID(repoType, namespace, repoName)

	repoUUID := uuid.New()

	now, _ := time.Parse(time.RFC3339, "2024-12-15T15:30:00Z")

	type dependencies struct {
		repo         *repository_mocks.MockRepository
		gitlabClient *gitlab_mocks.MockGitlabClient
	}

	testcases := []struct {
		name      string
		ctx       context.Context
		input     dto.CreateRepoAccessTokenRequest
		output    dto.CreateRepoAccessTokenResponse
		mockFn    func(d *dependencies)
		expectErr error
	}{
		{
			name: "should return usecase.ErrRecordNotFound if cannot found user ",
			ctx:  utils.WithUserId(context.Background(), mockUserID),
			input: dto.CreateRepoAccessTokenRequest{
				Name:      "test",
				UserID:    mockUserID,
				ExpiresAt: now,
				RepoID:    repoID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), mockUserID))
				}).Return(usecase.ErrRecordNotFound)
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(nil, usecase.ErrRecordNotFound)
			},
			expectErr: usecase.ErrRecordNotFound,
		},
		{
			name: "should pass",
			ctx:  utils.WithUserId(context.Background(), mockUserID),
			input: dto.CreateRepoAccessTokenRequest{
				Name:      "test",
				UserID:    mockUserID,
				ExpiresAt: now,
				RepoID:    repoID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), mockUserID))
				}).Return(nil)
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{
					BaseModel: entities.BaseModel{ID: mockUserID},
				}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					BaseModel: entities.BaseModel{ID: repoUUID},
				}, nil)
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything, mock.Anything).Return(nil, nil)
				d.gitlabClient.On("CreateRepoPersonalAccessToken", mock.Anything, mock.Anything).Return(&gitlab.CreateProjectAccessTokenResponse{
					Id:        2,
					Revoked:   false,
					Name:      "test",
					Scopes:    []string{"api"},
					Token:     "gitl-12easdasd",
					ExpiredAt: now.Format(time.RFC3339),
				}, nil)
				d.repo.On("CreateRepoAccessToken", mock.Anything, mock.Anything).Return(&entities.RepoAccessToken{
					RepoID:      repoUUID,
					Scopes:      "api",
					RefGitID:    2,
					ExpiresAt:   now,
					Name:        "test",
					AccessToken: "gitl-12easdasd",
					Revoked:     false,
				}, nil)
			},
			expectErr: nil,
		},
		{
			name: "should return error if FindRepository return error",
			ctx:  utils.WithUserId(context.Background(), mockUserID),
			input: dto.CreateRepoAccessTokenRequest{
				Name:      "test",
				UserID:    mockUserID,
				ExpiresAt: now,
				RepoID:    repoID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), mockUserID))
				}).Return(errors.New("error"))
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{
					BaseModel: entities.BaseModel{ID: mockUserID},
				}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return error if FindRepository return error",
			ctx:  utils.WithUserId(context.Background(), mockUserID),
			input: dto.CreateRepoAccessTokenRequest{
				Name:      "test",
				UserID:    mockUserID,
				ExpiresAt: now,
				RepoID:    repoID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), mockUserID))
				}).Return(gorm.ErrRecordNotFound)
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{
					BaseModel: entities.BaseModel{ID: mockUserID},
				}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expectErr: gorm.ErrRecordNotFound,
		},
		{
			name: "should return error if FindRepositoryMember return error",
			ctx:  utils.WithUserId(context.Background(), mockUserID),
			input: dto.CreateRepoAccessTokenRequest{
				Name:      "test",
				UserID:    mockUserID,
				ExpiresAt: now,
				RepoID:    repoID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), mockUserID))
				}).Return(nil)
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{
					BaseModel:         entities.BaseModel{ID: mockUserID},
					GitlabAccessToken: utils.Ptr("token"),
				}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					BaseModel: entities.BaseModel{ID: repoUUID},
				}, nil)
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectErr: nil,
		},
		{
			name: "should return error if CreateRepoPersonalAccessToken return error",
			ctx:  utils.WithUserId(context.Background(), mockUserID),
			input: dto.CreateRepoAccessTokenRequest{
				Name:      "test",
				UserID:    mockUserID,
				ExpiresAt: now,
				RepoID:    repoID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), mockUserID))
				}).Return(nil)
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{
					BaseModel: entities.BaseModel{ID: mockUserID},
				}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					BaseModel: entities.BaseModel{ID: repoUUID},
				}, nil)
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything, mock.Anything).Return(nil, nil)
				d.gitlabClient.On("CreateRepoPersonalAccessToken", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectErr: nil,
		},
		{
			name: "should return error if CreateRepoAccessToken return error",
			ctx:  utils.WithUserId(context.Background(), mockUserID),
			input: dto.CreateRepoAccessTokenRequest{
				Name:      "test",
				UserID:    mockUserID,
				ExpiresAt: now,
				RepoID:    repoID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), mockUserID))
				}).Return(nil)
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{
					BaseModel: entities.BaseModel{ID: mockUserID},
				}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					BaseModel: entities.BaseModel{ID: repoUUID},
				}, nil)
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything, mock.Anything).Return(nil, nil)
				d.gitlabClient.On("CreateRepoPersonalAccessToken", mock.Anything, mock.Anything).Return(&gitlab.CreateProjectAccessTokenResponse{
					Id:        2,
					Revoked:   false,
					Name:      "test",
					Scopes:    []string{"api"},
					Token:     "gitl-12easdasd",
					ExpiredAt: now.Format(time.RFC3339),
				}, nil)
				d.repo.On("CreateRepoAccessToken", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectErr: nil,
		},
	}

	for _, testcase := range testcases {
		t.Run(testcase.name, func(t *testing.T) {
			r := &dependencies{
				repo:         &repository_mocks.MockRepository{},
				gitlabClient: &gitlab_mocks.MockGitlabClient{},
			}
			testcase.mockFn(r)

			usecase := repository.New(nil, r.repo, r.gitlabClient, nil, nil, nil)
			_, err := usecase.CreateRepoAccessToken(context.Background(), dto.CreateRepoAccessTokenRequest{
				Name:      mock.Anything,
				ExpiresAt: now,
				UserID:    mockUserID,
			})

			// assertion
			require.Equal(t, testcase.expectErr, err)
		})
	}
}

func TestListAccessToken(t *testing.T) {
	mockUserID, _ := uuid.Parse("d2de43e6-fde6-4deb-a1d0-2d5c2c2e1038")

	repoUUID, _ := uuid.Parse("30c5ce2b-246e-46ce-8e01-eda2e7dda437")

	repoType := enums.RepoType_Models
	namespace := "namespace"
	repoName := "repo-name"
	repoID := *types.NewRepoID(repoType, namespace, repoName)

	now, _ := time.Parse(time.RFC3339, "2024-12-15T15:30:00Z")

	type dependencies struct {
		repo         *repository_mocks.MockRepository
		gitlabClient *gitlab_mocks.MockGitlabClient
	}

	testcases := []struct {
		name      string
		ctx       context.Context
		input     dto.ListRepoAccessTokenRequest
		output    *dto.ListRepoAccessTokenResponse
		mockFn    func(d *dependencies)
		expectErr error
	}{
		{
			name: "should return usecase.ErrInternal if FindRepository return error",
			ctx:  utils.WithUserId(context.Background(), mockUserID),
			input: dto.ListRepoAccessTokenRequest{
				UserID: mockUserID,
				RepoID: repoID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
		{
			name: "should return usecase.ErrInternal if FindRepository return error",
			ctx:  utils.WithUserId(context.Background(), mockUserID),
			input: dto.ListRepoAccessTokenRequest{
				UserID: mockUserID,
				RepoID: repoID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expectErr: usecase.ErrRepositoryNotFound,
		},
		{
			name: "should return usecase.ErrInternal if CountRepoAccessToken return error",
			ctx:  utils.WithUserId(context.Background(), mockUserID),
			input: dto.ListRepoAccessTokenRequest{
				UserID: mockUserID,
				RepoID: repoID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					BaseModel: entities.BaseModel{ID: repoUUID},
				}, nil)
				d.repo.On("CountRepoAccessToken", mock.Anything, mock.Anything).Return(int64(0), usecase.ErrInternal)
			},
			expectErr: usecase.ErrInternal,
		},
		{
			name: "should return usecase.ErrInternal if ListRepoAccessToken return error",
			ctx:  utils.WithUserId(context.Background(), mockUserID),
			input: dto.ListRepoAccessTokenRequest{
				UserID: mockUserID,
				RepoID: repoID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					BaseModel: entities.BaseModel{ID: repoUUID},
				}, nil)
				d.repo.On("CountRepoAccessToken", mock.Anything, mock.Anything).Return(int64(0), nil)
				d.repo.On("ListRepoAccessToken", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil, errors.New("error"))
			},
			expectErr: usecase.ErrInternal,
		},
		{
			name: "should pass",
			ctx:  utils.WithUserId(context.Background(), mockUserID),
			input: dto.ListRepoAccessTokenRequest{
				UserID: mockUserID,
				RepoID: repoID,
			},
			mockFn: func(d *dependencies) {
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					BaseModel: entities.BaseModel{ID: repoUUID},
				}, nil)
				d.repo.On("CountRepoAccessToken", mock.Anything, mock.Anything).Return(int64(1), nil)
				d.repo.On("ListRepoAccessToken", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]entities.RepoAccessToken{
					{
						Name:      "test",
						Scopes:    "api",
						ExpiresAt: now,
					},
				}, nil)
			},
			expectErr: nil,
			output: &dto.ListRepoAccessTokenResponse{
				Data: &[]dto.RepoAccessToken{
					{
						Name:      "test",
						Scopes:    "api",
						ExpiresAt: now,
					},
				},
			},
		},
	}

	for _, testcase := range testcases {
		t.Run(testcase.name, func(t *testing.T) {
			r := &dependencies{
				repo:         &repository_mocks.MockRepository{},
				gitlabClient: &gitlab_mocks.MockGitlabClient{},
			}
			testcase.mockFn(r)

			usecase := repository.New(nil, r.repo, r.gitlabClient, nil, nil, nil)
			_, err := usecase.ListRepoAccessToken(context.Background(), dto.ListRepoAccessTokenRequest{
				UserID:   mockUserID,
				RepoID:   repoID,
				Paginate: dto.PaginateRequest{},
			})

			// assertion
			require.Equal(t, testcase.expectErr, err)
		})
	}
}

func TestDeleteAccessToken(t *testing.T) {
	mockUserID, _ := uuid.Parse("d2de43e6-fde6-4deb-a1d0-2d5c2c2e1038")

	repoType := enums.RepoType_Models
	namespace := "namespace"
	repoName := "repo-name"
	repoID := *types.NewRepoID(repoType, namespace, repoName)

	repoUUID, _ := uuid.Parse("30c5ce2b-246e-46ce-8e01-eda2e7dda437")

	type dependencies struct {
		repo         *repository_mocks.MockRepository
		gitlabClient *gitlab_mocks.MockGitlabClient
	}

	mockAccessTokenID := uuid.New()

	testcases := []struct {
		ctx           context.Context
		name          string
		userID        uuid.UUID
		repoID        uuid.UUID
		accessTokenID uuid.UUID
		mockFn        func(d *dependencies)
		expectErr     error
	}{
		{
			name:          "should return usecase.ErrInternal if FindUserByID return error",
			ctx:           utils.WithUserId(context.Background(), mockUserID),
			userID:        mockUserID,
			accessTokenID: mockAccessTokenID,
			mockFn: func(d *dependencies) {
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), mockUserID))
				}).Return(usecase.ErrInternal)
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(nil, usecase.ErrInternal)
			},
			expectErr: usecase.ErrInternal,
		},
		{
			name:          "should return usecase.ErrInternal if FindUserByID return error ",
			ctx:           utils.WithUserId(context.Background(), mockUserID),
			userID:        mockUserID,
			accessTokenID: mockAccessTokenID,
			mockFn: func(d *dependencies) {
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), mockUserID))
				}).Return(gorm.ErrRecordNotFound)
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)
			},
			expectErr: gorm.ErrRecordNotFound,
		},
		{
			name:          "should pass",
			ctx:           utils.WithUserId(context.Background(), mockUserID),
			userID:        mockUserID,
			accessTokenID: mockAccessTokenID,
			mockFn: func(d *dependencies) {
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), mockUserID))
				}).Return(nil)
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{
					BaseModel: entities.BaseModel{ID: mockUserID}}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					BaseModel: entities.BaseModel{ID: repoUUID},
				}, nil)
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything, mock.Anything).Return(nil, nil)
				d.repo.On("FindRepoAccessToken", mock.Anything, mock.Anything).Return(&entities.RepoAccessToken{
					RepoID: repoUUID,
					BaseModel: entities.BaseModel{
						ID: mockAccessTokenID,
					},
				}, nil)
				d.gitlabClient.On("DeleteRepoPersonalAccessToken", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("DeleteRepoAccessToken", mock.Anything, mock.Anything, mock.Anything).Return(nil)
			},
			expectErr: nil,
		},
		{
			name:          "should return error if FindRepository return error",
			ctx:           utils.WithUserId(context.Background(), mockUserID),
			userID:        mockUserID,
			accessTokenID: mockAccessTokenID,
			mockFn: func(d *dependencies) {
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), mockUserID))
				}).Return(errors.New("error"))
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{
					BaseModel: entities.BaseModel{ID: mockUserID}}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, errors.New("error"))

			},
			expectErr: errors.New("error"),
		},
		{
			name:          "should return error if FindRepository return error ",
			ctx:           utils.WithUserId(context.Background(), mockUserID),
			userID:        mockUserID,
			accessTokenID: mockAccessTokenID,
			mockFn: func(d *dependencies) {
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), mockUserID))
				}).Return(gorm.ErrRecordNotFound)
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{
					BaseModel: entities.BaseModel{ID: mockUserID}}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(nil, gorm.ErrRecordNotFound)

			},
			expectErr: gorm.ErrRecordNotFound,
		},
		{
			name:          "should return error if FindRepositoryMember return error",
			ctx:           utils.WithUserId(context.Background(), mockUserID),
			userID:        mockUserID,
			accessTokenID: mockAccessTokenID,
			mockFn: func(d *dependencies) {
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), mockUserID))
				}).Return(errors.New("error"))
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{
					BaseModel: entities.BaseModel{ID: mockUserID}}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					BaseModel: entities.BaseModel{ID: repoUUID},
				}, nil)
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything, mock.Anything).Return(nil, errors.New("error"))

			},
			expectErr: errors.New("error"),
		},
		{
			name:          "should return error if FindRepoAccessToken return error",
			ctx:           utils.WithUserId(context.Background(), mockUserID),
			userID:        mockUserID,
			accessTokenID: mockAccessTokenID,
			mockFn: func(d *dependencies) {
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), mockUserID))
				}).Return(errors.New("error"))
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{
					BaseModel: entities.BaseModel{ID: mockUserID}}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					BaseModel: entities.BaseModel{ID: repoUUID},
				}, nil)
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything, mock.Anything).Return(nil, nil)
				d.repo.On("FindRepoAccessToken", mock.Anything, mock.Anything).Return(nil, errors.New("error"))

			},
			expectErr: errors.New("error"),
		},
		{
			name:          "should return error if DeleteRepoPersonal return error",
			ctx:           utils.WithUserId(context.Background(), mockUserID),
			userID:        mockUserID,
			accessTokenID: mockAccessTokenID,
			mockFn: func(d *dependencies) {
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), mockUserID))
				}).Return(errors.New("error"))
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{
					BaseModel: entities.BaseModel{ID: mockUserID}}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					BaseModel: entities.BaseModel{ID: repoUUID},
				}, nil)
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything, mock.Anything).Return(nil, nil)
				d.repo.On("FindRepoAccessToken", mock.Anything, mock.Anything).Return(&entities.RepoAccessToken{
					RepoID: repoUUID,
					BaseModel: entities.BaseModel{
						ID: mockAccessTokenID,
					},
				}, nil)
				d.gitlabClient.On("DeleteRepoPersonalAccessToken", mock.Anything, mock.Anything).Return(errors.New("error"))

			},
			expectErr: errors.New("error"),
		},
		{
			name:          "should return error if DeleteRepoAccessToken return error",
			ctx:           utils.WithUserId(context.Background(), mockUserID),
			userID:        mockUserID,
			accessTokenID: mockAccessTokenID,
			mockFn: func(d *dependencies) {
				d.repo.On("Transaction", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					callback := args.Get(1).(func(ctx context.Context) error)
					_ = callback(utils.WithUserId(context.Background(), mockUserID))
				}).Return(errors.New("error"))
				d.repo.On("FindUserByID", mock.Anything, mock.Anything).Return(&entities.User{
					BaseModel: entities.BaseModel{ID: mockUserID}}, nil)
				d.repo.On("FindRepository", mock.Anything, mock.Anything).Return(&entities.Repository{
					BaseModel: entities.BaseModel{ID: repoUUID},
				}, nil)
				d.repo.On("FindRepositoryMember", mock.Anything, mock.Anything, mock.Anything).Return(nil, nil)
				d.repo.On("FindRepoAccessToken", mock.Anything, mock.Anything).Return(&entities.RepoAccessToken{
					RepoID: repoUUID,
					BaseModel: entities.BaseModel{
						ID: mockAccessTokenID,
					},
				}, nil)
				d.gitlabClient.On("DeleteRepoPersonalAccessToken", mock.Anything, mock.Anything).Return(nil)
				d.repo.On("DeleteRepoAccessToken", mock.Anything, mock.Anything, mock.Anything).Return(errors.New("error"))
			},
			expectErr: errors.New("error"),
		},
	}

	for _, testcase := range testcases {
		t.Run(testcase.name, func(t *testing.T) {
			r := &dependencies{
				repo:         &repository_mocks.MockRepository{},
				gitlabClient: &gitlab_mocks.MockGitlabClient{},
			}
			testcase.mockFn(r)

			usecase := repository.New(nil, r.repo, r.gitlabClient, nil, nil, nil)
			err := usecase.DeleteRepoAccessToken(context.Background(), mockUserID, repoID, mockAccessTokenID)

			// assertion
			require.Equal(t, testcase.expectErr, err)
		})
	}
}
