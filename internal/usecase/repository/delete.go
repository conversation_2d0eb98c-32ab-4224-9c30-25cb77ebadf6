package repository

import (
	"context"
	"errors"
	"strings"

	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
	"gorm.io/gorm"
	kubeerrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"

	"api-server/internal/dto"
	"api-server/internal/entities"
	"api-server/internal/gateways/gitlab"
	repository "api-server/internal/repositories"
	"api-server/internal/usecase"
	"api-server/internal/usecase/workflow"
	"api-server/pkg/oteltrace"
)

// DeleteRepository implements the RepositoryUsecase interface for removing a repository.
// It handles the cleanup of all repository resources including:
// - Repository members
// - GitLab project
// - Deployments
// - Access tokens
//
// Parameters:
//   - ctx: Context for the operation
//   - input: Input data containing repository ID and current user ID
//
// Returns:
//   - error: Any error that occurred during deletion
func (u *impl) DeleteRepository(ctx context.Context, input dto.DeleteRepositoryInput) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.DeleteRepository")
	defer span.End()

	currentUser, err := u.repo.FindUserByID(ctx, input.CurrentUserId)
	if err != nil {
		span.SetStatus(codes.Error, "failed to find user by id")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return usecase.ErrUserNotFound
		}
		return err
	}

	repo, err := u.repo.FindRepository(ctx, repository.RepositoryFilter{
		Type:      input.RepoID.RepoType(),
		Namespace: input.RepoID.Namespace(),
		Name:      input.RepoID.RepoName(),
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find repository")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return usecase.ErrRepositoryNotFound
		}
		return err
	}

	//get user permission accesstoken
	token, err := getAccessToken(ctx, currentUser, *u, repo, span)
	if err != nil {
		return err
	}

	err = u.repo.Transaction(ctx, func(ctx context.Context) error {
		return DeleteRepository(ctx, span, u.repo, repo, u.kubeClient, u.workflow, u.gitlab, token)
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to execute transaction")
		span.RecordError(err)
		return err
	}

	span.AddEvent("repository deleted successfully")
	span.SetStatus(codes.Ok, "repository deleted successfully")
	return nil
}

// DeleteRepository is a helper function that performs the actual deletion of a repository
// and its associated resources. It is intended to be called within a database transaction.
// This function handles the deletion of Kubernetes resources, repository members, deployments,
// the repository record itself from the database, and the corresponding GitLab project.
//
// Parameters:
//   - ctx: Context for the operation.
//   - span: OpenTelemetry span for tracing.
//   - repo: The repository data access layer.
//   - userRepo: The repository entity to be deleted.
//   - kubeClient: Kubernetes client for managing cluster resources.
//   - argoWorkflow: Usecase for interacting with Argo Workflows.
//   - gitlabClient: Client for GitLab API interactions.
//   - gitlabToken: GitLab access token for authentication.
//
// Returns:
//   - error: Any error that occurred during the deletion process.
func DeleteRepository(
	ctx context.Context,
	span trace.Span,
	repo repository.Repository,
	userRepo *entities.Repository,
	kubeClient kubernetes.Interface,
	argoWorkflow workflow.WorkflowUsecase,
	gitlabClient gitlab.GitlabClient,
	gitlabToken string,
) error {
	// Delete Kubernetes resources if there's an active deployment
	if userRepo.Deployment != nil {
		err := argoWorkflow.TerminateWorkflow(ctx, userRepo.Deployment.WorkflowName, argoNamespace)
		if err != nil {
			if strings.Contains(err.Error(), "cannot shutdown a completed workflow") {
				err = nil
			} else {
				span.SetStatus(codes.Error, "failed to terminate workflow")
				span.RecordError(err)
				return usecase.ErrFailedToTerminateArgoWorkflow
			}
		}

		resourceName := spaceResourceName(userRepo.ID.String())
		if err := deleteResources(ctx, kubeClient, spaceNamespace, resourceName); err != nil {
			span.SetStatus(codes.Error, "failed to delete Kubernetes resources")
			span.RecordError(err)
			return err
		}

		// delete env configmap
		err = kubeClient.CoreV1().ConfigMaps(spaceNamespace).Delete(ctx, resourceName, metav1.DeleteOptions{})
		if err != nil {
			// skip if not found resource error else return error
			if !kubeerrors.IsNotFound(err) {
				span.SetStatus(codes.Error, "failed to delete resource ingress")
				span.RecordError(err)
				return err
			}
		}
	}

	if err := repo.DeleteRepositoryMemberByRepositoryID(ctx, userRepo.ID); err != nil {
		span.SetStatus(codes.Error, "failed to delete repository members")
		span.RecordError(err)
		return err
	}
	// delete repo's deployments
	if err := repo.DeleteDeploymentsByRepoID(ctx, userRepo.ID); err != nil {
		span.SetStatus(codes.Error, "failed to delete deployments by repository id")
		span.RecordError(err)
		return err
	}

	if err := repo.DeleteById(ctx, &entities.Repository{}, userRepo.ID); err != nil {
		span.SetStatus(codes.Error, "failed to delete repository by id")
		span.RecordError(err)
		return err
	}

	if err := gitlabClient.DeleteProject(ctx, gitlab.DeleteProjectRequest{
		Id:    userRepo.RefGitRepoID,
		Token: gitlabToken,
	}); err != nil {
		span.SetStatus(codes.Error, "failed to delete project in gitlab")
		span.RecordError(err)
		return err
	}

	return nil
}
