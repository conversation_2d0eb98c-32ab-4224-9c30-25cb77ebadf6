package repository

import (
	"context"
	"errors"
	"fmt"

	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
	"golang.org/x/sync/errgroup"
	"golang.org/x/sync/singleflight"
	"gorm.io/gorm"

	"api-server/internal/dto"
	"api-server/internal/entities"
	"api-server/internal/enums"
	"api-server/internal/gateways/gitlab"
	repository "api-server/internal/repositories"
	"api-server/internal/usecase"
	"api-server/internal/utils"
	"api-server/pkg/oteltrace"
)

// GetRepositoryCommits implements the RepositoryUsecase interface for retrieving repository commits.
// It fetches a list of commits for a specific repository with their details.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: Input data containing repository ID and commit retrieval parameters
//
// Returns:
//   - *dto.GetRepositoryCommitsOutput: List of commit information
//   - error: Any error that occurred during retrieval
func (u *impl) ListRepoCommits(ctx context.Context, input dto.GetRepositoryCommitsInput) (*dto.GetRepositoryCommitsOutput, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.ListRepoCommits")
	defer span.End()

	adminRefGitID := enums.AdminRefGitID
	adminUser, err := u.repo.FindUser(ctx, repository.FindUserFilter{RefGitUserID: &adminRefGitID})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find admin user")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, usecase.ErrUserNotFound
		}
		return nil, err
	}

	repo, err := u.repo.FindRepository(ctx, repository.RepositoryFilter{
		Type:      input.RepoID.RepoType(),
		Namespace: input.RepoID.Namespace(),
		Name:      input.RepoID.RepoName(),
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find repository")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, usecase.ErrRepositoryNotFound
		}
		return nil, err
	}

	token := ""
	if adminUser.GitlabAccessToken != nil {
		token = *adminUser.GitlabAccessToken
	}

	req := gitlab.GetProjectCommitsRequest{
		Id:      repo.RefGitRepoID,
		RefName: input.Ref,
		Path:    input.Path,
		Token:   token,
		Paginate: gitlab.PaginateRequest{
			Page:    input.Paginate.Page,
			PerPage: input.Paginate.PerPage,
		},
	}
	gitlabResp, err := u.gitlab.GetProjectCommits(ctx, req)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get project commits from gitlab")
		span.RecordError(err)
		return nil, err
	}

	// convert commit from Gitlab to our custom dto.RepositoryCommit
	commits := mapGitlabCommitResp(*gitlabResp.Data)

	//get unique users
	uniqueEmails := uniqueAuthorEmails(commits)
	uniqueUsers, err := getUniqueUsers(ctx, uniqueEmails, u)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get unique users")
		span.RecordError(err)
		return nil, err
	}
	//get latest commit
	deployment, err := u.repo.FindDeployment(ctx, repo.ID)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		span.SetStatus(codes.Error, "failed to find deployment")
		span.RecordError(err)
		return nil, err
	}
	//enrichCommits
	if err := enrichCommits(ctx, commits, deployment, uniqueUsers, u, repo, token); err != nil {
		span.SetStatus(codes.Error, "failed to enrich commits")
		span.RecordError(err)
		return nil, err
	}

	// get contribbutors avatar
	for i := 0; i < len(commits); i++ {
		cn, err := u.repo.FindUser(ctx, repository.FindUserFilter{
			Email: &commits[i].User.Email,
		})
		if err != nil {
			span.AddEvent("failed to get contributor infor", trace.WithAttributes(attribute.String("email not exist", commits[i].User.Email)))
			span.RecordError(err)
			continue
		}

		if cn.Avatar != nil {
			span.AddEvent("start generating repository avatar")
			repoImage, err := u.aws.GenPreSignUrl(ctx, *cn.Avatar)
			if err != nil {
				span.SetStatus(codes.Error, "failed to generate presigned url for avatar")
				span.RecordError(err)
				return nil, err
			}

			commits[i].User.Avatar = &repoImage
		}
	}

	span.AddEvent("list repository commits successfully")
	span.SetStatus(codes.Ok, "list repository commits successfully")
	return &dto.GetRepositoryCommitsOutput{
		Data: &commits,
		Pagination: &dto.Pagination{
			PageNo:   input.Paginate.Page,
			PageSize: input.Paginate.PerPage,
			Next:     gitlabResp.Pagination.Next,
			Previous: gitlabResp.Pagination.Previous,
		},
	}, nil
}

// enrichCommits enriches commit information with additional details including user information,
// deployment status, and merge request data.
//
// Parameters:
//   - ctx: Context for the operation
//   - commits: List of commits to enrich
//   - deployment: Current deployment information
//   - uniqueUsers: Map of user information by email
//   - u: Repository usecase implementation
//   - repo: Repository entity
//   - token: GitLab access token
//
// Returns:
//   - error: Any error that occurred during enrichment
func enrichCommits(
	ctx context.Context,
	commits []dto.RepositoryCommit,
	deployment *entities.Deployment,
	uniqueUsers map[string]dto.User,
	u *impl,
	repo *entities.Repository,
	token string,
) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.enrichCommits")
	defer span.End()

	g, gCtx := errgroup.WithContext(ctx)
	g.SetLimit(8)

	for i := 0; i < len(commits); i++ {
		select {
		case <-gCtx.Done():
			break
		default:
			g.Go(func() error {
				if deployment != nil && deployment.Commit == commits[i].ID {
					commits[i].Status = utils.Ptr("Current")
				}
				//user
				if user, exists := uniqueUsers[commits[i].AuthorEmail]; exists {
					commits[i].User = user
				}
				//merge request
				mr, err := u.gitlab.GetMergeRequests(ctx, gitlab.GetMergeRequestsRequest{
					Id:       repo.RefGitRepoID,
					Token:    token,
					CommitId: commits[i].ID,
				})
				if err != nil {
					return err
				}

				if len(mr) > 0 {
					commits[i].MergeRequest = &dto.MergeRequest{
						ID:        mr[0].ID,
						CreatedAt: mr[0].CreatedAt,
						UpdatedAt: mr[0].UpdatedAt,
						CommitId:  mr[0].SHA,
					}
				}

				return nil
			})
		}
	}
	if err := g.Wait(); err != nil {
		span.SetStatus(codes.Error, "failed to get merge request")
		span.RecordError(err)
		return err
	}

	span.AddEvent("repository commits listed successfully")
	span.SetStatus(codes.Ok, "repository commits listed successfully")
	return nil
}

// getUniqueUsers retrieves user information for a list of unique email addresses.
//
// Parameters:
//   - ctx: Context for the operation
//   - uniqueEmails: List of unique email addresses
//   - u: Repository usecase implementation
//
// Returns:
//   - map[string]dto.User: Map of user information by email
//   - error: Any error that occurred during retrieval
func getUniqueUsers(ctx context.Context, uniqueEmails []string, u *impl) (map[string]dto.User, error) {
	uniqueUsers := make(map[string]dto.User)
	g, gCtx := errgroup.WithContext(ctx)
	g.SetLimit(8)

	for _, val := range uniqueEmails {
		select {
		case <-gCtx.Done():
			break
		default:
			g.Go(func() error {
				user, err := u.repo.FindUser(ctx, repository.FindUserFilter{
					Email: &val,
				})
				if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
					return err
				}

				if user != nil {
					uniqueUsers[val] = dto.User{}.FromEntity(*user)
				} else {
					uniqueUsers[val] = dto.User{Email: val, Name: "Admin"} // Assign admin user for missing ones
				}

				return nil
			})
		}
	}
	if err := g.Wait(); err != nil {
		return nil, err
	}
	return uniqueUsers, nil
}

// uniqueAuthorEmails extracts unique author email addresses from a list of commits.
//
// Parameters:
//   - commits: List of repository commits
//
// Returns:
//   - []string: List of unique author email addresses
func uniqueAuthorEmails(commits []dto.RepositoryCommit) []string {
	emailSet := make(map[string]struct{})
	var uniqueEmails []string

	for _, commit := range commits {
		if _, exists := emailSet[commit.AuthorEmail]; !exists {
			emailSet[commit.AuthorEmail] = struct{}{}
			uniqueEmails = append(uniqueEmails, commit.AuthorEmail)
		}
	}

	return uniqueEmails
}

// mapGitlabCommitResp converts GitLab commit responses to repository commit DTOs.
//
// Parameters:
//   - gitlabCommits: List of GitLab commit responses
//
// Returns:
//   - []dto.RepositoryCommit: List of repository commit DTOs
func mapGitlabCommitResp(gitlabCommits []gitlab.RepositoryCommit) []dto.RepositoryCommit {
	commits := make([]dto.RepositoryCommit, len(gitlabCommits))
	for i, item := range gitlabCommits {
		commits[i] = *commits[i].FromGitlab(item)
	}
	return commits
}

var getLastRepoCommitGroup singleflight.Group

// GetLastRepoCommit implements the RepositoryUsecase interface for retrieving the most recent commit.
// It fetches the latest commit for a specific repository branch or path.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: Input data containing repository ID and commit retrieval parameters
//
// Returns:
//   - *dto.RepositoryCommit: The most recent commit information
//   - error: Any error that occurred during retrieval
func (u *impl) GetLastRepoCommit(ctx context.Context, input dto.GetRepositoryCommitsInput) (*dto.RepositoryCommit, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.GetLastRepoCommit")
	defer span.End()

	adminRefGitID := enums.AdminRefGitID

	flightResp, err, _ := getLastRepoCommitGroup.Do(fmt.Sprint(adminRefGitID), func() (any, error) {
		return u.repo.FindUser(ctx, repository.FindUserFilter{RefGitUserID: &adminRefGitID})
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find admin user")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, usecase.ErrUserNotFound
		}
		return nil, err
	}
	adminUser := flightResp.(*entities.User)

	flightResp, err, _ = getLastRepoCommitGroup.Do(input.RepoID.String(), func() (any, error) {
		return u.repo.FindRepository(ctx, repository.RepositoryFilter{
			Type:      input.RepoID.RepoType(),
			Namespace: input.RepoID.Namespace(),
			Name:      input.RepoID.RepoName(),
		})
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find repository")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, usecase.ErrRepositoryNotFound
		}
		return nil, err
	}
	repo := flightResp.(*entities.Repository)

	token := ""
	if adminUser.GitlabAccessToken != nil {
		token = *adminUser.GitlabAccessToken
	}

	req := gitlab.GetProjectCommitsRequest{
		Id:      repo.RefGitRepoID,
		RefName: input.Ref,
		Path:    input.Path,
		Token:   token,
	}
	gitlabResp, err := u.gitlab.GetProjectCommits(ctx, req)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get project commits from gitlab")
		span.RecordError(err)
		return nil, err
	}

	if len(*gitlabResp.Data) == 0 {
		return nil, usecase.ErrCannotFindLastCommit
	}

	var lastCommit dto.RepositoryCommit
	lastCommit = *lastCommit.FromGitlab((*gitlabResp.Data)[0])

	span.SetStatus(codes.Ok, "get last repo commit successfully")
	return &lastCommit, nil
}
