package ecr

import (
	"context"

	"github.com/google/uuid"

	"api-server/configs"
	"api-server/internal/dto"
	awsGw "api-server/internal/gateways/aws"
	repository "api-server/internal/repositories"

	"k8s.io/client-go/kubernetes"
)

// ECRUsecase defines operations for working with Amazon ECR
type ECRUsecase interface {
	// // ListRepositories lists all ECR repositories in the account
	// ListRepositories(ctx context.Context) ([]dto.ECRRepository, error)
	//
	// // GetRepository gets information about a single ECR repository
	// GetRepository(ctx context.Context, repositoryName string) (*dto.ECRRepository, error)
	//
	// // CreateRepository creates a new ECR repository
	// CreateRepository(ctx context.Context, id uuid.UUID, prefixName string, input dto.ECRRepositoryCreateInput) (*dto.ECRRepository, error)
	//
	// // DeleteRepository deletes an ECR repository
	// DeleteRepository(ctx context.Context, repositoryName string) error
	//
	// // ListImages lists all images in a repository
	// ListImages(ctx context.Context, input ListImagesInput) ([]dto.ECRImage, *string, error)
	//
	// // GetAuthorizationToken gets an authorization token for ECR
	// GetAuthorizationToken(ctx context.Context, id uuid.UUID) (*dto.ECRAuthorizationToken, error)
	//
	// // GetRepositoryName return name of the repository with a given prefix and id
	// GetRepositoryName(ctx context.Context, prefixName string, id uuid.UUID) string
	//
	// // DeleteImage deletes an image from an ECR repository
	// DeleteImage(ctx context.Context, repositoryName string, imageIdentifier dto.DeleteECRImageRequest) error

	// GetDeploymentStatus gets the status of a Kubernetes deployment
	GetDeploymentStatus(ctx context.Context, deploymentID uuid.UUID) (*dto.GetDeploymentStatusResponse, error)

	// ValidateECRImageExists validates that an ECR image exists in AWS ECR
	ValidateECRImageExists(ctx context.Context, imageURI string) error

	ListECRDeployment(ctx context.Context, currentUserID uuid.UUID, in dto.ListECRDeploymentInput) ([]dto.ECRDeployment, int64, error)

	// CreateECRDeployment deploys an ECR image to a Kubernetes cluster
	CreateECRDeployment(ctx context.Context, currentUserID uuid.UUID, input CreateECRDeploymentInput) (*dto.ECRDeployment, error)

	DeleteECRDeployment(ctx context.Context, currentUserID uuid.UUID, deploymentID uuid.UUID) error

	// StopECRDeployment stops a running ECR deployment by removing Kubernetes resources but keeping the database record
	StopECRDeployment(ctx context.Context, deploymentID uuid.UUID) error

	// DeleteKubernetesDeployment deletes a Kubernetes deployment
	// DeleteKubernetesDeployment(ctx context.Context, namespace, name string) error

	// UpdateECRDeployment
	UpdateECRDeployment(ctx context.Context, currentUserID uuid.UUID, input UpdateECRDeploymentInput) (*dto.ECRDeployment, error)

	CreateECRDeploymentEnv(ctx context.Context, currentUserID uuid.UUID, input dto.CreateECRDeploymentEnvInput) error

	UpdateECRDeploymentEnv(ctx context.Context, currentUserID uuid.UUID, input dto.UpdateECRDeploymentEnvInput) error

	DeleteECRDeploymentEnv(ctx context.Context, currentUserID uuid.UUID, input dto.DeleteECRDeploymentEnvInput) error

	// GetECRDeployment gets a single ECR deployment by ID
	GetECRDeployment(ctx context.Context, currentUserID uuid.UUID, deploymentID uuid.UUID) (*dto.ECRDeployment, error)

	DeployECR(ctx context.Context, currentUserID uuid.UUID, deploymentID uuid.UUID) (*KubernetesDeploymentResult, error)

	GetPodLogs(ctx context.Context, deploymentID uuid.UUID) (chan dto.GetDeploymentLogsResponse, error)
}

type impl struct {
	config    *configs.GlobalConfig
	awsClient awsGw.AWSClient

	repo repository.Repository

	// Kubernetes client for managing deployments
	k8sClient kubernetes.Interface
}

var _ ECRUsecase = (*impl)(nil)

// New creates a new instance of ECRUsecase
func New(
	config *configs.GlobalConfig,
	awsClient awsGw.AWSClient,
	repository repository.Repository,
	k8sClient kubernetes.Interface,
) ECRUsecase {
	return &impl{
		config:    config,
		awsClient: awsClient,
		repo:      repository,
		k8sClient: k8sClient,
	}
}

// // ListRepositories lists all ECR repositories in the account
// func (u *impl) ListRepositories(ctx context.Context) ([]dto.ECRRepository, error) {
// 	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.ecr.ListRepositories")
// 	defer span.End()
//
// 	input := &ecr.DescribeRepositoriesInput{}
// 	output, err := u.awsClient.ECR().DescribeRepositories(ctx, input)
// 	if err != nil {
// 		span.SetStatus(codes.Error, "failed to list ECR repositories")
// 		span.RecordError(err)
// 		return nil, fmt.Errorf("failed to list ECR repositories: %w", err)
// 	}
//
// 	// Map ECR repositories to DTO
// 	repositories := make([]dto.ECRRepository, 0, len(output.Repositories))
// 	for _, repo := range output.Repositories {
// 		repositories = append(repositories, dto.ECRRepository{
// 			RepositoryName: *repo.RepositoryName,
// 			RepositoryURI:  *repo.RepositoryUri,
// 			RegistryID:     *repo.RegistryId,
// 			CreatedAt:      *repo.CreatedAt,
// 		})
// 	}
//
// 	return repositories, nil
// }
//
// // GetRepository gets information about a single ECR repository
// func (u *impl) GetRepository(ctx context.Context, repositoryName string) (*dto.ECRRepository, error) {
// 	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.ecr.GetRepository")
// 	defer span.End()
//
// 	span.AddEvent("Creating ECR repository", trace.WithAttributes(attribute.String("erc_repository_name", repositoryName)))
//
// 	input := &ecr.DescribeRepositoriesInput{
// 		RepositoryNames: []string{repositoryName},
// 	}
//
// 	output, err := u.awsClient.ECR().DescribeRepositories(ctx, input)
// 	if err != nil {
// 		span.SetStatus(codes.Error, "failed to get ECR repository")
// 		span.RecordError(err)
//
// 		var e *types.RepositoryNotFoundException
// 		if errors.As(err, &e) {
// 			// If the error is not a "not found" error, return it
// 			return nil, usecase.ErrECRRepositoryNotFound
// 		}
// 		return nil, errors.New("failed to get ECR repository")
// 	}
//
// 	if len(output.Repositories) == 0 {
// 		return nil, fmt.Errorf("repository not found: %s", repositoryName)
// 	}
//
// 	repo := output.Repositories[0]
// 	return &dto.ECRRepository{
// 		RepositoryName: *repo.RepositoryName,
// 		RepositoryURI:  *repo.RepositoryUri,
// 		RegistryID:     *repo.RegistryId,
// 		CreatedAt:      *repo.CreatedAt,
// 	}, nil
// }
//
// // CreateRepository creates a new ECR repository
// func (u *impl) CreateRepository(ctx context.Context, id uuid.UUID, prefixName string, input dto.ECRRepositoryCreateInput) (*dto.ECRRepository, error) {
// 	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.ecr.CreateRepository")
// 	defer span.End()
//
// 	repositoryName := u.GetRepositoryName(ctx, prefixName, id)
// 	// Check if the repository already exists
// 	existingRepo, err := u.GetRepository(ctx, repositoryName)
// 	if err == nil {
// 		// Repository already exists, return it
// 		return existingRepo, nil
// 	} else if !errors.Is(err, usecase.ErrECRRepositoryNotFound) {
// 		// If the error is not a "not found" error, return it
// 		span.SetStatus(codes.Error, "failed to check if ECR repository exists")
// 		span.RecordError(err)
// 		return nil, err
// 	}
// 	// If the repository does not exist, create it
// 	span.AddEvent("Creating ECR repository", trace.WithAttributes(attribute.String("repository_name", repositoryName)))
//
// 	// Set the default value for ImageTagMutability if not provided
// 	if input.ImageTagMutability == "" {
// 		input.ImageTagMutability = enums.ImageTagMutabilityMutable
// 	}
// 	// Create the repository
// 	createInput := &ecr.CreateRepositoryInput{
// 		RepositoryName:     aws.String(repositoryName),
// 		ImageTagMutability: types.ImageTagMutability(input.ImageTagMutability),
// 	}
//
// 	output, err := u.awsClient.ECR().CreateRepository(ctx, createInput)
// 	if err != nil {
// 		span.SetStatus(codes.Error, "failed to create ECR repository")
// 		span.RecordError(err)
// 		return nil, err
// 	}
//
// 	repo := output.Repository
// 	return &dto.ECRRepository{
// 		RepositoryName: *repo.RepositoryName,
// 		RepositoryURI:  *repo.RepositoryUri,
// 		RegistryID:     *repo.RegistryId,
// 		CreatedAt:      *repo.CreatedAt,
// 	}, nil
// }
//
// // DeleteRepository deletes an ECR repository
// func (u *impl) DeleteRepository(ctx context.Context, repositoryName string) error {
// 	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.ecr.DeleteRepository",
// 		trace.WithAttributes(attribute.String("erc_repository_name", repositoryName)))
// 	defer span.End()
//
// 	input := &ecr.DeleteRepositoryInput{
// 		RepositoryName: aws.String(repositoryName),
// 		Force:          true, // Force delete even if images are present
// 	}
//
// 	_, err := u.awsClient.ECR().DeleteRepository(ctx, input)
// 	if err != nil {
// 		span.SetStatus(codes.Error, "failed to delete ECR repository")
// 		span.RecordError(err)
//
// 		var e *types.RepositoryNotFoundException
// 		if errors.As(err, &e) {
// 			// If the error is not a "not found" error, skip
// 			return nil
// 		}
// 		return errors.New("failed to delete ECR repository")
// 	}
//
// 	return nil
// }
//
// type ListImagesInput struct {
// 	RepositoryName string
// 	NextToken      *string
// 	PerPage        int32
// }
//
// // ListImages lists all images in a repository
// func (u *impl) ListImages(ctx context.Context, input ListImagesInput) ([]dto.ECRImage, *string, error) {
// 	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.ecr.ListImages",
// 		trace.WithAttributes(attribute.String("erc_repository_name", input.RepositoryName)))
// 	defer span.End()
//
// 	if input.NextToken != nil && *input.NextToken == "" {
// 		input.NextToken = nil
// 	}
//
// 	req := &ecr.DescribeImagesInput{
// 		RepositoryName: aws.String(input.RepositoryName),
// 		MaxResults:     &input.PerPage,
// 		NextToken:      input.NextToken,
// 	}
//
// 	output, err := u.awsClient.ECR().DescribeImages(ctx, req)
// 	if err != nil {
// 		span.SetStatus(codes.Error, "failed to list ECR images")
// 		span.RecordError(err)
//
// 		var e *types.RepositoryNotFoundException
// 		if errors.As(err, &e) {
// 			return nil, nil, usecase.ErrECRRepositoryNotFound
// 		}
// 		return nil, nil, errors.New("failed to list ECR images")
// 	}
//
// 	// Map ECR images to DTO
// 	images := make([]dto.ECRImage, 0, len(output.ImageDetails))
// 	for _, img := range output.ImageDetails {
// 		var tags []string
// 		if img.ImageTags != nil {
// 			tags = img.ImageTags
// 		}
//
// 		images = append(images, dto.ECRImage{
// 			ImageDigest:      img.ImageDigest,
// 			RegistryID:       img.RegistryId,
// 			RepositoryName:   img.RepositoryName,
// 			ImageTags:        tags,
// 			ImageSizeInBytes: img.ImageSizeInBytes,
// 			PushedAt:         img.ImagePushedAt,
// 		})
// 	}
//
// 	return images, output.NextToken, nil
// }
//
// // GetAuthorizationToken gets an authorization token for ECR
// func (u *impl) GetAuthorizationToken(ctx context.Context, id uuid.UUID) (*dto.ECRAuthorizationToken, error) {
// 	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.ecr.GetAuthorizationToken")
// 	defer span.End()
//
// 	tempCreds, err := u.assumeECRRoleForUser(ctx, id)
// 	if err != nil {
// 		span.SetStatus(codes.Error, "failed to assume ECR role for id")
// 		span.RecordError(err)
// 		return nil, fmt.Errorf("failed to assume ECR role for id: %w", err)
// 	}
//
// 	tempCredsProvider := credentials.NewStaticCredentialsProvider(
// 		tempCreds.AccessKeyId,
// 		tempCreds.SecretAccessKey,
// 		tempCreds.SessionToken,
// 	)
// 	ecrClient := ecr.New(ecr.Options{
// 		Region:      u.config.AwsConfig.Region,
// 		Credentials: tempCredsProvider,
// 	})
//
// 	input := &ecr.GetAuthorizationTokenInput{}
// 	output, err := ecrClient.GetAuthorizationToken(ctx, input)
// 	if err != nil {
// 		span.SetStatus(codes.Error, "failed to get ECR authorization token")
// 		span.RecordError(err)
// 		return nil, fmt.Errorf("failed to get ECR authorization token: %w", err)
// 	}
//
// 	if len(output.AuthorizationData) == 0 {
// 		return nil, errors.New("no authorization data returned")
// 	}
//
// 	authData := output.AuthorizationData[0]
//
// 	// Decode the token: it's base64("AWS:<password>")
// 	decoded, err := base64.StdEncoding.DecodeString(*authData.AuthorizationToken)
// 	if err != nil {
// 		return nil, errors.New("failed to decode auth token")
// 	}
//
// 	parts := string(decoded)
// 	const prefix = "AWS:"
// 	if len(parts) <= len(prefix) || parts[:len(prefix)] != prefix {
// 		return nil, errors.New("invalid auth token format")
// 	}
// 	password := parts[len(prefix):]
//
// 	return &dto.ECRAuthorizationToken{
// 		Password:           password,
// 		AuthorizationToken: *authData.AuthorizationToken,
// 		ExpiresAt:          *authData.ExpiresAt,
// 		ProxyEndpoint:      *authData.ProxyEndpoint,
// 	}, nil
// }
//
// func (u *impl) GetRepositoryName(ctx context.Context, prefixName string, id uuid.UUID) string {
// 	return fmt.Sprintf("%s/%s-%s", u.config.AwsConfig.EcrRepositoryNamespace, prefixName, id.String())
// }
//
// type TempECRCredentials struct {
// 	AccessKeyId     string
// 	SecretAccessKey string
// 	SessionToken    string
// 	Expiration      time.Time
// 	Region          string
// }
//
// func (u *impl) assumeECRRoleForUser(ctx context.Context, id uuid.UUID) (*TempECRCredentials, error) {
// 	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.ecr.assumeECRRoleForUser")
// 	defer span.End()
//
// 	repoName := u.GetRepositoryName(ctx, "user", id)
// 	roleArn := fmt.Sprintf("arn:aws:iam::%s:role/ECRUserAccessRole", u.config.AwsConfig.AccountID)
//
// 	// Inline session policy scoped to the user's ECR repository
// 	policy := map[string]interface{}{
// 		"Version": "2012-10-17",
// 		"Statement": []map[string]interface{}{
// 			{
// 				"Effect": "Allow",
// 				"Action": []string{
// 					"ecr:BatchCheckLayerAvailability",
// 					"ecr:GetDownloadUrlForLayer",
// 					"ecr:BatchGetImage",
// 					"ecr:InitiateLayerUpload",
// 					"ecr:UploadLayerPart",
// 					"ecr:CompleteLayerUpload",
// 					"ecr:PutImage",
// 					"ecr:DescribeRepositories",
// 				},
// 				"Resource": fmt.Sprintf("arn:aws:ecr:%s:%s:repository/%s", u.config.AwsConfig.Region, u.config.AwsConfig.AccountID, repoName),
// 			},
// 			{
// 				"Effect": "Allow",
// 				"Action": []string{
// 					"ecr:GetAuthorizationToken",
// 				},
// 				"Resource": "*",
// 			},
// 		},
// 	}
//
// 	policyJson, err := json.Marshal(policy)
// 	if err != nil {
// 		span.SetStatus(codes.Error, "failed to get ECR authorization token")
// 		span.RecordError(err)
// 		return nil, fmt.Errorf("failed to marshal inline policy: %w", err)
// 	}
//
// 	roleSessionName := fmt.Sprintf("session-%s", id.String())
// 	resp, err := u.awsClient.STS().AssumeRole(ctx, &sts.AssumeRoleInput{
// 		RoleArn:         aws.String(roleArn),
// 		RoleSessionName: aws.String(roleSessionName),
// 		DurationSeconds: aws.Int32(3600), // 1 hour
// 		Policy:          aws.String(string(policyJson)),
// 	})
// 	if err != nil {
// 		span.SetStatus(codes.Error, "failed to assume role for ECR")
// 		span.AddEvent("AssumeRole failed", trace.WithAttributes(
// 			attribute.String("role_arn", roleArn),
// 			attribute.String("role_session_name", roleSessionName),
// 			attribute.String("policy", string(policyJson)),
// 		))
// 		span.RecordError(err)
// 		return nil, fmt.Errorf("assume role for ECR failed: %w", err)
// 	}
//
// 	creds := &TempECRCredentials{
// 		AccessKeyId:     *resp.Credentials.AccessKeyId,
// 		SecretAccessKey: *resp.Credentials.SecretAccessKey,
// 		SessionToken:    *resp.Credentials.SessionToken,
// 		Expiration:      *resp.Credentials.Expiration,
// 		Region:          u.config.AwsConfig.Region,
// 	}
//
// 	return creds, nil
// }
//
// // DeleteImage deletes an image from an ECR repository
// func (u *impl) DeleteImage(ctx context.Context, repositoryName string, req dto.DeleteECRImageRequest) error {
// 	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.ecr.DeleteImage",
// 		trace.WithAttributes(
// 			attribute.String("repository_name", repositoryName),
// 			attribute.String("image_digest", req.ImageDigest),
// 			attribute.StringSlice("image_tags", req.ImageTags),
// 		))
// 	defer span.End()
//
// 	// Create image identifier input based on provided parameters
// 	var imageIds []types.ImageIdentifier
// 	if req.ImageDigest != "" {
// 		imageIds = append(imageIds, types.ImageIdentifier{
// 			ImageDigest: aws.String(req.ImageDigest),
// 		})
// 	}
//
// 	for _, tag := range req.ImageTags {
// 		imageIds = append(imageIds, types.ImageIdentifier{
// 			ImageTag: aws.String(tag),
// 		})
// 	}
//
// 	if len(imageIds) == 0 {
// 		span.SetStatus(codes.Error, usecase.ErrNoImageIdentifierProvided.Error())
// 		span.RecordError(usecase.ErrNoImageIdentifierProvided)
// 		return usecase.ErrNoImageIdentifierProvided
// 	}
//
// 	input := &ecr.BatchDeleteImageInput{
// 		RepositoryName: aws.String(repositoryName),
// 		ImageIds:       imageIds,
// 	}
// 	output, err := u.awsClient.ECR().BatchDeleteImage(ctx, input)
// 	if err != nil {
// 		span.SetStatus(codes.Error, "failed to delete ECR image")
// 		span.RecordError(err)
//
// 		var repoNotFoundErr *types.RepositoryNotFoundException
// 		if errors.As(err, &repoNotFoundErr) {
// 			return usecase.ErrECRRepositoryNotFound
// 		}
//
// 		return fmt.Errorf("failed to delete ECR image: %w", err)
// 	}
//
// 	// Check for failures in the deletion process
// 	if len(output.Failures) > 0 {
// 		failureMessages := make([]string, len(output.Failures))
// 		for i, failure := range output.Failures {
// 			failureMessages[i] = aws.ToString(failure.FailureReason)
// 			span.AddEvent("Image deletion failure", trace.WithAttributes(
// 				attribute.String("failure_code", string(failure.FailureCode)),
// 				attribute.String("failure_reason", aws.ToString(failure.FailureReason)),
// 			))
// 		}
//
// 		err := fmt.Errorf("failed to delete some images: %s", failureMessages)
// 		span.SetStatus(codes.Error, err.Error())
// 		span.RecordError(err)
// 		return err
// 	}
//
// 	span.AddEvent("Images deleted successfully", trace.WithAttributes(
// 		attribute.Int("images_deleted", len(output.ImageIds)),
// 	))
//
// 	return nil
// }
