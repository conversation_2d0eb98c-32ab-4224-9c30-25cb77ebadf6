package ecr_test

import (
	"context"
	"errors"
	"testing"

	awsEcr "github.com/aws/aws-sdk-go-v2/service/ecr"
	awsEcrTypes "github.com/aws/aws-sdk-go-v2/service/ecr/types"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	awsGwMocks "api-server/internal/gateways/aws/mocks"
	"api-server/internal/usecase/ecr"
)

func TestValidateECRImageExists(t *testing.T) {

	tests := []struct {
		name          string
		imageURI      string
		setupMocks    func(*awsGwMocks.MockAWSClient)
		expectedError error
	}{
		{
			name:     "successful validation",
			imageURI: "123456789012.dkr.ecr.us-west-2.amazonaws.com/my-repo:latest",
			setupMocks: func(aws *awsGwMocks.MockAWSClient) {
				aws.On("DescribeRepositories", mock.Anything, mock.Anything).Return(&awsEcr.DescribeRepositoriesOutput{}, nil)
				aws.On("DescribeImages", mock.Anything, mock.Anything).Return(&awsEcr.DescribeImagesOutput{
					ImageDetails: []awsEcrTypes.ImageDetail{
						{
							ImageTags: []string{"latest"},
						},
					},
				}, nil)
			},
			expectedError: nil,
		},
		{
			name:     "AWS error",
			imageURI: "123456789012.dkr.ecr.us-west-2.amazonaws.com/my-repo:latest",
			setupMocks: func(aws *awsGwMocks.MockAWSClient) {
				aws.On("DescribeRepositories", mock.Anything, mock.Anything).Return(&awsEcr.DescribeRepositoriesOutput{}, errors.New("AWS error"))
				// aws.On("DescribeImages", mock.Anything, mock.Anything).Return(&awsEcr.DescribeImagesOutput{}, nil)
			},
			expectedError: errors.New("AWS error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mocks
			mockAWS := awsGwMocks.NewMockAWSClient(t)
			tt.setupMocks(mockAWS)

			// Create usecase instance
			usecase := ecr.New(nil, mockAWS, nil, nil)

			// Execute test
			err := usecase.ValidateECRImageExists(context.Background(), tt.imageURI)

			// Assertions
			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedError.Error())
			} else {
				assert.NoError(t, err)
			}

			// Verify all expectations
			mockAWS.AssertExpectations(t)
		})
	}
}
