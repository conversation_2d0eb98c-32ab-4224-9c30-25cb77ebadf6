package ecr

import (
	"context"
	"errors"
	"fmt"

	"github.com/aws/aws-sdk-go-v2/service/ecr"
	"github.com/aws/aws-sdk-go-v2/service/ecr/types"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"

	"api-server/internal/usecase"
	"api-server/internal/utils"
	"api-server/pkg/oteltrace"
)

// ValidateECRImageExists checks if the specified ECR image exists in AWS ECR
func (u *impl) ValidateECRImageExists(ctx context.Context, imageURI string) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.ecr.ValidateECRImageExists")
	defer span.End()

	span.AddEvent("Validating ECR image existence", trace.WithAttributes(
		attribute.String("image_uri", imageURI),
	))

	// First validate the URI format
	if err := utils.ValidateECRImageURI(imageURI); err != nil {
		span.SetStatus(codes.Error, "invalid ECR image URI format")
		span.RecordError(err)
		return usecase.ErrInvalidECRImageURI
	}

	// Parse the ECR image URI
	imageInfo, err := utils.ParseECRImageURI(imageURI)
	if err != nil {
		span.SetStatus(codes.Error, "failed to parse ECR image URI")
		span.RecordError(err)
		return usecase.ErrInvalidECRImageURI
	}

	span.SetAttributes(
		attribute.String("registry_id", imageInfo.RegistryID),
		attribute.String("region", imageInfo.Region),
		attribute.String("repository_name", imageInfo.RepositoryName),
		attribute.String("image_identifier", imageInfo.GetImageIdentifier()),
	)

	// Check if the repository exists first
	err = u.validateRepositoryExists(ctx, imageInfo.RepositoryName)
	if err != nil {
		return err
	}

	// Check if the specific image exists
	err = u.validateImageExists(ctx, imageInfo)
	if err != nil {
		return err
	}

	span.AddEvent("ECR image validation successful")
	return nil
}

// validateRepositoryExists checks if the ECR repository exists
func (u *impl) validateRepositoryExists(ctx context.Context, repositoryName string) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.ecr.validateRepositoryExists")
	defer span.End()

	span.SetAttributes(attribute.String("repository_name", repositoryName))

	input := &ecr.DescribeRepositoriesInput{
		RepositoryNames: []string{repositoryName},
	}

	_, err := u.awsClient.DescribeRepositories(ctx, input)
	if err != nil {
		span.SetStatus(codes.Error, "failed to describe ECR repository")
		span.RecordError(err)

		var repoNotFoundErr *types.RepositoryNotFoundException
		if errors.As(err, &repoNotFoundErr) {
			return usecase.ErrECRRepositoryNotFound
		}

		return fmt.Errorf("failed to validate ECR repository: %w", err)
	}

	return nil
}

// validateImageExists checks if the specific image exists in the repository
func (u *impl) validateImageExists(ctx context.Context, imageInfo *utils.ECRImageInfo) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.ecr.validateImageExists")
	defer span.End()

	span.SetAttributes(
		attribute.String("repository_name", imageInfo.RepositoryName),
		attribute.String("image_identifier", imageInfo.GetImageIdentifier()),
	)

	// Prepare image identifiers for the DescribeImages call
	var imageIds []types.ImageIdentifier

	if imageInfo.IsTaggedImage() {
		// Image identified by tag
		imageIds = append(imageIds, types.ImageIdentifier{
			ImageTag: &imageInfo.Tag,
		})
	} else if imageInfo.IsDigestImage() {
		// Image identified by digest
		imageIds = append(imageIds, types.ImageIdentifier{
			ImageDigest: &imageInfo.Digest,
		})
	}

	input := &ecr.DescribeImagesInput{
		RepositoryName: &imageInfo.RepositoryName,
		ImageIds:       imageIds,
	}

	output, err := u.awsClient.DescribeImages(ctx, input)
	if err != nil {
		span.SetStatus(codes.Error, "failed to describe ECR images")
		span.RecordError(err)

		var repoNotFoundErr *types.RepositoryNotFoundException
		if errors.As(err, &repoNotFoundErr) {
			return usecase.ErrECRRepositoryNotFound
		}

		var imageNotFoundErr *types.ImageNotFoundException
		if errors.As(err, &imageNotFoundErr) {
			return usecase.ErrECRImageNotFound
		}

		return fmt.Errorf("failed to validate ECR image: %w", err)
	}

	// Check if any images were returned
	if len(output.ImageDetails) == 0 {
		span.SetStatus(codes.Error, "ECR image not found")
		return usecase.ErrECRImageNotFound
	}

	// Verify that we found the exact image we're looking for
	err = verifyImageFound(imageInfo, output.ImageDetails)
	if err != nil {
		span.SetStatus(codes.Error, "ECR image not found with specified identifier")
		span.RecordError(err)
		return err
	}

	span.AddEvent("ECR image found successfully")
	return nil
}

// Verify that we found the exact image we're looking for
func verifyImageFound(imageInfo *utils.ECRImageInfo, imageDetails []types.ImageDetail) error {
	found := false
imageDefailLoop:
	for _, imageDetail := range imageDetails {
		if imageInfo.IsTaggedImage() {
			// Check if any of the image tags match
		imageTagLoop:
			for _, tag := range imageDetail.ImageTags {
				if tag == imageInfo.Tag {
					found = true
					break imageTagLoop
				}
			}
		} else if imageInfo.IsDigestImage() {
			// Check if the digest matches
			if imageDetail.ImageDigest != nil && *imageDetail.ImageDigest == imageInfo.Digest {
				found = true
			}
		}

		if found {
			break imageDefailLoop
		}
	}

	if !found {
		return usecase.ErrECRImageNotFound
	}

	return nil
}
