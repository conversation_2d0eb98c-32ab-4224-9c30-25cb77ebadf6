// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	dto "api-server/internal/dto"
	ecr "api-server/internal/usecase/ecr"
	context "context"

	mock "github.com/stretchr/testify/mock"

	uuid "github.com/google/uuid"
)

// MockECRUsecase is an autogenerated mock type for the ECRUsecase type
type MockECRUsecase struct {
	mock.Mock
}

type MockECRUsecase_Expecter struct {
	mock *mock.Mock
}

func (_m *MockECRUsecase) EXPECT() *MockECRUsecase_Expecter {
	return &MockECRUsecase_Expecter{mock: &_m.Mock}
}

// CreateECRDeployment provides a mock function with given fields: ctx, currentUserID, input
func (_m *MockECRUsecase) CreateECRDeployment(ctx context.Context, currentUserID uuid.UUID, input ecr.CreateECRDeploymentInput) (*dto.ECRDeployment, error) {
	ret := _m.Called(ctx, currentUserID, input)

	if len(ret) == 0 {
		panic("no return value specified for CreateECRDeployment")
	}

	var r0 *dto.ECRDeployment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID, ecr.CreateECRDeploymentInput) (*dto.ECRDeployment, error)); ok {
		return rf(ctx, currentUserID, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID, ecr.CreateECRDeploymentInput) *dto.ECRDeployment); ok {
		r0 = rf(ctx, currentUserID, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*dto.ECRDeployment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uuid.UUID, ecr.CreateECRDeploymentInput) error); ok {
		r1 = rf(ctx, currentUserID, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockECRUsecase_CreateECRDeployment_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateECRDeployment'
type MockECRUsecase_CreateECRDeployment_Call struct {
	*mock.Call
}

// CreateECRDeployment is a helper method to define mock.On call
//   - ctx context.Context
//   - currentUserID uuid.UUID
//   - input ecr.CreateECRDeploymentInput
func (_e *MockECRUsecase_Expecter) CreateECRDeployment(ctx interface{}, currentUserID interface{}, input interface{}) *MockECRUsecase_CreateECRDeployment_Call {
	return &MockECRUsecase_CreateECRDeployment_Call{Call: _e.mock.On("CreateECRDeployment", ctx, currentUserID, input)}
}

func (_c *MockECRUsecase_CreateECRDeployment_Call) Run(run func(ctx context.Context, currentUserID uuid.UUID, input ecr.CreateECRDeploymentInput)) *MockECRUsecase_CreateECRDeployment_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID), args[2].(ecr.CreateECRDeploymentInput))
	})
	return _c
}

func (_c *MockECRUsecase_CreateECRDeployment_Call) Return(_a0 *dto.ECRDeployment, _a1 error) *MockECRUsecase_CreateECRDeployment_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockECRUsecase_CreateECRDeployment_Call) RunAndReturn(run func(context.Context, uuid.UUID, ecr.CreateECRDeploymentInput) (*dto.ECRDeployment, error)) *MockECRUsecase_CreateECRDeployment_Call {
	_c.Call.Return(run)
	return _c
}

// CreateECRDeploymentEnv provides a mock function with given fields: ctx, currentUserID, input
func (_m *MockECRUsecase) CreateECRDeploymentEnv(ctx context.Context, currentUserID uuid.UUID, input dto.CreateECRDeploymentEnvInput) error {
	ret := _m.Called(ctx, currentUserID, input)

	if len(ret) == 0 {
		panic("no return value specified for CreateECRDeploymentEnv")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID, dto.CreateECRDeploymentEnvInput) error); ok {
		r0 = rf(ctx, currentUserID, input)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockECRUsecase_CreateECRDeploymentEnv_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateECRDeploymentEnv'
type MockECRUsecase_CreateECRDeploymentEnv_Call struct {
	*mock.Call
}

// CreateECRDeploymentEnv is a helper method to define mock.On call
//   - ctx context.Context
//   - currentUserID uuid.UUID
//   - input dto.CreateECRDeploymentEnvInput
func (_e *MockECRUsecase_Expecter) CreateECRDeploymentEnv(ctx interface{}, currentUserID interface{}, input interface{}) *MockECRUsecase_CreateECRDeploymentEnv_Call {
	return &MockECRUsecase_CreateECRDeploymentEnv_Call{Call: _e.mock.On("CreateECRDeploymentEnv", ctx, currentUserID, input)}
}

func (_c *MockECRUsecase_CreateECRDeploymentEnv_Call) Run(run func(ctx context.Context, currentUserID uuid.UUID, input dto.CreateECRDeploymentEnvInput)) *MockECRUsecase_CreateECRDeploymentEnv_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID), args[2].(dto.CreateECRDeploymentEnvInput))
	})
	return _c
}

func (_c *MockECRUsecase_CreateECRDeploymentEnv_Call) Return(_a0 error) *MockECRUsecase_CreateECRDeploymentEnv_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockECRUsecase_CreateECRDeploymentEnv_Call) RunAndReturn(run func(context.Context, uuid.UUID, dto.CreateECRDeploymentEnvInput) error) *MockECRUsecase_CreateECRDeploymentEnv_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteECRDeployment provides a mock function with given fields: ctx, currentUserID, deploymentID
func (_m *MockECRUsecase) DeleteECRDeployment(ctx context.Context, currentUserID uuid.UUID, deploymentID uuid.UUID) error {
	ret := _m.Called(ctx, currentUserID, deploymentID)

	if len(ret) == 0 {
		panic("no return value specified for DeleteECRDeployment")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID, uuid.UUID) error); ok {
		r0 = rf(ctx, currentUserID, deploymentID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockECRUsecase_DeleteECRDeployment_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteECRDeployment'
type MockECRUsecase_DeleteECRDeployment_Call struct {
	*mock.Call
}

// DeleteECRDeployment is a helper method to define mock.On call
//   - ctx context.Context
//   - currentUserID uuid.UUID
//   - deploymentID uuid.UUID
func (_e *MockECRUsecase_Expecter) DeleteECRDeployment(ctx interface{}, currentUserID interface{}, deploymentID interface{}) *MockECRUsecase_DeleteECRDeployment_Call {
	return &MockECRUsecase_DeleteECRDeployment_Call{Call: _e.mock.On("DeleteECRDeployment", ctx, currentUserID, deploymentID)}
}

func (_c *MockECRUsecase_DeleteECRDeployment_Call) Run(run func(ctx context.Context, currentUserID uuid.UUID, deploymentID uuid.UUID)) *MockECRUsecase_DeleteECRDeployment_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID), args[2].(uuid.UUID))
	})
	return _c
}

func (_c *MockECRUsecase_DeleteECRDeployment_Call) Return(_a0 error) *MockECRUsecase_DeleteECRDeployment_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockECRUsecase_DeleteECRDeployment_Call) RunAndReturn(run func(context.Context, uuid.UUID, uuid.UUID) error) *MockECRUsecase_DeleteECRDeployment_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteECRDeploymentEnv provides a mock function with given fields: ctx, currentUserID, input
func (_m *MockECRUsecase) DeleteECRDeploymentEnv(ctx context.Context, currentUserID uuid.UUID, input dto.DeleteECRDeploymentEnvInput) error {
	ret := _m.Called(ctx, currentUserID, input)

	if len(ret) == 0 {
		panic("no return value specified for DeleteECRDeploymentEnv")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID, dto.DeleteECRDeploymentEnvInput) error); ok {
		r0 = rf(ctx, currentUserID, input)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockECRUsecase_DeleteECRDeploymentEnv_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteECRDeploymentEnv'
type MockECRUsecase_DeleteECRDeploymentEnv_Call struct {
	*mock.Call
}

// DeleteECRDeploymentEnv is a helper method to define mock.On call
//   - ctx context.Context
//   - currentUserID uuid.UUID
//   - input dto.DeleteECRDeploymentEnvInput
func (_e *MockECRUsecase_Expecter) DeleteECRDeploymentEnv(ctx interface{}, currentUserID interface{}, input interface{}) *MockECRUsecase_DeleteECRDeploymentEnv_Call {
	return &MockECRUsecase_DeleteECRDeploymentEnv_Call{Call: _e.mock.On("DeleteECRDeploymentEnv", ctx, currentUserID, input)}
}

func (_c *MockECRUsecase_DeleteECRDeploymentEnv_Call) Run(run func(ctx context.Context, currentUserID uuid.UUID, input dto.DeleteECRDeploymentEnvInput)) *MockECRUsecase_DeleteECRDeploymentEnv_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID), args[2].(dto.DeleteECRDeploymentEnvInput))
	})
	return _c
}

func (_c *MockECRUsecase_DeleteECRDeploymentEnv_Call) Return(_a0 error) *MockECRUsecase_DeleteECRDeploymentEnv_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockECRUsecase_DeleteECRDeploymentEnv_Call) RunAndReturn(run func(context.Context, uuid.UUID, dto.DeleteECRDeploymentEnvInput) error) *MockECRUsecase_DeleteECRDeploymentEnv_Call {
	_c.Call.Return(run)
	return _c
}

// DeployECR provides a mock function with given fields: ctx, currentUserID, deploymentID
func (_m *MockECRUsecase) DeployECR(ctx context.Context, currentUserID uuid.UUID, deploymentID uuid.UUID) (*ecr.KubernetesDeploymentResult, error) {
	ret := _m.Called(ctx, currentUserID, deploymentID)

	if len(ret) == 0 {
		panic("no return value specified for DeployECR")
	}

	var r0 *ecr.KubernetesDeploymentResult
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID, uuid.UUID) (*ecr.KubernetesDeploymentResult, error)); ok {
		return rf(ctx, currentUserID, deploymentID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID, uuid.UUID) *ecr.KubernetesDeploymentResult); ok {
		r0 = rf(ctx, currentUserID, deploymentID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*ecr.KubernetesDeploymentResult)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uuid.UUID, uuid.UUID) error); ok {
		r1 = rf(ctx, currentUserID, deploymentID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockECRUsecase_DeployECR_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeployECR'
type MockECRUsecase_DeployECR_Call struct {
	*mock.Call
}

// DeployECR is a helper method to define mock.On call
//   - ctx context.Context
//   - currentUserID uuid.UUID
//   - deploymentID uuid.UUID
func (_e *MockECRUsecase_Expecter) DeployECR(ctx interface{}, currentUserID interface{}, deploymentID interface{}) *MockECRUsecase_DeployECR_Call {
	return &MockECRUsecase_DeployECR_Call{Call: _e.mock.On("DeployECR", ctx, currentUserID, deploymentID)}
}

func (_c *MockECRUsecase_DeployECR_Call) Run(run func(ctx context.Context, currentUserID uuid.UUID, deploymentID uuid.UUID)) *MockECRUsecase_DeployECR_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID), args[2].(uuid.UUID))
	})
	return _c
}

func (_c *MockECRUsecase_DeployECR_Call) Return(_a0 *ecr.KubernetesDeploymentResult, _a1 error) *MockECRUsecase_DeployECR_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockECRUsecase_DeployECR_Call) RunAndReturn(run func(context.Context, uuid.UUID, uuid.UUID) (*ecr.KubernetesDeploymentResult, error)) *MockECRUsecase_DeployECR_Call {
	_c.Call.Return(run)
	return _c
}

// GetDeploymentStatus provides a mock function with given fields: ctx, deploymentID
func (_m *MockECRUsecase) GetDeploymentStatus(ctx context.Context, deploymentID uuid.UUID) (*dto.GetDeploymentStatusResponse, error) {
	ret := _m.Called(ctx, deploymentID)

	if len(ret) == 0 {
		panic("no return value specified for GetDeploymentStatus")
	}

	var r0 *dto.GetDeploymentStatusResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) (*dto.GetDeploymentStatusResponse, error)); ok {
		return rf(ctx, deploymentID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) *dto.GetDeploymentStatusResponse); ok {
		r0 = rf(ctx, deploymentID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*dto.GetDeploymentStatusResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uuid.UUID) error); ok {
		r1 = rf(ctx, deploymentID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockECRUsecase_GetDeploymentStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDeploymentStatus'
type MockECRUsecase_GetDeploymentStatus_Call struct {
	*mock.Call
}

// GetDeploymentStatus is a helper method to define mock.On call
//   - ctx context.Context
//   - deploymentID uuid.UUID
func (_e *MockECRUsecase_Expecter) GetDeploymentStatus(ctx interface{}, deploymentID interface{}) *MockECRUsecase_GetDeploymentStatus_Call {
	return &MockECRUsecase_GetDeploymentStatus_Call{Call: _e.mock.On("GetDeploymentStatus", ctx, deploymentID)}
}

func (_c *MockECRUsecase_GetDeploymentStatus_Call) Run(run func(ctx context.Context, deploymentID uuid.UUID)) *MockECRUsecase_GetDeploymentStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *MockECRUsecase_GetDeploymentStatus_Call) Return(_a0 *dto.GetDeploymentStatusResponse, _a1 error) *MockECRUsecase_GetDeploymentStatus_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockECRUsecase_GetDeploymentStatus_Call) RunAndReturn(run func(context.Context, uuid.UUID) (*dto.GetDeploymentStatusResponse, error)) *MockECRUsecase_GetDeploymentStatus_Call {
	_c.Call.Return(run)
	return _c
}

// GetECRDeployment provides a mock function with given fields: ctx, currentUserID, deploymentID
func (_m *MockECRUsecase) GetECRDeployment(ctx context.Context, currentUserID uuid.UUID, deploymentID uuid.UUID) (*dto.ECRDeployment, error) {
	ret := _m.Called(ctx, currentUserID, deploymentID)

	if len(ret) == 0 {
		panic("no return value specified for GetECRDeployment")
	}

	var r0 *dto.ECRDeployment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID, uuid.UUID) (*dto.ECRDeployment, error)); ok {
		return rf(ctx, currentUserID, deploymentID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID, uuid.UUID) *dto.ECRDeployment); ok {
		r0 = rf(ctx, currentUserID, deploymentID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*dto.ECRDeployment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uuid.UUID, uuid.UUID) error); ok {
		r1 = rf(ctx, currentUserID, deploymentID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockECRUsecase_GetECRDeployment_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetECRDeployment'
type MockECRUsecase_GetECRDeployment_Call struct {
	*mock.Call
}

// GetECRDeployment is a helper method to define mock.On call
//   - ctx context.Context
//   - currentUserID uuid.UUID
//   - deploymentID uuid.UUID
func (_e *MockECRUsecase_Expecter) GetECRDeployment(ctx interface{}, currentUserID interface{}, deploymentID interface{}) *MockECRUsecase_GetECRDeployment_Call {
	return &MockECRUsecase_GetECRDeployment_Call{Call: _e.mock.On("GetECRDeployment", ctx, currentUserID, deploymentID)}
}

func (_c *MockECRUsecase_GetECRDeployment_Call) Run(run func(ctx context.Context, currentUserID uuid.UUID, deploymentID uuid.UUID)) *MockECRUsecase_GetECRDeployment_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID), args[2].(uuid.UUID))
	})
	return _c
}

func (_c *MockECRUsecase_GetECRDeployment_Call) Return(_a0 *dto.ECRDeployment, _a1 error) *MockECRUsecase_GetECRDeployment_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockECRUsecase_GetECRDeployment_Call) RunAndReturn(run func(context.Context, uuid.UUID, uuid.UUID) (*dto.ECRDeployment, error)) *MockECRUsecase_GetECRDeployment_Call {
	_c.Call.Return(run)
	return _c
}

// GetPodLogs provides a mock function with given fields: ctx, deploymentID
func (_m *MockECRUsecase) GetPodLogs(ctx context.Context, deploymentID uuid.UUID) (chan dto.GetDeploymentLogsResponse, error) {
	ret := _m.Called(ctx, deploymentID)

	if len(ret) == 0 {
		panic("no return value specified for GetPodLogs")
	}

	var r0 chan dto.GetDeploymentLogsResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) (chan dto.GetDeploymentLogsResponse, error)); ok {
		return rf(ctx, deploymentID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) chan dto.GetDeploymentLogsResponse); ok {
		r0 = rf(ctx, deploymentID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(chan dto.GetDeploymentLogsResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uuid.UUID) error); ok {
		r1 = rf(ctx, deploymentID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockECRUsecase_GetPodLogs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetPodLogs'
type MockECRUsecase_GetPodLogs_Call struct {
	*mock.Call
}

// GetPodLogs is a helper method to define mock.On call
//   - ctx context.Context
//   - deploymentID uuid.UUID
func (_e *MockECRUsecase_Expecter) GetPodLogs(ctx interface{}, deploymentID interface{}) *MockECRUsecase_GetPodLogs_Call {
	return &MockECRUsecase_GetPodLogs_Call{Call: _e.mock.On("GetPodLogs", ctx, deploymentID)}
}

func (_c *MockECRUsecase_GetPodLogs_Call) Run(run func(ctx context.Context, deploymentID uuid.UUID)) *MockECRUsecase_GetPodLogs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *MockECRUsecase_GetPodLogs_Call) Return(_a0 chan dto.GetDeploymentLogsResponse, _a1 error) *MockECRUsecase_GetPodLogs_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockECRUsecase_GetPodLogs_Call) RunAndReturn(run func(context.Context, uuid.UUID) (chan dto.GetDeploymentLogsResponse, error)) *MockECRUsecase_GetPodLogs_Call {
	_c.Call.Return(run)
	return _c
}

// ListECRDeployment provides a mock function with given fields: ctx, currentUserID, in
func (_m *MockECRUsecase) ListECRDeployment(ctx context.Context, currentUserID uuid.UUID, in dto.ListECRDeploymentInput) ([]dto.ECRDeployment, int64, error) {
	ret := _m.Called(ctx, currentUserID, in)

	if len(ret) == 0 {
		panic("no return value specified for ListECRDeployment")
	}

	var r0 []dto.ECRDeployment
	var r1 int64
	var r2 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID, dto.ListECRDeploymentInput) ([]dto.ECRDeployment, int64, error)); ok {
		return rf(ctx, currentUserID, in)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID, dto.ListECRDeploymentInput) []dto.ECRDeployment); ok {
		r0 = rf(ctx, currentUserID, in)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]dto.ECRDeployment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uuid.UUID, dto.ListECRDeploymentInput) int64); ok {
		r1 = rf(ctx, currentUserID, in)
	} else {
		r1 = ret.Get(1).(int64)
	}

	if rf, ok := ret.Get(2).(func(context.Context, uuid.UUID, dto.ListECRDeploymentInput) error); ok {
		r2 = rf(ctx, currentUserID, in)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// MockECRUsecase_ListECRDeployment_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListECRDeployment'
type MockECRUsecase_ListECRDeployment_Call struct {
	*mock.Call
}

// ListECRDeployment is a helper method to define mock.On call
//   - ctx context.Context
//   - currentUserID uuid.UUID
//   - in dto.ListECRDeploymentInput
func (_e *MockECRUsecase_Expecter) ListECRDeployment(ctx interface{}, currentUserID interface{}, in interface{}) *MockECRUsecase_ListECRDeployment_Call {
	return &MockECRUsecase_ListECRDeployment_Call{Call: _e.mock.On("ListECRDeployment", ctx, currentUserID, in)}
}

func (_c *MockECRUsecase_ListECRDeployment_Call) Run(run func(ctx context.Context, currentUserID uuid.UUID, in dto.ListECRDeploymentInput)) *MockECRUsecase_ListECRDeployment_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID), args[2].(dto.ListECRDeploymentInput))
	})
	return _c
}

func (_c *MockECRUsecase_ListECRDeployment_Call) Return(_a0 []dto.ECRDeployment, _a1 int64, _a2 error) *MockECRUsecase_ListECRDeployment_Call {
	_c.Call.Return(_a0, _a1, _a2)
	return _c
}

func (_c *MockECRUsecase_ListECRDeployment_Call) RunAndReturn(run func(context.Context, uuid.UUID, dto.ListECRDeploymentInput) ([]dto.ECRDeployment, int64, error)) *MockECRUsecase_ListECRDeployment_Call {
	_c.Call.Return(run)
	return _c
}

// StopECRDeployment provides a mock function with given fields: ctx, deploymentID
func (_m *MockECRUsecase) StopECRDeployment(ctx context.Context, deploymentID uuid.UUID) error {
	ret := _m.Called(ctx, deploymentID)

	if len(ret) == 0 {
		panic("no return value specified for StopECRDeployment")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) error); ok {
		r0 = rf(ctx, deploymentID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockECRUsecase_StopECRDeployment_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'StopECRDeployment'
type MockECRUsecase_StopECRDeployment_Call struct {
	*mock.Call
}

// StopECRDeployment is a helper method to define mock.On call
//   - ctx context.Context
//   - deploymentID uuid.UUID
func (_e *MockECRUsecase_Expecter) StopECRDeployment(ctx interface{}, deploymentID interface{}) *MockECRUsecase_StopECRDeployment_Call {
	return &MockECRUsecase_StopECRDeployment_Call{Call: _e.mock.On("StopECRDeployment", ctx, deploymentID)}
}

func (_c *MockECRUsecase_StopECRDeployment_Call) Run(run func(ctx context.Context, deploymentID uuid.UUID)) *MockECRUsecase_StopECRDeployment_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *MockECRUsecase_StopECRDeployment_Call) Return(_a0 error) *MockECRUsecase_StopECRDeployment_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockECRUsecase_StopECRDeployment_Call) RunAndReturn(run func(context.Context, uuid.UUID) error) *MockECRUsecase_StopECRDeployment_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateECRDeployment provides a mock function with given fields: ctx, currentUserID, input
func (_m *MockECRUsecase) UpdateECRDeployment(ctx context.Context, currentUserID uuid.UUID, input ecr.UpdateECRDeploymentInput) (*dto.ECRDeployment, error) {
	ret := _m.Called(ctx, currentUserID, input)

	if len(ret) == 0 {
		panic("no return value specified for UpdateECRDeployment")
	}

	var r0 *dto.ECRDeployment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID, ecr.UpdateECRDeploymentInput) (*dto.ECRDeployment, error)); ok {
		return rf(ctx, currentUserID, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID, ecr.UpdateECRDeploymentInput) *dto.ECRDeployment); ok {
		r0 = rf(ctx, currentUserID, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*dto.ECRDeployment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uuid.UUID, ecr.UpdateECRDeploymentInput) error); ok {
		r1 = rf(ctx, currentUserID, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockECRUsecase_UpdateECRDeployment_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateECRDeployment'
type MockECRUsecase_UpdateECRDeployment_Call struct {
	*mock.Call
}

// UpdateECRDeployment is a helper method to define mock.On call
//   - ctx context.Context
//   - currentUserID uuid.UUID
//   - input ecr.UpdateECRDeploymentInput
func (_e *MockECRUsecase_Expecter) UpdateECRDeployment(ctx interface{}, currentUserID interface{}, input interface{}) *MockECRUsecase_UpdateECRDeployment_Call {
	return &MockECRUsecase_UpdateECRDeployment_Call{Call: _e.mock.On("UpdateECRDeployment", ctx, currentUserID, input)}
}

func (_c *MockECRUsecase_UpdateECRDeployment_Call) Run(run func(ctx context.Context, currentUserID uuid.UUID, input ecr.UpdateECRDeploymentInput)) *MockECRUsecase_UpdateECRDeployment_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID), args[2].(ecr.UpdateECRDeploymentInput))
	})
	return _c
}

func (_c *MockECRUsecase_UpdateECRDeployment_Call) Return(_a0 *dto.ECRDeployment, _a1 error) *MockECRUsecase_UpdateECRDeployment_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockECRUsecase_UpdateECRDeployment_Call) RunAndReturn(run func(context.Context, uuid.UUID, ecr.UpdateECRDeploymentInput) (*dto.ECRDeployment, error)) *MockECRUsecase_UpdateECRDeployment_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateECRDeploymentEnv provides a mock function with given fields: ctx, currentUserID, input
func (_m *MockECRUsecase) UpdateECRDeploymentEnv(ctx context.Context, currentUserID uuid.UUID, input dto.UpdateECRDeploymentEnvInput) error {
	ret := _m.Called(ctx, currentUserID, input)

	if len(ret) == 0 {
		panic("no return value specified for UpdateECRDeploymentEnv")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID, dto.UpdateECRDeploymentEnvInput) error); ok {
		r0 = rf(ctx, currentUserID, input)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockECRUsecase_UpdateECRDeploymentEnv_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateECRDeploymentEnv'
type MockECRUsecase_UpdateECRDeploymentEnv_Call struct {
	*mock.Call
}

// UpdateECRDeploymentEnv is a helper method to define mock.On call
//   - ctx context.Context
//   - currentUserID uuid.UUID
//   - input dto.UpdateECRDeploymentEnvInput
func (_e *MockECRUsecase_Expecter) UpdateECRDeploymentEnv(ctx interface{}, currentUserID interface{}, input interface{}) *MockECRUsecase_UpdateECRDeploymentEnv_Call {
	return &MockECRUsecase_UpdateECRDeploymentEnv_Call{Call: _e.mock.On("UpdateECRDeploymentEnv", ctx, currentUserID, input)}
}

func (_c *MockECRUsecase_UpdateECRDeploymentEnv_Call) Run(run func(ctx context.Context, currentUserID uuid.UUID, input dto.UpdateECRDeploymentEnvInput)) *MockECRUsecase_UpdateECRDeploymentEnv_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID), args[2].(dto.UpdateECRDeploymentEnvInput))
	})
	return _c
}

func (_c *MockECRUsecase_UpdateECRDeploymentEnv_Call) Return(_a0 error) *MockECRUsecase_UpdateECRDeploymentEnv_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockECRUsecase_UpdateECRDeploymentEnv_Call) RunAndReturn(run func(context.Context, uuid.UUID, dto.UpdateECRDeploymentEnvInput) error) *MockECRUsecase_UpdateECRDeploymentEnv_Call {
	_c.Call.Return(run)
	return _c
}

// ValidateECRImageExists provides a mock function with given fields: ctx, imageURI
func (_m *MockECRUsecase) ValidateECRImageExists(ctx context.Context, imageURI string) error {
	ret := _m.Called(ctx, imageURI)

	if len(ret) == 0 {
		panic("no return value specified for ValidateECRImageExists")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string) error); ok {
		r0 = rf(ctx, imageURI)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockECRUsecase_ValidateECRImageExists_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ValidateECRImageExists'
type MockECRUsecase_ValidateECRImageExists_Call struct {
	*mock.Call
}

// ValidateECRImageExists is a helper method to define mock.On call
//   - ctx context.Context
//   - imageURI string
func (_e *MockECRUsecase_Expecter) ValidateECRImageExists(ctx interface{}, imageURI interface{}) *MockECRUsecase_ValidateECRImageExists_Call {
	return &MockECRUsecase_ValidateECRImageExists_Call{Call: _e.mock.On("ValidateECRImageExists", ctx, imageURI)}
}

func (_c *MockECRUsecase_ValidateECRImageExists_Call) Run(run func(ctx context.Context, imageURI string)) *MockECRUsecase_ValidateECRImageExists_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockECRUsecase_ValidateECRImageExists_Call) Return(_a0 error) *MockECRUsecase_ValidateECRImageExists_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockECRUsecase_ValidateECRImageExists_Call) RunAndReturn(run func(context.Context, string) error) *MockECRUsecase_ValidateECRImageExists_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockECRUsecase creates a new instance of MockECRUsecase. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockECRUsecase(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockECRUsecase {
	mock := &MockECRUsecase{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
