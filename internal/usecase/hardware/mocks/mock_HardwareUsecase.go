// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	dto "api-server/internal/dto"
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// MockHardwareUsecase is an autogenerated mock type for the HardwareUsecase type
type MockHardwareUsecase struct {
	mock.Mock
}

type MockHardwareUsecase_Expecter struct {
	mock *mock.Mock
}

func (_m *MockHardwareUsecase) EXPECT() *MockHardwareUsecase_Expecter {
	return &MockHardwareUsecase_Expecter{mock: &_m.Mock}
}

// ListGPUNodes provides a mock function with given fields: ctx, short
func (_m *MockHardwareUsecase) ListGPUNodes(ctx context.Context, short bool) (*dto.ListGPUNodesResponse, error) {
	ret := _m.Called(ctx, short)

	if len(ret) == 0 {
		panic("no return value specified for ListGPUNodes")
	}

	var r0 *dto.ListGPUNodesResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, bool) (*dto.ListGPUNodesResponse, error)); ok {
		return rf(ctx, short)
	}
	if rf, ok := ret.Get(0).(func(context.Context, bool) *dto.ListGPUNodesResponse); ok {
		r0 = rf(ctx, short)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*dto.ListGPUNodesResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, bool) error); ok {
		r1 = rf(ctx, short)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockHardwareUsecase_ListGPUNodes_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListGPUNodes'
type MockHardwareUsecase_ListGPUNodes_Call struct {
	*mock.Call
}

// ListGPUNodes is a helper method to define mock.On call
//   - ctx context.Context
//   - short bool
func (_e *MockHardwareUsecase_Expecter) ListGPUNodes(ctx interface{}, short interface{}) *MockHardwareUsecase_ListGPUNodes_Call {
	return &MockHardwareUsecase_ListGPUNodes_Call{Call: _e.mock.On("ListGPUNodes", ctx, short)}
}

func (_c *MockHardwareUsecase_ListGPUNodes_Call) Run(run func(ctx context.Context, short bool)) *MockHardwareUsecase_ListGPUNodes_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(bool))
	})
	return _c
}

func (_c *MockHardwareUsecase_ListGPUNodes_Call) Return(_a0 *dto.ListGPUNodesResponse, _a1 error) *MockHardwareUsecase_ListGPUNodes_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockHardwareUsecase_ListGPUNodes_Call) RunAndReturn(run func(context.Context, bool) (*dto.ListGPUNodesResponse, error)) *MockHardwareUsecase_ListGPUNodes_Call {
	_c.Call.Return(run)
	return _c
}

// ListHardware provides a mock function with given fields: ctx, req
func (_m *MockHardwareUsecase) ListHardware(ctx context.Context, req dto.ListHardwareRequest) (*dto.ListHardwareResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for ListHardware")
	}

	var r0 *dto.ListHardwareResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.ListHardwareRequest) (*dto.ListHardwareResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, dto.ListHardwareRequest) *dto.ListHardwareResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*dto.ListHardwareResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, dto.ListHardwareRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockHardwareUsecase_ListHardware_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListHardware'
type MockHardwareUsecase_ListHardware_Call struct {
	*mock.Call
}

// ListHardware is a helper method to define mock.On call
//   - ctx context.Context
//   - req dto.ListHardwareRequest
func (_e *MockHardwareUsecase_Expecter) ListHardware(ctx interface{}, req interface{}) *MockHardwareUsecase_ListHardware_Call {
	return &MockHardwareUsecase_ListHardware_Call{Call: _e.mock.On("ListHardware", ctx, req)}
}

func (_c *MockHardwareUsecase_ListHardware_Call) Run(run func(ctx context.Context, req dto.ListHardwareRequest)) *MockHardwareUsecase_ListHardware_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.ListHardwareRequest))
	})
	return _c
}

func (_c *MockHardwareUsecase_ListHardware_Call) Return(_a0 *dto.ListHardwareResponse, _a1 error) *MockHardwareUsecase_ListHardware_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockHardwareUsecase_ListHardware_Call) RunAndReturn(run func(context.Context, dto.ListHardwareRequest) (*dto.ListHardwareResponse, error)) *MockHardwareUsecase_ListHardware_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockHardwareUsecase creates a new instance of MockHardwareUsecase. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockHardwareUsecase(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockHardwareUsecase {
	mock := &MockHardwareUsecase{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
