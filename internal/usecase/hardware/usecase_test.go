package hardware_test

import (
	"context"
	"errors"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"api-server/internal/dto"
	"api-server/internal/entities"
	"api-server/internal/enums"
	repository_mocks "api-server/internal/repositories/mocks"
	"api-server/internal/usecase"
	"api-server/internal/usecase/hardware"

	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/client-go/kubernetes/fake"
	kubetesting "k8s.io/client-go/testing"
)

func TestListHardware(t *testing.T) {
	mockUUID, _ := uuid.Parse("94de0f1a-be81-45cb-b56c-fef88895ea77")

	testcases := []struct {
		name      string
		mockFn    func(r *repository_mocks.MockRepository)
		expectErr error
	}{
		{
			name: "should return usecase.ErrInternal if CountHardware return error",
			mockFn: func(r *repository_mocks.MockRepository) {
				r.On("CountHardware", mock.Anything).Return(int64(0), errors.New("error"))
			},
			expectErr: usecase.ErrInternal,
		},
		{
			name: "should return usecase.ErrInternal if ListHardware return error",
			mockFn: func(r *repository_mocks.MockRepository) {
				r.On("CountHardware", mock.Anything).Return(int64(2), nil)
				r.On("ListHardware", mock.Anything, mock.Anything, mock.Anything).
					Return(nil, errors.New("error"))
			},
			expectErr: usecase.ErrInternal,
		},
		{
			name: "should pass",
			mockFn: func(r *repository_mocks.MockRepository) {
				r.On("CountHardware", mock.Anything).Return(int64(2), nil)

				gpuMem := 16 * 1024
				r.On("ListHardware", mock.Anything, mock.Anything, mock.Anything).
					Return([]entities.Hardware{
						{
							BaseModel: entities.BaseModel{
								ID: mockUUID,
							},
							CPU:    1,
							Mem:    1024,
							GPUMem: nil,
						},
						{
							BaseModel: entities.BaseModel{
								ID: mockUUID,
							},
							CPU:    1,
							Mem:    1024,
							GPUMem: &gpuMem,
						},
					}, nil)
			},
			expectErr: nil,
		},
	}

	for _, testcase := range testcases {
		t.Run(testcase.name, func(t *testing.T) {
			r := &repository_mocks.MockRepository{}
			testcase.mockFn(r)

			usecase := hardware.New(r, nil)
			resp, err := usecase.ListHardware(context.Background(), dto.ListHardwareRequest{
				Paginate: dto.PaginateRequest{},
			})

			// assertion
			assert.Equal(t, testcase.expectErr, err)

			if err == nil {
				machines := *resp.Data

				// cpu
				assert.Equal(t, uint(1), machines[0].Mem.Amount)
				assert.Equal(t, enums.MemoryUnit_GiB, machines[0].Mem.Unit)

				// gpu
				assert.Equal(t, uint(16), machines[1].GPUMem.Amount)
				assert.Equal(t, enums.MemoryUnit_GiB, machines[1].GPUMem.Unit)
			}
		})
	}
}

func TestListGPUNodes(t *testing.T) {
	ctx := context.Background()

	testcases := []struct {
		name              string
		mockRepoFn        func(mockRepo *repository_mocks.MockRepository)
		mockKubeReactorFn func(clientset *fake.Clientset)
		expectErr         bool
		expectedNodes     int
	}{
		{
			name: "should list GPU nodes successfully with one GPU node and one CPU node",
			mockRepoFn: func(mockRepo *repository_mocks.MockRepository) {
			},
			mockKubeReactorFn: func(clientset *fake.Clientset) {
				k8sNodeList := &v1.NodeList{
					Items: []v1.Node{
						{
							ObjectMeta: metav1.ObjectMeta{
								Name:   "gpu-node-1",
								Labels: map[string]string{"nvidia.com/gpu.product": "Tesla-T4"},
							},
							Status: v1.NodeStatus{
								Allocatable: map[v1.ResourceName]resource.Quantity{v1.ResourceName("nvidia.com/gpu"): resource.MustParse("1")},
							},
						},
					},
				}
				clientset.PrependReactor("list", "nodes", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					listAction := action.(kubetesting.ListAction)
					assert.Equal(t, "nvidia.com/gpu.product", listAction.GetListRestrictions().Labels.String())
					return true, k8sNodeList, nil
				})

				k8sPodList := &v1.PodList{Items: []v1.Pod{}}
				clientset.PrependReactor("list", "pods", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, k8sPodList, nil
				})
			},
			expectErr:     false,
			expectedNodes: 2, // 1 GPU node + 1 CPU node
		},
		{
			name:       "should return error if k8s Nodes().List() fails",
			mockRepoFn: nil,
			mockKubeReactorFn: func(clientset *fake.Clientset) {
				k8sNodeList := &v1.NodeList{Items: []v1.Node{}}
				clientset.PrependReactor("list", "nodes", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					listAction := action.(kubetesting.ListAction)
					assert.Equal(t, "nvidia.com/gpu.product", listAction.GetListRestrictions().Labels.String())
					return true, k8sNodeList, errors.New("k8s node list error")
				})
			},
			expectErr:     true,
			expectedNodes: 0,
		},
		{
			name: "should return error if k8s Pods().List() fails",
			mockRepoFn: func(mockRepo *repository_mocks.MockRepository) {
				// No repo calls expected if pod listing fails
			},
			mockKubeReactorFn: func(clientset *fake.Clientset) {
				k8sNodeList := &v1.NodeList{Items: []v1.Node{}}
				clientset.PrependReactor("list", "nodes", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					listAction := action.(kubetesting.ListAction)
					assert.Equal(t, "nvidia.com/gpu.product", listAction.GetListRestrictions().Labels.String())
					return true, k8sNodeList, nil
				})
				clientset.PrependReactor("list", "pods", func(action kubetesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, nil, errors.New("k8s pod list error")
				})
			},
			expectErr:     true,
			expectedNodes: 0,
		},
	}

	for _, tc := range testcases {
		t.Run(tc.name, func(t *testing.T) {
			mockRepo := &repository_mocks.MockRepository{}
			// Initialize fake clientset for Kubernetes
			fakeClient := fake.NewSimpleClientset()

			if tc.mockRepoFn != nil {
				tc.mockRepoFn(mockRepo)
			}
			if tc.mockKubeReactorFn != nil {
				tc.mockKubeReactorFn(fakeClient)
			}

			uc := hardware.New(mockRepo, fakeClient)
			resp, err := uc.ListGPUNodes(ctx, true)

			if tc.expectErr {
				assert.Error(t, err)
				assert.Nil(t, resp)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				assert.NotNil(t, resp.Data)
				assert.Len(t, *resp.Data, tc.expectedNodes)
				// Add more specific assertions based on mock data
				if tc.name == "should correctly parse GPU memory and container resources" {
					foundA100, foundV100, foundUnknown := false, false, false
					for _, node := range *resp.Data {
						if node.NodeName == "gpu-node-a100" {
							assert.Equal(t, 40, node.GPUMemoryGB)
							assert.Len(t, node.Deployments, 1)
							if len(node.Deployments) == 1 {
							}
							foundA100 = true
						}
						if node.NodeName == "gpu-node-v100" {
							assert.Equal(t, 16, node.GPUMemoryGB)
							assert.Len(t, node.Deployments, 0)
							foundV100 = true
						}
						if node.NodeName == "gpu-node-unknown" {
							assert.Equal(t, 8, node.GPUMemoryGB) // Default case from getGPUMemory
							assert.Len(t, node.Deployments, 0)
							foundUnknown = true
						}
					}
					assert.True(t, foundA100, "A100 node not processed correctly")
					assert.True(t, foundV100, "V100 node not processed correctly")
					assert.True(t, foundUnknown, "Unknown GPU node not processed correctly")
				} else if len(*resp.Data) > 0 && tc.expectedNodes > 1 && tc.name == "should list GPU nodes successfully with one GPU node and one CPU node" { // Check CPU node and first GPU node details for the original success case
					cpuNodeFound := false
					gpuNodeFound := false
					for _, node := range *resp.Data {
						if node.NodeName == "cpu" {
							cpuNodeFound = true
							assert.Equal(t, "CPU", node.Name)
						}
						if node.NodeName == "gpu-node-1" { // From mock data
							gpuNodeFound = true
							assert.Equal(t, "Tesla-T4", node.GPUModel)
							assert.Equal(t, int64(1), node.GPUCount)
						}
					}
					assert.True(t, cpuNodeFound, "CPU node not found")
					assert.True(t, gpuNodeFound, "GPU node 'gpu-node-1' not found")
				}
			}

			mockRepo.AssertExpectations(t)
			// No direct mockKube.AssertExpectations() needed as reactors handle it
			// mockKube.AssertExpectations(t)
			// mockCoreV1.AssertExpectations(t)
			// mockNode.AssertExpectations(t)
			// mockPod.AssertExpectations(t)
		})
	}
}
