---
language:
  - English
  - Spanish
  - French

license: CC BY 4.0
license_name: Creative Commons Attribution 4.0 International
license_link: https://creativecommons.org/licenses/by/4.0/
license_details: This dataset is licensed under CC BY 4.0, allowing commercial use with attribution.

libraries:
  - datasets
  - pandas
  - numpy

tags:
  - text-classification
  - sentiment-analysis
  - multilingual
  - social-media

annotations_creators:
  - crowd-sourced
  - expert-annotated

language_creators:
  - native-speakers
  - professional-translators

language_details:
  - English: American English
  - Spanish: Latin American Spanish
  - French: Metropolitan French

pretty_name: MultiLingual Sentiment Analysis Dataset

size_categories:
  - n<1K
  - 1K<n<10K
  - 10K<n<100K

source_datasets:
  - Twitter Sentiment Analysis Dataset
  - Amazon Reviews Dataset

task_categories:
  - Sentiment Analysis
  - Text Classification
  - Natural Language Processing

task_ids:
  - sentiment-classification
  - text-classification

paperswithcode_id: multilingual-sentiment-analysis

configs:
  - config_name: default
    data_files:
      - split: train
        path: data/train.json
      - split: validation
        path: data/validation.json
      - split: test
        path: data/test.json
  - config_name: small
    data_files:
      - split: train
        path: data/small/train.json
      - split: test
        path: data/small/test.json

dataset_info:
  features:
    - name: text
      dtype: string
    - name: label
      dtype: int64
    - name: language
      dtype: string
    - name: confidence
      dtype: float64
  config_name: default
  splits:
    - name: train
      num_bytes: 150MB
      num_examples: 50000
    - name: validation
      num_bytes: 15MB
      num_examples: 5000
    - name: test
      num_bytes: 15MB
      num_examples: 5000
  download_size: 180MB
  dataset_size: 180MB

extra_gated_fields:
  - name: email
    type: string
    description: "Your email address"
  - name: organization
    type: string
    description: "Your organization name"

extra_gated_prompt: "Please provide your email and organization to access this dataset."

train-eval-index:
  - config: default
    task: sentiment-classification
    task_id: sentiment-classification
    splits:
      train_split: train
      eval_split: validation
    col_mapping:
      text: text
      label: label
      language: language
    metrics:
      - type: accuracy
        name: Accuracy
      - type: f1
        name: F1 Score
      - type: precision
        name: Precision
      - type: recall
        name: Recall
---
